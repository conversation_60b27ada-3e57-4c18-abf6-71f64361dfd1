package seat_selection_consts

const (
	PMambaSeatSelectionInfoPrefix = "p_mamba_seat_selection_info"
	ExpireTime                    = 30 * 60

	OwnerTicket      = "owner_ticket"
	ChildTicket      = "child_ticket"
	CarryChildTicket = "infant_ticket"
	CouponTicket     = "coupon_ticket" // 优待票

	Undefined          PassengerType = 0
	Adult              PassengerType = 1
	Children           PassengerType = 2
	CarryChildren      PassengerType = 3
	HomeOwnerAdult     PassengerType = 4
	HomeOwnerChildren  PassengerType = 5
	HomeOwnerOldMan    PassengerType = 6
	PreferentialPeople PassengerType = 7 // 特殊优待

	NoOccupy Occupy = false
	IsOccupy Occupy = true

	NoLimit = -1
)

type (
	PassengerType int
	Occupy        bool
)

func (o Occupy) ToInt32() int32 {
	switch o.ToBool() {
	case true:
		return 1
	default:
		return 0
	}
}

func (o Occupy) ToBool() bool {
	return bool(o)
}

func (p PassengerType) ToInt() int {
	return int(p)
}

func (p PassengerType) ToInt32() int32 {
	return int32(p)
}

func (p PassengerType) ToInt64() int64 {
	return int64(p)
}
