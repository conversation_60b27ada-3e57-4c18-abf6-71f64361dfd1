package consts

const (
	// SingleType 单车型
	SingleType = 0
	// ShortDistanceType 聚合车型(三方,出租车)
	ShortDistanceType = 2
	// PeakType 高峰期盒子
	PeakType = 3
	// TPType TP盒子
	TPType = 4
	// TaxiPricingType 出租车计价盒子
	TaxiPricingType = 5
)

// athena style
const (
	ThemeStyleUnbalance                      = 7  // 供需包框
	ThemeStylePreferentialMarketing          = 8  // 优惠推荐
	ThemeStylePreferentialRecommendMarketing = 9  // 优惠推荐营销
	ThemeStyleNormative                      = 10 // 优惠推荐营销
	ThemeStyleDefaultSelectedCompensation    = 11 // 默勾无车赔 文案维护在api，所以新增这个类型
)

const (
	ThemeTypeBargain   = 5 // 老通用包框
	ThemeTypeCountDown = 6 // 倒计时包框
	ThemeTypeMarketing = 7 // 营销主题
	ThemeTypeNormative = 8 // 规范包框样式
)
