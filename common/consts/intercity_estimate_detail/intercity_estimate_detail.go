package intercity_estimate_detail

import (
	"errors"
	"strconv"
)

type Point int

const (
	HeadCard               = "head_card"
	StorePassengerInfoCard = "store_passenger_info_card"
	RealNameSeatInfoCard   = "real_name_seat_info_card"
	SeatInfoCard           = "seat_info_card"
	RuleCard               = "rule_card"
	BottomCard             = "bottom_card"
	PriceCard              = "price_card"
	StationPopUp           = "station_pop_up"
	ExtraInfo              = "extra_info"
	TravelCardInfo         = "travel_card_info"

	SupportChildTicket        = "children"
	NoSupportChildTicket      = "no_children"
	SupportHomeOwnerTicket    = "_and_home_owner"
	NoSupportHomeOwnerTicket  = "_and_no_home_owner"
	SupportCarryChildTicket   = "_carry_children"
	NoSupportCarryChildTicket = "_no_carry_children"
	SupportCouponTicket       = "_with_preferential"
	WithOccupy                = "_with_occupy"
	WithoutOccupy             = "_without_occupy"
	HongKong                  = "rule_list_hong_kong"
	FormatScanCode            = "station_scan"
	QuickScanCode             = "station_scan_quick"
	UnavaliablePrice          = -1
	CapPrice                  = 0
	TieredPrice               = 1

	RulesTypeRefund = 1 // 规则类型 - 退票
	RulesTypeRebook = 2 // 规则类型 - 改签

	NotBan = 0
	Ban    = 1

	BoardingPoint  Point = 1
	AlightingPoint Point = 2

	ChildIdExpired      = 1 // 儿童身份过期
	StationToFenceRoute = 1 //多点到门路线标识
	FenceRouteType      = 7 //多点到门路线类型
)
const (
	IntercityNewOrderPageSwitch = "intercity_new_order_page_switch"
	ZeroRule                    = 0                   // 退票率为0
	LastIndexFlagBit            = 1                   // 标志位，标识数组最后一个
	OneDateTime                 = int64(24 * 60 * 60) // 一天
	OneDateTimeStr              = "天"
	OneHour                     = int64(60 * 60) // 小时
	OneHourStr                  = "小时"
	TenRatioPricing             = 10 // 折扣除以的百分比
	HalfPrice                   = 5  // 5折
	ScanCode                    = "scan_code"
	FormatDetail                = "format_detail"
)

// ToString ...
func (p Point) ToString() string {
	return strconv.Itoa(int(p))
}

// 儿童、携童票类型
type SupportInfo struct {
	SupportChildrenTicket      bool // 是否支持儿童
	SupportHomeOwnerTicket     bool // 是否支持业主
	SupportCarryChildrenTicket bool // 是否支持携童
	SupportCouponTicket        bool // 是否支持优待票
	CarryChildrenOccupySeat    bool // 携童是否占座
}

var NoCheckPassenger error = errors.New("no check passenger")
