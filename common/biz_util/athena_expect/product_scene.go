package athena_expect

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

func BuildProductExpectContent(ctx context.Context, dcmpKey string, ets int32, sceneFlag int32, queueLen int32) string {
	var (
		expectText string
	)

	if ets <= 0 {
		return expectText
	}

	tag := map[string]string{
		"ets_sec":   util.ToString(ets),      // 秒单位
		"ets_min":   util.ToString(ets / 60), // 分钟单位
		"queue_len": util.ToString(queueLen), // 排队人数
	}
	if ets > 0 && ets <= 30 {
		// xx秒
		expectText = dcmp.GetJSONContentWithPath(ctx, dcmpKey, tag, "expect_scene_between_0_30")
	} else if ets > 30 && ets <= 60 {
		// 1分钟内
		expectText = dcmp.GetJSONContentWithPath(ctx, dcmpKey, tag, "expect_scene_between_30_60")
	} else if ets > 60 && ets <= 180 {
		// xx分钟
		expectText = dcmp.GetJSONContentWithPath(ctx, dcmpKey, tag, "expect_scene_between_60_180")
	} else if ets > 180 && ets <= 300 {
		// 最快3分钟
		expectText = dcmp.GetJSONContentWithPath(ctx, dcmpKey, tag, "expect_scene_between_180_300")
	} else if ets > 300 && ets <= 600 {
		// 最快5分钟
		expectText = dcmp.GetJSONContentWithPath(ctx, dcmpKey, tag, "expect_scene_between_300_600")
	} else if ets > 600 {
		if sceneFlag == int32(consts.QueueSceneFlag) {
			if queueLen <= 0 {
				expectText = dcmp.GetJSONContentWithPath(ctx, dcmpKey, tag, "expect_scene_over_600_not_queue_car")
			} else if queueLen > 0 && queueLen <= 200 {
				// 排队xx人
				expectText = dcmp.GetJSONContentWithPath(ctx, dcmpKey, tag, "expect_scene_over_600_queue_0_200")
			} else if queueLen > 200 {
				// 排队超过200人
				expectText = dcmp.GetJSONContentWithPath(ctx, dcmpKey, tag, "expect_scene_over_600_queue_over_200")
			}
		} else {
			expectText = dcmp.GetJSONContentWithPath(ctx, dcmpKey, tag, "expect_scene_over_600_not_queue_scene")
		}
	}

	return expectText
}
