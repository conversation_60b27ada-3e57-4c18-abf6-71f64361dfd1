package scene

import "git.xiaojukeji.com/s3e/pts/v2/scene"

// IsWaitFreeUpgrade 是否是长时未应答升舱
func IsWaitFreeUpgrade(sceneList *string) bool {
	if sceneList != nil {
		if scene.IsWaitFastUpgradePremier(*sceneList) || scene.IsWaitAPlusUpgradePremier(*sceneList) || scene.IsWaitSpecialRateUpgradeFast(*sceneList) || scene.IsWaitLimitSpecialUpgradeSpecialRate(*sceneList) {
			return true
		}
	}
	return false
}
