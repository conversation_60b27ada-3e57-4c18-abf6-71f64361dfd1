package pool

import (
	"context"
	"errors"
	"runtime"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

type TaskFunc func(ctx context.Context, req interface{}) (resp interface{}, err error)

type Task struct {
	Req     interface{}
	RunTask TaskFunc
	Resp    interface{}
	err     error
}

func (t *Task) GetErr() error {
	return t.err
}

type Pool struct {
	ctx      context.Context
	timeout  time.Duration
	taskList []*Task
}

func NewPool(ctx context.Context) *Pool {
	return &Pool{
		ctx:     ctx,
		timeout: 200 * time.Millisecond, // todo 确认线上效果后再修改时间
	}
}

func NewPoolByTimeout(ctx context.Context, timeout time.Duration) *Pool {
	return &Pool{
		ctx:     ctx,
		timeout: timeout,
	}
}

func (p *Pool) AddTask(t *Task) {
	p.taskList = append(p.taskList, t)
}

// MultiProc ...
func (p *Pool) MultiProc() (int, error) {
	if p.taskList == nil || len(p.taskList) == 0 {
		return 0, nil
	}

	ch := make(chan struct{}, len(p.taskList))
	closeCh := make(chan struct{})
	for index := range p.taskList {
		index := index
		go func(proc *Task) {
			defer func() {
				if err := recover(); err != nil {
					stack := make([]byte, 8192)
					stack = stack[:runtime.Stack(stack, false)]
					log.Trace.Warnf(p.ctx, "MultiProc", "{panic} multi_call panic=%v\n%s", err, stack)
					ch <- struct{}{}
				}
			}()
			proc.Resp, proc.err = proc.RunTask(p.ctx, proc.Req)
			ch <- struct{}{}
		}(p.taskList[index])
	}

	var returnNum int
	tk := time.NewTicker(p.timeout)
	defer tk.Stop()
	for {
		select {
		case <-ch:
			returnNum++
			if returnNum >= len(p.taskList) {
				return len(p.taskList), nil
			}
		case <-tk.C:
			close(closeCh)
			return returnNum, errors.New("multi process timeout")
		}
	}
}

func (p *Pool) GetResult() []*Task {
	return p.taskList
}
