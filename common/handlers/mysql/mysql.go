package mysql

import (
	"context"
	"os"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"

	legoTrace "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/nuwa/gorm"
)

var (
	MysqlClient *gorm.DB // gorm.DB对象是线程安全的
)

func Init() {
	dsn := conf.Viper.GetString("mysql.dsn")

	// 使用disf模式时，disf优先级高于dsn。使用过程中，切流操作1min生效。
	db, err := gorm.OpenWithDisf(
		"mysql",
		dsn,
		gorm.SetDisfEnable(conf.Viper.GetBool("mysql.disf_enable")),
		gorm.SetDisfName(conf.Viper.GetString("mysql.disf_name")),
	)
	if err != nil {
		log.Trace.Errorf(context.Background(), legoTrace.DLTagUndefined, "dail mysql dsn %v, err %v ", dsn, err)
		os.Exit(1)
	}

	// conn_max_lifetime must be set, otherwise dbproxy will kill the conn 120s
	db.DB().SetConnMaxLifetime(time.Duration(conf.Viper.GetInt("mysql.conn_max_lifetime")) * time.Second)
	db.DB().SetMaxIdleConns(conf.Viper.GetInt("mysql.max_idle_conns"))
	db.DB().SetMaxOpenConns(conf.Viper.GetInt("mysql.max_open_conns"))

	MysqlClient = db
}
