package util

import (
	"testing"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/terminal/access_key_id"
)

func TestIsFromMain(t *testing.T) {
	tests := []struct {
		name        string
		accessKeyId int32
		want        bool
	}{
		{
			name:        "iOS主端",
			accessKeyId: access_key_id.AccessKeyIdDidiIosPassengerApp,
			want:        true,
		},
		{
			name:        "Android主端",
			accessKeyId: access_key_id.AccessKeyIdDidiAndroidPassengerApp,
			want:        true,
		},
		{
			name:        "微信小程序",
			accessKeyId: access_key_id.AccessKeyIdDidiWeChatMiniProgram,
			want:        false,
		},
		{
			name:        "支付宝小程序",
			accessKeyId: access_key_id.AccessKeyIdDidiAlipayMiniProgram,
			want:        false,
		},
		{
			name:        "其他来源",
			accessKeyId: 999,
			want:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsFromMain(tt.accessKeyId); got != tt.want {
				t.Errorf("IsFromMain() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsFromMiniProgram(t *testing.T) {
	tests := []struct {
		name        string
		accessKeyId int32
		want        bool
	}{
		{
			name:        "微信小程序",
			accessKeyId: access_key_id.AccessKeyIdDidiWeChatMiniProgram,
			want:        true,
		},
		{
			name:        "支付宝小程序",
			accessKeyId: access_key_id.AccessKeyIdDidiAlipayMiniProgram,
			want:        true,
		},
		{
			name:        "iOS主端",
			accessKeyId: access_key_id.AccessKeyIdDidiIosPassengerApp,
			want:        false,
		},
		{
			name:        "Android主端",
			accessKeyId: access_key_id.AccessKeyIdDidiAndroidPassengerApp,
			want:        false,
		},
		{
			name:        "其他来源",
			accessKeyId: 999,
			want:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsFromMiniProgram(tt.accessKeyId); got != tt.want {
				t.Errorf("IsFromMiniProgram() = %v, want %v", got, tt.want)
			}
		})
	}
}
