package util

import (
	"strings"
)

func MergeAddress(displayName, address string) string {
	if "" == address || displayName == address || strings.Contains(address, displayName) {
		return address
	}

	if "" == displayName || strings.Contains(displayName, address) {
		return displayName
	}

	return displayName + "|" + address
}

func SplitAddress(mergedStr string) (displayName, address string) {
	stringList := strings.Split(mergedStr, "|")
	if len(stringList) <= 1 {
		return mergedStr, ""
	}

	return stringList[1], stringList[0]
}
