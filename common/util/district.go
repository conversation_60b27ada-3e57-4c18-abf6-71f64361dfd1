package util

import (
	"context"
	"encoding/json"
	"strconv"

	commonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	legoTrace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
	apolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
)

type CityInfo struct {
	Cityid       int32
	CityDesc     string
	CityPinYin   string
	CityLat      string
	CityLng      string
	Countyid     int32
	CountyDesc   string
	Countryid    *int32
	CountryDesc  *string
	Provinceid   *int32
	ProvinceDesc *string
	CountryCode  *string
	DistrictCode *string
}

type CountyInfo struct {
	Countyid     string `json:"county_id"`
	CountyName   string `json:"county_name"`
	CountyEnName string `json:"county_en_name"`
}

type StationInfo struct {
	Stationid   int32
	StationName string
	StationLat  string
	StationLng  string
	StationType string
	MergerName  string
}

const CITY_DETAIL_CONFIG_NS = "cn_city_base_info"
const COUNTY_DETAIL_CONFIG_NS = "cn_county_base_info"

func GetCityInfo(ctx context.Context, cityId int32) (city *CityInfo, err error) {
	type ConfCity struct {
		Name         string `json:"name"`
		PinYin       string `json:"pinyin"`
		CityID       int32  `json:"city_id"`
		Lat          string `json:"lat"`
		Lng          string `json:"lng"`
		DistrictCode string `json:"district"`
	}

	configName := CITY_DETAIL_CONFIG_NS
	if tracer := legoTrace.GetTrace(ctx); tracer.GetHintCode() == strconv.Itoa(int(commonConsts.PressureHintCode)) {
		configName = NameSpace2PressureNameSpace(configName)
	}

	config, err := apolloSDK.GetConfig(configName, cast.ToString(cityId))
	if err != nil {
		return nil, err
	}
	result, err := json.Marshal(config.GetJsonConfigs())
	if err != nil {
		return nil, err
	}
	var confCity = &ConfCity{}
	err = json.Unmarshal(result, &confCity)
	if err != nil || confCity == nil {
		return nil, err
	}
	city = &CityInfo{}
	city.Cityid = confCity.CityID
	city.CityDesc = confCity.Name
	city.CityPinYin = confCity.PinYin
	city.CityLat = confCity.Lat
	city.CityLng = confCity.Lng
	if confCity.DistrictCode != "" {
		city.DistrictCode = &confCity.DistrictCode
	}
	return city, nil
}

func GetCountyInfo(ctx context.Context, countyId int32) (county *CountyInfo, err error) {
	configName := COUNTY_DETAIL_CONFIG_NS
	if tracer := legoTrace.GetTrace(ctx); tracer.GetHintCode() == strconv.Itoa(int(commonConsts.PressureHintCode)) {
		configName = NameSpace2PressureNameSpace(configName)
	}

	config, err := apolloSDK.GetConfig(configName, cast.ToString(countyId))
	if err != nil {
		return nil, err
	}
	result, err := json.Marshal(config.GetJsonConfigs())

	if err != nil {
		return nil, err
	}
	var confCounty = &CountyInfo{}
	err = json.Unmarshal(result, &confCounty)
	if err != nil || confCounty == nil {
		return nil, err
	}
	return confCounty, nil
}
