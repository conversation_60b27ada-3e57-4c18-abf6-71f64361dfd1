package httpserv

import (
	"io"
	"net"
	"net/http"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"

	legoTrace "git.xiaojukeji.com/lego/context-go"
	"github.com/grpc-ecosystem/grpc-gateway/runtime"
	"golang.org/x/net/context"
)

var (
	// HTTPError replies to the request with the error.
	// You can set a custom function to this variable to customize error format.
	HTTPError = DefaultHTTPError
)

type errorBody struct {
	Errno  int32  `protobuf:"varint,100,name=errno" json:"errno"` // refer: "google.golang.org/grpc/codes"
	Errmsg string `protobuf:"bytes,2,name=errmsg" json:"errmsg"`
	Data   string `protobuf:"bytes,3,name=data" json:"data"`
}

func DefaultHTTPError(ctx context.Context, mux *runtime.ServeMux, marshaler runtime.Marshaler, w http.ResponseWriter, req *http.Request, err error) {
	const fallback = `{"errno": 2, "errmsg": "failed to marshal error message"}`

	contentType := marshaler.ContentType()

	w.Header().Set("Content-Type", contentType)

	body := &errorBody{
		Errno:  3, // http server内部错误，暂定3，可以改
		Errmsg: err.Error(),
	}

	buf, merr := marshaler.Marshal(body)
	if merr != nil {
		log.Trace.Infof(ctx, legoTrace.DLTagUndefined, "Failed to marshal error message %q: %v", body, merr)
		w.WriteHeader(http.StatusInternalServerError)
		if _, err := io.WriteString(w, fallback); err != nil {
			log.Trace.Infof(ctx, legoTrace.DLTagUndefined, "Failed to write response: %v", err)
		}
		return
	}

	w.WriteHeader(http.StatusOK)
	if _, err := w.Write(buf); err != nil {
		if ne, ok := err.(net.Error); ok && ne.Temporary() {
			// 连接暂时不可用，但可能稍后恢复
			log.Trace.Infof(ctx, legoTrace.DLTagHTTPFailed, "Temporary network error=%v", err)
		} else {
			// 可能是连接已经关闭
			log.Trace.Infof(ctx, legoTrace.DLTagHTTPFailed, "Connection closed, err=%v", err)
		}
	}
}
