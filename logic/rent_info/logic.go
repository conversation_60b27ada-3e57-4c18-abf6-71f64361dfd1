package rent_info

import (
	"context"
	"errors"
	"fmt"
	Locsvr "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Locsvr"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car/charter_open_products"
	"git.xiaojukeji.com/gulfstream/passenger-common/common"
	"sort"

	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/locsvr"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car/charter_combo"
	commonUtil "git.xiaojukeji.com/gulfstream/passenger-common/util"
	"git.xiaojukeji.com/nuwa/trace"
)

type Service struct {
	req *proto.GetRentInfoReq

	fromAreaInfo *Locsvr.Cityinfo

	openSourceIds        []int
	openProductIds       []int // 已开城的包车业务线
	openProductIdsByCity []int // 指定城市的开城业务线

	charterOpenProductHandler *charter_open_products.CharterOpenProductHandler
	charterComboHandler       *charter_combo.CharterComboHandler
}

func GetRentInfo(ctx context.Context, req *proto.GetRentInfoReq) (rspData *proto.RentInfo, errno int) {
	service := newService(req)
	errno = service.initParams(ctx)
	if consts.NoErr != errno {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "service initParams failed. err=%s", errno)
		return nil, errno
	}

	if err := service.load(ctx); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "service load failed. err=%s", err.Error())
		errno = BizError.ErrnoSystemError
		return
	}

	rspData, err := service.buildResponse(ctx)
	if err != nil {
		if errors.Is(err, BizError.ErrAreaNotOpenService) {
			errno = BizError.ErrnoAreaNotOpenService
			return
		}

		log.Trace.Warnf(ctx, trace.DLTagUndefined, "render failed. err=%s", err.Error())
		errno = BizError.ErrnoSystemError
		return
	}

	return
}

func newService(req *proto.GetRentInfoReq) *Service {
	return &Service{req: req}
}

// 参数校验
func (s *Service) initParams(ctx context.Context) int {
	if s.req == nil || s.req.FromArea <= 0 || s.req.Flat == 0 || s.req.Flng == 0 || s.req.AccessKeyId <= 0 {
		return BizError.ErrnoInvalidArgument
	}

	// 获取城市信息
	var err error
	if s.fromAreaInfo, err = s.getAreaInfo(ctx); err != nil {
		return BizError.ErrnoRpcFailed
	}

	// 初始化source_id，后续改为配置
	if s.req.AccessKeyId == bizCommonConsts.AccessKeyIDDiDiEnterprise {
		s.openSourceIds = []int{source_id.SourceIDCharterCarBusiness}
	}

	return consts.NoErr
}

// 加载开城列表和套餐信息
func (s *Service) load(ctx context.Context) error {
	s.charterOpenProductHandler = charter_open_products.NewCharterOpenProductHandler(ctx).WithOpenStatusCheck().SetNeedBuildPCityListMap()
	if err := s.charterOpenProductHandler.Load("", util.IntArrayToStringArray(s.openSourceIds), nil, s.req.Token); err != nil {
		return err
	}

	// 获取开城的业务线，以及当前城市开的业务线
	s.openProductIds, s.openProductIdsByCity = s.charterOpenProductHandler.GetOpenProductIds(s.openSourceIds, int(s.req.FromArea))
	if len(s.openProductIds) == 0 {
		// 没有开城业务线，异常
		return fmt.Errorf("empty openProductIds")
	}

	// 只获取当前城市开的业务线的套餐数据
	if len(s.openProductIdsByCity) != 0 {
		s.charterComboHandler = charter_combo.NewCharterComboHandler(ctx).SetNeedTimeSlice()
		if err := s.charterComboHandler.Load(s.openProductIdsByCity, int(s.req.FromArea), s.fromAreaInfo.GetDistrictCode(), s.req.Timestamp, int(s.req.AccessKeyId), s.req.AppVersion); err != nil {
			return err
		}

		for _, id := range s.openProductIdsByCity {
			if s.charterComboHandler.GetPackageInfo(id) == nil {
				// 说明账单没有配置该业务线的计价规则，则有问题
				return BizError.ErrAreaNotOpenService
			}
		}
	}

	return nil
}

func (s *Service) buildResponse(ctx context.Context) (rspData *proto.RentInfo, err error) {
	// 构建城市列表 和 套餐
	cityListDataMap := s.charterOpenProductHandler.GetProductCityListData(s.openSourceIds)
	resp := &proto.RentInfo{}
	for _, sId := range s.openSourceIds {
		for pId, cityList := range cityListDataMap[sId] {
			if common.InArrayInt(pId, s.openProductIds) {
				tab := &proto.RentTabItem{SourceId: int32(sId), BusinessId: int64(commonUtil.NewProductUtil().GetCommonProductId(pId)), CityList: cityList}
				if s.charterComboHandler != nil {
					tab.ComboList = s.buildComboList(ctx, pId)
				}

				resp.TabList = append(resp.TabList, tab)
			}
		}
	}

	return resp, nil
}

// buildComboList 构建套餐+时间片
func (s *Service) buildComboList(ctx context.Context, pId int) []*proto.RentComboDetail {
	comboId2Package := s.charterComboHandler.GetPackageInfo(pId)
	comboId2TimeSlice := s.charterComboHandler.GetComboTimeSlice(pId)
	comboList := make([]*proto.RentComboDetail, 0, len(comboId2Package))
	for comboId, pack := range comboId2Package {
		if pack == nil {
			continue
		}

		ts, ok := comboId2TimeSlice[comboId]
		if !ok || ts == nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "comboId=%d with no comboId2TimeSlice")
			continue
		}

		detail := &proto.RentComboDetail{
			ComboId:              int32(comboId),
			ComboTime:            pack.Time,
			ComboDistance:        pack.Distance,
			ComboStartTime:       int32(ts.StartTime),
			ComboEndTime:         int32(ts.EndTime),
			ComboEarliestMinutes: int32(ts.EarliestMinutes),
			ComboDuringDays:      int32(ts.DuringDays),
			ComboTimeGap:         int32(ts.TimeGap),
		}
		comboList = append(comboList, detail)
	}

	// 按照combo_id正序排序
	sort.Slice(comboList, func(i, j int) bool { return comboList[i].ComboId < comboList[j].ComboId })
	return comboList
}

func (s *Service) getAreaInfo(ctx context.Context) (*Locsvr.Cityinfo, error) {
	var coords = make([]map[string]float64, 0)
	coords = append(coords, map[string]float64{"lat": s.req.Flat, "lng": s.req.Flng})
	cityInfos, err := locsvr.MultiAreaInfoByCoords(ctx, coords, s.req.MapType)
	if err != nil || len(cityInfos) == 0 || cityInfos[0] == nil {
		return nil, err
	}

	return cityInfos[0], nil
}
