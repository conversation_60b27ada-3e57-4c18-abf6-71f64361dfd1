package get_form_real_data

import (
	"context"
	"testing"
)

// TestService_getFromHeadRule4NormalCompensation 函数用于测试 getFromHeadRule4NormalCompensation 方法的逻辑
func TestService_getFromHeadRule4NormalCompensation(t *testing.T) {
	// 定义测试用例结构体
	testCases := []struct {
		name               string   // 测试用例名称
		service            *Service // 测试服务的实例
		sourceType         int32    // 输入的 sourceType
		expectedNil        bool     // 期望结果是否为 nil
		expectedActionType int32    // 期望的 ActionType
		expectedHasArrow   bool     // 期望的 HasArrow
	}{
		{
			name: "场景1: NormalCompensationInfo 为 nil",
			service: &Service{
				CompensationInfoResult: CompensationInfoResult{
					NormalCompensationInfo: &NormalCompensation{
						WaitTime:                 180,
						MemberLevel:              6,
						InflateRatio:             0,
						ProductCategoryList:      []string{"1", "2", "3"},
						ProductCategoryNum:       3,
						CalledProductCategoryNum: 2,
						PrivilegeSource:          "new_passenger",
						CouponInfo:               CouponInfo{Amount: 10},
						FrequencyStatus:          0,
						Status:                   1,
					},
				},
				selectPcIds: []int64{1, 2},
			},
			sourceType:  2,
			expectedNil: true, // 期望返回 nil
		},
	}

	// 遍历测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			tc.service.getFromHeadRule4NormalCompensation(ctx, tc.sourceType)

		})
	}
}
