package get_form_real_data

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"runtime/debug"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"

	"git.xiaojukeji.com/engine/gaia-sdk/common"

	Apollo "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"

	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/nuwa/trace"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"

	errors2 "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/locsvr"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

var argumentErrorWithMultiProduct error = errors.New("req invalid with multiProduct")

const QuotationCachePrefix = "form_real_data"
const CalledCarsPriceExpire = 300 * time.Second // 已呼叫车型的汇总价格redis expire
const FirstCall = 0
const EtsResult = 1
const CompensationResult = 2

type Service struct {
	req *proto.PGetFormRealDataReq

	reqMultiProduct []*MultiProduct
	reqBoxInfo      map[string][]string
	pcIdToBoxId     map[string]string

	selectPcIds   []int64
	unselectPcIds []int64
	selectPcIdSet map[int64]struct{}

	UserInfo  *passport.UserInfo
	CityInfo  *locsvr.StartDestInfo
	OrderInfo *dos.OrderInfo

	StripInfo               *AthenaApiv3.EstimateStripInfo
	PriceDiffInfo           *PriceDiffInfo         // 比价推荐信息
	TransDataMap            map[string]interface{} // 透传信息
	RecommendOrderMatchInfo *AthenaApiv3.RecommendOrderMatchInfo
	EtdInfo                 []*AthenaApiv3.EstimateEtpEtdInfo

	// 赔付系统接口结果
	CompensationInfoResult
	// athena新预期接口结果
	AthenaExpectInfoResult
	BargainRangePriceInfo *proto.BargainRangePriceInfo

	// 特征信息，TransData产生的一系列变量存储在内
	TransDataFeature

	// 实验信息
	IsToEtp bool
	Cfg     *Apollo.Config
}

type TransDataFeature struct {
	isHitPredictGlobalEts     bool // 等应答是否命中全局ets场景
	isPredictGlobalEtsTimeout bool // 相比于全局ets预期时间是否超时
}

type CompensationInfoResult struct {
	NormalCompensationInfo          *NormalCompensation
	DefaultSelectedCompensationInfo *DefaultSelectedCompensation
}

type AthenaExpectInfoResult struct {
	GlobalSceneExpect           *AthenaApiv3.GlobalExpectInfo    // 全局预期
	V3GlobalSceneExpect         *AthenaApiv3.GlobalExpectInfo    // 全局预期 - v3表单
	MapQueueExpectInfo          *AthenaApiv3.MapQueueExpectInfo  // 排队人数预期
	PriceAxisExpect             *AthenaApiv3.PriceAxisExpectInfo // 价格轴预期场景
	ProductExpectInfo           *AthenaApiv3.ProductExpectInfo   // 品类预期场景
	OrderMatchSaveTimeExpect    *AthenaApiv3.SaveTimeExpectInfo  // 追加车型全局预期
	OrderMatchGlobalSceneExpect *AthenaApiv3.GlobalOrderMatchExpectInfo
}

type MultiProduct struct {
	EstimateID      string  `json:"estimate_id"`
	ProductCategory int64   `json:"product_category"`
	PaymentID       int     `json:"payment_id"`
	FeeAmount       float64 `json:"fee_amount"`
	BusinessID      int32   `json:"business_id"`
	Select          *int    `json:"is_selected,omitempty"`
	RequireLevel    int32   `json:"require_level"`
	ComboType       int32   `json:"combo_type"`
	LevelType       int32   `json:"level_type"`
	CategoryId      int32   `json:"category_id"`
	Extra           string  `json:"extra"`
}

// TransDataFormRealData 解析 trans_data 按需扩充
type TransDataFormRealData struct {
	IsHitPredictGlobalEts     int `json:"is_hit_predict_global_ets"`
	IsPredictGlobalEtsTimeout int `json:"is_predict_global_ets_timeout"`
}

// recoverPanic ...
func recoverPanic(ctx context.Context) {
	if r := recover(); r != nil {
		log.Trace.Warnf(ctx, "recoverPanic", "panic=%v||stack=%v", r, string(debug.Stack()))
	}
}

func handleTransData(ctx context.Context, req *proto.PGetFormRealDataReq, instance *Service) {
	// 解析 trans_data
	transData := TransDataFormRealData{}
	if req.TransData == nil {
		return
	}
	err := json.Unmarshal([]byte(*req.TransData), &transData)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "get_form_real_data service transData json unmarshal error: %v ", err)
		return
	}

	instance.isHitPredictGlobalEts = transData.IsHitPredictGlobalEts == 1         // 预期是否命中全局ets场景
	instance.isPredictGlobalEtsTimeout = transData.IsPredictGlobalEtsTimeout == 1 // 相比于全局ets预期时间是否超时
	log.Trace.Infof(ctx, trace.DLTagUndefined, "get_form_real_data service transData isHitPredictGlobalEts: %v, isPredictGlobalEtsTimeout: %v",
		instance.isHitPredictGlobalEts, instance.isPredictGlobalEtsTimeout)
}

func serviceInit(ctx context.Context, req *proto.PGetFormRealDataReq) (*Service, error) {
	instance := &Service{
		req:             req,
		reqMultiProduct: make([]*MultiProduct, 0),
	}

	var err error

	if req.AccessKeyId == 0 || len(req.Appversion) == 0 || req.Token == "" ||
		req.FromLat == 0 || req.FromLng == 0 || req.ToLng == 0 || req.ToLat == 0 {
		return nil, errors2.ErrInvalidArgument
	}

	// 解析 trans_data
	handleTransData(ctx, req, instance)

	// 先面向等应答加参数校验，以免影响冒泡流程
	if req.MultiProduct == nil || *req.MultiProduct == "" {
		if req.SourceType != nil && (*req.SourceType == consts.SourceTypeAnyCarEstimate ||
			*req.SourceType == consts.SourceTypeAnyCarRecBox ||
			*req.SourceType == consts.SourceTypeOneStop85 ||
			*req.SourceType == consts.SourceTypeAnyCarRecBoxSingle) {
			return nil, errors2.NewBizError(argumentErrorWithMultiProduct, errors2.ErrnoInvalidArgument)
		}
	} else {
		err = json.Unmarshal([]byte(*req.MultiProduct), &instance.reqMultiProduct)
		if err != nil || len(instance.reqMultiProduct) == 0 {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "parse json error with %v", err)
			return nil, errors2.NewBizError(argumentErrorWithMultiProduct, errors2.ErrnoInvalidArgument)
		}
	}

	if req.BoxInfo != nil && *req.BoxInfo != "" &&
		req.SourceType != nil &&
		(*req.SourceType == consts.SourceTypeNormalTab || *req.SourceType == consts.SourceTypeClassifyTab) {
		err = json.Unmarshal([]byte(*req.BoxInfo), &instance.reqBoxInfo)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "parse json error with %v", err)
			return nil, errors2.NewBizError(errors.New("req invalid with boxInfo"), errors2.ErrnoInvalidArgument)
		}
	}

	//透传字段转换 （非rpc)
	if req.TransData != nil && len(*req.TransData) > 0 && *req.TransData != "[]" {
		transDataMap := make(map[string]interface{})
		unmarshalErr := json.Unmarshal([]byte(*req.TransData), &transDataMap)
		if unmarshalErr != nil {
			log.Trace.Errorf(ctx, "json unmarshal error", "trans data is%v,  unmarshal err ", *req.TransData)
			return nil, errors.New("trans data json unmarshal error")
		}
		instance.TransDataMap = transDataMap
	}

	group, ctx := errgroup.WithContext(ctx)

	// 用户 (rpc)
	if len(req.Token) <= 0 {
		return nil, errors2.BizErrorFromErrNo(errors2.ErrnoInvalidArgument)
	} else {
		group.Go(func() error {
			instance.UserInfo, err = passport.GetUserInfo(ctx, req.Token, "")
			if err != nil {
				log.Trace.Infof(ctx, "GetUserInfo error", "err %+v ", err)
				return errors2.BizErrorFromErrNo(errors2.ErrnoInvalidArgument)
			}

			return nil
		})
	}

	// 地区信息 (rpc)
	var coords []map[string]float64
	coords = append(coords, map[string]float64{"lat": req.FromLat, "lng": req.FromLng})
	coords = append(coords, map[string]float64{"lat": req.ToLat, "lng": req.ToLng})
	group.Go(func() error {
		if instance.CityInfo, err = locsvr.BuildStartDestInfo(ctx, coords, "soso"); err != nil {
			log.Trace.Infof(ctx, "BuildStartDestInfo error", "err %+v ", err)
			return err
		}

		return nil
	})

	// 订单 (rpc) （表单场景不调用）
	if req.Oid != nil && !isFormSourceType(req.SourceType) {
		orderID, district, err := util.DecodeOrderID(*req.Oid)
		if err != nil {
			log.Trace.Errorf(ctx, "oid_decode_error", "decodeErr: %v", err)
		}

		group.Go(func() error {
			orderInfo, dosErr := dos.GetOrderInfo(ctx, orderID, district)

			if dosErr != nil || orderInfo == nil {
				log.Trace.Infof(ctx, "GetOrderInfo", "err %+v ", err)
				return errors.New("fail to get dos")
			}
			instance.OrderInfo = orderInfo
			return nil
		})
	}

	err = group.Wait()
	if err != nil {
		log.Trace.Infof(ctx, "initService fail", "err %+v ", err)
		return nil, err
	}

	if len(instance.reqMultiProduct) > 0 {
		instance.selectPcIds = make([]int64, 0, len(instance.reqMultiProduct))
		instance.unselectPcIds = make([]int64, 0, len(instance.reqMultiProduct))
		instance.selectPcIdSet = make(map[int64]struct{}, len(instance.reqMultiProduct))

		for _, product := range instance.reqMultiProduct {
			if product == nil {
				continue
			}

			if product.ProductCategory == estimate_pc_id.EstimatePcIdHuiXuanCar && product.Extra != "" {
				var bargainRangePriceInfo = &proto.BargainRangePriceInfo{}
				if err = json.Unmarshal([]byte(product.Extra), bargainRangePriceInfo); err == nil {
					instance.BargainRangePriceInfo = bargainRangePriceInfo
				}
			}

			if product.Select == nil || *product.Select == 1 {
				instance.selectPcIds = append(instance.selectPcIds, product.ProductCategory)
				instance.selectPcIdSet[product.ProductCategory] = struct{}{}
			} else {
				instance.unselectPcIds = append(instance.unselectPcIds, product.ProductCategory)
			}
		}
	}

	// 处理 box 和 pcId 的映射关系
	if instance.reqBoxInfo != nil && len(instance.reqBoxInfo) > 0 {
		instance.pcIdToBoxId = make(map[string]string)
		for boxId, pcIds := range instance.reqBoxInfo {
			if len(pcIds) == 0 {
				continue
			}

			// 建立 pcId 到 boxId 的映射
			for _, pcid := range pcIds {
				// 只有当 pcid 不是 boxId 时才建立映射
				if _, exists := instance.reqBoxInfo[pcid]; !exists {
					instance.pcIdToBoxId[pcid] = boxId
				}
			}
		}
	}

	if instance.BargainRangePriceInfo == nil && req.BargainRangePriceInfo != nil {
		instance.BargainRangePriceInfo = req.BargainRangePriceInfo
	}

	return instance, nil
}

func NewService(ctx context.Context, req *proto.PGetFormRealDataReq) (instance *Service, err error) {

	instance, err = serviceInit(ctx, req)
	if err != nil || instance == nil {
		return nil, err
	}

	group, ctx := errgroup.WithContext(ctx)

	// 推荐（rpc)
	if req.SourceType == nil || (*req.SourceType == consts.SourceTypeAnyCarRecBoxSingle || *req.SourceType == consts.SourceTypeBargainRangeBubble || *req.SourceType == consts.SourceTypeBargainRangeAnyCar) {
		//后续新加sourceType，需要判断是否需要调用athena。以及调用athena入参的event的值
		group.Go(func() error {
			defer recoverPanic(ctx)
			// 推荐信息
			err = instance.buildRecommendInfo(ctx)
			if err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get recommendInfo with err %v", err)
				return errors.New("fail to buildRecommendInfo")
			}
			return nil
		})
	}

	if req.SourceType != nil && (*req.SourceType == consts.SourceTypeAnyCarEstimate || *req.SourceType == consts.SourceTypeAnyCarRecBox) {
		group.Go(func() error {
			defer recoverPanic(ctx)
			instance.buildWaitAnswerPriceDiff(ctx)
			return nil
		})
	}

	// 赔付（rpc)
	if req.SourceType != nil && (*req.SourceType == consts.SourceTypeNormalTab || *req.SourceType == consts.SourceTypeClassifyTab || *req.SourceType == consts.SourceTypeAnyCarEstimate || *req.SourceType == consts.SourceTypeAnyCarRecBox || *req.SourceType == consts.SourceTypeAnyCarRecBoxSingle) {
		group.Go(func() error {
			defer recoverPanic(ctx)
			instance.buildCompensationInfo(ctx)
			return nil
		})
	}

	// 8.5(rpc)
	if req.SourceType != nil && (*req.SourceType == consts.SourceTypeOneStop85) {
		group.Go(func() error {
			defer recoverPanic(ctx)
			instance.buildEstimateTimeInfo(ctx)
			return nil
		})
	}

	// athena新预期接口 (rpc)
	if req.SourceType == nil || (*req.SourceType == consts.SourceTypeNormalTab || *req.SourceType == consts.SourceTypeClassifyTab ||
		*req.SourceType == consts.SourceTypeAnyCarEstimate || *req.SourceType == consts.SourceTypeAnyCarRecBox || *req.SourceType == consts.SourceTypeOneStop85 ||
		*req.SourceType == consts.SourceTypeAnyCarRecBoxSingle) {
		group.Go(func() error {
			defer recoverPanic(ctx)
			instance.buildBubbleExpectInfo(ctx)
			return nil
		})
	}

	err = group.Wait()
	if err != nil {
		return instance, err
	}

	if req.SourceType != nil && *req.SourceType == consts.SourceTypeClassifyTab {
		instance.buildIsToEtp(ctx)
	}

	return instance, nil
}

func isFormSourceType(sourceType *int32) bool {
	if sourceType == nil {
		return false
	}

	if *sourceType == consts.SourceTypeNormalTab ||
		*sourceType == consts.SourceTypeClassifyTab ||
		*sourceType == consts.SourceTypeOneStop85 {
		return true
	}

	return false
}

func (c *Service) buildRecommendInfo(ctx context.Context) error {
	if c.CityInfo == nil {
		return errors.New("CityInfo empty")
	}

	athenaReq, err := c.buildAthenaReq(ctx)
	if err != nil {
		return err
	}

	resp, err := athena.GetAthenaUserRecommendInfo(ctx, athenaReq)
	if err != nil {
		return err
	}

	c.StripInfo = resp.GetEstimateStripInfo()
	c.RecommendOrderMatchInfo = resp.GetOrderMatchInfo()
	return nil
}

func (c *Service) buildBubbleExpectInfo(ctx context.Context) {
	if c.CityInfo == nil || c.UserInfo == nil {
		return
	}

	athenaBubbleExpectInfoReq := c.buildAthenaBubbleExpectInfoReq(ctx)
	resp, err := athena.GetAthenaBubbleExpectInfo(ctx, athenaBubbleExpectInfoReq)
	if err != nil {
		// log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get bubbleExpectInfo with err %v", err)
		return
	}

	c.GlobalSceneExpect = resp.GetGlobalSceneExpect()
	c.V3GlobalSceneExpect = resp.GetV3GlobalSceneExpect()
	c.MapQueueExpectInfo = resp.GetMapQueueSceneExpect()
	c.PriceAxisExpect = resp.GetPriceAxisSceneExpect()
	c.OrderMatchSaveTimeExpect = resp.GetOrderMatchSaveTimeSceneExpect()
	c.ProductExpectInfo = resp.GetProductSceneExpect()
	c.OrderMatchGlobalSceneExpect = resp.GetOrderMatchGlobalSceneExpect()

	return
}

func (c *Service) buildAthenaReq(ctx context.Context) (*AthenaApiv3.AthenaUserRecommendInfoReq, error) {

	clientType := cast.ToString(c.req.ClientType)
	req := &AthenaApiv3.AthenaUserRecommendInfoReq{
		AccessKeyID:   c.req.GetAccessKeyId(),
		AppVersion:    c.req.GetAppversion(),
		Lang:          c.req.GetLang(),
		Phone:         c.UserInfo.Phone,
		FromArea:      int64(c.CityInfo.FromCityId),
		FromLng:       c.req.GetFromLng(),
		FromLat:       c.req.GetFromLat(),
		Pid:           strconv.FormatUint(c.UserInfo.PID, 10),
		Event:         "estimate_strip_info",
		BubbleTraceID: c.req.EstimateTraceId,
		To_Lng:        &c.req.ToLng,
		To_Lat:        &c.req.ToLat,
		ToName:        c.req.ToName,
		ClientType:    &clientType,
	}
	if c.req.Oid != nil && *c.req.Oid != "" {
		orderID, _, err := util.DecodeOrderID(*c.req.Oid)
		if err != nil {
			log.Trace.Errorf(ctx, "oid_decode_error", "decodeErr: %v", err)
		} else {
			req.OrderID = &orderID
		}
	}
	if c.req.GetSourceType() == consts.SourceTypeClassifyTab {
		req.Event = "classify_estimate_info"
		req.ExtraInfo = c.buildBargainRangePrice()
	}
	if c.req.GetSourceType() == consts.SourceTypeAnyCarEstimate {
		req.Event = "order_match_anycar"
		c.buildMinEts(req)
	}
	if c.req.GetSourceType() == consts.SourceTypeAnyCarRecBox || c.req.GetSourceType() == consts.SourceTypeAnyCarRecBoxSingle {
		req.Event = "order_match_rec_product"
		c.buildMinEts(req)
	}
	if c.req.GetSourceType() == consts.SourceTypeBargainRangeBubble || c.req.GetSourceType() == consts.SourceTypeBargainRangeAnyCar {
		req.Event = "range_answer_rate"
		// c.buildBargainRangePriceInfo(req)
		req.ExtraInfo = c.buildBargainRangePrice()
	}

	filteredMultiProduct := make([]*MultiProduct, 0, len(c.selectPcIds))
	for _, product := range c.reqMultiProduct {
		if product == nil {
			continue
		}
		if product.Select == nil || *product.Select == 1 {
			filteredMultiProduct = append(filteredMultiProduct, product)
		}
	}

	str, err := jsoniter.MarshalToString(filteredMultiProduct)
	if err != nil {
		return req, errors.New(fmt.Sprintf("build MultiProduct failed. reqMultiProduct = %+v, filtered = %+v, err = %+v", c.reqMultiProduct, filteredMultiProduct, err))
	}

	req.MultiProduct = util.StringPtr(str)

	return req, nil
}

func (c *Service) buildAthenaBubbleExpectInfoReq(ctx context.Context) *AthenaApiv3.AthenaBubbleExpectInfoReq {
	var (
		estimateTraceId string
		highOrderID     int64
	)
	clientType := cast.ToString(c.req.ClientType)
	accessKeyID := strconv.FormatInt(c.req.GetAccessKeyId(), 10)
	appVersion := c.req.GetAppversion()
	lang := c.req.GetLang()
	phone := c.UserInfo.Phone
	cityID := c.CityInfo.FromCityId
	fromLng := c.req.GetFromLng()
	fromLat := c.req.GetFromLat()
	fromName := c.req.GetFromName()
	pid := strconv.FormatUint(c.UserInfo.PID, 10)
	toLng := c.req.GetToLng()
	toLat := c.req.GetToLat()
	toName := c.req.GetToName()
	channel := int32(c.req.GetChannel())
	if c.req.Oid != nil {
		orderID, district, err := util.DecodeOrderID(*c.req.Oid)
		if err == nil {
			highOrderId, err1 := util.EncodeOrderIdWithoutBase64(orderID, district)
			if err1 == nil {
				highOrderID = highOrderId
			} else {
				log.Trace.Errorf(ctx, "oid_encode_error", "encodeErr: %v", err)
			}
		} else {
			log.Trace.Errorf(ctx, "oid_decode_error", "decodeErr: %v", err)
		}
	}

	if c.OrderInfo != nil && (*c.req.SourceType == consts.SourceTypeAnyCarRecBox || *c.req.SourceType == consts.SourceTypeAnyCarEstimate || *c.req.SourceType == consts.SourceTypeAnyCarRecBoxSingle) {
		estimateTraceId = c.OrderInfo.ExtendFeatureParsed.EstimateTraceID
	} else {
		estimateTraceId = c.req.GetEstimateTraceId()
	}
	canAddProductList := make([]*AthenaApiv3.ExpectProductInfo, 0)
	for _, multiProduct := range c.reqMultiProduct {
		var isSelected bool
		businessID := int64(multiProduct.BusinessID)
		comboType := int64(multiProduct.ComboType)
		requireLevel := int64(multiProduct.RequireLevel)
		levelType := int64(multiProduct.LevelType)
		if multiProduct.Select == nil || *multiProduct.Select == 1 {
			isSelected = true
		} else {
			isSelected = false
		}

		categoryId := multiProduct.CategoryId
		estimateId := multiProduct.EstimateID
		nTuple := &AthenaApiv3.NTuple{
			BusinessID:      &businessID,
			ProductCategory: &multiProduct.ProductCategory,
			ComboType:       &comboType,
			RequireLevel:    &requireLevel,
			IsSelected:      &isSelected,
			LevelType:       &levelType,
			EstimateID:      &estimateId,
		}
		finalFee := 100 * multiProduct.FeeAmount
		canAddProduct := &AthenaApiv3.ExpectProductInfo{
			NTuple:     nTuple,
			FinalFee:   &finalFee,
			CategoryID: &categoryId,
		}

		// 处理分组信息
		if c.pcIdToBoxId != nil {
			if groupId, ok := c.pcIdToBoxId[cast.ToString(multiProduct.ProductCategory)]; ok {
				if groupIdUtil := group_id.NewGroupId(groupId); groupIdUtil != nil {
					subGroupID := groupIdUtil.GetSubGroupIdByGroupId()
					isFlowGroup := groupIdUtil.IsFlowBoxByGroupId()

					canAddProduct.SubGroupID = subGroupID
					canAddProduct.IsFlowGroup = &isFlowGroup
				}
			}
		}

		canAddProductList = append(canAddProductList, canAddProduct)
	}

	alreadySendProductList := make([]*AthenaApiv3.ExpectProductInfo, 0)
	if c.OrderInfo != nil {
		for _, productItem := range c.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct {
			nTuple := &AthenaApiv3.NTuple{
				ProductID:       util.IntToInt64Ptr(productItem.ProductId),
				BusinessID:      util.IntToInt64Ptr(productItem.BusinessId),
				RequireLevel:    util.IntToInt64Ptr(util.ToInt(productItem.RequiredLevel)),
				ComboType:       util.IntToInt64Ptr(productItem.ComboType),
				ProductCategory: util.IntToInt64Ptr(productItem.ProductCategory),
				LevelType:       util.IntToInt64Ptr(productItem.LevelType),
				EstimateID:      &productItem.EstimateID,
			}
			alreadySendProduct := &AthenaApiv3.ExpectProductInfo{
				NTuple: nTuple,
			}
			alreadySendProductList = append(alreadySendProductList, alreadySendProduct)
		}
	}

	expectSceneMap := map[int32][]string{
		consts.SourceTypeNormalTab:          {consts.MapQueueSceneExpectScene, consts.ProductSceneExpectScene, consts.V3FormGlobalSceneExpectScene},
		consts.SourceTypeClassifyTab:        {consts.MapQueueSceneExpectScene, consts.ProductSceneExpectScene, consts.ClassifyFormGlobalSceneExpectScene},
		consts.SourceTypeAnyCarEstimate:     {consts.OrderMatchSaveTimeScene, consts.OrderMatchGlobalSceneExpectScene, consts.ProductSceneExpectScene},
		consts.SourceTypeAnyCarRecBox:       {consts.OrderMatchSaveTimeScene, consts.OrderMatchGlobalSceneExpectScene, consts.ProductSceneExpectScene},
		consts.SourceTypeAnyCarRecBoxSingle: {consts.OrderMatchSaveTimeScene, consts.OrderMatchGlobalSceneExpectScene, consts.ProductSceneExpectScene},
		consts.SourceTypeOneStop85:          {consts.MapQueueSceneExpectScene, consts.ProductSceneExpectScene},
	}
	expectScene := make([]string, 0)
	if c.req.SourceType == nil {
		expectScene = expectSceneMap[consts.SourceTypeNormalTab]
	} else {
		expectScene = expectSceneMap[*c.req.SourceType]
	}

	showTypeList := []string{"ets", "answer_rate", "etp", "queue"}
	req := &AthenaApiv3.AthenaBubbleExpectInfoReq{
		AccessKeyID:        &accessKeyID,
		AppVersion:         &appVersion,
		Lang:               &lang,
		Phone:              &phone,
		CityID:             &cityID,
		FromLng:            &fromLng,
		FromLat:            &fromLat,
		FromName:           &fromName,
		Pid:                &pid,
		ToLng:              &toLng,
		ToLat:              &toLat,
		ToName:             &toName,
		ClientType:         &clientType,
		Channel:            &channel,
		OrderID:            &highOrderID,
		CanAddProduct:      canAddProductList,
		AlreadySendProduct: alreadySendProductList,
		EstimateTraceID:    &estimateTraceId,
		ExpectScene:        expectScene,
		ShowTypeList:       showTypeList,
	}

	if c.req.GetSourceType() == consts.SourceTypeClassifyTab || c.req.GetSourceType() == consts.SourceTypeAnyCarEstimate {
		req.ExtraInfo = c.buildBargainRangePrice()
	}

	return req
}

func (c *Service) buildCompensationInfo(ctx context.Context) {
	if !c._preCheck(ctx) {
		return
	}

	req := c._buildCompensationReq(ctx)
	resp, err := compensation.GetLightMultiCompensationAbility(ctx, req)
	if err != nil {
		return
	}

	if resp == nil || resp.Data == nil || resp.Data.CompensationInfo == nil {
		return
	}

	data := resp.Data.CompensationInfo

	// 解析 NormalCompensationInfo
	if res, ok := data["normal_no_answer_compensation"]; ok && res != nil {
		bytes, err := jsoniter.Marshal(res)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to marshal, res %+v", res)
			return
		}

		info := &NormalCompensation{}
		if err := json.Unmarshal(bytes, info); err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to unmarshal, res %+v", res)
			return
		}
		c.NormalCompensationInfo = info
	}

	// 解析 DefaultSelectedCompensationInfo
	if res, ok := data["default_selected_compensation"]; ok && res != nil {
		bytes, err := jsoniter.Marshal(res)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to marshal, res %+v", res)
			return
		}

		info := &DefaultSelectedCompensation{}
		if err = json.Unmarshal(bytes, info); err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to unmarshal, res %+v", res)
			return
		}
		c.DefaultSelectedCompensationInfo = info
	}

	return
}

// todo: 这里要更新下
func (c *Service) _preCheck(ctx context.Context) bool {
	if c.req == nil || c.UserInfo == nil || c.CityInfo == nil {
		return false
	}

	// 大字版屏蔽无车赔
	if c.req.GetFontScaleType() != 0 {
		return false
	}

	// 新竹订单屏蔽无车赔
	if c.OrderInfo != nil && util.IsFromXinZhuWithBusinessTravelOrderType(c.OrderInfo.ExtendFeatureParsed.BusinessTravelOrderType) {
		return false
	}

	if len(c.selectPcIds)+len(c.unselectPcIds) == 0 {
		return false
	}

	if cast.ToInt(c.req.SourceType) == consts.SourceTypeAnyCarEstimate {
		if formRealData, ok := c.TransDataMap["form_real_data"]; ok && formRealData != nil && util.ToString(formRealData) != "" {
			data := map[string]interface{}{}
			json.Unmarshal([]byte(util.ToString(formRealData)), &data)
			if isWaitAnswerUpdate, ok := data["is_wait_answer_upgrade"]; ok && isWaitAnswerUpdate != nil {
				if isWaitAnswerUpdate != 1 {
					return false
				}
			}
		}
	}

	return true
}

func (c *Service) _buildCompensationReq(ctx context.Context) *Compensation.GetLightMultiCompensationAbilityReq {
	// 追加车型表单
	var fromType int32
	if c.req.SourceType != nil && *c.req.SourceType == consts.SourceTypeAnyCarEstimate {
		fromType = FromTypeAnyCarEstimate
	}
	req := &Compensation.GetLightMultiCompensationAbilityReq{
		// 业务
		CompensationBusiness: []string{"normal_no_answer_compensation", "default_selected_compensation"},
		Caller:               "get_form_real_data",
		BubbleTraceId:        util.String2PtrString(c.req.EstimateTraceId),

		// 位置
		Area:     c.CityInfo.FromCityId,
		ToArea:   c.CityInfo.ToCityId,
		FromLat:  util.ToString(c.req.FromLat),
		FromLng:  util.ToString(c.req.FromLng),
		ToLat:    util.ToString(c.req.ToLat),
		ToLng:    util.ToString(c.req.ToLng),
		FromName: util.StringPtr2String(c.req.FromName),
		ToName:   util.StringPtr2String(c.req.ToName),

		// 公参
		Lang:        c.req.Lang,
		AppVersion:  c.req.Appversion,
		AccessKeyId: util.ToInt32(c.req.AccessKeyId),
		ClientType:  c.req.ClientType,

		// 用户
		Phone:       c.UserInfo.Phone,
		PassengerId: util.ToString(c.UserInfo.PID),
		FromType:    &fromType,
	}

	if c.req.OrderType != nil {
		req.OrderType = *c.req.OrderType
	}

	// 预估页请求，再来一单场景下，端会传缓存的oid。需要在预估场景下屏蔽订单信息。
	if cast.ToInt(c.req.SourceType) != consts.SourceTypeNormalTab && cast.ToInt(c.req.SourceType) != consts.SourceTypeClassifyTab {
		if c.OrderInfo != nil {
			req.CallCarType = util.ToInt32(c.OrderInfo.CallCar)
			if c.req.OrderType == nil {
				req.OrderType = util.ToInt32(c.OrderInfo.Type)
			}
		}

		if c.req.Oid != nil {
			oidStr := *c.req.Oid
			oid, district, _ := util.DecodeOrderID(oidStr)
			req.District = util.StringPtr(district)
			req.OrderId = util.Int64Ptr(oid)
		}
	}

	req.ProductCategoryIds = make([]string, 0, len(c.selectPcIds))
	for _, pc := range c.selectPcIds {
		req.ProductCategoryIds = append(req.ProductCategoryIds, util.ToString(pc))
	}

	estimateProducts := make([]*Compensation.SelectEstimateItem, 0, len(c.selectPcIds))

	for _, product := range c.reqMultiProduct {
		if product == nil {
			continue
		}

		if product.Select != nil && *product.Select == 0 {
			continue
		}

		item := &Compensation.SelectEstimateItem{
			ProductCategory: product.ProductCategory,
			EstimateId:      util.StringPtr(product.EstimateID),
		}

		estimateProducts = append(estimateProducts, item)
	}

	req.SelectEstimateProducts = estimateProducts

	return req
}

func (c *Service) buildEstimateTimeInfo(ctx context.Context) {

	athenaReq := c.buildAthenaETDReq(ctx)
	if athenaReq == nil {
		return
	}
	resp := athena.GetMultiEstimateEtpEtaInfo(ctx, athenaReq)

	if resp == nil {
		return
	}

	c.EtdInfo = resp.GetEstimateEtpEtdInfo()
}

func (c *Service) buildAthenaETDReq(ctx context.Context) *AthenaApiv3.AthenaMultiEstimateEtpEtdInfoReq {
	if c.req == nil || c.UserInfo == nil || c.CityInfo == nil {
		return nil
	}

	estimateProducts := make([]*AthenaApiv3.EstimateInfoProductItem, 0, len(c.selectPcIds))
	for i := 0; i < len(c.reqMultiProduct); i++ {
		if c.reqMultiProduct[i] == nil {
			continue
		}

		if c.reqMultiProduct[i].Select != nil && *c.reqMultiProduct[i].Select == 0 {
			continue
		}

		estimateProducts = append(estimateProducts, &AthenaApiv3.EstimateInfoProductItem{
			BubbleId:        c.reqMultiProduct[i].EstimateID,
			BusinessId:      c.reqMultiProduct[i].BusinessID,
			RequireLevel:    c.reqMultiProduct[i].RequireLevel,
			ComboType:       c.reqMultiProduct[i].ComboType,
			ProductCategory: int32(c.reqMultiProduct[i].ProductCategory),
			LevelType:       c.reqMultiProduct[i].LevelType,
		})
	}

	if len(estimateProducts) == 0 {
		// 无品类不请求athena
		return nil
	}

	return &AthenaApiv3.AthenaMultiEstimateEtpEtdInfoReq{
		EstimateInfoProducts: estimateProducts,
		Pid:                  cast.ToString(c.UserInfo.PID),
		Phone:                c.UserInfo.Phone,
		CityId:               c.CityInfo.FromCityId,
		AppVersion:           c.req.Appversion,
		ClientType:           cast.ToString(c.req.ClientType),
		Channel:              c.req.GetChannel(),
		Lang:                 c.req.Lang,
		FLat:                 c.req.FromLat,
		FLng:                 c.req.FromLng,
		TLat:                 c.req.ToLat,
		TLng:                 c.req.ToLng,
		FromName:             c.req.FromName,
		ToName:               c.req.ToName,
		ExtraInfo:            nil,
	}
}

func (c *Service) buildIsToEtp(ctx context.Context) {
	if c.GlobalSceneExpect != nil && c.GlobalSceneExpect.ExtraInfo != nil {
		if v, ok := c.GlobalSceneExpect.ExtraInfo["is_shield_wuchepei"]; ok && cast.ToBool(v) {
			return
		}
	}

	if c.NormalCompensationInfo != nil && (c.NormalCompensationInfo.Status == 0 || c.NormalCompensationInfo.Status == 1) {
		return
	}

	cityId := 0
	if c.CityInfo != nil {
		cityId = int(c.CityInfo.FromCityId)
	}

	apolloParam := map[string]string{
		common.Pid:         cast.ToString(c.UserInfo.PID),
		common.City:        cast.ToString(cityId),
		common.AccessKeyId: cast.ToString(c.req.AccessKeyId),
		common.AppVersion:  cast.ToString(c.req.Appversion),
		"phone":            c.UserInfo.Phone,
		"form_style_exp":   cast.ToString(c.req.FormStyleExp),
		"source_type":      cast.ToString(c.req.GetSourceType()),
		"tab_id":           "classify",
	}
	toggleAllow := Apollo.FeatureToggle(ctx, "ets_to_etp_toggle", cast.ToString(c.UserInfo.PID), apolloParam)
	if !toggleAllow {
		return
	}
	if util.CompareAppVersion(c.req.Appversion, "6.6.75") < 0 && util.InArrayInt64(c.req.AccessKeyId, []int64{9, 22}) {
		return
	}
	if util.CompareAppVersion(c.req.Appversion, "6.6.15") < 0 && util.InArrayInt64(c.req.AccessKeyId, []int64{1, 2}) {
		return
	}
	c.IsToEtp, c.Cfg = Apollo.FeatureGaia(ctx, "ets_to_etp", apolloParam, "hit_ets_to_etp")
	return
}

// 获取自选车价格range的价格信息
func (c *Service) buildBargainRangePriceInfo(req *AthenaApiv3.AthenaUserRecommendInfoReq) {
	if req.ExtraInfo == nil {
		req.ExtraInfo = map[string]string{}
	}
	switch c.req.GetSourceType() {
	case consts.SourceTypeBargainRangeBubble:
		req.ExtraInfo["stage"] = consts.AthenaUserRecStageRangeBubble
		if c.req.BargainRangePriceInfo != nil && c.req.BargainRangePriceInfo.RecommendPrice != nil {
			req.ExtraInfo["recommend_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.req.BargainRangePriceInfo.RecommendPrice)))
		}
	case consts.SourceTypeBargainRangeAnyCar:
		req.ExtraInfo["stage"] = consts.AthenaUserRecStageRangeAnycar
	}
	if c.req.BargainRangePriceInfo != nil {
		// athena入参的价格单位为分。入参的单位都是元，须处理
		if c.req.BargainRangePriceInfo.UpperPrice != nil {
			req.ExtraInfo["upper_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.req.BargainRangePriceInfo.UpperPrice)))
		}
		if c.req.BargainRangePriceInfo.LowerPrice != nil {
			req.ExtraInfo["lower_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.req.BargainRangePriceInfo.LowerPrice)))
		}
		if c.req.BargainRangePriceInfo.UpperSliderPrice != nil {
			req.ExtraInfo["upper_slider_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.req.BargainRangePriceInfo.UpperSliderPrice)))
		}
		if c.req.BargainRangePriceInfo.LowerSliderPrice != nil {
			req.ExtraInfo["lower_slider_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.req.BargainRangePriceInfo.LowerSliderPrice)))
		}
		if c.req.BargainRangePriceInfo.FastCarEstimateFee != nil {
			req.ExtraInfo["fast_car_estimate_fee"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.req.BargainRangePriceInfo.FastCarEstimateFee)))
		}
		if c.req.BargainRangePriceInfo.SpFastCarEstimateFee != nil {
			req.ExtraInfo["sp_fast_car_estimate_fee"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.req.BargainRangePriceInfo.SpFastCarEstimateFee)))
		}
	}
}

// 填充自选车价格range的价格信息
func (c *Service) buildBargainRangePrice() map[string]string {
	var resp = map[string]string{}
	if c.BargainRangePriceInfo == nil {
		return resp
	}

	switch c.req.GetSourceType() {
	case consts.SourceTypeBargainRangeBubble, consts.SourceTypeClassifyTab:
		resp["stage"] = consts.AthenaUserRecStageRangeBubble
		if c.BargainRangePriceInfo != nil && c.BargainRangePriceInfo.RecommendPrice != nil {
			resp["recommend_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.BargainRangePriceInfo.RecommendPrice)))
		}
	case consts.SourceTypeBargainRangeAnyCar:
		resp["stage"] = consts.AthenaUserRecStageRangeAnycar
	}

	// athena入参的价格单位为分。入参的单位都是元，须处理
	if c.BargainRangePriceInfo.UpperPrice != nil {
		resp["upper_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.BargainRangePriceInfo.UpperPrice)))
	}
	if c.BargainRangePriceInfo.LowerPrice != nil {
		resp["lower_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.BargainRangePriceInfo.LowerPrice)))
	}
	if c.BargainRangePriceInfo.UpperSliderPrice != nil {
		resp["upper_slider_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.BargainRangePriceInfo.UpperSliderPrice)))
	}
	if c.BargainRangePriceInfo.LowerSliderPrice != nil {
		resp["lower_slider_price"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.BargainRangePriceInfo.LowerSliderPrice)))
	}
	if c.BargainRangePriceInfo.FastCarEstimateFee != nil {
		resp["fast_car_estimate_fee"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.BargainRangePriceInfo.FastCarEstimateFee)))
	}
	if c.BargainRangePriceInfo.SpFastCarEstimateFee != nil {
		resp["sp_fast_car_estimate_fee"] = cast.ToString(util.Float64Hundred(cast.ToFloat64(c.BargainRangePriceInfo.SpFastCarEstimateFee)))
	}

	return resp
}

// 获取透传字段的ets信息
func (c *Service) buildMinEts(req *AthenaApiv3.AthenaUserRecommendInfoReq) {
	if req.ExtraInfo == nil {
		req.ExtraInfo = map[string]string{}
	}
	if c.TransDataMap != nil {
		req.ExtraInfo["min_ets"] = cast.ToString(c.TransDataMap["ets"])
	}
}

func (c *Service) LogPublic(ctx context.Context, data *proto.FormRealData) {
	logInfo := make(map[string]interface{})
	if c.PriceDiffInfo != nil && data.PriceDiffInfo != nil {
		logInfo["called_max_price"] = c.PriceDiffInfo.calledMaxPrice
		logInfo["calling_max_price"] = c.PriceDiffInfo.callingMaxPrice
		logInfo["diff_value"] = c.PriceDiffInfo.diffAtMax
		logInfo["price_diff_tag"] = data.PriceDiffInfo.Text
	}

	if len(logInfo) > 0 {
		log.Public.Public(ctx, "g_form_get_real_data", logInfo)
	}
}

func (c *Service) getFromHeadRule(ctx context.Context, sourceType int32) (*proto.FormHeadRule, int32) {
	if c.req.GetRecForm() == 1 {
		return c.getFromHeadRule4RecForm(ctx, sourceType)
	}
	return c.getFromHeadRule4NormalCompensation(ctx, sourceType), CompensationResult
}

func (c *Service) getRecBubble(ctx context.Context, sourceType int32) *proto.RecBubble {
	if !util.InArrayInt32(sourceType,
		[]int32{consts.SourceTypeAnyCarEstimate, consts.SourceTypeAnyCarRecBox, consts.SourceTypeAnyCarRecBoxSingle}) {
		return nil
	}

	return c.getRecBubble4OrderMatch(ctx)
}
