package get_form_real_data

import (
	"context"
	"crypto/md5"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_unione"
	"math"
	"sort"
	"strconv"
	"strings"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/business_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	trace "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/s3e/x-engine/material"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
)

type PriceDiffInfo struct {
	calledMaxPrice  float64 //已呼叫车型的价格最大值
	callingMaxPrice float64 //计划呼叫车型的价格最大值
	diffAtMax       float64 //基于最大值的diff价格

	_hitGroup string //apollo命中的group，实验后下线
}

func (c *Service) preHitShowPriceTag(ctx context.Context) (hit bool, paramsSet map[string]string) {
	if c.UserInfo == nil || c.CityInfo == nil {
		return false, nil
	}
	pidStr := cast.ToString(c.UserInfo.PID)
	apolloParams := map[string]string{
		"phone": c.UserInfo.Phone,
		"city":  cast.ToString(c.CityInfo.FromCityId),
		"pid":   pidStr,
	}

	allow, params := apollo.GetParameters("ab_anycar_estimate_price_diff_info", pidStr, apolloParams)
	if !allow {
		return false, nil
	}

	if params["ratio"] == "" || params["cap"] == "" || params["group"] == "" {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "buildWaitAnswerPriceDiff get invalid apolloParams")
		return false, nil
	}

	return true, params
}

func (c *Service) buildWaitAnswerPriceDiff(ctx context.Context) {
	if c.OrderInfo == nil || len(c.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct) == 0 {
		return
	}

	hit, apolloParams := c.preHitShowPriceTag(ctx)
	if !hit {
		return
	}

	fCallingCarPrice := c.maxCallingCarPrice(ctx)
	if fCallingCarPrice <= 0 {
		return
	}

	fCalledCarPrice := c.maxCalledCarPrice(ctx)
	if fCalledCarPrice <= 0 {
		return
	}

	ratio, _ := strconv.Atoi(apolloParams["ratio"])
	capValue, _ := strconv.ParseFloat(apolloParams["cap"], 64)

	//当待呼叫车型比已呼叫车型贵的比较多，多过某些阈值时，就不展示
	diffValue := fCallingCarPrice - fCalledCarPrice
	if fCallingCarPrice > fCalledCarPrice && (diffValue > capValue || diffRatio(fCallingCarPrice, fCalledCarPrice) > ratio) {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "check price rule fail with calledPrice %v and callingPrice %v", fCalledCarPrice, fCallingCarPrice)
		return
	}

	c.PriceDiffInfo = &PriceDiffInfo{
		callingMaxPrice: fCallingCarPrice,
		calledMaxPrice:  fCalledCarPrice,
		diffAtMax:       diffValue,
		_hitGroup:       apolloParams["group"],
	}
}

type MaterialStruct struct {
	Text string `json:"text"`
}

func (c *Service) buildDiffPriceTag(ctx context.Context, component string) *proto.PriceDiffInfo {
	if c.PriceDiffInfo == nil {
		return nil
	}

	var status string
	if c.PriceDiffInfo.diffAtMax == 0 {
		status = "equal"
	} else if c.PriceDiffInfo.diffAtMax < 0 {
		status = "less"
	} else {
		status = "more"
	}

	materialConfig := map[string]string{
		"diff_amount": util.FormatPrice(math.Abs(c.PriceDiffInfo.diffAtMax), -1),
		"status":      status,
	}

	mC := material.NewDefaultComponentMaterial("anycar_estimate_price_diff_tag", "wait_answer", component, c.PriceDiffInfo._hitGroup)
	mC.SetCondition(materialConfig)

	var conf = &MaterialStruct{}
	err := mC.RenderContent(conf)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get material with err %v and conf = nil :%v", err, conf == nil)
	}

	return &proto.PriceDiffInfo{
		Text: conf.Text,
	}
}

func diffRatio(a, b float64) int {
	return int(100 * (a - b) / b)
}

// 已呼叫车型的最大值
func (c *Service) maxCalledCarPrice(ctx context.Context) float64 {
	if c.OrderInfo == nil || len(c.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct) == 0 {
		return 0
	}

	estimateList := make([]string, 0)
	for _, p := range c.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct {
		estimateList = append(estimateList, p.EstimateID)
	}

	sort.Strings(estimateList)
	redisKey := fmt.Sprintf("%s_%x", QuotationCachePrefix, md5.Sum([]byte(strings.Join(estimateList, "."))))
	reply, redisErr := redis.GetEstimateClient().Get(ctx, redisKey) //优先从redis取已勾选车型的最大值
	if redisErr == nil && reply != "" {
		fValue, convErr := strconv.ParseFloat(reply, 64)
		if convErr == nil {
			return fValue
		}
	}

	quotationBatchInfo, err := price_api.GetQuotationExpands(ctx, estimateList)
	if err != nil || len(quotationBatchInfo) == 0 {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "fail to get quotation for called cars with error %v", err)
		return 0
	}

	var maxValue float64
	var hasBusinessPayment, hasMeteredUnione bool
	for _, qtList := range quotationBatchInfo {
		if len(qtList) >= 1 {
			qt := qtList[0]
			if qt.DefaultPayType == consts.BusinessPaymentType {
				hasBusinessPayment = true
				break
			}

			if qt.ProductId == consts.ProductIDUNITAXI {
				productInfo := &ProductInfo{
					productCategory: qt.ProductCategory,
					orderInfo:       c.OrderInfo,
					userInfo:        c.UserInfo,
					req:             c.req,
				}

				if !category_unione.CheckTaxiShowEstimateFee(ctx, productInfo) {
					hasMeteredUnione = true
					break
				}
			}
			if qt.ProductCategory == estimate_pc_id.EstimatePcIdHuiXuanCar && qt.FastRangeRecUpper != nil {
				maxValue = math.Max(maxValue, *qt.FastRangeRecUpper)
			} else {
				maxValue = math.Max(maxValue, qt.EstimateFee)
			}
		}
	}

	if hasBusinessPayment || hasMeteredUnione {
		maxValue = 0
	} else if maxValue == 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to parse called car fee_amount")
	}

	_, redisErr = redis.GetEstimateClient().SetEx(ctx, redisKey, CalledCarsPriceExpire, maxValue)
	if redisErr != nil {
		log.Trace.Warnf(ctx, "redis_error", "fail to set redis with key %v and value %v", redisKey, maxValue)
	}

	return maxValue
}

// 计划呼叫车型的最大值
func (c *Service) maxCallingCarPrice(ctx context.Context) float64 {
	var hasBusinessPayment, hasMeteredUniOne bool
	var maxValue float64
	for _, p := range c.reqMultiProduct {
		if p == nil {
			continue
		}

		if p.Select != nil && *p.Select == 0 {
			continue
		}

		if p.ProductCategory == estimate_pc_id.EstimatePcIdHuiXuanCar && c.BargainRangePriceInfo != nil && c.BargainRangePriceInfo.UpperPrice != nil {
			maxValue = math.Max(maxValue, float64(*c.BargainRangePriceInfo.UpperPrice))
		} else {
			maxValue = math.Max(maxValue, p.FeeAmount)
		}
		//是否包含企业付
		if p.PaymentID == consts.BusinessPaymentType {
			hasBusinessPayment = true
			break
		}

		//是否包含打表计价出租车
		if p.BusinessID == business_id.BusinessIdUnitaxiCar {
			productInfo := &ProductInfo{
				productCategory: p.ProductCategory,
				req:             c.req,
				orderInfo:       c.OrderInfo,
				userInfo:        c.UserInfo,
			}

			if !category_unione.CheckTaxiShowEstimateFee(ctx, productInfo) {
				hasMeteredUniOne = true
				break
			}
		}
	}

	//包含企业付或者打表计价出租车时, 不出
	if hasBusinessPayment || hasMeteredUniOne {
		return 0
	}

	//理论上不该有是0 的情况，特殊记录一下
	if maxValue == 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to parse calling car fee_amount")
	}

	return maxValue
}

type ProductInfo struct {
	productCategory int64
	orderInfo       *dos.OrderInfo
	req             *proto.PGetFormRealDataReq
	userInfo        *passport.UserInfo
}

func (p *ProductInfo) GetProductCategory() int64 {
	return p.productCategory
}

func (p *ProductInfo) GetBillDriverMetre() int64 {
	return cast.ToInt64(p.orderInfo.StartDestDistance)
}

func (p *ProductInfo) ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string) {
	ap := apollo_model.ParamsConnector{
		City:        p.orderInfo.Area,
		Phone:       p.orderInfo.PassengerPhone,
		UID:         cast.ToString(p.userInfo.UID),
		PID:         cast.ToString(p.userInfo.PID),
		Lang:        p.req.Lang,
		AccessKeyID: cast.ToString(p.req.AccessKeyId),
		AppVersion:  cast.ToString(p.req.Appversion),
	}

	ap.SetCounty(cast.ToString(p.orderInfo.County)).
		SetProductCategory(cast.ToString(p.GetProductCategory())).
		SetMenuID("dache_anycar").
		SetIsCrossCity(cast.ToString(cast.ToInt64(p.orderInfo.Area != p.orderInfo.ToArea)))

	return ap.ApolloParamsGen(keyFunc, paramsFunc...)
}
