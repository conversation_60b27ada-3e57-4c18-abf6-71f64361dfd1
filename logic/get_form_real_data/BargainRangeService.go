package get_form_real_data

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"math"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/bargain_range/bargain_range_estimate"
	"github.com/spf13/cast"
)

func (c *Service) getBargainRangeAnswerRateInfoByAnycar() *proto.BargainRangeAnswerRateInfo {
	if c.StripInfo == nil || c.StripInfo.AnswerRate == nil || *c.StripInfo.AnswerRate <= 0 {
		return nil
	}
	res := new(proto.BargainRangeAnswerRateInfo)
	res.AnswerRate = c.StripInfo.AnswerRate
	return res
}
func (c *Service) getBargainRangeAnswerRateInfoByBubble(ctx context.Context) *proto.BargainRangeAnswerRateInfo {
	var (
		dcmpMap map[string]string
	)

	var answerRate int

	if c.req.GetSourceType() == consts.SourceTypeClassifyTab || c.req.GetSourceType() == consts.SourceTypeNormalTab {
		if answerRate = c.getBargainRangeAnswerRateOnClassifyTab(); answerRate == 0 {
			return nil
		}
	} else {
		if c.StripInfo == nil || c.StripInfo.AnswerRate == nil || *c.StripInfo.AnswerRate <= 0 {
			return nil
		}
		answerRate = int(*c.StripInfo.AnswerRate)
	}

	template := dcmp.GetDcmpPlainContent(ctx, "bargain_range-estimate_info")
	if err := json.Unmarshal([]byte(template), &dcmpMap); err != nil {
		return nil
	}
	res := new(proto.BargainRangeAnswerRateInfo)
	res.AnswerRateText = util.ReplaceTag(ctx, dcmpMap["answer_rate_text"], map[string]string{
		"num": cast.ToString(answerRate),
	})
	//不同应答率展示不同颜色
	res.AnswerRateColor = bargain_range_estimate.GetAnswerRateColor(dcmpMap, int32(answerRate))
	return res
}

func (c *Service) getBargainRangeAnswerRateOnClassifyTab() int {
	if c.ProductExpectInfo == nil || len(c.ProductExpectInfo.ProductInfos) == 0 {
		return 0
	}

	for _, productItem := range c.ProductExpectInfo.ProductInfos {
		if productItem == nil || productItem.ProductCategory == nil || *productItem.ProductCategory != estimate_pc_id.EstimatePcIdHuiXuanCar {
			continue
		}
		if productItem.ExpectInfo == nil || productItem.ShowType == nil || AnswerRate != *productItem.ShowType {
			return 0
		}
		return int(math.Round(productItem.ExpectInfo.GetAnswerRate() * 100))
	}
	return 0
}
