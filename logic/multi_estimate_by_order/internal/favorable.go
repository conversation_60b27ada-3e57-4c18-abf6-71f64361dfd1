package internal

import (
	"context"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/multi_estimate_by_order"
)

const FavorablePricePcID = "328" //惠选车pcid=328

// FavorablePrice 惠选车价格-一口价
type FavorablePrice struct {
	base
}

func (f *FavorablePrice) Name(ctx context.Context) string {
	return "favorable_price"
}

func NewFavorableDispatch() Dispatcher {
	return &FavorablePrice{}
}

func (f *FavorablePrice) Selected(ctx context.Context, serviceReq *model.ServiceRequest) bool {
	if serviceReq == nil || serviceReq.OrderInfo == nil || serviceReq.OrderInfo.EstimatePcId == nil {
		return false
	}
	if *serviceReq.OrderInfo.EstimatePcId != FavorablePricePcID {
		return false
	}
	return true
}

func (f *FavorablePrice) Render(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull) (*proto.PEstimateByOrderData, NewErrors.BizError) {
	return multi_estimate_by_order.RenderByFavorable(ctx, serviceReq)
}

func (f *FavorablePrice) WritePublicLog(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull, resp *proto.PEstimateByOrderData) public_log.WritePublicFunc {
	return func() {
		if serviceReq == nil || serviceReq.QuotationInfo == nil || serviceReq.Req == nil || resp == nil {
			return
		}

		logInfo := make(map[string]interface{})
		logInfo["product_category"] = serviceReq.QuotationInfo.GetProductCategory()
		logInfo["area"] = serviceReq.QuotationInfo.Area

		logInfo["estimate_id"] = serviceReq.QuotationInfo.EstimateId
		logInfo["estimate_fee"] = resp.EstimateFee
		logInfo["fee_type"] = resp.FeeType

		logInfo["access_key_id"] = serviceReq.Req.AccessKeyId
		logInfo["app_version"] = serviceReq.Req.AppVersion
		logInfo["oid"] = serviceReq.Req.Oid

		logInfo["dispatch"] = consts.Favorable

		log.Public.Public(ctx, public_log.GEstimateByOrder, logInfo)
	}
}
