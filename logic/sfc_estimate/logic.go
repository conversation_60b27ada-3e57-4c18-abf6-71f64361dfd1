// Package sfc_estimate
package sfc_estimate

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/baichuanRpc"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_price_filter"
	"math"
	"math/rand"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	apollo2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/risk"

	Dirpc_SDK_Feeds "git.xiaojukeji.com/dirpc/dirpc-go-http-Feeds"
	BeatlesUser "git.xiaojukeji.com/gulfstream/beatles-common/model/user"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/adx"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/casper"
	ufsV2 "git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/sfc_car"
	"git.xiaojukeji.com/nuwa/trace"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	DirpcDcmp "git.xiaojukeji.com/dirpc/dirpc-go-dcmp/v2"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/beatles-common/constant"
	beatlesModel "git.xiaojukeji.com/gulfstream/beatles-common/model"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/apollo"
	beatlesApollo "git.xiaojukeji.com/gulfstream/beatles-common/model/apollo"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/coupon"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/notes"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/order"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/psgtimepicker"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/scheme"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/ufs"
	BeatlesUtil "git.xiaojukeji.com/gulfstream/beatles-common/util"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	apolloUtil "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/ddmq"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/business_estimate/mq"
	"git.xiaojukeji.com/gulfstream/mamba/logic/sfc_estimate/internal"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_dds_filter"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

const (
	LOG_TAG          = "sfc"
	LOG_WYC_SIDE     = "wyc_estimate_side"
	LOG_HISTORY_LIST = "wyc_history_list"
	LOG_SFC_GUESS    = "guess"
	Animation_Url    = "animation_url"
	AnyCar           = 372

	ControlGroup    = "control_group"
	TreatmentGroup  = "treatment_group"
	TreatmentGroup2 = "treatment_group_2"
	TreatmentGroup3 = "treatment_group_3"
	AirType         = "airport_type"
	RailwayType     = "railway_type"
	NoAirOrRailType = 0 //非场站
	IsAirType       = 2 //终点机场
	ISRailwayType   = 2 //终点车站
	ReAppointment   = "re_appointment"
)

// Logic 数据
type Logic struct {
	generator     *biz_runtime.ProductsGenerator
	passengerNums int32
	maxNum        int32 //可选择最大座位数
	dcmpData      proto.SFCEstimateDcmp
	userInfo      passport.UserInfo
	traceID       string
	request       proto.SFCEstimateRequest
	extraParams   *proto.ExtraParams
	login         bool
	policy        *baichuanRpc.Privacy // 百川政策
}

type AdditionalInfo struct {
	Title      string `json:"title"`
	LegacyType string `json:"legacy_type"`
	AuthUrl    string `json:"auth_url"`
	LegacyText string `json:"legacy_text"`
}

type MultiRequireProduct struct {
	ProductCategory int32 `json:"product_category"`
	IsSelected      int32 `json:"is_selected"`
}

type IntercityDcmp struct {
	AdditionalTime int64          `json:"additional_time"`
	TimeFactor     int64          `json:"time_factor"`
	Subtitle       string         `json:"subtitle"`
	Cartitle       []string       `json:"cartitle"`
	FeeMsg         string         `json:"fee_msg"`
	Caricon        string         `json:"caricon"`
	BasicUrl       string         `json:"basic_url"`
	Additional     AdditionalInfo `json:"additional_info"`
	WycAdditional  AdditionalInfo `json:"wyc_additional"`
}

// NewLogic 数据处理
func NewLogic(ctx context.Context, request *proto.SFCEstimateRequest, traceId string) (*Logic, error) {
	var (
		err            error
		userInfo       *passport.UserInfo
		dcmpData       proto.SFCEstimateDcmp
		departureTime  int64
		departureRange models.DepartureRange
		extraInputMap  map[string]interface{} //品类查询会用用掉的额外入参
	)

	extraInputMap = make(map[string]interface{})

	//风控
	sfcRiskReq := buildSFCRiskReq(ctx, request)
	if sfcRiskReq == nil {
		return nil, BizError.ErrCheckRiskFailed
	}
	sfcRiskCode, riskErr := risk.HackSfcEstimatePrice(ctx, sfcRiskReq)
	if errors.Is(riskErr, risk.ErrRiskHack) {
		return nil, BizError.ErrCheckRiskFailed
	}
	extraInputMap["sfc_risk_code"] = sfcRiskCode

	//参数校验
	checkParams := func(request *proto.SFCEstimateRequest) error {
		if request.FromLat == 0 || request.FromLng == 0 || request.ToLat == 0 || request.ToLng == 0 {
			log.Trace.Warnf(ctx, LOG_TAG, "参数校验")
			return BizError.ErrInvalidArgument
		}
		return nil
	}
	if err = checkParams(request); err != nil {
		return nil, err
	}

	// 扩展参数
	var extraParams proto.ExtraParams
	if len(request.GetExtraParams()) > 0 {
		err = json.Unmarshal([]byte(request.GetExtraParams()), &extraParams)
		if err != nil {
			log.Trace.Warnf(ctx, LOG_TAG, "ExtraParams err=%v", err)
		}
	}

	//用户信息获取
	userInfo = &passport.UserInfo{
		PID:      0,
		UID:      0,
		Phone:    "",
		Role:     1,
		Channel:  "",
		OriginId: "",
	}
	login := true
	if len(request.Token) == 0 {
		if allow := apollo.MiniLoginBehind(ctx, cast.ToString(request.GetAccessKeyId()), request.GetAppVersion()); !allow {
			return nil, BizError.ErrNotLogin
		}
		login = false
	} else {
		res, err := passport.GetUserInfo(ctx, request.Token, "")
		if err != nil || res == nil {
			log.Trace.Warnf(ctx, LOG_TAG, "user err=%+v", err)
			if allow := apollo.MiniLoginBehind(ctx, cast.ToString(request.GetAccessKeyId()), request.GetAppVersion()); !allow {
				if err == passport.ErrOffline {
					return nil, BizError.ErrNotLogin
				} else {
					return nil, BizError.ErrSystem
				}
			}
			login = false
		} else {
			userInfo = res
		}
	}

	//文案获取
	dcmpStr := DirpcDcmp.GetContent(ctx, "sfc-estimate_text", "zh-CN", nil)
	errJSON := json.Unmarshal([]byte(dcmpStr), &dcmpData)
	if errJSON != nil {
		log.Trace.Warnf(ctx, "sfc-dcmp", "err=%+v", errJSON)
		return nil, errJSON
	}

	//最大座位数获取
	maxNum := dcmpData.SeatInfo.MaxNum
	dealPassengerNums := func(ctx context.Context, request *proto.SFCEstimateRequest, maxNum int32) int32 {
		// 端不传乘客人数时，默认用1
		if request.PassengerNums == nil || *request.PassengerNums == int32(0) {
			return 1
		} else if *request.PassengerNums > maxNum {
			return maxNum
		}
		return *request.PassengerNums
	}
	PassengerNums := dealPassengerNums(ctx, request, maxNum)
	if PassengerNums == 0 {
		return nil, BizError.ErrInvalidArgument
	}

	//出发时间获取
	timeRange := request.GetDepartureRange()
	if len(timeRange) > 1 && timeRange[0] > 0 && timeRange[1] > 0 {
		departureTime = timeRange[0]
		departureRange = models.DepartureRange{
			From: time.Unix(timeRange[0], 0),
			To:   time.Unix(timeRange[1], 0),
		}
	} else if len(timeRange) == 1 && timeRange[0] > 0 { //目前时间相等也会传两个值，以下代码需验证
		departureTime = timeRange[0]
		departureRange = models.DepartureRange{
			From: time.Unix(timeRange[0], 0),
			To:   time.Unix(timeRange[0], 0),
		}
	} else if request.GetDepartureTime() > int64(0) { //目前没有不传departureRange的情况，以下代码需验证
		departureTime = request.GetDepartureTime()
		departureRange = models.DepartureRange{
			From: time.Unix(departureTime, 0),
			To:   time.Unix(departureTime, 0),
		}
	} else {
		departureTime = time.Now().Unix()
		departureRange = models.DepartureRange{
			From: time.Now(),
			To:   time.Now(),
		}
	}

	//品类查询
	productsGen, err := getProductInfo(ctx, departureRange, userInfo, request, departureTime, PassengerNums, dcmpData, extraInputMap)
	if err != nil {
		return nil, err
	}

	return &Logic{
		generator:     productsGen,
		passengerNums: PassengerNums,
		maxNum:        maxNum,
		dcmpData:      dcmpData,
		userInfo:      *userInfo,
		traceID:       traceId,
		request:       *request,
		extraParams:   &extraParams,
		login:         login,
		policy:        GetPrivacyNotSignFromBaiChuan(ctx, request.AccessKeyId, int64(userInfo.UID), request.AppVersion), // 获取百川政策
	}, nil
}

func getProductInfo(ctx context.Context, departureRange models.DepartureRange, userInfo *passport.UserInfo, request *proto.SFCEstimateRequest, departureTime int64, PassengerNums int32, dcmpData proto.SFCEstimateDcmp, extraMap map[string]interface{}) (*biz_runtime.ProductsGenerator, error) {
	var (
		err         error
		baseReqData *models.BaseReqData
		sfcRiskCode int32
	)
	// platformType代表端类型，1ios，2安卓，3小程序
	platformType := request.AccessKeyId
	if request.AccessKeyId == 1 || request.AccessKeyId == 2 {
		platformType = request.AccessKeyId
	} else {
		platformType = 3
	}
	pageType := 35
	if baseReqData, err = models.NewBaseReqDataBuilder().
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(userInfo.UID),
			PID:      int64(userInfo.PID),
			Phone:    userInfo.Phone,
			Role:     userInfo.Role,
			Channel:  userInfo.Channel,
			UserType: 1, //默认成人
			OriginID: userInfo.OriginId,
		}).
		SetClientInfo(&models.ClientInfo{
			AppVersion:   request.AppVersion,
			AccessKeyID:  request.AccessKeyId,
			MenuID:       "dache_anycar",
			PageType:     cast.ToInt32(pageType),
			Channel:      int64(request.Channel),
			PlatformType: platformType,
			Lang:         "zh-CN",
		}).
		SetGEOInfo(&models.GEOInfo{
			MapType:     request.MapType,
			CurrLat:     request.GetLat(),
			CurrLng:     request.GetLng(),
			FromLat:     request.FromLat,
			FromLng:     request.FromLng,
			FromPOIID:   request.FromPoiId,
			FromPOIType: request.GetFromPoiType(),
			FromAddress: request.GetFromAddress(),
			FromName:    request.FromName,
			ToLat:       request.ToLat,
			ToLng:       request.ToLng,
			ToPOIID:     request.ToPoiId,
			ToPOIType:   request.GetToPoiType(),
			ToAddress:   request.GetToAddress(),
			ToName:      request.ToName,
		}).
		SetUserOption(&models.UserOption{
			OrderType:           request.OrderType,
			DepartureTime:       departureTime,   // 简化版账单计价规则不区分时间，但还是传给账单departure_time
			DepartureRange:      &departureRange, // 简化版未处理端上传的departure_range，看后续计价规则和产品方案
			CarpoolSeatNum:      PassengerNums,
			MultiRequireProduct: request.GetMultiRequireProduct(),
		}).TryBuild(ctx); err != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "new logic1 err=%+v", err)
		return nil, err
	}

	//extraMap 信息获取
	if riskCode, ok := extraMap["sfc_risk_code"]; ok {
		sfcRiskCode = cast.ToInt32(riskCode)
	}

	var productsGen *biz_runtime.ProductsGenerator
	if productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pSFCEstimate"),
		biz_runtime.WithBaseReq(baseReqData),
		biz_runtime.WithRiskCode(ctx, sfcRiskCode),
	); err != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "new logic2 err=%+v", err)
		return nil, err
	}

	productsGen.SetNeedMember(true)
	productsGen.SetSendReqKafka(false)
	RegisterRpc(ctx, productsGen)

	if AuthRPC := rpc_process.NewSFCAuthRPC(ctx, ufsV2.DomainPassenger, strconv.FormatInt(productsGen.BaseReqData.PassengerInfo.PID, 10)); AuthRPC != nil {
		productsGen.RegisterAfterPriceRPCProcess(AuthRPC)
		productsGen.RegisterFinalFilter(AuthRPC)
	}
	if WycAuthRPC := rpc_process.NewSFCWYCAuthRPC(ctx, ufsV2.DomainPassenger, strconv.FormatInt(productsGen.BaseReqData.PassengerInfo.PID, 10)); WycAuthRPC != nil {
		productsGen.RegisterAfterPriceRPCProcess(WycAuthRPC)
		productsGen.RegisterFinalFilter(WycAuthRPC)
	}
	RegisterProductFilter(ctx, productsGen, dcmpData)
	return productsGen, nil
}

// Do 执行方法
func (logic *Logic) Do(ctx context.Context) (*proto.SFCEstimateResponse, error) {
	var (
		err      error
		products []*biz_runtime.ProductInfoFull
	)

	if products, err = logic.generator.GenProducts(ctx); err != nil {
		//未获取到品类信息暂定为未开城
		if err != nil && errors.Is(err, consts.ErrorGetFromDDSFail) {
			title := logic.dcmpData.Title["from_city_not_open"]
			errRes := proto.SFCEstimateResponse{
				Title: &title,
			}
			return &errRes, BizError.ErrStartFenceNotOpen
		}
		log.Trace.Warnf(ctx, LOG_TAG, "genProducts err=%+v", err)
		return nil, err
	}

	return logic.render(ctx, products), nil
}

func (logic *Logic) render(ctx context.Context, products []*biz_runtime.ProductInfoFull) (ret *proto.SFCEstimateResponse) {
	var (
		InsuranceInfo   *proto.InsuranceInfo
		estimateData    = make([]*proto.SFCEstimateData, 0, len(products))
		isCrossCity     int8
		selectCarpool   int32 = 0
		selectAlone     int32 = 0
		driverMetre     int64 = 0
		animationEnable bool
		animationKey    string
		animationImg    string
		//highwayFee      float64
		estimateEnable   bool
		spsId            string
		retSpsId         string
		isThroughHighway int
		intercityCarpool *biz_runtime.ProductInfoFull
		fencePriceId     string
	)

	for _, product := range products {
		if product.GetCarpoolType() == int64(consts.CarPoolTypeInterCity) {
			intercityCarpool = product
			break
		}
	}

	// 修改品类顺序
	sortProducts := sortProducts(ctx, products)

	if logic.generator.BaseReqData.AreaInfo.Area == logic.generator.BaseReqData.AreaInfo.ToArea {
		isCrossCity = 0
	} else {
		isCrossCity = 1
	}

	var maxStringAddr float64
	var isNewUser, isNewVersion bool
	var (
		maxDiscountExpireTime   string
		maxBatchId              string
		isCouponMarketingSwicth bool
		isRemindToCarpool       bool
	)
	if beatlesApollo.GetNewUserApolloSwitch(ctx, int64(logic.userInfo.PID), logic.request.FromArea, logic.request.GetAccessKeyId(), logic.request.GetAppVersion()) {
		isNewVersion = true
	}

	if beatlesApollo.GetSfcRemindToCarpool(ctx, int64(logic.userInfo.PID), logic.request.FromArea, logic.request.GetAccessKeyId(), logic.request.GetAppVersion()) {
		isRemindToCarpool = true
	}

	if coupon.GetPassengerCouponMarketingSwitch(ctx, cast.ToString(logic.userInfo.PID), cast.ToString(logic.request.GetAccessKeyId()), logic.request.GetAppVersion(), logic.request.FromArea) {
		isCouponMarketingSwicth = true
		isNewVersion = true
	}

	if BeatlesUser.IsNewUser(ctx, int64(logic.userInfo.PID)) {
		isNewUser = true
	}

	newPriceSwitch := apollo.GetPassengerEstimateNewStylePrice(cast.ToInt64(logic.userInfo.PID), logic.request.GetAccessKeyId(), logic.request.GetAppVersion(), logic.request.FromArea)
	doubleSwitch := getApolloSwitch(ctx, cast.ToInt64(logic.userInfo.PID), logic.request.FromArea, logic.request.GetAppVersion(), logic.request.GetAccessKeyId())
	newFenceStyleSwitch := sfc_car.GetNewFenceStyleSwitch(logic.request, logic.userInfo.PID)
	var abGroup string
	omega := make(map[string]string)
	var routeId string
	extraParams := make(map[string]string, 3)
	// 拼座拼成价和拼失败价是否相等
	for _, product := range products {
		if product.GetProductCategory() == int64(estimate_pc_id.EstimatePcIdCarpoolSFCar) || product.GetProductCategory() == int64(estimate_pc_id.EstimatePcIdCarpoolCrossSFCar) {
			prov := &internal.SFCarAdapter{ProductInfoFull: product}
			estimateFeeList := prov.GetMultiPrice()
			if len(estimateFeeList) == 2 && estimateFeeList[0] != estimateFeeList[1] {
				extraParams["estimateNotEqual"] = "1"
			}
			break
		}
	}
	//获取品类信息
	for _, product := range sortProducts {
		if len(product.GetRouteIdList()) > 0 {
			routeId = product.GetRouteIdList()[0]
		}
		driverMetre = product.GetBillDriverMetre()
		//highwayFee = product.GetHighWayFee()
		isThroughHighway = getIsHighway(product)
		spsId = getSpsID(product)
		fencePriceId = getHitFencePriceId(product)
		if retSpsId == "" {
			retSpsId = fencePriceId
		}
		extraParams["newFenceStyle"] = sfc_car.OldFenceStyle
		extraParams["fencePriceId"] = fencePriceId
		if spsId != "" && abGroup == "" {
			abGroup = apollo.GetPassengerEstimateImproveSwitch(ctx, cast.ToInt64(logic.userInfo.PID), logic.request.FromArea, logic.request.GetAccessKeyId(), logic.request.GetAppVersion())
		}
		//获取价格信息
		if item, discount, discountExpireTime, batchId, couponType := internal.Render(ctx, product, logic.dcmpData, isCrossCity, isNewUser,
			isNewVersion, isCouponMarketingSwicth, abGroup, newPriceSwitch, doubleSwitch, logic.passengerNums, extraParams, newFenceStyleSwitch); item != nil {
			if cast.ToFloat64(discount) > maxStringAddr {
				maxStringAddr = cast.ToFloat64(discount)
				maxDiscountExpireTime = discountExpireTime
				maxBatchId = batchId
			}
			if item.ExtraMap.ProductCategory == estimate_pc_id.EstimatePcIdAloneSFCar {
				if item.IsSelected == 1 {
					selectAlone = 1
				}
				if cast.ToFloat64(discount) > 0 && isNewUser && isNewVersion {
					omega[cast.ToString(item.ExtraMap.ProductCategory)] = "new"
				} else if couponType != "" {
					omega[cast.ToString(item.ExtraMap.ProductCategory)] = couponType
				}
			} else if item.ExtraMap.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolSFCar ||
				item.ExtraMap.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar {
				if item.IsSelected == 1 {
					selectCarpool = 1
				}
				if cast.ToFloat64(discount) > 0 && isNewUser && isNewVersion {
					omega[cast.ToString(item.ExtraMap.ProductCategory)] = "new"
				} else if couponType != "" {
					omega[cast.ToString(item.ExtraMap.ProductCategory)] = couponType
				}
			}
			if item.PriceInfo.GetIsRoll() == 1 {
				estimateEnable = true
			}
			estimateData = append(estimateData, item)
		}

		allow, params := sfc_car.RegionAnimationSupport(product)

		if !animationEnable && (product.ShowAnimation() || fencePriceId != "") && allow && len(params) > 0 {
			animationEnable = true
			if getSpsID(product) == "" {
				animationKey = fencePriceId
			} else {
				animationKey = getSpsID(product)
			}
			animationImg = params[Animation_Url]
		}
	}
	isMulti := order.IsMulti(logic.userInfo.UID, logic.request.GetAppVersion(), cast.ToString(logic.request.GetAccessKeyId()), logic.generator.BaseReqData.AreaInfo.Area)
	//品类选择-默认选中逻辑
	estimateData, selectCarpool, selectAlone = internal.HandelMultiRequireProduct(ctx, estimateData, selectCarpool, selectAlone, logic.userInfo.PID, isMulti, logic.request, logic.generator.BaseReqData.AreaInfo.Area, logic.generator.BaseReqData.AreaInfo.ToArea, driverMetre)
	isStation, station := logic.getIsStation(ctx, selectCarpool, products)

	//意外险介绍
	logic.dcmpData.ExtraTab.ExtraUrl = logic.dcmpData.InsuranceUrl
	//保险声明链接
	logic.dcmpData.BottomInfo.JumpUrl = logic.dcmpData.DeclareUrl
	logic.dcmpData.BottomInfo.LegacyType = logic.dcmpData.InsuranceLegacyType

	InsuranceInfo = &proto.InsuranceInfo{
		Title: logic.dcmpData.ExtraTab.ExtraText,
		Url:   logic.dcmpData.ExtraTab.ExtraUrl,
	}

	//右上角 发票说明
	dcmpMenu := logic.dcmpData.MenuInfo
	menuInfo := proto.MenuInfo{
		Title:   dcmpMenu.Title,
		JumpUrl: dcmpMenu.JumpUrl,
	}

	// 座位数
	var group string
	newVersion := false
	group = logic.GetSelectNumInfoSwitch(ctx)
	if group == TreatmentGroup || group == TreatmentGroup3 {
		newVersion = true
	}
	seatInfo := internal.GetPassengerSeatInfo(ctx, logic.passengerNums, logic.maxNum, logic.dcmpData, newVersion)

	// timepicker 类型
	timePickerType := psgtimepicker.GetType(ctx, cast.ToString(logic.userInfo.PID), cast.ToString(logic.request.AccessKeyId), logic.request.AppVersion, logic.request.FromArea, logic.request.ToArea)

	// 处理下挂远途拼车
	dcmpConfig := getAdditionalInfoDcmp(ctx)
	if isMulti == 0 && intercityCarpool != nil {
		for _, item := range estimateData {
			if item.ExtraMap.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolSFCar ||
				item.ExtraMap.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar {
				item.LinkList = append(item.LinkList, getLinkListCard(intercityCarpool, dcmpConfig, logic.dcmpData.PriceIcon))
			}
		}
	}

	ret = &proto.SFCEstimateResponse{
		MenuInfo:             &menuInfo,
		EstimateData:         estimateData,
		PassengerSeatInfo:    seatInfo,
		InsuranceInfo:        InsuranceInfo,
		IsMulti:              &isMulti,
		TimepickerScenesType: timePickerType,
		AdditionalInfo:       getAdditionalInfo(intercityCarpool, dcmpConfig),
		SpsId:                &spsId,
		MapRouteId:           &routeId,
	}

	if ret.GetSpsId() == "" {
		ret.SpsId = &retSpsId
	}

	//备注
	if notes.CheckPassengerNotesEdit(ctx, cast.ToInt64(logic.userInfo.PID), logic.request.GetAppVersion(),
		logic.request.GetAccessKeyId(), logic.generator.BaseReqData.AreaInfo.Area) {
		noteUrl := ""
		if logic.request.FromType == ReAppointment && logic.request.PreOid != "" {
			noteUrl = notes.GetEditUrl(ctx, logic.generator.BaseReqData.AreaInfo.Area, logic.generator.BaseReqData.AreaInfo.ToArea, constant.SourceTypeBubble, cast.ToString(logic.request.GetAccessKeyId()), &logic.request.PreOid, &logic.request.FromType, nil)
		} else {
			noteUrl = notes.GetEditUrl(ctx, logic.generator.BaseReqData.AreaInfo.Area, logic.generator.BaseReqData.AreaInfo.ToArea, constant.SourceTypeBubble, cast.ToString(logic.request.GetAccessKeyId()), nil, nil, nil)
		}
		if noteUrl != "" && logic.dcmpData.OperationInfo != nil {
			logic.dcmpData.OperationInfo.Url = &noteUrl
			ret.OperationInfo = logic.dcmpData.OperationInfo
		}
	}

	//发单按钮
	ret.SubmitButton = logic.dcmpData.SubmitButton
	ret = fillBottomInfo(ctx, logic, ret)

	if logic.request.FromType == ReAppointment {
		ret.SubmitButton.Title = logic.dcmpData.ReAppointmentText
	}

	if animationEnable && logic.dcmpData.AnimationInfo != nil {
		ret.AnimationInfo = &proto.AnimationInfo{}
		ret.AnimationInfo.SetAnimationKey(animationKey)
		ret.AnimationInfo.SetAnimationImg(animationImg)
		ret.AnimationInfo.SetDefaultImg(logic.dcmpData.AnimationInfo.GetDefaultImg())
		ret.AnimationInfo.SetCloseImg(logic.dcmpData.AnimationInfo.GetCloseImg())
		ret.AnimationInfo.SetAnimationTime(logic.dcmpData.AnimationInfo.GetAnimationTime())
	}

	if estimateEnable && logic.dcmpData.FireworksInfo != nil {
		ret.RollFireworksInfo = logic.dcmpData.FireworksInfo
	}

	if logic.userInfo.UID != 0 && logic.userInfo.PID != 0 {
		isHighWay := false
		if isThroughHighway > 0 {
			isHighWay = true
		}
		tollFeeSwitch, _ := apollo.GetSfcPassengerTollFeeToggle(int64(logic.userInfo.PID), logic.request.GetFromArea(),
			logic.request.GetAccessKeyId(), logic.request.GetAppVersion())
		if tollFeeSwitch {
			ret.TollFeeInfo = logic.dcmpData.TollFeeInfo
			if !isHighWay {
				ret.TollFeeInfo.HighwayInfo = nil
			}
		}
	}

	if isNewUser && isNewVersion {
		//ret.BannerInfo = logic.buildBannerInfo(ctx, maxStringAddr)
		omegaData, err := json.Marshal(omega)
		if err == nil {
			omegaDataNew := &proto.SfcOmegaParams{
				CarpCouponType: proto.StrPtr(string(omegaData)),
			}
			ret.OmegaParams = omegaDataNew
		}
	} else if isCouponMarketingSwicth && omega != nil {
		omegaData, err := json.Marshal(omega)
		if err == nil {
			omegaDataNew := &proto.SfcOmegaParams{
				CarpCouponType: proto.StrPtr(string(omegaData)),
			}
			ret.OmegaParams = omegaDataNew
		}
	}

	// 返乡单感知
	homeComingBannerDcmp := logic.dcmpData.HomeComingBannerInfo
	isHomeComing := false
	if logic.request.SelectRouteId != nil &&
		len(*logic.request.SelectRouteId) != 0 &&
		homeComingBannerDcmp != nil {
		leftIcon := ""
		if homeComingBannerDcmp.LeftIcon != nil {
			leftIcon = *homeComingBannerDcmp.LeftIcon
		}
		var backgroundColors []string
		if len(homeComingBannerDcmp.BackgroundColors) != 0 {
			backgroundColors = homeComingBannerDcmp.BackgroundColors
		}
		title := ""
		if homeComingBannerDcmp.Title != nil {
			title = *homeComingBannerDcmp.Title
		}
		homeComingBannerData := &proto.BannerInfoData{
			BackgroundColors: backgroundColors,
			LeftIcon:         &leftIcon,
			Title:            &title,
		}
		isHomeComing = true

		ret.BannerInfo = &proto.BannerInfo{
			Data:     homeComingBannerData,
			CardType: util.Int32Ptr(1010),
		}
	}

	//获取优先级，只拿一个模版去获取banner信息
	paramsToCardID := map[string]bool{
		"newVersionToChild": newVersion,
		"isNewUser":         isNewUser,
		"isNewVersion":      isNewVersion,
		"isStation":         isStation,
		"isRemindToCarpool": isRemindToCarpool,
		"isHomeComing":      isHomeComing,
	}
	cardId := logic.getCardId(ctx, paramsToCardID)

	casperBannerInfo := logic.buildEndBannerInfo(ctx, cardId, maxStringAddr, station)
	if ret.BannerInfo != nil && casperBannerInfo != nil {
		ret.BannerInfo.CasperContent = casperBannerInfo.CasperContent
	} else {
		ret.BannerInfo = casperBannerInfo
	}

	if isCouponMarketingSwicth {
		ret.PreCancelDetail = logic.buildPreCancelInfo(ctx, maxStringAddr, maxDiscountExpireTime, cast.ToInt64(logic.userInfo.PID), maxBatchId)
	}

	if intercityCarpool != nil {
		sortProducts = append(sortProducts, intercityCarpool)
	}
	logic.writePublicLog(ctx, sortProducts, selectCarpool, selectAlone, isThroughHighway, spsId)
	if intercityCarpool != nil {
		logic.writeAdditionalPublicLog(ctx, []*biz_runtime.ProductInfoFull{intercityCarpool})
	}
	logic.sendSFCEstimateDDMQ(ctx, sortProducts)
	return ret
}

// 三方授权
func getAdditionalInfo(intercityCarpool *biz_runtime.ProductInfoFull, dcmpConfig *IntercityDcmp) *proto.AdditionalInfo {
	if intercityCarpool == nil {
		return nil
	}
	res := &proto.AdditionalInfo{
		ProductList: []int32{int32(intercityCarpool.Product.ProductCategory)},
		Title:       dcmpConfig.Additional.Title,
	}
	// 三方授权
	if _, ok := intercityCarpool.Product.BizInfo.UfsTripCloudAuthBusinessID[intercityCarpool.GetBusinessID()]; ok {
		res.LegacyType = dcmpConfig.Additional.LegacyType
		res.LegacyText = dcmpConfig.Additional.LegacyText
		res.LegacyUrl = dcmp.TranslateTemplate(dcmpConfig.Additional.AuthUrl, map[string]string{"product_id": cast.ToString(intercityCarpool.GetProductId())})
		return res
	}
	// 自营授权
	if _, ok := intercityCarpool.Product.BizInfo.UfsWycAuthBusinessID[AnyCar]; ok {
		res.LegacyType = dcmpConfig.WycAdditional.LegacyType
		res.LegacyText = dcmpConfig.WycAdditional.LegacyText
		res.LegacyUrl = dcmpConfig.WycAdditional.AuthUrl
		return res
	}
	return res
}

func getCarTitleTemplate(forecastTime, bubbleTime int64, config *IntercityDcmp) string {
	forecastDayTime := util.GetSomeDayEarlyTimeStamp(forecastTime)
	bubbleDayTime := util.GetSomeDayEarlyTimeStamp(bubbleTime)
	dayDistance := (forecastDayTime - bubbleDayTime) / (24 * 60 * 60)
	switch dayDistance {
	case 2:
		return config.Cartitle[0]
	case 1:
		return config.Cartitle[1]
	case 0:
		if forecastTime != bubbleTime {
			return config.Cartitle[2]
		} else {
			return config.Cartitle[3]
		}
	}
	return ""
}

func getCarTitle(departureRange []int64, config *IntercityDcmp) (int64, int64, string) {
	var (
		forecastTime, bAdditional int64
		bubbleTime                = time.Now().Unix()
		aAdditional               = config.AdditionalTime
	)

	if len(departureRange) == 0 || len(config.Cartitle) == 0 {
		return 0, 0, ""
	}

	if len(departureRange) > 0 {
		bAdditional = (departureRange[0] - bubbleTime) * config.TimeFactor / 100
	}

	forecastTime = bubbleTime + int64(math.Max(float64(aAdditional), float64(bAdditional)))
	temp := getCarTitleTemplate(forecastTime, bubbleTime, config)
	tmp := time.Unix(forecastTime, 0).Format("15:04")

	return bubbleTime, forecastTime, dcmp.TranslateTemplate(temp, map[string]string{"time": cast.ToString(tmp)})
}

func getPriceInfo(dcmpConfig *IntercityDcmp, data *biz_runtime.ProductInfoFull, priceIcon string) *proto.PriceInfo {
	if len(dcmpConfig.FeeMsg) == 0 {
		return nil
	}

	return &proto.PriceInfo{
		JumpUrl:     dcmp.TranslateTemplate(dcmpConfig.BasicUrl, map[string]string{"business_id": cast.ToString(data.GetBusinessID()), "estimate_id": cast.ToString(data.GetEstimateID()), "product_id": cast.ToString(data.GetProductId())}),
		FareAmount:  cast.ToString(data.GetEstimateFee()),
		AwardAmount: "0",
		Icon:        priceIcon,
		Title:       dcmp.TranslateTemplate(dcmpConfig.FeeMsg, map[string]string{"amount": cast.ToString(data.GetEstimateFee())}),
	}
}

func getAdditionalInfoDcmp(ctx context.Context) *IntercityDcmp {
	var (
		dcmpConfig IntercityDcmp
	)
	tmp := dcmp.GetDcmpContent(ctx, "intercity_station-sfc_additional", nil)
	if err := json.Unmarshal([]byte(tmp), &dcmpConfig); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal failed err : %s", err)
		return nil
	}
	return &dcmpConfig
}

func getIntercityTimeFactors(passengerInfo models.PassengerInfo) (additionalTime int64, timeFactor int64) {
	user := ApolloModel.NewUser(strconv.Itoa(int(passengerInfo.PID))).
		With("phone", passengerInfo.Phone).
		With("pid", strconv.Itoa(int(passengerInfo.PID)))
	toggle, err := ApolloSDK.FeatureToggle("gs_sfc_intercity_trigger_time_toggle", user)
	if err != nil {
		return 0, 0
	}
	if !toggle.IsAllow() {
		return 0, 0
	}
	additionalTime = cast.ToInt64(toggle.GetAssignment().GetParameter("additional_time", "0"))
	timeFactor = cast.ToInt64(toggle.GetAssignment().GetParameter("time_factor", "0"))
	return
}

func getLinkListCard(data *biz_runtime.ProductInfoFull, dcmpConfig *IntercityDcmp, priceIcon string) *proto.AdditionalEstimateData {
	if data == nil || dcmpConfig == nil {
		return nil
	}

	var (
		triggerTime, bubbleTime int64
		carTitle                string
	)

	dcmpConfig.AdditionalTime, dcmpConfig.TimeFactor = getIntercityTimeFactors(data.BaseReqData.PassengerInfo)
	bubbleTime, triggerTime, carTitle = getCarTitle(data.BaseReqData.CommonInfo.DepartureRange, dcmpConfig)
	res := &proto.AdditionalEstimateData{
		EstimateId: data.Product.EstimateID,
		CarIcon:    dcmpConfig.Caricon,
		CarTitle:   carTitle,
		Subtitle:   dcmpConfig.Subtitle,
		PriceInfo:  getPriceInfo(dcmpConfig, data, priceIcon),
		ExtraMap: &proto.SFCExtraMap{
			ProductCategory:          int32(data.GetProductCategory()),
			RequireLevel:             cast.ToInt32(data.GetRequireLevel()),
			BusinessId:               int32(data.GetProductId()),
			ProductId:                int32(data.GetProductId()),
			ComboType:                int32(data.GetComboType()),
			LevelType:                data.GetLevelType(),
			CarpoolType:              int32(data.GetCarpoolType()),
			CountPriceType:           0,
			PayType:                  consts.PayTypeByPerson,
			TriggerTime:              int32(triggerTime),
			BubbleTime:               int32(bubbleTime),
			PolymericProductCategory: internal.GetPolymericProductCategory(data.GetProductCategory(), data.GetCarpoolType()),
		},
	}
	for _, item := range data.BaseReqData.CommonBizInfo.MultiRequireProduct {
		if item.ProductCategory == int64(res.ExtraMap.ProductCategory) {
			res.IsSelected = item.IsSelected
		}
	}
	return res
}

// 城际的wyc埋点
func (logic *Logic) writeAdditionalPublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull) {
	PublicKey := "g_order_cap_multi_estimate_price"
	for _, product := range products {
		if product == nil || product.Product == nil || product.GetSceneEstimatePrice() == nil {
			continue
		}
		logInfo := make(map[string]interface{})
		// 索引key
		logInfo["estimate_trace_id"] = util.GetTraceIDFromCtxWithoutCheck(ctx)
		logInfo["estimate_id"] = product.GetEstimateID()

		// 地理位置信息
		areaInfo := product.GetAreaInfo()
		logInfo["area"] = areaInfo.City
		logInfo["to_area"] = areaInfo.ToArea
		logInfo["flat"] = areaInfo.FromLat
		logInfo["flng"] = areaInfo.FromLng
		logInfo["tlat"] = areaInfo.ToLat
		logInfo["tlng"] = areaInfo.ToLng
		logInfo["district"] = areaInfo.District
		logInfo["current_lat"] = areaInfo.CurLat
		logInfo["current_lng"] = areaInfo.CurLng
		logInfo["county"] = areaInfo.FromCounty
		logInfo["to_county"] = areaInfo.ToCounty
		logInfo["from_name"] = util.StringEscape(areaInfo.FromName)
		logInfo["to_name"] = util.StringEscape(areaInfo.ToName)

		// 端信息
		commonInfo := product.GetClientInfo()
		logInfo["app_version"] = commonInfo.AppVersion
		logInfo["client_type"] = commonInfo.ClientType
		logInfo["access_key_id"] = commonInfo.AccessKeyID
		logInfo["channel"] = commonInfo.Channel
		logInfo["page_type"] = commonInfo.PageType
		logInfo["lang"] = commonInfo.Lang
		logInfo["menu_id"] = commonInfo.MenuID
		// 产品信息
		logInfo["product_category"] = product.GetProductCategory()
		logInfo["require_level"] = product.Product.RequireLevel
		logInfo["combo_type"] = product.Product.ComboType
		logInfo["carpool_type"] = product.Product.CarpoolType
		logInfo["product_id"] = product.Product.ProductID
		if product.GetBizInfo() != nil {
			logInfo["combo_id"] = product.GetBizInfo().ComboID
		}

		// 用户信息
		logInfo["pid"] = product.GetUserInfo().PID

		if product.Product.BizInfo != nil {
			logInfo["carpool_seat_num"] = product.Product.BizInfo.CarpoolSeatNum
		}

		if product != nil && product.GetBizInfo() != nil && product.GetBizInfo().RouteInfo != nil && product.GetBizInfo().RouteInfo.RouteGroup != nil {
			if IsSkuModel(product) {
				logInfo["is_sku_model"] = 1
			}
		}

		if product != nil && product.BaseReqData != nil && (product.BaseReqData.CommonBizInfo.IsAllowHomeOwnerTicket() || product.BaseReqData.CommonBizInfo.GetChildTicket() != nil) {
			logInfo["support_select_seat"] = 1
		}

		log.Public.Public(ctx, PublicKey, logInfo)
	}
}

func (logic *Logic) writePublicLog(ctx context.Context, sortProducts []*biz_runtime.ProductInfoFull, selectCarpool int32, selectAlone int32, isThroughHighway int, spsId string) {
	defer func() {
		if err := recover(); err != nil {
			log.Trace.Warnf(ctx, LOG_TAG, "sfc estimate publicLog panic:"+string(debug.Stack()))
		}
	}()

	//满足四要素记录log：起点，终点，时间，人数
	if logic.request.GetFromName() != "" &&
		logic.request.GetToName() != "" &&
		len(logic.request.GetDepartureRange()) > 0 &&
		*logic.request.PassengerNums != cast.ToInt32(0) {
		logLength := 25

		for _, product := range sortProducts {
			logInfo := make(map[string]interface{}, logLength)
			logInfo["estimate_id"] = product.GetEstimateID()
			logInfo["estimate_trace_id"] = logic.traceID
			logInfo["wyc_bubble_traceid"] = logic.extraParams.GetWycBubbleId()

			switch logic.request.GetFromPageId() { //后期作废bubble_page字段，替换为from_page_id
			case scheme.SchemePagePsgWait:
				logInfo["bubble_page"] = 2 //等待列表-重新预约
			case LOG_WYC_SIDE:
				logInfo["bubble_page"] = 3 //网约车冒泡页导流
			case LOG_HISTORY_LIST:
				logInfo["bubble_page"] = 4 //历史行程
			case LOG_SFC_GUESS:
				logInfo["bubble_page"] = 5 //猜你想去
			default:
				logInfo["bubble_page"] = 1 //首页发单
			}

			logInfo["bubble_time"] = time.Now().Unix()
			logInfo["passenger_id"] = logic.userInfo.PID
			logInfo["app_version"] = logic.request.GetAppVersion()
			logInfo["access_key_id"] = logic.request.GetAccessKeyId()
			logInfo["city_id"] = logic.generator.BaseReqData.AreaInfo.Area
			logInfo["from_city_id"] = logic.generator.BaseReqData.AreaInfo.Area
			logInfo["to_city_id"] = logic.generator.BaseReqData.AreaInfo.ToArea
			logInfo["start_lng"] = logic.request.GetFromLng()
			logInfo["start_lat"] = logic.request.GetFromLat()
			logInfo["dest_lng"] = logic.request.GetToLng()
			logInfo["dest_lat"] = logic.request.GetToLat()
			logInfo["navi_dis"] = product.GetBillInfo().DriverMetre
			logInfo["start_name"] = logic.request.GetFromName()
			logInfo["start_addr"] = logic.request.GetFromAddress()
			logInfo["dest_name"] = logic.request.GetToName()
			logInfo["dest_addr"] = logic.request.GetToAddress()
			logInfo["start_county_id"] = logic.generator.BaseReqData.AreaInfo.FromCounty
			logInfo["dest_county_id"] = logic.generator.BaseReqData.AreaInfo.ToCounty
			departureTimeRange := logic.request.GetDepartureRange()
			logInfo["min_depart_time"] = departureTimeRange[0]
			logInfo["max_depart_time"] = departureTimeRange[1]
			logInfo["passenger_num"] = logic.request.GetPassengerNums()
			logInfo["from_page_id"] = logic.request.GetFromPageId()
			logInfo["menu_id"] = logic.request.GetMenuId()
			logInfo["from_poi_id"] = logic.request.GetFromPoiId()
			logInfo["to_poi_id"] = logic.request.GetToPoiId()
			logInfo["is_through_highway"] = isThroughHighway
			logInfo["sps_id"] = spsId
			logInfo["risk_code"] = logic.generator.BaseReqData.CommonInfo.RiskCode
			//品类勾选

			if product.GetCarpoolType() == consts.CarPoolTypeInterCity {
				logInfo["multi_mode_dict"] = map[string]string{"intercity": cast.ToString(selectCarpool)}
				logInfo["before_price_dict"] = map[string]string{"intercity": cast.ToString(BeatlesUtil.RoundHalfUp(product.GetBillInfo().TotalFee*100, 0))}
				logInfo["coupon_price_dict"] = map[string]string{"intercity": cast.ToString(BeatlesUtil.RoundHalfUp(product.GetEstimateFee()*100, 0))}
			} else if product.GetProductCategory() != estimate_pc_id.EstimatePcIdAloneSFCar { //拼座
				carpoolSuccPrice := product.GetCapPrice()
				carpoolSuccWithCoupon := carpoolSuccPrice
				carpoolFailDiscountInfo := product.GetCarpoolFailedCouponFromRawData()
				if carpoolFailDiscountInfo != nil {
					discountAmount := cast.ToFloat64(carpoolFailDiscountInfo.Amount) / 100
					carpoolSuccWithCoupon, _ = coupon.GetCarpoolSuccInfoWithCoupon(carpoolSuccPrice, discountAmount)
				}

				logInfo["multi_mode_dict"] = map[string]string{"carp": cast.ToString(selectCarpool)}

				carpoolFailPrice, _ := product.GetCarpoolFailEstimateFee()
				logInfo["before_price_dict"] = map[string]string{"carp_succ": cast.ToString(BeatlesUtil.RoundHalfUp(product.GetBillInfo().CapPrice*100, 0)), "carp_fail": cast.ToString(BeatlesUtil.RoundHalfUp(product.GetBillInfo().TotalFee*100, 0))}
				logInfo["coupon_price_dict"] = map[string]string{"carp_succ": cast.ToString(BeatlesUtil.RoundHalfUp(carpoolSuccWithCoupon*100, 0)), "carp_fail": cast.ToString(BeatlesUtil.RoundHalfUp(carpoolFailPrice*100, 0))}

			} else { //独享
				logInfo["multi_mode_dict"] = map[string]string{"only": cast.ToString(selectAlone)}

				onlyPrice := product.GetEstimateFee()
				logInfo["before_price_dict"] = map[string]string{"only": cast.ToString(BeatlesUtil.RoundHalfUp(product.GetBillInfo().CapPrice*100, 0))}
				logInfo["coupon_price_dict"] = map[string]string{"only": cast.ToString(BeatlesUtil.RoundHalfUp(onlyPrice*100, 0))}
			}

			log.Public.Public(ctx, "beatles_passenger_bubble_record", logInfo)
		}
	}
}

// RegisterRpc
func RegisterRpc(ctx context.Context, productsGen *biz_runtime.ProductsGenerator) {
	if productsGen.BaseReqData == nil {
		return
	}

	// 城际库存模式
	if r := rpc_process.NewCarpoolEtx(productsGen.BaseReqData, rpc_process.Sync); r != nil {
		productsGen.RegisterAfterPriceRPCProcess(r)
	}
}

// RegisterProductFilter 获取品类信息
func RegisterProductFilter(ctx context.Context, generator *biz_runtime.ProductsGenerator, dcmpData proto.SFCEstimateDcmp) {
	if generator.BaseReqData == nil {
		return
	}

	if rpFilter := after_dds_filter.NewSFCCarpoolPassengerFilter(ctx, generator.BaseReqData, dcmpData); rpFilter != nil {
		generator.RegisterAfterDdsFilter(rpFilter)
	}

	if irFilter := after_dds_filter.NewIntercityDepartureRangeFilter(ctx, &generator.BaseReqData.CommonInfo); irFilter != nil {
		generator.RegisterAfterDdsFilter(irFilter)
	}

	if intercityPolymericFilter := after_price_filter.NewSfcIntercityPolymericFilter(generator.BaseReqData); intercityPolymericFilter != nil {
		generator.RegisterAfterPriceFilter(intercityPolymericFilter)
	}
}

// sortProducts 品类排序 先展示拼车再展示独享
func sortProducts(ctx context.Context, products []*biz_runtime.ProductInfoFull) []*biz_runtime.ProductInfoFull {
	// 简化版：按product_category排序
	var (
		sortMap      = make(map[int]*biz_runtime.ProductInfoFull)
		sortProducts []*biz_runtime.ProductInfoFull
	)

	// TODO 这个地方这么排序是不是有点尴尬？
	sortKey := []int{estimate_pc_id.EstimatePcIdCarpoolSFCar, estimate_pc_id.EstimatePcIdCarpoolCrossSFCar, estimate_pc_id.EstimatePcIdAloneSFCar}
	for _, p := range products {
		key := int(p.Product.ProductCategory)
		sortMap[key] = p
	}

	for _, key := range sortKey {
		if sortMap[key] != nil {
			sortProducts = append(sortProducts, sortMap[key])
		}
	}

	return sortProducts
}

func getSpsID(product *biz_runtime.ProductInfoFull) string {
	if product == nil || product.GetBillExtraMap() == nil {
		return ""
	}

	spsId, ok := product.GetBillExtraMap()["sps_id"]
	if !ok {
		return ""
	}

	return util.ToString(spsId)
}

func getHitFencePriceId(product *biz_runtime.ProductInfoFull) string {
	if product == nil || product.GetBillExtraMap() == nil {
		return ""
	}

	fencePriceId, ok := product.GetBillExtraMap()["is_hit_fence_price"]
	if !ok {
		return ""
	}

	return util.ToString(fencePriceId)
}

func (logic *Logic) sendSFCEstimateDDMQ(ctx context.Context, sortProducts []*biz_runtime.ProductInfoFull) {
	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, "carpool_estimate_mq", "mq send failed %s", r)
		}
	}()

	for _, product := range sortProducts {
		rawMsg := mq.NewMQMsgBuilder().SetTraceFromCtx(ctx).SetDataByProduct(product).Build()

		rawMsg.Data.PassengerNumber = int(logic.passengerNums)
		rawMsg.Data.DepartureRange = product.BaseReqData.CommonInfo.DepartureRange

		if product.GetProductCategory() != estimate_pc_id.EstimatePcIdAloneSFCar {
			rawMsg.Data.ScenePrice = append(rawMsg.Data.ScenePrice, &mq.ScenePrice{
				EstimateFee:      product.GetCapPrice(),
				IsCarpoolSuccess: true,
			})
			carpoolFailEstimate, _ := product.GetCarpoolFailEstimateFee()
			rawMsg.Data.ScenePrice = append(rawMsg.Data.ScenePrice, &mq.ScenePrice{
				EstimateFee:      carpoolFailEstimate,
				IsCarpoolSuccess: false,
			})
		}

		var buf bytes.Buffer
		encoder := json.NewEncoder(&buf)
		if err := encoder.Encode(rawMsg); err != nil {
			log.Trace.Warnf(ctx, "carpool_estimate_mq", "mq send failed %s", err)
			return
		}

		hashOption := func(bd *ddmq.MessageBuilder) {
			const hashRng = 16383
			rand.Seed(time.Now().Unix())
			bd.SetHashId(rand.Int63n(hashRng))
		}

		if err := ddmq.SendV2(ctx, ddmq.TopicWanliuPassengerEstimate, buf.String(), hashOption); err != nil {
			log.Trace.Warnf(ctx, "carpool_estimate_mq", "mq send failed %s", err)
		}
	}
}

func (logic *Logic) buildBannerInfo(ctx context.Context, maxPrice float64) *proto.BannerInfo {
	CardIds := "na_sfc_psg_communicate_1006"
	var CardType = int32(1006)
	casperReq := &Dirpc_SDK_Feeds.CasperCardsReq{
		CardIds: CardIds,
	}
	resp, err := casper.GetCasperFeedResp(ctx, casperReq)
	if err != nil {
		log.Trace.Warnf(ctx, "casper.GetCasperFeedResp", "failed %v", err)
		return nil
	}
	casperContent := &proto.CasperContent{
		Id:       &CardIds,
		Template: resp.Template,
		WeexCdn:  resp.WeexCdn,
	}
	omegaData := make(map[string]string)
	omegaData["coupon_type"] = "new"
	tag := map[string]string{
		"price": cast.ToString(maxPrice),
	}
	omega := &proto.OmegaCk{
		EventId:    proto.StrPtr("beat_p_bubble_guide_sw"),
		Parameters: nil,
	}
	parameters, errMarshel := json.Marshal(omegaData)
	if errMarshel == nil {
		Parameters := &proto.OmegaCkParams{
			CouponType: proto.StrPtr(string(parameters)),
		}
		omega.Parameters = Parameters
	}
	priceDesc := dcmp.GetDcmpContent(ctx, "sfc-estimate_bannber_text", tag)
	configMap := make(map[string]interface{})
	errUnmarshal := json.Unmarshal([]byte(priceDesc), &configMap)
	data := &proto.BannerInfoData{}
	if errUnmarshal == nil {
		data = &proto.BannerInfoData{
			Background: proto.StrPtr(cast.ToString(configMap["background"])),
			LeftIcon:   proto.StrPtr(cast.ToString(configMap["left_icon"])),
			//Title:      proto.StrPtr(cast.ToString(configMap["title"])),
			Subtitle: proto.StrPtr(cast.ToString(configMap["subtitle"])),
			TitleImg: proto.StrPtr(cast.ToString(configMap["title_img"])),
			OmegaSw:  omega,
		}
	}
	if maxPrice == 0.0 {
		data = &proto.BannerInfoData{
			Background: proto.StrPtr(cast.ToString(configMap["background"])),
			LeftIcon:   proto.StrPtr(cast.ToString(configMap["left_icon"])),
			Title:      proto.StrPtr(cast.ToString(configMap["title"])),
			//Subtitle:   proto.StrPtr(cast.ToString(configMap["subtitle"])),
			//TitleImg:   proto.StrPtr(cast.ToString(configMap["title_img"])),
			OmegaSw: omega,
		}
	}
	ret := &proto.BannerInfo{
		CasperContent: casperContent,
		CardType:      &CardType,
		Data:          data,
	}
	return ret
}

func (logic *Logic) buildEndBannerInfo(ctx context.Context, cardId string, maxPrice float64, station string) *proto.BannerInfo {
	if cardId == "" {
		return nil
	}
	endCardId := "na_sfc_psg_communicate_" + cardId
	var CardType = cast.ToInt32(cardId)
	casperReq := &Dirpc_SDK_Feeds.CasperCardsReq{
		CardIds: endCardId,
	}
	resp, err := casper.GetCasperFeedResp(ctx, casperReq)
	if err != nil {
		log.Trace.Warnf(ctx, "casper.GetCasperFeedResp", "failed %v", err)
		return nil
	}
	casperContent := &proto.CasperContent{
		Id:       &endCardId,
		Template: resp.Template,
		WeexCdn:  resp.WeexCdn,
	}

	tag := map[string]string{
		"price": cast.ToString(maxPrice),
	}

	priceDesc := dcmp.GetDcmpContent(ctx, "sfc-estimate_bannber_text", tag)
	configMap := make(map[string]interface{})
	errUnmarshal := json.Unmarshal([]byte(priceDesc), &configMap)
	if errUnmarshal != nil {
		return nil
	}
	data := &proto.BannerInfoData{}
	if cardId == "1006" {
		omegaData := make(map[string]string)
		omegaData["coupon_type"] = "new"
		omega := &proto.OmegaCk{
			EventId:    proto.StrPtr("beat_p_bubble_guide_sw"),
			Parameters: &proto.OmegaCkParams{},
		}
		omega.Parameters.CouponType = proto.StrPtr(omegaData["coupon_type"])
		data = &proto.BannerInfoData{
			Background: proto.StrPtr(cast.ToString(configMap["background"])),
			LeftIcon:   proto.StrPtr(cast.ToString(configMap["left_icon"])),
			Subtitle:   proto.StrPtr(cast.ToString(configMap["subtitle"])),
			TitleImg:   proto.StrPtr(cast.ToString(configMap["title_img"])),
			OmegaSw:    omega,
		}
		if maxPrice == 0.0 {
			data = &proto.BannerInfoData{
				Background: proto.StrPtr(cast.ToString(configMap["background"])),
				LeftIcon:   proto.StrPtr(cast.ToString(configMap["left_icon"])),
				Title:      proto.StrPtr(cast.ToString(configMap["title"])),
				OmegaSw:    omega,
			}
		}
	}
	if cardId == "1008" {
		//从准星获取数据
		passengerNoticeBar := logic.buildSelectChildParams(ctx)
		//准星为空直接返回
		if passengerNoticeBar == nil {
			return nil
		}
		var leftIcon *string
		var stayTime *int32
		var backgroundColors []string
		str := cast.ToString(configMap["child_info_left_icon"])
		leftIcon = &str
		endTime := cast.ToInt32(configMap["stay_time"])
		stayTime = &endTime
		backgroundColors = cast.ToStringSlice(configMap["background_colors"])
		data = &proto.BannerInfoData{
			BackgroundColors:   backgroundColors,
			LeftIcon:           leftIcon,
			PassengerNoticeBar: passengerNoticeBar,
			StayTime:           stayTime,
		}
	}
	if cardId == "1010" {
		omegaData := make(map[string]string)
		omegaData["sw_type"] = "1"
		omega := &proto.OmegaCk{
			EventId:    proto.StrPtr("beat_p_bubble_guide_sw"),
			Parameters: &proto.OmegaCkParams{},
		}
		omega.Parameters.SwType = proto.StrPtr(omegaData["sw_type"])
		remindInfoTitle := "remind_info_" + station
		backgroundColors := cast.ToStringSlice(configMap["remind_info_background_colors"])
		data = &proto.BannerInfoData{
			BackgroundColors: backgroundColors,
			LeftIcon:         proto.StrPtr(cast.ToString(configMap["remind_info_left_icon"])),
			Title:            proto.StrPtr(cast.ToString(configMap[remindInfoTitle])),
			OmegaSw:          omega,
		}
	}

	ret := &proto.BannerInfo{
		CasperContent: casperContent,
		CardType:      &CardType,
		Data:          data,
	}
	return ret

}

func (logic *Logic) getCardId(ctx context.Context, params map[string]bool) string {
	var cardId string
	if params["isHomeComing"] {
		cardId = "1010"
		return cardId
	}
	if params["isStation"] && params["isRemindToCarpool"] {
		cardId = "1010"
		return cardId
	}
	if params["isNewUser"] && params["isNewVersion"] {
		cardId = "1006"
		return cardId
	}
	if params["newVersionToChild"] {
		cardId = "1008"
		return cardId
	}
	return ""
}

func (logic *Logic) getIsStation(ctx context.Context, params int32, products []*biz_runtime.ProductInfoFull) (bool, string) {
	if logic.passengerNums >= 4 {
		return false, ""
	}
	station := ""
	for _, product := range products {
		if product.GetBusinessID() != consts.ProductIDSFC {
			continue
		}
		if product.Product.AirportType != IsAirType && product.Product.RailwayType != ISRailwayType {
			return false, ""
		}
		if product.Product.AirportType == IsAirType {
			station = AirType
		}
		if product.Product.RailwayType == ISRailwayType {
			station = RailwayType
		}
	}
	var requireProductList []*MultiRequireProduct
	if logic.request.MultiRequireProduct != nil {
		errUnmarshal := json.Unmarshal([]byte(*logic.request.MultiRequireProduct), &requireProductList)
		if errUnmarshal != nil {
			return false, ""
		}
	}
	if len(requireProductList) > 0 {
		for _, product := range requireProductList {
			if (product.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolSFCar || product.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar) && product.IsSelected == 1 {
				return true, station
			}
		}
	} else if params == 1 {
		return true, station
	}
	return false, ""
}

func (logic *Logic) buildChildSelectBannerInfo(ctx context.Context, maxPrice float64) *proto.BannerInfo {
	//从准星获取数据
	passengerNoticeBar := logic.buildSelectChildParams(ctx)
	//准星为空直接返回
	if passengerNoticeBar == nil {
		return nil
	}
	CardIds := "na_sfc_psg_communicate_1008"
	var CardType = int32(1008)
	casperReq := &Dirpc_SDK_Feeds.CasperCardsReq{
		CardIds: CardIds,
	}
	resp, err := casper.GetCasperFeedResp(ctx, casperReq)
	if err != nil {
		log.Trace.Warnf(ctx, "casper.GetCasperFeedResp", "failed %v", err)
		return nil
	}
	casperContent := &proto.CasperContent{
		Id:       &CardIds,
		Template: resp.Template,
		WeexCdn:  resp.WeexCdn,
	}

	var leftIcon *string
	var stayTime *int32
	var backgroundColors []string
	dcmpContent := dcmp.GetDcmpContent(ctx, "sfc-estimate_bannber_text", nil)
	configMap := make(map[string]interface{})
	errUnmarshal := json.Unmarshal([]byte(dcmpContent), &configMap)
	if errUnmarshal == nil {
		str := cast.ToString(configMap["child_info_left_icon"])
		leftIcon = &str

		time := cast.ToInt32(configMap["stay_time"])
		stayTime = &time

		backgroundColors = cast.ToStringSlice(configMap["background_colors"])
	}
	data := &proto.BannerInfoData{
		BackgroundColors:   backgroundColors,
		LeftIcon:           leftIcon,
		PassengerNoticeBar: passengerNoticeBar,
		StayTime:           stayTime,
	}

	ret := &proto.BannerInfo{
		CasperContent: casperContent,
		CardType:      &CardType,
		Data:          data,
	}
	return ret
}

func (logic *Logic) buildSelectChildParams(ctx context.Context) []*proto.PassengerNoticeBar {
	var noticeBars []*proto.PassengerNoticeBar
	adxRsp, err := adx.GetCarouselFromAdx(ctx, logic.request.GetAccessKeyId(), &logic.userInfo)
	if err != nil || adxRsp == nil {
		return nil
	}
	marshal, err := json.Marshal(adxRsp)
	if err != nil {
		log.Trace.Warnf(ctx, "adx_carouse", "carouse marshal failed,err=%v", err)
		return nil
	}

	err = json.Unmarshal(marshal, &noticeBars)
	if err != nil {
		log.Trace.Warnf(ctx, "adx_carouse", "carouse marshal failed,err=%v", err)
		return nil
	}
	if len(noticeBars) == 0 {
		return nil
	}
	return noticeBars

}

func (logic *Logic) buildPreCancelInfo(ctx context.Context, maxPrice float64, maxExpireTime string, pid int64, batchId string) *proto.SFCPreCancelDetail {
	ret := new(proto.SFCPreCancelDetail)
	if maxExpireTime == "" || maxPrice <= 5 {
		return nil
	}
	loc, _ := time.LoadLocation("Local") //nolint
	t, err := time.ParseInLocation("2006-01-02 15:04:05", maxExpireTime, loc)
	if err != nil {
		return nil
	}
	timestamp := t.Unix()
	if timestamp < time.Now().Unix()+10*60 {
		return nil
	}
	// 获取缓存不展示
	value, err := coupon.GetUfsForPassengerCouponExpire(ctx, pid)
	if err != nil {
		return nil
	}
	if value > 0 {
		return nil
	}
	var dcmpData *proto.SFCPreCancelDetail
	tag := map[string]string{
		"price": cast.ToString(maxPrice),
	}
	dcmpStr := dcmp.GetDcmpContent(ctx, "sfc-estimate_pre_cancel_detail", tag)
	err = json.Unmarshal([]byte(dcmpStr), &dcmpData)
	if err != nil {
		return nil
	}
	ret = dcmpData
	ret.Timestamp = &timestamp
	ret.OmegaParams = &proto.SFCPreCancelOmegaParam{
		BatchId:         batchId,
		EstimateTraceId: util.GetTraceIDFromCtxWithoutCheck(ctx),
	}
	return ret
}

func IsSkuModel(prod *biz_runtime.ProductInfoFull) bool {
	routeGroup := prod.GetBizInfo().RouteInfo.RouteGroup
	param := ApolloModel.NewUser("").
		With("city", strconv.Itoa(int(prod.GetAreaInfo().City))).
		With("phone", prod.GetUserInfo().Phone).
		With("route_group", strconv.Itoa(int(*routeGroup))).
		With("access_key_id", strconv.Itoa(int(prod.GetClientInfo().AccessKeyID))).
		With("app_version", prod.GetAppVersion()).
		With("source", "mamba")
	toggle, err := ApolloSDK.FeatureToggle("intercity_carpool_sku_model_grayscale", param)
	//后续城际自营新开城的库存模式判断统一使用dds返回的is_sku_model
	if (err == nil && toggle.IsAllow()) || biz_runtime.RenderSelfSupportSkuMode(prod) {
		return true
	}
	return false
}
func getIsHighway(product *biz_runtime.ProductInfoFull) int {
	if product == nil || product.GetBillExtraMap() == nil {
		return 0
	}

	throughHighway, ok := product.GetBillExtraMap()["ToRb_have_toll_fee"]
	if !ok {
		return 0
	}

	return util.ToInt(throughHighway)
}

// apollo控制
func (logic *Logic) GetSelectNumInfoSwitch(ctx context.Context) string {
	if logic.userInfo.PID == 0 {
		return ControlGroup
	}
	user := ApolloModel.NewUser(cast.ToString(logic.userInfo.PID))
	user.With("access_key_id", cast.ToString(logic.request.GetAccessKeyId()))
	user.With("app_version", logic.request.GetAppVersion())
	user.With("pid", cast.ToString(logic.userInfo.PID))
	user.With("city", cast.ToString(logic.request.GetFromArea()))
	user.With("key", cast.ToString(logic.userInfo.PID))
	toggle, err := ApolloSDK.FeatureToggle("sfc_select_children", user)
	if err != nil || !toggle.IsAllow() {
		return ControlGroup
	}
	return toggle.GetAssignment().GetGroupName()
}

func getApolloSwitch(ctx context.Context, pid int64, cityId int32, appVersion string, accessKeyId int32) bool {
	pidStr := cast.ToString(pid)
	params := map[string]string{
		"pid":           pidStr,
		"city":          cast.ToString(cityId),
		"access_key_id": cast.ToString(accessKeyId),
		"app_version":   appVersion,
	}
	return apollo2.FeatureToggle(ctx, "sfc_passenger_estimate_double_price_switch", pidStr, params)
}

// 预估价防刷参数构造
func buildSFCRiskReq(ctx context.Context, req *proto.SFCEstimateRequest) *risk.HacSfcEstimatePriceDataBody {
	if req == nil {
		return nil
	}

	ArrayToString := func(array []int64) string {
		departureRange, err := json.Marshal(req.DepartureRange)
		if err != nil {
			return ""
		}
		return string(departureRange)
	}

	params := &risk.HacSfcEstimatePriceDataParams{
		AccessKeyID: int64(req.GetAccessKeyId()),
		ToPoiType:   req.GetToPoiType(),
		AppVersion:  req.GetAppVersion(),
		FromLng:     req.GetFromLng(),
		//PriceTime:           req.PriceTime,
		//OID:                 req.Oid,
		FromName:      req.GetFromName(),
		PassengerNums: int64(req.GetPassengerNums()),
		MapType:       req.GetMapType(),
		//SelectNum:           req.SelectNum,
		ChooseFSearchID: req.GetChooseFSearchid(),
		Wsgenv:          req.GetWsgenv(),
		DepartureRange:  ArrayToString(req.GetDepartureRange()),
		ToLng:           req.GetToLng(),
		API:             "/gulfstream/mamba/v1/pSFCEstimate",
		//PI:                 req.api,
		//CarpoolType:         req.carpooltype,
		OrderType:     int64(req.GetOrderType()),
		FromPoiType:   req.GetFromPoiType(),
		FromAddress:   req.GetFromAddress(),
		DepartureTime: strconv.FormatInt(req.GetDepartureTime(), 10),
		Lat:           req.GetLat(),
		FromPoiID:     req.GetFromPoiId(),
		FromLat:       req.GetFromLat(),
		ToLat:         req.GetToLat(),
		//FromCityID:          req.FromCityID,
		ChooseTSearchID: req.GetChooseTSearchid(),
		Lng:             req.GetLng(),
		ToName:          req.GetToName(),
		//EstimateID:          req.EstimateID,
		//SelectTime:          req.SelectTime,
		ToAddress:           req.GetToAddress(),
		PassengerNum:        int64(req.GetPassengerNums()),
		Token:               req.GetToken(),
		FromPageID:          req.GetFromPageId(),
		ToPoiID:             req.GetToPoiId(),
		MultiRequireProduct: req.GetMultiRequireProduct(),
		//District:            req.District,
		//ToCityID:            req.ToCityId,
		//PriceType:           req.PriceType,

	}

	header := &risk.HacSfcEstimatePriceDataHeader{
		Referer:       proto.GetHeader(ctx, "Referer"),
		DidiHeaderRID: proto.GetHeader(ctx, "didi-header-rid"),
		XRealIP:       proto.GetHeader(ctx, "X-Real-IP"),
		UserAgent:     proto.GetHeader(ctx, "User-Agent"),
		Wsgdid:        proto.GetHeader(ctx, "wsgdid"),
	}

	return &risk.HacSfcEstimatePriceDataBody{
		Header: header,
		Params: params,
	}

}

// GetPrivacyNotSignFromBaiChuan 获取百川协议信息
func GetPrivacyNotSignFromBaiChuan(ctx context.Context, accessKeyId int32, uid int64, appVersion string) *baichuanRpc.Privacy {

	appID := consts.ConvertAccessKeyID2BaiChuanAppID(accessKeyId)
	if appID <= 0 {
		return nil
	}

	// 检查策略开关
	if !getSwitch(ctx, uid, accessKeyId, appVersion) {
		return nil
	}

	// 获取隐私策略信息
	sign, err := baichuanRpc.GetPrivacyNotSign(ctx, consts.BaiChuanSFCPolicy, appID, uid)
	if err != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "PSFCEstimate NewLogic GetPrivacyNotSign err=%+v", err)
		return nil
	}
	if sign == nil {
		return nil
	}

	return &baichuanRpc.Privacy{
		DocId:   sign.DocId,
		LinkUrl: sign.LinkUrl,
		Name:    sign.Name,
	}

}

// fillBottomInfo 获取百川协议信息
func fillBottomInfo(ctx context.Context, logic *Logic, ret *proto.SFCEstimateResponse) *proto.SFCEstimateResponse {

	// 策略开关: pid 灰度 + accessKeyID 列表
	policyEnabled := getSwitch(ctx, int64(logic.userInfo.UID), logic.request.AccessKeyId, logic.request.AppVersion)

	// 公共字段设置
	setCommonFields := func() {
		if ret.AdditionalInfo == nil {
			ret.SubmitButton.Subtitle = &logic.dcmpData.SubmitButtonSubtitle
		}
		//底部意外险说明
		ret.BottomInfo = logic.dcmpData.BottomInfo
	}

	if policyEnabled {
		// 协议填充
		if logic.policy != nil {
			logic.dcmpData.BottomInfo.JumpUrl = logic.policy.LinkUrl
			logic.dcmpData.BottomInfo.PolicyId = logic.policy.DocId
			setCommonFields()
		}
	} else {
		if getInsurance := ufs.GetUserAuthorizedInsuranceTime(ctx, cast.ToInt64(logic.userInfo.PID)); getInsurance == 0 {
			setCommonFields()
		}
	}
	return ret
}

// getSwitch 获取灰度开关
func getSwitch(ctx context.Context, uid int64, accessKeyId int32, appVersion string) bool {
	// apollo 配置生效 accessKeyID
	accessKeyIDList := strings.Split(
		apolloUtil.GetConfigItem(consts.ApolloBizConfig, consts.PolicyAccessKeyIDName, consts.PolicyAccessKeyIDKey),
		consts.MarkComma,
	)
	if !lo.Contains(accessKeyIDList, cast.ToString(accessKeyId)) {
		return false
	}

	if !beatlesApollo.GetSwitchInsurance(ctx, uid, "", 0) {
		return false
	}
	if !beatlesModel.CheckInsuranceVersion(ctx, accessKeyId, appVersion) {
		return false

	}
	return true
}
