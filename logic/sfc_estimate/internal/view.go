// Package internal
package internal

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/beatles-common/constant"
	"git.xiaojukeji.com/gulfstream/beatles-common/model"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/apollo"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/order"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/sfc_car"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"github.com/spf13/cast"
)

const (
	Default = iota
	SFCUnique
	SFCCarpool
	SFCCarpoolIntercity
	Intercity
	IntercityTripCloud
)

// Render 数据渲染
func Render(ctx context.Context, product *biz_runtime.ProductInfoFull, dcmpData proto.SFCEstimateDcmp, isCrossCity int8,
	isNewUser, isNewVersion, isCouponMarketingSwitch bool, estimateAbGroup string, newPriceSwitch, doubleSwitch bool, passengerCount int32, extraParams map[string]string, newFenceStyleSwitch bool) (*proto.SFCEstimateData, string, string, string, string) {
	var (
		resp *proto.SFCEstimateData
	)

	if product == nil || product.Product == nil {
		return nil, "", "", "", ""
	}

	// 无差异化且无特殊逻辑的字段
	resp = initEstimateData(product)

	prov := &SFCarAdapter{product}
	discount := ""
	customTag := ""
	selfCoupon := ""
	expireTime := ""
	batchId := ""
	couponType := ""
	// 渲染
	resp.CarTitle = car_info.GetNormalCarName(ctx, prov)
	resp.CarIcon = car_info.GetCarIconByDcmp(ctx, prov)
	resp.IsSelected = sfc_car.GetSelection(ctx, prov)
	resp.SubTitle = sfc_car.GetSubTitle(ctx, prov, dcmpData)
	//这里拼成要用未拼成的优惠券
	resp.PriceInfo = sfc_car.GetEstimatePriceInfo(ctx, prov, dcmpData, isCrossCity, product.Product.EstimateID, estimateAbGroup, newPriceSwitch, doubleSwitch, passengerCount, extraParams, newFenceStyleSwitch)
	resp.Bubble, discount, customTag, selfCoupon, expireTime, batchId = sfc_car.GetCouponBubble(ctx, product, dcmpData)
	resp.CouponList, couponType = sfc_car.GetCouponList(ctx, discount, isNewUser, isNewVersion, selfCoupon, customTag, isCouponMarketingSwitch, resp.PriceInfo.TitleRollInfo, resp.PriceInfo.SubtitleRollInfo, extraParams)
	if isNewVersion && isNewUser {
		resp.Bubble = nil
	}
	if isCouponMarketingSwitch {
		resp.Bubble = nil
	}
	return resp, discount, expireTime, batchId, couponType
}

func GetPolymericProductCategory(pcid, carpoolType int64) int32 {
	switch pcid {
	case estimate_pc_id.EstimatePcIdAloneSFCar:
		return SFCUnique
	case estimate_pc_id.EstimatePcIdCarpoolSFCar:
		return SFCCarpool
	case estimate_pc_id.EstimatePcIdCarpoolCrossSFCar:
		return SFCCarpoolIntercity
	case estimate_pc_id.EstimatePcIdCarpoolInter:
		return Intercity
	default:
		if carpoolType == consts.CarPoolTypeInterCity {
			return IntercityTripCloud
		}
		return Default
	}
}

func initEstimateData(product *biz_runtime.ProductInfoFull) *proto.SFCEstimateData {
	extraMap := &proto.SFCExtraMap{
		ProductCategory:          int32(product.Product.ProductCategory),
		RequireLevel:             util.ToInt32(product.Product.RequireLevel),
		BusinessId:               int32(product.Product.BusinessID),
		ProductId:                int32(product.Product.ProductID),
		ComboType:                int32(product.Product.ComboType),
		LevelType:                product.Product.LevelType,
		CarpoolType:              int32(product.Product.CarpoolType),
		CountPriceType:           0,
		PayType:                  consts.PayTypeByPerson, // 顺风车暂只有个人付
		PolymericProductCategory: GetPolymericProductCategory(product.Product.ProductCategory, product.Product.CarpoolType),
	}

	estimateData := &proto.SFCEstimateData{
		EstimateId: product.Product.EstimateID,
		ExtraMap:   extraMap,
	}

	return estimateData
}

// GetPassengerSeatInfo 人数组件
func GetPassengerSeatInfo(ctx context.Context, passengerNum int32, maxNum int32, dcmpData proto.SFCEstimateDcmp, newVersion bool) *proto.PassengerSeatInfo {
	// 【简化版】：标题文案，最大人数等配在dcmp，之后根据需求考虑如何优化配置
	var (
	//passengerType = make([]*proto.PassengerType, 0)
	)
	dcmpTemplate := dcmpData.SeatInfo
	// dcmp结构："passenger_type": [{"type": 1, "name": "成人", "default_num":1, "min_num":0}]
	//passengerTypeStr := gjson.Get(dcmpTemplate, "passenger_type").String()
	//err := json.Unmarshal([]byte(passengerTypeStr), &passengerType)
	//if err != nil {
	//	return nil
	//}

	// 注意！！此处展示的人数总和要和默认预估人数保持一致，默认预估人数指入参人数不传时使用的人数
	//for _, item := range passengerType {
	//	totalNum += item.DefaultNum
	//}
	var subtitle string
	if newVersion {
		subtitle = dcmpTemplate.NewSubTitle
	} else {
		subtitle = dcmpTemplate.SubTitle
	}
	return &proto.PassengerSeatInfo{
		Title:      dcmpTemplate.Title,
		SubTitle:   subtitle,
		MaxSeatNum: maxNum,
		TotalNum:   passengerNum,
		//PassengerType: passengerType,
	}
}

// HandelMultiRequireProduct 处理未选中品类的情况
func HandelMultiRequireProduct(ctx context.Context, estimateData []*proto.SFCEstimateData, selectCarpool int32, selectAlone int32, pid uint64, isMulti int32, req proto.SFCEstimateRequest, fromArea int32, toArea int32, driverMetre int64) ([]*proto.SFCEstimateData, int32, int32) {
	//都没选中 取上次勾选逻辑；没有 市内订单且里程<=30km「独享」|其他情况「愿拼」
	if selectCarpool == 0 && selectAlone == 0 {

		//实验
		isTest := apollo.GetEstimateOptimize(ctx, cast.ToInt64(pid), req.GetAppVersion(), req.GetAccessKeyId(), fromArea)
		if order.GetCarTypeSave(pid, req.GetAppVersion(), cast.ToString(req.GetAccessKeyId()), fromArea) == true {
			//获取上次勾选结果
			res, cacheInfo := ufs.GetUserCarTypeSelect(ctx, cast.ToInt64(pid), cast.ToString(isMulti))
			if res != true || cacheInfo == nil || (cacheInfo.Carpool == 0 && cacheInfo.Alone == 0) {
				//selectCarpool = 1
				selectCarpool, selectAlone = GetDefaultMarkOptimize(ctx, isTest, driverMetre, fromArea, toArea)
			} else { //从ufs取
				selectCarpool = cacheInfo.Carpool
				selectAlone = cacheInfo.Alone

				//都选中 默认选拼车
				//if selectCarpool == 1 && selectAlone == 1 { //版本号大于等于二期版本号{
				//	selectAlone = 0
				//}
			}
		} else {
			selectCarpool, selectAlone = GetDefaultMarkOptimize(ctx, isTest, driverMetre, fromArea, toArea)
			//selectCarpool = 1
		}
	}

	for _, val := range estimateData {
		if val.ExtraMap.ProductCategory == estimate_pc_id.EstimatePcIdAloneSFCar {
			if len(estimateData) == 1 { //只展示了一个 默认选中（座位数=车最大载重时）
				selectAlone = 1
			}
			val.IsSelected = selectAlone

		} else if val.ExtraMap.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolSFCar ||
			val.ExtraMap.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar {
			if len(estimateData) == 1 { //只展示了一个 默认选中
				selectCarpool = 1
			}
			val.IsSelected = selectCarpool
		}
		//双勾 新版本 且只支持单勾时
		if selectCarpool == 1 && selectAlone == 1 && isMulti == 0 && model.CompareAppVersion(req.GetAppVersion(), constant.SecondPhaseVersion) >= 0 {
			selectCarpool = 1
			selectAlone = 0
			break
		}
	}

	return estimateData, selectCarpool, selectAlone
}

// GetDefaultMarkOptimize 发单框默勾逻辑
// return carpool alone
func GetDefaultMarkOptimize(ctx context.Context, isTest bool, driverMetre int64, fromArea int32, toArea int32) (int32, int32) {
	if isTest {
		//市内订单且里程<=30km，默认勾选「独享」；其他情况，默认勾选「愿拼」
		if fromArea == toArea && driverMetre <= 30000 {
			return 0, 1
		}
	}

	//对照组：默认勾选「愿拼」&&实验组其他情况
	return 1, 0
}
