// Package internal 顺风车相关方法
package internal

import (
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/coupon"
	BeatlesUtil "git.xiaojukeji.com/gulfstream/beatles-common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
)

type SFCarAdapter struct {
	*biz_runtime.ProductInfoFull
	//apolloParams *plain_text_render.ApolloParams
}

// GetPID 获取乘客pid
func (a *SFCarAdapter) GetPID() int64 {
	user := a.GetUserInfo()
	if user == nil {
		return 0
	}

	return user.PID
}

func (a *SFCarAdapter) GetAccessKeyId() int32 {
	if a.GetClientInfo() == nil {
		return 0
	}
	return a.GetClientInfo().AccessKeyID
}

// GetIsSelected 获取是否选中
func (a *SFCarAdapter) GetIsSelected() int32 {
	multiRequireProds := a.BaseReqData.CommonBizInfo.MultiRequireProduct
	if multiRequireProds == nil || len(multiRequireProds) == 0 {
		return 0
	}
	for _, item := range multiRequireProds {
		if item.ProductCategory == a.GetProductCategory() {
			return item.IsSelected
		}

		//兼容跨城，市内切换问题
		if (item.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolSFCar && a.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar) ||
			(item.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar && a.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolSFCar) {
			return item.IsSelected
		}
	}

	return 0
}

// HasMultiRequireProduct 是否多品类
func (a *SFCarAdapter) HasMultiRequireProduct() int32 {
	multiRequireProds := a.BaseReqData.CommonBizInfo.MultiRequireProduct
	if multiRequireProds == nil || len(multiRequireProds) == 0 {
		return 0
	}
	return 1
}

// GetMultiPrice 获取金额
func (a *SFCarAdapter) GetMultiPrice() []float64 {
	var (
		estimateFeeList = make([]float64, 0)
	)

	prices := a.GetSceneEstimatePrice()
	for key, price := range prices {
		if a.GetProductCategory() == estimate_pc_id.EstimatePcIdAloneSFCar { //独享
			estimateFeeList = append(estimateFeeList, price.GetFee())
		} else {
			if key == 0 { //拼成(用未拼成优惠券)
				carpoolSuccPrice := price.GetFeeDetail().GetCapPrice()
				priceWithDiscount := carpoolSuccPrice
				carpoolFailedCoupon := a.GetCarpoolFailedCouponFromRawData()
				if carpoolFailedCoupon != nil {
					CarpoolFailCouponAmount := BeatlesUtil.RoundHalfUp(cast.ToFloat64(carpoolFailedCoupon.Amount)/100, 2) //条件：拼失败抵扣金额必大于拼成功金额
					priceWithDiscount, _ = coupon.GetCarpoolSuccInfoWithCoupon(carpoolSuccPrice, CarpoolFailCouponAmount)
				}

				priceWithDiscount = BeatlesUtil.RoundHalfUp(priceWithDiscount, 2)
				estimateFeeList = append(estimateFeeList, priceWithDiscount)
			} else { //未拼成
				estimateFeeList = append(estimateFeeList, price.GetFee())
			}
		}
	}

	return estimateFeeList
}

func (a *SFCarAdapter) GetExtraInfo() []map[string]interface{} {
	extraInfoList := make([]map[string]interface{}, 0, 2)
	extraInfoList = append(extraInfoList, a.GetBillExtraMap())
	extendList := a.GetExtendList()
	if extendList != nil && len(extendList) > 0 {
		carpoolFailExtraInfo := extendList[0].BillInfo.HistoryExtraMap
		if extraMap, ok := carpoolFailExtraInfo.(map[string]interface{}); ok {
			extraInfoList = append(extraInfoList, extraMap)
		}
	}
	return extraInfoList
}

func (a *SFCarAdapter) GetOriginalPriceInfo() []float64 {
	var originalPriceList []float64
	extraInfoList := a.GetExtraInfo()
	if extraInfoList == nil || len(extraInfoList) <= 0 {
		return nil
	}
	// 处理拼车
	if len(extraInfoList) == 2 {
		if originalCapPrice, ok := extraInfoList[0]["original_cap_price"]; ok {
			originalPriceList = append(originalPriceList, cast.ToFloat64(originalCapPrice))
		}
		if originalCapPrice, ok := extraInfoList[1]["original_cap_price"]; ok {
			originalPriceList = append(originalPriceList, cast.ToFloat64(originalCapPrice))
		}
	} else { //处理独享
		if originalCapPrice, ok := extraInfoList[0]["original_cap_price"]; ok {
			originalPriceList = append(originalPriceList, cast.ToFloat64(originalCapPrice))
		}
	}
	return originalPriceList
}

func (a *SFCarAdapter) GetPriceRollSwitch() int32 {
	if priceRoll, ok := a.GetBillExtraMap()["show_roll_price"]; ok {
		return cast.ToInt32(priceRoll)
	}
	return 0
}
