package data

import (
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	midl "git.xiaojukeji.com/dirpc/dirpc-go-http-Member"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
)

type PetAnyCarAdapter struct {
	*biz_runtime.ProductInfoFull
	// apolloParams *plain_text_render.ApolloParams
}

func (a *PetAnyCarAdapter) GetTabId() string {
	return a.BaseReqData.CommonInfo.TabId
}

func (a *PetAnyCarAdapter) GetEstimateStyleType() int32 {
	return a.BaseReqData.CommonInfo.EstimateStyleType
}

func (a *PetAnyCarAdapter) GetTimeSpan() []*EstimateDecision.TimeSpanV2 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil || len(bizInfo.TimeSpan) == 0 {
		return nil
	}
	return bizInfo.TimeSpan
}

func (a *PetAnyCarAdapter) GetEstimateFeeAmount() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	return fee.GetFee()
}

func (a *PetAnyCarAdapter) GetBusinessPayAmount() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}

	paymentType := a.GetCurrentPaymentType()
	if consts.BusinessPaymentType == paymentType {
		return fee.GetFee()
	}

	return 0
}
func (a *PetAnyCarAdapter) GetBonus() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (a *PetAnyCarAdapter) GetCoupon() *fee_info_render.FeeItem {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return nil
	}
	coupon := fee.GetFeeDetail().GetCoupon()
	if coupon == nil {
		return nil
	}
	return &fee_info_render.FeeItem{
		Tag:    coupon.Tag,
		Amount: coupon.Amount,
	}
}

func (a *PetAnyCarAdapter) GetHolidayFee() (amount float64) {

	if a.GetBillDisplayLines() == nil {
		return
	}

	for _, item := range a.GetBillDisplayLines() {
		if item.Name == "red_packet" && item.Value > 0.0 {
			amount = item.Value
		}
	}

	return
}

func (a *PetAnyCarAdapter) GetMaxSeatNum() int32 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.MaxCarpoolSeatNum
}
func (a *PetAnyCarAdapter) GetUserSelectSeatNum() int32 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.CarpoolSeatNum
}

func (a *PetAnyCarAdapter) GetAllPaymentOptions() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := a.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (a *PetAnyCarAdapter) GetCurrentPaymentType() int {
	opts := a.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (a *PetAnyCarAdapter) GetMultiRequireProduct() []models.RequireProduct {
	if a.BaseReqData == nil {
		return nil
	}

	return a.BaseReqData.CommonBizInfo.MultiRequireProduct
}

func (a *PetAnyCarAdapter) GetMenuId() string {
	return a.BaseReqData.CommonInfo.MenuID
}

func (a *PetAnyCarAdapter) GetUserPhone() string {
	if a.BaseReqData == nil {
		return ""
	}
	return a.BaseReqData.PassengerInfo.Phone
}

func (a *PetAnyCarAdapter) GetUserChoosePayment() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonInfo.PaymentsType
}

func (a *PetAnyCarAdapter) GetCarpoolSeatNum() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonBizInfo.CarpoolSeatNum
}

func (a *PetAnyCarAdapter) GetProductCheckStatus() int {
	return a.GetBizInfo().CheckStatus
}

func (a *PetAnyCarAdapter) GetPassengerCount() *int32 {
	if a.BaseReqData == nil {
		return nil
	}
	return a.BaseReqData.CommonBizInfo.PassengerCount
}

func (a *PetAnyCarAdapter) GetIsShowFemaleDriver() int64 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonBizInfo.IsShowFemaleDriverFirst
}

func (a *PetAnyCarAdapter) GetBillRouteIdList() []string {
	if a.GetBillInfo() == nil {
		return nil
	}
	return a.GetBillInfo().RouteIdList
}

func (a *PetAnyCarAdapter) GetBillDriverMetre() int64 {
	if a.GetBillInfo() == nil {
		return 0
	}
	return a.GetBillInfo().DriverMetre
}

func (a *PetAnyCarAdapter) GetBillFeeDetailInfoFee(key string) (bool, float64) {
	if len(a.GetBillFeeDetailInfo()) < 1 {
		return false, 0
	}
	return true, a.GetBillFeeDetailInfo()[key]
}

func (a *PetAnyCarAdapter) GetFullProduct() *biz_runtime.ProductInfoFull {
	return a.ProductInfoFull
}

func (a *PetAnyCarAdapter) GetFastEstimatePrice() float64 {
	return a.BaseReqData.CommonBizInfo.FastCarPrice
}

// IsBusinessPay 是否是企业付
func (a *PetAnyCarAdapter) IsBusinessPay() bool {
	return a.GetDefaultPayType() == consts.BusinessPaymentType ||
		a.GetDefaultPayType() == consts.BusinessPayByTeam
}

// GetMixedDeductPrice 返回企业付 抵扣金额
func (a *PetAnyCarAdapter) GetMixedDeductPrice() float64 {
	var deductInfo float64

	if !a.IsBusinessPay() {
		return 0.0
	}

	deductInfo = a.GetEstimateFee()

	if payInfo := a.GetPaymentInfo(); payInfo != nil && payInfo.MixedPayDeductInfo != nil && payInfo.MixedPayDeductInfo.DeductFee > 0 {
		deductInfo = payInfo.MixedPayDeductInfo.DeductFee
	}

	return deductInfo
}

func (a *PetAnyCarAdapter) GetVcard() *PriceApi.EstimateNewFormVCardInfo {
	return a.GetVCard()
}

func (a *PetAnyCarAdapter) GetCountPriceType() int32 {
	if a.GetBillInfo() == nil {
		return 0
	}

	return a.GetBillInfo().CountPriceType
}

func (a *PetAnyCarAdapter) GetMemberLevel() int32 {
	if a.GetPrivateBizInfo() == nil {
		return 0
	}

	return a.GetPrivateBizInfo().UserMemberProfile.LevelID
}

func (a *PetAnyCarAdapter) GetMemberProfile() *midl.V1QueryInfo {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil {
		return nil
	}

	return bizInfo.UserMemberProfile.MemberProfile
}

func (a *PetAnyCarAdapter) GetCarpoolFailExactEstimateFee() float64 {
	if len(a.GetExtendList()) == 1 {
		return a.GetExtendList()[0].ExactEstimateFee
	}

	return 0
}

func (a *PetAnyCarAdapter) GetNoAnswerCompensationData() []*Compensation.GetNoAnswerCompensationTackData {
	if a.BaseReqData == nil {
		return nil
	}

	return a.GetCommonBizInfo().NoAnswerCompensationData
}
