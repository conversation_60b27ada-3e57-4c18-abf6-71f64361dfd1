package pet_anycar_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/dao/ufs_logic"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"git.xiaojukeji.com/nuwa/trace"
	"sort"
	"strconv"
)

type BaseParams struct {
	OrderInfo    *dos.OrderInfo
	UserInfo     *passport.UserInfo
	OrderFeature *ufs.OrderFeature
	ExtraParams  *ExtraParams
}

type ExtraParams struct {
	MaxPrice float64
}

// mamab协议，
func InitProductGenerator(ctx context.Context, req *proto.AnyCarEstimateReq, orderInfo *dos.OrderInfo, reqFrom string) (*biz_runtime.ProductsGenerator, int) {
	var (
		errno       int
		err         error
		baseParams  *BaseParams
		productsGen *biz_runtime.ProductsGenerator
	)

	baseParams, errno = initParams(ctx, req, orderInfo)
	if consts.NoErr != errno {
		return nil, errno
	}

	productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom(reqFrom),
		biz_runtime.WithOrderInfo(ctx, req, baseParams.OrderInfo, baseParams.UserInfo, baseParams.OrderFeature),
		biz_runtime.WithInfoByRequest(ctx, req),
		biz_runtime.WithPetInfoByRequest(ctx, req, baseParams.OrderInfo),
	)
	if err != nil {
		return nil, consts.GetErrorNum(err)
	}

	//专车独有逻辑，没有增值服务时过滤专车品类
	if ssseRPC := rpc_process.NewAdditionalServiceHundun(ctx, productsGen.BaseReqData); ssseRPC != nil {
		productsGen.RegisterBeforePriceRpcProcess(ssseRPC)
		productsGen.RegisterBeforePriceFilter(ssseRPC)
	}
	// 处理默勾,临时方案
	productsGen.SetNeedMember(true)
	productsGen.SetSendReqKafka(true)
	productsGen.SetNeedCarAggregation(true)

	// 注册过滤器
	RegisterProductFilter(ctx, baseParams, productsGen)

	return productsGen, consts.NoErr
}

func initParams(ctx context.Context, req *proto.AnyCarEstimateReq, orderInfo *dos.OrderInfo) (params *BaseParams, errno int) {
	//请求 passport
	userInfo, err := passport.GetUserInfo(ctx, req.Token, passport.Callee)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getUserInfo: %v", err)
		return nil, consts.ErrnoGetUserInfo
	}

	// 请求 ufs
	orderFeature, err := ufs_logic.GetOrderFeature(ctx, orderInfo, req)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getOrderFeature: %v", err)
		return nil, consts.ErrnoGetOrderFeature
	}

	// 获取已发单车型最高价格
	maxPrice := getMaxPrice(orderInfo)

	// 订单归属校验
	if orderInfo.PassengerId != strconv.FormatInt(int64(userInfo.PID), 10) {
		return nil, consts.ErrnoOrderNotBelongUser
	}

	// 设置PageType
	req.PageType = util.Int32Ptr(page_type.PageTypePetAppendFormCar)
	// 创建extraParams
	extraParams := &ExtraParams{
		// 已发单品类最高价
		MaxPrice: maxPrice,
	}

	params = &BaseParams{
		OrderInfo:    orderInfo,
		UserInfo:     userInfo,
		OrderFeature: orderFeature,
		ExtraParams:  extraParams,
	}
	return params, consts.NoErr
}

func getMaxPrice(orderInfo *dos.OrderInfo) float64 {
	if orderInfo == nil || len(orderInfo.ExtendFeatureParsed.MultiRequiredProduct) == 0 {
		return 0
	}
	priceList := make([]float64, 0)
	for _, productInfo := range orderInfo.ExtendFeatureParsed.MultiRequiredProduct {
		priceList = append(priceList, productInfo.EstimateFee)
	}

	// 排序取最高价
	sort.Float64s(priceList)
	return priceList[len(priceList)-1]
}
