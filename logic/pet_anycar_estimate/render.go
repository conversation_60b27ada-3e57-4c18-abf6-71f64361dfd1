package pet_anycar_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	pool2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/pool"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v3"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pet_anycar_estimate/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	context2 "git.xiaojukeji.com/lego/context-go"
	Trace "git.xiaojukeji.com/nuwa/trace"
)

func PetRenderProductList(ctx context.Context, gen *biz_runtime.ProductsGenerator, products []*biz_runtime.ProductInfoFull) (
	*proto.NewFormEstimateResponse, int) {

	var (
		estimateData    = make(map[int64]*proto.NewFormEstimateData)   // 渲染后数据
		rawEstimateData = make(map[int64]*proto.RawEstimateData)       // 纯数据
		productMap      = make(map[int64]*biz_runtime.ProductInfoFull) // 存储已渲染的品类数据
		resp            = &proto.NewFormEstimateResponse{
			EstimateTraceId: context2.GetTrace(ctx).GetTraceId(),
		}
		renderService = data.NewRender(ctx)

		renderFunc pool2.TaskFunc
	)

	renderService.PreBuilder(ctx, products)
	renderFunc = data.RenderByProduct

	// 1. 构建预估数据
	pool := pool2.NewPool(ctx)
	for _, pFull := range products {
		pool.AddTask(&pool2.Task{
			Req:     pFull,
			RunTask: renderFunc,
		})
	}

	//并行渲染
	_, err := pool.MultiProc()
	if err != nil {
		log.Trace.Warnf(ctx, Trace.DLTagUndefined, "multi_render got err: %v", err)
		return resp, consts.ErrnoRenderErr
	}
	//串行封装结果
	for _, task := range pool.GetResult() {
		if task.GetErr() != nil || task.Resp == nil {
			continue
		}
		pFull, ok1 := task.Req.(*biz_runtime.ProductInfoFull)
		if !ok1 {
			continue
		}
		buildEstimateProduct(task, pFull, productMap, estimateData)
		if assembleResp, ok := task.Resp.(*biz_runtime.AssembleResp); ok && assembleResp != nil {
			rawData := assembleResp.RawEstimateData
			rawEstimateData[rawData.ProductCategory] = rawData
		}
	}

	resp.EstimateData = estimateData
	resp.RawEstimateData = rawEstimateData

	resp.Layout = anycar_v3.New(ctx, gen.BaseReqData, estimateData, productMap).BuildLayout(ctx)
	// 3. 复用 预估额外数据
	renderService.ExtendDataRender(ctx, resp, gen)
	return resp, consts.NoErr
}

func buildEstimateProduct(task *pool2.Task, pFull *biz_runtime.ProductInfoFull, productMap map[int64]*biz_runtime.ProductInfoFull, estimateData map[int64]*proto.NewFormEstimateData) {
	if assembleResp, ok := task.Resp.(*biz_runtime.AssembleResp); ok && assembleResp != nil {
		product := assembleResp.EstimateData
		pFull.Product.BizInfo.CheckStatus = int(product.IsSelected)
		productMap[product.ProductCategory] = pFull
		estimateData[product.ProductCategory] = product
	}
}
