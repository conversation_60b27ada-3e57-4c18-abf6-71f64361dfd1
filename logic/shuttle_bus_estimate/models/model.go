package models

import "git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

type CommonParam struct {
	Token          string  `json:"token"`
	AppVersion     string  `json:"app_version"`
	PageType       int32   `json:"page_type"`
	UserType       int32   `json:"user_type"`
	SourceType     int32   `json:"source_type"`
	AccessKeyID    int32   `json:"access_key_id"`
	Channel        string  `json:"channel"`
	Lang           string  `json:"lang"`
	CityId         int32   `json:"city_id"`
	MapType        string  `json:"map_type"`
	FromLat        float64 `json:"from_lat"`
	FromLng        float64 `json:"from_lng"`
	ToLat          float64 `json:"to_lat"`
	ToLng          float64 `json:"to_lng"`
	RegionId       int32   `json:"region_id"` // 区域id
	CarpoolSeatNum int32   `json:"carpool_seat_num"`
	StartStationId int32   `json:"start_station_num"`
	EndStationId   int32   `json:"end_station_id"`
	RouteId        int32   `json:"route_id"`
	IsForGuideBar  bool    `json:"is_for_guide_bar"` // 是否是给预估和等待应答导流的数据
	DistanceType   int32   `json:"distance_type"`
}

type StationInfo struct {
	StationId int     `json:"station_id"`
	CityId    int     `json:"city_id"`
	Name      string  `json:"name"`
	Address   string  `json:"address"`
	Lat       float64 `json:"lat"`
	Lng       float64 `json:"lng"`
}

type BaseInfos struct {
	PassengerInfo models.PassengerInfo
	Token         string
	AreaInfo      models.AreaInfo
	CommonInfo    models.CommonInfo
	CommonBizInfo models.CommonBizInfo
}
