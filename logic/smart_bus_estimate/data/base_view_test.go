package data

import (
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/from_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func Test_BuildBestViewType(t *testing.T) {
	convey.Convey("Test_BuildBestViewType", t, func() {
		resp := &proto.MiniBusEstimateData{}
		baseReqData := &models.BaseReqData{
			CommonInfo:    models.CommonInfo{},
			CommonBizInfo: models.CommonBizInfo{},
		}

		// 测试正常情况，默认显示起点模式
		baseReqData.CommonInfo.FromType = 1
		baseReqData.CommonBizInfo.PreMatchTPErrNo = 0
		(BuildBestViewType)(resp, baseReqData)
		convey.So(resp.BestViewType, convey.ShouldEqual, BestViewTypeStartPoint)

		// 测试车上扫码场景
		baseReqData.CommonInfo.FromType = from_type.FromTypeFromTypeScanInCar
		(BuildBestViewType)(resp, baseReqData)
		convey.So(resp.BestViewType, convey.ShouldEqual, BestViewTypeOverview)

		// 测试起终点过近场景
		baseReqData.CommonInfo.FromType = 1
		baseReqData.CommonBizInfo.PreMatchTPErrNo = rpc_process.ErrPreMatchStartDestOverlap
		(BuildBestViewType)(resp, baseReqData)
		convey.So(resp.BestViewType, convey.ShouldEqual, BestViewTypeOverview)
	})
}

func Test_BuildBestViewTypeSwitch(t *testing.T) {
	convey.Convey("Test_BuildBestViewTypeSwitch", t, func() {
		resp := &proto.MiniBusEstimateData{}
		baseReqData := &models.BaseReqData{
			CommonInfo:    models.CommonInfo{},
			CommonBizInfo: models.CommonBizInfo{},
		}

		// 测试正常情况，允许切换
		baseReqData.CommonInfo.FromType = 1
		baseReqData.CommonBizInfo.PreMatchTPErrNo = 0
		(BuildBestViewTypeSwitch)(resp, baseReqData)
		convey.So(resp.DisableBestViewSelect, convey.ShouldEqual, EnableBestViewSelect)

		// 测试车上扫码场景
		baseReqData.CommonInfo.FromType = from_type.FromTypeFromTypeScanInCar
		baseReqData.CommonBizInfo.PreMatchTPErrNo = 0
		resp = &proto.MiniBusEstimateData{} // 重新初始化 resp
		(BuildBestViewTypeSwitch)(resp, baseReqData)
		convey.So(resp.DisableBestViewSelect, convey.ShouldEqual, DisableBestViewSelect)

		// 测试起终点过近场景
		baseReqData.CommonInfo.FromType = 1
		baseReqData.CommonBizInfo.PreMatchTPErrNo = rpc_process.ErrPreMatchStartDestOverlap
		resp = &proto.MiniBusEstimateData{} // 重新初始化 resp
		(BuildBestViewTypeSwitch)(resp, baseReqData)
		convey.So(resp.DisableBestViewSelect, convey.ShouldEqual, DisableBestViewSelect)
	})
}

func Test_buildRespWithNoTP(t *testing.T) {
	convey.Convey("Test_buildRespWithNoTP", t, func() {
		// 创建 BaseReqData 实例
		baseReqData := &models.BaseReqData{
			CommonInfo: models.CommonInfo{
				FromType: 1,
			},
			CommonBizInfo: models.CommonBizInfo{
				PreMatchTPErrNo: 0,
			},
		}

		//// 创建 BaseExtraInfo 实例
		conf := &BaseExtraInfo{
			StartDestOverlapIcon: "Overlap icon",
			StartDestOverlapText: "Overlap text",
			DefaultIconScanInCar: "scan in car icon",
			DefaultTextScanInCar: "scan in car text",
			DefaultIcon:          "default icon",
			DefaultText:          "default text",
			RetryButtonText:      "retry button text",
		}

		// 情况一：正常情况
		resp := &proto.MiniBusEstimateData{}
		resp.DynamicEffectParams = &proto.DynamicEffectParams{}
		buildRespWithNoTP(resp, baseReqData, conf)
		convey.So(resp.DynamicEffectParams.DefaultText, convey.ShouldEqual, "default text")

		// 情况二：起终点过近
		baseReqData.CommonBizInfo.PreMatchTPErrNo = rpc_process.ErrPreMatchStartDestOverlap
		resp = &proto.MiniBusEstimateData{}
		resp.DynamicEffectParams = &proto.DynamicEffectParams{}
		buildRespWithNoTP(resp, baseReqData, conf)
		convey.So(resp.DynamicEffectParams.DefaultText, convey.ShouldEqual, "Overlap text")

		// 情况三：扫码上车，不顺路
		baseReqData.CommonInfo.FromType = from_type.FromTypeFromTypeScanInCar
		baseReqData.CommonBizInfo.PreMatchTPErrNo = rpc_process.ErrPreMatchRouteNotMatch
		resp = &proto.MiniBusEstimateData{}
		resp.DynamicEffectParams = &proto.DynamicEffectParams{}
		buildRespWithNoTP(resp, baseReqData, conf)
		convey.So(resp.DynamicEffectParams.DefaultText, convey.ShouldEqual, "scan in car text")
	})
}

func Test_FillMapStationWithoutTP(t *testing.T) {
	convey.Convey("Test_FillMapStationWithoutTP", t, func() {
		resp := &proto.MiniBusEstimateData{}
		baseReqData := &models.BaseReqData{
			CommonInfo: models.CommonInfo{
				PageType: page_type.PageTypeSmartBusEstimate,
				FromType: 1,
			},
			CommonBizInfo: models.CommonBizInfo{},
			AreaInfo: models.AreaInfo{
				Area: 1,
			},
		}

		// 测试正常情况：存在 EstimateData，无需填充
		resp.EstimateData = []*proto.EstimateFromData{{}} // 设置至少一条 EstimateData
		FillMapStationWithoutTP(resp, baseReqData)
		convey.So(len(resp.EstimateData), convey.ShouldEqual, 1) // 验证未改变原始数据

		// 清除 EstimateData 并重新初始化 resp
		resp = &proto.MiniBusEstimateData{}

		// 测试车上扫码场景：填充起始和目标站牌信息
		baseReqData.CommonInfo.FromType = from_type.FromTypeFromTypeScanInCar
		baseReqData.CommonBizInfo.SmartBusData = &models.SmartBusData{
			StartStationInfo: &models.SmartBusStation{
				PoiId:       "start poi id",
				Lat:         123,
				Lng:         456,
				Displayname: "start display name",
			},
			DestStationInfo: &models.SmartBusStation{
				PoiId:       "dest poi id",
				Lat:         789,
				Lng:         987,
				Displayname: "dest display name",
			},
		}

		FillMapStationWithoutTP(resp, baseReqData)
		convey.So(resp.StartStationInfo, convey.ShouldNotBeNil) // 验证起始站牌信息已被填充
		convey.So(resp.DestStationInfo, convey.ShouldNotBeNil)  // 验证目标站牌信息已被填充

		// 清除 EstimateData 并重新初始化 resp
		resp = &proto.MiniBusEstimateData{}

		// 测试其他场景：不应填充站牌信息
		baseReqData.CommonInfo.FromType = 1
		FillMapStationWithoutTP(resp, baseReqData)
		convey.So(resp.StartStationInfo, convey.ShouldBeNil) // 验证未填充起始站牌信息
		convey.So(resp.DestStationInfo, convey.ShouldBeNil)  // 验证未填充目标站牌信息
	})
}
