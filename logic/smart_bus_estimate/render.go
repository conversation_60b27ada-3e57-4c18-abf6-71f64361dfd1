package smart_bus_estimate

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/from_type"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/smart_bus_estimate/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"sort"
)

func RenderProductList(ctx context.Context, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull) (*proto.MiniBusEstimateData, error) {
	var (
		renderService = data.NewRender(ctx, baseReqData)
		estimateData  = make([]*proto.EstimateFromData, 0)
		resp          = &proto.MiniBusEstimateData{}
	)

	//render前 check
	if err := renderCheck(ctx, baseReqData, products); nil != err {
		return nil, err
	}

	//render
	if len(products) > 0 {
		sort.SliceStable(products, func(i, j int) bool {
			// 按etp升序排序
			return products[i].Product.BizInfo.SmartBusPreMatch.EtpInfo.EtpTimeDuration < products[j].Product.BizInfo.SmartBusPreMatch.EtpInfo.EtpTimeDuration
		})

		for _, pFull := range products {
			if product := renderService.RenderByProduct(ctx, pFull); product != nil {
				// 获取座位数
				estimateData = append(estimateData, product)
			}
		}
		data.AdjustSelected(estimateData)
		resp.EstimateData = estimateData
	}

	renderService.ExtendDataRender(ctx, resp, baseReqData, products)

	resp.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
	return resp, nil
}

// renderCheck
func renderCheck(ctx context.Context, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull) error {
	//map token check
	if err := renderMapTokenDependencyCheck(ctx, baseReqData); nil != err {
		return err
	}

	//车上扫码场景
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType {
		if 1 < len(products) {
			log.Trace.Warnf(ctx, consts.TagErrSmartBusScanInCarTpCount, "products:%v", products)
			return BizError.ErrInvalidArgument
		}
	}

	return nil
}

// renderMapTokenDependencyCheck 依赖map_token渲染 check
func renderMapTokenDependencyCheck(ctx context.Context, baseReqData *models.BaseReqData) error {
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType {
		return nil
	}

	if baseReqData.CommonBizInfo.TokenInfo == nil || baseReqData.CommonBizInfo.TokenInfo.MapinfoStartCacheToken == "" || baseReqData.CommonBizInfo.TokenInfo.MapinfoDestCacheToken == "" {
		log.Trace.Warnf(ctx, consts.TagErrMapToken, "token:%v", baseReqData.CommonBizInfo.TokenInfo)
		return BizError.ErrInvalidArgument
	}
	return nil
}
