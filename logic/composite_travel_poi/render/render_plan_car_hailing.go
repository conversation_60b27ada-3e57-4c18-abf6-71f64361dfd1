package render

import (
	"context"
	"fmt"
	"math"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel/model"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

const (
	DCMPCarHailingKey = "one_stop-car_travel_poi"
)

type CarHailingRender struct {
	*BaseRender

	planData *model.PrivateDataCarData
	conf     CarHailingDCMPConf
}

type CarHailingDCMPConf struct {
	LinkUrl         map[string]string `json:"link_url"`
	LinkUrlV2       map[string]string `json:"link_url_v2"`
	DescList        DescListDCMP      `json:"desc_list"`
	ButtonContent   string            `json:"button_content"`
	ButtonColor     string            `json:"button_color"`
	ButtonTextColor string            `json:"button_text_color"`
	RichFeeMsg      string            `json:"rich_fee_msg"`
}
type DescListDCMP struct {
	DriverContent      string `json:"driver_content"` // 行驶距离
	DriverMeterContent string `json:"driver_meter_content"`
	CarCount2          string `json:"car_count_2"` // 两个车型文案
	CarCount3          string `json:"car_count_3"` // 多车型文案
	ETSContent         string `json:"ets_content"`
	ETSMinContent      string `json:"ets_min_content"`
}

func NewCarHailingPlanRender(ctx context.Context, base *BaseRender) *CarHailingRender {
	conf, err := initCarHailingDCMPConf(ctx)
	if err != nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "init car service dcmp error || err = %v", err)
	}
	return &CarHailingRender{BaseRender: base, conf: conf}
}

func initCarHailingDCMPConf(ctx context.Context) (CarHailingDCMPConf, error) {
	conf := CarHailingDCMPConf{}
	str := dcmp.GetDcmpContent(ctx, DCMPCarHailingKey, nil)
	err := jsoniter.UnmarshalFromString(str, &conf)
	if err != nil {
		return conf, err
	}
	return conf, nil
}

func (c *CarHailingRender) Render(ctx context.Context, plan *model.PlanFull) *proto.PlanInfo {
	data := plan.PrivateData
	if data == nil || data.CarData == nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "car service plan render err || data =%v", data)
		return nil
	}
	c.planData = data.CarData

	buttonInfo := &proto.PlanButtonInfo{
		BgGradients: strings.Split(c.conf.ButtonColor, ","),
		Content:     c.conf.ButtonContent,
		FontColor:   c.conf.ButtonTextColor,
	}
	return &proto.PlanInfo{
		Title:          c.renderPlanTitle(ctx, plan),
		EstimateTime:   plan.EstimateTime,
		FeeMsg:         plan.FeeMsg,
		RecommendTag:   nil,
		PlanType:       model.CarHailing,
		TipData:        nil,
		ButtonInfo:     buttonInfo,
		SegmentList:    c.renderSegmentList(ctx, plan),
		DescList:       c.renderDescList(ctx, plan, c.buildDerMeterDesc, c.buildCarNameDesc),
		ReplyDesc:      c.renderDescList(ctx, plan, c.buildEtsDesc),
		RightTitleList: c.renderRightTitleList(ctx, plan, c.getRichFeeMsg, c.buildEstimateTime),
		ProductList:    plan.ProductList,
		LinkType:       model.LinkTypeUrl,
		LinkUrl:        c.buildLinkUrl(ctx, c.conf.LinkUrl, c.conf.LinkUrlV2),
		SceneId:        plan.SceneId,
		Params:         plan.Params,
		MapParams:      plan.MapParams,
		ExtraData:      plan.ExtraData,
	}
}

// 距离展示
func (c *CarHailingRender) buildDerMeterDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	if c.planData == nil || c.planData.DriverMeter <= 0 {
		return nil
	}

	if c.planData.DriverMeter < 1000 {
		return &proto.PlanDesc{
			Content: util.ReplaceTag(ctx, c.conf.DescList.DriverMeterContent, map[string]string{
				"driverMeter": cast.ToString(c.planData.DriverMeter),
			}),
		}
	}

	return &proto.PlanDesc{
		Content: util.ReplaceTag(ctx, c.conf.DescList.DriverContent, map[string]string{
			"driverMeter": util.FormatPrice(float64(c.planData.DriverMeter)/1000, 1),
		}),
	}
}

// 车型展示
func (c *CarHailingRender) buildCarNameDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	if c.planData == nil || len(c.planData.CarNameList) <= 0 {
		return nil
	}
	carNameList := c.planData.CarNameList
	var desc *proto.PlanDesc
	if len(carNameList) == 1 {
		desc = &proto.PlanDesc{
			// 快车
			Content: carNameList[0],
		}
	} else if len(carNameList) == 2 {
		desc = &proto.PlanDesc{
			// 快车、特惠
			Content: fmt.Sprintf(c.conf.DescList.CarCount2, carNameList[0], carNameList[1]),
		}
	} else if len(carNameList) > 1 {
		desc = &proto.PlanDesc{
			// 1个就是车型名称   大于1个就是     前两个默勾车型的车型名称
			Content: fmt.Sprintf(c.conf.DescList.CarCount3, carNameList[0], carNameList[1], len(carNameList)),
		}
	}
	return desc
}

// 应答时间
func (c *CarHailingRender) buildEtsDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	if c.planData == nil || c.planData.EtpInfo == nil || c.planData.EtpInfo.Ets == nil || *c.planData.EtpInfo.Ets <= 0 {
		return nil
	}

	etsInfo := *c.planData.EtpInfo.Ets
	etsContent := c.conf.DescList.ETSContent
	if etsInfo > 60 {
		etsInfo = int32(math.Ceil(float64(etsInfo) / 60))
		etsContent = c.conf.DescList.ETSMinContent
	}
	return &proto.PlanDesc{
		Content: util.ReplaceTag(ctx, etsContent, map[string]string{
			"ets": cast.ToString(etsInfo),
		}),
	}
}

func (c *CarHailingRender) getRichFeeMsg(ctx context.Context, plan *model.PlanFull) string {
	var feeMsg string

	if c.planData.MinFeeAmount == c.planData.MaxFeeAmount {
		feeMsg = c.planData.SingleFeeMsg
		return feeMsg
	}

	feeMsg = util.ReplaceTag(ctx, c.conf.RichFeeMsg, map[string]string{
		"min": cast.ToString(c.planData.MinFeeAmount),
		"max": cast.ToString(c.planData.MaxFeeAmount),
	})
	return feeMsg
}
