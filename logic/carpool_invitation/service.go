package carpool_invitation

import (
	"context"
	"encoding/json"
	"fmt"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
	"time"
)

const (
	RedisKeyReq   = "estimate_req_cache"
	RedisKeyMatch = "P_INVITATION_MATCH_INFO"
)

func CarpoolInvitationEstimate(ctx context.Context, req *proto.CarpoolInvitationEstimateRequest) (rspData *proto.InvitationEstimateData, errno int) {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	orderID, district, err := util.DecodeOrderID(req.GetInviteOid())
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "decodeErr: %v", err)
		return nil, consts.ErrnoDecode
	}

	orderInfo, err := dos.GetOrderInfo(ctx, orderID, district)
	if err != nil || orderInfo == nil {
		return nil, NewErrors.ErrnoRpcFailed
	}

	productsGenerator, errno := InitProductGenerator(ctx, req, orderInfo)
	if consts.NoErr != errno {
		return nil, errno
	}

	products, err = productsGenerator.GenProducts(ctx)
	if len(products) == 0 || err != nil {
		return nil, consts.ErrnoNoProductOpen
	}

	return Render(ctx, req, products)
}

func CacheInvitationMatchInfo(ctx context.Context, data *models.BaseReqData, eid string, isRouteMatch bool, departureRange string) {
	invitationInfo := data.CommonBizInfo.InvitationInfo
	if invitationInfo == nil {
		return
	}

	cacheData := map[string]interface{}{
		"order_type":      data.CommonInfo.OrderType,
		"match_code":      util.Bool2I32(isRouteMatch),
		"invite_order":    invitationInfo.InviterHighOid,
		"departure_range": departureRange,
		"invitation_type": 1,
	}

	marshal, err := json.Marshal(cacheData)
	if err != nil || len(marshal) <= 0 {
		return
	}

	if _, err := redis.GetCarpoolClient().SetEx(ctx, fmt.Sprintf("%s_%s", RedisKeyMatch, eid), 10*60, string(marshal)); err != nil {
		log.Trace.Warnf(ctx, "_CacheMatchInfo", "set redis failed %s", err)
	}
}
func CacheEstimateInfo(ctx context.Context, data *models.BaseReqData, req *proto.CarpoolInvitationEstimateRequest) {
	invitationInfo := data.CommonBizInfo.InvitationInfo
	if invitationInfo == nil || invitationInfo.InviterOid == "" {
		return
	}

	inviteePid := data.PassengerInfo.PID
	oid := invitationInfo.InviterOid

	if cacheData, err := redis.GetEstimateClient().Get(ctx, fmt.Sprintf("%s_%s_%d", RedisKeyReq, oid, inviteePid)); err == nil && cacheData != "" {
		return
	}

	marshal, err := json.Marshal(req)
	if err != nil || len(marshal) <= 0 {
		return
	}

	if _, err := redis.GetEstimateClient().SetEx(ctx, fmt.Sprintf("%s_%s_%d", RedisKeyReq, oid, inviteePid), 300*time.Second, string(marshal)); err != nil {
		log.Trace.Warnf(ctx, "_CacheEstimate", "set redis failed %s", err)
	}
}

func GetEstimateInfo(ctx context.Context, oid string, inviteePid int64) *proto.CarpoolInvitationEstimateRequest {
	key := fmt.Sprintf("%s_%s_%d", RedisKeyReq, oid, inviteePid)
	data, err := redis.GetEstimateClient().Get(ctx, key)
	if err != nil {
		return nil
	}

	var er = &proto.CarpoolInvitationEstimateRequest{}
	UnmarshalErr := json.Unmarshal([]byte(data), er)
	if UnmarshalErr != nil {
		log.Trace.Warnf(ctx, "_CacheEstimate", "get EstimateCache UnmarshalErr=%v ", err)
	} else {
		return er
	}
	return nil
}
