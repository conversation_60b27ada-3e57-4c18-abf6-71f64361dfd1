package wyc_transfer

import (
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// WycAdapter 追加适配器
type WycAdapter struct {
	*biz_runtime.ProductInfoFull
	*model.WycAreaInfo
	*model.Request
}

// IsBusinessPay 是否是企业付
func (a *WycAdapter) IsBusinessPay() bool {
	return a.GetDefaultPayType() == consts.BusinessPaymentType ||
		a.GetDefaultPayType() == consts.BusinessPayByTeam
}

// GetMixedDeductPrice 返回企业付 抵扣金额
func (a *WycAdapter) GetMixedDeductPrice() float64 {
	var deductInfo float64

	if !a.IsBusinessPay() {
		return 0.0
	}

	deductInfo = a.GetEstimateFee()

	if payInfo := a.GetPaymentInfo(); payInfo != nil && payInfo.MixedPayDeductInfo != nil && payInfo.MixedPayDeductInfo.DeductFee > 0 {
		deductInfo = payInfo.MixedPayDeductInfo.DeductFee
	}

	return deductInfo
}

func (a *WycAdapter) GetToCityID() int {
	return int(a.ToCityId)
}

func (a *WycAdapter) GetFromCounty() int32 {
	return a.FromCounty
}

func (a *WycAdapter) GetUID() int64 {
	if a.UserInfo != nil {
		return int64(a.UserInfo.UID)
	}
	return 0
}

func (a *WycAdapter) GetUserPhone() string {
	if a.UserInfo != nil {
		return a.UserInfo.Phone
	}
	return ""
}

func (a *WycAdapter) GetMenuId() string {
	return "dache_anycar"
}

func (a *WycAdapter) GetLang() string {
	if a.Request == nil {
		return ""
	}
	return a.Request.Lang
}

func (a *WycAdapter) GetAppVersion() string {
	if a.Request == nil {
		return ""
	}
	return a.Request.AppVersion
}

func (a *WycAdapter) GetOrderType() int16 {
	if a.Product == nil {
		return 0
	}
	return a.Product.OrderType
}

func (a *WycAdapter) GetCityID() int {
	if a.WycAreaInfo == nil {
		return 0
	}
	return int(a.WycAreaInfo.FromCityId)
}

func (a *WycAdapter) GetPID() int64 {
	if a.UserInfo == nil {
		return 0
	}

	return int64(a.UserInfo.PID)
}

func (a *WycAdapter) GetAccessKeyId() int32 {
	return a.AccessKeyId
}

func (a *WycAdapter) GetETInfoByPcId(pcId int32) *AthenaApiv3.EstimatedTimeInfo {
	if len(a.GetCommonBizInfo().ETInfoMap) > 0 {
		return a.GetCommonBizInfo().ETInfoMap[pcId]
	}
	return nil
}

func (a *WycAdapter) GetMultiRequireProduct() []models.RequireProduct {
	// 用户没有上次勾选
	return nil
}

func (a *WycAdapter) GetProductCheckStatus() int {
	return a.GetBizInfo().CheckStatus
}
