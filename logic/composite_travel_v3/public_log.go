package composite_travel_v3

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	LegoContext "git.xiaojukeji.com/lego/context-go"
	"strconv"
)

const OperaKeyEstimateData = "ws_composite_travel"

func (s *CompositeTravelV3Service) WritePublicLog(ctx context.Context) {
	if s.CompositeTravelV3Req == nil || len(s.planList) < 1 {
		return
	}

	if s.recResp == nil || len(s.recResp.RecItem) < 1 {
		return
	}

	req := s.CompositeTravelV3Req
	athenaAllowPlans := make([]int, 0)

	for _, recItem := range s.recResp.RecItem {
		i, err := strconv.Atoi(recItem.GetUniqueID())
		if err != nil || i > len(s.planList)-1 {
			continue
		}
		athenaAllowPlans = append(athenaAllowPlans, i)
	}

	index := 0
	for i, plan := range s.planList {
		if util.InArrayInt(i, athenaAllowPlans) {
			s.writePlan(ctx, req, index, plan, true)
			index++
		} else {
			s.writePlan(ctx, req, -1, plan, false)
		}

	}

}

func (s *CompositeTravelV3Service) writePlan(ctx context.Context, req *proto.CompositeTravelV3Req,
	i int, plan *model.PlanFull, isShow bool) {
	logInfo := make(map[string]interface{})
	logInfo["trace_id"] = LegoContext.GetTrace(ctx).GetTraceId()
	logInfo["composite_version"] = "9.0"

	// // ..... 基础信息 .....
	logInfo["client_type"] = req.ClientType
	logInfo["access_key_id"] = req.AccessKeyId
	logInfo["app_version"] = req.AppVersion
	logInfo["lang"] = req.Lang
	logInfo["channel"] = req.Channel
	logInfo["page_type"] = "34"
	logInfo["xpsid"] = req.Xpsid
	logInfo["xpsid_root"] = req.XpsidRoot

	// // ..... 地图信息 .....
	logInfo["wyc_from_lat"] = req.WycFromLat
	logInfo["wyc_from_lng"] = req.WycFromLng
	logInfo["wyc_from_poi_id"] = req.WycFromPoiId
	logInfo["wyc_from_name"] = req.WycFromName
	logInfo["wyc_from_address"] = req.WycFromAddress
	logInfo["wyc_to_lat"] = req.WycToLat
	logInfo["wyc_to_lng"] = req.WycToLng
	logInfo["wyc_to_poi_id"] = req.WycToPoiId
	logInfo["wyc_to_name"] = req.WycToName
	logInfo["wyc_to_address"] = req.WycToAddress
	if s.Area != nil {
		logInfo["city_id"] = s.Area.FromCityId
	}

	// 公交  经纬
	logInfo["from_lat"] = req.FromLat
	logInfo["from_lng"] = req.FromLng
	logInfo["from_poi_id"] = req.FromPoiId
	logInfo["from_address"] = req.FromAddress
	logInfo["from_name"] = util.StringEscape(req.FromName)
	logInfo["to_lat"] = req.ToLat
	logInfo["to_lng"] = req.ToLng
	logInfo["to_poi_id"] = req.ToPoiId
	logInfo["to_address"] = req.ToAddress
	logInfo["to_name"] = util.StringEscape(req.ToName)

	// // ..... 方案信息 .....
	logInfo["plan_index"] = i
	logInfo["plan_type"] = plan.PlanType
	logInfo["plan_cost"] = plan.Cost
	logInfo["plan_total_time"] = plan.TotalTime
	logInfo["fid"] = plan.GetRenderMetaData().Fid
	logInfo["plan_id"] = plan.GetRenderMetaData().NewPlanId
	logInfo["is_show"] = isShow
	logInfo["removed"] = !isShow

	if s.UserInfo != nil {
		logInfo["pid"] = s.UserInfo.PID
		logInfo["uid"] = s.UserInfo.UID
	}

	log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
}
