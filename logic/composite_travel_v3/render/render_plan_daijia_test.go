package render

import (
	"context"
	Daijia "git.xiaojukeji.com/dirpc/dirpc-go-http-DaijiaKopService"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/daijia"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewDaijiaPlanRender(t *testing.T) {

	ctx := context.Background()
	t.Run("success", func(t *testing.T) {
		result := NewDaijiaPlanRender(ctx, nil)
		assert.Empty(t, result)
	})
}

func TestRender(t *testing.T) {
	ctx := context.Background()
	d := &DaijiaPlanRender{}
	t.Run("success", func(t *testing.T) {
		result := d.Render(ctx, nil, nil, 0, false)
		assert.Nil(t, result)
	})
}

func TestGetSubTimeText(t *testing.T) {
	ctx := context.Background()
	d := &DaijiaPlanRender{
		BaseRender: &BaseRender{
			baseConfig: BaseConfig{
				BaseTimeConf: &BaseTimeConf{
					Minutes: "{minutes}分钟",
				},
			},
			BaseRequest: &model.Request{
				CompositeTravelV3Req: &proto.CompositeTravelV3Req{
					Token: "xxx",
				},
				UserInfo: &passport.UserInfo{
					UID: 12345,
					PID: 12345,
				},
				AreaInfo: &model.WycAreaInfo{
					FromCityId: 12345,
				},
			},
		},
	}

	t.Run("duration 0", func(t *testing.T) {
		patches := gomonkey.ApplyFunc(daijia.GetTripEstimation,
			func(ctx context.Context, req *Daijia.GetTripEstimationReq) *Daijia.TripEstimationResult {
				return &Daijia.TripEstimationResult{
					Duration: 0,
				}
			})
		defer patches.Reset()
		result := d.GetSubTimeText(ctx, nil)
		assert.Empty(t, result)
	})

	t.Run("duration 10", func(t *testing.T) {
		patches := gomonkey.ApplyFunc(daijia.GetTripEstimation,
			func(ctx context.Context, req *Daijia.GetTripEstimationReq) *Daijia.TripEstimationResult {
				return &Daijia.TripEstimationResult{
					Duration: 10,
				}
			})
		patches.ApplyMethodReturn(&DaijiaPlanRender{}, "GetEstimateTimeWithoutMinutesUnit", "10")

		defer patches.Reset()
		result := d.GetSubTimeText(ctx, nil)
		assert.Empty(t, result)
	})

}

func TestGetEstimateTimeWithoutMinutesUnit(t *testing.T) {
	ctx := context.Background()
	t.Run("1", func(t *testing.T) {
		d := &DaijiaPlanRender{}
		res := d.getEstimateTimeWithoutMinutesUnit(ctx, 0)
		assert.Equal(t, res, "")
	})

	t.Run("2", func(t *testing.T) {
		d := &DaijiaPlanRender{
			BaseRender: &BaseRender{
				baseConfig: BaseConfig{
					BaseTimeConf: &BaseTimeConf{
						Minutes: "{{minutes}}分钟",
					},
				},
			},
		}
		res := d.getEstimateTimeWithoutMinutesUnit(ctx, 54)
		assert.Equal(t, res, "54分钟")
	})

	t.Run("3", func(t *testing.T) {
		d := &DaijiaPlanRender{
			BaseRender: &BaseRender{
				baseConfig: BaseConfig{
					BaseTimeConf: &BaseTimeConf{
						Hours: "{{hours}}小时",
					},
				},
			},
		}
		res := d.getEstimateTimeWithoutMinutesUnit(ctx, 60)
		assert.Equal(t, res, "1小时")
	})

	t.Run("4", func(t *testing.T) {
		d := &DaijiaPlanRender{
			BaseRender: &BaseRender{
				baseConfig: BaseConfig{
					BaseTimeConf: &BaseTimeConf{
						HoursMinutesWithoutMinuteUnit: "{{hours}}小时{{minutes}}",
					},
				},
			},
		}
		res := d.getEstimateTimeWithoutMinutesUnit(ctx, 61)
		assert.Equal(t, res, "1小时1")
	})
}
