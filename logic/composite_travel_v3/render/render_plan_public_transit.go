package render

import (
	"context"
	"fmt"
	metro "git.xiaojukeji.com/dirpc/dirpc-go-http-TransitWind"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

const (
	PublicTransitRPCLogTag   = "PublicTransitRPC"
	ModeTypeTrans_Walk       = "0"
	ModeTypeTrans_Bus        = "1"
	ModeTypeTrans_Subway     = "2"
	ModeTypeTrans_Bus_Subway = "3"
)

type PublicTransRender struct {
	*BaseRender
	startStationName string // 第一个地铁站名称
	entranceName     string
	startSubMode     int32
	transitCostSum   int64 // 地铁/公交总费用-> 渲染"x元"
	conf             TransitDCMPConf
	baseData         *metro.TransitSimple
	fid              string
}

type TransitDCMPConf struct {
	Title        Title             `json:"title"`
	MapParams    map[string]string `json:"map_params"`
	SubTitle     SubTitle          `json:"sub_title"`
	DescList     DescList          `json:"desc_list"`
	RightDesc    RightDesc         `json:"right_desc"`
	ReminderInfo ReminderInfo      `json:"reminder_info"`
	LinkUrl      LinkUrl           `json:"link_url"`
	Segment      Segment           `json:"segment"`
	SceneId      string            `json:"scene_id"`
}

func NewPublicTransitRender(ctx context.Context, base *BaseRender) *PublicTransRender {
	conf, err := initPublicTransitDCMPConf(ctx)
	if err != nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "init public service dcmp error || err = %v", err)
		return nil
	}
	return &PublicTransRender{BaseRender: base, conf: conf}
}

func initPublicTransitDCMPConf(ctx context.Context) (TransitDCMPConf, error) {
	conf := TransitDCMPConf{}
	str := dcmp.GetDcmpContent(ctx, "new_one_stop-v3_public_transit_conf", nil)
	err := jsoniter.UnmarshalFromString(str, &conf)
	if err != nil {
		return conf, err
	}
	return conf, nil
}

func (p *PublicTransRender) GetSubTimeText(ctx context.Context, plan *model.PlanFull) string {
	metaData := plan.GetRenderMetaData()
	if metaData == nil || metaData.PublicTransitData == nil || metaData.PublicTransitData.SubTimeData == nil {
		return ""
	}

	return metaData.PublicTransitData.SubTimeData.GetDescText()
}

func (p *PublicTransRender) Render(ctx context.Context, plan *model.PlanFull, rec *AthenaApiv3.UTRecItem, index int, canTag bool) *proto.PlanV3 {

	renderData := plan.GetRenderMetaData()
	if renderData == nil || renderData.PublicTransitData == nil || renderData.PublicTransitData.TransitData == nil {
		return nil
	}
	publicTransData := renderData.PublicTransitData.TransitData

	if publicTransData.Segments == nil || len(publicTransData.Segments) < 1 {
		return nil
	}

	p.baseData = publicTransData
	p.NewPlanId = renderData.NewPlanId
	p.fid = renderData.Fid

	var (
		segmentList         []*proto.Segment
		segTypeList         []string
		firstTransitSegment *metro.SegmentSimple // 第一个公交/地铁
	)
	segTypeToTitleMap := make(map[string]string)

	for _, segment := range publicTransData.Segments {
		var (
			item    *proto.Segment
			segType string
			title   string
		)

		if segment == nil {
			continue
		}

		switch segment.Mode {
		case model.ModeWalk:
			item, segType, _ = p.renderWalk(ctx, segment)
		case model.ModeTRANSIT:
			item, segType, title = p.renderPublicTrans(ctx, segment)
			// 记录第一个公共交通
			if firstTransitSegment == nil {
				firstTransitSegment = segment
			}
		}

		if item == nil {
			continue
		}

		segmentList = append(segmentList, item)
		if segType != ModeTypeTrans_Walk {
			if _, exist := segTypeToTitleMap[segType]; !exist {
				segTypeToTitleMap[segType] = title
				segTypeList = append(segTypeList, segType)
			}
		}

	}

	recommendTag, recommendContent := p.buildRecommendTag(rec, canTag)
	recommendReason, recommendReasonContent := p.buildRecommendReason(rec, canTag)
	title := p.getTitle(segTypeToTitleMap, segTypeList)
	o := &proto.PlanV3{
		PlanType:        model.PublicTransit,
		MapParams:       p.buildMapParams(),
		LinkType:        model.LinkTypeUrl,
		LinkUrl:         p.buildLinkUrl(ctx),
		SceneId:         &p.conf.SceneId,
		Title:           title,
		SegmentList:     segmentList,
		DescList:        p.getDescList(ctx),
		RightTitle:      p.getRightTitle(ctx),
		SubTitle:        p.getSubTitle(ctx),
		RightDesc:       p.getRightDesc(ctx),
		Params:          p.buildParams(),
		TipData:         p.buildTipData(),
		RecommendTag:    recommendTag,
		RecommendReason: recommendReason,
		OmegaInfo:       p.buildOmegaInfo(index, title, recommendContent, recommendReasonContent),
	}

	return o

}

func (p *PublicTransRender) buildOmegaInfo(index int, title, recommendContent, recommendReasonContent string) map[string]interface{} {

	omegaInfo := map[string]interface{}{
		"index":         index,
		"plan_type":     model.OmegaPublicTransit,
		"plan_title":    title,
		"tag_value":     recommendContent,
		"top_recommend": recommendReasonContent,
		"plan_id":       p.NewPlanId,
	}

	// 总耗时
	if p.baseData.GetDuration() <= 0 {
		omegaInfo["time"] = ""
	} else {
		omegaInfo["time"] = fmt.Sprintf("{%s}分钟", cast.ToString(SecondsToMinutesCeil(int64(p.baseData.GetDuration()))))
	}

	// 总费用
	omegaInfo["price"] = fmt.Sprintf("{%s}元", util.Fen2Yuan(int64(p.baseData.GetCost())))

	return omegaInfo

}

func (p *PublicTransRender) renderWalk(ctx context.Context, data *metro.SegmentSimple) (*proto.Segment, string, string) {

	if data.Walking == nil || data.Walking.GetDuration() < ShowWalkThreshold {
		return nil, ModeTypeTrans_Walk, p.conf.Title.Walk
	}
	segment := &proto.Segment{
		Icon:      &p.conf.Segment.WalkInfo.Icon,
		Content:   p.GetEstimateTime(ctx, int64(data.Walking.GetDuration())),
		TextColor: p.conf.Segment.WalkInfo.TextColor,
	}

	return segment, ModeTypeTrans_Walk, p.conf.Title.Walk

}

func (p *PublicTransRender) renderPublicTrans(ctx context.Context, data *metro.SegmentSimple) (*proto.Segment, string, string) {
	var (
		content, title, icon, bgColor, borderColor, textColor, segType, color string
	)

	if data == nil || len(data.Metrobus) < 1 {
		return nil, "", ""
	}

	metroBusMap := make(map[int32]bool)

	// 目前只有公交聚合，预留地铁聚合、公交+地铁聚合
	index := 0
	for _, m := range data.Metrobus {
		if m == nil {
			continue
		}

		mm := m
		if index == 0 {
			color = *mm.Color
		}
		if _, exist := metroBusMap[mm.Type]; !exist {
			metroBusMap[mm.Type] = true
		}
		if content == "" {
			content = mm.Name
		} else {
			content = content + "/" + mm.Name
		}
		index++

	}

	// 只有一种聚合
	if len(metroBusMap) == 1 {
		metroType := data.Metrobus[0].Type
		if metroType == model.TypeSubway {
			segType = ModeTypeTrans_Subway
			title = p.conf.Title.Subway
			icon = p.conf.Segment.SubwayInfo.Icon
			textColor = p.conf.Segment.SubwayInfo.TextColor
			bgColor = color
			borderColor = color
		}

		if metroType == model.TypeBus {
			segType = ModeTypeTrans_Bus
			title = p.conf.Title.Bus
			icon = p.conf.Segment.BusInfo.Icon
			textColor = p.conf.Segment.BusInfo.TextColor
			borderColor = p.conf.Segment.BusInfo.BorderColor
		}
	} else {
		// 暂时用不到
		segType = ModeTypeTrans_Bus_Subway
		title = p.conf.Title.Subway + p.conf.Title.Bus
		icon = p.conf.Segment.SubwayInfo.Icon
		bgColor = color
	}

	segment := &proto.Segment{
		Icon:        &icon,
		TextColor:   textColor,
		BorderColor: borderColor,
		BgColor:     bgColor,
		Content:     content,
	}

	return segment, segType, title

}

func (p *PublicTransRender) buildMapParams() map[string]string {
	mapParams := p.conf.MapParams
	mapParams = make(map[string]string)
	mapParams["product_id"] = p.conf.MapParams["productId"]
	mapParams["acc_key"] = p.conf.MapParams["accKey"]
	mapParams["caller_id"] = p.conf.MapParams["callerId"]
	mapParams["fid"] = p.fid
	mapParams["transit_id"] = p.baseData.TransitId
	return mapParams
}

func (p *PublicTransRender) buildLinkUrl(ctx context.Context) string {
	if p.BaseRequest != nil && (p.BaseRequest.AccessKeyId == 9 || p.BaseRequest.AccessKeyId == 22) {
		return util.ReplaceTag(ctx, p.conf.LinkUrl.MiniProgram, map[string]string{
			"fid":       p.fid,
			"transitId": p.baseData.TransitId,
		})
	}

	return util.ReplaceTag(ctx, p.conf.LinkUrl.Default, map[string]string{
		"fid":        p.fid,
		"transit_id": p.baseData.TransitId,
	})
}

func (p *PublicTransRender) buildParams() map[string]string {
	return map[string]string{
		"fid":        p.fid,
		"transitId":  p.baseData.TransitId,
		"transit_id": p.baseData.TransitId,
	}
}

func (p *PublicTransRender) buildTipData() *proto.TipData {

	content := p.baseData.MissingTip
	if content == nil || *content == "" {
		return nil
	}

	return &proto.TipData{
		Text:      *content,
		Icon:      &p.conf.ReminderInfo.Icon,
		TextColor: p.conf.ReminderInfo.TextColor,
		BgColor:   p.conf.ReminderInfo.BgColor,
	}
}

func (p *PublicTransRender) getDescList(ctx context.Context) []string {
	descList := make([]string, 0)
	var entranceInfo string
	if p.baseData.GetDisplayInfo() != nil && p.baseData.GetDisplayInfo().GetStopsCount() > 0 {
		descList = append(descList, util.ReplaceTag(ctx, p.conf.DescList.StationContent, map[string]string{
			"station_count": cast.ToString(p.baseData.GetDisplayInfo().GetStopsCount()),
		}))

	}

	displayInfo := p.baseData.GetDisplayInfo()
	if displayInfo != nil {
		if displayInfo.GetDepartureStopName() != "" {
			if displayInfo.GetDepartureStopEntranceName() != "" {
				entranceInfo = fmt.Sprintf(p.conf.DescList.SubwayDesc, displayInfo.GetDepartureStopName(), displayInfo.GetDepartureStopEntranceName())
			} else {
				entranceInfo = util.ReplaceTag(ctx, p.conf.DescList.BusDesc, map[string]string{
					"start_station": displayInfo.GetDepartureStopName(),
				})
			}
		}
		descList = append(descList, entranceInfo)
	}

	return descList
}

func (p *PublicTransRender) getSubTitle(ctx context.Context) *proto.SubTitleV3 {

	walkTotalDistance := p.baseData.GetWalkingDistance()
	if walkTotalDistance <= 0 {
		return nil
	}

	subTitle := &proto.SubTitleV3{
		Icon:    p.conf.SubTitle.Icon,
		Content: p.GetDistance(ctx, walkTotalDistance),
	}

	return subTitle

}

func (p *PublicTransRender) getRightDesc(ctx context.Context) string {
	totalCost := p.baseData.GetCost()
	if totalCost <= 0 {
		return ""
	}
	return util.ReplaceTag(ctx, p.conf.RightDesc.FeeMsg, map[string]string{
		"fee": util.Fen2Yuan(int64(totalCost)),
	})
}

func (p *PublicTransRender) getRightTitle(ctx context.Context) string {
	totalDuration := p.baseData.GetDuration()
	if totalDuration <= 0 {
		return ""
	}

	// 渲染
	return p.GetEstimateTimeWithHighLight(ctx, int64(totalDuration))
}

func (p *PublicTransRender) getTitle(segTypeToTitleMap map[string]string, segTypeList []string) string {
	var (
		title string
	)

	usedTitleMap := make(map[string]bool)
	if len(segTypeToTitleMap) < 1 || len(segTypeList) < 1 {
		return ""
	}

	// 只有一种交通方式
	if len(segTypeToTitleMap) == 1 {
		return segTypeToTitleMap[segTypeList[0]]
	}

	// 多种交通方式混合
	for _, segType := range segTypeList {
		if segType == ModeTypeTrans_Bus_Subway {
			return segTypeToTitleMap[ModeTypeTrans_Bus_Subway]
		}

		if _, used := usedTitleMap[segType]; !used {
			if title == "" {
				title = segTypeToTitleMap[segType]
			} else {
				title = title + "+" + segTypeToTitleMap[segType]
			}
			usedTitleMap[segType] = true
		}
	}

	return title

}
