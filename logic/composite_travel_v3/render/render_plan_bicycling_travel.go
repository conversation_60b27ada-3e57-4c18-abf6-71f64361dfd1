package render

import (
	"context"
	"encoding/json"
	"fmt"
	route "git.xiaojukeji.com/dirpc/dirpc-go-http-OrderRouteApi"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	trace "git.xiaojukeji.com/lego/context-go"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type BicyclePlanRender struct {
	*BaseRender
	conf            BicyclingDCMPConf
	bicycleDataList []*route.BicyclingRouteDetail
	baseData        *route.MpRouteDetail
	baseDataType    int32
}

type BicyclingDCMPConf struct {
	LinkUrl    map[string]string `json:"link_url"`
	DescList   string            `json:"desc_list"`
	Title      string            `json:"title"`
	RightTitle RightTitle        `json:"right_title"`
}

type VehicleDataModel struct {
	VehicleType int32            `json:"vehicle_type"`
	RouteData   []*RouteDataItem `json:"route_data"`
}

type VehicleMapInfo struct {
	DefaultType int32               `json:"default_type"`
	VehicleData []*VehicleDataModel `json:"vehicle_data"`
}

type RouteDataItem struct {
	RouteId   string `json:"route_id"`
	IsDefault int32  `json:"is_default"`
	SelectKey string `json:"select_key"`
}

type VehicleDataReplace struct {
	RightTitle string   `json:"right_title"` // 时长
	RightDesc  string   `json:"right_desc"`  // 价格
	DescList   []string `json:"desc_list"`
}

const (
	CyclingStyle_Bike             = "bike"             // 自行车
	CyclingStyle_Electric_Vehicle = "electric_vehicle" // 电动车
)

func NewBicyclePlanRender(ctx context.Context, base *BaseRender) *BicyclePlanRender {

	conf, err := initBicyclingTravelDCMPConf(ctx)
	if err != nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "init bicycle service dcmp error || err = %v", err)
		return nil
	}
	return &BicyclePlanRender{
		BaseRender: base,
		conf:       conf,
	}
}

func initBicyclingTravelDCMPConf(ctx context.Context) (BicyclingDCMPConf, error) {
	conf := BicyclingDCMPConf{}
	str := dcmp.GetDcmpContent(ctx, "new_one_stop-v3_bicycle_conf", nil)
	err := jsoniter.UnmarshalFromString(str, &conf)
	if err != nil {
		return conf, err
	}
	return conf, nil
}

func (b *BicyclePlanRender) GetSubTimeText(ctx context.Context, plan *model.PlanFull) string {

	if plan == nil || plan.RenderMetaData == nil || plan.RenderMetaData.BicycleData == nil || plan.RenderMetaData.BicycleData.TargetBicycleDataItem == nil {
		return ""
	}

	bicycleData := plan.RenderMetaData.BicycleData.TargetBicycleDataItem
	if bicycleData.GetEtaSec() <= 0 {
		return ""
	}
	return b.GetEstimateTimeWithoutMinutesUnit(ctx, int64(bicycleData.GetEtaSec()))
}

func (b *BicyclePlanRender) Render(ctx context.Context, plan *model.PlanFull, rec *AthenaApiv3.UTRecItem, index int, canTag bool) *proto.PlanV3 {
	renderData := plan.GetRenderMetaData()
	if renderData == nil || renderData.BicycleData == nil || renderData.BicycleData.TargetBicycleDataItem == nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "bicycle plan render err || enderData =%v", renderData)
		return nil
	}

	b.baseData = renderData.BicycleData.TargetBicycleDataItem
	b.bicycleDataList = renderData.BicycleData.BicycleDataList
	b.baseDataType = renderData.BicycleData.TargetDataType
	b.NewPlanId = renderData.NewPlanId

	recommendTag, recommendContent := b.buildRecommendTag(rec, canTag)
	recommendReason, recommendReasonContent := b.buildRecommendReason(rec, canTag)

	return &proto.PlanV3{
		PlanType:        model.BicyclingTravel,
		Title:           b.conf.Title,
		DescList:        b.getDescList(ctx, b.baseData.GetTrafficLightCount()),
		RightTitle:      b.getRightTitle(ctx, b.baseData.GetEtaSec()),
		RightDesc:       b.getRightDesc(ctx, b.baseData.GetEdaMeter()),
		LinkType:        model.LinkTypeUrl,
		LinkUrl:         b.buildLinkUrl(ctx),
		MapParams:       b.buildMapParams(ctx),
		ExtraData:       b.buildExtraData(ctx),
		OmegaInfo:       b.buildOmegaInfo(index, recommendContent, recommendReasonContent),
		RecommendTag:    recommendTag,
		RecommendReason: recommendReason,
	}
}

func (b *BicyclePlanRender) buildMapParams(ctx context.Context) map[string]string {

	if b.bicycleDataList == nil || len(b.bicycleDataList) < 1 {
		return nil
	}
	vehicleDataList := make([]*VehicleDataModel, len(b.bicycleDataList))
	for index, detail := range b.bicycleDataList {

		routeDataList := make([]*RouteDataItem, len(detail.RouteDetail))
		for i, routeDetail := range detail.RouteDetail {
			routeData := &RouteDataItem{
				RouteId:   routeDetail.GetRouteId(),
				SelectKey: fmt.Sprintf("%d_%s", detail.GetVehicleType(), routeDetail.GetRouteId()),
			}
			if i == 0 {
				routeData.IsDefault = 1
			}
			routeDataList[i] = routeData
		}

		vehicleDataList[index] = &VehicleDataModel{
			VehicleType: detail.GetVehicleType(),
			RouteData:   routeDataList,
		}
	}

	res := &VehicleMapInfo{
		DefaultType: b.baseDataType,
		VehicleData: vehicleDataList,
	}

	resStr, err := json.Marshal(res)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "marshal vehicleData error|| err=%v", err)
		return nil
	}

	return map[string]string{
		"map_info": string(resStr),
	}

}

func (b *BicyclePlanRender) buildExtraData(ctx context.Context) map[string]string {
	if b.bicycleDataList == nil || len(b.bicycleDataList) < 1 {
		return nil
	}
	dataReplaceMap := make(map[string]string)

	for _, detail := range b.bicycleDataList {
		for _, routeDetail := range detail.RouteDetail {

			key := fmt.Sprintf("%d_%s", detail.GetVehicleType(), routeDetail.GetRouteId())
			data := &VehicleDataReplace{
				RightTitle: b.getRightTitle(ctx, routeDetail.GetEtaSec()),
				RightDesc:  b.getRightDesc(ctx, routeDetail.GetEdaMeter()),
				DescList:   b.getDescList(ctx, routeDetail.GetTrafficLightCount()),
			}

			dataStr, err := json.Marshal(data)
			if err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "marshal vehicleData error|| err=%v", err)
				return nil
			}
			dataReplaceMap[key] = string(dataStr)
		}

	}

	return dataReplaceMap
}

func (b *BicyclePlanRender) buildOmegaInfo(index int, recommendContent, recommendReasonContent string) map[string]interface{} {

	omegaInfo := map[string]interface{}{
		"index":         index,
		"plan_type":     model.OmegaBicyclingTravel,
		"plan_title":    b.conf.Title,
		"tag_value":     recommendContent,
		"top_recommend": recommendReasonContent,
		"plan_id":       b.NewPlanId,
	}

	// 总耗时
	if b.baseData.GetEtaSec() <= 0 {
		omegaInfo["time"] = ""
	} else {
		omegaInfo["time"] = fmt.Sprintf("{%s}分钟", cast.ToString(SecondsToMinutesCeil(int64(b.baseData.GetEtaSec()))))
	}

	// 总费用
	omegaInfo["price"] = ""

	return omegaInfo

}

func (b *BicyclePlanRender) getDescList(ctx context.Context, trafficLightCount int32) []string {

	if trafficLightCount <= 0 {
		return nil
	}

	descList := make([]string, 0)
	descList = append(descList, util.ReplaceTag(ctx, b.conf.DescList, map[string]string{
		"traffic_light_count": cast.ToString(b.baseData.GetTrafficLightCount()),
	}))

	return descList
}

func (b *BicyclePlanRender) getRightDesc(ctx context.Context, edaMeter int32) string {

	if edaMeter <= 0 {
		return ""
	}

	distance, unit := b.GetDistanceV2(edaMeter)
	return util.ReplaceTag(ctx, b.conf.RightTitle.OnlyDistance, map[string]string{
		"distance": distance,
		"unit":     unit,
	})

}

func (b *BicyclePlanRender) getRightTitle(ctx context.Context, totalDuration int32) string {

	if totalDuration <= 0 {
		return ""
	}

	return b.GetEstimateTimeWithHighLight(ctx, int64(totalDuration))

}

func (b *BicyclePlanRender) buildLinkUrl(ctx context.Context) string {
	url := b.conf.LinkUrl[cast.ToString(b.BaseRequest.AccessKeyId)]
	fromPoiId := b.BaseRequest.FromPoiId
	if b.BaseRequest.FromName == consts.MyLocationFromName {
		fromPoiId = consts.MyLocationFromPoiId
	}
	return util.ReplaceTag(ctx, url, map[string]string{
		"from_lng":            cast.ToString(b.BaseRequest.FromLng),
		"from_lat":            cast.ToString(b.BaseRequest.FromLat),
		"from_name":           b.BaseRequest.FromName,
		"from_poi_id":         fromPoiId,
		"to_lng":              cast.ToString(b.BaseRequest.ToLng),
		"to_lat":              cast.ToString(b.BaseRequest.ToLat),
		"to_name":             b.BaseRequest.ToName,
		"to_poi_id":           b.BaseRequest.ToPoiId,
		"navi_end_poi_cityid": cast.ToString(b.BaseRequest.AreaInfo.ToCityId),
	})
}
