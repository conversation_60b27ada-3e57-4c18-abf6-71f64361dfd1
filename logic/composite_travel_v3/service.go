package composite_travel_v3

import (
	"context"
	"encoding/json"
	"fmt"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/render"
	"github.com/spf13/cast"
	"runtime/debug"
	"strconv"
	"sync"

	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/rpc"
	trace "git.xiaojukeji.com/lego/context-go"
)

type CompositeTravelV3Service struct {
	// 前端入参
	*model.Request

	Area *model.WycAreaInfo

	// RPC
	rpcList []TravelPlanV3RPC

	// PlanList 最终方案列表
	planList []*model.PlanFull
	// planListRemoved 被过滤的方案列表
	planListRemoved []*model.PlanFull
	recResp         *AthenaApiv3.AthenaUTRecommendResp

	// 渲染工具类
	renderEngine          *render.PlanRenderEngine
	isRecommend           int32
	planTypeToPlanListMap map[int32][]*model.PlanFull

	ServiceDCMPConf ServiceDCMPConf
}

type ServiceDCMPConf struct {
	NoPlanTips string `json:"no_plan_tips"`
}

type TravelPlanV3RPC interface {
	// Execute 执行RPC
	Execute(ctx context.Context, request *model.Request)
	// GetModel 获取方案模型
	GetModel(ctx context.Context) (int32, []*model.PlanFull)
}

func NewService(ctx context.Context, req *proto.CompositeTravelV3Req) (*CompositeTravelV3Service, error) {

	baseRequest, err1 := model.NewRequest(ctx, req)
	if err1 != nil {
		return nil, err1
	}

	var rpcList []TravelPlanV3RPC
	res := &CompositeTravelV3Service{}

	// 构造位置信息
	baseRequest.BuildAreaInfo(ctx, req)

	// 构造用户信息
	if e := baseRequest.BuildUserInfo(ctx, req); e != nil {
		return nil, e
	}

	// 注册网约车(强依赖)
	if r, err := rpc.NewCarHailingRPC(ctx, baseRequest); err != nil {
		return nil, err
	} else {
		res.Area = r.WycAreaInfo
		rpcList = append(rpcList, r)
	}
	// 注册组合出行
	if r := rpc.NewCombinedTravelRPC(ctx, baseRequest); r != nil {
		rpcList = append(rpcList, r)
	}

	// 注册公交
	if r := rpc.NewPublicTransitRPC(ctx, baseRequest); r != nil {
		rpcList = append(rpcList, r)
	}

	// 注册全程骑行
	if r := rpc.NewBicyclingTravelRpc(ctx, baseRequest); r != nil {
		rpcList = append(rpcList, r)
	}

	// 注册全程步行
	if r := rpc.NewWalkingTravelRpc(ctx, baseRequest); r != nil {
		rpcList = append(rpcList, r)
	}

	if DCMPConf, err := initServiceConf(ctx); err == nil {
		res.ServiceDCMPConf = DCMPConf
	}

	res.Request = baseRequest
	res.rpcList = rpcList

	// 构建渲染引擎
	res.renderEngine = render.NewPlanRenderEngine(ctx, baseRequest)

	return res, nil
}

func initServiceConf(ctx context.Context) (ServiceDCMPConf, error) {
	conf := ServiceDCMPConf{}
	str := dcmp.GetDcmpContent(ctx, "new_one_stop-service_conf", nil)
	err := json.Unmarshal([]byte(str), &conf)
	if err != nil {
		return conf, err
	}
	return conf, nil
}

type PlanTotalData struct {
	PlanType     int32
	PlanFullList []*model.PlanFull
}

func (s *CompositeTravelV3Service) GetOriginPlanList(ctx context.Context) {
	var result []*model.PlanFull
	planTypeToPlanListMap := make(map[int32][]*model.PlanFull)
	planTotalDataChan := make(chan *PlanTotalData, len(s.rpcList))

	var wg sync.WaitGroup

	for _, rpcOne := range s.rpcList {
		wg.Add(1)
		go func(ctx context.Context, request *model.Request, rpcOne TravelPlanV3RPC) {
			defer InnerRecover(ctx)()
			defer wg.Done()
			rpcOne.Execute(ctx, request)
			planType, plans := rpcOne.GetModel(ctx)
			if len(plans) > 0 {
				planTotalDataChan <- &PlanTotalData{
					PlanType:     planType,
					PlanFullList: plans,
				}
			}
		}(ctx, s.Request, rpcOne)
	}
	wg.Wait()
	close(planTotalDataChan)

	for planTotalData := range planTotalDataChan {
		if planTotalData == nil {
			continue
		}

		planTypeToPlanListMap[planTotalData.PlanType] = planTotalData.PlanFullList
		result = append(result, planTotalData.PlanFullList...)
	}

	s.planList = result
	s.planTypeToPlanListMap = planTypeToPlanListMap
}

func InnerRecover(ctx context.Context) func() {
	return func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
		}
	}
}

func (s *CompositeTravelV3Service) CheckBeforeRecommend(ctx context.Context) error {
	if s.planList == nil || len(s.planList) < 1 {
		return errors.New(s.ServiceDCMPConf.NoPlanTips)
	}

	if s.planTypeToPlanListMap == nil || len(s.planTypeToPlanListMap) < 1 {
		return errors.New(s.ServiceDCMPConf.NoPlanTips)
	}

	if _, exist := s.planTypeToPlanListMap[model.CarHailing]; !exist {
		return errors.New(s.ServiceDCMPConf.NoPlanTips)
	}

	carPlanList := s.planTypeToPlanListMap[model.CarHailing]

	if len(carPlanList) < 1 {
		return errors.New(s.ServiceDCMPConf.NoPlanTips)
	}

	return nil
}

func (s *CompositeTravelV3Service) Recommend(ctx context.Context) error {

	// 提供降级athena能力
	if apollo.FeatureToggle(ctx, "ut_athena_recommend_596", cast.ToString(s.UserInfo.UID), s.GetApolloParam()) {
		log.Trace.Infof(ctx, "utAthena596", "func = UTRecommend || pid = %v", s.UserInfo.PID)
		return nil
	}

	req := &AthenaApiv3.AthenaUTRecommendReq{
		Stage:       util.StringPtr("2"),
		PassengerID: util.StringPtr(util.ToString(s.UserInfo.PID)),
		UID:         util.StringPtr(util.ToString(s.UserInfo.UID)),
		CityID:      util.Int32Ptr(s.Area.FromCityId),
		AccessKeyID: util.Int32Ptr(s.AccessKeyId),
		AppVersion:  util.StringPtr(s.AppVersion),
		Lang:        util.StringPtr(s.Lang),
		ExtraInfo: map[string]string{
			"version_tag": model.Version90,
			"sort_type":   "1,2,3",
		},
	}

	if s.IsRecLanding != nil {
		req.ExtraInfo["is_rec_landing"] = "1"
	}

	for i, plan := range s.planList {
		utRouteInfo := AthenaApiv3.UTRouteInfo{
			RouteType:     rpc.GetPlanRouteType(plan),
			UniqueID:      util.StringPtr(cast.ToString(i)),
			Flat:          util.Float64Ptr(s.FromLat),
			Flng:          util.Float64Ptr(s.FromLng),
			Tlat:          util.Float64Ptr(s.ToLat),
			Tlng:          util.Float64Ptr(s.ToLng),
			TransferCnt:   util.Int32Ptr(rpc.CalTransferCnt(plan)),
			ExtraInfo:     map[string]string{},
			TotalDuration: util.Int32Ptr(int32(plan.TotalTime)),
			TotalFee:      util.Int32Ptr(int32(plan.Cost)),
			FromPoiID:     util.StringPtr(s.FromPoiId),
			ToPoiID:       util.StringPtr(s.ToPoiId),
		}

		if plan.TipData != nil && plan.TipData.Text != "" {
			utRouteInfo.ExtraInfo["missing_tip"] = plan.TipData.Text
		}

		for _, v := range plan.SegmentDataList {
			curSegment := AthenaApiv3.UTRouteSegment{
				Mode:       rpc.GetSegmentRouteType(v),
				Distance:   util.Int32Ptr(v.Distance),
				Fee:        util.Int32Ptr(int32(v.Cost)),
				Duration:   util.Int32Ptr(v.Time),
				StationCnt: util.Int32Ptr(v.StationCnt),
				Etp:        util.Int32Ptr(v.Etp),
				Etq:        util.Int32Ptr(v.Etq),
				Eta:        util.Int32Ptr(v.Eta),
				CarIsQueue: &v.CarIsQu,

				TransitLineID:   util.StringPtr(v.LineId),
				TransitLineName: util.StringPtr(v.LineName),
				CanReachStatus:  rpc.GetCanReachStatus(v),
			}

			// 途径站点信息
			if len(v.ViaStops) > 0 {
				var viaStops []*AthenaApiv3.Stop
				for _, stop := range v.ViaStops {
					viaStops = append(viaStops, &AthenaApiv3.Stop{
						ID:       &stop.Id,
						Name:     &stop.Name,
						Location: &stop.Location,
					})
				}

				curSegment.ViaStops = viaStops
			}

			utRouteInfo.SegmentInfo = append(utRouteInfo.SegmentInfo, &curSegment)
		}
		req.RouteInfo = append(req.RouteInfo, &utRouteInfo)
	}

	s.recResp = athena.UTRecommend(ctx, req)

	// test
	if apollo.FeatureToggle(ctx, "new_one_stop_use_mock_data_switch", cast.ToString(s.UserInfo.PID), s.GetApolloParam()) {
		s.recResp = s.mockAthena()
	}

	return nil
}

func (s *CompositeTravelV3Service) mockAthena() *AthenaApiv3.AthenaUTRecommendResp {

	recItem := make([]*AthenaApiv3.UTRecItem, 0)
	recResult := make([]*AthenaApiv3.RecGroup, 0)

	for i, _ := range s.planList {
		id := strconv.Itoa(i)
		reason := "推荐测试"
		style := int32(1001)
		recTag := make([]int32, 0)
		if i%3 == 0 {
			recTag = append(recTag, 1)
		} else if i%3 == 1 {
			recTag = append(recTag, 2)
		} else {
			recTag = append(recTag, 3)
		}

		item := &AthenaApiv3.UTRecItem{
			UniqueID:        &id,
			RecommendReason: &reason,
			RecommendStyle:  &style,
			RecTag:          recTag,
		}

		recItem = append(recItem, item)
	}

	g1 := &AthenaApiv3.RecGroup{
		GroupType:  int32(1),
		GroupTitle: "时间短",
		GroupItems: recItem,
	}

	g2 := &AthenaApiv3.RecGroup{
		GroupType:  int32(2),
		GroupTitle: "价格低",
		GroupItems: recItem,
	}

	g3 := &AthenaApiv3.RecGroup{
		GroupType:  int32(3),
		GroupTitle: "换乘少",
		GroupItems: recItem,
	}
	recResult = append(recResult, g1, g2, g3)

	resp := &AthenaApiv3.AthenaUTRecommendResp{
		RecItem:    recItem,
		RecResult_: recResult,
	}

	return resp

}

func (s *CompositeTravelV3Service) Render(ctx context.Context) (*proto.CompositeTravelV3Data, error) {

	if s.recResp == nil || len(s.recResp.RecResult_) == 0 || len(s.recResp.RecItem) == 0 {
		// 兜底
		return s.defaultRenderResp(ctx)
	}

	return s.renderEngine.RenderResp(ctx, s.planList, s.recResp)
}

// 打车兜底、不写缓存
func (s *CompositeTravelV3Service) defaultRenderResp(ctx context.Context) (*proto.CompositeTravelV3Data, error) {
	carPlanList := s.planTypeToPlanListMap[model.CarHailing]
	planList := make([]*proto.PlanV3, 0)
	if len(carPlanList) < 1 || carPlanList[0] == nil {
		return nil, errors.New(s.ServiceDCMPConf.NoPlanTips)
	}

	renderCarEngine := s.renderEngine.UtilMap[model.CarHailing]
	if renderCarEngine == nil {
		return nil, errors.New("renderCarEngine is nil")
	}

	planList = append(planList, renderCarEngine.Render(ctx, carPlanList[0], nil, 0, false))
	// 构建TabExtraData
	tabExtraData := s.renderEngine.BuildTabExtraData(ctx, carPlanList)

	// 返回
	return &proto.CompositeTravelV3Data{
		TabExtraData:   tabExtraData,
		PlanList:       planList,
		SortTypeList:   s.renderEngine.BuildDefaultSortTypeList(),
		SelectSortType: s.renderEngine.BaseRender.BaseRequest.GetSortType(),
	}, nil

}

func (s *CompositeTravelV3Service) GetRespFromCache(ctx context.Context) *proto.CompositeTravelV3Data {

	key := fmt.Sprintf("%s_%s_%s_%s_%s", model.RedisKeyPrefix, cast.ToString(s.UserInfo.PID), s.GetSortType(), s.GetWycFromPoiId(), s.GetWycToPoiId())
	planListStr, redisErr := redis.GetClient().Get(ctx, key)
	if redisErr != nil {
		log.Trace.Infof(ctx, "redis_error", "redisErr=%v", redisErr)
		return nil
	}

	data := &proto.CompositeTravelV3Data{}

	if parseErr := json.Unmarshal([]byte(planListStr), data); parseErr != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "PlanV3 Unmarshal fail")
		return nil
	}

	return data

}
