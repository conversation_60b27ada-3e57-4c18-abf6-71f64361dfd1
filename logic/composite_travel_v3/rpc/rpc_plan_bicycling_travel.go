package rpc

import (
	"context"
	route "git.xiaojukeji.com/dirpc/dirpc-go-http-OrderRouteApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/bicycle_route"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	"github.com/spf13/cast"
	"time"
)

type BicyclingTravelRpc struct {
	baseRequest *model.Request
	RouteDetail []*route.BicyclingRouteDetail
}

func NewBicyclingTravelRpc(ctx context.Context, baseRequest *model.Request) *BicyclingTravelRpc {
	req := baseRequest.CompositeTravelV3Req
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Warnf(ctx, PublicTransitRPCLogTag, "req is lost")
		return nil
	}

	// 开城灰度
	if !apollo.FeatureToggle(ctx, "one_stop-bicycling_travel_toggle", cast.ToString(baseRequest.UserInfo.UID), baseRequest.GetApolloParam()) {
		return nil
	}

	return &BicyclingTravelRpc{
		baseRequest: baseRequest,
	}
}

func (b *BicyclingTravelRpc) Execute(ctx context.Context, request *model.Request) {
	srcPoint := &route.DoublePoint{
		Lat:  request.FromLat,
		Lng:  request.FromLng,
		Name: util.StringPtr(request.FromName),
		UID:  util.StringPtr(request.FromPoiId),
	}

	if request.FromName == consts.MyLocationFromName {
		srcPoint.UID = util.StringPtr(consts.MyLocationFromPoiId)
	}
	tripReq := &route.RecommendBicyclingTripReq{
		Token:    request.GetToken(),
		UserId:   cast.ToString(request.UserInfo.UID),
		BizType:  85004,
		SrcPoint: srcPoint,
		DstPoint: &route.DoublePoint{
			Lat:  request.ToLat,
			Lng:  request.ToLng,
			Name: util.StringPtr(request.ToName),
			UID:  util.StringPtr(request.ToPoiId),
		},
		SrcCityId:   util.Int32Ptr(request.AreaInfo.FromCityId),
		DstCityId:   util.Int32Ptr(request.AreaInfo.ToCityId),
		VehicleType: nil,
		Caller:      "internal_api",
		ReqType:     route.ReqSourceType_SERVER_API,
		ReqTime:     time.Now().Unix(),
		DidiVersion: util.StringPtr(request.AppVersion),
	}

	res := bicycle_route.RecommendBicyclingTripInfo(ctx, tripReq)

	if res == nil {
		return
	}

	b.RouteDetail = res.RouteDetail
}

func (b *BicyclingTravelRpc) GetModel(ctx context.Context) (planType int32, result []*model.PlanFull) {

	planType = model.BicyclingTravel
	if len(b.RouteDetail) < 1 {
		return
	}

	var (
		targetType          int32
		defaultTransitRoute *route.MpRouteDetail
	)
	if b.baseRequest.GetCyclingStyle() == model.CyclingStyle_Bike {
		targetType = 1
	} else if b.baseRequest.GetCyclingStyle() == model.CyclingStyle_Electric_Vehicle {
		targetType = 2
	} else {
		// 兜底
		targetType = 1
	}

	for _, detail := range b.RouteDetail {
		if detail.GetVehicleType() == targetType {
			for _, item := range detail.RouteDetail {
				if item != nil {
					defaultTransitRoute = item
					break
				}
			}
		}
	}

	var planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型
	planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, &model.SegmentItem{
		Mode:     model.ModeBike,
		Distance: defaultTransitRoute.GetEdaMeter(),
		Time:     defaultTransitRoute.GetEtaSec(),
		Cost:     0,
	})

	result = append(result, &model.PlanFull{
		PlanType:             model.BicyclingTravel,
		TotalTime:            int64(defaultTransitRoute.GetEtaSec()),
		Cost:                 0,
		Distance:             defaultTransitRoute.GetEdaMeter(),
		PlanFormatDetailData: planFormatDetailData,
		RenderMetaData: &model.RenderMetaData{
			NewPlanId: GenPlanId(ctx, model.BicyclingTravel),
			BicycleData: &model.BicycleData{
				BicycleDataList:       b.RouteDetail,
				TargetBicycleDataItem: defaultTransitRoute,
				TargetDataType:        targetType,
			},
		},
	})

	return
}
