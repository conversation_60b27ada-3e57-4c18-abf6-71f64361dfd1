package composite_travel_options

import (
	"context"
	"encoding/json"
	ApolloSwitch "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/model"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"strconv"
	"time"
)

type MaterialManagement struct {
	condition               string
	tabId                   string
	material                string
	req                     *model.Request
	TotalFrequency          int64
	FrequencyControl        int64
	isCheckTotalFrequency   bool
	isCheckFrequencyControl bool
}

func NewMaterialManagement(tabId string, req *model.Request) *MaterialManagement {

	return &MaterialManagement{
		tabId: tabId,
		req:   req,
	}
}

func (m *MaterialManagement) LoadConfig(ctx context.Context) error {

	confResult, err := apollo.GetConfig(Ns_OneStopTabBubbleConf, m.tabId+PostFix)
	if err != nil {
		return err
	}

	conditionStr, err := confResult.GetStringValue(ConditionKey)
	if err != nil {
		return err
	}

	materialStr, err := confResult.GetStringValue(MaterialKey)
	if err != nil {
		return err
	}

	m.material = materialStr
	m.condition = conditionStr
	return nil

}

func (m *MaterialManagement) getBubble(ctx context.Context) *proto.Bubble {

	// 前置校验
	if m.req == nil || m.req.UserInfo == nil {
		return nil
	}

	err := m.LoadConfig(ctx)
	if err != nil {
		return nil
	}

	if !m.CheckCondition(ctx) {
		return nil
	}

	material := &Material{}
	err = json.Unmarshal([]byte(m.material), &material)
	if err != nil {
		return nil
	}

	err = m.DoAfter(ctx)
	if err != nil {
		return nil
	}
	return &proto.Bubble{
		TabId:     m.tabId,
		Text:      material.Text,
		TextColor: material.TextColor,
		BgColor:   material.BgColor,
	}

}

func (m *MaterialManagement) DoAfter(ctx context.Context) error {

	if m.isCheckTotalFrequency {
		v1 := map[string]string{
			PassengerTabBubbleTotalFrequency: strconv.FormatInt(m.TotalFrequency+1, 10),
		}
		p1 := map[string]string{
			"passenger_id": strconv.Itoa(int(m.req.UserInfo.PID)),
			"tab_id":       m.tabId,
		}
		if _, err := ufs.SetFeature(ctx, DomainPassenger, p1, v1); err != nil {
			return err
		}
	}

	if m.isCheckFrequencyControl {

		v2 := map[string]string{
			PassengerTabBubbleFrequencyControl: "1",
		}
		p2 := map[string]string{
			"passenger_id": strconv.Itoa(int(m.req.UserInfo.PID)),
			"tab_id":       m.tabId,
		}
		if _, err := ufs.SetFeature(ctx, DomainPassenger, p2, v2); err != nil {
			return err
		}
	}

	return nil

}

func (m *MaterialManagement) CheckCondition(ctx context.Context) bool {
	// 没有配置，不校验
	if m.condition == "" {
		return true
	}
	condition := &Condition{}
	err := json.Unmarshal([]byte(m.condition), condition)
	if err != nil {
		return false
	}

	// 灰度校验
	if !m.CheckApolloSwitch(ctx, condition) {
		return false
	}

	// 总频次校验
	if !m.CheckTotalFrequency(ctx, condition) {
		return false
	}

	// 频率校验
	if !m.CheckFrequencyControl(ctx, condition) {
		return false
	}

	return true
}

func (m *MaterialManagement) CheckApolloSwitch(ctx context.Context, condition *Condition) bool {
	if condition == nil || condition.ApolloSwitch == "" {
		return true
	}

	params := map[string]string{
		"access_key_id": strconv.Itoa(int(m.req.GetAccessKeyId())),
		"lang":          m.req.GetLang(),
		"app_version":   m.req.GetAppVersion(),
		"maas_type":     strconv.Itoa(int(m.req.GetMaasType())),
		"from_type":     strconv.Itoa(int(m.req.GetFromType())),
		"pid":           strconv.Itoa(int(m.req.UserInfo.PID)),
		"key":           strconv.Itoa(int(m.req.UserInfo.PID)),
	}

	if !ApolloSwitch.FeatureToggle(ctx, condition.ApolloSwitch, strconv.Itoa(int(m.req.UserInfo.PID)), params) {
		return false
	}

	return true
}

func (m *MaterialManagement) CheckTotalFrequency(ctx context.Context, condition *Condition) bool {

	// 未配置
	if condition == nil {
		return true
	}

	// 未配置条件返回true
	if condition.TotalFrequency <= 0 {
		return true
	}

	m.isCheckTotalFrequency = true

	// 获取频次
	params := map[string]string{
		"passenger_id": strconv.Itoa(int(m.req.UserInfo.PID)),
		"tab_id":       m.tabId,
	}

	// 频次校验
	ufsData, err := ufs.GetFeature(ctx, DomainPassenger, []string{PassengerTabBubbleTotalFrequency}, params, "")

	if err != nil {
		return false
	}

	// 没有记录，返回true
	if ufsData == nil || len(ufsData) < 1 {
		return true
	}

	data := ufsData[PassengerTabBubbleTotalFrequency]

	if data == nil {
		return true
	}

	if data.Errno != 0 {
		return false
	}

	if data.GetValue() == "" {
		return true
	}

	checkFrequency, err := strconv.ParseInt(data.GetValue(), 10, 64)
	if err != nil {
		return false
	}

	m.TotalFrequency = checkFrequency

	if checkFrequency < condition.TotalFrequency {
		return true
	}

	return false

}

func (m *MaterialManagement) CheckFrequencyControl(ctx context.Context, condition *Condition) bool {

	// 条件为空返回true
	if condition == nil {
		return true
	}

	// 未配置条件返回true
	frequencyControlInfo := condition.FrequencyControl
	if frequencyControlInfo == nil || frequencyControlInfo.Day <= 0 || frequencyControlInfo.Times <= 0 {
		return true
	}

	m.isCheckFrequencyControl = true

	// 获取ufs
	params := map[string]string{
		"passenger_id": strconv.Itoa(int(m.req.UserInfo.PID)),
		"end_date":     time.Now().Format("20060102"),
		"start_date":   calcStartDate(int(frequencyControlInfo.Day) - 1),
		"tab_id":       m.tabId,
	}

	// 频次校验
	ufsData, err := ufs.GetFeature(ctx, DomainPassenger, []string{PassengerTabBubbleFrequencyControl}, params, "")
	if err != nil {
		return false
	}

	// 没有记录，返回true
	if ufsData == nil || len(ufsData) < 1 {
		return true
	}

	data := ufsData[PassengerTabBubbleFrequencyControl]

	if data == nil {
		return true
	}

	if data.Errno != 0 {
		return false
	}

	if data.GetValue() == "" {
		return true
	}

	checkFrequency, err := strconv.ParseInt(data.GetValue(), 10, 64)
	if err != nil {
		return false
	}
	m.FrequencyControl = checkFrequency
	if checkFrequency < frequencyControlInfo.Times {
		return true
	}

	return false

}

func calcStartDate(days int) string {
	ts := time.Now().AddDate(0, 0, -days)
	return ts.Format("20060102")
}
