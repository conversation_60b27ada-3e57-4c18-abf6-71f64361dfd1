package rpc

import (
	"context"
	DaijiaKop "git.xiaojukeji.com/dirpc/dirpc-go-http-DaijiaKopService"
	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/utils"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/daijia"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/model"
	"github.com/spf13/cast"
)

type DaijiaTravelRpc struct {
	baseRequest *model.Request
	res         *DaijiaKop.TripEstimationResult
	*BaseHandle
}

func NewDaijiaTravelRpc(ctx context.Context, baseRequest *model.Request, baseHandle *BaseHandle) *DaijiaTravelRpc {
	return &DaijiaTravelRpc{
		baseRequest: baseRequest,
		BaseHandle:  baseHandle,
	}
}

func (d *DaijiaTravelRpc) Execute(ctx context.Context, request *model.Request) {

	req := &DaijiaKop.GetTripEstimationReq{
		Token:            utils.StringPtr(request.Token),
		UserId:           int64(request.UserInfo.UID),
		StartAddress:     request.WycFromAddress,
		StartName:        request.WycFromName,
		StartLat:         request.WycFromLat,
		StartLng:         request.WycFromLng,
		StartPoiId:       request.WycFromPoiId,
		EndAddress:       request.WycToAddress,
		EndLat:           request.WycToLat,
		EndLng:           request.WycToLng,
		EndName:          request.WycToName,
		EndPoiId:         request.WycToPoiId,
		StartDidiCityId:  request.AreaInfo.FromCityId,
		UserStartLat:     request.FromLat,
		UserStartLng:     request.FromLng,
		UserStartPoiId:   request.FromPoiId,
		UserStartName:    request.FromName,
		UserStartAddress: request.FromAddress,
		UserEndLat:       request.ToLat,
		UserEndLng:       request.ToLng,
		UserEndPoiId:     request.ToPoiId,
		UserEndName:      request.ToName,
		UserEndAddress:   request.ToAddress,
		AppKey:           conf.Viper.GetString("dai_jia.app_key"),
		WayPointList:     utils.StringPtr(request.GetStopoverPoints()),
	}
	res := daijia.GetTripEstimation(ctx, req)

	d.res = res
}

func (d *DaijiaTravelRpc) RenderTimeText(ctx context.Context) (string, string) {
	if d.res == nil || d.res.GetDuration() <= 0 {
		return model.SubType_Daijia, ""
	}

	totalTime := d.res.GetDuration()
	return model.SubType_Daijia, d.getEstimateTime(ctx, totalTime/60)

}

func (d *DaijiaTravelRpc) getEstimateTime(ctx context.Context, etd int64) string {
	if etd <= 0 {
		return ""
	}

	hours := etd / 60
	minutes := etd % 60

	if hours == 0 {
		return util.ReplaceTag(ctx, d.conf.BaseTimeConf.Minutes, map[string]string{
			"minutes": cast.ToString(minutes),
		})
	}

	if minutes == 0 {
		return util.ReplaceTag(ctx, d.conf.BaseTimeConf.Hours, map[string]string{
			"hours": cast.ToString(hours),
		})
	}

	return util.ReplaceTag(ctx, d.conf.BaseTimeConf.HoursMinutesWithoutMinuteUnit, map[string]string{
		"hours":   cast.ToString(hours),
		"minutes": cast.ToString(minutes),
	})

}
