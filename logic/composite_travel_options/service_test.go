package composite_travel_options

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBuildDaijia(t *testing.T) {

	t.Run("1", func(t *testing.T) {
		result := buildDaijia(nil)
		assert.Nil(t, result)
	})

	t.Run("2", func(t *testing.T) {
		subTypeToTimeText := map[string]string{
			"daijia": "10分钟",
		}
		result := buildDaijia(subTypeToTimeText)
		assert.NotNil(t, result)
	})

	t.Run("3", func(t *testing.T) {
		subTypeToTimeText := map[string]string{
			"xxxx": "10分钟",
		}
		result := buildDaijia(subTypeToTimeText)
		assert.Nil(t, result)
	})
}
