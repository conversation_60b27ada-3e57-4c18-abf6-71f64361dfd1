package get_intercity_basic_fee_lite

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_intercity_basic_fee_lite/data"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_intercity_basic_fee_lite/logic"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/get_intercity_basic_fee_lite"
	"runtime/debug"
)

func GetIntercityBasicFeeLite(ctx context.Context, req *proto.IntercityBasicFeeLiteRequest) (respData *proto.IntercityBasicFeeLiteData, errno int) {
	if req == nil {
		return nil, 0
	}

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, "GetIntercityBasicFeeLite", "stack: %s \n %v", errMsg, string(debug.Stack()))
		} else {
			// AddPublicLog(ctx)
		}
	}()

	newLogic := logic.NewLogic(req)
	ntupleInfo := newLogic.GetNTupleInfo(ctx)
	if ntupleInfo == nil {
		log.Trace.Warnf(ctx, "GetIntercityBasicFeeLite", "fail to get ntuple info")
		return nil, consts.ErrnoParams
	}

	stationBasicFeeResp, err := newLogic.GetStationBasicFee(ctx, ntupleInfo)
	if err != nil {
		log.Trace.Warnf(ctx, "GetIntercityBasicFeeLite", "fail to get station basic fee")
		return nil, consts.ErrnoGetPriceEstimateRsp
	}

	stationElementList := newLogic.FormatStationBusBasicFeeResp(stationBasicFeeResp)

	adapter := data.NewIntercityBasicFeeLiteAdapter(req, stationElementList, stationBasicFeeResp)

	respData = get_intercity_basic_fee_lite.GenerateFeeDesc(ctx, adapter)

	return respData, 0

}
