package data

import (
	"strconv"

	TripCloudPassenger "git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassenger"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type IntercityBasicFeeLiteAdapter struct {
	req                *proto.IntercityBasicFeeLiteRequest
	stationElementList []*StationElement
	resp               *TripCloudPassenger.GetStationPriceResponse
}

func NewIntercityBasicFeeLiteAdapter(req *proto.IntercityBasicFeeLiteRequest, stationElementList []*StationElement, resp *TripCloudPassenger.GetStationPriceResponse) *IntercityBasicFeeLiteAdapter {
	return &IntercityBasicFeeLiteAdapter{
		req:                req,
		stationElementList: stationElementList,
		resp:               resp,
	}
}

func (i *IntercityBasicFeeLiteAdapter) GetRequest() *proto.IntercityBasicFeeLiteRequest {
	return i.req
}

func (i *IntercityBasicFeeLiteAdapter) GetStationElementList() []*StationElement {
	return i.stationElementList
}

func (i *IntercityBasicFeeLiteAdapter) GetStartStationId() int64 {
	if i.req == nil || i.req.StartStationId == "" {
		return 0
	}

	startStationId, err := strconv.ParseInt(i.req.StartStationId, 10, 64)
	if err != nil {
		return 0
	}
	return startStationId
}

func (i *IntercityBasicFeeLiteAdapter) GetEndStationId() int64 {
	if i.req == nil || i.req.EndStationId == "" {
		return 0
	}

	endStationId, err := strconv.ParseInt(i.req.EndStationId, 10, 64)
	if err != nil {
		return 0
	}
	return endStationId
}

func (i *IntercityBasicFeeLiteAdapter) GetBasicPrice() float64 {
	if i.resp.Data == nil {
		return 0
	}

	return i.resp.Data.Price
}

func (i *IntercityBasicFeeLiteAdapter) GetPriceType() int64 {
	if i.resp == nil || i.resp.Data == nil {
		return -1
	}

	return i.resp.Data.IsPrice

}
