package intercity_station_rebook

// 定义 MockCarpoolBookingData 类型
// MockCarpoolBookingData 是 CarpoolBookingData 的一个 mock 实现
// 需要根据实际接口方法进行实现

//func Test_initTimeSpan(t *testing.T) {
//	t.Run("正常情况", func(t *testing.T) {
//		ctx := context.Background()
//
//		busShift := make([]*CarpoolOpenApi.StationInventory, 0)
//		busShift = append(busShift, &CarpoolOpenApi.StationInventory{
//			BusServiceShiftID: "123",
//			DepartureTime:     1742916573,
//			Status:            1,
//			RemainSeats:       20,
//			ExtraInfo: map[string]string{
//				"carry_child_need_seats": "0",
//				"has_child_ticket":       "0",
//				"has_coupon_ticket":      "0",
//				"remain_carry_child_num": "0",
//				"shift_id":               "123",
//			},
//		})
//		busShift = append(busShift, &CarpoolOpenApi.StationInventory{
//			BusServiceShiftID: "456",
//			DepartureTime:     1743002973,
//			Status:            1,
//			RemainSeats:       20,
//			ExtraInfo: map[string]string{
//				"carry_child_need_seats": "0",
//				"has_child_ticket":       "0",
//				"has_coupon_ticket":      "0",
//				"remain_carry_child_num": "0",
//				"shift_id":               "456",
//			},
//		})
//
//		passengerTicket := make([]*dto.PassengerDetail, 0)
//
//		prov := &intercity_common.AdapterInterCity{
//			ProductInfoFull: &biz_runtime.ProductInfoFull{
//				BaseReqData: &models.BaseReqData{
//					CommonBizInfo: models.CommonBizInfo{
//						OpenAppointmentRange:  13,
//						SelectedBusServiceDay: "",
//						PreBusServiceShiftId:  "789",
//						PassengerTickerInfo:   passengerTicket,
//					},
//				},
//				Product: &models.Product{
//					BizInfo: &models.PrivateBizInfo{
//						IntercityData: models.IntercityData{
//							StationInventoryInfo: &models.StationInventoryInfo{
//								StationInventorys: busShift,
//							},
//						},
//					},
//				},
//			}}
//
//		got, _ := initTimeSpan(ctx, prov)
//
//		res := make([]*proto.InterCityTimeSpan, 0)
//		res = append(res, &proto.InterCityTimeSpan{
//			Date: "今天",
//			Range: []*proto.InterCityTimeRange{
//				&proto.InterCityTimeRange{
//					Value:             "1742916573",
//					Msg:               "",
//					OuterMsg:          "23:29",
//					OrderType:         0,
//					SkuDesc:           "",
//					Icon:              "",
//					Available:         true,
//					BusServiceShiftId: "123",
//					Selected:          false,
//				},
//			},
//			Day:      "3.25",
//			Selected: true,
//		})
//		res = append(res, &proto.InterCityTimeSpan{
//			Date: "明天",
//			Range: []*proto.InterCityTimeRange{
//				&proto.InterCityTimeRange{
//					Value:             "1743002973",
//					Msg:               "",
//					OuterMsg:          "23:29",
//					OrderType:         0,
//					SkuDesc:           "",
//					Icon:              "",
//					Available:         true,
//					BusServiceShiftId: "456",
//					Selected:          false,
//				},
//			},
//			Day:      "3.26",
//			Selected: false,
//		})
//		// 使用 assert 进行断言
//		assert.ElementsMatch(t, got, res)
//	})
//
//	t.Run("无库存情况", func(t *testing.T) {
//		ctx := context.Background()
//		busShift := make([]*CarpoolOpenApi.StationInventory, 0)
//		busShift = append(busShift, &CarpoolOpenApi.StationInventory{
//			BusServiceShiftID: "123",
//			DepartureTime:     1742898573,
//			Status:            1,
//			RemainSeats:       20,
//			ExtraInfo: map[string]string{
//				"carry_child_need_seats": "0",
//				"has_child_ticket":       "0",
//				"has_coupon_ticket":      "0",
//				"remain_carry_child_num": "0",
//				"shift_id":               "123",
//			},
//		})
//		busShift = append(busShift, &CarpoolOpenApi.StationInventory{
//			BusServiceShiftID: "456",
//			DepartureTime:     1743002973,
//			Status:            1,
//			RemainSeats:       20,
//			ExtraInfo: map[string]string{
//				"carry_child_need_seats": "0",
//				"has_child_ticket":       "0",
//				"has_coupon_ticket":      "0",
//				"remain_carry_child_num": "0",
//				"shift_id":               "456",
//			},
//		})
//
//		passengerTicket := make([]*dto.PassengerDetail, 0)
//
//		prov := &intercity_common.AdapterInterCity{
//			ProductInfoFull: &biz_runtime.ProductInfoFull{
//				BaseReqData: &models.BaseReqData{
//					CommonBizInfo: models.CommonBizInfo{
//						OpenAppointmentRange:  13,
//						SelectedBusServiceDay: "",
//						PreBusServiceShiftId:  "789",
//						PassengerTickerInfo:   passengerTicket,
//					},
//				},
//				Product: &models.Product{
//					BizInfo: &models.PrivateBizInfo{
//						IntercityData: models.IntercityData{
//							StationInventoryInfo: &models.StationInventoryInfo{
//								StationInventorys: busShift,
//							},
//						},
//					},
//				},
//			}}
//
//		got, _ := initTimeSpan(ctx, prov)
//
//		res := make([]*proto.InterCityTimeSpan, 0)
//		res = append(res, &proto.InterCityTimeSpan{
//			Date: "明天",
//			Range: []*proto.InterCityTimeRange{
//				&proto.InterCityTimeRange{
//					Value:             "1743002973",
//					Msg:               "",
//					OuterMsg:          "23:29",
//					OrderType:         0,
//					SkuDesc:           "",
//					Icon:              "",
//					Available:         true,
//					BusServiceShiftId: "456",
//					Selected:          false,
//				},
//			},
//			Day:      "3.26",
//			Selected: true,
//		})
//		// 使用 assert 进行断言
//		assert.ElementsMatch(t, got, res)
//	})
//}
