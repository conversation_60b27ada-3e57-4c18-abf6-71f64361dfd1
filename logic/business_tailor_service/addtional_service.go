package business_tailor_service

import (
	"context"
	"encoding/json"
	"fmt"
	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hundun"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

type AdditionService struct {
	serviceReq   *hundunClient.SceneReq
	pcId         models.ProductCategory
	preferOption []*proto.BusinessPreferOption

	serviceData []*hundunClient.PcServiceData
}

func AdditionServiceRPC(ctx context.Context, baseInfos *BaseInfos) *AdditionService {
	var (
		nodeReqs  []*hundun.HundunNodeReq
		hundunReq = &hundunClient.SceneReq{}
		nodeReq   string
	)

	commonData := &hundunClient.SceneCommReq{
		Caller:      hundun.Caller,
		AppVersion:  baseInfos.CommonInfo.AppVersion,
		AccessKeyId: cast.ToString(baseInfos.CommonInfo.AccessKeyID),
		Channel:     int32(baseInfos.CommonInfo.Channel),
		Pid:         baseInfos.PassengerInfo.PID,
		Area:        baseInfos.AreaInfo.Area,
		Phone:       baseInfos.PassengerInfo.Phone,
		Lang:        baseInfos.CommonInfo.Lang,
		Callee:      hundun.Callee,
		District:    baseInfos.AreaInfo.District,
	}

	nodeOriginalReq := &hundun.HundunNodeReq{
		ProductCategory:   cast.ToInt(baseInfos.ProductCategory),
		ProductId:         cast.ToInt(baseInfos.ProductId),
		BusinessId:        cast.ToInt(baseInfos.BusinessId),
		ComboType:         GetComboType(baseInfos.CommonInfo.AirportType),
		RequireLevel:      cast.ToString(baseInfos.RequireLevel),
		Pid:               baseInfos.PassengerInfo.PID,
		Phone:             baseInfos.PassengerInfo.Phone,
		AccessKeyId:       cast.ToInt(baseInfos.CommonInfo.AccessKeyID),
		AppVersion:        baseInfos.CommonInfo.AppVersion,
		Lang:              baseInfos.CommonInfo.Lang,
		Area:              cast.ToInt(baseInfos.AreaInfo.Area),
		Flat:              baseInfos.AreaInfo.FromLat,
		Flng:              baseInfos.AreaInfo.FromLng,
		Tlat:              baseInfos.AreaInfo.ToLat,
		Tlng:              baseInfos.AreaInfo.ToLng,
		Type:              cast.ToInt(baseInfos.CommonInfo.OrderType),
		PageType:          cast.ToInt(baseInfos.CommonInfo.PageType),
		CallCarType:       cast.ToInt(baseInfos.CommonInfo.CallCarType),
		DepartureTime:     baseInfos.CommonInfo.DepartureTime,
		MenuId:            baseInfos.CommonInfo.MenuID,
		SubMenuId:         "",
		TrafficNumber:     baseInfos.CommonInfo.TrafficNumber,
		TrafficDepTime:    baseInfos.CommonInfo.TrafficDepTime,
		AirportId:         cast.ToInt(baseInfos.CommonInfo.AirportId),
		RailwayType:       0,
		FlightDepCode:     baseInfos.CommonInfo.FlightDepCode,
		FlightDepTerminal: baseInfos.CommonInfo.FlightDepTerminal,
	}

	nodeReqs = append(nodeReqs, nodeOriginalReq)

	nodeData, err := json.Marshal(nodeReqs)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagCheckDiffS2H, "req marshel err")
		return nil
	}

	nodeReq = fmt.Sprintf("%s", nodeData)
	hundunReq.SetCommonReq(commonData)
	hundunReq.SetNodeReq(nodeReq)

	return &AdditionService{
		serviceReq: hundunReq,
		pcId:       models.ProductCategory(baseInfos.ProductCategory),
	}
}

func (as *AdditionService) Fetch(ctx context.Context) bool {
	hundunResp := hundun.GetService(ctx, as.serviceReq)
	if hundunResp == nil || len(hundunResp[as.pcId]) == 0 {
		return false
	}

	as.serviceData = hundunResp[as.pcId]
	return true
}

func (as *AdditionService) BuildResultInfo(ctx context.Context, info *RpcResultInfo) {
	info.AdditionalServiceData = as.serviceData
}
