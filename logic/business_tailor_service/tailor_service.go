package business_tailor_service

import (
	"context"
	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hundun"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type TailorService struct {
	tailorReq    *hundunClient.GetTailorServiceReq
	preferOption []*proto.BusinessPreferOption
}

func TailorServiceRPC(ctx context.Context, baseInfos *BaseInfos) *TailorService {
	tailorReq := &hundunClient.GetTailorServiceReq{
		ProductId:  baseInfos.ProductId,
		BusinessId: baseInfos.BusinessId,
		//ProductCategory: &baseInfos.ProductCategory,
		Uid:  baseInfos.PassengerInfo.UID,
		Oid:  &baseInfos.OrderId,
		Lang: baseInfos.CommonInfo.Lang,
		Area: baseInfos.AreaInfo.Area,
	}

	if baseInfos.RequireLevel != 0 {
		tailorReq.RequireLevel = &baseInfos.RequireLevel
	}

	if baseInfos.OrderId != "" {
		tailorReq.Oid = &baseInfos.OrderId
	}

	return &TailorService{
		tailorReq: tailorReq,
	}
}

func (ts *TailorService) Fetch(ctx context.Context) bool {
	tailorData := hundun.GetTailorServiceV2(ctx, ts.tailorReq)
	if tailorData == nil || tailorData.PreferInfo == nil || len(tailorData.PreferInfo.PreferOption) == 0 {
		return false
	}

	var (
		preferOption       = tailorData.PreferInfo.PreferOption
		preferOptionResult = make([]*proto.BusinessPreferOption, 0)
	)

	for _, optionalInfo := range preferOption {
		preferInfo := &proto.BusinessPreferOption{
			Id:       optionalInfo.Id,
			IsSelect: optionalInfo.IsSelect,
		}

		preferOptionResult = append(preferOptionResult, preferInfo)
	}

	ts.preferOption = preferOptionResult
	return true
}

func (ts *TailorService) BuildResultInfo(ctx context.Context, info *RpcResultInfo) {
	info.HundunTailorData = ts.preferOption
}
