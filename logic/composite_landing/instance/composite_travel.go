package instance

import (
	"context"
	"encoding/json"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/controller/composite_travel"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type CompositeTravel struct {
	req        *proto.CompositeTravelReq
	controller *composite_travel.PCompositeTravelController
}

func (c *CompositeTravel) Adapter(ctx context.Context, req *proto.CompositeTravelV2Req) {
	reqStr, _ := json.Marshal(req)
	_ = json.Unmarshal(reqStr, &c.req)
	c.req.SetIsRecLanding(1)
	c.controller = &composite_travel.PCompositeTravelController{}
}

func (c *CompositeTravel) GetRes(ctx context.Context) (*proto.PRecommendLandingData, BizError.BizError) {
	resp := c.controller.PCompositeTravel(ctx, c.req)
	if resp == nil || resp.Errno != 0 || resp.Data == nil {
		return nil, BizError.ErrSystem
	}

	if resp.Data.IsRecommend != 1 {
		return &proto.PRecommendLandingData{JumpTab: tab.TabIdNormal}, BizError.Success
	}

	data := make(map[string]interface{})
	marshal, err := json.Marshal(resp.Data)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json.Marshal error|| err= %v||data=%v", err, resp.Data)
		return nil, BizError.ErrSystem
	}
	err = json.Unmarshal(marshal, &data)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json.Unmarshal error|| err= %v||data=%s", err, marshal)
		return nil, BizError.ErrSystem
	}
	data["trace_id"] = resp.TraceId

	return &proto.PRecommendLandingData{
		RenderData: data,
		JumpTab:    tab.TabIdMaas80,
	}, BizError.Success
}
