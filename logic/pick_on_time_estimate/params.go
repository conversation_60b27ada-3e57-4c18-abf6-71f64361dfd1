package pick_on_time_estimate

import (
	"context"
	"strconv"
	"time"

	"git.xiaojukeji.com/nuwa/trace"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"

	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const (
	PickOnTime = 1        //必有车标识 1代表必有车
	DefaultTab = "normal" //普通打车的tab
	ZhCn       = "zh-CN"  //简体中文

	// AirportTypeNone 0
	AirportTypeNone int16 = 0
	// AirportTypeFrom 接机 1
	AirportTypeFrom int16 = 1
	//AirportTypeTo 送机 2
	AirportTypeTo int16 = 2

	// RailwayTypeNone 0 非接站
	RailwayTypeNone int16 = 0
	// RailwayTypeFrom 1 接站
	RailwayTypeFrom int16 = 1
	// RailwayTypeTo 2 送站
	RailwayTypeTo int16 = 2
)

type BaseParams struct {
	OrderInfo    *dos.OrderInfo
	UserInfo     *passport.UserInfo
	OrderFeature *ufs.OrderFeature
}

type Logic struct {
	generator *biz_runtime.ProductsGenerator
}

func InitProductGenerator(ctx context.Context, req *proto.PickOnTimeEstimateReq) (*biz_runtime.ProductsGenerator, int) {
	var (
		errno       int
		err         error
		baseReq     *models.BaseReqData
		productsGen *biz_runtime.ProductsGenerator
	)

	baseReq, errno = initParams(ctx, req)

	if consts.NoErr != errno {
		return nil, errno
	}
	if productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pPickOnTimeEstimate"),
		biz_runtime.WithBaseReq(baseReq),
		biz_runtime.WithSourceID(ctx, int32(source_id.SourceIDPickOnTime)),
	); err != nil {
		return nil, consts.GetErrorNum(err)
	}
	//设置必有车标识
	productsGen.BaseReqData.CommonBizInfo.IsPickOnTime = PickOnTime

	productsGen.SetNeedMember(false)
	productsGen.SetSendReqKafka(false)

	return productsGen, consts.NoErr
}

func initParams(ctx context.Context, request *proto.PickOnTimeEstimateReq) (res *models.BaseReqData, errno int) {
	var (
		err         error
		userInfo    *passport.UserInfo
		baseReqData *models.BaseReqData
	)
	tryGetUserInfo := func(ctx context.Context, token string, accessKeyID int32) (*passport.UserInfo, error) {
		if token != "" {
			return passport.GetUserInfo(ctx, token, "")
		}

		param := ApolloModel.NewUser("").With("access_key_id", strconv.Itoa(int(accessKeyID)))
		toggle, err2 := ApolloSDK.FeatureToggle("check_token_pMultiEstimatePriceV2", param)
		if err2 != nil || toggle.IsAllow() {
			return nil, BizError.ErrNotLogin
		}
		return &passport.UserInfo{}, nil
	}
	if userInfo, err = tryGetUserInfo(ctx, request.Token, request.AccessKeyId); err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getUserInfo: %v", err)
		return nil, consts.ErrnoGetUserInfo
	}

	//等待应答校验参数；预估的校验在pre-sale
	var orderInfo *dos.OrderInfo
	if request.PageType == 20 {
		//判断请求是否支持
		if !isSupport(request) {
			return nil, consts.ErrnoParams
		}
		//获取订单信息，校验场站
		if request.GetOrderId() != "" {
			orderID, district, err := util.DecodeOrderID(request.OrderId)
			if err != nil {
				log.Trace.Errorf(ctx, trace.DLTagUndefined, "decodeErr: %v", err)
				return nil, consts.ErrnoDecode
			}
			// 请求 dos
			orderInfo, err = dos.GetOrderInfo(ctx, orderID, district)
			if err != nil {
				log.Trace.Infof(ctx, trace.DLTagUndefined, "getOrderInfo: %v", err)
				return nil, consts.ErrnoGetOrderInfo
			}
			//接机或者接站不展示必有车
			if orderInfo.AirportType == strconv.Itoa(int(AirportTypeFrom)) || orderInfo.RailwayType == strconv.Itoa(int(RailwayTypeFrom)) {
				return nil, consts.ErrnoParams
			}
		}
	}

	if baseReqData, err = models.NewBaseReqDataBuilder().SetPassengerInfoV2(
		&models.PassengerInfoV2{
			UID:      int64(userInfo.UID),
			PID:      int64(userInfo.PID),
			Phone:    userInfo.Phone,
			Role:     userInfo.Role,
			Channel:  userInfo.Channel,
			UserType: request.UserType,
			OriginID: userInfo.OriginId,
		}).
		SetClientInfo(&models.ClientInfo{
			AppVersion:   request.AppVersion,
			AccessKeyID:  request.AccessKeyId,
			Channel:      util.IntParseWithDefault(request.Channel, 0),
			ClientType:   request.ClientType,
			Lang:         request.Lang,
			PlatformType: request.PlatformType,
			MenuID:       "dache_anycar",
			PageType:     request.PageType,
			Xpsid:        util.StringPtr2String(request.Xpsid),
			XpsidRoot:    util.StringPtr2String(request.XpsidRoot),
		}).
		SetGEOInfo(&models.GEOInfo{
			MapType:           request.MapType,
			CurrLat:           request.Lat,
			CurrLng:           request.Lng,
			FromLat:           request.FromLat,
			FromLng:           request.FromLng,
			FromPOIID:         request.FromPoiId,
			FromPOIType:       request.FromPoiType,
			FromAddress:       request.FromAddress,
			FromName:          request.FromName,
			ToLat:             request.ToLat,
			ToLng:             request.ToLng,
			ToPOIID:           request.ToPoiId,
			ToPOIType:         request.ToPoiType,
			ToAddress:         request.ToAddress,
			ToName:            request.ToName,
			StopoverPointInfo: request.StopoverPoints,
		}).
		SetUserOption(&models.UserOption{
			CallCarType:    request.GetCallCarType(),
			OrderType:      request.GetOrderType(),
			PaymentsType:   request.GetPaymentsType(),
			DepartureTime:  time.Now().Unix(),
			StopoverPoints: &(request.StopoverPoints),
		}).TryBuild(ctx); err != nil {
		return nil, consts.GetErrorNum(err)
	}
	//屏蔽跨城订单
	if baseReqData.AreaInfo.Area != baseReqData.AreaInfo.ToArea {
		return nil, consts.ErrnoParams
	}
	baseReqData.SendOrder.BuildByDosOrder(ctx, orderInfo, request.OrderId)

	return baseReqData, consts.NoErr

}

// 等待应答判断是否支持必有车,过滤不支持必有车的case
func isSupport(request *proto.PickOnTimeEstimateReq) bool {
	//1、不支持预约单; 0 实时 1预约
	if request.GetOrderType() != 0 {
		return false
	}
	//2、不支持代叫; 0为普通叫车；1为代叫车
	if request.GetCallCarType() != 0 {
		return false
	}
	//3、不支持企业付; 2:个人付 21 :企业付 默认支付方式支持必有车，由收银侧拦截
	if request.GetPaymentsType() == 21 {
		return false
	}
	//4、非中文（zh-CN）不支持
	if request.Lang != ZhCn {
		return false
	}
	return true
}
