package mcp_estimate

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// CheckParams 参数校验
func CheckParams(ctx context.Context, req *proto.PMCPEstimateReq) error {
	if req == nil {
		return fmt.Errorf("request is nil")
	}

	// 校验必填字段
	if req.Uid <= 0 {
		return fmt.Errorf("invalid uid: %d", req.Uid)
	}

	if req.Pid <= 0 {
		return fmt.Errorf("invalid pid: %d", req.Pid)
	}

	if req.AppVersion == "" {
		return fmt.Errorf("app_version is empty")
	}

	if req.AccessKeyId <= 0 {
		return fmt.Errorf("invalid access_key_id: %d", req.AccessKeyId)
	}

	if req.Channel <= 0 {
		return fmt.Errorf("invalid channel: %d", req.Channel)
	}

	if req.Lang == "" {
		return fmt.Errorf("lang is empty")
	}

	// 校验地理位置信息
	if req.FromLat == 0 || req.FromLng == 0 {
		return fmt.Errorf("invalid from location: lat=%f, lng=%f", req.FromLat, req.FromLng)
	}

	if req.ToLat == 0 || req.ToLng == 0 {
		return fmt.Errorf("invalid to location: lat=%f, lng=%f", req.ToLat, req.ToLng)
	}

	if req.FromName == "" {
		return fmt.Errorf("from_name is empty")
	}

	if req.ToName == "" {
		return fmt.Errorf("to_name is empty")
	}

	return nil
}

// InitLogic 初始化业务逻辑
func InitLogic(ctx context.Context, req *proto.PMCPEstimateReq) (*BizLogic, error) {
	// 构建基础请求数据
	baseReq, err := models.NewBaseReqDataBuilder().
		SetClientInfo(&models.ClientInfo{
			AppVersion:  req.AppVersion,
			AccessKeyID: req.AccessKeyId,
			Channel:     int64(req.Channel),
			Lang:        req.Lang,
		}).
		SetGEOInfo(&models.GEOInfo{
			FromLat:  req.FromLat,
			FromLng:  req.FromLng,
			FromName: req.FromName,
			ToLat:    req.ToLat,
			ToLng:    req.ToLng,
			ToName:   req.ToName,
		}).
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:   req.Uid,
			PID:   req.Pid,
			Phone: req.Phone,
		}).
		TryBuild(ctx)

	if err != nil {
		return nil, fmt.Errorf("build base request failed: %w", err)
	}

	// no-check
	productsGen, _ := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pPbdEstimate"),
		biz_runtime.WithBaseReq(baseReq),
	)

	productsGen.SetNeedMember(false)

	productsGen.SetSendReqKafka(false)

	RegisterRpcProcessWithBasicProducts(ctx, productsGen)

	return &BizLogic{generator: productsGen}, nil
}
