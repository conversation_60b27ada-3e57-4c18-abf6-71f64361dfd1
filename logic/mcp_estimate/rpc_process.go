package mcp_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

func RegisterRpcProcessWithBasicProducts(ctx context.Context, generator *biz_runtime.ProductsGenerator) {
	// 出租车
	if taxiSps := rpc_process.NewTaxiPeakFee(ctx, generator.BaseReqData); taxiSps != nil {
		generator.RegisterBeforePriceRpcProcess(taxiSps)
	}
}
