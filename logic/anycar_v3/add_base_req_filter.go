package anycar_v3

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	minibus_estimate "git.xiaojukeji.com/gulfstream/mamba/logic/minibus_estimate/filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_dds_filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_price_filter"
)

func RegisterProductFilter(ctx context.Context, params *BaseParams, generator *biz_runtime.ProductsGenerator) {
	if generator.BaseReqData == nil {
		return
	}
	//过滤当前已呼叫品类
	if cpFilter := after_dds_filter.NewAnyCarFilterSendProducts(ctx,
		params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct, generator.BaseReqData.PassengerInfo.UserType); cpFilter != nil {
		generator.RegisterAfterDdsFilter(cpFilter)
	}

	//对于 曹操/T3/广汽/哈啰 出行，不支持 多个同productID 的品类追加
	if cpFilter := after_dds_filter.NewAnyCarFilterTCSendProductsFilter(ctx,
		params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct, generator.BaseReqData); cpFilter != nil {
		generator.RegisterAfterDdsFilter(cpFilter)
	}

	//两口价 打勾 拼车；过滤快车
	if fFilter := after_dds_filter.NewGuideAnyCarFilterFastCar(ctx,
		params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct, generator.BaseReqData.PassengerInfo.UserType); fFilter != nil {
		generator.RegisterAfterDdsFilter(fFilter)
	}

	// 豪华车车型过滤 已发单六座 则不再出六座
	if lFilter := after_dds_filter.NewLuxuryProductFilter(ctx, params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct, generator.BaseReqData); lFilter != nil {
		generator.RegisterAfterDdsFilter(lFilter)
	}

	// 司乘议价补充信息
	if lFilter := rpc_process.NewBargainInfoLogic(ctx, params.OrderInfo.ExtendFeatureParsed.EstimateTraceID, generator.BaseReqData, "v3"); lFilter != nil {
		generator.RegisterAfterDdsFilter(lFilter)
		generator.RegisterBeforePriceRpcProcess(lFilter)
	}

	//部分场景过滤三方车型（未授权三方不再过滤）
	isAddTripCloudFilter := true
	//Apollo控制是否要过滤未授权三方车型
	if apollo.FeatureToggle(ctx, "tripcloud_is_filter_not_auth_switch", strconv.Itoa(int(params.UserInfo.UID)), generator.BaseReqData.GetApolloParam()) {
		isAddTripCloudFilter = false
	}

	if tcFilter := after_dds_filter.NewGuideAnyCarFilterTripCloud(params.OrderInfo.PassengerId, params.OrderInfo.ExtraType,
		params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct, isAddTripCloudFilter, params.OrderInfo); tcFilter != nil {
		generator.RegisterAfterDdsFilter(tcFilter)
	}

	// 惠选车 准出条件
	if brFilter := after_price_filter.NewBargainRangeFilter(params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct); brFilter != nil {
		generator.RegisterAfterPriceFilter(brFilter)
	}

	//过滤A+
	if tcFilter := after_dds_filter.NewAPlusAbFilter(ctx, params.OrderInfo.ExtraType, generator.BaseReqData); tcFilter != nil {
		generator.RegisterAfterDdsFilter(tcFilter)
	}

	//针对企业支付 预估额外添加了快车的情况，需要再次过滤已呼叫品类
	if cpFilter := after_price_filter.NewGuideAnyCarFilterCurrentProducts(ctx,
		params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct); cpFilter != nil {
		generator.RegisterAfterPriceFilter(cpFilter)
	}

	//过滤tripcloud近14天日均完单小于10的且价格最低的冒泡
	generator.RegisterAfterPriceFilter(after_price_filter.NewAbnormalEstimateNewFilter(ctx))

	// filter 出租车计价盒子
	if cpFilter := after_dds_filter.NewTaxiPricingFilter(ctx, generator.BaseReqData,
		params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct, "v3"); cpFilter != nil {
		generator.RegisterAfterDdsFilter(cpFilter)
	}

	// 远必省车型和普通三方车型的实验，根据实验过滤相关车型
	if fpFilter := after_price_filter.NewFarMustCheapAgainstTCFilter(generator); fpFilter != nil {
		generator.RegisterAfterPriceFilter(fpFilter)
	}

	// 超值达盒子，过滤相关车型
	if cpFilter := after_price_filter.NewChaoZhiDaFilter(); cpFilter != nil {
		generator.RegisterAfterPriceFilter(cpFilter)
	}

	// 预匹配过滤小巴
	if MbFilter := minibus_estimate.NewMiniBusFilter(); MbFilter != nil {
		generator.RegisterAfterPriceFilter(MbFilter)
	}
	// 追加车型过滤小巴
	if AddMbFilter := minibus_estimate.NewAdditionalMinibusFilter(ctx, generator.GetReqFrom(), generator.BaseReqData, params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct); AddMbFilter != nil {
		generator.RegisterAfterDdsFilter(AddMbFilter)
	}
	// 处理互斥品类
	if mutexFilter := after_dds_filter.NewDealMutexFilter(ctx, generator.BaseReqData); mutexFilter != nil {
		generator.RegisterAfterDdsFilter(mutexFilter)
	}
	// 香港现金支付发单追加车型过滤不支持现金支付的车型
	if cashFilter := after_price_filter.NewHkCashNewOrderFilter(); cashFilter != nil {
		if generator.IsHongKong() {
			generator.RegisterAfterPriceFilter(cashFilter)
		}
	}
}
