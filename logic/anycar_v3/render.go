package anycar_v3

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	pool2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/pool"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v3/data"
	v4_data "git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v4/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	context2 "git.xiaojukeji.com/lego/context-go"
	Trace "git.xiaojukeji.com/nuwa/trace"
)

func RenderProductList(ctx context.Context, gen *biz_runtime.ProductsGenerator, products []*biz_runtime.ProductInfoFull, reqFrom string) (
	*proto.NewFormEstimateResponse, int) {

	var (
		estimateData    = make(map[int64]*proto.NewFormEstimateData)   // 渲染后数据
		rawEstimateData = make(map[int64]*proto.RawEstimateData)       // 纯数据
		productMap      = make(map[int64]*biz_runtime.ProductInfoFull) // 存储已渲染的品类数据
		resp            = &proto.NewFormEstimateResponse{
			EstimateTraceId: context2.GetTrace(ctx).GetTraceId(),
		}
		renderService = data.NewRender(ctx)

		renderFunc pool2.TaskFunc
	)

	ctx = renderService.PreBuilder(ctx, products)

	if reqFrom == "pAnycarEstimateV4" {
		renderFunc = v4_data.RenderByProduct
	} else {
		renderFunc = data.RenderByProduct
	}

	// 1. 构建预估数据
	pool := pool2.NewPool(ctx)
	for _, pFull := range products {
		pool.AddTask(&pool2.Task{
			Req:     pFull,
			RunTask: renderFunc,
		})
	}

	//并行渲染
	_, err := pool.MultiProc()
	if err != nil {
		log.Trace.Warnf(ctx, Trace.DLTagUndefined, "multi_render got err: %v", err)
		return resp, consts.ErrnoRenderErr
	}
	//串行封装结果
	for _, task := range pool.GetResult() {
		if task.GetErr() != nil || task.Resp == nil {
			continue
		}
		pFull, ok1 := task.Req.(*biz_runtime.ProductInfoFull)
		if !ok1 {
			continue
		}

		userPid, params := pFull.ApolloParamsGen(apollo_model.WithPIDKey, apollo_model.WithProductCategory)
		ok, assignment := apollo.FeatureExp(ctx, "pcl_addto_anycar_list", userPid, params)
		// 特价拼车实验组渲染，其他非拼车渲染
		if pFull.GetProductCategory() == estimate_pc_id.EstimatePcIdLowPriceCarpool && !(ok && "treatment_group" == assignment.GetGroupName()) {
			// 过滤
		} else {
			// 追加车型页面unRenderData中的数据不渲染
			buildEstimateProduct(task, pFull, productMap, estimateData)
		}

		if assembleResp, ok := task.Resp.(*biz_runtime.AssembleResp); ok && assembleResp != nil {
			rawData := assembleResp.RawEstimateData
			rawEstimateData[rawData.ProductCategory] = rawData
		}
	}
	resp.EstimateData = estimateData
	resp.RawEstimateData = rawEstimateData

	// 2. 构建Layout
	resp.Layout = New(ctx, gen.BaseReqData, estimateData, productMap).BuildLayout(ctx)
	// 3. 预估额外数据
	renderService.ExtendDataRender(ctx, resp, gen)
	return resp, consts.NoErr
}

func buildEstimateProduct(task *pool2.Task, pFull *biz_runtime.ProductInfoFull, productMap map[int64]*biz_runtime.ProductInfoFull, estimateData map[int64]*proto.NewFormEstimateData) {
	if assembleResp, ok := task.Resp.(*biz_runtime.AssembleResp); ok && assembleResp != nil {
		product := assembleResp.EstimateData
		pFull.Product.BizInfo.CheckStatus = int(product.IsSelected)
		productMap[product.ProductCategory] = pFull
		estimateData[product.ProductCategory] = product
	}
}
