package bargain_range

import (
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
)

type BargainRangeViewAdapter struct {
	DupsFeeExtraInfo  map[string]interface{} //定价返回的价格单位是分，故需要进行转换
	EstimateStripInfo *AthenaApiv3.EstimateStripInfo
	Req               *proto.BargainRangeEstimateReq
	EstimateQuotation *biz_runtime.Quotation
}

const (
	PriceLimitUpper     = "price_limit_upper"      //价格上界
	PriceLimitLower     = "price_limit_lower"      //价格下界
	RecommendPriceUpper = "recommend_price_upper"  //推荐价上界
	RecommendPriceLower = "recommend_price_lower"  //推荐价下界
	WaitReplyPriceUpper = "wait_reply_price_upper" //等待应答价格上界
)

func (v *BargainRangeViewAdapter) GetPriceLimitUpper() int32 {
	return cast.ToInt32(cast.ToFloat64(v.DupsFeeExtraInfo[PriceLimitUpper]) / 100)
}
func (v *BargainRangeViewAdapter) GetPriceLimitLower() int32 {
	return cast.ToInt32(cast.ToFloat64(v.DupsFeeExtraInfo[PriceLimitLower]) / 100)
}
func (v *BargainRangeViewAdapter) GetRecommendPriceUpper() int32 {
	return cast.ToInt32(cast.ToFloat64(v.DupsFeeExtraInfo[RecommendPriceUpper]) / 100)
}
func (v *BargainRangeViewAdapter) GetRecommendPriceLower() int32 {
	return cast.ToInt32(cast.ToFloat64(v.DupsFeeExtraInfo[RecommendPriceLower]) / 100)
}
func (v *BargainRangeViewAdapter) GetWaitReplyPriceUpper() int32 {
	return cast.ToInt32(cast.ToFloat64(v.DupsFeeExtraInfo[WaitReplyPriceUpper]) / 100)
}
func (v *BargainRangeViewAdapter) GetAnswerRate() int32 {
	if v.EstimateStripInfo != nil && v.EstimateStripInfo.AnswerRate != nil && *v.EstimateStripInfo.AnswerRate > 0 {
		return *v.EstimateStripInfo.AnswerRate
	}
	return 0
}
func (v *BargainRangeViewAdapter) GetFastCarEstimateFee() float64 {
	return v.Req.GetFastCarEstimateFee()
}
func (v *BargainRangeViewAdapter) GetSpFastCarEstimateFee() float64 {
	return v.Req.GetSpFastCarEstimateFee()
}
func (v *BargainRangeViewAdapter) GetHighWayFee() float64 {
	return v.EstimateQuotation.HighwayFee
}
func (v *BargainRangeViewAdapter) GetQuotation() *biz_runtime.Quotation {
	return v.EstimateQuotation
}
