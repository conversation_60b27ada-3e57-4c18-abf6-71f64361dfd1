package sfc_simple_estimate

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	ufsV2 "git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"github.com/spf13/cast"
	"strconv"
	"time"
)

const (
	LOG_TAG = "sfc"
)

type SFCarAdapter struct {
	*biz_runtime.ProductInfoFull
	//apolloParams *plain_text_render.ApolloParams
}

type Logic struct {
	generator     *biz_runtime.ProductsGenerator
	passengerNums int32
	maxNum        int32 //可选择最大座位数
	dcmpData      proto.SFCEstimateDcmp
	userInfo      passport.UserInfo
	traceID       string
	request       proto.SFCSimpleEstimateReq
}

func NewLogic(ctx context.Context, request *proto.SFCSimpleEstimateReq, traceId string) (*Logic, error) {
	var (
		userInfo       *passport.UserInfo
		departureTime  int64
		departureRange models.DepartureRange
	)
	//参数校验
	checkParams := func(request *proto.SFCSimpleEstimateReq) error {
		if request.FromLat == 0 || request.FromLng == 0 || request.ToLat == 0 || request.ToLng == 0 {
			log.Trace.Warnf(ctx, LOG_TAG, "参数校验")
			return BizError.ErrInvalidArgument
		}
		return nil
	}
	if err := checkParams(request); err != nil {
		return nil, err
	}

	//用户信息获取
	userInfo = &passport.UserInfo{
		PID:      util.UID2pid(uint64(request.Uid)),
		UID:      uint64(request.Uid),
		Phone:    request.GetPhone(),
		Role:     1,
		Channel:  request.GetUserChannel(),
		OriginId: "1",
	}

	//乘客人数
	passengerNums := request.GetPassengerNums()
	if request.PassengerNums == nil || *request.PassengerNums == int32(0) {
		passengerNums = 1
	}

	timeRange := request.GetDepartureRange()
	if len(timeRange) > 1 && timeRange[0] > 0 && timeRange[1] > 0 {
		departureTime = timeRange[0]
		departureRange = models.DepartureRange{
			From: time.Unix(timeRange[0], 0),
			To:   time.Unix(timeRange[1], 0),
		}
	} else {
		log.Trace.Warnf(ctx, LOG_TAG, "time range is error!")
		return nil, BizError.ErrInvalidArgument
	}

	//品类查询
	productsGen, err := getProductInfo(ctx, departureRange, userInfo, request, departureTime, passengerNums)
	if err != nil {
		return nil, err
	}

	return &Logic{
		generator: productsGen,
		userInfo:  *userInfo,
		traceID:   traceId,
		request:   *request,
	}, nil

}

func getProductInfo(ctx context.Context, departureRange models.DepartureRange, userInfo *passport.UserInfo, request *proto.SFCSimpleEstimateReq, departureTime int64, PassengerNums int32) (*biz_runtime.ProductsGenerator, error) {
	var (
		err         error
		baseReqData *models.BaseReqData
	)
	// platformType代表端类型，1ios，2安卓，3小程序
	platformType := request.AccessKeyId
	if request.AccessKeyId == 1 || request.AccessKeyId == 2 {
		platformType = request.AccessKeyId
	} else {
		platformType = 3
	}
	pageType := 35
	if baseReqData, err = models.NewBaseReqDataBuilder().
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(userInfo.UID),
			PID:      int64(userInfo.PID),
			Phone:    userInfo.Phone,
			Role:     userInfo.Role,
			Channel:  userInfo.Channel,
			UserType: 1, //默认成人
			OriginID: userInfo.OriginId,
		}).
		SetClientInfo(&models.ClientInfo{
			AppVersion:   request.AppVersion,
			AccessKeyID:  request.AccessKeyId,
			MenuID:       "dache_anycar",
			PageType:     cast.ToInt32(pageType),
			Channel:      request.GetChannel(),
			PlatformType: platformType,
			Lang:         "zh-CN",
		}).
		SetGEOInfo(&models.GEOInfo{
			MapType:     request.MapType,
			CurrLat:     request.GetLat(),
			CurrLng:     request.GetLng(),
			FromLat:     request.FromLat,
			FromLng:     request.FromLng,
			FromPOIID:   request.FromPoiId,
			FromPOIType: request.GetFromPoiType(),
			FromAddress: request.GetFromAddress(),
			FromName:    request.FromName,
			ToLat:       request.ToLat,
			ToLng:       request.ToLng,
			ToPOIID:     request.ToPoiId,
			ToPOIType:   request.GetToPoiType(),
			ToAddress:   request.GetToAddress(),
			ToName:      request.ToName,
		}).
		SetUserOption(&models.UserOption{
			OrderType:      request.OrderType,
			DepartureTime:  departureTime,   // 简化版账单计价规则不区分时间，但还是传给账单departure_time
			DepartureRange: &departureRange, // 简化版未处理端上传的departure_range，看后续计价规则和产品方案
			CarpoolSeatNum: PassengerNums,
		}).TryBuild(ctx); err != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "new logic1 err=%+v", err)
		return nil, err
	}

	var productsGen *biz_runtime.ProductsGenerator
	if productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pSFCEstimate"),
		biz_runtime.WithBaseReq(baseReqData),
	); err != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "new logic2 err=%+v", err)
		return nil, err
	}

	productsGen.SetNeedMember(true)
	productsGen.SetSendReqKafka(false)

	if AuthRPC := rpc_process.NewSFCAuthRPC(ctx, ufsV2.DomainPassenger, strconv.FormatInt(productsGen.BaseReqData.PassengerInfo.PID, 10)); AuthRPC != nil {
		productsGen.RegisterAfterPriceRPCProcess(AuthRPC)
		productsGen.RegisterFinalFilter(AuthRPC)
	}
	if WycAuthRPC := rpc_process.NewSFCWYCAuthRPC(ctx, ufsV2.DomainPassenger, strconv.FormatInt(productsGen.BaseReqData.PassengerInfo.PID, 10)); WycAuthRPC != nil {
		productsGen.RegisterAfterPriceRPCProcess(WycAuthRPC)
		productsGen.RegisterFinalFilter(WycAuthRPC)
	}
	return productsGen, nil
}

// Do 执行方法
func (logic *Logic) Do(ctx context.Context) (*proto.SFCSimpleEstimateData, error) {
	var (
		err      error
		products []*biz_runtime.ProductInfoFull
	)
	if products, err = logic.generator.GenProducts(ctx); err != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "genProducts err=%+v", err)
		return nil, err
	}
	data := &proto.SFCSimpleEstimateData{}
	var sfcSimpleProductList []*proto.SFCSimpleEstimatePriceInfo
	for _, product := range products {
		prov := &SFCarAdapter{product}
		estimateFeeList := prov.GetMultiPrice()
		if len(estimateFeeList) <= 0 {
			continue
		}
		//城际表单先不出
		if product.GetBusinessID() != consts.ProductIDSFC {
			continue
		}
		sfcSimpleProductInfo := &proto.SFCSimpleEstimatePriceInfo{
			EstimateId:               product.GetEstimateID(),
			EstimateCarpoolFailPrice: estimateFeeList[0],
			EstimateCarpoolPrice:     estimateFeeList[0],
			ProductCategory:          product.GetProductCategory(),
		}
		if len(estimateFeeList) >= 2 {
			sfcSimpleProductInfo.EstimateCarpoolFailPrice = estimateFeeList[1]
		}
		sfcSimpleProductList = append(sfcSimpleProductList, sfcSimpleProductInfo)
	}
	data.PriceInfo = sfcSimpleProductList

	return data, nil
}

// GetMultiPrice 获取金额
func (a *SFCarAdapter) GetMultiPrice() []float64 {
	var (
		estimateFeeList = make([]float64, 0)
	)

	prices := a.GetSceneEstimatePrice()
	for _, price := range prices {
		estimateFeeList = append(estimateFeeList, price.GetFeeDetail().GetCapPrice())
	}

	return estimateFeeList
}
