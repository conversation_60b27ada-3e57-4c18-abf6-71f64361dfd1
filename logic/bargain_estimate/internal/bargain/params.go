package bargain

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/dao/ufs_logic"
	"github.com/spf13/cast"
	"strconv"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type BaseParams struct {
	OrderInfo    *dos.OrderInfo
	UserInfo     *passport.UserInfo
	OrderFeature *ufs.OrderFeature
	Request      *proto.BargainEstimateReq
}

func InitParams(ctx context.Context, req *proto.BargainEstimateReq) (params *BaseParams, errno int) {
	var (
		orderInfo *dos.OrderInfo
		err1      error
	)

	//请求 passport
	tryGetUserInfo := func(ctx context.Context, token string, accessKeyID int32) (*passport.UserInfo, error) {
		if token != "" {
			return passport.GetUserInfo(ctx, token, "")
		}

		param := ApolloModel.NewUser("").With("access_key_id", strconv.Itoa(int(accessKeyID)))
		toggle, err2 := ApolloSDK.FeatureToggle("check_token_pMultiEstimatePriceV2", param)
		if err2 != nil || toggle.IsAllow() {
			return nil, BizError.ErrNotLogin
		}
		return &passport.UserInfo{}, nil
	}
	userInfo, err := tryGetUserInfo(ctx, req.Token, req.AccessKeyId)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getUserInfo: %v", err)
		return nil, consts.ErrnoGetUserInfo
	}
	//等待应答场景
	if req.GetFrom() == "anycar" {
		if req.GetOrderId() == "" {
			return nil, consts.ErrnoParams
		}
		orderID, district, err := util.DecodeOrderID(req.OrderId)
		if err != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "decodeErr: %v", err)
			return nil, consts.ErrnoDecode
		}

		// 请求 dos
		orderInfo, err1 = dos.GetOrderInfo(ctx, orderID, district)
		if err1 != nil {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "getOrderInfo: %v", err1)
			return nil, consts.ErrnoGetOrderInfo
		}

		if orderInfo.SourceType == source_type.SourceTypeCanceledCreateOrder {
			// 改派订单特殊处理 获取一下最原始orderid
			initialOrderID, _ := ufs.GetInitialOrderIDFeature(ctx, orderID)
			if initialOrderID != "" {
				orderInitialID, initialDistrict, erro := util.DecodeOrderID(initialOrderID)
				if erro != nil {
					log.Trace.Errorf(ctx, trace.DLTagUndefined, "getRedisInitialOrderId decodeErr: %v", erro)
				}
				if orderInitialID != 0 && initialDistrict != "" {
					orderID = orderInitialID
					district = initialDistrict
				}
			}
		}

		// 请求 mock ufs
		feature := ufs_logic.BuildMockOrderFeature(ctx, orderInfo, &proto.AnyCarEstimateReq{UserType: &req.UserType,
			MapType: &req.MapType, PlatformType: &req.PlatformType, Channel: cast.ToInt64(req.Channel)})

		// 订单归属校验
		if orderInfo.PassengerId != strconv.FormatInt(int64(userInfo.PID), 10) {
			return nil, consts.ErrnoOrderNotBelongUser
		}
		//设置经纬度信息
		req.Lat = util.String2float64(ctx, orderInfo.CurrentLat)
		req.Lng = util.String2float64(ctx, orderInfo.CurrentLng)
		req.MapType = feature.MapType

		req.FromLng = util.String2float64(ctx, feature.FromLng)
		req.FromLat = util.String2float64(ctx, feature.FromLat)
		req.FromAddress = feature.FromAddress
		req.FromName = feature.FromName
		req.FromPoiId = orderInfo.StartingPoiId
		req.FromPoiType = feature.FromPoiType

		req.ToLat = util.String2float64(ctx, feature.ToLat)
		req.ToLng = util.String2float64(ctx, feature.ToLng)
		req.ToAddress = feature.ToAddress
		req.ToName = feature.ToName
		req.ToPoiId = orderInfo.DestPoiId
		req.ToPoiType = feature.ToPoiType
	}

	params = &BaseParams{
		UserInfo:  userInfo,
		Request:   req,
		OrderInfo: orderInfo,
	}
	return params, consts.NoErr
}
