package business_estimate

import (
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_buildB2BBill(t *testing.T) {
	t.Run("构建价格", func(t *testing.T) {
		mockey.PatchConvey("构建价格", t, func() {
			var carpoolFee float64
			product := &biz_runtime.ProductInfoFull{
				BillDetail: &PriceApi.BillDetail{
					CarpoolFailDynamicTotalFee: &carpoolFee,
					DriverRealtimeTotalFee:     14.2,
				},
			}
			bill, err := buildB2BBill(product)
			assert.Equal(t, consts.NoErr, err)
			assert.Equal(t, 14.2, bill.DriverRealtimeTotalFee)
			assert.Equal(t, float64(0), bill.BasicTotalFee)
		})
	})
}
