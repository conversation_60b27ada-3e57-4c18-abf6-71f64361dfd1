package mq

import (
	"strconv"

	funk "github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
)

func getProductConfigMap() (string, error) {
	return ApolloSDK.GetConfigItem("biz_config", "config_product_map", "product_map")
}

func productIDToBizType(productID int) int {
	const suffix = ".business_id"
	mm, e := getProductConfigMap()
	if e != nil {
		return 0
	}
	path := strconv.Itoa(productID) + suffix
	if b := gjson.Get(mm, path); b.Exists() {
		return int(b.Int())
	}
	return int(gjson.Get(mm, "0.business_id").Int())
}

func isFastCar(productID int) int {
	const suffix = ".belongs_product_group"
	mm, e := getProductConfigMap()
	if e != nil {
		return 0
	}
	getProductGroup := func(path string) []int {
		if b := gjson.Get(mm, path); b.Exists() && b.<PERSON>y() {
			raw := b.Array()
			x := make([]int, 0, len(raw))
			for _, r := range raw {
				x = append(x, int(r.Int()))
			}
			return x
		}
		return nil
	}

	path := strconv.Itoa(productID) + suffix
	if group := getProductGroup(path); len(group) > 0 {
		if funk.ContainsInt(group, 2) {
			return 1
		}
		return 0
	}
	if group := getProductGroup("0.belongs_product_group"); len(group) > 0 {
		if funk.ContainsInt(group, 2) {
			return 1
		}
		return 0 //
	}
	return 0
}
