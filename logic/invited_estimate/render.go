package invited_estimate

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/invited"
	context2 "git.xiaojukeji.com/lego/context-go"
	legoTrace "git.xiaojukeji.com/lego/context-go"
	"strconv"
)

func RenderProductList(ctx context.Context, req *proto.InvitedEstimateReq, products []*biz_runtime.ProductInfoFull) (*proto.InvitedEstimateData, int) {
	var (
		resp = &proto.InvitedEstimateData{
			TraceId: context2.GetTrace(ctx).GetTraceId(),
		}
	)
	defer func() {
		writePublicLog(ctx, req, products, *resp)
	}()

	resp.CouponInfo = invited.CouponInfo(ctx, products)
	return resp, consts.NoErr
}

func writePublicLog(ctx context.Context, req *proto.InvitedEstimateReq, products []*biz_runtime.ProductInfoFull, res proto.InvitedEstimateData) {
	// 屏蔽压测流量
	if _trace := legoTrace.GetTrace(ctx); _trace.GetHintCode() == strconv.Itoa(int(consts.PressureHintCode)) {
		return
	}

	for _, product := range products {
		if product == nil || product.Product == nil {
			continue
		}
		logInfo := make(map[string]interface{})
		orderNTuple, _ := json.Marshal(product.Product)
		// 索引key
		logInfo["estimate_trace_id"] = util.GetTraceIDFromCtxWithoutCheck(ctx)

		// 产品信息
		logInfo["estimate_id"] = product.Product.EstimateID
		logInfo["product_category"] = product.GetProductCategory()
		logInfo["product_id"] = product.Product.ProductID
		logInfo["business_id"] = product.Product.BusinessID
		logInfo["require_level"] = product.Product.RequireLevel
		logInfo["combo_type"] = product.Product.ComboType
		logInfo["carpool_type"] = product.Product.CarpoolType
		logInfo["order_type"] = product.Product.OrderType
		logInfo["level_type"] = product.Product.LevelType
		logInfo["is_special_price"] = product.Product.IsSpecialPrice
		logInfo["order_type"] = product.Product.OrderType
		logInfo["airport_type"] = product.Product.AirportType
		logInfo["railway_type"] = product.Product.RailwayType
		logInfo["lang"] = req.Lang
		logInfo["menu_id"] = "dache_anycar"
		logInfo["order_n_tuple"] = string(orderNTuple)

		// 地理位置信息
		if areaInfo := product.GetAreaInfo(); areaInfo != nil {
			logInfo["area"] = areaInfo.City
			logInfo["to_area"] = areaInfo.ToArea
			logInfo["district"] = areaInfo.District
			logInfo["county"] = areaInfo.FromCounty
			logInfo["to_county"] = areaInfo.ToCounty
			logInfo["from_name"] = util.StringEscape(areaInfo.FromName)
			logInfo["to_name"] = util.StringEscape(areaInfo.ToName)
			logInfo["flat"] = areaInfo.FromLat
			logInfo["flng"] = areaInfo.FromLng
			logInfo["tlat"] = areaInfo.ToLat
			logInfo["tlng"] = areaInfo.ToLng
			logInfo["current_lat"] = areaInfo.CurLat
			logInfo["current_lng"] = areaInfo.CurLng
			logInfo["map_type"] = areaInfo.MapType
		}

		// 端信息
		if commonInfo := product.GetClientInfo(); commonInfo != nil {
			logInfo["app_version"] = commonInfo.AppVersion
			logInfo["client_type"] = commonInfo.ClientType
			logInfo["access_key_id"] = commonInfo.AccessKeyID
			logInfo["channel_id"] = commonInfo.Channel
			logInfo["page_type"] = commonInfo.PageType
			logInfo["origin_id"] = commonInfo.OriginID
			logInfo["xpsid"] = commonInfo.Xpsid
			logInfo["xpsid_root"] = commonInfo.XpsidRoot
		}
		// 用户信息
		if product.BaseReqData != nil {
			logInfo["pid"] = product.BaseReqData.PassengerInfo.PID
			logInfo["uid"] = product.BaseReqData.PassengerInfo.UID
			logInfo["phone"] = product.BaseReqData.PassengerInfo.Phone
			logInfo["user_type"] = product.BaseReqData.CommonInfo.UserType
		}

		// 价格信息
		logInfo["driver_metre"] = product.GetBillDriverMetre()
		logInfo["driver_minute"] = product.GetBillDriverMinute()
		logInfo["pre_total_fee"] = product.GetPreTotalFee()
		logInfo["cap_price"] = product.GetCapPrice()
		logInfo["dynamic_total_fee"] = product.GetDynamicTotalFee()
		logInfo["estimate_fee"] = product.GetEstimateFee()
		if res.CouponInfo != nil && res.CouponInfo.GetEstimateId() == product.GetEstimateID() {
			logInfo["invited_show_coupon_discount"] = res.CouponInfo.GetCouponDiscount()
			logInfo["invited_show_estimate_id"] = res.CouponInfo.GetEstimateId()
			logInfo["invited_show_coupon_type"] = res.CouponInfo.GetCouponType()
		}

		log.Public.Public(ctx, "g_order_cap_multi_estimate_price", logInfo)
	}
}
