package invited_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	trace "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

func InvitedEstimate(ctx context.Context, req *proto.InvitedEstimateReq) (rspData *proto.InvitedEstimateData, errno int) {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	productsGenerator, errno := InitProductGenerator(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	products, err := productsGenerator.GenProducts(ctx)
	if len(products) == 0 || err != nil {
		return nil, consts.ErrnoNoProductOpen
	}
	log.Trace.Debugf(ctx, trace.DLTagUndefined, "products: %s", util.JustJsonEncode(products))

	if consts.NoErr != errno {
		return nil, errno
	}
	return RenderProductList(ctx, req, products)
}
