package invited_estimate

import (
	"context"
	"strconv"
	"time"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type BaseParams struct {
	OrderInfo    *dos.OrderInfo
	UserInfo     *passport.UserInfo
	OrderFeature *ufs.OrderFeature
}

type Logic struct {
	generator *biz_runtime.ProductsGenerator
}

func InitProductGenerator(ctx context.Context, req *proto.InvitedEstimateReq) (*biz_runtime.ProductsGenerator, int) {
	var (
		errno       int
		err         error
		baseReq     *models.BaseReqData
		productsGen *biz_runtime.ProductsGenerator
	)

	baseReq, errno = initParams(ctx, req)

	if consts.NoErr != errno {
		return nil, errno
	}
	if productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pInvitedEstimate"),
		biz_runtime.WithBaseReq(baseReq),
	); err != nil {
		return nil, consts.GetErrorNum(err)
	}

	productsGen.SetNeedMember(false)
	productsGen.SetSendReqKafka(false)

	return productsGen, consts.NoErr
}

func initParams(ctx context.Context, request *proto.InvitedEstimateReq) (res *models.BaseReqData, errno int) {
	var (
		err         error
		userInfo    *passport.UserInfo
		baseReqData *models.BaseReqData
	)
	tryGetUserInfo := func(ctx context.Context, token string, accessKeyID int32) (*passport.UserInfo, error) {
		if token != "" {
			return passport.GetUserInfo(ctx, token, "")
		}

		param := ApolloModel.NewUser("").With("access_key_id", strconv.Itoa(int(accessKeyID)))
		toggle, err2 := ApolloSDK.FeatureToggle("check_token_pMultiEstimatePriceV2", param)
		if err2 != nil || toggle.IsAllow() {
			return nil, BizError.ErrNotLogin
		}
		return &passport.UserInfo{}, nil
	}
	if userInfo, err = tryGetUserInfo(ctx, request.Token, request.AccessKeyId); err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getUserInfo: %v", err)
		return nil, consts.ErrnoGetUserInfo
	}

	if baseReqData, err = models.NewBaseReqDataBuilder().SetPassengerInfoV2(
		&models.PassengerInfoV2{
			UID:      int64(userInfo.UID),
			PID:      int64(userInfo.PID),
			Phone:    userInfo.Phone,
			Role:     userInfo.Role,
			Channel:  userInfo.Channel,
			UserType: request.UserType,
			OriginID: userInfo.OriginId,
		}).
		SetClientInfo(&models.ClientInfo{
			AppVersion:   request.AppVersion,
			AccessKeyID:  request.AccessKeyId,
			Channel:      util.IntParseWithDefault(request.Channel, 0),
			ClientType:   request.ClientType,
			Lang:         request.Lang,
			PlatformType: request.PlatformType,
			MenuID:       "dache_anycar",
			PageType:     request.PageType,
			Xpsid:        util.StringPtr2String(request.Xpsid),
			XpsidRoot:    util.StringPtr2String(request.XpsidRoot),
		}).
		SetGEOInfo(&models.GEOInfo{
			MapType:     request.MapType,
			CurrLat:     request.Lat,
			CurrLng:     request.Lng,
			FromLat:     request.FromLat,
			FromLng:     request.FromLng,
			FromPOIID:   request.FromPoiId,
			FromPOIType: request.FromPoiType,
			FromAddress: request.FromAddress,
			FromName:    request.FromName,
			ToLat:       request.ToLat,
			ToLng:       request.ToLng,
			ToPOIID:     request.ToPoiId,
			ToPOIType:   request.ToPoiType,
			ToAddress:   request.ToAddress,
			ToName:      request.ToName,
		}).
		SetUserOption(&models.UserOption{
			CallCarType:   0,
			OrderType:     0,
			PaymentsType:  0,
			DepartureTime: time.Now().Unix(),
		}).TryBuild(ctx); err != nil {
		return nil, consts.GetErrorNum(err)
	}

	return baseReqData, consts.NoErr

}
