package coupon_price

import (
	"context"
	"fmt"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"testing"
)

func TestBuildFerrariReq(t *testing.T) {
	req := buildFerrariReq(context.TODO(), &proto.CouponPriceRequest{}, &passport.UserInfo{}, &PriceApi.EstimateQuotation{})
	fmt.Println(req)
}
