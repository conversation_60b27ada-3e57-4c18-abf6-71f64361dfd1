package coupon_price

import (
	"context"
	"encoding/json"
	"errors"
	"math"
	"strconv"

	CommonPlatCoupon "git.xiaojukeji.com/dirpc/dirpc-go-http-CommonPlatCoupon"
	Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	CommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	util2 "git.xiaojukeji.com/gulfstream/passenger-common/util"
	"github.com/spf13/cast"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/utils"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/coupon"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	isConfirm       = 1 // 确认改价
	productCouponID = 210
	requireLevel    = "13000"
)

type Logic struct {
	baseReqData *proto.CouponPriceRequest
	userInfo    *passport.UserInfo
	quotation   *PriceApi.EstimateQuotation
}

func NewLogic(ctx context.Context, request *proto.CouponPriceRequest) (*Logic, error) {
	var (
		err           error
		userInfo      *passport.UserInfo
		quotationData = make(map[string]PriceApi.EstimateQuotation)
	)
	if request.FeeAmount > 1000000 || request.FeeAmount < 0 {
		return nil, errors.New("fee_amount is invalid")
	}

	tryGetUserInfo := func(ctx context.Context, token string, accessKeyID int32) (*passport.UserInfo, error) {
		if token != "" {
			return passport.GetUserInfo(ctx, token, "")
		}

		param := ApolloModel.NewUser("").With("access_key_id", strconv.Itoa(int(accessKeyID)))
		toggle, err2 := ApolloSDK.FeatureToggle("check_token_pMultiEstimatePriceV2", param)
		if err2 != nil || toggle.IsAllow() {
			return nil, BizError.ErrNotLogin
		}
		return &passport.UserInfo{}, nil
	}

	userInfo, err = tryGetUserInfo(ctx, request.Token, request.AccessKeyId)
	if err != nil {
		return nil, err
	}

	if request.GetEstimateId() != "" {
		quotationResp, PriceErr := getQuotationBatch(ctx, []string{request.GetEstimateId()})
		if PriceErr != nil || quotationResp == nil || quotationResp.Data == nil {
			return nil, PriceErr
		}

		quotationData = quotationResp.Data
	}

	//获取司乘议价的报价单数据
	quotation := quotationData[request.GetEstimateId()]
	return &Logic{baseReqData: request, userInfo: userInfo, quotation: &quotation}, err

}

func (logic *Logic) Do(ctx context.Context) (*proto.CouponEstimateData, error) {
	result := &proto.CouponEstimateData{}
	var err error

	pidStr := strconv.FormatUint(logic.userInfo.PID, 10)
	param := ApolloModel.NewUser(pidStr).
		With("pid", pidStr).
		With("phone", logic.userInfo.Phone).
		With("access_key_id", strconv.Itoa(int(logic.baseReqData.AccessKeyId)))
	if toggle, _ := ApolloSDK.FeatureToggle("gs_mamba_use_ferrari", param); toggle.IsAllow() {
		result, err = logic.doFerrari(ctx)
	} else {
		result, err = logic.doCoupon(ctx)
	}

	if result == nil {
		return nil, errors.New("result nil")
	}

	// 如果是等应答追加车型且是最后确认改价
	if logic.baseReqData.GetPageType() == page_type.PageTypeGuideAnyCar && logic.baseReqData.GetIsConfirm() == isConfirm {
		// 转oid
		orderHelper := util2.Helper{}
		orderParam, err2 := orderHelper.DecodeId(logic.baseReqData.GetOid())
		if err2 != nil {
			log.Trace.Warnf(ctx, "getCouponPrice", "decode order id fail, err:%v", err2)
			return nil, err2
		}
		sFeeAmount := strconv.FormatFloat(logic.baseReqData.GetFeeAmount(), 'f', 2, 64)

		// 用低位oid存ufs
		params := map[string]string{"order_id": cast.ToString(orderParam.Oid)}
		feeInfo := map[string]string{"estimate_fee": result.EstimateFee, "dynamic_total_fee": sFeeAmount, "coupon_discount": result.CouponDiscount}

		if result.DiscountAmount > 0 {
			sDiscountAmount := strconv.FormatFloat(result.DiscountAmount, 'f', 2, 64)
			feeInfo["discount_amount"] = sDiscountAmount
		}

		feeInfoString, err3 := json.Marshal(feeInfo)
		if err3 != nil {
			log.Trace.Warnf(ctx, "getCouponPrice", "setFeature: %v", err3)
			return nil, err3
		}
		kv := map[string]string{"bargain_adjust_price.fee_info": string(feeInfoString)}
		if _, err4 := ufs.SetFeature(ctx, "order", params, kv); err4 != nil {
			log.Trace.Warnf(ctx, "getCouponPrice", "setFeature: err:%v, key:%v", err4, "bargain_adjust_price.fee_info")
			return nil, err4
		}
	}

	return result, err
}

func (logic *Logic) doCoupon(ctx context.Context) (*proto.CouponEstimateData, error) {
	couponReq := buildCouponReq(ctx, logic.baseReqData, logic.userInfo, *logic.quotation)
	couponInfo, err := coupon.GetCouponInfo(ctx, couponReq)
	if err != nil {
		return nil, err
	}
	return BuildResponse(ctx, logic.baseReqData, convertToFerrariCoupon(couponInfo, logic)), nil
}

func convertToFerrariCoupon(couponInfo *CommonPlatCoupon.GetMultiAvailableCouponsResp, logic *Logic) (coupon *Ferrari.CalcBargainPostCouponPriceData) {
	if couponInfo != nil && len(couponInfo.Info) == 1 && len(couponInfo.Info[0].Info) != 0 && couponInfo.Info[0].Info[0] != nil {
		couponItem := couponInfo.Info[0].Info[0]
		feeAmount := int64(math.Round(logic.baseReqData.FeeAmount * 100))
		deductionFee, err := utils.ConvertToInt64(couponItem.DeductionAmount)
		if err != nil {
			return
		}

		coupon = &Ferrari.CalcBargainPostCouponPriceData{
			EstimateFee:  float64(feeAmount-deductionFee) / 100,
			CouponType:   couponItem.CouponType,
			Discount:     couponItem.Discount,
			DeductionFee: deductionFee,
		}
	}
	return
}

func (logic *Logic) doFerrari(ctx context.Context) (*proto.CouponEstimateData, error) {
	ferrariReq := buildFerrariReq(ctx, logic.baseReqData, logic.userInfo, logic.quotation)
	resp, err := ferrari.Client.CalcBargainPostCouponPrice(ctx, ferrariReq)
	if err != nil {
		return nil, err
	}

	return BuildResponse(ctx, logic.baseReqData, resp.GetData()), nil
}

func buildCouponReq(ctx context.Context, baseReq *proto.CouponPriceRequest, userInfo *passport.UserInfo, Quotation PriceApi.EstimateQuotation) *CommonPlatCoupon.GetMultiAvailableCouponsReq {
	var multiParamList = make([]*CommonPlatCoupon.GetMultiAvailableCouponsReqParams, 0)

	pid := int64(userInfo.PID)
	uid := int64(userInfo.UID)

	startLoc := &biz_runtime.Pos{
		Lat: baseReq.FromLat,
		Lng: baseReq.FromLng,
	}
	DestLoc := &biz_runtime.Pos{
		Lat: baseReq.ToLat,
		Lng: baseReq.ToLng,
	}
	location := &biz_runtime.Location{
		Start: startLoc,
		Dest:  DestLoc,
	}
	orderEnv := &biz_runtime.OrderEnv{
		CouponType:    CommonConsts.CouponTypeNormal,
		Fee:           int64(baseReq.FeeAmount * 100),
		Area:          int64(baseReq.FromArea),
		Source:        biz_runtime.GetBillingChannel(int(baseReq.AccessKeyId)),
		EstimatePrice: int64(baseReq.FeeAmount * 100),
		BusinessID:    CommonConsts.CommonProductIDBargain,
		ComboType:     CommonConsts.ComboTypeDefault,
		OrderType:     CommonConsts.OrderTypeNormalOrder,
		Loc:           location,
	}

	if Quotation.DepartureTime > 0 {
		//如果出发时间为0表示报价单为空，不传预估数据
		orderEnv.DepartureTime = Quotation.DepartureTime
		orderEnv.EstimateDistance = Quotation.DriverMetre
	}

	nTuple := &biz_runtime.NTuple{
		ProductID:       CommonConsts.ProductIDBargain,
		ProductCategory: estimate_pc_id.EstimatePcIdBargain,
		BusinessID:      int64(CommonConsts.CommonProductIDBargain),
		ComboType:       CommonConsts.ComboTypeDefault,
		OrderType:       int16(CommonConsts.OrderTypeNormalOrder),
		RequireLevel:    requireLevel,
	}

	orderNTuple, _ := json.Marshal(nTuple)
	orderNTupleStr := string(orderNTuple)
	orderEnvByte, _ := json.Marshal(orderEnv)

	multiParam := &CommonPlatCoupon.GetMultiAvailableCouponsReqParams{
		Productid:   productCouponID,
		EstimateId:  baseReq.EstimateId,
		OrderNTuple: &orderNTupleStr,
		OrderEnv:    string(orderEnvByte),
	}
	multiParamList = append(multiParamList, multiParam)

	couponReq := &CommonPlatCoupon.GetMultiAvailableCouponsReq{
		Caller: conf.Viper.GetString("module.name"),
		Pid:    &pid,
		Uid:    &uid,
		Phone:  userInfo.Phone,
		Params: multiParamList,
	}

	return couponReq
}

func getQuotationBatch(ctx context.Context, eidLists []string) (*PriceApi.GetQuotationBatchResp, error) {
	req := &price_api.PriceQuotationBatch{
		EstimateIdList: eidLists,
		Fields:         []string{},
	}

	resp, err := price_api.GetQuotationBatch(ctx, req)
	if err != nil || resp == nil {
		return nil, err
	}

	return resp, nil
}
