package coupon_price

import (
	"context"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/utils"
)

func getPopeCoupon(quotation *PriceApi.EstimateQuotation) *Ferrari.Coupon {
	discountList := quotation.ScenePriceDiscountV1
	if len(discountList) == 0 {
		return nil
	}
	discount := discountList[0]
	if len(discount.Coupon) == 0 {
		return nil
	}
	coupon := discount.Coupon[0]
	if coupon.CouponSource != "pope" {
		return nil
	}
	return &Ferrari.Coupon{
		Discount: int64(coupon.Discount),
		BatchId:  coupon.BatchId,
	}
}

func buildFerrariReq(ctx context.Context, baseReq *proto.CouponPriceRequest, userInfo *passport.UserInfo,
	quotation *PriceApi.EstimateQuotation) *Ferrari.CalcBargainPostCouponPriceRequest {
	pope := getPopeCoupon(quotation)
	originId, _ := utils.ConvertToInt64(userInfo.OriginId)
	return &Ferrari.CalcBargainPostCouponPriceRequest{
		Pid:              int64(userInfo.PID),
		AccessKeyId:      baseReq.AccessKeyId,
		FromArea:         baseReq.FromArea,
		FromLat:          baseReq.FromLat,
		FromLng:          baseReq.FromLng,
		ToLat:            baseReq.ToLat,
		ToLng:            baseReq.ToLng,
		FeeAmount:        baseReq.FeeAmount,
		EstimateId:       baseReq.GetEstimateId(),
		Uid:              int64(userInfo.UID),
		Phone:            userInfo.Phone,
		Caller:           "mamba",
		DepartureTime:    quotation.DepartureTime,
		EstimateDistance: quotation.DriverMetre,
		Pope:             pope,
		Role:             int64(userInfo.Role),
		OriginId:         originId,
		RedPacket:        util.Float64Ptr(quotation.RedPacket),
	}
}
