package data

import (
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
)

type DynamicFeeDescListAdapter struct {
	quotations map[string]*biz_runtime.Quotation
	userInfo   *UserInfo
	req        *proto.DynamicFeeDescListRequest
}

func NewDynamicFeeDescListAdapter(quotations map[string]*biz_runtime.Quotation, userInfo *UserInfo, req *proto.DynamicFeeDescListRequest) *DynamicFeeDescListAdapter {
	return &DynamicFeeDescListAdapter{
		quotations: quotations,
		userInfo:   userInfo,
		req:        req,
	}
}

func (d *DynamicFeeDescListAdapter) GetRequest() *proto.DynamicFeeDescListRequest {
	return d.req
}

func (d *DynamicFeeDescListAdapter) GetQuotations() map[string]*biz_runtime.Quotation {
	return d.quotations
}

func (d *DynamicFeeDescListAdapter) GetQuotationsByEid(eid string) *biz_runtime.Quotation {
	return d.quotations[eid]
}

func (d *DynamicFeeDescListAdapter) GetUserInfo() *UserInfo {
	return d.userInfo
}

func (d *DynamicFeeDescListAdapter) GetAccessKeyId() int32 {
	if d.req == nil {
		return 0
	}

	return d.req.AccessKeyId
}

func (d *DynamicFeeDescListAdapter) GetMenuId() string {
	return "dache_anycar"
}

func (d *DynamicFeeDescListAdapter) GetLang() string {
	if d.req == nil {
		return ""
	}

	return d.req.Lang
}

func (d *DynamicFeeDescListAdapter) GetAppVersion() string {
	if d.req == nil {
		return ""
	}

	return d.req.AppVersion
}

func (d *DynamicFeeDescListAdapter) GetFormType() *int32 {
	if d.req == nil {
		return nil
	}

	switch d.req.FormType {
	case page_type.PageTypeRecCarpoolEstimate:
		return util.Int32Ptr(consts.RecCarpoolOthersForm)
	case page_type.PageTypeSimpleEstimate:
		return util.Int32Ptr(consts.SimpleForm)
	case page_type.PageTypeGuideAnyCar:
		return util.Int32Ptr(consts.AnyCarV3Form)
	default:
		return util.Int32Ptr(consts.DefaultForm)
	}
}

func (d *DynamicFeeDescListAdapter) GetFontStyleType() int32 {
	if d.req == nil {
		return 0
	}

	return d.req.GetFontScaleType()
}
