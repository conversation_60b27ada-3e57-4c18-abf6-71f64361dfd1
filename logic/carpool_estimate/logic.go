package carpool_estimate

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/intercity_anycar"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_price_filter"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/ab"
	"github.com/spf13/cast"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/option_gen_price"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_fission/after_dds_fission"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/consts"
	bizCommonConstsV6 "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/seat_selection"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/ticket_detail_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/back"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/blank"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/intercity"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/intercity_new"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/intercity_sku"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/intercity_station"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/pincheche"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_common"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_common/filter"
	IntercityStation "git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate/internal1/intercity_station"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate/internal1/intercity_station_rebook"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/before_price_filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/private/pincheche"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type Request struct {
	*proto.CarpoolEstimateRequest
}

type BizLogic struct {
	generator *biz_runtime.ProductsGenerator
}

func (logic *BizLogic) render(ctx context.Context, products []*biz_runtime.ProductInfoFull) *proto.CarpoolEstimateResponseData {
	data := &proto.CarpoolEstimateResponseData{
		TabType:         proto.TabType_Blank,
		EstimateTraceId: util.GetTraceIDFromCtxWithoutCheck(ctx),
	}
	for _, prod := range products {
		var renderLogic = registerRenderLogic(ctx, prod, products)
		if renderLogic == nil {
			continue
		}
		go seat_selection.CacheSeatInfo(ctx, logic.generator.BaseReqData, prod)
		go SyncSendMQMessageNocheck(ctx, prod)

		renderLogic.SetTabType(data)
		renderLogic.RenderPageData(ctx, prod, data)
		renderLogic.BuildExTraData(ctx, logic.generator.BaseReqData, prod, data)
	}

	if data.TabType == proto.TabType_Blank {
		data.TabBlank = blank.BuildEstimateData(ctx, logic.generator.BaseReqData.CommonInfo.TabList, "")
	}
	isGuide := int32(0)
	if len(products) != 0 {
		isGuide = products[0].BaseReqData.CommonBizInfo.IsGuide
	}
	if isGuide == 1 {
		data.BackButton = back.BuildBackInfo(ctx, products[0])
	}

	return data
}

func registerRenderLogic(ctx context.Context, prod *biz_runtime.ProductInfoFull, products []*biz_runtime.ProductInfoFull) RenderPageService {
	switch true {
	case carpool.IsPinCheCheNewForm(prod.Product.ProductCategory, prod.BaseReqData.CommonBizInfo.PinCheCheFormInfo.FormSytle):
		return &pincheche.NewFormRenderPage{Products: products}
	case carpool.IsPinCheChe(prod.Product.ProductCategory):
		return &pincheche.RenderPage{}
	case carpool.IsIntercityStation(ctx, int(prod.Product.CarpoolType)):
		return &intercity_station.RenderPage{} // 城际站点模式
	case carpool.IsInterCityCarpool(prod.Product.CarpoolType, prod.Product.ComboType) && intercity_sku.IsSkuModel(prod):
		return &intercity_sku.RenderPage{} // 库存模式
	case carpool.IsInterCityCarpool(prod.Product.CarpoolType, prod.Product.ComboType) && intercity_new.IsNewModel(prod):
		return &intercity_new.RenderPage{} // 新城际样式拉齐独立页卡片, 目前看无量了。不要再继续迭代了，会删掉！！！
	case carpool.IsInterCityCarpool(prod.Product.CarpoolType, prod.Product.ComboType):
		return &intercity.RenderPage{} // 城际
	}
	return nil
}

type RenderPageService interface {
	SetTabType(*proto.CarpoolEstimateResponseData)
	RenderPageData(context.Context, *biz_runtime.ProductInfoFull, *proto.CarpoolEstimateResponseData)
	BuildExTraData(context.Context, *models.BaseReqData, *biz_runtime.ProductInfoFull, *proto.CarpoolEstimateResponseData)
}

func (logic *BizLogic) blankTab(ctx context.Context, tabList string, scene string) *proto.CarpoolEstimateResponseData {
	return &proto.CarpoolEstimateResponseData{
		TabType:         proto.TabType_Blank,
		EstimateTraceId: util.GetTraceIDFromCtxWithoutCheck(ctx),
		TabBlank:        blank.BuildEstimateData(ctx, tabList, scene),
	}
}

func (logic *BizLogic) intercityGuide(ctx context.Context, access_key_id int) *proto.IntercityEstimateData {
	res := &proto.IntercityEstimateData{}
	errUrl := ""
	if access_key_id == bizCommonConsts.AccessKeyIDDiDiAndroid || access_key_id == bizCommonConsts.AccessKeyIDDiDiIos ||
		access_key_id == bizCommonConstsV6.AccessKeyIDHarmonyOS {
		errUrl = dcmp.GetDcmpContent(ctx, "intercity_estimate-area_not_open_guide_url", nil)
	} else if access_key_id == bizCommonConsts.AccessKeyIDDiDiWechatMini || access_key_id == bizCommonConsts.AccessKeyIDDiDiAlipayMini {
		errUrl = dcmp.GetDcmpContent(ctx, "intercity_estimate-area_not_open_guide_applet_url", nil)
	}
	res.ErrorUrl = &errUrl
	return res
}

func BlankStopoverPointTab(ctx context.Context, req *proto.CarpoolEstimateRequest) *proto.CarpoolEstimateResponseData {
	return &proto.CarpoolEstimateResponseData{
		TabType:         proto.TabType_Blank,
		EstimateTraceId: util.GetTraceIDFromCtxWithoutCheck(ctx),
		TabBlank:        blank.BuildStopoverPointTabBlank(ctx, req),
	}
}

func (logic *BizLogic) DoBizLogic(ctx context.Context, req *proto.CarpoolEstimateRequest) (res *proto.CarpoolEstimateResponseData, publicFunc public_log.WritePublicFunc, err error) {
	products, err := logic.generator.GenProducts(ctx)
	if err != nil {
		if errors.Is(err, consts.ErrorGetFromDDSFail) {
			return logic.blankTab(ctx, logic.generator.BaseReqData.CommonInfo.TabList, ""), nil, nil
		}
		return nil, nil, err
	}

	err = logic.check(ctx, products)
	if err != nil {
		if errors.Is(err, consts.ErrorDDSTimeSliceNotMatch) {
			return logic.blankTab(ctx, logic.generator.BaseReqData.CommonInfo.TabList, "carpool_time_slice"), nil, nil
		}
		return nil, nil, err
	}
	var ok bool
	if res, ok = logic.renderIntercityAnycar(ctx, products); !ok {
		res = logic.render(ctx, products)
	}
	if logic.generator.BaseReqData.CommonInfo.PageType == page_type.PageTypeLowCarpoolEstimate {
		publicFunc = func() {
			WritePublicLogV2(ctx, public_log.GOrderEstimatePrice, products, req, logic.generator.BaseReqData)
		}
	} else {
		publicFunc = func() {
			WritePublicLog(ctx, products, req, int32(res.TabType))
		}
	}

	return res, publicFunc, nil
}

func (logic *BizLogic) renderPrice(product *biz_runtime.ProductInfoFull) []*proto.CarpoolScenePrice {
	carpoolPriceList := make([]*proto.CarpoolScenePrice, 0)
	sceneEstimateFeeList := product.GetSceneEstimatePrice()

	buildSceneMark := func(attr biz_runtime.FeeAttribute) map[string]string {
		sceneMark := make(map[string]string)
		markList := []string{"is_carpool_success", "pool_num", "seat_num"}
		for _, mark := range markList {
			if v := attr.GetString(mark); v != nil {
				sceneMark[mark] = *v
			}
		}
		return sceneMark
	}

	for _, scene := range sceneEstimateFeeList {

		attr := scene.GetFeeAttributes()
		var couponInfo *proto.Coupon
		if coupon := scene.GetFeeDetail().GetCoupon(); coupon != nil {
			couponInfo = &proto.Coupon{
				Tag:          coupon.Tag,
				Amount:       coupon.Amount,
				Discount:     coupon.Discount,
				CouponSource: coupon.Source,
			}
		}
		price := &proto.CarpoolScenePrice{
			SceneMark:       buildSceneMark(attr),
			CapPrice:        scene.GetFeeDetail().GetCapPrice(),
			DynamicTotalFee: scene.GetFeeDetail().GetTotalFeeWithoutCouponSome(),
			PreTotalFee:     scene.GetFeeDetail().GetPreTotalFee(),
			EstimateFee:     scene.GetFee(),
			CouponInfo:      couponInfo,
			FeeDetailInfo:   scene.GetFeeDetail().GetFeeDetailInfo(),
		}
		scene.GetFeeDetail().GetFeeItem()
		carpoolPriceList = append(carpoolPriceList, price)
	}
	return carpoolPriceList
}

func deriveDepartureTimeFromDepartureRange(rng *models.DepartureRange, tim *time.Time) time.Time {
	if tim != nil {
		return *tim
	}
	if rng == nil {
		return time.Now()
	}
	return rng.DepartureTime()
}

func BuildService(ctx context.Context, estimateReq *param_handler.EstimateRequest) (*BizLogic, error) {
	req, ok := estimateReq.ReqFromParams.(*proto.CarpoolEstimateRequest)
	if !ok {
		return nil, errors.New("error")
	}
	passenger := estimateReq.GetPassengerInfo()

	departureRange := intercity_common.DecodeDepartureRangeV3(req.DepartureRange)
	departureTime := intercity_common.DecodeDepartureTime(req.DepartureTime)

	tabListStr := ""
	if req.TabList != nil && *(req.TabList) != "" {
		tabListStr = *(req.TabList)
	}

	builder := models.NewBaseReqDataBuilder().
		SetClientInfo(&models.ClientInfo{
			AppVersion:      req.AppVersion,
			AccessKeyID:     req.AccessKeyId,
			Channel:         util.IntParseWithDefault(req.Channel, 0),
			ClientType:      req.ClientType,
			Lang:            req.Lang,
			PlatformType:    req.PlatformType,
			TerminalID:      0, // MAYBUG
			OriginID:        0, // MAYBUG
			Imei:            "",
			MenuID:          "dache_anycar",
			PageType:        estimateReq.GetPageType(),
			FromType:        estimateReq.GetFromType(),
			GuideTraceId:    estimateReq.GetGuideTraceId(),
			TabList:         tabListStr,
			IsScanCode:      req.GetAgentType(),
			ScanCodeShiftId: req.GetBusServiceShiftId(),
			Dchn:            req.GetDchn(),
		}).
		SetGEOInfo(&models.GEOInfo{
			MapType: req.MapType,

			CurrLat: req.Lat,
			CurrLng: req.Lng,

			FromLat:         req.FromLat,
			FromLng:         req.FromLng,
			FromAddress:     req.FromAddress,
			FromName:        req.FromName,
			FromPOIType:     req.FromPoiType,
			FromPOIID:       req.FromPoiId,
			ChooseFSearchid: req.ChooseFSearchid,

			ToLat:           req.ToLat,
			ToLng:           req.ToLng,
			ToAddress:       req.ToAddress,
			ToName:          req.ToName,
			ToPOIType:       req.ToPoiType,
			ToPOIID:         req.ToPoiId,
			ChooseTSearchid: req.ChooseTSearchid,
		}).
		SetUserOption(&models.UserOption{
			CallCarType:    0,
			CarpoolSeatNum: req.CarpoolSeatNum,
			OrderType:      int32(req.OrderType),
			DepartureTime:  deriveDepartureTimeFromDepartureRange(departureRange, departureTime).Unix(),
			DepartureRange: departureRange,
			PaymentsType:   req.PaymentsType,
			IsGuide:        req.IsGuide,
		})

	if passenger != nil {
		builder.SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(passenger.UID),
			PID:      int64(passenger.PID),
			Phone:    passenger.Phone,
			Role:     passenger.Role,
			Channel:  passenger.Channel,
			UserType: int32(req.UserType),
			OriginID: passenger.OriginId,
		})
	}

	baseReq, err := builder.TryBuild(ctx)
	if err != nil {
		return nil, BizError.ErrSystem
	}

	BuildPinCheCheFormStyleByAb(baseReq, estimateReq)

	// no-check
	productsGen, _ := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom(intercity_common.GetCaller(estimateReq.GetPageType())),
		biz_runtime.WithBaseReq(baseReq),
		biz_runtime.WithRouteInfo(req),
		biz_runtime.WithDecodeSeatDetailInfo(ctx, req.SeatDetailInfo),
		biz_runtime.WithRebookInfo(ctx, req, estimateReq.CommonData),
	)

	productsGen.SetNeedMember(true)

	if fissionOption := after_dds_fission.NewCarpoolLowPriceFission(ctx, baseReq); fissionOption != nil {
		productsGen.RegisterAfterDdsFission(fissionOption)
	}

	productsGen.RegisterBeforePriceRpcProcess(
		// 城际大车班车模式
		rpc_process.NewStationInventory(productsGen.BaseReqData.CommonInfo, &productsGen.BaseReqData.CommonBizInfo, &productsGen.BaseReqData.AreaInfo, &productsGen.BaseReqData.PassengerInfo),
	)

	productsGen.RegisterWithPriceRPCProcess(
		// 儿童票信息
		rpc_process.NewGetChildTicketRuleRPC(productsGen.BaseReqData),
	)

	productsGen.RegisterBeforePriceFilter(
		// 通过duse给的推荐路线过滤品类
		&filter.TIntercityStationFilter{},
	)

	productsGen.RegisterBeforePriceFilter(
		// 修正座位数
		before_price_filter.NewCheckSeatInfoFilter(productsGen.BaseReqData),
	)

	productsGen.RegisterBeforePriceFilter(
		// 扫码上车过滤
		before_price_filter.NewScanCodeFilter(productsGen.BaseReqData),
	)

	// 注册optionProcessPrice
	doRegisterOptionProcessPrice(productsGen)

	// 此处针对城际拼车小车anycar做一个新的距离过滤器，可以出多个三方+自营（吐槽：这里的过滤器太多了，品类耦合太厉害，需要重构了）
	if isIntercityAnycarScene(ctx, baseReq) {
		productsGen.RegisterAfterPriceFilter(
			after_price_filter.NewIntercityAnycarFilter(baseReq),
		)
	} else if req.BizSceneType == nil || *req.BizSceneType != ticket_detail_consts.SceneRebook {
		// 大巴改签不需要距离过滤
		productsGen.RegisterAfterPriceFilter(
			// 距离过滤
			filter.NewDistanceFilter(productsGen.BaseReqData),
		)
	}

	// 异步预匹配信息
	if r := rpc_process.NewCarpoolEtx(productsGen.BaseReqData, rpc_process.Sync); r != nil {
		productsGen.RegisterAfterPriceRPCProcess(r)
	}

	productsGen.RegisterAfterPriceRPCProcess(
		rpc_process.NewVcardInfo(&productsGen.BaseReqData.CommonInfo, &productsGen.BaseReqData.AreaInfo, &productsGen.BaseReqData.PassengerInfo),
	)

	tuples := []*rpc_process.FeatureReqTuple{
		{
			Domain:    ufs.DomainPassenger,
			Key:       ufs.WaitSendEmptyCarTimeKey,
			ParamKeys: []string{ufs.FeatureParamKey_EID},
			Filters: []func(*biz_runtime.ProductInfoFull) bool{
				rpc_process.ProductCategoryFilter([]int64{estimate_pc_id.EstimatePcIdLowPriceCarpool}),
			},
		},
	}

	productsGen.RegisterAfterPriceRPCProcess(
		rpc_process.NewUfsFeature(&productsGen.BaseReqData.CommonInfo, tuples),
	)

	productsGen.SetSendReqKafka(true)

	return &BizLogic{generator: productsGen}, nil
}

func isIntercityAnycarScene(ctx context.Context, baseReq *models.BaseReqData) bool {
	if baseReq.CommonInfo.PageType == page_type.PageTypeIntercityEstimate && ab.IsHitIntercityAnycarAb(ctx, cast.ToString(baseReq.PassengerInfo.PID), baseReq.GetApolloParam()) {
		return true
	}
	return false
}

func (logic *BizLogic) DoBizLogicList(ctx context.Context, req *proto.CarpoolEstimateRequest) (res *proto.IntercityEstimateData, err error) {
	products, err := logic.generator.GenProducts(ctx)
	if len(products) == 0 && req.RouteId < 200000 {
		return logic.intercityGuide(ctx, int(req.AccessKeyId)), nil
	}
	if err != nil {
		return nil, err
	}
	defer func() {
		WritePublicLog(ctx, products, req, 0)
	}()
	return logic.randerList(ctx, products), nil
}

func (logic *BizLogic) randerList(ctx context.Context, products []*biz_runtime.ProductInfoFull) *proto.IntercityEstimateData {
	if len(products) == 0 {
		return nil
	}
	for _, prod := range products {
		go seat_selection.CacheSeatInfo(ctx, logic.generator.BaseReqData, prod)
		go SyncSendMQMessageNocheck(ctx, prod)
		if carpool.IsIntercityStation(ctx, int(prod.Product.CarpoolType)) { // 大巴 -属于客企（大巴、小车）
			if prod.BaseReqData.CommonBizInfo.Scene == ticket_detail_consts.SceneRebook {
				data := intercity_station_rebook.BuildEstimateData(ctx, prod)
				if data == nil {
					continue
				}
				if card := intercity_station_rebook.BuildEstimateCardData(ctx, prod); card != nil {
					data.EstimateCardData = append(data.EstimateCardData, card)
				}
				return data
			} else {
				data := IntercityStation.BuildEstimateData(ctx, prod)
				if data == nil {
					continue
				}
				if card := IntercityStation.BuildEstimateCardData(ctx, prod); card != nil {
					data.EstimateCardData = append(data.EstimateCardData, card)
				}
				return data
			}
		} else if carpool.IsInterCityCarpool(prod.Product.CarpoolType, prod.Product.ComboType) && intercity_sku.IsSkuModel(prod) {
			data := intercity_sku.BuildOutSideEstimateData(ctx, prod)
			if data == nil {
				continue
			}
			if card := intercity_sku.BuildEstimateData(ctx, prod); card != nil {
				data.EstimateCardData = append(data.EstimateCardData, card)
			}
			return data
		} else if carpool.IsInterCityCarpool(prod.Product.CarpoolType, prod.Product.ComboType) {
			data := intercity_new.BuildOutSideEstimateData(ctx, prod)
			if data == nil {
				continue
			}
			if card := intercity_new.BuildEstimateData(ctx, prod); card != nil {
				data.EstimateCardData = append(data.EstimateCardData, card)
			}
			return data
		}

	}
	return nil
}

func (logic *BizLogic) check(ctx context.Context, products []*biz_runtime.ProductInfoFull) error {
	apolloUser := func(prod *biz_runtime.ProductInfoFull) *ApolloModel.User {
		passengerID := prod.GetUserPID()
		return ApolloModel.NewUser(strconv.FormatInt(passengerID, 10)).
			With("city", strconv.Itoa(prod.GetCityID())).
			With("phone", prod.GetUserPhone()).
			With("access_key_id", strconv.Itoa(int(prod.GetAccessKeyId()))).
			With("app_version", prod.GetAppVersion()).
			With("uid", strconv.Itoa(int(prod.GetUID()))).
			With("pid", strconv.FormatInt(passengerID, 10))
	}

	// 拼成乐
	if len(products) == 1 && products[0] != nil && carpool.IsPinCheChe(products[0].GetProductCategory()) {
		prod := products[0]
		// 延长运营时间: 如果夜间因为跨城或者距离过滤，判断预估的时间片是否是夜间，是的话说明是二次预估时间片跳变导致，需要报错处理
		if util2.GetNightCarpoolStateApollo(apolloUser(prod)) == "1" {
			isOriginNight := func() bool {
				departureTime := logic.generator.BaseReqData.CommonInfo.DepartureTime
				if departureTime == 0 && len(logic.generator.BaseReqData.CommonInfo.DepartureRange) == 2 {
					departureTime = logic.generator.BaseReqData.CommonInfo.DepartureRange[0]
				}
				return util2.IsNight(time.Unix(departureTime, 0))
			}

			isCurInvalid := func() bool {
				if prod.GetBillDriverMetre() > 30000 {
					return true
				}
				if prod.GetCityID() != prod.GetToCityID() {
					return true
				}
				return false
			}

			if isOriginNight() && isCurInvalid() {
				return consts.ErrorDDSTimeSliceNotMatch
			}
		}
	}

	return nil
}

func BuildPinCheCheFormStyleByAb(baseReqData *models.BaseReqData, estimateReq *param_handler.EstimateRequest) {
	if baseReqData == nil {
		return
	}

	if baseReqData.CommonInfo.PageType != page_type.PageTypeLowCarpoolEstimate {
		return
	}

	// 兜底dchn，从trans_data中获取
	if baseReqData.CommonInfo.Dchn == "" {
		baseReqData.CommonInfo.Dchn = estimateReq.GetDchn()
	}

	apoParams := baseReqData.GetApolloParam()
	apoParams["dchn"] = baseReqData.CommonInfo.Dchn
	// 灰度 + dchn；通过后展示新样式
	isAllow, expParams := apollo.GetParameters("pincheche_new_booking_style_toggle", strconv.FormatInt(baseReqData.PassengerInfo.PID, 10), apoParams)
	if !isAllow {
		return
	}

	baseReqData.CommonBizInfo.PinCheCheFormInfo.FormSytle = 1
	// 默认锚定模块： 1预约出发 or 0现在出发
	if orderType, ok := expParams["type"]; ok {
		t, err := strconv.ParseInt(orderType, 10, 64)
		if err == nil {
			baseReqData.CommonBizInfo.PinCheCheFormInfo.DefaultOrderType = t
		}
	}

	return
}

func doRegisterOptionProcessPrice(productsGen *biz_runtime.ProductsGenerator) {
	// 按优先级处理 OptionProcessPrice ，避免覆盖
	// 拼车车
	if priceOption := option_gen_price.NewCarpoolLowPriceEstimateOpt(productsGen.BaseReqData); priceOption != nil {
		productsGen.RegisterOptionProcessPrice(priceOption)
		return
	}

	// 优待票改签
	if priceOption := option_gen_price.NewStationBusCouponTicketRebookEstimate(productsGen.BaseReqData); priceOption != nil {
		productsGen.RegisterOptionProcessPrice(priceOption)
	}
}

// 城际品类anycar渲染拓展逻辑，本方法本次只for城际，未来可以拓展其他品类
func (logic *BizLogic) renderIntercityAnycar(ctx context.Context, products []*biz_runtime.ProductInfoFull) (resp *proto.CarpoolEstimateResponseData, ok bool) {
	// 如果非城际小车页、没有品类，直接返回
	baseReq := logic.generator.BaseReqData
	if baseReq.CommonInfo.PageType != page_type.PageTypeIntercityEstimate || len(products) == 0 {
		return resp, ok
	}
	if !ab.IsHitIntercityAnycarAb(ctx, cast.ToString(baseReq.PassengerInfo.PID), baseReq.GetApolloParam()) {
		return resp, ok
	}
	resp = &proto.CarpoolEstimateResponseData{
		TabType:         proto.TabType_InterCityAnycar,
		EstimateTraceId: util.GetTraceIDFromCtxWithoutCheck(ctx),
	}
	renderLogic := intercity_anycar.RenderPage{}
	renderLogic.Init(ctx)
	renderLogic.RenderProducts(ctx, products, resp)
	renderLogic.RenderExtraData(ctx, baseReq, products, resp)
	ok = true
	return resp, ok
}
