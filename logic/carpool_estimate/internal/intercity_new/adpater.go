package intercity_new

import (
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_sku"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"

	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

// AdapterInterCity 城际表单渲染适配
type AdapterInterCity struct {
	*biz_runtime.ProductInfoFull
}

func (inter *AdapterInterCity) GetAccessKeyID() int {
	return int(inter.GetClientInfo().AccessKeyID)
}

func (inter *AdapterInterCity) GetTimeSpan() []*EstimateDecision.TimeSpanV2 {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil || len(bizInfo.TimeSpan) == 0 {
		return nil
	}
	return bizInfo.TimeSpan
}

func (inter *AdapterInterCity) GetEstimateFeeAmount() float64 {
	fee := inter.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	return fee.GetFee()
}

func (inter *AdapterInterCity) GetBusinessPayAmount() float64 {
	fee := inter.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}

	// MAYBUG: 某一品类的企业支付抵扣金额, 应该在前面做判断, 不应该在UI层
	paymentType := inter.GetCurrentPaymentType()
	if consts.BusinessPaymentType == paymentType {
		return fee.GetFee()
	}

	return 0
}
func (inter *AdapterInterCity) GetBonus() float64 {
	fee := inter.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (inter *AdapterInterCity) GetCoupon() *intercity_sku.FeeItem {
	fee := inter.GetDirectEstimatePrice()
	if fee == nil {
		return nil
	}
	coupon := fee.GetFeeDetail().GetCoupon()
	if coupon == nil {
		return nil
	}
	return &intercity_sku.FeeItem{
		Tag:    coupon.Tag,
		Amount: coupon.Amount,
	}
}

func (inter *AdapterInterCity) GetMaxSeatNum() int32 {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.MaxCarpoolSeatNum
}
func (inter *AdapterInterCity) GetUserSelectSeatNum() int32 {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.CarpoolSeatNum
}

func (inter *AdapterInterCity) GetAllFormattedPaymentOption() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := inter.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (inter *AdapterInterCity) GetCurrentPaymentType() int {
	opts := inter.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (inter *AdapterInterCity) GetSkuInfo() map[string]struct { // 库存模式信息
	Status int32
	Seat   int
} {
	if inter.GetBizInfo() == nil {
		return nil
	}

	return inter.GetBizInfo().IntercitySkuInfo
}

func (inter *AdapterInterCity) GetDepartureRangeStr() string {
	departureRange := inter.GetUserOption().DepartureRange
	return "[" + strconv.Itoa(int(departureRange.From.Unix())) + "," + strconv.Itoa(int(departureRange.To.Unix())) + "]"
}

func (inter *AdapterInterCity) GetVersion() string {
	return inter.GetClientInfo().AppVersion
}

func (inter *AdapterInterCity) GetAllPaymentOptions() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := inter.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (inter *AdapterInterCity) GetRouteGroup() int64 {
	if inter.GetBizInfo() == nil {
		return 0
	}
	routeGroup := inter.GetBizInfo().RouteInfo.RouteGroup
	if routeGroup == nil {
		return 0
	}
	return *routeGroup
}

func (inter *AdapterInterCity) GetAgentType() string {
	if inter.BaseReqData == nil {
		return ""
	}
	return inter.BaseReqData.CommonInfo.IsScanCode
}

func (inter *AdapterInterCity) GetScene() string {
	if inter == nil || inter.BaseReqData == nil {
		return ""
	}
	return inter.BaseReqData.CommonBizInfo.Scene
}

func (inter *AdapterInterCity) GetRebookServiceCharge() float64 {
	if inter == nil {
		return 0
	}

	if inter.ProductInfoFull != nil {
		if rebookServiceFee := inter.ProductInfoFull.GetRebookServiceFee(); rebookServiceFee > 0 {
			return rebookServiceFee
		}
	}

	fees := inter.GetBillFeeDetailInfo()
	if fees == nil || len(fees) == 0 {
		return 0
	}
	if amount, ok := fees["rebook_service_charge"]; ok && amount > 0 {
		return amount
	}

	return 0
}

func (inter *AdapterInterCity) GetSelectedBusServiceShiftId() string {
	if inter == nil || inter.BaseReqData == nil {
		return ""
	}

	return inter.BaseReqData.CommonBizInfo.SeletedBusServiceShiftId
}
