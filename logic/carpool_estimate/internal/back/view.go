package back

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

const (
	CarpoolTab string = "carpool"
)

type Tab struct {
	TabID         string `json:"tab_id"`
	DefaultSelect bool   `json:"default_select"`
}

// BuildBackInfo render back button
func BuildBackInfo(ctx context.Context, productInfo *biz_runtime.ProductInfoFull) *proto.BackButton {
	template := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-back_button")
	backButton := &proto.BackButton{
		Text:                gjson.Get(template, "text").String(),
		TextColor:           gjson.Get(template, "text_color").String(),
		LeftIcon:            gjson.Get(template, "left_icon").String(),
		BorderColor:         gjson.Get(template, "border_color").String(),
		JumpTo:              gjson.Get(template, "jump_to").String(),
		BackgroundGradients: []string{gjson.Get(template, "start_bg_gradient").String(), gjson.Get(template, "end_bg_gradient").String()},
	}

	tabList := getTabList(productInfo.BaseReqData.CommonInfo.TabList)
	if tabList != nil {
		for _, v := range tabList {
			if v.TabID != CarpoolTab {
				backButton.JumpTo = v.TabID
				break
			}
		}
	}

	return backButton
}

func getTabList(tabListStr string) []Tab {
	tabList := []Tab{}
	if tabListStr == "" {
		return tabList
	}
	_ = json.Unmarshal([]byte(tabListStr), &tabList)
	return tabList
}
