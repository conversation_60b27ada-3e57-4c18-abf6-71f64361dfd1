package pincheche

import (
	"context"
	"encoding/json"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/pincheche"
	"git.xiaojukeji.com/nuwa/trace"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

// 静态检查
var (
	_ pincheche.ApolloParamProvider       = &ViewAdapter{}
	_ pincheche.IntroMsgProvider          = &ViewAdapter{}
	_ pincheche.ExtraIntroTagProvider     = &ViewAdapter{}
	_ pincheche.CarpoolBookingProvider    = &ViewAdapter{}
	_ pincheche.ExtraMapProvider          = &ViewAdapter{}
	_ pincheche.PriceDescProvider         = &EstimateFee{}
	_ pincheche.CarpoolSeatModuleProvider = &ViewAdapter{}
	_ pincheche.SpecialPriceProvider      = &SpecialPrice{}
	_ pincheche.ExtraPriceTagProvider     = &ExtraPrice{}
)

const CappedCouponType = "123" //封顶券

type ViewAdapter struct {
	*biz_runtime.ProductInfoFull
}

func (p *ViewAdapter) ApolloParam() *ApolloModel.User {
	passengerID := p.GetUserInfo().PID
	return ApolloModel.NewUser(strconv.FormatInt(passengerID, 10)).
		With("city", strconv.Itoa(int(p.GetAreaInfo().City))).
		With("phone", p.GetUserInfo().Phone).
		With("access_key_id", strconv.Itoa(int(p.GetClientInfo().AccessKeyID))).
		With("app_version", p.GetAppVersion()).
		With("uid", strconv.FormatInt(p.GetUserInfo().UID, 10)).
		With("pid", strconv.FormatInt(passengerID, 10))
}

func (p *ViewAdapter) IsBookingOrder() bool {
	return p.GetOrderType() == 1
}

func (p *ViewAdapter) IsHitHotRoute() bool {
	biz := p.GetBizInfo()
	if biz == nil || biz.CarpoolRouteInfo == nil {
		return false
	}
	return true
}

func (p *ViewAdapter) GetCommuteCard() *pincheche.CommuteCard {
	biz := p.GetBizInfo()
	if biz == nil {
		return nil
	}

	if card := biz.CarpoolCommuteCard; card != nil {
		return &pincheche.CommuteCard{
			ID: card.CardID,
		}
	}
	return nil
}

func (p *ViewAdapter) GetDepartureTime() time.Time {
	biz := p.GetBizInfo()
	if biz == nil || len(biz.DepartureRange) != 2 {
		return time.Now()
	}
	return time.Unix(biz.DepartureRange[1], 0)
}

func (p *ViewAdapter) GetCurSeatNum() int32 {
	biz := p.GetBizInfo()
	return biz.CarpoolSeatNum
}

func (p *ViewAdapter) GetToCityId() int32 {
	req := p.BaseReqData
	return req.AreaInfo.ToArea
}

func (p *ViewAdapter) GetDriverMetre() int64 {
	bill := p.GetBillDetail()
	return bill.DriverMetre
}

func (p *ViewAdapter) GetTimeSpan() []*EstimateDecision.TimeSpanV2 {
	biz := p.GetBizInfo()
	if biz == nil {
		return nil
	}
	return biz.TimeSpan
}

func (p *ViewAdapter) IsFission() bool {
	biz := p.GetBizInfo()
	if biz == nil {
		return false
	}
	return biz.IsFission
}

func (p *ViewAdapter) IsPinchecheNewForm() bool {
	return p.GetCommonBizInfo().PinCheCheFormInfo.FormSytle == 1
}

type EstimateFee struct {
	amount       float64
	discount     float64
	poolNum      int
	payment      int
	couponInfo   *PriceApi.EstimateNewFormCouponInfo
	redPacketFee float64
}

func (t *EstimateFee) GetRedPacketFee() float64 {
	return t.redPacketFee
}

func (t *EstimateFee) GetEstimateFeeAmount() float64 {

	return t.amount
}
func (t *EstimateFee) GetDiscountFeeAmount() float64 {
	return t.discount
}
func (t *EstimateFee) GetExpectPoolNum() int {
	return t.poolNum
}
func (t *EstimateFee) GetPaymentType() int {
	return t.payment
}
func (t *EstimateFee) GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	return t.couponInfo
}
func (p *ViewAdapter) MultiPrice() []pincheche.PriceDescProvider {
	prices := p.GetSceneEstimatePrice()
	result := make([]pincheche.PriceDescProvider, 0, len(prices))
	for _, price := range prices {
		poolNum := price.GetFeeAttributes().GetInt("pool_num")
		if poolNum == nil || *poolNum == 0 {
			continue
		}
		coupon := p.GetMultiPriceCouponInfoByScene(map[string]string{
			"pool_num":           strconv.FormatInt(*poolNum, 10),
			"seat_num":           strconv.Itoa(p.GetCarpoolSeatCurrentNum()),
			"is_carpool_success": "1",
		})
		tmp := &EstimateFee{
			amount:       price.GetFee(),
			discount:     price.GetFeeDetail().GetTotalFeeWithoutAnyDiscount() - price.GetFee(),
			poolNum:      int(*poolNum),
			payment:      int(price.GetPayment().PayType),
			couponInfo:   coupon, //newline
			redPacketFee: p.RedPacketFee(price.GetFeeDetail().GetFeeDetailInfo()),
		}
		result = append(result, tmp)
	}
	// MUST NOT RETURN NIL
	return result
}

type ExtraPrice struct {
	amount      float64
	poolNum     int
	dcExtraInfo string
	displayTag  string
	customTag   string
}

func (t *ExtraPrice) GetEstimateFeeAmount() float64 {
	return t.amount
}
func (t *ExtraPrice) GetExpectPoolNum() int {
	return t.poolNum
}
func (t *ExtraPrice) GetDcExtraInfo() string {
	return t.dcExtraInfo
}

func (t *ExtraPrice) GetDisplayTag() string {
	return t.displayTag
}

func (t *ExtraPrice) GetCustomTag() string {
	return t.customTag
}

func (p *ViewAdapter) ExtraPriceTag(ctx context.Context) []pincheche.ExtraPriceTagProvider {
	prices := p.GetSceneEstimatePrice()
	result := make([]pincheche.ExtraPriceTagProvider, 0, len(prices))
	for _, price := range prices {
		tmp := &ExtraPrice{
			amount: price.GetFee(),
		}

		poolNum := price.GetFeeAttributes().GetInt("pool_num")
		if poolNum == nil || *poolNum == 0 {
			continue
		}
		tmp.poolNum = int(*poolNum)

		if dcExtraJSON := price.GetFeeAttributes().GetString("dc_extra_json"); dcExtraJSON != nil {
			tmp.dcExtraInfo = *dcExtraJSON
		}

		var hasDiscount = false
		// 2. sps活动
		if p.GetBillInfo() != nil && p.GetBillInfo().DisplayLines != nil {
			for _, line := range p.GetBillInfo().DisplayLines {

				if line == nil || line.ExtraInfo == nil || line.ExtraInfo["discounts_display"] == nil || len(line.ExtraInfo["discounts_display"].(string)) <= 0 ||
					line.ExtraInfo["carpool_succ_two_seat_cap_price"] == nil || line.ExtraInfo["carpool_succ_two_seat_cap_price"] == 0 {
					continue
				}
				discountDisplay := map[string]interface{}{}
				if err := json.Unmarshal([]byte(line.ExtraInfo["discounts_display"].(string)), &discountDisplay); err == nil {
					if discountDisplay["display_tag"] == nil {
						continue
					}
					var twoSeatPrice float64
					if err := json.Unmarshal([]byte(line.ExtraInfo["carpool_succ_two_seat_cap_price"].(json.Number)), &twoSeatPrice); err == nil {
						if tmp.amount <= twoSeatPrice {
							tmp.displayTag = discountDisplay["display_tag"].(string)
						} else {
							hasDiscount = true
							log.Trace.Infof(ctx, trace.DLTagUndefined, "display_tag filtered because of sps price")
						}
					} else {
						log.Trace.Warnf(ctx, trace.DLTagUndefined, "_msg=unmarshal carpool_succ_two_seat_cap_price err:%v", err)
					}

				} else {
					log.Trace.Warnf(ctx, trace.DLTagUndefined, "_msg=unmarshal discounts_display err:%v", err)
				}
				break
			}
		}

		// 3. 封顶券（displayTag优先级高于sps活动）
		coupon := p.GetCarpoolFailedCouponFromRawData()
		if coupon != nil && len(coupon.CouponType) > 0 && coupon.CouponAmount != nil {
			if coupon.CouponType == CappedCouponType {
				amountStr, _ := strconv.ParseFloat(*coupon.CouponAmount, 64)
				amount := amountStr / 100
				if amount > 0 && tmp.amount <= amount {
					tag := ""
					apolloParams := map[string]string{}
					apolloParams["coupon_type"] = coupon.CouponType
					if ok, toggle := apollo.GetParameters("coupon_tag_by_coupon_type", "", apolloParams); ok {
						tag = toggle["coupon_tag"]
					} else {
						tag = toggle["default_tag"]
						log.Trace.Infof(ctx, trace.DLTagUndefined, "display_tag filtered because of toggle:coupon_tag_by_coupon_type ")
					}
					tmp.displayTag = tag
				} else {
					hasDiscount = true
					log.Trace.Infof(ctx, trace.DLTagUndefined, "display_tag filtered because of coupon amount")
				}
			}
		}

		if hasDiscount {
			tmp.displayTag = dcmp.GetDcmpContent(ctx, "carpool_tab_pincheche-capped_discounts_default_tag", nil)
		}

		result = append(result, tmp)
	}
	// MUST NOT RETURN NIL
	return result
}

func (p *ViewAdapter) GetEstimateFeeAmountWhenTwoComrade() *float64 {
	prices := p.GetSceneEstimatePrice()
	amount := 0.0
	for _, price := range prices {
		poolNum := price.GetFeeAttributes().GetInt("pool_num")
		if poolNum == nil || *poolNum != 2 {
			continue
		}
		amount = price.GetFee()
		return &amount
	}
	return nil
}

type SpecialPrice struct {
	typ    pincheche.ExtraPriceType
	amount float64
}

func (t *SpecialPrice) SpecialPriceType() pincheche.ExtraPriceType {
	return t.typ
}
func (t *SpecialPrice) SpecialPriceAmount() float64 {
	return t.amount
}

func (p *ViewAdapter) ExtraPrice(ctx context.Context) *SpecialPrice {
	_prices := p.GetSceneEstimatePrice()
	if len(_prices) == 0 {
		return &SpecialPrice{
			typ: pincheche.ExtraPriceType_None,
		}
	}

	amount := 0.0

	for _, _price := range _prices {
		if redPacketAmount := p.RedPacketFee(_price.GetFeeDetail().GetFeeDetailInfo()); redPacketAmount > 0 {
			amount = redPacketAmount
		}
	}

	if amount > 0 {
		var seatNum int32 = 1
		if p.BaseReqData.CommonBizInfo.CarpoolSeatNum > 0 {
			seatNum = p.BaseReqData.CommonBizInfo.CarpoolSeatNum
		}

		if seatNum <= 1 {
			// 未拼成展示均含
			return &SpecialPrice{
				typ:    pincheche.ExtraPriceType_MultiSpringFestival,
				amount: amount,
			}
		} else if seatNum > 1 {
			return &SpecialPrice{
				typ:    pincheche.ExtraPriceType_SpringFestival,
				amount: amount,
			}
		}
	}

	return &SpecialPrice{
		typ: pincheche.ExtraPriceType_None,
	}
}

func (p *ViewAdapter) RedPacketFee(feeDetailInfo map[string]float64) float64 {
	if !apollo.FeatureToggle(nil, "gs_holiday_fee_fee_desc", strconv.Itoa(int(p.GetUID())), p.ApolloParam().GetProperties()) {
		return 0
	}

	if value, ok := feeDetailInfo["red_packet"]; ok {
		return value
	}

	return 0
}

func (p *ViewAdapter) GetCarpoolSeatMaxNum() int {
	return 2
}

func (p *ViewAdapter) GetCarpoolSeatCurrentNum() int {
	biz := p.GetBizInfo()
	if biz == nil {
		return 1
	}
	return int(biz.CarpoolSeatNum)
}

func (p *ViewAdapter) GetDepartureRange() []int64 {
	biz := p.GetBizInfo()
	if biz == nil || len(biz.DepartureRange) != 2 {
		return nil
	}
	return biz.DepartureRange
}

func (p *ViewAdapter) NewOrderParam() *pincheche.ProductNewOrderParam {
	intNilToZero := func(i *int64) int64 {
		if i == nil {
			return 0
		}
		return *i
	}

	fees := p.GetSceneEstimatePrice()
	if len(fees) == 0 {

		return nil
	}
	attr := fees[0].GetFeeAttributes()

	return &pincheche.ProductNewOrderParam{
		BusinessId:      int32(p.Product.BusinessID),
		RequireLevel:    int32(p.Product.RequireLevelInt),
		ComboType:       int32(p.Product.ComboType),
		ProductCategory: int32(p.GetProductCategory()),
		LevelType:       p.Product.LevelType,
		CountPriceType:  int32(intNilToZero(attr.GetInt("count_price_type"))),
		PageType:        p.BaseReqData.CommonInfo.PageType,
	}
}

func (p *ViewAdapter) GetVcardData() *models.VcardResult {
	return p.Product.BizInfo.VcardData
}

func (p *ViewAdapter) GetWaitSendEmptyCarTime(EID string) int64 {
	features := p.GetUfsFeatures()
	if features == nil {
		return 0
	}

	key := ufs.GenFeatureKey(
		ufs.GenFeature(ufs.DomainPassenger, ufs.WaitSendEmptyCarTimeKey, map[string]string{ufs.FeatureParamKey_EID: EID}),
	)

	if strVal, ok := features[key]; ok && strVal != "" {
		if intVal, err := strconv.ParseInt(strVal, 10, 64); err == nil {
			return intVal
		}
	}

	return 0
}

func (p *ViewAdapter) GetDiscountCard() *PriceApi.DiscountCard {
	discSet := p.GetDiscountSet()
	if discSet == nil {
		return nil
	}

	return discSet.DiscountCard
}
