package intercity_sku

import (
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

func IsSkuModel(prod *biz_runtime.ProductInfoFull) bool {
	routeGroup := prod.GetBizInfo().RouteInfo.RouteGroup
	param := ApolloModel.NewUser("").
		With("city", strconv.Itoa(int(prod.GetAreaInfo().City))).
		With("phone", prod.GetUserInfo().Phone).
		With("route_group", strconv.Itoa(int(*routeGroup))).
		With("access_key_id", strconv.Itoa(int(prod.GetClientInfo().AccessKeyID))).
		With("app_version", prod.GetAppVersion()).
		With("source", "mamba")
	toggle, err := ApolloSDK.FeatureToggle("intercity_carpool_sku_model_grayscale", param)
	//后续城际自营新开城的库存模式判断统一使用dds返回的is_sku_model
	if (err == nil && toggle.IsAllow()) || biz_runtime.RenderSelfSupportSkuMode(prod) {
		return true
	}
	return false
}
