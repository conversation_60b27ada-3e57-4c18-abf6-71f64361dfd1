package intercity_station

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_common"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_sku"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_station"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_seat_module_with_children_ticket"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/support_select_seat"
)

type RenderPage struct {
}

func (r *RenderPage) SetTabType(data *proto.CarpoolEstimateResponseData) {
	data.TabType = proto.TabType_InterCitySkuStation
}

func (r *RenderPage) RenderPageData(ctx context.Context, product *biz_runtime.ProductInfoFull, res *proto.CarpoolEstimateResponseData) {
	if product == nil || product.Product == nil {
		return
	}
	prod := &intercity_common.AdapterInterCity{ProductInfoFull: product}
	var (
		remainSeatsMap map[string]int32
		businessName   = car_info.GetBusinessName(ctx, product)
	)

	skuStation := &proto.IntercitySkuStationData{}
	skuStation = initEstimateData(product)
	skuStation.CarIcon = dcmp.GetDcmpContent(ctx, "intercity_station-car_icon", nil)
	skuStation.SubTitle = car_info.SubTitle(ctx, prod, businessName)
	skuStation.SubIcon = car_info.SubIcon(ctx, prod)
	skuStation.IntroImage = dcmp.GetDcmpContent(ctx, "intercity_station-intro_image", nil)
	skuStation.RightIcon = dcmp.GetDcmpContent(ctx, "intercity_station-right_icon", nil)
	skuStation.RightTag = dcmp.GetDcmpContent(ctx, "intercity_station-right_tag", nil)
	skuStation.FeeMsg = fee_info_render.FeeMsg(ctx, prod)
	skuStation.FeeDescList = intercity_sku.FeeDescList(ctx, prod, prod)
	skuStation.UserPayInfo = payment.UserPayInfo(ctx, prod)
	skuStation.FeeDetailUrl = fee_detail_info.GetDetailUrlV2(ctx, prod)
	skuStation.CarpoolSeatModule = carpool_seat_module_with_children_ticket.NewCarpoolSeatModule(product).GetCarpoolSeatModule(ctx, prod)
	skuStation.ConfirmButton = intercity_station.ConfirmButton(ctx, prod)
	skuStation.ConfirmSubText = dcmp.GetDcmpContent(ctx, "intercity_station-confirm_sub_text", nil)
	skuStation.StationInfo = intercity_station.StationInfo(ctx, prod)
	skuStation.CarpoolBooking, remainSeatsMap = intercity_station.CarpoolBooking(ctx, prod)
	skuStation.CarpoolBookingOuter = intercity_station.CarpoolBookingOuter(ctx, prod, skuStation.CarpoolBooking, remainSeatsMap)
	skuStation.SupportSelectSeat = support_select_seat.GetSupportSelectSeat(ctx, product)
	res.IntercitySkuStation = skuStation
	return

}

func (r *RenderPage) BuildExTraData(ctx context.Context, req *models.BaseReqData, p *biz_runtime.ProductInfoFull, res *proto.CarpoolEstimateResponseData) {
	if res == nil {
		res = &proto.CarpoolEstimateResponseData{}
	}

	if req != nil && req.CommonBizInfo.IsRestSeatInfo {
		toast := dcmp.GetDcmpPlainContent(ctx, "intercity_sku-seat_reset")
		res.ForceNoticeToast = &toast
	}
}

func initEstimateData(product *biz_runtime.ProductInfoFull) *proto.IntercitySkuStationData {
	data := &proto.IntercitySkuStationData{
		EstimateId: product.Product.EstimateID,
		ExtraMap: &proto.IntercityNewOrderParam{
			ProductCategory: product.Product.ProductCategory,
			ComboType:       product.Product.ComboType,
			RequireLevel:    product.Product.RequireLevelInt,
			BusinessId:      product.Product.BusinessID,
			PageType:        product.BaseReqData.CommonInfo.PageType,
			RouteType:       int32(product.Product.RouteType),
		},
	}
	if product.Product.BizInfo != nil {
		data.ExtraMap.ComboId = product.Product.BizInfo.ComboID
	}
	return data
}
