package intercity_anycar

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/back"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_anycar"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_sku"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_booking_time_module"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_seat_module_with_children_ticket"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
	"github.com/spf13/cast"
	"sort"
	"time"
)

const (
	// IntercityAnycarPageDetailKey 城际拼车dcmp的物料配置
	IntercityAnycarPageDetailKey = "intercity_anycar-page_detail"
	//
	ApolloNamespaceIntercitySelfConf = "intercity_self_conf"
	ApolloNameIntercitySelfMaterial  = "intercity_self_material"
	LogTag                           = "intercity_anycar"
)

type RenderPage struct {
	PageDetail *IntercityAnycarPageDetail
}

type IntercityAnycarPageDetail struct {
	CarIcon                   string `json:"car_icon"`
	SubTitle                  string `json:"sub_title"`
	IntroImage                string `json:"intro_image"`
	ConfirmButton             string `json:"confirm_button"`
	BorderColor               string `json:"border_color"`
	ServiceInfoTitle          string `json:"service_info_title"`
	ServiceInfoSubTitle       string `json:"service_info_sub_title"`
	ServiceInfoDisableToast   string `json:"service_info_disable_toast"`
	FeeMsg                    string `json:"fee_msg"`
	FeeDetailUrl              string `json:"fee_detail_url"`
	IntercitySelfProductTitle string `json:"intercity_self_product_title"`
	IntercitySelfProductIcon  string `json:"intercity_self_product_icon"`
	SubTitleSelfContent       string `json:"sub_title_self_content"`
	SubTitleThirdContent      string `json:"sub_title_third_content"`
	SubTitleFontColor         string `json:"sub_title_font_color"`
	SubTitleBorderColor       string `json:"sub_title_border_color"`
}

func (r *RenderPage) Init(ctx context.Context) {
	dcmpPageDetail := dcmp.GetDcmpContent(ctx, IntercityAnycarPageDetailKey, nil)
	pageDetail := &IntercityAnycarPageDetail{}
	err := json.Unmarshal([]byte(dcmpPageDetail), pageDetail)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "Unmarshal dcmp intercity_anycar-page-detail fail, err: %v", err)
	}
	materials := apollo.GetConfig(ctx, ApolloNamespaceIntercitySelfConf, ApolloNameIntercitySelfMaterial)
	pageDetail.IntercitySelfProductTitle = materials["product_title"]
	pageDetail.IntercitySelfProductIcon = materials["product_icon"]
	r.PageDetail = pageDetail
}

// RenderProducts 渲染品类信息，价格等
func (r *RenderPage) RenderProducts(ctx context.Context, products []*biz_runtime.ProductInfoFull, resp *proto.CarpoolEstimateResponseData) {
	data := &proto.IntercitySkuStationData{}
	serviceInfo := &proto.IntercityServiceInfo{}
	estimateInfo := &proto.IntercityEstimateInfo{}
	estimateInfo.EstimateData = make(map[int64]*proto.IntercityAnycarEstimateData, len(products))
	estimateInfo.Layout = make([]*proto.NewFormLayout, 0, len(products))
	sort.Slice(products, func(i, j int) bool {
		pi, pj := &AdapterIntercity{products[i]}, &AdapterIntercity{products[j]}
		return pi.GetEstimateFeeAmount() < pj.GetEstimateFeeAmount()
	})
	for _, product := range products {
		pc, eData, group := r.buildSingleProduct(ctx, product)
		estimateInfo.EstimateData[pc] = eData
		estimateInfo.Layout = append(estimateInfo.Layout, group)
	}
	serviceInfo.EstimateInfo = estimateInfo
	serviceInfo.Title = fmt.Sprintf(r.PageDetail.ServiceInfoTitle, len(products))
	serviceInfo.SubTitle = r.PageDetail.ServiceInfoSubTitle
	serviceInfo.DisableToast = r.PageDetail.ServiceInfoDisableToast
	data.ServiceInfo = serviceInfo
	// 每个品类的时间片都是相同的，随便拿一个就行
	prod := &AdapterIntercity{products[0]}
	data.CarpoolBooking = carpool_booking_time_module.IntercityCarpoolBookingTimeAnycar(ctx, prod)
	data.CarpoolBookingOuter = intercity_sku.CarpoolBookingOuter(ctx, prod, data.CarpoolBooking)
	resp.IntercitySku = data
}

func (r *RenderPage) buildSingleProduct(ctx context.Context, product *biz_runtime.ProductInfoFull) (int64, *proto.IntercityAnycarEstimateData, *proto.NewFormLayout) {
	prod := &AdapterIntercity{product}
	// 获取标题和图片
	var title, icon, subTitleContent string
	if prod.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolInter {
		title = r.PageDetail.IntercitySelfProductTitle
		icon = r.PageDetail.IntercitySelfProductIcon
		subTitleContent = r.PageDetail.SubTitleSelfContent
	} else {
		title, icon = car_info.GetEstimateNameAndIcon(ctx, prod)
		subTitleContent = r.PageDetail.SubTitleThirdContent
	}
	eData := &proto.IntercityAnycarEstimateData{
		EstimateId:      product.GetEstimateID(),
		ProductCategory: product.GetProductCategory(),
		CarTitle:        title,
		CarIcon:         icon,
		FeeAmount:       cast.ToString(prod.GetEstimateFeeAmount()),
		FeeMsg:          fee_info_render.FeeMsg(ctx, prod),
		FeeDescList:     intercity_anycar.FeeDescList(ctx, prod, prod),
		ExtraMap: &proto.IntercityNewOrderParam{
			ProductCategory: product.Product.ProductCategory,
			ComboType:       product.Product.ComboType,
			RequireLevel:    product.Product.RequireLevelInt,
			BusinessId:      product.Product.BusinessID,
			PageType:        product.BaseReqData.CommonInfo.PageType,
			RouteType:       int32(product.Product.RouteType),
		},
		SideExtra: &proto.IntercityAnycarSideExtra{
			SubTitleList: []*proto.GroupSubTitle{{
				Content:     subTitleContent,
				FontColor:   r.PageDetail.SubTitleFontColor,
				BorderColor: r.PageDetail.SubTitleBorderColor,
			}},
		},
		IsSelected: 1,
	}
	if product.Product.BizInfo != nil {
		eData.ExtraMap.ComboId = product.Product.BizInfo.ComboID
	}
	// 这里的layout只给端上复用表单组件使用
	group := &proto.NewFormLayout{
		Groups: []*proto.NewFormGroup{{
			Type:     1,
			Products: []string{cast.ToString(product.GetProductCategory())},
		}},
	}
	return product.GetProductCategory(), eData, group
}

// RenderExtraData 渲染标题，副标题，文案等
func (r *RenderPage) RenderExtraData(ctx context.Context, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull, resp *proto.CarpoolEstimateResponseData) {
	prods := make([]*AdapterIntercity, 0, 0)
	for _, product := range products {
		prods = append(prods, &AdapterIntercity{product})
	}
	resp.IntercitySku.CarIcon = r.PageDetail.CarIcon
	resp.IntercitySku.SubTitle = r.PageDetail.SubTitle
	resp.IntercitySku.IntroImage = r.PageDetail.IntroImage
	// 如果是多个品类，座位数取最大的
	var seatModule *proto.InterCityCarpoolSeatModule
	for i, prod := range prods {
		curSeatModule := carpool_seat_module_with_children_ticket.NewCarpoolSeatModule(products[i]).GetCarpoolSeatModule(ctx, prod)
		if seatModule == nil || (curSeatModule != nil && curSeatModule.MaxCount > seatModule.MaxCount) {
			seatModule = curSeatModule
		}
	}
	resp.IntercitySku.CarpoolSeatModule = seatModule
	resp.IntercitySku.ConfirmButton = r.PageDetail.ConfirmButton
	resp.IntercitySku.UserPayInfo = payment.UserPayInfo(ctx, prods[0])
	// 本次没有儿童票等逻辑，此字段赋1即可，如有需要，逻辑可以参考 support_select_seat.GetSupportSelectSeat 方法
	resp.IntercitySku.SupportSelectSeat = 0
	resp.IntercitySku.BorderColor = r.PageDetail.BorderColor
	resp.IntercitySku.FeeMsg = r.PageDetail.FeeMsg
	resp.IntercitySku.FeeDetailUrl = r.PageDetail.FeeDetailUrl
	// 选择当前时间片的时候支持多选，否则不支持，改anycar缩需要的字段
	if baseReqData.CommonInfo.DepartureRange == nil || util.IsCurrentTimeDepartureRange(baseReqData.CommonInfo.DepartureRange, time.Now()) {
		resp.IntercitySku.IsSupportMultiSelection = proto.Int32Ptr(1)
	} else {
		resp.IntercitySku.IsSupportMultiSelection = proto.Int32Ptr(0)
	}
	if baseReqData.CommonBizInfo.IsGuide == 1 {
		resp.BackButton = back.BuildBackInfo(ctx, products[0])
	}
}
