package intercity_anycar

import (
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
	"strconv"
)

type AdapterIntercity struct {
	*biz_runtime.ProductInfoFull
}

// InterCityCarpoolBookingTimeSpan 的实现

func (inter *AdapterIntercity) GetTimeSpan() []*EstimateDecision.TimeSpanV2 {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil || len(bizInfo.TimeSpan) == 0 {
		return nil
	}
	return bizInfo.TimeSpan
}

// GetSkuInfo TODO lsy 看下IntercitySkuInfo是否有值
func (inter *AdapterIntercity) GetSkuInfo() map[string]struct { // 库存模式信息
	Status int32
	Seat   int
} {
	if inter.GetBizInfo() == nil {
		return nil
	}

	return inter.GetBizInfo().IntercitySkuInfo
}

func (inter *AdapterIntercity) GetVersion() string {
	return inter.GetClientInfo().AppVersion
}

func (inter *AdapterIntercity) GetAccessKeyID() int {
	return int(inter.GetClientInfo().AccessKeyID)
}

func (inter *AdapterIntercity) GetAgentType() string {
	if inter.BaseReqData == nil {
		return ""
	}
	return inter.BaseReqData.CommonInfo.IsScanCode
}

// CarpoolBookingOuterData的实现

func (inter *AdapterIntercity) GetDepartureRangeStr() string {
	departureRange := inter.GetUserOption().DepartureRange
	return "[" + strconv.Itoa(int(departureRange.From.Unix())) + "," + strconv.Itoa(int(departureRange.To.Unix())) + "]"
}

// UserPayInfoProvider的实现

func (inter *AdapterIntercity) GetAllPaymentOptions() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := inter.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (inter *AdapterIntercity) GetMaxSeatNum() int32 {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.MaxCarpoolSeatNum
}

func (inter *AdapterIntercity) GetUserSelectSeatNum() int32 {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.CarpoolSeatNum
}

// FeeInfo的实现

func (inter *AdapterIntercity) GetScene() string {
	if inter.BaseReqData == nil {
		return ""
	}
	return inter.BaseReqData.CommonBizInfo.Scene
}

func (inter *AdapterIntercity) GetEstimateFeeAmount() float64 {
	fee := inter.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	return fee.GetFee()
}

func (inter *AdapterIntercity) GetCurrentPaymentType() int {
	opts := inter.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (inter *AdapterIntercity) GetRebookServiceCharge() float64 {
	if inter == nil {
		return 0
	}

	if inter.ProductInfoFull != nil {
		if rebookServiceFee := inter.ProductInfoFull.GetRebookServiceFee(); rebookServiceFee > 0 {
			return rebookServiceFee
		}
	}

	fees := inter.GetBillFeeDetailInfo()
	if fees == nil || len(fees) == 0 {
		return 0
	}
	if amount, ok := fees["rebook_service_charge"]; ok && amount > 0 {
		return amount
	}

	return 0
}

func (inter *AdapterIntercity) GetSelectedBusServiceShiftId() string {
	if inter == nil || inter.BaseReqData == nil {
		return ""
	}

	return inter.BaseReqData.CommonBizInfo.SeletedBusServiceShiftId
}
