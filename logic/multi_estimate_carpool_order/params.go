package multi_estimate_carpool_order

import (
	"context"
	"errors"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	carpool "git.xiaojukeji.com/dirpc/dirpc-go-http-Carpool"
	Dos "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	Vcard "git.xiaojukeji.com/dirpc/dirpc-go-http-Vcard"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/order_status"
	"git.xiaojukeji.com/gulfstream/biz-common-go/channel"
	consts2 "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	carpool2 "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_web"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	vcardClient "git.xiaojukeji.com/gulfstream/mamba/dao/rpc/vcard"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"git.xiaojukeji.com/gulfstream/mamba/models/option_gen_price"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/ability/carpool"
	"git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool/price_communication"
	"git.xiaojukeji.com/gulfstream/passenger-common/biz/carpoolintercity"
	util2 "git.xiaojukeji.com/gulfstream/passenger-common/util"
	"strings"
)

type ServiceRequest struct {
	DriverId           string
	TravelId           string
	CarpoolSuccessInfo map[string]*proto.CarpoolSuccessInfo
	OrderInfoList      map[int64]*order_info.OrderInfo
	PoolInfo           map[int64]*price_communication.PoolInfo
	QuoationMap        map[int64]*biz_runtime.QuotationV2
	BaseReqMap         map[int64]*models.BaseReqData
	CarpoolSceneData   map[int64]*option_gen_price.CarpoolSceneData
	OrderUsedCard      map[int64]*Vcard.UsableCard
}

func BuildServiceReq(ctx context.Context, req *proto.PEstimateCarpoolOrderRequest) (*ServiceRequest, NewErrors.BizError) {
	if req == nil || len(req.OrderList) == 0 {
		return nil, NewErrors.NewBizError(errors.New("params error"), NewErrors.ErrnoSystemError)
	}

	if req.DriverId == "" || req.TravelId == "" || len(req.CarpoolSuccessInfo) == 0 {
		return nil, NewErrors.NewBizError(errors.New("params error"), NewErrors.ErrnoSystemError)
	}

	res := &ServiceRequest{
		DriverId:           req.DriverId,
		TravelId:           req.TravelId,
		CarpoolSuccessInfo: req.CarpoolSuccessInfo,
	}

	err := res.buildOrderInfo(ctx, req.OrderList)
	if err != nil {
		return nil, NewErrors.NewBizError(err, NewErrors.ErrnoSystemError)
	}

	if len(res.OrderInfoList) == 0 {
		return nil, nil
	}

	res.buildQuoationInfo(ctx)
	res.buildOrderUsedCard(ctx)
	res.buildBaseReqData(ctx)
	res.buildCarpoolSceneData(ctx)

	return res, nil
}

func (sr *ServiceRequest) buildOrderInfo(ctx context.Context, orderList []string) NewErrors.BizError {
	req := &Dos.GetTravelOrderInfoReq{
		DriverId:   util.StringPtr(sr.DriverId),
		TravelId:   util.StringPtr(sr.TravelId),
		WhereEqual: &Dos.TravelWhereEqual{},
		Limit:      &Dos.Limit{},
	}

	travelOrderList, err := carpoolintercity.GetTravelOrderInfo(ctx, req)

	if err != nil || len(travelOrderList) == 0 {
		return NewErrors.NewBizError(errors.New("get travel order error"), NewErrors.ErrnoSystemError)
	}

	res := make(map[int64]*order_info.OrderInfo)
	for _, orderInfo := range travelOrderList {
		if orderInfo == nil || util.String2int64(ctx, orderInfo.CarpoolType) <= 0 {
			continue
		}

		if channel.IsPbdChannel(util.String2Int(ctx, orderInfo.Channel)) {
			continue
		}

		if isBusiness, err := product.IsBusiness(consts2.ProductID(util.String2Int(ctx, orderInfo.ProductId))); err == nil && isBusiness {
			continue
		}

		pcid := util.String2int64(ctx, util.StringPtr2String(orderInfo.EstimatePcId))
		carpoolType := util.String2int32(ctx, orderInfo.CarpoolType)
		carpoolPriceType := util.String2int32(ctx, orderInfo.CarpoolPriceType)
		idDualCarpoolPrice := util2.String2Bool(ctx, orderInfo.IsDualCarpoolPrice)
		// 只处理 市内小巴 两口价v3 拼成乐v2 网约公交 订单
		if !carpool2.IsMiniBus(int(carpoolType)) && !carpool2.IsCarpoolDualPriceV3(carpoolType, carpoolPriceType, idDualCarpoolPrice) &&
			!carpool2.IsPinCheCheV2(pcid, carpoolPriceType) && !carpool2.IsSmartBus(int(carpoolType)) {
			continue
		}

		// 将要开始计费的 订单
		for _, oid := range orderList {
			orderID, _, decodeErr := util.DecodeOrderID(oid)
			if decodeErr != nil {
				continue
			}
			if orderInfo.OrderId == util.Int642String(orderID) {
				res[orderID] = &order_info.OrderInfo{OrderInfo: orderInfo}
			}
		}

		// 开始计费中的订单, 公交小巴不处理已经开始计费的订单
		if !carpool2.IsSmartBus(int(carpoolType)) && util.String2Int(ctx, orderInfo.OrderStatus) == order_status.OrderStatusBeginCharge {
			res[util.String2int64(ctx, orderInfo.OrderId)] = &order_info.OrderInfo{OrderInfo: orderInfo}
		}
	}

	sr.OrderInfoList = res
	sr.buildPoolInfo(ctx, travelOrderList)
	return nil
}

func (sr *ServiceRequest) buildQuoationInfo(ctx context.Context) {
	if len(sr.OrderInfoList) == 0 {
		return
	}

	eidList := make([]string, 0)
	eidToOid := make(map[string]int64)
	for _, orderInfo := range sr.OrderInfoList {
		if len(orderInfo.EstimateId) <= 0 {
			continue
		}
		eidList = append(eidList, orderInfo.EstimateId)
		eidToOid[orderInfo.EstimateId] = util.String2int64(ctx, orderInfo.OrderId)
	}

	if len(eidList) == 0 {
		return
	}

	quotation, err := price_api.GetQuotationBatch(ctx, &price_api.PriceQuotationBatch{
		EstimateIdList: eidList,
	})

	if err != nil || quotation == nil || len(quotation.Data) <= 0 {
		return
	}

	res := make(map[int64]*biz_runtime.QuotationV2)

	for eid, quoationInfo := range quotation.Data {
		if oid, ok := eidToOid[eid]; ok {
			res[oid] = biz_runtime.EstimateQuotation2V2(quoationInfo)
		}
	}

	sr.QuoationMap = res
}

func (sr *ServiceRequest) buildOrderUsedCard(ctx context.Context) {
	if len(sr.OrderInfoList) == 0 {
		return
	}

	oidList := make([]string, 0)
	for _, orderInfo := range sr.OrderInfoList {
		if len(orderInfo.OrderId) <= 0 {
			continue
		}
		// 小巴不需要用卡信息
		if carpool2.IsSmartBus(util2.String2Int(ctx, orderInfo.CarpoolType)) || carpool2.IsMiniBus(util2.String2Int(ctx, orderInfo.CarpoolType)) {
			continue
		}

		oidList = append(oidList, orderInfo.OrderId)
	}

	if len(oidList) == 0 {
		return
	}

	res := make(map[int64]*Vcard.UsableCard)
	ret, err := vcardClient.GetBatchOrderUsedCard(ctx, &Vcard.OrderUsedCardBatchReq{
		OrderIds: strings.Join(oidList, ","),
	})
	if err != nil || ret == nil || len(ret.Data) == 0 {
		return
	}

	for oid, cardInfo := range ret.Data {
		ioid := util.String2int64(ctx, oid)
		res[ioid] = cardInfo.UsableCard
	}

	sr.OrderUsedCard = res
}

func (sr *ServiceRequest) buildPoolInfo(ctx context.Context, travelOrderList []*Dos.OrderInfo) {
	if sr.DriverId == "" || sr.TravelId == "" || len(sr.OrderInfoList) == 0 {
		return
	}

	poolInfos := make(map[int64]*price_communication.PoolInfo)
	for oid, orderInfo := range sr.OrderInfoList {
		if len(orderInfo.OrderId) <= 0 {
			continue
		}
		// 小巴不需要构建同行拼友信息
		if carpool2.IsSmartBus(util2.String2Int(ctx, orderInfo.CarpoolType)) || carpool2.IsMiniBus(util2.String2Int(ctx, orderInfo.CarpoolType)) {
			continue
		}

		poolInfos[oid] = &price_communication.PoolInfo{}

		if len(sr.CarpoolSuccessInfo) == 0 {
			continue
		}

		if val, ok := sr.CarpoolSuccessInfo[util.Int642String(oid)]; ok {
			poolInfos[oid].ChargePassenger = int64(val.ChargePoolNum)
			poolInfos[oid].RelationPassenger = int64(val.ChargePoolNum)
			if val.IsCarpoolSuccess && carpool2.IsCarpoolDualPriceV3(int32(orderInfo.GetCarpoolType()), orderInfo.GetCarpoolPriceType(), orderInfo.GetIsDualCarpoolPrice()) {
				poolInfos[oid].ChargePassenger = 1
				poolInfos[oid].RelationPassenger = 1
			}
		}
	}

	if len(poolInfos) == 0 {
		return
	}

	driverId := util.String2int64(ctx, sr.DriverId)
	travelId := util.String2int64(ctx, sr.TravelId)
	poolInfoResult, err := carpool_web.CGetPoolInfo(ctx, driverId, travelId)
	if err != nil || poolInfoResult == nil {
		return
	}

	for oid, poolInfo := range poolInfoResult.PoolInfo {
		if poolItem, ok := poolInfos[util.String2int64(ctx, oid)]; ok {
			poolItem.RelationPassenger = buildRelationCount(ctx, poolInfo, travelOrderList)
		}
	}

	sr.PoolInfo = poolInfos
}

func buildRelationCount(ctx context.Context, poolItem *carpool.PoolInfoItem, travelOrderList []*Dos.OrderInfo) int64 {
	if poolItem == nil || (len(poolItem.RelationPassenger) == 0 && len(poolItem.ChargePassenger) == 0) {
		return 0
	}

	// 规划共乘拼友 实际共乘拼友
	var relCnt, chaCnt int64
	for _, relationPid := range poolItem.RelationPassenger {
		for _, info := range travelOrderList {
			if info.PassengerId == util.Int642String(relationPid) {
				relCnt += util.String2int64(ctx, info.PassengerCount)
			}
		}
	}

	for _, relationPid := range poolItem.ChargePassenger {
		for _, info := range travelOrderList {
			if info.PassengerId == util.Int642String(relationPid) {
				chaCnt += util.String2int64(ctx, info.PassengerCount)
			}
		}
	}

	// 优先使用规划共乘拼友
	if relCnt != 0 {
		return relCnt
	}

	// 没有的话使用实际共乘拼友兜底
	return chaCnt
}

func (sr *ServiceRequest) buildBaseReqData(ctx context.Context) {
	res := make(map[int64]*models.BaseReqData)
	for oid, orderInfo := range sr.OrderInfoList {
		if orderInfo == nil {
			continue
		}
		if baseReq, err := buildBaseReq(ctx, orderInfo); err == nil {
			res[oid] = baseReq
		}
	}
	sr.BaseReqMap = res
}

func (sr *ServiceRequest) buildCarpoolSceneData(ctx context.Context) {
	if len(sr.OrderInfoList) == 0 {
		return
	}

	res := make(map[int64]*option_gen_price.CarpoolSceneData)
	// 区分 品类 极速拼车（拼成 未拼成） 小巴（未拼成） 拼成乐（未拼成 拼一人 拼两人）
	for oid, orderInfo := range sr.OrderInfoList {
		if carpool2.IsCarpoolDualPriceV3(int32(orderInfo.GetCarpoolType()), int32(orderInfo.GetCarpoolPriceType()), orderInfo.GetIsDualCarpoolPrice()) {
			if val, ok := sr.CarpoolSuccessInfo[util.Int642String(oid)]; ok {
				res[oid] = sr.bulidSceneData(ctx, orderInfo.GetPassengerCount(), 0, val.IsCarpoolSuccess, orderInfo)
			} else {
				res[oid] = sr.bulidSceneData(ctx, orderInfo.GetPassengerCount(), 0, false, orderInfo)
			}
		}

		if carpool2.IsPinCheCheV2(orderInfo.GetProductCategory(), orderInfo.GetCarpoolPriceType()) {
			if val, ok := sr.CarpoolSuccessInfo[util.Int642String(oid)]; ok {
				res[oid] = sr.bulidSceneData(ctx, orderInfo.GetPassengerCount(), val.GetChargePoolNum(), val.IsCarpoolSuccess, orderInfo)
			} else {
				res[oid] = sr.bulidSceneData(ctx, orderInfo.GetPassengerCount(), 0, false, orderInfo)
			}
		}

		if carpool2.IsMiniBus(int(orderInfo.GetCarpoolType())) || carpool2.IsSmartBus(int(orderInfo.GetCarpoolType())) {
			res[oid] = sr.bulidSceneData(ctx, orderInfo.GetPassengerCount(), 0, false, orderInfo)
		}
	}

	sr.CarpoolSceneData = res
}

func (sr *ServiceRequest) bulidSceneData(ctx context.Context, seatNum, pooNum int32, isCarpoolSuccess bool, orderData *order_info.OrderInfo) *option_gen_price.CarpoolSceneData {
	res := &option_gen_price.CarpoolSceneData{}
	oid := util.String2int64(ctx, orderData.OrderId)

	if multiPriceScene := orderData.GetMultiPriceScene(); multiPriceScene != nil {
		sceneOrderPrice := carpool3.GetSceneBill(ctx, &PriceApi.CarpoolSceneBill{ScenePrice: multiPriceScene}, seatNum, pooNum, isCarpoolSuccess)
		if quoationInfo, ok := sr.QuoationMap[oid]; ok {
			if quoationPrice := carpool3.GetSceneBill(ctx, quoationInfo.CarpoolSceneBillAll, seatNum, pooNum, isCarpoolSuccess); quoationPrice != nil {
				res.FeeDetailInfo = quoationPrice.FeeDetailInfo
			}
		}

		if res.FeeDetailInfo == nil {
			res.FeeDetailInfo = make(map[string]float64)
		}

		if sceneOrderPrice != nil {
			res.DynamicTotalFee = sceneOrderPrice.DynamicTotalFee
			res.FeeDetailInfo["cap_price"] = sceneOrderPrice.CapPrice
			res.BubbleEstimateId = orderData.EstimateId
			orderData.CapPrice = util.FormatPrice(sceneOrderPrice.CapPrice, -1)
		}
	}

	res.OrderInfo = option_gen_price.BuildOrderInfo(ctx, orderData)
	res.Option = &PriceApi.CarpoolPriceSceneOption{
		IsCarpoolSuccess: isCarpoolSuccess,
		PoolNum:          pooNum,
		SeatNum:          seatNum,
	}

	return res
}

func buildBaseReq(ctx context.Context, orderInfo *order_info.OrderInfo) (*models.BaseReqData, error) {
	baseReq, err := models.NewBaseReqDataBuilder().
		SetClientInfo(&models.ClientInfo{
			AccessKeyID: util2.String2Int32(ctx, orderInfo.PAccessKeyId),
			Channel:     orderInfo.GetChannel(),
			MenuID:      consts.MenuIDDaCheAnyCar,
		}).
		SetGEOInfo(&models.GEOInfo{
			CurrLat:     orderInfo.GetCurrentLat(),
			CurrLng:     orderInfo.GetCurrentLng(),
			FromLat:     orderInfo.GetFromLat(),
			FromLng:     orderInfo.GetFromLng(),
			FromPOIID:   orderInfo.StartingPoiId,
			FromAddress: orderInfo.FromAddress,
			FromName:    orderInfo.FromName,
			ToLat:       orderInfo.GetToLat(),
			ToLng:       orderInfo.GetToLng(),
			ToPOIID:     orderInfo.DestPoiId,
			ToAddress:   orderInfo.ToAddress,
			ToName:      orderInfo.ToName,
		}).
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:   util.GetUidByPassengerId(util.String2int64(ctx, orderInfo.PassengerId), util.Passenger),
			PID:   util.String2int64(ctx, orderInfo.PassengerId),
			Phone: orderInfo.PassengerPhone,
		}).SetUserOption(&models.UserOption{
		CarpoolSeatNum: orderInfo.GetPassengerCount(),
		DepartureTime:  orderInfo.GetDepartureTime(ctx),
	}).TryBuild(ctx)
	if err != nil {
		return nil, NewErrors.NewBizError(err, NewErrors.ErrnoSystemError)
	}

	baseReq.SendOrder = models.SendOrder{
		OrderIdLow: util.String2int64(ctx, orderInfo.OrderId),
	}

	return baseReq, nil
}
