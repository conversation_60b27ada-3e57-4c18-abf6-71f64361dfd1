package assembler

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/homepage_estimate_v2/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	PassengerUtil "git.xiaojukeji.com/gulfstream/passenger-common/util"
	jsoniter "github.com/json-iterator/go"
	"math"
	"sort"
	"strconv"
	"strings"
)

// buildEtpInfo 从产品列表中获取预估时间信息
func buildEtpInfo(_ context.Context, infoMaterial *etpInfoMaterial, products []*biz_runtime.ProductInfoFull) *proto.HomePSceneTag {
	var (
		isGetAnyEtpInfoSucceed bool
		template               string
		minEtp                 int32
	)
	for _, product := range products {
		if product.Product.BizInfo == nil || product.Product.BizInfo.AthenaEstimateEtpEtdInfo == nil {
			continue
		}

		sceneFlag := consts.AthenaSceneFlag(product.Product.BizInfo.AthenaEstimateEtpEtdInfo.SceneFlag)
		if _, ok := consts.ETPInvalidScene[sceneFlag]; ok {
			product.Product.BizInfo.AthenaEstimateEtpEtdInfo.EtpMin = math.MaxInt32
		}
		if !isGetAnyEtpInfoSucceed {
			template = infoMaterial.EtpTime
			minEtp = product.Product.BizInfo.AthenaEstimateEtpEtdInfo.EtpMin
		} else {
			if minEtp != product.Product.BizInfo.AthenaEstimateEtpEtdInfo.EtpMin {
				template = infoMaterial.EtpFastestTime
				minEtp = util.MinInt32(minEtp, product.Product.BizInfo.AthenaEstimateEtpEtdInfo.EtpMin)
			}
		}
		isGetAnyEtpInfoSucceed = true
	}
	if !isGetAnyEtpInfoSucceed || template == "" {
		return nil
	}

	if minEtp > 3 {
		template = infoMaterial.EtpNearby
	}
	if template == "" {
		return nil
	}
	return &proto.HomePSceneTag{
		BorderColor: util.String2PtrString(infoMaterial.BorderColor),
		BgColor:     util.String2PtrString(infoMaterial.BgColor),
		FontColor:   util.String2PtrString(infoMaterial.FontColor),
		Content: dcmp.TranslateTemplate(template,
			map[string]string{"time": strconv.FormatInt(int64(minEtp), 10)}),
	}
}

// buildCarpoolPrice 从拼车多口价中获取拼成人数最多的价格
func buildCarpoolPrice(carpoolProduct *biz_runtime.ProductInfoFull) float64 {
	adapter := data.ViewAdapter{Prod: carpoolProduct}
	prov := adapter.MultiPrice()

	if len(prov) == 0 {
		return 0
	}

	// 拼到人数多的在前面
	sort.Slice(prov, func(i, j int) bool {
		return prov[i].GetExpectPoolNum() > prov[j].GetExpectPoolNum()
	})

	return prov[0].GetEstimateFeeAmount()
}

// buildMultiRequireProducts 构架发单多品类参数
func buildMultiRequireProducts(ctx context.Context, products []*biz_runtime.ProductInfoFull) []*proto.ProductInfo {
	var multiProduct []*proto.ProductInfo
	for _, product := range products {
		productInfo := &proto.ProductInfo{
			ProductId:       product.Product.ProductID,
			BusinessId:      product.Product.BusinessID,
			ComboType:       product.Product.ComboType,
			RequireLevel:    product.Product.RequireLevelInt,
			LevelType:       product.Product.LevelType,
			RouteType:       product.Product.RouteType,
			IsSpecialPrice:  product.Product.IsSpecialPrice,
			CountPriceType:  product.GetCountPriceType(),
			PayType:         strconv.Itoa(int(product.GetDefaultPayType())),
			EstimateId:      product.GetEstimateID(),
			ProductCategory: product.GetProductCategory(),
		}
		if product.Product.BizInfo != nil {
			productInfo.ComboId = product.Product.BizInfo.ComboID
		}
		if product.Product.ProductCategory == PassengerUtil.ProductCategoryCarpoolStation {
			carpoolSeatNum := int64(1)
			productInfo.CarpoolSeatNum = &carpoolSeatNum
		}
		multiProduct = append(multiProduct, productInfo)
	}
	return multiProduct
}

func buildProductsDesc(_ context.Context, products []*biz_runtime.ProductInfoFull) string {
	productDesc := buildProductNameArray(products)
	if len(productDesc) == 0 {
		return ""
	}
	return strings.Join(productDesc, "、")
}

func buildProductNameArray(products []*biz_runtime.ProductInfoFull) []string {
	var productDesc []string
	for _, product := range products {
		singleDesc, ok := ProductCategoryToNameMap[product.Product.ProductCategory]
		if !ok {
			continue
		}
		productDesc = append(productDesc, singleDesc)
	}
	return productDesc
}

func buildButton(_ context.Context, material *buttonMaterial) *proto.HomePCallCarButtonInfo {
	return &proto.HomePCallCarButtonInfo{
		BgStartColor: material.BgStartColor,
		BgEndColor:   material.BgEndColor,
		TextColor:    material.TextColor,
		BorderColor:  material.BorderColor,
		Text:         material.Text,
	}
}

func buildNewOrderParams(ctx context.Context, products []*biz_runtime.ProductInfoFull,
	estimateTraceId string, enableMultiSelect int32) (*proto.HomePNewOrderParams, error) {

	requireStr, err := jsoniter.MarshalToString(buildMultiRequireProducts(ctx, products))
	if err != nil {
		return nil, fmt.Errorf("buildNewOrderParams marshal Products err:%v", err)
	}
	return &proto.HomePNewOrderParams{
		MultiRequireProduct:     requireStr,
		EstimateTraceId:         estimateTraceId,
		IsSupportMultiSelection: enableMultiSelect,
	}, nil
}

func renderFeeMsg(ctx context.Context, feeMat *feeMaterial, products []*biz_runtime.ProductInfoFull) string {
	switch len(products) {
	case 0:
		return ""
	case 1:
		product := products[0]
		k := getSingleFeeMsg(ctx, feeMat.SingleRichText, product)
		return k
	default:
		minFee, maxFee := getFeeRange(ctx, products)
		if minFee == maxFee {
			return getSingleFeeMsg(ctx, feeMat.SingleRichText, products[0])
		}
		return dcmp.TranslateTemplate(feeMat.MultiRichText, map[string]string{
			"min_fee": util.FormatPrice(minFee, 1),
			"max_fee": util.FormatPrice(maxFee, 1),
		})
	}
}

func getFeeRange(_ context.Context, products []*biz_runtime.ProductInfoFull) (float64, float64) {
	minFee, maxFee := products[0].GetEstimateFee(), products[0].GetEstimateFee()
	for _, product := range products {
		fee := product.GetEstimateFee()
		payType := util.ToInt(product.GetDefaultPayType())
		if payType == consts.BusinessPaymentType || payType == consts.BusinessPayByTeam {
			fee = product.GetPersonalEstimateFee()
		}
		minFee = math.Min(minFee, fee)
		maxFee = math.Max(maxFee, fee)
	}
	return minFee, maxFee
}

func getSingleFeeMsg(ctx context.Context, richTextTemplate string, product *biz_runtime.ProductInfoFull) string {
	fee := util.FormatPrice(product.GetEstimateFee(), 1)
	feeTemplate := fee_info_render.GetNormalFeeMsgTemplate(ctx, product)
	k := dcmp.TranslateTemplate(feeTemplate, map[string]string{
		"num": dcmp.TranslateTemplate(richTextTemplate,
			map[string]string{"fee": fee}),
	})
	return k
}
