package assembler

import (
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	PassengerUtil "git.xiaojukeji.com/gulfstream/passenger-common/util"
)

const (
	callCarLinkTypeInterior = "interior"
	callCarLinkTypeNewOrder = "neworder"
)

var ProductCategoryToNameMap = map[int64]string{ // todo 改
	estimate_pc_id.EstimatePcIdFastCar:           "快车",
	PassengerUtil.ProductCategoryFastSpecialRate: "特惠",
	PassengerUtil.ProductCategorySpecialRate:     "特惠",
	PassengerUtil.ProductCategoryLowPriceCarpool: "拼车",
	PassengerUtil.ProductCategoryCarpoolStation:  "拼车",
	PassengerUtil.ProductCategoryUnione:          "出租",
	PassengerUtil.ProductCategoryAplus:           "特快",
	PassengerUtil.ProductCategoryYouxiang:        "优享",
}
