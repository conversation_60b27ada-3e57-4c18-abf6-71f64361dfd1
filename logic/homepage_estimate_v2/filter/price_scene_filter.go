package filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace/v2"
)

type PriceSceneFilter struct {
}

func (p *PriceSceneFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var allProducts []models.ProductCategory

	for _, product := range products {
		if len(product.GetBillDisplayLines()) != 0 {
			redPacketSkip := false
			energyConsumeFeeSkip := false
			taxiHolidayPriceSkip := false

			if redPacketSkip || energyConsumeFeeSkip || taxiHolidayPriceSkip {

			}
		}

		for name, value := range product.GetBillInfo().FeeDetailInfo {
			// 春节服务费过滤
			if name == "red_packet" && value != 0 {
				allProducts = append(allProducts, models.ProductCategory(product.GetProductCategory()))
				log.Trace.Infof(ctx, trace.DLTagUndefined, "filter by special scene: red packet")
				break
			}

			// 能耗费过滤
			if name == "energy_consume_fee" && value != 0 {
				allProducts = append(allProducts, models.ProductCategory(product.GetProductCategory()))
				log.Trace.Infof(ctx, trace.DLTagUndefined, "filter by special scene: energy consume")
				break
			}

			// 春节服务费过滤
			if name == "taxi_holiday_price" && value != 0 {
				allProducts = append(allProducts, models.ProductCategory(product.GetProductCategory()))
				log.Trace.Infof(ctx, trace.DLTagUndefined, "filter by special scene: taxi holiday price")
				break
			}
		}

		if product.GetNewDynamicDiffPrice() != 0 {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "filter by GetDynamicDiffPrice scene")
			allProducts = append(allProducts, models.ProductCategory(product.GetProductCategory()))
			continue
		}

		params := map[string]string{
			"phone":         product.BaseReqData.PassengerInfo.Phone,
			"pid":           strconv.FormatInt(product.BaseReqData.PassengerInfo.PID, 10),
			"product_id":    strconv.FormatInt(product.Product.ProductID, 10),
			"combo_type":    strconv.FormatInt(product.Product.ComboType, 10),
			"require_level": product.Product.RequireLevel,
			"city":          strconv.FormatInt(int64(product.BaseReqData.AreaInfo.City), 10),
			"access_key_id": strconv.FormatInt(int64(product.BaseReqData.CommonInfo.AccessKeyID), 10),
			"app_version":   product.BaseReqData.CommonInfo.AppVersion,
			"lang":          product.BaseReqData.CommonInfo.Lang,
			"uid":           strconv.FormatInt(product.BaseReqData.PassengerInfo.UID, 10),
		}
		if apollo.FeatureToggle(ctx, "special_scene_switch", strconv.FormatInt(product.BaseReqData.PassengerInfo.UID, 10), params) {
			if product.GetHighWayFee() != 0 {
				log.Trace.Infof(ctx, trace.DLTagUndefined, "filter by GetHighWayFee scene")
				allProducts = append(allProducts, models.ProductCategory(product.GetProductCategory()))
				continue
			}
		}
	}

	return allProducts
}
