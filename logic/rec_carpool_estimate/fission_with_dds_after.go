package rec_carpool_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_fission/after_dds_fission"
)

func RegisterProductFission(ctx context.Context, generator *biz_runtime.ProductsGenerator) {
	if generator.BaseReqData == nil {
		return
	}
	if cpFission := after_dds_fission.NewRecCarpoolFission(ctx, generator.BaseReqData); cpFission != nil {
		generator.RegisterAfterDdsFission(cpFission)
	}
}
