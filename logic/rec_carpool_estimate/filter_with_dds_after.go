package rec_carpool_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_dds_filter"
)

func RegisterProductFilter(ctx context.Context, generator *biz_runtime.ProductsGenerator) {
	if generator.BaseReqData == nil {
		return
	}
	// 二次开城过滤
	if cpFilter := after_dds_filter.NewRecCarpoolFilter(ctx, generator.BaseReqData); cpFilter != nil {
		generator.RegisterAfterDdsFilter(cpFilter)
	}

}
