package route_estimate

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	trace "git.xiaojukeji.com/lego/context-go"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/route_estimate/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"git.xiaojukeji.com/gulfstream/mamba/models/option_gen_price"
	"git.xiaojukeji.com/gulfstream/mamba/models/option_gen_product"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/route_estimate/route_detail"
)

// RouteEstimate 路线预估
func RouteEstimate(ctx context.Context, req *proto.RouteEstimateRequest) (resp *proto.RouteEstimateData, err error) {
	resp = newResp(ctx)

	serviceReq, err := buildServiceReq(ctx, req)
	if err != nil {
		return resp, err
	}

	baseReq := buildBaseReq(ctx, serviceReq)
	serviceReq.WithBaseReq(baseReq)
	productsGen, err := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("p_route_estimate"),
		biz_runtime.WithBaseReq(baseReq),
	)
	if err != nil {
		return resp, err
	}

	// SetNeedMember 设置是否需要请求会员
	productsGen.SetNeedMember(true)

	// 注册自定义产出品类的函数
	productsGen.RegisterOptionProcessProduct(option_gen_product.NewRouteEstimateProductOutput(req.RouteIdList, serviceReq.OrderInfo))

	// 注册自定义产出价格的函数
	productsGen.RegisterOptionProcessPrice(option_gen_price.NewEstimatePriceByOrder(serviceReq.OrderInfo, nil, nil, option_gen_price.CallerRouteEstimate))

	// 注册请求 price-api[前]  的RPC
	productsGen.RegisterBeforePriceRpcProcess(rpc_process.NewTravelQuotation(ctx, serviceReq.OrderInfo.GetOrderID(), serviceReq.OrderInfo.EstimateId, []string{ferrari.BeforeFreeUpgradeFee, ferrari.WaitFreeUpgradeFee, ferrari.SurprisePrivilege}))
	productsGen.RegisterBeforePriceRpcProcess(rpc_process.NewOFS(serviceReq.OrderInfo))
	productsGen.RegisterBeforePriceRpcProcess(rpc_process.NewPetInfo(serviceReq))

	products, err := productsGen.GenProducts(ctx)
	if err != nil {
		return resp, err
	}

	err = render(ctx, resp, serviceReq, products)
	if err != nil {
		return
	}

	util.Go(ctx, func() {
		WritePublicLog(ctx, serviceReq, resp, products)
	})

	return
}

func buildBaseReq(ctx context.Context, req *model.ServiceReq) *models.BaseReqData {
	if req == nil || req.UserInfo == nil || req.OrderInfo == nil {
		return nil
	}

	passenger, orderInfo, originReq := req.UserInfo, req.OrderInfo, req.OriginRequest
	fromAddress, fromName := getAddressAndNameByName(orderInfo.StartingName)
	toAddress, toName := getAddressAndNameByName(orderInfo.DestName)
	fromLat, fromLng := getFromLatLng(originReq, orderInfo)
	baseReq, err := models.NewBaseReqDataBuilder().SetPassengerInfoV2(&models.PassengerInfoV2{
		UID:      int64(passenger.UID),
		PID:      int64(passenger.PID),
		Phone:    passenger.Phone,
		Role:     passenger.Role,
		Channel:  passenger.Channel,
		AppID:    passenger.AppID,
		UserType: int32(originReq.UserType),
		OriginID: passenger.OriginId,
		Token:    originReq.Token,
	}).SetClientInfo(&models.ClientInfo{
		AppVersion:   originReq.AppVersion,
		AccessKeyID:  originReq.AccessKeyId,
		Channel:      orderInfo.GetChannel(),
		ClientType:   int32(originReq.ClientType),
		Lang:         originReq.Lang,
		PlatformType: originReq.PlatformType,
		TerminalID:   originReq.TerminalId,
		OriginID:     originReq.OriginId,
		Imei:         originReq.Imei,
		MenuID:       consts2.MenuIDDaCheAnyCar,
	}).SetGEOInfo(&models.GEOInfo{
		MapType:     originReq.MapType,
		CurrLat:     orderInfo.GetCurrentLat(),
		CurrLng:     orderInfo.GetCurrentLng(),
		Lat:         originReq.Lat,
		Lng:         originReq.Lng,
		FromLat:     fromLat,
		FromLng:     fromLng,
		FromPOIID:   originReq.FromPoiId,
		FromPOIType: originReq.FromPoiType,
		FromAddress: fromAddress,
		FromName:    fromName,
		ToLat:       orderInfo.GetToLat(),
		ToLng:       orderInfo.GetToLng(),
		ToPOIID:     originReq.ToPoiType,
		ToPOIType:   originReq.ToPoiType,
		ToAddress:   toAddress,
		ToName:      toName,
	}).SetUserOption(&models.UserOption{
		CarpoolSeatNum: orderInfo.GetPassengerCount(),
		DepartureTime:  orderInfo.GetDepartureTime(ctx),
	}).SetSendOrder(req).TryBuild(ctx)
	if err != nil {
		return nil
	}

	return baseReq
}

func getFromLatLng(req *proto.RouteEstimateRequest, info *order_info.OrderInfo) (lat float64, lng float64) {
	// 默认dos经纬度，其次端经纬度
	if info != nil {
		lat, lng = info.GetFromLat(), info.GetFromLng()
	} else if info == nil && req != nil {
		lat, lng = req.GetFromLat(), req.GetFromLng()
	}

	// 开始计费优先使用端当前经纬度
	//if info.GetOrderStatus() == consts.OrderStatusBeginCharge && req != nil && req.GetLat() > 0 && req.GetLng() > 0 {
	//	lat, lng = req.GetLat(), req.GetLng()
	//}

	return lat, lng
}

func buildServiceReq(ctx context.Context, request *proto.RouteEstimateRequest) (*model.ServiceReq, error) {
	serviceReq := model.NewServiceReq()
	err := serviceReq.Do(ctx, []model.Opts{
		setParams(request),
		setUserInfo(request),
		setOrderInfo(request),
		checkByOrderInfo(request),
	}...)
	if err != nil {
		// log.Trace.Warnf(ctx, "buildServiceReq", "err=%v", err)
		return nil, err
	}

	return serviceReq, err
}

func setParams(req *proto.RouteEstimateRequest) func(context.Context, *model.ServiceReq) NewErrors.BizError {
	return func(ctx context.Context, serviceReq *model.ServiceReq) NewErrors.BizError {
		if serviceReq == nil {
			return NewErrors.NewBizError(errors.New("data is nil"), NewErrors.ErrnoSystemError)
		}

		if req == nil {
			return NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
		}

		// 由于小程序app_version传的是miniapp_mapsdk，无法通过price-api的displaycontrol.IsAllow的校验，赋一个默认值
		if req.AppVersion == "miniapp_mapsdk" {
			req.AppVersion = "6.8.05"
		}

		serviceReq.OriginRequest = req

		return nil
	}
}

func setUserInfo(req *proto.RouteEstimateRequest) func(context.Context, *model.ServiceReq) NewErrors.BizError {
	return func(ctx context.Context, serviceReq *model.ServiceReq) NewErrors.BizError {
		if serviceReq == nil {
			return NewErrors.NewBizError(errors.New("data is nil"), NewErrors.ErrnoSystemError)
		}

		if req == nil {
			return NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
		}

		if req.Token == "" {
			return NewErrors.NewBizError(errors.New("token is nil"), NewErrors.ErrnoInvalidArgument)
		}

		userInfo, err := passport.GetUserInfo(ctx, req.Token, "")
		if err != nil {
			if err == passport.ErrOffline {
				return NewErrors.ErrNotLogin
			}
			return NewErrors.ErrSystem
		}

		serviceReq.UserInfo = userInfo
		return nil
	}
}

func setOrderInfo(req *proto.RouteEstimateRequest) func(context.Context, *model.ServiceReq) NewErrors.BizError {
	return func(ctx context.Context, serviceReq *model.ServiceReq) NewErrors.BizError {
		if serviceReq == nil {
			return NewErrors.NewBizError(errors.New("data is nil"), NewErrors.ErrnoSystemError)
		}

		if req == nil || req.Oid == "" {
			return NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
		}

		if req.Oid == "" {
			return NewErrors.NewBizError(errors.New("oid is nil"), NewErrors.ErrnoInvalidArgument)
		}

		orderID, district, err := util.DecodeOrderID(req.Oid)
		if err != nil {
			return NewErrors.NewBizError(errors.New("decode oid fail"), NewErrors.ErrnoInvalidArgument)
		}

		highOrderID, weakErr := util.EncodeOrderIdWithoutBase64(orderID, district)
		if weakErr != nil {
			log.Trace.Warnf(ctx, "setOrderInfo", "encode order id fail, err:%v", err)
		}

		serviceReq.OriginOrderID = req.Oid
		serviceReq.LowOrderID = orderID
		serviceReq.HighOrderID = highOrderID

		orderInfo, err := dos.GetOrderInfo(ctx, orderID, district)
		if err != nil || orderInfo == nil {
			return NewErrors.NewBizError(errors.New("get order info fail"), NewErrors.ErrnoRpcFailed)
		}

		serviceReq.OrderInfo = order_info.OrderInfo2OrderInfo(orderInfo)

		return nil
	}
}

func checkByOrderInfo(req *proto.RouteEstimateRequest) func(context.Context, *model.ServiceReq) NewErrors.BizError {
	return func(ctx context.Context, serviceReq *model.ServiceReq) NewErrors.BizError {
		if serviceReq == nil || serviceReq.OrderInfo == nil {
			return NewErrors.NewBizError(fmt.Errorf("order status dont pass, status:%v", serviceReq.OrderInfo.OrderStatus), NewErrors.ErrnoInvalidArgument)
		}

		// 订单状态检查
		if !checkOrderStatus(ctx, serviceReq) {
			return NewErrors.NewBizError(fmt.Errorf("order status dont pass, status:%v", serviceReq.OrderInfo.OrderStatus), NewErrors.ErrnoInvalidArgument)
		}

		// 一口价检查
		if !checkCapPrice(ctx, req, serviceReq) {
			return NewErrors.NewBizError(errors.New("cap price dont support"), NewErrors.ErrnoInvalidArgument)
		}

		// 品类检查
		if !checkProduct(ctx, serviceReq) {
			return NewErrors.NewBizError(fmt.Errorf("product dont support, pcid:%v", serviceReq.OrderInfo.GetProductCategory()), NewErrors.ErrnoInvalidArgument)
		}
		return nil
	}
}

// checkCapPrice 判断是否是一口价场景
func checkCapPrice(ctx context.Context, req *proto.RouteEstimateRequest, serviceReq *model.ServiceReq) bool {
	if serviceReq == nil || serviceReq.OrderInfo == nil || req == nil || serviceReq.UserInfo == nil {
		return false
	}

	if serviceReq.OrderInfo.GetCapPrice() > 0 {
		//需要增加企业付拦截，一口价、送驾、企业付时需要拦截选择路线的价格展示
		if !apollo.FeatureToggle(ctx, "gs_route_estimate_cap_price_controller", "", map[string]string{
			"city":          serviceReq.OrderInfo.Area,
			"access_key_id": util.Int32String(req.GetAccessKeyId()),
			"app_version":   req.GetAppVersion(),
			"lang":          req.GetLang(),
			"pid":           util.Int642String(int64(serviceReq.UserInfo.PID)),
			"phone":         serviceReq.UserInfo.Phone,
			"order_status":  serviceReq.OrderInfo.OrderStatus,
			"caller":        util.StringPtr2String(req.Caller),
			"pay_type":      serviceReq.OrderInfo.PayType,
			"channel":       serviceReq.OrderInfo.Channel,
			"client_type":   strconv.FormatInt(req.GetClientType(), 10),
		}) {
			return false
		}
	}

	return true
}

// checkOrderStatus 订单状态检查
func checkOrderStatus(ctx context.Context, serviceReq *model.ServiceReq) bool {
	if serviceReq == nil || serviceReq.OrderInfo == nil {
		return false
	}

	if !util.InArrayInt(serviceReq.OrderInfo.GetOrderStatus(), []int{order_info.DriverGrabbing.ToInt(), order_info.ArrivingPoint.ToInt(), order_info.StartBilling.ToInt()}) {
		return false
	}

	return true
}

// checkProduct 检查品类
func checkProduct(ctx context.Context, serviceReq *model.ServiceReq) bool {
	if serviceReq == nil || serviceReq.OrderInfo == nil {
		return false
	}

	// 出租车，拼车，三方不允许切换路线
	// 出租车已经允许切换路线了
	if carpool.IsCarpool(serviceReq.OrderInfo.GetCarpoolType()) {
		return false
	}

	return true
}

// render 渲染
func render(ctx context.Context, resp *proto.RouteEstimateData, serviceReq *model.ServiceReq, productFulls []*biz_runtime.ProductInfoFull) error {
	resp.RouteDetail = route_detail.BuildRouteDetail(ctx, serviceReq, productFulls)
	resp.ExtraInfo = route_detail.BuildExtraInfo(ctx, serviceReq, productFulls)
	return nil
}

// getAddressAndNameByName 解析name
func getAddressAndNameByName(name string) (string, string) {
	res := strings.Split(name, "|")
	if len(res) < 2 {
		return "", name
	}

	return res[0], res[1]
}

// newResp ...
func newResp(ctx context.Context) *proto.RouteEstimateData {
	return &proto.RouteEstimateData{
		EstimateTraceId: trace.GetTrace(ctx).GetTraceId(),
	}
}

func WritePublicLog(ctx context.Context, serviceReq *model.ServiceReq, resp *proto.RouteEstimateData, productFulls []*biz_runtime.ProductInfoFull) {
	if len(productFulls) <= 0 {
		return
	}

	for _, product := range productFulls {
		if product == nil {
			continue
		}

		logInfo := make(map[string]interface{})
		logInfo["product_category"] = product.GetProductCategory()
		logInfo["estimate_id"] = product.GetEstimateID()
		logInfo["estimate_fee"] = product.GetEstimateFee()
		logInfo["cap_price"] = product.GetCapPrice()

		if serviceReq != nil && serviceReq.OrderInfo != nil {
			logInfo["area"] = serviceReq.OrderInfo.Area
			logInfo["order_status"] = serviceReq.OrderInfo.OrderStatus
		}

		if serviceReq != nil && serviceReq.OriginRequest != nil {
			logInfo["access_key_id"] = serviceReq.OriginRequest.AccessKeyId
			logInfo["app_version"] = serviceReq.OriginRequest.AppVersion
			logInfo["caller"] = serviceReq.OriginRequest.Caller
		}

		log.Public.Public(ctx, public_log.GRouteEstimateByOrder, logInfo)
	}
}
