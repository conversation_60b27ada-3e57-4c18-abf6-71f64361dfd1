package model

import (
	"context"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
)

type ServiceReq struct {
	OriginRequest *proto.RouteEstimateRequest
	UserInfo      *passport.UserInfo
	OrderInfo     *order_info.OrderInfo
	BaseReq       *models.BaseReqData

	CommonParam

	OriginBills map[string]*proto.OriginBills
}

type CommonParam struct {
	OriginOrderID string
	LowOrderID    int64
	HighOrderID   int64
}

func (c *CommonParam) GetLowOrderID() int64 {
	return c.LowOrderID
}

func (c *CommonParam) GetHighOrderID() int64 {
	return c.HighOrderID
}

type UserInfo struct {
	Pid   int64
	Uid   int64
	Phone string
}

type Opts func(context.Context, *ServiceReq) NewErrors.BizError

func NewServiceReq() *ServiceReq {
	return &ServiceReq{}
}

func (s *ServiceReq) Do(ctx context.Context, opts ...Opts) NewErrors.BizError {
	if len(opts) == 0 {
		return nil
	}

	for _, o := range opts {
		if o == nil {
			continue
		}

		err := o(ctx, s)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *ServiceReq) WithBaseReq(baseReq *models.BaseReqData) {
	s.BaseReq = baseReq
}
