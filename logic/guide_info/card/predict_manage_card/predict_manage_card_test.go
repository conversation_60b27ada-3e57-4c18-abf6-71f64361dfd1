package predict_manage_card

import (
	"context"
	"encoding/json"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/card"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/consts"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/data/order_info"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func TestBargainBuildFeeInfo(t *testing.T) {

	quation := biz_runtime.Quotation{
		ProductCategory: 327,
	}

	// 模拟OrderInfoService
	orderInfoService := &order_info.OrderInfoService{}

	// 创建一个实际的 card.Input 对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	adapter := NewAdapter(quation, input, dos.RequiredProductStruct{
		PassengerBargainRangeInfo: "2",
	}, &RenderBaseData{})
	adapter.RequiredProductStruct = dos.RequiredProductStruct{
		PassengerBargainRangeInfo: "2",
	}

	pmCard := PredictManageCard{}
	pmCard.buildFeeInfo(context.TODO(), adapter)
}

func TestFastRangeBuildFeeInfo(t *testing.T) {
	quation := biz_runtime.Quotation{
		ProductCategory: 328,
	}

	OrderedFeeInfo := map[string]float64{}
	OrderedFeeInfo["min_price"] = 2
	OrderedFeeInfo["max_price"] = 5
	marshal, _ := json.Marshal(OrderedFeeInfo)

	// 模拟OrderInfoService
	orderInfoService := &order_info.OrderInfoService{}

	// 创建一个实际的 card.Input 对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	adapter := NewAdapter(quation, input, dos.RequiredProductStruct{
		PassengerBargainRangeInfo: string(marshal),
	}, &RenderBaseData{})

	pmCard := PredictManageCard{}
	pmCard.buildFeeInfo(context.TODO(), adapter)
}

func TestCarpoolLowPriceBuildFeeInfo1(t *testing.T) {
	quation := biz_runtime.Quotation{
		ProductCategory: 46,
		CarpoolSceneBill: &PriceApi.CarpoolSceneBill{
			ScenePrice: nil,
			},
		}

	scenePrice := make([]*PriceApi.CarpoolScenePrice, 0)
	scenePrice = append(scenePrice, &PriceApi.CarpoolScenePrice{
		Option: &PriceApi.CarpoolPriceSceneOption{IsCarpoolSuccess: true, PoolNum: 1, SeatNum: 1},
		EstimateFee: 20.0,
	})
	scenePrice = append(scenePrice, &PriceApi.CarpoolScenePrice{
		Option: &PriceApi.CarpoolPriceSceneOption{IsCarpoolSuccess: true, PoolNum: 2, SeatNum: 1},
		EstimateFee: 18.0,
	})

	quation.CarpoolSceneBill.ScenePrice = scenePrice

	// Mock DCMP.GetDcmpContent
	dcmpMocker := mockey.Mock(dcmp.GetDcmpContent).Return("\"seat_1\": \"拼2人{{carpool_2}}元 | 拼1人{{carpool_1}}元\",\n  \"seat_2\": \"拼1人{{carpool_1}}元\",\n  \"seat_1_same\": \"拼成{{carpool_1}}元\"").Build()
	defer dcmpMocker.UnPatch()
	// 模拟OrderInfoService
	orderInfoService := &order_info.OrderInfoService{}

	// 创建一个实际的 card.Input 对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	adapter := NewAdapter(quation, input, dos.RequiredProductStruct{
		PassengerCount: 1,
	}, &RenderBaseData{})

	pmCard := PredictManageCard{}
	_, feeAmount, _ := pmCard.buildFeeInfo(context.TODO(), adapter)
	assert.Equal(t, "18", feeAmount)
}

func TestCarpoolLowPriceBuildFeeInfo2(t *testing.T) {
	quation := biz_runtime.Quotation{
		ProductCategory: 46,
		CarpoolSceneBill: &PriceApi.CarpoolSceneBill{
			ScenePrice: nil,
		},
	}

	scenePrice := make([]*PriceApi.CarpoolScenePrice, 0)
	scenePrice = append(scenePrice, &PriceApi.CarpoolScenePrice{
		Option: &PriceApi.CarpoolPriceSceneOption{IsCarpoolSuccess: true, PoolNum: 1, SeatNum: 2},
		EstimateFee: 20.0,
	})

	quation.CarpoolSceneBill.ScenePrice = scenePrice
	// 模拟OrderInfoService
	orderInfoService := &order_info.OrderInfoService{}

	// 创建一个实际的 card.Input 对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	adapter := NewAdapter(quation, input, dos.RequiredProductStruct{
		PassengerCount: 2,
	}, &RenderBaseData{})

	pmCard := PredictManageCard{}
	_, feeAmount, _ := pmCard.buildFeeInfo(context.TODO(), adapter)
	assert.Equal(t, "20", feeAmount)
}

// 测试RegisterRPC方法
func TestRegisterRPC(t *testing.T) {
	pmCard := &PredictManageCard{}

	// 执行测试
	result := pmCard.RegisterRPC()

	// 验证结果
	assert.NotNil(t, result, "返回的RPC列表不应为nil")
	assert.Contains(t, result, consts.RPCOrderInfo, "应包含OrderInfo RPC")
}

// 测试IsCanRender方法 - 无OrderInfo的情况
func TestIsCanRenderNoOrderInfo(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 使用mockey来mock IsCanRender方法内部对order_info.OrderInfoService的依赖
	isCanRenderMocker := mockey.Mock((*PredictManageCard).IsCanRender).To(func(_ *PredictManageCard, ctx context.Context, input *card.Input) bool {
		if input.OrderInfoService.GetOrderInfo() == nil {
			return false
		}
		return true
	}).Build()
	defer isCanRenderMocker.UnPatch()

	// 创建一个测试用的input
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: order_info.NewOrderInfoService(nil),
	}

	// 模拟GetOrderInfo返回nil
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(nil).Build()
	defer getOrderInfoMocker.UnPatch()

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "没有OrderInfo应返回false")
}

// 测试IsCanRender方法 - 空QuotationMap的情况
func TestIsCanRenderEmptyQuotationMap(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	orderInfo := &order_info.SimpleOrderInfo{}
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建一个空的 map[string]PriceApi.EstimateQuotation
	emptyQuotations := make(map[string]PriceApi.EstimateQuotation)
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(emptyQuotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "空QuotationMap应返回false")
}

// 测试IsCanRender方法 - IsAnycar="1"但数量不一致的情况
func TestIsCanRenderAnycarCountMismatch(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 创建模拟订单信息
	orderInfo := &order_info.SimpleOrderInfo{
		IsAnycar: "1",
		ExtendFeatureParsed: dos.ExtendFeatureStruct{
			MultiRequiredProduct: map[string]dos.RequiredProductStruct{
				"key1": {},
				"key2": {},
			},
		},
	}

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建不一致数量的 map[string]PriceApi.EstimateQuotation
	quotations := map[string]PriceApi.EstimateQuotation{
		"key1": {},
	}
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(quotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "IsAnycar='1'但数量不一致应返回false")
}

// 测试IsCanRender方法 - 非IsAnycar但QuotationMap数量不为1的情况
func TestIsCanRenderNonAnycarQuotationCountMismatch(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 创建模拟订单信息
	orderInfo := &order_info.SimpleOrderInfo{
		IsAnycar: "0",
	}

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建多于1个元素的 map[string]PriceApi.EstimateQuotation
	quotations := map[string]PriceApi.EstimateQuotation{
		"key1": {},
		"key2": {},
	}
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(quotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "非IsAnycar但QuotationMap数量不为1应返回false")
}

// 测试IsCanRender方法 - DCMP配置解析失败的情况
func TestIsCanRenderDCMPError(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 创建模拟订单信息
	orderInfo := &order_info.SimpleOrderInfo{
		IsAnycar: "0",
	}

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建只有1个元素的 map[string]PriceApi.EstimateQuotation
	quotations := map[string]PriceApi.EstimateQuotation{
		"key1": {},
	}
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(quotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// Mock DCMP.GetDcmpContent
	dcmpMocker := mockey.Mock(dcmp.GetDcmpContent).Return("{invalid json}").Build()
	defer dcmpMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "DCMP配置解析失败应返回false")
}

// 测试IsCanRender方法 - 满足所有条件的情况
func TestIsCanRenderSuccess(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 创建模拟订单信息
	orderInfo := &order_info.SimpleOrderInfo{
		IsAnycar: "0",
	}

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建只有1个元素的 map[string]PriceApi.EstimateQuotation
	quotations := map[string]PriceApi.EstimateQuotation{
		"key1": {},
	}
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(quotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// Mock DCMP获取
	config := &PredictManagerCardConfig{
		MainTitle: "正在同时呼叫{{carCount}}种车型",
	}
	configJSON, _ := json.Marshal(config)
	dcmpMocker := mockey.Mock(dcmp.GetDcmpContent).Return(string(configJSON)).Build()
	defer dcmpMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.True(t, result, "满足所有条件应返回true")
	assert.Equal(t, config.MainTitle, pmCard.DCMPConfig.MainTitle, "DCMPConfig应被正确设置")
}
