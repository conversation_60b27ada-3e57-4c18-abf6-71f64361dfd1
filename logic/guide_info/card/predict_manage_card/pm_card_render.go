package predict_manage_card

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gobiz/ctxutil"
	Currency "git.xiaojukeji.com/gulfstream/biz-common-go/v6/currency"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/data/order_info"
	carpool2 "git.xiaojukeji.com/s3e/pts/carpool"
	"strconv"

	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	bargain_range "git.xiaojukeji.com/gulfstream/mamba/logic/bargain_range_estimate/bargain"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool"
	util2 "git.xiaojukeji.com/gulfstream/passenger-common/util"
	"github.com/tidwall/gjson"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/route_type"
	taxi_util "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/card"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic"
	"github.com/spf13/cast"
)

type RenderBaseData struct {
	tpInfo map[int64]string

	PricingBoxData *taxi_util.PricingBoxData

	estimateMin  float64
	estimateMax  float64
	redPacketFee float64

	carCount   int // 车型个数
	FeeMsg     string
	FeeAmount  string
	MultiPrice []*proto.NewFormMultiPrice

	IsBusinessPay bool
}

func (p *PredictManageCard) buildSubTitleList1(ctx context.Context, renderBaseData *RenderBaseData, carList []*proto.CarData, input *card.Input) []string {
	var subTitleList1 = []string{
		p.buildSubTitleSelectInfo(ctx, renderBaseData, carList), // 已选3种车型
	}

	if renderBaseData.carCount == 1 {
		if len(renderBaseData.MultiPrice) > 0 {
			// 多口价
			for _, multiPrice := range renderBaseData.MultiPrice {
				subTitleList1 = append(subTitleList1, multiPrice.FeeMsg)
			}
		} else {
			// 单车型  一口价/预估价/个人付
			subTitleList1 = append(subTitleList1, renderBaseData.FeeMsg)
		}
	} else {
		// 预估xx-yy元
		subTitleList1 = append(subTitleList1, p.buildEstimateInfo(ctx, renderBaseData, input))
	}

	return subTitleList1
}

func (p *PredictManageCard) buildCarIcon(ctx context.Context, provider *Adapter) string {
	if provider.PageType == page_type.PageTypeNewEnergyCar {
		newEnergyCarIcon := dcmp.GetJSONContentWithPath(ctx, "predict_manage_card-new_energy_car_icon", nil, util.ToString(provider.GetProductCategory()))
		if newEnergyCarIcon != "" {
			return newEnergyCarIcon
		}
	}

	return car_info.GetCarNormalIcon(ctx, provider)
}

func (p *PredictManageCard) buildCarName(ctx context.Context, provider *Adapter, baseData *RenderBaseData) string {
	if provider.PageType == page_type.PageTypeNewEnergyCar {
		newEnergyCarName := dcmp.GetJSONContentWithPath(ctx, "predict_manage_card-new_energy_car_name", nil, util.ToString(provider.GetProductCategory()))
		if newEnergyCarName != "" {
			return newEnergyCarName
		}
	}

	if baseData.PricingBoxData != nil {
		if !baseData.PricingBoxData.EnableRangeStyle {
			if provider.GetProductCategory() == estimate_pc_id.EstimatePcIdUnione {
				return baseData.PricingBoxData.Conf.V3LayoutConf.Title
			}
			if provider.GetProductCategory() == 188 {
				return baseData.PricingBoxData.Conf.V3LayoutConf.Title
			}
		} else {
			pcid := cast.ToString(provider.GetProductCategory())
			if provider.GetProductCategory() == estimate_pc_id.EstimatePcIdUnione {
				return baseData.PricingBoxData.Conf.V3LayoutConf.Title + "-" + baseData.PricingBoxData.Conf.V3EstimateConf[pcid].Title
			}
			if provider.GetProductCategory() == 188 {
				return baseData.PricingBoxData.Conf.V3LayoutConf.Title + "-" + baseData.PricingBoxData.Conf.V3EstimateConf[pcid].Title
			}
		}
	}

	if len(baseData.tpInfo) > 0 && len(baseData.tpInfo[provider.GetProductCategory()]) > 0 && len(p.DCMPConfig.TPName) > 0 {
		pre := p.DCMPConfig.TPName[fmt.Sprintf("%d", provider.GetProductCategory())]
		return pre + baseData.tpInfo[provider.GetProductCategory()]
	}
	// 景区接驳改名
	if provider.GetRouteType() == route_type.RouteTypeCarpoolShuttleBus {
		return fmt.Sprintf(consts.ShuttleBusCarName, provider.RequiredProductStruct.PassengerCount)
	}

	return car_info.GetNormalCarName(ctx, provider)
}

func (p *PredictManageCard) buildFeeInfo(ctx context.Context, provider *Adapter) (string, string, []string) {
	if provider.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain {
		// 司乘议价
		currencyUnit, currencySymbol := util.GetCurrencyUnitAndSymbol(ctx, provider.GetProductId(), provider.GetBillInfoCurrency())
		tag := map[string]string{
			"total_fee":       util.Float64ToString(provider.RequiredProductStruct.CapPrice + provider.RedPacket),
			"currency_unit":   currencyUnit,
			"currency_symbol": currencySymbol,
		}
		return dcmp.GetDcmpContent(ctx, "common-price_desc", tag), util.Float64ToString(provider.RequiredProductStruct.CapPrice + provider.RedPacket), []string{}
	}

	if provider.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiCarpool &&
		provider.GetQuotationPageType() != nil &&
		*provider.GetQuotationPageType() == page_type.PageTypeLankeBao {
		// 出租车拼车揽客宝
		msg := dcmp.GetDcmpContent(ctx, "unione-carpool_lankebao_fee_msg", nil)
		if len(msg) > 0 {
			return msg, util.Float64ToString(provider.EstimateFee), []string{}
		}
	}

	if provider.GetProductCategory() == estimate_pc_id.EstimatePcIdHuiXuanCar && provider.RequiredProductStruct.PassengerBargainRangeInfo != "" {
		redPacket := provider.RedPacket
		// 惠选车
		rangePriceInfo := provider.RequiredProductStruct.PassengerBargainRangeInfo
		leftPrice, err1 := bargain_range.GetBargainRangeOrderPrice(rangePriceInfo, consts.MinPrice)
		rightPrice, err2 := bargain_range.GetBargainRangeOrderPrice(rangePriceInfo, consts.MaxPrice)

		leftPrice = util.Float64Add(leftPrice, redPacket)
		rightPrice = util.Float64Add(rightPrice, redPacket)

		if err1 == nil && err2 == nil {
			tag := map[string]string{
				consts.MinPrice: util.Float64ToString(leftPrice),
				consts.MaxPrice: util.Float64ToString(rightPrice),
			}
			return dcmp.GetDcmpContent(ctx, "common-fast_range_fee_msg", tag), util.Float64ToString(rightPrice), []string{util.Float64ToString(leftPrice), util.Float64ToString(rightPrice)}
		}
	}

	if provider.GetProductCategory() == estimate_pc_id.EstimatePcIdAplus {
		feeInfoResp := fee_info_render.GetAPlusFeeInfo(ctx, provider)
		var multiPrice []string
		if feeInfoResp.MinFeeAmount != "" {
			multiPrice = append(multiPrice, feeInfoResp.MinFeeAmount)
		}
		return feeInfoResp.FeeMsg, feeInfoResp.FeeAmount, multiPrice
	}

	if provider.GetProductCategory() == estimate_pc_id.EstimatePcIdLowPriceCarpool {
		extendList := provider.GetCarpoolScenePrice()
		var multiPrice []string
		var feeMsg string
		if extendList != nil && len(extendList) > 0 {
			var (
				pool_1        float64 // 拼1人价
				pool_2        float64 // 拼2人价
				no_pool       float64 // 未拼成价
				minSuccessFee float64
			)
			content := ""
			for _, item := range extendList {
				if item.Option == nil {
					continue
				}
				if item.Option.IsCarpoolSuccess {
					if item.Option.PoolNum == 1 {
						pool_1 = carpool3.FormatPrice(item.EstimateFee, carpool3.PageDefault, util.ToString(provider.GetCityID()), util.Int642String(provider.GetUserPID()), util.RoundAbs(item.EstimateFee, 1)).FloatVal
					} else if item.Option.PoolNum == 2 {
						pool_2 = carpool3.FormatPrice(item.EstimateFee, carpool3.PageDefault, util.ToString(provider.GetCityID()), util.Int642String(provider.GetUserPID()), util.RoundAbs(item.EstimateFee, 1)).FloatVal
					}

					if item.Option.SeatNum == 1 {
						minSuccessFee = pool_2
						content = gjson.Get(dcmp.GetDcmpContent(ctx, "common-pcl_fee_msg", nil), "seat_1").String()
					} else if item.Option.SeatNum == 2 {
						minSuccessFee = pool_1
						content = gjson.Get(dcmp.GetDcmpContent(ctx, "common-pcl_fee_msg", nil), "seat_2").String()
					}

				} else {
					// 未拼成价 1座/2座 都只有一个未拼成价，直接取
					no_pool = carpool3.FormatPrice(item.EstimateFee, carpool3.PageDefault, util.ToString(provider.GetCityID()), util.Int642String(provider.GetUserPID()), util.RoundAbs(item.EstimateFee, 1)).FloatVal
				}
			}

			if pool_1 == pool_2 {
				content = gjson.Get(dcmp.GetDcmpContent(ctx, "common-pcl_fee_msg", nil), "seat_1_same").String()
			}

			// 点击边走边拼, 显示未拼成价
			if carpool2.IsPoolInTripCarpool(ctx,
				cast.ToInt(provider.GetRequireLevel()),
				int(provider.GetComboType()),
				int(provider.GetProductId()),
				int(provider.GetCarpoolType()),
				int(provider.GetOrderType()),
				int(provider.GetCarpoolPriceType()),
				provider.RequiredProductStruct.CarpoolRequireNum) {
				content = gjson.Get(dcmp.GetDcmpContent(ctx, "common-pcl_fee_msg", nil), "pool_in_trip").String()
			}

			feeMsg = util.ReplaceTag(ctx, content, map[string]string{
				"carpool_1":  cast.ToString(pool_1),
				"carpool_2":  cast.ToString(pool_2),
				"no_carpool": cast.ToString(no_pool),
			})
			return feeMsg, util.Float64ToString(minSuccessFee), multiPrice
		}
		return feeMsg, "", multiPrice
	}

	feeMsg, feeAmount, _ := fee_info_render.GetFeeInfo(ctx, provider)
	return feeMsg, feeAmount, []string{}
}

func (p *PredictManageCard) buildSubTitleSelectInfo(ctx context.Context, renderBaseData *RenderBaseData, carList []*proto.CarData) string {
	if renderBaseData.carCount == 1 {
		return util.ReplaceTag(ctx, p.DCMPConfig.SubTitleSelectInfoByName, map[string]string{
			"carName": carList[0].LeftInfo.CarName,
		})
	}

	return util.ReplaceTag(ctx, p.DCMPConfig.SubTitleSelectInfo, map[string]string{
		"carCount": strconv.Itoa(renderBaseData.carCount),
	})
}

func (p *PredictManageCard) buildSubTile(ctx context.Context, input *card.Input, renderBaseData *RenderBaseData) string {
	if renderBaseData.redPacketFee > 0 {
		params := map[string]string{
			"key":   strconv.FormatInt(input.Req.Pid, 10),
			"phone": input.Req.Phone,
			"city":  strconv.FormatInt(int64(input.Req.FromCityId), 10),
		}
		isShow := apollo.FeatureToggle(ctx, "gs_holiday_fee_fee_desc", input.Req.Phone, params)
		if isShow {
			return util.ReplaceTag(ctx, p.DCMPConfig.PopSubTitle, map[string]string{
				"price_range": util.Float64ToString(renderBaseData.redPacketFee),
			})
		}
	}
	return ""
}

func (p *PredictManageCard) buildEstimateInfo(ctx context.Context, data *RenderBaseData, input *card.Input) string {

	// price_range
	var priceRange string
	minString := util.Float64ToString(data.estimateMin)
	maxString := util.Float64ToString(data.estimateMax)
	if minString == maxString {
		priceRange = maxString
	} else {
		priceRange = util.ReplaceTag(ctx, p.DCMPConfig.PriceRange, map[string]string{
			"min": minString,
			"max": maxString,
		})
	}

	// 处理企业付
	if data.IsBusinessPay {
		return util.ReplaceTag(ctx, p.DCMPConfig.MixPersonal, map[string]string{
			"price_range": priceRange,
		})
	}

	// 预估{{price_range}}元
	localCurrency := input.OrderInfoService.GetOrderInfo().LocalCurrency
	if len(localCurrency) == 0 {
		localCurrency = Currency.CNY
	}
	ctx = ctxutil.SetLang(ctx, input.Req.Lang)
	unit, _ := Currency.GetCurrencyUnit(ctx, localCurrency)
	return util.ReplaceTag(ctx, p.DCMPConfig.SubTitleEstimateInfoV2, map[string]string{
		"price_range": priceRange,
		"unit":        unit,
	})
}

func (p *PredictManageCard) buildMainTitle(ctx context.Context, carCount int) string {
	return util.ReplaceTag(ctx, p.DCMPConfig.MainTitle, map[string]string{
		"carCount": strconv.Itoa(carCount),
	})
}

func (p *PredictManageCard) buildMultiPriceDesc(ctx context.Context, provider *Adapter) (descList []*proto.NewFormMultiPrice) {
	if provider.GetRouteType() == route_type.RouteTypeCarpoolShuttleBus {
		estimatePriceDesc := &proto.NewFormMultiPrice{
			FeeMsg: util.ReplaceTag(ctx, consts.ShuttleBusPriceDesc, map[string]string{
				"num": cast.ToString(provider.RequiredProductStruct.CapPrice),
			}),
			FeeAmount: provider.RequiredProductStruct.CapPrice,
		}
		descList = append(descList, estimatePriceDesc)
		return
	}
	if provider.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
		//站点拼车独有拼友推荐文案
		if provider.IsBusinessPay() {
			//企业付文案优先级更高
			goto ReturnValue
		}
		// 站点拼车独有 拼友推荐文案
		if p.ufsRPC == nil {
			goto ReturnValue
		}
		//从ufs判断是否展示
		if p.ufsRPC.Fetch(ctx) {
			if p.ufsRPC.AlwaysResult {
				successFee, err := util.FormatPriceCeilFloat64(provider.GetEstimateFee(), 1)
				if err != nil {
					successFee = provider.GetEstimateFee()
				}
				successFee = carpool3.FormatPrice(provider.GetEstimateFee(), carpool3.PageDefault, util.ToString(provider.GetCityID()), util.Int642String(provider.GetUserPID()), successFee).FloatVal
				successDesc := &proto.NewFormMultiPrice{
					FeeMsg: util.ReplaceTag(ctx, p.DCMPConfig.AlwaysCarpool, map[string]string{
						"price_range": cast.ToString(successFee),
					}),
					FeeAmount: successFee,
				}

				if redPacketDesc := common_logic.GetHolidayFee(ctx, provider.GetBillFeeDetailInfo()); redPacketDesc == nil {
					successCouponInfo := provider.GetCouponInfo()
					setCouponInfo(ctx, successCouponInfo, successDesc.FeeDesc)
				}
				descList = append(descList, successDesc)
				return
			}
		}
	}

ReturnValue:
	return fee_info_render.GetMultiPriceDescSation(ctx, provider)
}

func (p *PredictManageCard) initTp(order *order_info.SimpleOrderInfo) (res map[int64]string) {
	res = make(map[int64]string)
	if len(order.ExtendFeatureParsed.TPInfo.TPInfoList) > 0 {
		for _, info := range order.ExtendFeatureParsed.TPInfo.TPInfoList {
			res[info.PcID] = info.ETPText
		}
	}
	return
}

func setCouponInfo(ctx context.Context, info *PriceApi.EstimateNewFormCouponInfo, successDesc *proto.NewFormFeeDesc) {
	if info != nil {
		couponAmount, err := strconv.ParseFloat(info.Amount, 64)
		if err != nil || couponAmount == 0 {
			return
		}
		couponAmount = couponAmount / 100

		if config := dcmp.GetJSONContentWithPath(ctx, "anycar_v3-coupon_custom_tag", nil, "default"); config != "" {
			var tagMap common_logic.CustomTagMap
			err := json.Unmarshal([]byte(config), &tagMap)
			if err != nil {
				return
			}

			tag := map[string]string{
				"amount": util.FormatPrice(couponAmount, -1),
			}
			successDesc = &proto.NewFormFeeDesc{
				BorderColor: tagMap.BorderColor,
				Content:     util.ReplaceTag(ctx, tagMap.Content, tag),
				Icon:        tagMap.Icon,
				Amount:      couponAmount,
			}
		}

	}
}

func (p *PredictManageCard) buildFeeDescList(ctx context.Context, provider *Adapter) []*proto.NewFormFeeDesc {
	var (
		feeDescItem *proto.NewFormFeeDesc
		feeDescList = make([]*proto.NewFormFeeDesc, 0)
	)

	//两口价
	if provider.GetProductCategory() == ProductCategory.ProductCategoryCarpoolStation {
		return getDualPriceFeeDescList(ctx, provider, page_type.PageTypeGuideAnyCar)
	}

	couponInfo := provider.GetCouponInfo()
	if couponInfo == nil || couponInfo.CustomTag == "" {
		return nil
	}

	// 仅展示特定custom_tag的感知
	if conf := dcmp.GetJSONMap(ctx, "predict_manage_card-fee_desc_list_coupon_info", couponInfo.CustomTag); len(conf) > 0 {
		textColor := conf["text_color"].String()
		highlightColor := conf["highlight_color"].String()
		feeDescItem = &proto.NewFormFeeDesc{
			BorderColor:    conf["border_color"].String(),
			Content:        util.ReplaceTag(ctx, conf["content"].String(), map[string]string{"num": couponInfo.Amount}),
			Icon:           conf["icon"].String(),
			TextColor:      &textColor,
			HighlightColor: &highlightColor,
		}
		feeDescList = append(feeDescList, feeDescItem)
		return feeDescList
	}

	return nil
}

// getDualPriceFeeDescList 等应答车型弹窗列表 两口价fee_desc_list。目前只渲染券相关，所以把券渲染逻辑直接写在这个logic上
func getDualPriceFeeDescList(ctx context.Context, prov *Adapter, pageType int32) []*proto.NewFormFeeDesc {
	var (
		feeDescItem *proto.NewFormFeeDesc
		feeDescList = make([]*proto.NewFormFeeDesc, 0)
	)

	var (
		discountDescSuc  []*PriceApi.DiscountItem
		discountDescFail []*PriceApi.DiscountItem

		couponDescSuc  *PriceApi.DiscountItem
		couponDescFail *PriceApi.DiscountItem
	)
	if carpool.CarPoolPriceTypeDefault != prov.RequiredProductStruct.CarpoolPriceType {
		return nil
	}

	if nil == prov || nil == prov.Quotation || nil == prov.Quotation.CarpoolSceneBill || nil == prov.Quotation.CarpoolSceneBill.ScenePrice {
		return nil
	}

	//1、数据获取
	scenePrice := prov.Quotation.CarpoolSceneBill.ScenePrice
	for _, item := range scenePrice {
		if item.Option.IsCarpoolSuccess && cast.ToInt64(item.Option.SeatNum) == prov.CarpoolSeatNum {
			discountDescSuc = item.DiscountItem
		}
		if !item.Option.IsCarpoolSuccess && cast.ToInt64(item.Option.SeatNum) == prov.CarpoolSeatNum {
			discountDescFail = item.DiscountItem
		}
	}

	for _, desc := range discountDescSuc {
		if desc.Type == "coupon" {
			couponDescSuc = desc
			break
		}
	}

	for _, desc := range discountDescFail {
		if desc.Type == "coupon" {
			couponDescFail = desc
			break
		}
	}

	//2、渲染。拼成存在指定券tag，根据拼成下券tag进行渲染；反之，若未拼成下存在指定tag，按照未拼成券tag渲染
	customCouponKeyPath := func(priceMerge bool, couponDescSuc, couponDescFail *PriceApi.DiscountItem, customTagList []string) (string, string) {
		dcmpKey := "anycar_estimate-custom_tag_coupon"
		path := util2.Ternary(priceMerge, "merge-", "").(string) //NOLINT

		customTag := ""
		switch true {
		case nil != couponDescSuc && util.InArrayStr(couponDescSuc.CustomTag, customTagList):
			customTag = couponDescSuc.CustomTag
		case nil != couponDescFail && util.InArrayStr(couponDescFail.CustomTag, customTagList):
			customTag = couponDescFail.CustomTag
		}

		if "" == customTag {
			return "", ""
		}

		switch true {
		case nil != couponDescSuc && nil == couponDescFail:
			path += customTag + common_logic.SuffixCarpoolSuc
		case nil == couponDescSuc && nil != couponDescFail:
			path += customTag + common_logic.SuffixCarpoolFail
		case nil != couponDescSuc && nil != couponDescFail:
			path += customTag + common_logic.SuffixCarpoolDual
		default:
			path = ""
		}

		return dcmpKey, path
	}

	renderData := func(couponDescSuc, couponDescFail *PriceApi.DiscountItem, pageType int32) (string, string, string, string) {
		sucBeforeFail := common_logic.DualPriceOrderedOnAscInner(prov)
		suc := util2.TernaryDelayExec(nil == couponDescSuc, func() interface{} { return float64(0) }, func() interface{} { return couponDescSuc.Amount }).(float64)    //NOLINT
		fail := util2.TernaryDelayExec(nil == couponDescFail, func() interface{} { return float64(0) }, func() interface{} { return couponDescFail.Amount }).(float64) //NOLINT
		price1 := util2.Ternary(sucBeforeFail, suc, fail).(float64)                                                                                                    //NOLINT
		price2 := util2.Ternary(sucBeforeFail, fail, suc).(float64)                                                                                                    //NOLINT
		return util.Float64ToString(suc), util.Float64ToString(fail), util.Float64ToString(price1), util.Float64ToString(price2)

	}

	key, path := customCouponKeyPath(prov.IsCarpoolV3Merge(ctx), couponDescSuc, couponDescFail, []string{"add_select_bind_coupon"})
	sucPrice, failPrice, price1, price2 := renderData(couponDescSuc, couponDescFail, pageType)

	if key == "" {
		return nil
	}

	conf := dcmp.GetJSONMap(ctx, key, path)
	if len(conf) <= 0 {
		return nil
	}

	textColor := conf["text_color"].String()
	highlightColor := conf["highlight_color"].String()
	feeDescItem = &proto.NewFormFeeDesc{
		BorderColor: conf["border_color"].String(),
		Content: util.ReplaceTag(ctx, conf["content"].String(), map[string]string{
			"suc_num":  sucPrice,
			"fail_num": failPrice,
			"num_1":    price1,
			"num_2":    price2,
		}),
		Icon:           conf["icon"].String(),
		TextColor:      &textColor,
		HighlightColor: &highlightColor,
	}
	feeDescList = append(feeDescList, feeDescItem)
	return feeDescList
}
