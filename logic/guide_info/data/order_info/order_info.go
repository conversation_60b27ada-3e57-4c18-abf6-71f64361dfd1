package order_info

import (
	"context"
	"encoding/json"
	"fmt"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

func FetchOrderInfo(ctx context.Context, serviceInterface interface{}) error {
	service, ok := serviceInterface.(*OrderInfoService)
	if !ok {
		return fmt.Errorf("base input not OrderInfoService")
	}

	// 获取dos数据
	service.orderInfo = service.fetchDos(ctx)

	// 获取报价单
	service.getQuotationData(ctx)

	return nil
}

type OrderInfoService struct {
	req *proto.PGetGuideInfoReq

	orderInfo            *SimpleOrderInfo
	estimateQuotationMap map[string]PriceApi.EstimateQuotation
}

type SimpleOrderInfo struct {
	OrderId string
	// 基本信息
	IsAnycar     string
	EstimateId   string
	ProductId    string
	RequireLevel string
	ComboType    string

	County string

	// 货币信息
	LocalCurrency string

	// 扩展信息
	ExtendFeatureParsed dos.ExtendFeatureStruct
}

func NewOrderInfoService(req *proto.PGetGuideInfoReq) *OrderInfoService {

	return &OrderInfoService{req: req}
}
func (o *OrderInfoService) GetOrderInfo() *SimpleOrderInfo {
	return o.orderInfo
}
func (o *OrderInfoService) GetEstimateQuotationMap() map[string]PriceApi.EstimateQuotation {
	return o.estimateQuotationMap
}

func (o *OrderInfoService) fetchDos(ctx context.Context) *SimpleOrderInfo {
	if o.req != nil && o.req.SimpleOrderInfo != nil {
		var extendFeatureStruct dos.ExtendFeatureStruct
		err := json.Unmarshal([]byte(o.req.SimpleOrderInfo.ExtendFeature), &extendFeatureStruct)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "extendFeatureStruct unmarshal  err||err=%v", err)
			return nil
		}

		return &SimpleOrderInfo{
			OrderId:             o.req.SimpleOrderInfo.OrderId,
			IsAnycar:            o.req.SimpleOrderInfo.IsAnycar,
			EstimateId:          o.req.SimpleOrderInfo.EstimateId,
			ProductId:           o.req.SimpleOrderInfo.ProductId,
			RequireLevel:        o.req.SimpleOrderInfo.RequireLevel,
			ComboType:           o.req.SimpleOrderInfo.ComboType,
			County:              o.req.SimpleOrderInfo.County,
			LocalCurrency:       o.req.SimpleOrderInfo.LocalCurrency,
			ExtendFeatureParsed: extendFeatureStruct,
		}
	}

	// 实时获取
	orderID, district, err := util.DecodeOrderID(o.req.OrderId)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "decodeErr: %v", err)
		return nil
	}

	// 请求 dos
	orderInfo, err := dos.GetOrderInfo(ctx, orderID, district)
	if err != nil {
		// log.Trace.Errorf(ctx, trace.DLTagUndefined, "getOrderInfo: %v", err)
		return nil
	}

	return &SimpleOrderInfo{
		OrderId:             orderInfo.OrderId,
		IsAnycar:            orderInfo.IsAnycar,
		EstimateId:          orderInfo.EstimateId,
		ProductId:           orderInfo.ProductId,
		RequireLevel:        orderInfo.RequireLevel,
		ComboType:           orderInfo.ComboType,
		County:              orderInfo.County,
		LocalCurrency:       orderInfo.LocalCurrency,
		ExtendFeatureParsed: orderInfo.ExtendFeatureParsed,
	}
}
