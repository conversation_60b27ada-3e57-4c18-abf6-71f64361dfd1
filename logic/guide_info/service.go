package guide_info

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/card"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/card/predict_manage_card"
	"git.xiaojukeji.com/nuwa/trace/v2"
)

func Handler(ctx context.Context, req *proto.PGetGuideInfoReq) (*proto.GuideInfoData, int) {
	var (
		respData = new(proto.GuideInfoData)
		cardList = GetCardList() // 卡片列表
		input    = NewInput(req) // 数据仓库
	)
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
		} else {
			AddPublicLog(ctx, input, respData, cardList)
		}
	}()

	// 处理基础数据
	done := concurrentBuildData(ctx, cardList, input)
	if done {
		return respData, consts.ErrnoConcurrentErr
	}

	// todo  并行构建卡片
	for _, cardInfo := range cardList {
		if cardInfo.IsCanRender(ctx, input) {
			cardInfo.RenderResponse(ctx, input, respData)
		}
	}

	return respData, 0
}

func GetCardList() []card.BaseInterface {
	var cardList []card.BaseInterface

	cardList = append(cardList, &predict_manage_card.PredictManageCard{})

	//cardList = append(cardList, &anycar_card.AnyCarV3Card{})

	return cardList
}
