package guide_info

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/card"
)

const OperaKeyEstimateData = "g_guide_info"

func AddPublicLog(ctx context.Context, input *card.Input, data *proto.GuideInfoData, cardList []card.BaseInterface) {

	for _, cardObject := range cardList {

		logInfo := cardObject.GetCardLog(ctx, input, data)
		if len(logInfo) <= 0 {
			continue
		}

		log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
	}
}
