package internal

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	midl "git.xiaojukeji.com/dirpc/dirpc-go-http-Member"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/client_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_point_estimate/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/member_info"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"github.com/spf13/cast"
)

type priceInfo struct {
	req          *proto.MultiPointEstimateRequest
	products     []*models.Product
	productsFull []*biz_runtime.ProductInfoFull
}

const Caller = "p_multi_point_estimate"

func NewPriceInfo(req *proto.MultiPointEstimateRequest, products []*models.Product) *priceInfo {
	return &priceInfo{
		req:      req,
		products: products,
	}
}

func (p *priceInfo) Do(ctx context.Context, data *model.CommonData) error {
	if data == nil || data.OrderInfo == nil {
		return errors.New("data is nil")
	}

	if util.InArrayInt(data.OrderInfo.GetOrderStatus(), []int{order_info.DriverGrabbing.ToInt(), order_info.ArrivingPoint.ToInt()}) {
		resp, err := price_api.GetMultiEstimatePriceForRoute(ctx, Caller, p.buildRequest(ctx, data), map[string]string{
			"is_use_discount": "1",
		})

		if err != nil {
			return err
		}

		// 路线价格校验
		err = p.checkPrice(ctx, resp)
		if err != nil {
			return err
		}

		p.formatRespByPickUp(ctx, resp)
	}

	return nil
}

// buildRequest 构建入参
func (p *priceInfo) buildRequest(ctx context.Context, data *model.CommonData) []*price_api.PriceEstimateReq {
	if p.products == nil {
		return nil
	}

	reqs := make([]*price_api.PriceEstimateReq, 0)

	for _, product := range p.products {
		req := &price_api.PriceEstimateReq{
			CommonInfoSt:  p.buildCommonInfoSt(ctx, product, data),
			OrderInfoSt:   p.buildOrderInfoSt(ctx, product, data),
			OrderExt:      p.buildOrderExtInfoSt(ctx, product, data),
			PassengerInfo: p.buildPassengerInfoSt(ctx, product, data),
			ExtraInfo:     p.buildExtraInfoSt(ctx, product, data),
		}

		reqs = append(reqs, req)
	}

	return reqs
}

// buildCommonInfoSt 构建通用数据
func (p *priceInfo) buildCommonInfoSt(ctx context.Context, product *models.Product, data *model.CommonData) *PriceApi.CommonInfoSt {
	if p.req == nil || product == nil {
		return nil
	}

	return &PriceApi.CommonInfoSt{
		TransparentData: "",
		BusinessId:      product.BusinessID,
		OriginId:        int64(util.Int32Ptr2Int32(p.req.OriginId)),
		AppVersion:      p.req.AppVersion,
		ClientType:      util.Int64Ptr2Int64(p.req.ClientType),
		Imei:            util.StringPtr2String(p.req.Imei),
		Lang:            p.req.Lang,
		DataType:        0,
		IsFromB2B:       client_type.IsFromB2B(int32(util.Int64Ptr2Int64(p.req.ClientType))),
		IsFromWebapp:    client_type.IsFromWebApp(int32(util.Int64Ptr2Int64(p.req.ClientType))),
		GuideRequest:    false,
		OpenId:          nil,
		Suuid:           nil,
		PlatformType:    p.req.PlatformType,
		TerminalId:      util.Int64Ptr2Int64(p.req.TerminalId),
		RouteId:         util.Int64Ptr(util.String2int64(ctx, product.RouteID)),
	}
}

// buildOrderInfoSt 构建订单信息
func (p *priceInfo) buildOrderInfoSt(ctx context.Context, product *models.Product, data *model.CommonData) *PriceApi.OrderInfoSt {
	if data == nil || data.OrderInfo == nil || data.AreaInfo == nil {
		return nil
	}

	curAreaInfo := data.AreaInfo[product.EstimateID]
	if curAreaInfo == nil {
		return nil
	}

	return &PriceApi.OrderInfoSt{
		CurrentLng:            curAreaInfo.FromLat,
		CurrentLat:            curAreaInfo.FromLng,
		Area:                  int64(curAreaInfo.Area),
		OrderType:             int64(product.OrderType),
		Channel:               data.OrderInfo.GetChannel(),
		FromLng:               curAreaInfo.FromLng,
		FromLat:               curAreaInfo.FromLat,
		FromPoiId:             curAreaInfo.FromPoiID,
		FromPoiType:           curAreaInfo.FromPoiType,
		FromAddress:           curAreaInfo.FromAddress,
		FromName:              curAreaInfo.FromName,
		ToLng:                 curAreaInfo.ToLng,
		ToLat:                 curAreaInfo.ToLat,
		ToPoiId:               curAreaInfo.ToPoiID,
		ToPoiType:             curAreaInfo.ToPoiType,
		ToAddress:             curAreaInfo.ToAddress,
		ToName:                curAreaInfo.ToName,
		SceneType:             product.SceneType,
		ComboType:             product.ComboType,
		ComboId:               data.OrderInfo.GetComboID(),
		DepartureTime:         time.Now().Unix(),
		CallCarType:           data.OrderInfo.GetCallCarType(),
		CallCarPhone:          "",
		WillingWait:           0,
		MapType:               util.StringPtr2String(p.req.MapType),
		ActivityId:            0,
		UserType:              int64(util.Int32Ptr2Int32(p.req.UserType)),
		PaymentsType:          data.OrderInfo.GetPayType(),
		GuideType:             0,
		GuideState:            0,
		ProductId:             product.ProductID,
		RequireLevel:          product.RequireLevel,
		IsFastCar:             product_id.IsFastCar(product.ProductID),
		CarpoolSeatNum:        data.OrderInfo.GetPassengerCount(),
		CarpoolStationType:    0,
		CarpoolRequireTraceId: "",
		CurrentStationId:      0,
		StartingName:          curAreaInfo.StartingName,
		DestName:              curAreaInfo.DestName,
		District:              util.StringPtr(curAreaInfo.District),
		AbstractDistrict:      util.StringPtr(curAreaInfo.AbstractDistrict),
		SeatDetailInfo:        nil,
	}
}

// buildOrderExtInfoSt 构建额外信息
func (p *priceInfo) buildOrderExtInfoSt(ctx context.Context, product *models.Product, data *model.CommonData) *price_api.OrderExt {
	tag := "buildOrderExtInfoSt"
	if data == nil || data.OrderInfo == nil || data.AreaInfo == nil || product == nil {
		return nil
	}

	curAreaInfo := data.AreaInfo[product.EstimateID]
	if curAreaInfo == nil {
		return nil
	}

	nTuple := &price_api.OrderNTuple{}

	if orderNTupleStr, err := json.Marshal(product); err == nil {
		err = json.Unmarshal(orderNTupleStr, nTuple)
		if err != nil {
			log.Trace.Warnf(ctx, tag, "n tuple unmarshal fail, err:%v", err)
		}
	}

	return &price_api.OrderExt{
		ActualComboType:           0,
		StartDestDistance:         0,
		Tip:                       0,
		CouponType:                0,
		ComboIDs:                  "",
		EstimateID:                product.EstimateID,
		MatchRoutes:               nil,
		AirportID:                 0,
		AirportCode:               "",
		GsAirport:                 0,
		ToArea:                    int64(curAreaInfo.ToArea),
		County:                    int64(curAreaInfo.FromCounty),
		ToCounty:                  int64(curAreaInfo.ToCounty),
		TripCountry:               curAreaInfo.TripCountry,
		AutoTrigger:               0,
		EstimateCarpoolOpen:       0,
		OrderNTuple:               nTuple,
		DepartureRange:            nil,
		IsSelectInsurance:         false,
		InterInsuranceInfo:        nil,
		AgentType:                 "",
		IsAthenaAddProduct:        false,
		CommuteRouteID:            "",
		InsuranceInfo:             nil,
		RecognitionResult:         nil,
		StationList:               nil,
		DesignatedDriver:          "",
		DesignatedDriverTag:       "",
		HotelID:                   0,
		StartBroadcastTime:        0,
		FilterCashPay:             0,
		StopoverPoints:            p.buildStopoverPoints(ctx, data),
		WayPointsVersion:          "",
		WayPointsAInfo:            "",
		WayPointsAStatus:          0,
		WayPointsBInfo:            "",
		WayPointsBStatus:          0,
		IsExtraStop:               0,
		RedPacket:                 0,
		RedPacketList:             nil,
		IsSpecialPrice:            product.IsSpecialPrice,
		MenuID:                    "",
		PersonalizedCustomOptions: nil,
		BubbleTime:                nil,
		ProductCategory:           product.ProductCategory,
		PreferredRouteId:          0,
		DepartureTimeBuffer:       0,
		AccessKeyID:               p.req.AccessKeyId,
		LevelType:                 data.OrderInfo.GetLevelType(),
		CommuteCardInfo:           nil,
		PageType:                  0,
		ExamType:                  product.ExamType,
		PricePrivilegeInfo:        nil,
		OrderID:                   data.OrderInfo.GetOrderID(),
	}
}

// buildPassengerInfoSt 构建用户信息
func (p *priceInfo) buildPassengerInfoSt(ctx context.Context, product *models.Product, data *model.CommonData) *price_api.PassengerInfo {
	if data == nil || data.UserInfo == nil {
		return nil
	}

	channel := util.String2Int(ctx, data.UserInfo.Channel)
	originID := util.String2Int(ctx, data.UserInfo.OriginId)

	memberProfileV1 := &midl.V1QueryInfo{}

	if memberProfile, ok := data.MemberInfo[member_info.GenKey(&GoMember.ProductInfo{
		BusinessId:   int32(product.BusinessID),
		ComboType:    int32(product.ComboType),
		CarpoolType:  int32(product.CarpoolType),
		RequireLevel: int32(product.RequireLevelInt),
	})]; ok {
		memberProfileV1 = memberProfile.ToV1()
	}

	return &price_api.PassengerInfo{
		UID:                  int64(data.UserInfo.UID),
		ID:                   int64(data.UserInfo.PID),
		Phone:                data.UserInfo.Phone,
		Channel:              channel,
		Status:               0,
		Role:                 int64(data.UserInfo.Role),
		Origin:               int64(originID),
		UserDpaSelected:      1,
		IsUserUseDpa:         false,
		MemberProfile:        memberProfileV1,
		MemberProductProfile: nil,
		FreeDistance:         0,
	}
}

// buildExtraInfoSt 构建附加信息
func (p *priceInfo) buildExtraInfoSt(ctx context.Context, product *models.Product, data *model.CommonData) map[string]string {
	extraInfo := make(map[string]string)

	extraInfo["product_category"] = cast.ToString(product.ProductCategory)
	extraInfo["order"] = util.JustJsonEncode(p.buildOrderExtInfoSt(ctx, product, data))

	return extraInfo
}

// formatRespByPickUp 格式化响应接驾阶段
func (p *priceInfo) formatRespByPickUp(ctx context.Context, resp *PriceApi.EstimateNewFormResponse) {
	if resp == nil {
		return
	}

	productFulls := make([]*biz_runtime.ProductInfoFull, 0)

	for _, product := range p.products {
		if product == nil {
			continue
		}

		productFull := &biz_runtime.ProductInfoFull{
			Product: product,
		}

		productFull.SetHu(resp.Data[product.EstimateID])
		productFulls = append(productFulls, productFull)
	}

	p.productsFull = productFulls
}

// GetProductsFull 获取products full
func (p *priceInfo) GetProductsFull() []*biz_runtime.ProductInfoFull {
	return p.productsFull
}

// checkPrice 检查价格
func (p *priceInfo) checkPrice(ctx context.Context, resp *PriceApi.EstimateNewFormResponse) error {
	if resp == nil {
		return errors.New("resp is nil")
	}

	if len(resp.Data) <= 0 {
		return errors.New("data is nil")
	}

	if len(resp.Data) != len(p.req.GetPointList()) {
		return errors.New("route list not match price list")
	}

	for _, data := range resp.Data {
		if data == nil {
			continue
		}

		if data.EstimateFee == 0 {
			return errors.New("estimate fee is zero")
		}
	}

	return nil
}

// buildStopoverPoints ...
func (p *priceInfo) buildStopoverPoints(ctx context.Context, data *model.CommonData) []*price_api.StopoverPointInfo {
	if data == nil || data.OrderInfo == nil {
		return nil
	}

	var (
		logTag = "buildStopoverPoints"

		stopoverPointList = make([]*price_api.StopoverPointInfo, 0)

		wayPointsAInfo   = data.OrderInfo.WayPointsAInfo
		wayPointsAStatus = data.OrderInfo.WayPointsAStatus
		wayPointsBInfo   = data.OrderInfo.WayPointsBInfo
		wayPointsBStatus = data.OrderInfo.WayPointsBStatus
	)

	if wayPointsAInfo != "" {
		wayPoint := &price_api.StopoverPointInfo{}
		err := json.Unmarshal([]byte(wayPointsAInfo), wayPoint)
		if err != nil {
			log.Trace.Warnf(ctx, logTag, "way point a info unmarshal fail, origin:%v, err:%v", wayPointsAInfo, err)
		}

		if wayPointsAStatus != "" {
			wayPoint.Status, err = strconv.Atoi(wayPointsAStatus)
			if err != nil {
				log.Trace.Warnf(ctx, logTag, "way point a status a to i fail, origin:%v, err:%v", wayPointsAStatus, err)
			}
		}

		stopoverPointList = append(stopoverPointList, wayPoint)
	}

	if wayPointsBInfo != "" {
		wayPoint := &price_api.StopoverPointInfo{}
		err := json.Unmarshal([]byte(wayPointsBInfo), wayPoint)
		if err != nil {
			log.Trace.Warnf(ctx, logTag, "way point b info unmarshal fail, origin:%v, err:%v", wayPointsAInfo, err)
		}

		if wayPointsBStatus != "" {
			wayPoint.Status, err = strconv.Atoi(wayPointsBStatus)
			if err != nil {
				log.Trace.Warnf(ctx, logTag, "way point b status a to i fail, origin:%v, err:%v", wayPointsBStatus, err)
			}
		}

		stopoverPointList = append(stopoverPointList, wayPoint)
	}

	return stopoverPointList
}
