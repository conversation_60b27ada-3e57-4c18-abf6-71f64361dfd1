package intercity_multi_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/before_price_filter"

	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/option_gen_product"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

type BizLogic struct {
	generator *biz_runtime.ProductsGenerator
}

func BuildService(ctx context.Context, estimateReq *param_handler.EstimateRequest) (*BizLogic, error) {
	req, _ := estimateReq.ReqFromParams.(*proto.IntercityMultiEstimateRequest)
	passenger := estimateReq.GetPassengerInfo()
	builder := models.NewBaseReqDataBuilder().
		SetClientInfo(&models.ClientInfo{
			AppVersion:   req.AppVersion,
			AccessKeyID:  req.AccessKeyId,
			ClientType:   req.ClientType,
			Lang:         req.Lang,
			TerminalID:   0,
			OriginID:     0,
			Imei:         "",
			MenuID:       "dache_anycar",
			Xpsid:        req.Xpsid,
			XpsidRoot:    req.XpsidRoot,
			PageType:     estimateReq.GetPageType(),
			Dchn:         req.Dchn,
			IsEstimateV2: req.GetIsEstimateV2(),
			Channel:      req.Channel,
			IsDRN:        req.IsDrn == 1,
			SortType:     req.Sort, // 班次列表页面排序类型
		}).SetStationInfo(
		&models.StationInfo{
			RouteId:        req.RouteId,
			StartStationId: req.StartStationId,
			EndStationId:   req.EndStationId,
			StartCity:      req.StartCity,
			EndCity:        req.EndCity,
			StartTime:      req.StartTime,
			EndTime:        req.EndTime,
			DayTime:        req.DayTime,
			LastShiftId:    req.LastShiftId,
			StartCountyId:  req.StartCountyId,
			EndCountyId:    req.EndCountyId,
			SupportCounty:  req.SupportCounty,
			FromLng:        req.GetFromLng(),
			FromLat:        req.GetFromLat(),
			ToLng:          req.GetToLng(),
			ToLat:          req.GetToLat(),
			StartPoiName:   req.GetStartPoiName(),
			EndPoiName:     req.GetEndPoiName(),
			ProductId:      req.GetProductId(),
			OpenSource:     req.GetOpenSource(),
		}, req.StartTimeV2, req.EndTimeV2).SetAreaInfo(ctx, req.StartCity, req.EndCity).SetUserOption(
		&models.UserOption{
			CallCarType:    0,
			CarpoolSeatNum: 1,
			OrderType:      1,
		})
	if passenger != nil {
		builder.SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(passenger.UID),
			PID:      int64(passenger.PID),
			Phone:    passenger.Phone,
			Role:     passenger.Role,
			Channel:  passenger.Channel,
			UserType: 1,
			OriginID: passenger.OriginId,
		})
	}
	productsGen, _ := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pIntercityMutliEstimatePrice"),
		biz_runtime.WithBaseReq(builder.GetBaseReqData()),
	)

	productsGen.RegisterOptionProcessProduct(option_gen_product.NewStationService(ctx))

	// 获取起终点信息
	if itemRpc := rpc_process.NewBusStationItemInfoRPC(productsGen.BaseReqData); itemRpc != nil {
		productsGen.RegisterAfterDdsNoBaseProductsRpcProcess(itemRpc)
	}

	// 获取prfs站点数据
	if prfsStation := rpc_process.NewBatchRouteDetail(productsGen.BaseReqData); prfsStation != nil {
		productsGen.RegisterBeforePriceRpcProcess(prfsStation)
	}

	if shiftDetail := rpc_process.NewBatchShiftDetail(productsGen.BaseReqData); shiftDetail != nil {
		productsGen.RegisterBeforePriceRpcProcess(shiftDetail)
	}

	// 过滤停售站点
	if filterBusStation := before_price_filter.NewFilterBusStations(productsGen.BaseReqData); filterBusStation != nil {
		productsGen.RegisterBeforePriceFilter(filterBusStation)
	}

	// 获取prfs可选区县站点列表
	if prfsStation := rpc_process.NewRouteStaionListV2(productsGen.BaseReqData); prfsStation != nil {
		productsGen.RegisterAfterDdsNoBaseProductsRpcProcess(prfsStation)
	}

	if prfsStation := rpc_process.NewRouteShiftInventory(productsGen.BaseReqData); prfsStation != nil {
		productsGen.RegisterAfterDdsNoBaseProductsRpcProcess(prfsStation)
	}

	// robin获取
	if robinRpc := rpc_process.NewBusInventoryRobinRPC(productsGen.BaseReqData); robinRpc != nil {
		productsGen.RegisterAfterPriceRPCProcess(robinRpc)
	}

	productsGen.SetSendReqKafka(true)

	return &BizLogic{generator: productsGen}, nil
}

func (b *BizLogic) DoBizLogicList(ctx context.Context, req *proto.IntercityMultiEstimateRequest) (*proto.IntercityMultiEstimateData, error) {
	products, _ := b.generator.GenProducts(ctx)
	data := b.Render(ctx, req, products)
	defer func() {
		WritePublicLog(ctx, products, req, data)
	}()
	return data, nil
}
