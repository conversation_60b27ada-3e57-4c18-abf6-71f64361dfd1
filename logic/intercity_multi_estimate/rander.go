package intercity_multi_estimate

import (
	"context"
	"encoding/json"
	"fmt"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_multi_estimate/internal"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_sku"
	"git.xiaojukeji.com/s3e/x-engine/condition"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_multi_station"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	LogTag                                                     = "ufs_get_feature"
	EstimateAllInventory                                       = "intercity_station-estimate_all_inventory" // 精确+一般推荐
	EstimateRecommendInventory                                 = "intercity_station-estimate_recommend_inventory"
	EstimateNoInventory                                        = "intercity_station-estimate_no_inventory"
	EstimateOptional                                           = "intercity_station-inventory_optional" // 跳转其他日期文案
	EstimateTodayOptional                                      = "intercity_station-inventory_today_optional"
	EstimateWithoutOptional                                    = "intercity_station-inventory_without_optional"     // 只有一般推荐
	EstimateOnlyWholeCityRecommend                             = "intercity_station-inventory_whole_city_recommend" // 只有全程推荐
	EstimateWithoutOptionalAndRecommend                        = "intercity_station-inventory_without_optional_and_recommend"
	AVALIABLEACCURATE                                          = 1 // 是否有精确班次
	AVALIABLERecommend                                         = 2 // 是否有推荐班次
	AvailableNormalStationBus                                  = 4 // 非多点到门班次
	AvailableWholeCityRecommend                                = 8 // 城市-城市是否有班次
	CalendarAccurateAndRecommend                               = 0 // 判断日历页可点击 判断是否有精确班次或一般推荐班次或全城推荐
	CalendarAccurateAndRecommendWithoutDoor2Station            = 1 // 判断日历页可点击 判断是否有非多点到门的精确班次或推荐班次或全城班次
	ShiftPageNavigationAccurateAndRecommend                    = 2 // 班次列表页 跳转按钮判断条件
	ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station = 3 // 非多点到门班次列表页跳转按钮判断条件
	// 班次列表页 duse 推荐策略
	PickUpPointDistanceRecommendation  = 1 // duse 推荐策略, 依据上车点 召回一般距离推荐
	DropOffPointDistanceRecommendation = 2 // duse 推荐策略, 依据下车点 召回一般距离推荐
	WholeCityRecommendation            = 3 // duse 推荐策略, 召回全城班次
	// 埋点信息, 推荐召回策略
	OmegaInfoDistanceRecommendation  = "1"
	OmegaInfoWholeCityRecommendation = "2"
	// 埋点信息，批量预估页面状态组成
	PageStatusTodayAccurateAndRecommend                    = "1" // 当天有精确+推荐
	PageStatusTodayOnlyAccurate                            = "2" // 当天 只有精确班次
	PageStatusTodayOnlyRecommend                           = "3" // 当天 只有推荐
	PageStatusTodayRecommendAndOtherDayAccurateOrRecommend = "4" // 有跳转其他日期按钮 + 推荐班次
	PageStatusOtherDayAccurateOrRecommend                  = "5" // 有跳转其他日期按钮
	PageStatusNoAnyBusShift                                = "6" // 空页面，无班次
)

func (b *BizLogic) getOldVersionAllow(ctx context.Context, products []*biz_runtime.ProductInfoFull) bool {
	var phone string
	var productId string
	if products != nil && products[0] != nil && products[0].BaseReqData != nil {
		phone = products[0].BaseReqData.PassengerInfo.Phone

	}

	if products != nil && products[0] != nil && products[0].Product != nil {
		productId = cast.ToString(products[0].Product.ProductID)
	}

	params := map[string]string{
		"phone":      cast.ToString(phone),
		"product_id": cast.ToString(productId),
	}

	isAllow := apollo.FeatureToggle(ctx, "gs_carpool_station_oldVersion_addTag_toggle", "", params)
	return isAllow
}

type SortListDcmp struct {
	SortList []*proto.SortList `json:"sort_list"`
}

// 班次列表页面返回给h5的排序信息
func (b *BizLogic) getSortBy(ctx context.Context) []*proto.SortList {
	// 从dcmp获取文案
	sortListText := dcmp.GetDcmpContent(ctx, "intercity_estimate-shift_sort_optimization", nil)
	var sortList SortListDcmp
	err := json.Unmarshal([]byte(sortListText), &sortList)
	if err != nil {
		log.Trace.Infof(ctx, "intercity_estimate-shift_sort_optimization", "intercity_estimate-shift_sort_optimization")
		return nil
	}
	return sortList.SortList
}

func (b *BizLogic) Render(ctx context.Context, req *proto.IntercityMultiEstimateRequest, products []*biz_runtime.ProductInfoFull) *proto.IntercityMultiEstimateData {
	estimateData := make([]*proto.EstimateCard, 0)
	estimateDataRecommend := make([]*proto.EstimateCard, 0)
	estimateDataWholeCityRecommend := make([]*proto.EstimateCard, 0, 0)
	isAllow := b.getOldVersionAllow(ctx, products)

	for _, product := range products {
		if product == nil || product.Product == nil {
			continue
		}

		intercity_multi_station.FillBusModeInfo(product)
		// 站点巴士线上测试用户
		businessName, icon := car_info.GetBusinessNameAndIcon(ctx, product)
		prod := &internal.AdapterMultiInterCity{ProductInfoFull: product}
		stationInfo, timeTotal, propertyList := intercity_multi_station.StationInfo(ctx, prod, req, b.generator.BaseReqData.PassengerInfo.PID)
		isRecommend := stationInfo.Detail.ShiftType != 0

		estimateCard := &proto.EstimateCard{
			EstimateId:         prod.GetEstimateID(),
			TimeMsg:            intercity_multi_station.TimeMsg(ctx, prod, req.GetIsEstimateV2()),
			FeeMsg:             intercity_multi_station.FeeMsg(ctx, prod),
			FeeDescList:        intercity_sku.FeeDescList(ctx, prod, prod),
			StationInfo:        stationInfo,
			ButtonInfo:         intercity_multi_station.ButtonInfo(ctx, prod, b.generator.BaseReqData.CommonBizInfo.StationInfo, b.generator.BaseReqData.PassengerInfo.PID, isRecommend, req, b.generator.BaseReqData.CommonBizInfo.StationListV2),
			BusShiftId:         prod.GetSelectInfo().ShiftID,
			TimeMsgDescription: intercity_multi_station.TimeMsgDescription(ctx, prod, req.GetIsEstimateV2()),
		}

		if req.GetIsEstimateV2() {
			// 标签列表
			estimateCard.SubTagList = intercity_multi_station.SubTagList(ctx, prod)
			// 改造为合规区域，下掉预计行驶时间
			estimateCard.SubTitleList = intercity_multi_station.Compliance(ctx, prod, businessName, propertyList, icon)

			// 路线简称赋值
			estimateCard.LeftTopTitle = product.GetBizInfo().RouteDetail.RouteExtendInfo.RouteShortName
		} else {
			estimateCard.SubTagList = intercity_multi_station.SubTagListForOldVersion(ctx, prod, isAllow)
			estimateCard.SubTitle = intercity_multi_station.SubTitle(ctx, prod, timeTotal, businessName, propertyList)
			//  等h5上线后下掉这行代码
			estimateCard.SubMsg = intercity_multi_station.SubMsg(ctx, prod)
		}

		// 旅游班次皮肤
		estimateCard.Background = intercity_multi_station.SubBackground(ctx, prod)

		// 精确班次，一般推荐班次，全城推荐班次
		if stationInfo.Detail != nil && (stationInfo.Detail.ShiftType == PickUpPointDistanceRecommendation || stationInfo.Detail.ShiftType == DropOffPointDistanceRecommendation) {
			estimateDataRecommend = append(estimateDataRecommend, estimateCard)
		} else if stationInfo.Detail != nil && stationInfo.Detail.ShiftType == WholeCityRecommendation {
			estimateDataWholeCityRecommend = append(estimateDataWholeCityRecommend, estimateCard)
		} else {
			estimateData = append(estimateData, estimateCard)
		}
	}

	optimizedData, omegaParams := b.getOptimizedData(ctx, req, estimateData, estimateDataRecommend, estimateDataWholeCityRecommend)
	LastItem := true
	LastShiftId := ""
	Refresh := int32(1)
	if len(products) != 0 {
		LastItem = products[len(products)-1].Product.BizInfo.StationInventoryInfo.SelectInfo.LastItem
		LastShiftId = products[len(products)-1].Product.BizInfo.StationInventoryInfo.SelectInfo.ShiftID
		Refresh = products[len(products)-1].Product.BizInfo.StationInventoryInfo.SelectInfo.Refresh
	}

	// 全局数据
	intercityMultiEstimateData := &proto.IntercityMultiEstimateData{
		Refresh:               Refresh,
		PageTitle:             intercity_multi_station.PageTitle(ctx),
		DisabledInfo:          intercity_multi_station.DisabledInfo(ctx, products),
		ScreenData:            intercity_multi_station.ScreenData(ctx, b.generator.BaseReqData.CommonBizInfo, req.GetIsEstimateV2()),
		EstimateCard:          estimateData,
		EstimateCardRecommend: b.getEstimateCardRecommend(estimateData, estimateDataRecommend, estimateDataWholeCityRecommend),
		EstimateTraceId:       util.GetTraceIDFromCtxWithoutCheck(ctx),
		LastItem:              LastItem,
		Title:                 intercity_multi_station.Title(b.generator.BaseReqData.CommonBizInfo),
		LastShiftId:           LastShiftId,
		Guide:                 b.getInstructions(ctx, req),
		OptimizedData:         optimizedData,
		Calendar:              b.getCalendar(ctx, req),
		StartSearchBox:        b.getSearchBox(b.generator.BaseReqData.CommonBizInfo.StartItemInfo),
		EndSearchBox:          b.getSearchBox(b.generator.BaseReqData.CommonBizInfo.EndItemInfo),
		OmegaParams:           omegaParams,
	}

	sortList := make([]*proto.SortList, 0)
	// 返回h5排序列表
	if req.Sort != nil {
		sortList = b.getSortBy(ctx)
	}
	// 排序列表进行灰度放量
	if b.checkApolloToggle(ctx) {
		// 判断排序是否放量
		intercityMultiEstimateData.SortList = sortList
	}
	return intercityMultiEstimateData
}

func (b *BizLogic) getEstimateCardRecommend(estimateData []*proto.EstimateCard, estimateDataRecommend []*proto.EstimateCard, estimateDataWholeCityRecommend []*proto.EstimateCard) []*proto.EstimateCard {
	// 精确和一般推荐都没有班次，推荐全程班次
	if len(estimateData) == 0 && len(estimateDataRecommend) == 0 {
		return estimateDataWholeCityRecommend
	}

	return estimateDataRecommend
}

func (b *BizLogic) getSearchBox(itemInfo *models.ItemInfo) *proto.SearchBox {
	if itemInfo == nil {
		return nil
	}

	return &proto.SearchBox{
		CityName:    itemInfo.CityName,
		CountyName:  itemInfo.CountyName,
		StationName: itemInfo.StationName,
		CityId:      itemInfo.CityId,
		CountyId:    itemInfo.CountyId,
		StationId:   itemInfo.StationId,
		Lat:         itemInfo.Lat,
		Lng:         itemInfo.Lng,
	}
}

func (b *BizLogic) getCalendar(ctx context.Context, req *proto.IntercityMultiEstimateRequest) []*proto.CalendarItem {
	if !req.GetIsEstimateV2() {
		return nil
	}

	inventoryList := b.generator.BaseReqData.CommonBizInfo.BusRegionInventoryList
	var calendarLen = len(inventoryList)
	res := make([]*proto.CalendarItem, calendarLen)
	// 0,1,2,3=>0,1
	for i := 0; i < calendarLen; i++ {
		res[i] = &proto.CalendarItem{
			Date: inventoryList[i].DayStartTime,
		}
		// 根据版本判断status取值, 切量灰度下
		if b.MultiPointToDoorAvailableVersion(ctx, req, "Calendar") && CalenderStatusChecker(inventoryList[i].Status, CalendarAccurateAndRecommend) {
			res[i].Status = 1
		}
		if !b.MultiPointToDoorAvailableVersion(ctx, req, "Calendar") && CalenderStatusChecker(inventoryList[i].Status, CalendarAccurateAndRecommendWithoutDoor2Station) {
			res[i].Status = 1
		}
	}
	return res
}

func (b *BizLogic) avaliableInventory(ctx context.Context, data *CarpoolOpenApi.BusRegionInventory, req *proto.IntercityMultiEstimateRequest) bool {
	if data == nil {
		return false
	}
	// 新版本无需过滤多点到门班次
	if b.MultiPointToDoorAvailableVersion(ctx, req, "InventoryRecommend") {
		return CalenderStatusChecker(data.Status, ShiftPageNavigationAccurateAndRecommend)
	}
	// 老版本端要过滤掉多点到门班次
	return CalenderStatusChecker(data.Status, ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station)

}

func CalenderStatusChecker(status int32, mode int) bool {
	switch mode {
	case CalendarAccurateAndRecommend:
		return (status&AVALIABLEACCURATE) == AVALIABLEACCURATE || (status&AVALIABLERecommend) == AVALIABLERecommend || (status&AvailableWholeCityRecommend) == AvailableWholeCityRecommend
	case CalendarAccurateAndRecommendWithoutDoor2Station:
		return status&AvailableNormalStationBus == AvailableNormalStationBus && ((status&AVALIABLEACCURATE) == AVALIABLEACCURATE ||
			(status&AVALIABLERecommend) == AVALIABLERecommend || (status&AvailableWholeCityRecommend) == AvailableWholeCityRecommend)
	case ShiftPageNavigationAccurateAndRecommend:
		return (status&AVALIABLEACCURATE) == AVALIABLEACCURATE || (status&AVALIABLERecommend) == AVALIABLERecommend
	case ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station:
		return status&AvailableNormalStationBus == AvailableNormalStationBus && ((status&AVALIABLEACCURATE) == AVALIABLEACCURATE ||
			(status&AVALIABLERecommend) == AVALIABLERecommend)
	default:
		return false
	}
}

func (b *BizLogic) getCandidate(ctx context.Context, dayTime int64, inventoryList []*CarpoolOpenApi.BusRegionInventory, req *proto.IntercityMultiEstimateRequest) int64 {
	if len(inventoryList) == 0 {
		return 0
	}
	targetDayTime := util.GetSomeDayEarlyTimeStamp(dayTime)
	var target int
	for index, inventory := range inventoryList {
		if inventory.DayStartTime == targetDayTime {
			target = index
			break
		}
	}
	left := target
	right := target

	for left >= 0 || right < len(inventoryList) {
		if right < len(inventoryList) && b.avaliableInventory(ctx, inventoryList[right], req) {
			return inventoryList[right].DayStartTime
		}
		if left >= 0 && b.avaliableInventory(ctx, inventoryList[left], req) {
			return inventoryList[left].DayStartTime
		}
		right++
		left--
	}
	return 0
}

func (b *BizLogic) getOptimizedData(ctx context.Context, req *proto.IntercityMultiEstimateRequest, estimateData []*proto.EstimateCard, estimateDataRecommend []*proto.EstimateCard, estimateDataWholeCityRecommend []*proto.EstimateCard) (*proto.OptimizedData, *proto.OmegaParams) {
	OptimizedData := &proto.OptimizedData{}
	omegaParams := &proto.OmegaParams{}
	// 精准班次 && 推荐班次都有
	if len(estimateData) != 0 && len(estimateDataRecommend) != 0 {
		OptimizedData = b.getDcmpText(ctx, EstimateAllInventory)
		omegaParams.PageStatus = PageStatusTodayAccurateAndRecommend
		omegaParams.RecommendedStrategy = OmegaInfoDistanceRecommendation
		return OptimizedData, omegaParams
	}

	if len(estimateData) != 0 {
		omegaParams.PageStatus = PageStatusTodayOnlyAccurate
		return nil, omegaParams
	}

	if req.GetIsEstimateV2() {
		// 一般推荐
		if len(estimateDataRecommend) != 0 {
			omegaParams.PageStatus = PageStatusTodayOnlyRecommend
			omegaParams.RecommendedStrategy = OmegaInfoDistanceRecommendation
			OptimizedData = b.getDcmpText(ctx, EstimateWithoutOptional)
			return OptimizedData, omegaParams
		}
		var candidateData int64
		inventoryList := b.generator.BaseReqData.CommonBizInfo.BusRegionInventoryList
		candidateData = b.getCandidate(ctx, int64(req.GetDayTime()), inventoryList, req)

		if candidateData > 0 {
			OptimizedData = b.getDcmpText(ctx, EstimateOptional)
			if OptimizedData.IconCard != nil && len(OptimizedData.IconCard.Text) > 1 {
				currentDate := time.Unix(cast.ToInt64(candidateData), 0).Format("1月2日")
				OptimizedData.IconCard.Text[1] = dcmp.TranslateTemplate(OptimizedData.IconCard.Text[1], map[string]string{"date": currentDate})
				omegaParams.PageStatus = PageStatusOtherDayAccurateOrRecommend
			}
			if len(estimateDataWholeCityRecommend) != 0 {
				omegaParams.PageStatus = PageStatusTodayRecommendAndOtherDayAccurateOrRecommend
				omegaParams.RecommendedStrategy = OmegaInfoWholeCityRecommendation
				OptimizedData.IconCard.ImgLink = ""
			}
		} else {
			if len(estimateDataWholeCityRecommend) != 0 {
				OptimizedData = b.getDcmpText(ctx, EstimateOnlyWholeCityRecommend)
				omegaParams.PageStatus = PageStatusTodayOnlyRecommend
				omegaParams.RecommendedStrategy = OmegaInfoWholeCityRecommendation
			} else {
				omegaParams.PageStatus = PageStatusNoAnyBusShift
				OptimizedData = b.getDcmpText(ctx, EstimateWithoutOptionalAndRecommend)
			}
		}
	} else {
		startName := b.getCombinedName(ctx, req.StartCity, req.StartCountyId, req.StartStationId)
		endName := b.getCombinedName(ctx, req.EndCity, req.EndCountyId, req.EndStationId)
		// 防止dayTime是0，进行兜底
		dayTime := b.generator.BaseReqData.CommonBizInfo.StationInfo.DayTime
		if dayTime == 0 {
			dayTime = int32(util.GetNowEarlyTimeStamp())
		}
		currentDate := time.Unix(cast.ToInt64(dayTime), 0).Format("1月2日")

		// 只有推荐班次/没有班次
		if len(estimateDataRecommend) != 0 {
			OptimizedData = b.getDcmpText(ctx, EstimateRecommendInventory)
		} else {
			OptimizedData = b.getDcmpText(ctx, EstimateNoInventory)
		}
		// %s到%s的路线%暂无余票 (上车站点-下车站点-X月X日)
		if OptimizedData.IconCard != nil && len(OptimizedData.IconCard.Text) > 0 {
			OptimizedData.IconCard.Text[0] = fmt.Sprintf(OptimizedData.IconCard.Text[0], startName, endName, currentDate)
		}
	}

	return OptimizedData, omegaParams
}

func (b *BizLogic) getDcmpText(ctx context.Context, dcmpKey string) *proto.OptimizedData {
	OptimizedData := &proto.OptimizedData{}
	config := dcmp.GetDcmpContent(ctx, dcmpKey, nil)
	if config != "" {
		err := json.Unmarshal([]byte(config), OptimizedData)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal fail with dcmp intercity_station-estimate_all_inventory with err %v", err)
		}
	}
	params := map[string]string{
		"phone": b.generator.BaseReqData.PassengerInfo.Phone,
		"city":  cast.ToString(b.generator.BaseReqData.AreaInfo.City),
	}
	isAllow, assignParams := apollo.GetParameters("gs_bus_guide_intercity", b.generator.BaseReqData.PassengerInfo.Phone, params)
	if isAllow && assignParams != nil && len(assignParams) > 0 && dcmpKey == EstimateWithoutOptionalAndRecommend {
		OptimizedData.JumpButton = &proto.SimpleButton{}
		if text, ok := assignParams["text"]; ok {
			OptimizedData.JumpButton.Text = text
		}
		if color, ok := assignParams["color"]; ok {
			OptimizedData.JumpButton.Color = color
		}
		if jumpUrl, ok := assignParams[cast.ToString(b.generator.BaseReqData.CommonInfo.AccessKeyID)]; ok {
			OptimizedData.JumpButton.JumpUrl = &jumpUrl
		}
	}
	return OptimizedData
}

func (b *BizLogic) getCombinedName(ctx context.Context, cityId int32, countyId int32, stationId int64) string {
	// 如果有站点，返回其名称
	if stationId != 0 {
		// check起点列表
		for _, county := range b.generator.BaseReqData.CommonBizInfo.StationListV2.FromCounties {
			if county.Countyid == countyId {
				for _, station := range county.Stations {
					if cast.ToInt64(station.Stationid) == stationId {
						return station.Name
					}
				}
			}
		}

		// check终点列表
		for _, county := range b.generator.BaseReqData.CommonBizInfo.StationListV2.ToCounties {
			if county.Countyid == countyId {
				for _, station := range county.Stations {
					if cast.ToInt64(station.Stationid) == stationId {
						return station.Name
					}
				}
			}
		}
	}
	res := ""
	// 有区县返回 城市+区县，有城市只返回城市
	cityInfo, err := util.GetCityInfo(ctx, cityId)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get city info from apollo, err = %v", err)
		return ""
	}
	res = cityInfo.CityDesc

	if countyId != 0 {
		// 有区县返回 城市+区县，有城市只返回城市
		countyInfo, err1 := util.GetCountyInfo(ctx, countyId)
		if err1 != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get county info from apollo, err = %v", err1)
			return res
		}
		res += countyInfo.CountyName
	}
	return res
}

func (b *BizLogic) getInstructions(ctx context.Context, req *proto.IntercityMultiEstimateRequest) *proto.GuideData {
	guideData := &proto.GuideData{}
	if req.Refresh == 1 {
		return nil
	}

	var source = "normal"
	var ufsKey = "intercity_multi_estimate.guide_cnt"
	if req.RouteId != 0 {
		source = "route_code"
		ufsKey = "intercity_multi_estimate.guide_cnt_by_route"
	}

	//apollo配置人群+频控次数
	pid := strconv.Itoa(int(b.generator.BaseReqData.PassengerInfo.PID))
	param := map[string]interface{}{
		"phone":       b.generator.BaseReqData.PassengerInfo.Phone,
		"city_id":     req.StartCity,
		"pid":         pid,
		"app_version": req.GetAppVersion(),
		"tag_caller":  "mamba",
		// x-engine的ufs和mamba有冲突
		//"ufs_caller":  "mamba",
		//"ufs_parameter": map[string]string{
		//	"passenger_id": pid,
		//},
		"source": source,
	}

	result, err := condition.Check(ctx, "station_page_conf", param, condition.WithConfName("estimate_guide"))
	if err != nil || result == nil || (!result.IsAllow) {
		log.Trace.Infof(ctx, LogTag, "get instructions failed ")
		return nil
	}
	err = result.GetMaterial(guideData)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "x-engine get material fail||err:%v", err)
		return nil
	}

	// 频控
	ufsParameter := map[string]string{
		"passenger_id": pid,
	}
	featureIntercity, err := ufs.GetFeatureV2(ctx, ufs.DomainPassenger, ufsKey, ufsParameter)
	if featureIntercity != "0" {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "ufs frequency control")
		return nil
	}

	//写ufs
	params := map[string]string{"passenger_id": strconv.FormatInt(b.generator.BaseReqData.PassengerInfo.PID, 10)}
	kv := map[string]string{ufsKey: "1"}
	if _, err := ufs.SetFeature(ctx, "passenger", params, kv); err != nil {
		log.Trace.Warnf(ctx, LogTag, "setFeature %s err: %v", ufsKey, err)
	}

	return guideData

}

func (b *BizLogic) MultiPointToDoorAvailableVersion(ctx context.Context, req *proto.IntercityMultiEstimateRequest, scene string) bool {
	params := map[string]string{
		"access_key_id": cast.ToString(req.AccessKeyId),
		"app_version":   req.AppVersion,
		"city":          cast.ToString(req.StartCity),
		"phone":         b.generator.BaseReqData.PassengerInfo.Phone,
		"scene":         scene,
	}

	if apollo.FeatureToggle(ctx, "gs_intercity_fence_bus_appversion_toggle", strconv.FormatInt(b.generator.BaseReqData.PassengerInfo.PID, 10), params) {
		return true
	}
	return false
}

func (b *BizLogic) checkApolloToggle(ctx context.Context) bool {
	// 总灰度控制，开量验证后可删除总灰度控制逻辑
	apolloParams := map[string]string{
		"pid":           util.ToString(b.generator.BaseReqData.PassengerInfo.PID),
		"city":          util.ToString(b.generator.BaseReqData.CommonBizInfo.StationInfo.StartCity),
		"end_city":      util.ToString(b.generator.BaseReqData.CommonBizInfo.StationInfo.EndCity),
		"access_key_id": cast.ToString(b.generator.BaseReqData.CommonInfo.AccessKeyID),
		"page_type":     util.ToString(b.generator.BaseReqData.CommonInfo.PageType),
	}
	return apollo.FeatureToggle(ctx, "pre_sale_optimization_shift_toggle", b.generator.BaseReqData.PassengerInfo.Phone, apolloParams)
}
