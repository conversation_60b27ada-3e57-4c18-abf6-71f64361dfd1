package rpc

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	route "git.xiaojukeji.com/dirpc/dirpc-go-http-OrderRouteApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/bicycle_route"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel/model"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
)

type BicyclingTravelRpc struct {
	baseRequest *model.Request
	RouteDetail []*route.BicyclingRouteDetail

	conf *BicyclingDCMPConf
}

func NewBicyclingTravelRpc(ctx context.Context, baseRequest *model.Request) *BicyclingTravelRpc {
	req := baseRequest.CompositeTravelReq
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Warnf(ctx, PublicTransitRPCLogTag, "req is lost")
		return nil
	}

	// 开城灰度
	if !apollo.FeatureToggle(ctx, "one_stop-bicycling_travel_toggle", cast.ToString(baseRequest.UserInfo.UID), baseRequest.GetApolloParam()) {
		return nil
	}

	// 文案配置
	conf, err := initBicyclingTravelDCMPConf(ctx)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, CombinedTravelRPCLogTag, "init DCMPConf err:[%v]", err)
		return nil
	}

	return &BicyclingTravelRpc{
		baseRequest: baseRequest,
		conf:        conf,
	}
}

func (b *BicyclingTravelRpc) Execute(ctx context.Context, request *model.Request) {
	tripReq := &route.RecommendBicyclingTripReq{
		Token:   request.GetToken(),
		UserId:  cast.ToString(request.UserInfo.UID),
		BizType: 85004,
		SrcPoint: &route.DoublePoint{
			Lat:  request.FromLat,
			Lng:  request.FromLng,
			Name: util.StringPtr(request.FromName),
			UID:  util.StringPtr(request.FromPoiId),
		},
		DstPoint: &route.DoublePoint{
			Lat:  request.ToLat,
			Lng:  request.ToLng,
			Name: util.StringPtr(request.ToName),
			UID:  util.StringPtr(request.ToPoiId),
		},
		SrcCityId:   util.Int32Ptr(request.AreaInfo.FromCityId),
		DstCityId:   util.Int32Ptr(request.AreaInfo.ToCityId),
		VehicleType: nil,
		Caller:      "internal_api",
		ReqType:     route.ReqSourceType_SERVER_API,
		ReqTime:     time.Now().Unix(),
		DidiVersion: util.StringPtr(request.AppVersion),
	}

	res := bicycle_route.RecommendBicyclingTripInfo(ctx, tripReq)

	if res == nil {
		return
	}

	b.RouteDetail = res.RouteDetail
}

func (b *BicyclingTravelRpc) GetRpcResult(ctx context.Context) (planType int, result []*model.PlanFull) {

	planType = model.BicyclingTravel
	if len(b.RouteDetail) < 1 {
		return
	}

	var planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型

	defaultTransit := b.RouteDetail[0]

	if len(defaultTransit.RouteDetail) < 1 {
		return
	}

	defaultTransitRoute := defaultTransit.RouteDetail[0]

	// 构造
	detailSegmentItem := model.SegmentItem{
		Mode:     model.ModeBike,
		Distance: defaultTransitRoute.GetEdaMeter(),
		Time:     defaultTransitRoute.GetEtaSec(),
		Cost:     0,
	}
	planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, &detailSegmentItem)

	o := proto.Plan{
		Title:          b.conf.Title,
		EstimateTime:   GetEstimateTime(ctx, cast.ToInt64(defaultTransitRoute.GetEtaSec())),
		PlanType:       model.BicyclingTravel,
		RightTitleList: b.buildRightTitleList(ctx, defaultTransitRoute),
		ButtonContent:  b.conf.ButtonContent,
		DescList:       b.buildDescList(ctx, defaultTransitRoute),

		LinkType:  model.LinkTypeUrl,
		LinkUrl:   b.buildListUrl(ctx),
		MapParams: b.buildMapParams(ctx, defaultTransit),
		ExtraData: b.buildExtraData(ctx),
	}

	result = append(result, &model.PlanFull{
		TotalTime:            int64(defaultTransitRoute.GetEtaSec()),
		Cost:                 0,
		Distance:             defaultTransitRoute.GetEdaMeter(),
		PlanType:             model.BicyclingTravel,
		Plan:                 o,
		PlanFormatDetailData: planFormatDetailData,
		PrivateData: &model.PrivateData{
			BicycleData: b.RouteDetail,
		},
	})

	return
}

func (b *BicyclingTravelRpc) GetCommentResult(ctx context.Context) *proto.Evaluation {
	return nil
}

func (b *BicyclingTravelRpc) buildRightTitleList(ctx context.Context, detail *route.MpRouteDetail) []string {
	var res []string
	if detail.GetEtaStr() != "" {
		split := strings.Split(detail.GetEtaStr(), " ")
		if len(split) == 2 {
			res = append(res,
				util.ReplaceTag(ctx, b.conf.Duration, map[string]string{
					"duration": split[0],
					"unit":     split[1],
				}))
		}
	}

	if detail.GetEdaStr() != "" {
		split := strings.Split(detail.GetEdaStr(), " ")
		if len(split) == 2 {
			res = append(res,
				util.ReplaceTag(ctx, b.conf.Distance, map[string]string{
					"distance": split[0],
					"unit":     split[1],
				}))
		}
	}
	return res
}

func (b *BicyclingTravelRpc) buildDescList(ctx context.Context, detail *route.MpRouteDetail) []*proto.PlanDesc {
	return []*proto.PlanDesc{
		&proto.PlanDesc{
			Content: util.ReplaceTag(ctx, b.conf.TrafficLightMsg, map[string]string{
				"trafficLightCount": cast.ToString(detail.GetTrafficLightCount()),
			}),
		},
	}
}

func (b *BicyclingTravelRpc) buildListUrl(ctx context.Context) string {
	url, _ := b.conf.ListUrl[cast.ToString(b.baseRequest.AccessKeyId)]

	return util.ReplaceTag(ctx, url, map[string]string{
		"from_lng":            cast.ToString(b.baseRequest.FromLng),
		"from_lat":            cast.ToString(b.baseRequest.FromLat),
		"from_name":           b.baseRequest.FromName,
		"from_poi_id":         b.baseRequest.FromPoiId,
		"to_lng":              cast.ToString(b.baseRequest.ToLng),
		"to_lat":              cast.ToString(b.baseRequest.ToLat),
		"to_name":             b.baseRequest.ToName,
		"to_poi_id":           b.baseRequest.ToPoiId,
		"navi_end_poi_cityid": cast.ToString(b.baseRequest.AreaInfo.ToCityId),
	})
}

type RouteDataItem struct {
	RouteId   string `json:"route_id"`
	IsDefault int32  `json:"is_default"`
	SelectKey string `json:"select_key"`
}

type VehicleDataModel struct {
	VehicleType int32            `json:"vehicle_type"`
	RouteData   []*RouteDataItem `json:"route_data"`
}

type VehicleMapInfo struct {
	DefaultType int32               `json:"default_type"`
	VehicleData []*VehicleDataModel `json:"vehicle_data"`
}

func (b *BicyclingTravelRpc) buildMapParams(ctx context.Context, defaultTransit *route.BicyclingRouteDetail) map[string]string {

	vehicleDataList := make([]*VehicleDataModel, len(b.RouteDetail))
	for index, detail := range b.RouteDetail {

		routeDataList := make([]*RouteDataItem, len(detail.RouteDetail))
		for i, routeDetail := range detail.RouteDetail {
			routeData := &RouteDataItem{
				RouteId:   routeDetail.GetRouteId(),
				SelectKey: fmt.Sprintf("%d_%s", detail.GetVehicleType(), routeDetail.GetRouteId()),
			}
			if i == 0 {
				routeData.IsDefault = 1
			}
			routeDataList[i] = routeData
		}

		vehicleDataList[index] = &VehicleDataModel{
			VehicleType: detail.GetVehicleType(),
			RouteData:   routeDataList,
		}
	}

	res := &VehicleMapInfo{
		DefaultType: defaultTransit.GetVehicleType(),
		VehicleData: vehicleDataList,
	}

	resStr, err := json.Marshal(res)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "marshal vehicleData error|| err=%v", err)
	}

	return map[string]string{
		"map_info": string(resStr),
	}
}

type VehicleDataReplace struct {
	EstimateTime   string            `json:"estimate_time"`
	RightTitleList []string          `json:"right_title_list"` // 时间，价格，距离描述
	DescList       []*proto.PlanDesc `json:"desc_list"`
}

func (b *BicyclingTravelRpc) buildExtraData(ctx context.Context) map[string]string {
	dataReplaceMap := make(map[string]string)

	for _, detail := range b.RouteDetail {
		for _, routeDetail := range detail.RouteDetail {

			key := fmt.Sprintf("%d_%s", detail.GetVehicleType(), routeDetail.GetRouteId())
			data := &VehicleDataReplace{
				EstimateTime:   GetEstimateTime(ctx, cast.ToInt64(routeDetail.GetEtaSec())),
				RightTitleList: b.buildRightTitleList(ctx, routeDetail),
				DescList:       b.buildDescList(ctx, routeDetail),
			}

			dataStr, err := json.Marshal(data)
			if err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "marshal vehicleData error|| err=%v", err)
			}
			dataReplaceMap[key] = string(dataStr)
		}

	}

	return dataReplaceMap
}

type BicyclingDCMPConf struct {
	Title           string            `json:"title"`
	Distance        string            `json:"distance"`
	ButtonContent   *string           `json:"button_content"`
	TrafficLightMsg string            `json:"traffic_light_msg"`
	ListUrl         map[string]string `json:"list_url"`
	Duration        string            `json:"duration"`
}

func initBicyclingTravelDCMPConf(ctx context.Context) (*BicyclingDCMPConf, error) {
	conf := new(BicyclingDCMPConf)
	str := dcmp.GetDcmpContent(ctx, "one_stop-bicycling_travel", nil)
	err := json.Unmarshal([]byte(str), conf)
	if err != nil || conf == nil {
		return nil, err
	}
	return conf, nil
}
