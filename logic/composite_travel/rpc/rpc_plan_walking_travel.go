package rpc

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	walkApi "git.xiaojukeji.com/dirpc/dirpc-go-http-DolphinApiService"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/walking_route"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel/model"
	"github.com/spf13/cast"
)

type WalkingTravelRpc struct {
	baseRequest *model.Request
	res         *walkApi.PassengerWalkRouteRes

	conf *WalkingDCMPConf
}

func NewWalkingTravelRpc(ctx context.Context, baseRequest *model.Request) *WalkingTravelRpc {
	req := baseRequest.CompositeTravelReq
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Warnf(ctx, PublicTransitRPCLogTag, "req is lost")
		return nil
	}

	// 开城灰度
	if !apollo.FeatureToggle(ctx, "one_stop-walking_travel_toggle", cast.ToString(baseRequest.UserInfo.UID), baseRequest.GetApolloParam()) {
		return nil
	}

	// 文案配置
	conf, err := initWalkingTravelDCMPConf(ctx)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, CombinedTravelRPCLogTag, "init DCMPConf err:[%v]", err)
		return nil
	}

	return &WalkingTravelRpc{
		baseRequest: baseRequest,
		conf:        conf,
	}
}

func (w *WalkingTravelRpc) Execute(ctx context.Context, request *model.Request) {
	tripReq := &walkApi.PassengerWalkRouteReq{
		StartLng:   request.FromLng,
		StartLat:   request.FromLat,
		EndLng:     request.ToLng,
		EndLat:     request.ToLat,
		Timestamp:  time.Now().Unix(),
		BizType:    util.Int32Ptr(90001),
		UserId:     util.Int64Ptr(cast.ToInt64(request.UserInfo.UID)),
		Caller:     util.StringPtr("walk_bubble"),
		Token:      util.StringPtr(request.Token),
		StartPoiId: util.StringPtr(request.FromPoiId),
		EndPoiId:   util.StringPtr(request.ToPoiId),
	}
	res := walking_route.RecommendWalkingTripInfo(ctx, tripReq)

	w.res = res
}

func (w *WalkingTravelRpc) GetRpcResult(ctx context.Context) (planType int, result []*model.PlanFull) {

	if w.res == nil {
		return
	}

	planType = model.WalkingTravel

	var planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型

	// 构造
	detailSegmentItem := model.SegmentItem{
		Mode:     model.ModeWalk,
		Distance: w.res.GetDist(),
		Time:     w.res.GetDuration(),
		Cost:     0,
	}
	planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, &detailSegmentItem)

	o := proto.Plan{
		Title:          w.conf.Title,
		EstimateTime:   GetEstimateTime(ctx, cast.ToInt64(w.res.GetDuration())),
		PlanType:       model.WalkingTravel,
		RightTitleList: w.buildRightTitleList(ctx),
		ButtonContent:  w.conf.ButtonContent,
		DescList:       w.buildDescList(ctx),

		LinkType:  model.LinkTypeUrl,
		LinkUrl:   w.buildListUrl(ctx),
		MapParams: w.buildMapParams(),
	}

	result = append(result, &model.PlanFull{
		TotalTime:            int64(w.res.GetDuration()),
		Cost:                 0,
		Distance:             w.res.GetDist(),
		PlanType:             model.WalkingTravel,
		Plan:                 o,
		PlanFormatDetailData: planFormatDetailData,
		PrivateData: &model.PrivateData{
			WalkData: w.res,
		},
	})

	return
}

func (w *WalkingTravelRpc) GetCommentResult(ctx context.Context) *proto.Evaluation {
	return nil
}

func (w *WalkingTravelRpc) buildRightTitleList(ctx context.Context) []string {
	var res []string
	if w.res.GetDurationString() != "" {
		split := strings.Split(w.res.GetDurationString(), " ")
		if len(split) == 2 {
			res = append(res,
				util.ReplaceTag(ctx, w.conf.Duration, map[string]string{
					"duration": split[0],
					"unit":     split[1],
				}))
		}
	}

	if w.res.GetDistString() != "" {
		split := strings.Split(w.res.GetDistString(), " ")
		if len(split) == 2 {
			res = append(res,
				util.ReplaceTag(ctx, w.conf.Distance, map[string]string{
					"distance": split[0],
					"unit":     split[1],
				}))
		}
	}
	return res
}

func (w *WalkingTravelRpc) buildDescList(ctx context.Context) []*proto.PlanDesc {
	return []*proto.PlanDesc{
		&proto.PlanDesc{
			Content: util.ReplaceTag(ctx, w.conf.TrafficLightMsg, map[string]string{
				"trafficLightCount": cast.ToString(w.res.GetLightNum()),
			}),
		},
	}
}

func (w *WalkingTravelRpc) buildListUrl(ctx context.Context) string {
	url, _ := w.conf.ListUrl[cast.ToString(w.baseRequest.AccessKeyId)]

	return util.ReplaceTag(ctx, url, map[string]string{
		"from_lng":    cast.ToString(w.baseRequest.FromLng),
		"from_lat":    cast.ToString(w.baseRequest.FromLat),
		"from_name":   w.baseRequest.FromName,
		"from_poi_id": w.baseRequest.FromPoiId,
		"to_lng":      cast.ToString(w.baseRequest.ToLng),
		"to_lat":      cast.ToString(w.baseRequest.ToLat),
		"to_name":     w.baseRequest.ToName,
		"to_poi_id":   w.baseRequest.ToPoiId,
	})
}

func (w *WalkingTravelRpc) buildMapParams() map[string]string {

	return map[string]string{
		"route_id": cast.ToString(w.res.GetRouteId()),
	}
}

type WalkingDCMPConf struct {
	Title           string            `json:"title"`
	Distance        string            `json:"distance"`
	ButtonContent   *string           `json:"button_content"`
	TrafficLightMsg string            `json:"traffic_light_msg"`
	ListUrl         map[string]string `json:"list_url"`
	Duration        string            `json:"duration"`
}

func initWalkingTravelDCMPConf(ctx context.Context) (*WalkingDCMPConf, error) {
	conf := new(WalkingDCMPConf)
	str := dcmp.GetDcmpContent(ctx, "one_stop-walking_travel", nil)
	err := json.Unmarshal([]byte(str), conf)
	if err != nil || conf == nil {
		return nil, err
	}
	return conf, nil
}
