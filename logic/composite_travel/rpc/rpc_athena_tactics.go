package rpc

import (
	"context"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel/model"
	"github.com/spf13/cast"
)

var errType AthenaApiv3.RouteType

func GetAthenaRecList(ctx context.Context, req *AthenaApiv3.AthenaUTRecommendReq) *AthenaApiv3.AthenaUTRecommendResp {
	return athena.UTRecommend(ctx, req)
}

func GetPlanRouteType(planFull *model.PlanFull) *AthenaApiv3.RouteType {
	switch planFull.PlanType {
	case model.CarHailing:
		return RouteTypePtr(AthenaApiv3.RouteType_Car)
	case model.PublicTransit:
		return RouteTypePtr(AthenaApiv3.RouteType_Bus)
	case model.BicyclingTravel:
		return RouteTypePtr(AthenaApiv3.RouteType_Bicycle)
	case model.WalkingTravel:
		return RouteTypePtr(AthenaApiv3.RouteType_Walking)
	}

	// 组合出行类的方案特别处理
	if planFull.PlanType == model.CombinedTravel {
		for _, v := range planFull.SegmentDataList {
			if v.Mode == model.ModeCAR {
				return RouteTypePtr(AthenaApiv3.RouteType_ComboCar)
			}

			if v.Mode == model.ModeBike {
				return RouteTypePtr(AthenaApiv3.RouteType_ComboBicycle)
			}
		}
	}

	return RouteTypePtr(errType)
}

func GetSegmentRouteType(segmentItem *model.SegmentItem) *AthenaApiv3.RouteType {
	switch segmentItem.Mode {
	case model.ModeCAR:
		return RouteTypePtr(AthenaApiv3.RouteType_Car)
	case model.ModeWalk:
		return RouteTypePtr(AthenaApiv3.RouteType_Walking)
	case model.ModeBike:
		return RouteTypePtr(AthenaApiv3.RouteType_Bicycle)
	}

	if model.ModeTRANSIT == segmentItem.Mode {
		if segmentItem.SubMode == cast.ToString(model.TypeBus) {
			return RouteTypePtr(AthenaApiv3.RouteType_Bus)
		}

		// 默认地铁
		return RouteTypePtr(AthenaApiv3.RouteType_Subway)
	}

	return RouteTypePtr(errType)
}

func RouteTypePtr(r AthenaApiv3.RouteType) *AthenaApiv3.RouteType {
	return &r
}

func CalTransferCnt(full *model.PlanFull) int32 {
	var cnt int32
	if full.SegmentDataList == nil {
		return 0
	}

	for _, v := range full.SegmentDataList {
		if util.InArrayStr(v.Mode, []string{model.ModeCAR, model.ModeTRANSIT, model.ModeBike}) {
			cnt++
		}
	}

	return cnt
}

func GetCanReachStatus(segmentItem *model.SegmentItem) *AthenaApiv3.CanReachStatus {
	switch segmentItem.CanReachStatus {
	case model.CanReachStatusYes:
		return GetCanReachStatusPtr(AthenaApiv3.CanReachStatus_CanReachStatus_yes)
	case model.CanReachStatusFirstNotDepart:
		return GetCanReachStatusPtr(AthenaApiv3.CanReachStatus_CanReachStatus_firstNotDepart)
	case model.CanReachStatusMayMisPre:
		return GetCanReachStatusPtr(AthenaApiv3.CanReachStatus_CanReachStatus_mayMisPre)
	case model.CanReachStatusNextNotDepart:
		return GetCanReachStatusPtr(AthenaApiv3.CanReachStatus_CanReachStatus_nextNotDepart)
	case model.CanReachStatusMayMisLast:
		return GetCanReachStatusPtr(AthenaApiv3.CanReachStatus_CanReachStatus_mayMisLast)
	case model.CanReachStatusNot:
		return GetCanReachStatusPtr(AthenaApiv3.CanReachStatus_CanReachStatus_not)
	}

	var errorCanReachStatus AthenaApiv3.CanReachStatus

	return GetCanReachStatusPtr(errorCanReachStatus)

}

func GetCanReachStatusPtr(r AthenaApiv3.CanReachStatus) *AthenaApiv3.CanReachStatus {
	return &r
}
