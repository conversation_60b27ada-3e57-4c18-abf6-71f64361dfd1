package render

import (
	"context"
	"strings"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v2/model"
	"github.com/spf13/cast"
)

type BaseRender struct {
	baseConfig  BaseConfig
	baseRequest *model.Request
}

type BaseConfig struct {
	RichTimeMsg   string                        `json:"rich_time_msg"`
	RichFeeMsg    string                        `json:"rich_fee_msg"`
	WalkDesc      WalkDesc                      `json:"walk_desc"`
	StationDesc   StationDesc                   `json:"station_desc"`
	ReminderInfo  Reminder                      `json:"reminder_info"`
	TitleConf     map[string]string             `json:"title_conf"`
	SegmentConf   map[string]proto.Segment      `json:"segment_conf"`
	RecommendConf map[string]proto.RecommendTag `json:"recommend_conf"`
}

type Reminder struct {
	Icon      string `json:"icon"`
	TextColor string `json:"text_color"`
	BgColor   string `json:"bg_color"`
}

type WalkDesc struct {
	KM       string `json:"km"`
	M        string `json:"m"`
	WalkIcon string `json:"walk_icon"`
}

type StationDesc struct {
	Station1 string `json:"station_1"`
	Station2 string `json:"station_2"`
}

func (b *BaseRender) renderPlanTitle(plan *model.PlanFull) string {
	// 单段直接渲染
	if len(plan.SegmentDataList) == 1 {
		return "全程" + b.renderSegmentTitle(plan.SegmentDataList[0])
	}

	// 多段组合需要过滤步行
	var (
		res     = ""
		modeMap = make(map[string]struct{})
	)

	for _, segment := range plan.SegmentDataList {
		if segment.Mode == model.ModeWalk {
			continue
		}
		var buf strings.Builder
		buf.WriteString(segment.Mode)
		if segment.SubMode != "" {
			buf.WriteString("_")
			buf.WriteString(segment.SubMode)
		}
		key := buf.String()
		if _, ok := modeMap[key]; ok {
			continue
		}
		res += b.renderSegmentTitle(segment)

		modeMap[key] = struct{}{}
	}

	return res
}

func (b *BaseRender) renderSegmentTitle(seg *model.SegmentItem) string {
	var buf strings.Builder
	buf.WriteString(seg.Mode)
	if seg.SubMode != "" {
		buf.WriteString("_")
		buf.WriteString(seg.SubMode)
	}
	key := buf.String()

	res, _ := b.baseConfig.TitleConf[key]
	return res
}

func (b *BaseRender) renderSegmentList(plan *model.PlanFull) (res [][]*proto.Segment) {
	if len(plan.SegmentDataList) <= 0 {
		return
	}
	var (
		modeIndex           = -1
		lastShowSegmentMode *model.SegmentItem
	)

	// 单段直接渲染
	if len(plan.SegmentDataList) == 1 {
		res = append(res, []*proto.Segment{})
		modeIndex++
		res[modeIndex] = append(res[modeIndex],
			b.renderSegment(plan.SegmentDataList[0]),
		)
		return
	}

	// 多段组合需要过滤步行
	for _, segment := range plan.SegmentDataList {
		if segment.Mode == model.ModeWalk {
			continue
		}
		modeIndex++
		if lastShowSegmentMode != nil &&
			segment.Mode == lastShowSegmentMode.Mode &&
			lastShowSegmentMode.Mode == model.ModeTRANSIT &&
			lastShowSegmentMode.SubMode == segment.SubMode &&
			lastShowSegmentMode.SubMode == cast.ToString(model.TypeSubway) {
			// 地铁方案不新增端
			modeIndex--
		}
		if len(res) <= modeIndex {
			res = append(res, []*proto.Segment{})
		}

		res[modeIndex] = append(res[modeIndex],
			b.renderSegment(segment),
		)
		lastShowSegmentMode = segment
	}

	return
}

func (b *BaseRender) renderSegment(seg *model.SegmentItem) *proto.Segment {
	var buf strings.Builder
	buf.WriteString(seg.Mode)
	if seg.SubMode != "" {
		buf.WriteString("_")
		buf.WriteString(seg.SubMode)
	}
	key := buf.String()

	res, ok := b.baseConfig.SegmentConf[key]
	if !ok {
		return nil
	}
	if seg.Mode == model.ModeTRANSIT {
		// 公共交通要展示线路名字
		res.Content = seg.LineName
	}
	return &res
}

type TitleDescRender func(ctx context.Context, plan *model.PlanFull) string

func (b *BaseRender) renderRightTitleList(ctx context.Context, plan *model.PlanFull, opt ...TitleDescRender) []string {
	if len(opt) == 0 {
		opt = append(opt, b.buildEstimateTime)
		opt = append(opt, b.buildFeeMsg)
	}

	var res []string
	for _, render := range opt {
		desc := render(ctx, plan)
		if desc != "" {
			res = append(res, desc)
		}
	}
	return res
}

func (b *BaseRender) buildEstimateTime(ctx context.Context, plan *model.PlanFull) string {
	minutes := plan.TotalTime / 60
	if minutes <= 0 {
		return ""
	}

	return util.ReplaceTag(ctx, b.baseConfig.RichTimeMsg, map[string]string{
		"minutes": cast.ToString(minutes),
	})
}

func (b *BaseRender) buildFeeMsg(ctx context.Context, plan *model.PlanFull) string {
	if plan.Cost <= 0 {
		return ""
	}

	return util.ReplaceTag(ctx, b.baseConfig.RichFeeMsg, map[string]string{
		"cost": util.Fen2Yuan(plan.Cost),
	})
}

type DescRender func(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc

func (b *BaseRender) renderDescList(ctx context.Context, plan *model.PlanFull, opt ...DescRender) []*proto.PlanDesc {
	var res []*proto.PlanDesc
	for _, render := range opt {
		desc := render(ctx, plan)
		if desc != nil {
			res = append(res, desc)
		}
	}
	return res
}

// 步行距离数据渲染
func (b *BaseRender) buildWalkDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	// cac walk distance
	var walkDistance int32
	for _, segment := range plan.SegmentDataList {
		if segment.Mode == model.ModeWalk {
			walkDistance += segment.Distance
		}
	}

	if walkDistance >= 1000 {
		return &proto.PlanDesc{
			Icon: &b.baseConfig.WalkDesc.WalkIcon,
			Content: util.ReplaceTag(ctx, b.baseConfig.WalkDesc.KM, map[string]string{
				"kilometer": util.FormatPrice(float64(walkDistance)/1000, 1),
			}),
		}
	}

	return &proto.PlanDesc{
		Icon: &b.baseConfig.WalkDesc.WalkIcon,
		Content: util.ReplaceTag(ctx, b.baseConfig.WalkDesc.M, map[string]string{
			"meter": cast.ToString(walkDistance), // 步行673米
		}),
	}
}

// 末班车提示
func (b *BaseRender) renderTipData(content *string) *proto.TipData {

	if content == nil || *content == "" {
		return nil
	}

	return &proto.TipData{
		Text:      *content,
		Icon:      &b.baseConfig.ReminderInfo.Icon,
		TextColor: b.baseConfig.ReminderInfo.TextColor,
		BgColor:   b.baseConfig.ReminderInfo.BgColor,
	}
}

// 推荐标签
func (b *BaseRender) renderRecommendTag(rec *AthenaApiv3.UTRecItem) *proto.RecommendTag {

	if rec.RecommendReason == nil || rec.RecommendStyle == nil || rec.GetRecommendReason() == "" || rec.GetRecommendStyle() == 0 {
		return nil
	}

	res, ok := b.baseConfig.RecommendConf[cast.ToString(rec.GetRecommendStyle())]
	if !ok {
		return nil
	}
	res.Content = rec.GetRecommendReason()

	return &res
}
