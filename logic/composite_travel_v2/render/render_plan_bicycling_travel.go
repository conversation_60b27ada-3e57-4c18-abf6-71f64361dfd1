package render

import (
	"context"
	"strings"

	route "git.xiaojukeji.com/dirpc/dirpc-go-http-OrderRouteApi"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v2/model"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type BicyclePlanRender struct {
	*BaseRender

	conf     BicyclingDCMPConf
	planData []*route.BicyclingRouteDetail
}

func NewBicyclePlanRender(ctx context.Context, base *BaseRender) *BicyclePlanRender {

	conf, err := initBicyclingTravelDCMPConf(ctx)
	if err != nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "init bicycle service dcmp error || err = %v", err)
	}
	return &BicyclePlanRender{
		BaseRender: base,
		conf:       conf,
	}
}

type BicyclingDCMPConf struct {
	Duration string            `json:"duration"`
	DescTip  string            `json:"desc_tip"`
	LinkUrl  map[string]string `json:"link_url"`
}

func initBicyclingTravelDCMPConf(ctx context.Context) (BicyclingDCMPConf, error) {
	conf := BicyclingDCMPConf{}
	str := dcmp.GetDcmpContent(ctx, "one_stop_85-bicycle_travel", nil)
	err := jsoniter.UnmarshalFromString(str, &conf)
	if err != nil {
		return conf, err
	}
	return conf, nil
}

// todo: 骑行方案拆分待定
func (b *BicyclePlanRender) Render(ctx context.Context, plan *model.PlanFull, rec *AthenaApiv3.UTRecItem) *proto.NormalPlanItem {
	data := plan.GetPrivateData()
	if data == nil || len(data.BicycleData) == 0 || len(data.BicycleData[0].RouteDetail) == 0 {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "bicycle plan render err || data =%v", data)
		return nil
	}

	b.planData = data.BicycleData
	return &proto.NormalPlanItem{
		PlanType:       model.BicyclingTravel,
		PlanTitle:      b.renderPlanTitle(plan),
		TipData:        nil,
		SegmentList:    b.renderSegmentList(plan),
		DescList:       b.renderDescList(ctx, plan, b.buildBicycleDisDesc, b.buildBicycleTipDesc),
		RightTitleList: b.renderRightTitleList(ctx, plan, b.buildEstimateTime),
		LinkType:       model.LinkTypeUrl,
		LinkUrl:        b.buildLinkUrl(ctx),
		Params:         nil,
		MapParams:      nil,
		ExtraData:      nil,
		RecommendTag:   b.renderRecommendTag(rec),
	}
}

func (b *BicyclePlanRender) buildLinkUrl(ctx context.Context) string {
	url := b.conf.LinkUrl[cast.ToString(b.baseRequest.AccessKeyId)]

	return util.ReplaceTag(ctx, url, map[string]string{
		"from_lng":            cast.ToString(b.baseRequest.FromLng),
		"from_lat":            cast.ToString(b.baseRequest.FromLat),
		"from_name":           b.baseRequest.FromName,
		"from_poi_id":         b.baseRequest.FromPoiId,
		"to_lng":              cast.ToString(b.baseRequest.ToLng),
		"to_lat":              cast.ToString(b.baseRequest.ToLat),
		"to_name":             b.baseRequest.ToName,
		"to_poi_id":           b.baseRequest.ToPoiId,
		"navi_end_poi_cityid": cast.ToString(b.baseRequest.AreaInfo.ToCityId),
	})
}

func (b *BicyclePlanRender) buildEstimateTime(ctx context.Context, plan *model.PlanFull) string {
	defaultBicycle := b.planData[0]
	detailRoute := defaultBicycle.RouteDetail[0]
	split := strings.Split(detailRoute.GetEtaStr(), " ")
	if len(split) == 2 {
		return util.ReplaceTag(ctx, b.conf.Duration, map[string]string{
			"duration": split[0],
			"unit":     split[1],
		})
	}

	return ""
}

func (b *BicyclePlanRender) buildBicycleDisDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	defaultBicycle := b.planData[0]
	detailRoute := defaultBicycle.RouteDetail[0]
	disDes := strings.ReplaceAll(detailRoute.GetEdaStr(), " ", "")
	if disDes == "" {
		return nil
	}
	return &proto.PlanDesc{
		Content: disDes,
	}
}

func (b *BicyclePlanRender) buildBicycleTipDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	return &proto.PlanDesc{
		Content: b.conf.DescTip,
	}
}
