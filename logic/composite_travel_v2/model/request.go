package model

import (
	"context"
	"errors"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/risk"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/locsvr"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/spf13/cast"
)

type Request struct {
	*proto.CompositeTravelV2Req `json:"params"`

	UserInfo *passport.UserInfo `json:"user_info"`

	AreaInfo *WycAreaInfo `json:"area_info"`
}

type WycAreaInfo struct {
	FromCityId   int32
	FromDistrict string
	FromCounty   int32

	ToCityId int32
	ToCounty int32

	AbstractDistrict string
}

func NewRequest(ctx context.Context, compositeTravelReq *proto.CompositeTravelV2Req) (*Request, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: compositeTravelReq,
	}

	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		param_handler.CheckCompositeRisk(ctx, compositeV2RickCheck),
	})

	return &Request{CompositeTravelV2Req: compositeTravelReq}, do
}

func (r *Request) BuildUserInfo(ctx context.Context, req *proto.CompositeTravelV2Req) error {
	// 请求 passport
	userInfo, err := passport.GetUserInfo(ctx, req.Token, "")
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "get user err:[%v],token: [%v] ", req.Token, err)
		return errors.New("not found user info")
	}

	r.UserInfo = userInfo
	return nil
}

func (r *Request) GetApolloParam() map[string]string {
	city := ""
	if r.AreaInfo != nil {
		city = cast.ToString(r.AreaInfo.FromCityId)
	}

	return map[string]string{
		"uid":           strconv.FormatUint(r.UserInfo.UID, 10),
		"pid":           strconv.FormatUint(r.UserInfo.PID, 10),
		"app_version":   r.AppVersion,
		"access_key_id": strconv.Itoa(int(r.AccessKeyId)),
		"phone":         r.UserInfo.Phone,
		"city":          city,
	}
}

func (r *Request) BuildAreaInfo(ctx context.Context, req *proto.CompositeTravelV2Req) {

	var coords []map[string]float64

	coords = append(coords, map[string]float64{"lat": req.WycFromLat, "lng": req.WycFromLng})
	coords = append(coords, map[string]float64{"lat": req.WycToLat, "lng": req.WycToLng})

	cityInfos, err := locsvr.MultiAreaInfoByCoords(ctx, coords, req.MapType)
	if err != nil || len(cityInfos) == 0 {
		// 地点获取失败  -> down
		return
	}
	areaInfo := WycAreaInfo{}
	if cityInfos[0] != nil {
		areaInfo.FromCityId = cityInfos[0].Cityid
		if cityInfos[0].DistrictCode != nil {
			areaInfo.FromDistrict = *cityInfos[0].DistrictCode
		}
		areaInfo.FromCounty = cityInfos[0].Countyid
	}

	if cityInfos[1] != nil {
		areaInfo.ToCityId = cityInfos[1].Cityid
		areaInfo.ToCounty = cityInfos[1].Countyid
	}
	areaInfo.AbstractDistrict = areaInfo.FromDistrict + "," + strconv.Itoa(int(areaInfo.FromCounty))

	r.AreaInfo = &areaInfo
}

func compositeV2RickCheck(req2 interface{}) *risk.HackCompositeDataParams {
	req, ok := req2.(*proto.CompositeTravelV2Req)
	if !ok {
		return nil
	}

	nilStringToEmpty := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	return &risk.HackCompositeDataParams{
		AccessKeyID:  util.ToString(req.AccessKeyId),
		WycFromLng:   req.WycFromLng,
		AppVersion:   req.AppVersion,
		FromLng:      req.FromLng,
		WycFromLat:   req.WycFromLat,
		Channel:      util.ToString(req.Channel),
		ClientType:   util.ToString(req.ClientType),
		FromName:     req.FromName,
		UserType:     util.ToString(req.UserType),
		ToLng:        req.ToLng,
		OrderType:    util.ToString(req.OrderType),
		Lat:          req.Lat,
		FromLat:      req.FromLat,
		WycFromName:  req.WycFromName,
		ToLat:        req.ToLat,
		WycToLat:     req.WycToLat,
		Lng:          req.Lng,
		Ddfp:         nilStringToEmpty(req.Ddfp),
		WycToLng:     req.WycToLng,
		ToName:       req.ToName,
		WycToName:    req.WycToName,
		Token:        req.Token,
		IsRecLanding: util.ToString(util.Int32Ptr2Int32(req.IsRecLanding)),
	}
}
