package model

import (
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/spf13/cast"
)

func (plan *PlanFull) GetPlanType() *AthenaApiv3.RouteType {
	switch plan.PlanType {
	case CarHailing:
		return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_Car)
	case PublicTransit:
		return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_Bus)
	case BicyclingTravel:
		return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_Bicycle)
	case WalkingTravel:
		return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_Walking)
	}

	// 组合出行类的方案特别处理
	if plan.PlanType == CombinedTravel {
		for _, v := range plan.SegmentDataList {
			if v.Mode == ModeCAR {
				return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_ComboCar)
			}

			if v.Mode == ModeBike {
				return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_ComboBicycle)
			}
		}
	}
	var errType AthenaApiv3.RouteType
	return AthenaApiv3.RouteTypePtr(errType)
}

func (plan *PlanFull) GetTransferCnt() int32 {
	var cnt int32
	if plan.SegmentDataList == nil {
		return 0
	}

	for _, v := range plan.SegmentDataList {
		if util.InArrayStr(v.Mode, []string{ModeCAR, ModeTRANSIT, ModeBike}) {
			cnt++
		}
	}

	return cnt
}

func (plan *PlanFull) GetPrivateData() *PrivateData {
	if plan.PrivateData != nil {
		return plan.PrivateData
	}
	return &PrivateData{}
}

func (segmentItem *SegmentItem) GetSegmentRouteType() *AthenaApiv3.RouteType {
	switch segmentItem.Mode {
	case ModeCAR:
		return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_Car)
	case ModeWalk:
		return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_Walking)
	case ModeBike:
		return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_Bicycle)
	}

	if ModeTRANSIT == segmentItem.Mode {
		if segmentItem.SubMode == cast.ToString(TypeBus) {
			return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_Bus)
		}

		// 默认地铁
		return AthenaApiv3.RouteTypePtr(AthenaApiv3.RouteType_Subway)
	}
	var errType AthenaApiv3.RouteType
	return AthenaApiv3.RouteTypePtr(errType)
}

func (plan *PlanFull) GetUTCarDetailList() (res []*AthenaApiv3.UTCarDetail) {
	if plan.GetPrivateData() == nil || plan.GetPrivateData().CarData == nil {
		return
	}

	if plan.GetPrivateData().CarData.V4Rsp.EstimateData == nil || len(plan.GetPrivateData().CarData.CarList) == 0 {
		return
	}
	carList := plan.GetPrivateData().CarData.CarList
	for _, product := range carList {
		res = append(res, &AthenaApiv3.UTCarDetail{
			ProductCategory: util.Int32Ptr(int32(product.ProductCategory)),
			EstimateFee:     util.Float64Ptr(product.FeeAmount),
			SelectFlag:      util.Int32Ptr(product.IsSelected),
			EtpInfoMap:      product.EtpInfo,
		})
	}
	return
}

func (plan *PlanFull) GetGroupType() int32 {
	switch plan.PlanType {
	case CarHailing:
		return GroupTypeCarI
	case PublicTransit:
		return GroupTypeBusI
	case BicyclingTravel:
		return GroupTypeWalkBicycleI
	case WalkingTravel:
		return GroupTypeWalkBicycleI
	}

	// 组合出行类的方案特别处理
	if plan.PlanType == CombinedTravel {
		for _, v := range plan.SegmentDataList {
			if v.Mode == ModeCAR {
				return GroupTypeComboCarI
			}

			if v.Mode == ModeBike {
				return GroupTypeBusI
			}
		}
	}
	return GroupTypeControlI
}
