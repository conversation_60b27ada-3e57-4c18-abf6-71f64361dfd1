package rpc

import (
	"context"
	"errors"
	"sort"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/controller/core_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v2/model"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4"
	"git.xiaojukeji.com/nuwa/trace"
)

type CarHailingRPC struct {
	// 基础数据
	Request     *model.Request
	WycAreaInfo *model.WycAreaInfo

	// 数据存储
	V4Data *model.EstimateData
}

const (
	CarHailingLogTag = "CarHailingRPC"
)

func NewCarHailingRPC(ctx context.Context, baseRequest *model.Request) (*CarHailingRPC, error) {
	req := baseRequest.CompositeTravelV2Req
	// 参数校验
	if req.WycFromLat == 0 || req.WycFromLng == 0 || req.WycToLat == 0 || req.WycToLng == 0 ||
		req.MapType == "" || req.WycFromPoiId == "" {
		return nil, errors.New("wyc area params lost")
	}

	if baseRequest.AreaInfo == nil {
		log.Trace.Warnf(ctx, CarHailingLogTag, "baseRequest.AreaInfo is nil")
		return nil, errors.New("baseRequest.AreaInfo is nil")
	}

	// 开城校验
	if !model.GetTravelTypeIsOpen(ctx, baseRequest, model.CarHailing) {
		return nil, errors.New("config not open")
	}

	return &CarHailingRPC{
		Request:     baseRequest,
		WycAreaInfo: baseRequest.AreaInfo,
	}, nil
}

func (c *CarHailingRPC) Execute(ctx context.Context, request *model.Request) {

	v4Controller := core_estimate.EstimateV4Controller{}

	rspData := v4Controller.PEstimateV4(ctx, &estimate_v4.EstimateV4Req{
		AreaInfo: &estimate_v4.WycAreaInfo{
			FromCityId:       c.WycAreaInfo.FromCityId,
			FromDistrict:     c.WycAreaInfo.FromDistrict,
			FromCounty:       c.WycAreaInfo.FromCounty,
			ToCityId:         c.WycAreaInfo.ToCityId,
			ToCounty:         c.WycAreaInfo.ToCounty,
			AbstractDistrict: c.WycAreaInfo.AbstractDistrict,
		},
		OriReq:   request.CompositeTravelV2Req,
		UserInfo: request.UserInfo,
	})

	if rspData == nil || rspData.DataForService == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "wyc plan is nil ")
		return
	}

	// 获取车型数据
	c.V4Data = &model.EstimateData{
		CarList: rspData.DataForService,
		V4Rsp:   rspData.DataForNA.Data,
	}
}

func (c *CarHailingRPC) GetModel(ctx context.Context) (result []*model.PlanFull) {
	if c.V4Data == nil || c.V4Data.CarList == nil || len(c.V4Data.CarList) == 0 {
		return
	}
	carList := c.V4Data.CarList

	// 取价格最低车型的数据
	sort.Slice(carList, func(i, j int) bool {
		car1 := carList[i]
		car2 := carList[j]
		return car1.FeeAmount < car2.FeeAmount
	})
	var (
		bFirstCar    = true
		FirstCardata *model.PlanFull
	)

	// 优先以默勾的价格最低的车型数据
	for _, car := range carList {
		if car.IsSelected != consts.Checked {
			continue
		}

		if bFirstCar {
			FirstCardata = c.buildModel(car)
			bFirstCar = false
		}

		if car.EtpInfo != nil {
			result = append(result, c.buildModel(car))
			return
		}
	}
	if FirstCardata != nil {
		result = append(result, FirstCardata)
		return
	}

	// 兜底无默勾的第一个
	if len(carList) > 0 {
		result = append(result, c.buildModel(carList[0]))
		return
	}
	return
}

func (c *CarHailingRPC) buildModel(car *estimate_v4.CarEstimateData) *model.PlanFull {
	var (
		etp                  int32
		etd                  int32
		etq                  int32
		CarIsQu              int32
		CarEtqLen            int32
		planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型
	)

	if car.EtpInfo != nil {
		etd = car.EtpInfo.Etd * 60
		etp = car.EtpInfo.Etp * 60
	}

	if car.EtpInfo != nil && car.EtpInfo.EtQueueInfo != nil {
		etq = car.EtpInfo.EtQueueInfo.WaitTime * 60
		CarIsQu = 1
		CarEtqLen = car.EtpInfo.EtQueueInfo.QueueLength
	}

	detailSegmentItem := model.SegmentItem{
		Mode:     model.ModeCAR,
		Distance: int32(car.DriverMeter),
		Time:     etd,
		Cost:     int64(car.FeeAmount * 100),
		ExpectedInfo: model.ExpectedInfo{
			Eta:       etd - etp,
			Etp:       &etp,
			Etq:       &etq,
			CarIsQu:   CarIsQu,
			CarEtqLen: CarEtqLen,
		},
	}
	planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, &detailSegmentItem)
	return &model.PlanFull{
		PlanType:             model.CarHailing,
		TotalTime:            int64(etd),
		Cost:                 int64(car.FeeAmount * 100),
		Distance:             int32(car.DriverMeter),
		PlanFormatDetailData: planFormatDetailData,
		PrivateData: &model.PrivateData{
			CarData: c.V4Data,
		},
	}
}
