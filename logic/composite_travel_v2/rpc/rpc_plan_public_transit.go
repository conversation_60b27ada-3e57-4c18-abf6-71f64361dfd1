package rpc

import (
	"context"

	metro "git.xiaojukeji.com/dirpc/dirpc-go-http-TransitWind"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/transit_wind"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v2/model"
	"github.com/spf13/cast"
)

type PublicTransitRPC struct {
	baseRequest *model.Request
	Res         []*metro.TransitSimple
	Fid         string
}

const (
	PublicTransitRPCLogTag = "PublicTransitRPC"
)

func NewPublicTransitRPC(ctx context.Context, baseRequest *model.Request) *PublicTransitRPC {
	req := baseRequest.CompositeTravelV2Req
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Warnf(ctx, PublicTransitRPCLogTag, "req is lost")
		return nil
	}

	// 开城校验
	if !model.GetTravelTypeIsOpen(ctx, baseRequest, model.PublicTransit) {
		return nil
	}

	return &PublicTransitRPC{
		baseRequest: baseRequest,
	}
}

func (p *PublicTransitRPC) Execute(ctx context.Context, request *model.Request) {
	simpleReq := &metro.TransitInnerSimpleReq{
		ProductId:      "111",
		CallerId:       "comprehensive_rec",
		AccKey:         "w9mQQxBLInvx4Gm3d1Hv",
		BusinessSource: 101,

		Ddfp:             request.GetDdfp(),
		OriginLng:        request.GetFromLng(),
		OriginLat:        request.GetFromLat(),
		OriginName:       request.FromName,
		OriginPoiId:      &request.FromPoiId,
		DestinationLng:   request.GetToLng(),
		DestinationLat:   request.GetToLat(),
		DestinationName:  request.ToName,
		DestinationPoiId: request.GetToPoiId(),
		Token:            &request.Token,
		AppVersion:       &request.AppVersion,
	}
	if request.UserInfo != nil {
		simpleReq.Uid = cast.ToString(request.UserInfo.UID)
	}

	res := transit_wind.TransitInnerSimple(ctx, simpleReq)
	if res == nil {
		return
	}

	p.Res = res.Transits
	p.Fid = res.Fid
}

func (p *PublicTransitRPC) GetModel(ctx context.Context) (result []*model.PlanFull) {
	if len(p.Res) < 1 {
		return
	}

	for _, transit := range p.Res {
		if len(transit.Segments) < 1 {
			continue
		}

		var planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型

		for _, segment := range transit.Segments {
			var detailSegmentItem *model.SegmentItem

			if segment.Mode == model.ModeWalk || segment.Walking != nil {
				detailSegmentItem = &model.SegmentItem{
					Mode:     segment.Mode,
					SubMode:  "",
					Distance: segment.Walking.Distance,
					Time:     segment.Walking.Duration,
					Cost:     0,
				}
			}

			if segment.Mode == model.ModeTRANSIT || segment.Metrobus != nil {
				vv := segment.Metrobus[0] // @Todo： 公共交通路段目前默认使用第一辆的数据，没有传递所有车辆信息
				var viaStops []*model.Stop
				for _, stop := range vv.ViaStops {
					viaStops = append(viaStops, &model.Stop{
						Id:       stop.Id,
						Name:     stop.Name,
						Location: stop.Location,
					})
				}
				detailSegmentItem = &model.SegmentItem{
					Mode:     segment.Mode,
					SubMode:  cast.ToString(vv.Type),
					Distance: vv.GetDistance(),
					Time:     vv.GetDuration(),
					Cost:     int64(vv.GetPrice()),
					ExpectedInfo: model.ExpectedInfo{
						Etp: vv.Waittime,
						Eta: vv.GetDuration(),
					},
					StationInfo: model.StationInfo{
						StationCnt:     int32(len(vv.ViaStops)),
						ViaStops:       viaStops,
						LineId:         vv.GetId(),
						LineName:       vv.Name,
						CanReachStatus: int32(vv.GetCanreachStatus()),
					},
				}
			}

			planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, detailSegmentItem)
		}

		result = append(result, &model.PlanFull{
			TotalTime:            int64(transit.Duration),
			Cost:                 int64(transit.Cost),
			PlanType:             model.PublicTransit,
			TipData:              transit.MissingTip,
			PlanFormatDetailData: planFormatDetailData,
			PrivateData: &model.PrivateData{
				PublicTransitData: transit,
				Fid:               p.Fid,
				PlanId:            transit.TransitId,
			},
		})
	}

	return
}
