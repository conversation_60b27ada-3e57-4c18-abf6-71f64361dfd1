package rpc

import (
	"context"
	"time"

	walkApi "git.xiaojukeji.com/dirpc/dirpc-go-http-DolphinApiService"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/walking_route"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v2/model"
	"github.com/spf13/cast"
)

type WalkingTravelRpc struct {
	baseRequest *model.Request
	res         *walkApi.PassengerWalkRouteRes
}

func NewWalkingTravelRpc(ctx context.Context, baseRequest *model.Request) *WalkingTravelRpc {
	req := baseRequest.CompositeTravelV2Req
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Warnf(ctx, PublicTransitRPCLogTag, "req is lost")
		return nil
	}
	// 开城校验
	if !model.GetTravelTypeIsOpen(ctx, baseRequest, model.WalkingTravel) {
		return nil
	}

	return &WalkingTravelRpc{
		baseRequest: baseRequest,
	}
}

func (w *WalkingTravelRpc) Execute(ctx context.Context, request *model.Request) {
	tripReq := &walkApi.PassengerWalkRouteReq{
		StartLng:   request.FromLng,
		StartLat:   request.FromLat,
		EndLng:     request.ToLng,
		EndLat:     request.ToLat,
		Timestamp:  time.Now().Unix(),
		BizType:    util.Int32Ptr(90001),
		UserId:     util.Int64Ptr(cast.ToInt64(request.UserInfo.UID)),
		Caller:     util.StringPtr("walk_bubble"),
		Token:      util.StringPtr(request.Token),
		StartPoiId: util.StringPtr(request.FromPoiId),
		EndPoiId:   util.StringPtr(request.ToPoiId),
	}
	res := walking_route.RecommendWalkingTripInfo(ctx, tripReq)

	w.res = res
}

func (w *WalkingTravelRpc) GetModel(ctx context.Context) (result []*model.PlanFull) {

	if w.res == nil {
		return
	}

	var planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型

	// 构造
	detailSegmentItem := model.SegmentItem{
		Mode:     model.ModeWalk,
		Distance: w.res.GetDist(),
		Time:     w.res.GetDuration(),
		Cost:     0,
	}
	planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, &detailSegmentItem)

	result = append(result, &model.PlanFull{
		TotalTime:            int64(w.res.GetDuration()),
		Cost:                 0,
		Distance:             w.res.GetDist(),
		PlanType:             model.WalkingTravel,
		PlanFormatDetailData: planFormatDetailData,
		PrivateData: &model.PrivateData{
			WalkData: w.res,
		},
	})

	return
}
