package charter_multi_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/logutil"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const OperaKeyEstimateData = "g_order_estimate_price"

func AddPublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull, rspData *proto.CharterMultiEstimateData) {
	// 压测流量不写Public日志
	if util.IsPressureTraffic(ctx) {
		return
	}

	for _, product := range products {
		if "" == product.Product.EstimateID {
			continue
		}
		writeProductLog(ctx, product, rspData)
	}
}

func writeProductLog(ctx context.Context, full *biz_runtime.ProductInfoFull, rspData *proto.CharterMultiEstimateData) {
	if full == nil || full.BaseReqData == nil || full.Product == nil || full.GetBillInfo() == nil {
		return
	}

	logInfo := logutil.GenerateCommonProductMap(ctx, full)
	product := full.Product

	logInfo["combo_id"] = product.BizInfo.ComboID

	log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
}
