package intercity_estimate_detail

import (
	"context"
	"errors"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail"
	"github.com/spf13/cast"

	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail/internal"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/option_gen_price"
	"git.xiaojukeji.com/gulfstream/mamba/models/option_gen_product"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_dds_filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/before_price_filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type Detail interface {
	buildGEOInfo(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.GEOInfo
	buildUserOption(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.UserOption
	registerRpcProcess(ctx context.Context, gen *biz_runtime.ProductsGenerator, req *proto.IntercityEstimateDetailRequest, baseReq *models.BaseReqData, data *model.CommonData)
	registerFilter(ctx context.Context, gen *biz_runtime.ProductsGenerator, baseReq *models.BaseReqData, data *model.CommonData)
	buildRouteInfo(req *proto.IntercityEstimateDetailRequest, data *model.CommonData) *models.RouteInfo
	buildAreaInfoV2(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.AreaInfo
}

type ScanCode struct {
}

type FormatDetail struct {
}

// IntercityEstimateDetail ...
func IntercityEstimateDetail(ctx context.Context, req *proto.IntercityEstimateDetailRequest) (*proto.IntercityEstimateDetailData, error) {
	var (
		productsFull []*biz_runtime.ProductInfoFull

		logTag = "IntercityEstimateDetail"
	)

	defer func() {
		WritePublicLog(ctx, productsFull, req)
	}()

	if req == nil {
		return nil, errors.New("req is nil")
	}

	commonData, err := preBuild(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, logTag, "pre build fail, err:%v", err)
		return nil, errors.New("pre build fail")
	}

	productsFull, err = buildGeneral(ctx, req, commonData)
	if err != nil {
		return nil, err
	}

	render := intercity_estimate_detail.NewRender()
	respData, err := render.Do(ctx, productsFull, req, commonData)
	if err != nil {
		return nil, err
	}
	return respData, nil
}

// buildGeneral ...
func buildGeneral(ctx context.Context, req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) ([]*biz_runtime.ProductInfoFull, error) {
	if commonData == nil {
		return nil, errors.New("common data is nil")
	}

	detailExample := newDispatcher(req)
	baseReq, err := models.NewBaseReqDataBuilder().
		SetClientInfo(buildClientInfo(req)).
		SetGEOInfo(detailExample.buildGEOInfo(req, commonData)).
		SetPassengerInfoV2(buildPassengerInfo(req, commonData)).
		SetStationInfo(buildStationInfo(req, commonData), 0, 0).
		SetUserOption(detailExample.buildUserOption(req, commonData)).
		SetAreaInfov2(detailExample.buildAreaInfoV2(req, commonData)).
		TryBuild(ctx)

	if err != nil {
		return nil, err
	}

	productsGen, err := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pIntercityEstimateDetail"),
		biz_runtime.WithBaseReq(baseReq),
		biz_runtime.WithRouteInfoV2(detailExample.buildRouteInfo(req, commonData)),
		biz_runtime.WithSeatDetailInfoV2(buildSeatDetailInfo(commonData)),
		biz_runtime.WithPassengerDetailInfo(commonData.PassengerInfo),
		biz_runtime.WithInventory(req.MaxInventory, req.CarryChildrenMaxInventory),
	)

	if err != nil {
		return nil, err
	}

	productsGen.RegisterOptionProcessProduct(getReplaceDDS(baseReq, commonData))

	productsGen.RegisterOptionProcessPrice(getReplacePrice(baseReq, commonData))

	detailExample.registerRpcProcess(ctx, productsGen, req, baseReq, commonData)

	// 注册处理departure_time
	detailExample.registerFilter(ctx, productsGen, baseReq, commonData)

	productsFull, err := productsGen.GenProducts(ctx)
	if err != nil {
		return nil, err
	}
	//check 数据有效性
	err = validityData(ctx, productsFull)
	if err != nil {
		return nil, err
	}
	return productsFull, nil
}

func validityData(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) error {
	//vcard返回了优惠，但是查询卡列表失败或者匹配不到对应的batchId，则强行阻断流程。不阻断则会导致用户看不到勾选的卡但是报价单中存储了卡和优惠信息的不一致问题，后边发单也会有问题
	var (
		product *biz_runtime.ProductInfoFull
	)
	for _, pfull := range productFulls {
		if pfull != nil {
			product = pfull
			break
		}
	}
	//1、命中勾选卡和优惠场景
	if product.GetBizInfo().BusCardSelectedBatchId != 0 && product.GetBusCard() != nil && product.GetBusCard().Amount > 0 {
		hasSelectedBatchId := false
		//2、卡列表中没没有对应的batchId
		if product.GetBizInfo().BusCardListData != nil && len(product.GetBizInfo().BusCardListData.CardList) > 0 {
			for _, v := range product.GetBizInfo().BusCardListData.CardList {
				if v.BatchId == product.GetBizInfo().BusCardSelectedBatchId {
					hasSelectedBatchId = true
				}
			}
		}
		if !hasSelectedBatchId {
			log.Trace.Info(ctx, "IntercityEstimateDetail", "combine card discount no has batchId")
			return consts.ErrorSelectedBusCard
		}
	}
	return nil
}

// buildSeatDetailInfo ...
func buildSeatDetailInfo(data *model.CommonData) []*models.SeatDetailInfo {
	if data == nil || data.PassengerInfo == nil {
		return nil
	}

	if !util.InArrayInt32(data.PassengerInfo.Mode, []int32{
		models.RealNameNoHistory.ToInt32(),
		models.RealName.ToInt32(),
		models.NoRealName.ToInt32(),
	}) {
		return nil
	}

	return models.BuildSeatDetailInfoList(data.PassengerInfo, seat_selection_consts.IsOccupy.ToInt32())
}

// registerRpcProcess ...
func (c FormatDetail) registerRpcProcess(ctx context.Context, gen *biz_runtime.ProductsGenerator, req *proto.IntercityEstimateDetailRequest, baseReq *models.BaseReqData, data *model.CommonData) {
	routeDetailRpc := rpc_process.NewRouteDetail(baseReq)
	if routeDetailRpc != nil {
		gen.RegisterBeforePriceRpcProcess(routeDetailRpc)
	}

	busShiftInfoRpc := rpc_process.NewBusShiftInfo(ctx, baseReq)
	if busShiftInfoRpc != nil {
		gen.RegisterBeforePriceRpcProcess(busShiftInfoRpc)
	}

	detailPageUpgrade := rpc_process.NewIntercityDetailPageUpgrade(baseReq)
	if detailPageUpgrade != nil {
		gen.RegisterBeforePriceRpcProcess(detailPageUpgrade)
	}

	// 里面两个rpc
	identityPageInfoRpc := rpc_process.NewIdentityPageInfo(req, baseReq)
	if identityPageInfoRpc != nil {
		gen.RegisterBeforePrice2RpcProcess(identityPageInfoRpc)
	}

	if intercity_estimate_detail.IsNewVersionAnyCarFlow(req) {
		// 从用户中心获取用户是否有实名信息
		guardianIdentityInfo := rpc_process.NewGuardianIdentityInfo(req, baseReq)
		if guardianIdentityInfo != nil {
			gen.RegisterBeforePrice2RpcProcess(guardianIdentityInfo)
		}
		if req.IsBestShift != nil && *req.IsBestShift != 0 {
			// robin获取班次详情
			if robinRpc := rpc_process.NewBusRobinInfo(req, baseReq, data); robinRpc != nil {
				gen.RegisterAfterPriceRPCProcess(robinRpc)
			}
		}
	}
	if ticketRuleRpc := rpc_process.NewTicketReBookRefundRule(req, baseReq, data); ticketRuleRpc != nil {
		gen.RegisterAfterPriceRPCProcess(ticketRuleRpc)
	}
	if recommendCardListRpc := rpc_process.NewBusRecommendCardList(baseReq); recommendCardListRpc != nil {
		gen.RegisterAfterPriceRPCProcess(recommendCardListRpc)
	}
}

// registerRpcProcess ...
func (c ScanCode) registerRpcProcess(ctx context.Context, gen *biz_runtime.ProductsGenerator, req *proto.IntercityEstimateDetailRequest, baseReq *models.BaseReqData, data *model.CommonData) {
	scanCodeDetailRpc := rpc_process.NewRouteScanCodeDetail(baseReq)
	if scanCodeDetailRpc != nil {
		gen.RegisterAfterDdsNoBaseProductsRpcProcess(scanCodeDetailRpc)
	}
	tcRoutePriceRpc := rpc_process.NewStationPrice(baseReq)
	if tcRoutePriceRpc != nil {
		gen.RegisterBeforePriceRpcProcess(tcRoutePriceRpc)
	}

	busShiftInfoRpc := rpc_process.NewBusShiftInfo(ctx, baseReq)
	if busShiftInfoRpc != nil {
		gen.RegisterBeforePriceRpcProcess(busShiftInfoRpc)
	}

	routeDetailRpc := rpc_process.NewRouteDetail(baseReq)
	if routeDetailRpc != nil {
		gen.RegisterBeforePriceRpcProcess(routeDetailRpc)
	}

	detailPageUpgrade := rpc_process.NewIntercityDetailPageUpgrade(baseReq)
	if detailPageUpgrade != nil {
		gen.RegisterBeforePriceRpcProcess(detailPageUpgrade)
	}

	// 里面两个rpc
	identityPageInfoRpc := rpc_process.NewIdentityPageInfo(req, baseReq)
	if identityPageInfoRpc != nil {
		gen.RegisterBeforePrice2RpcProcess(identityPageInfoRpc)
	}

	if recommendCardListRpc := rpc_process.NewBusRecommendCardList(baseReq); recommendCardListRpc != nil {
		gen.RegisterAfterPriceRPCProcess(recommendCardListRpc)
	}

	if ticketRuleRpc := rpc_process.NewTicketReBookRefundRule(req, baseReq, data); ticketRuleRpc != nil {
		gen.RegisterAfterPriceRPCProcess(ticketRuleRpc)
	}
}

// registerFilter ...
func (c ScanCode) registerFilter(ctx context.Context, gen *biz_runtime.ProductsGenerator, baseReq *models.BaseReqData, data *model.CommonData) {
	pcFilter := after_dds_filter.NewScanCodeRedundancyPCFilter(baseReq)
	if pcFilter != nil {
		gen.RegisterAfterDdsFilter(pcFilter)
	}

	stationFilter := before_price_filter.NewFilterStations(baseReq)
	if stationFilter != nil {
		gen.RegisterBeforePriceFilter(stationFilter)
	}

	stationInfo := before_price_filter.NewDerivateStation(baseReq)
	if stationInfo != nil {
		gen.RegisterBeforePriceFilter(stationInfo)
	}
	if fixSelectedBusCard := before_price_filter.NewFixSelectedBusCard(baseReq); fixSelectedBusCard != nil {
		gen.RegisterBeforePriceFilter(fixSelectedBusCard)
	}
}

// registerFilter ...
func (c FormatDetail) registerFilter(ctx context.Context, gen *biz_runtime.ProductsGenerator, baseReq *models.BaseReqData, data *model.CommonData) {
	stationFilter := before_price_filter.NewFilterStations(baseReq)
	if stationFilter != nil {
		gen.RegisterBeforePriceFilter(stationFilter)
	}

	if baseReq.CommonBizInfo.CorrectStation {
		stationInfo := before_price_filter.NewDerivateStation(baseReq)
		if stationInfo != nil {
			gen.RegisterBeforePriceFilter(stationInfo)
		}
	}
	if fixSelectedBusCard := before_price_filter.NewFixSelectedBusCard(baseReq); fixSelectedBusCard != nil {
		gen.RegisterBeforePriceFilter(fixSelectedBusCard)
	}
	// 导流位跳转该参数为0，目前只针对导流位进行参数矫正，改造班次详情接口后全切
	if baseReq.CommonBizInfo.MaxInventory == 0 {
		fixMaxInventory := before_price_filter.NewFixMaxInventory(baseReq)
		if fixMaxInventory != nil {
			gen.RegisterBeforePriceFilter(fixMaxInventory)
		}
	}
}

// buildRouteInfo ...
func (c ScanCode) buildRouteInfo(req *proto.IntercityEstimateDetailRequest, data *model.CommonData) *models.RouteInfo {
	if data == nil {
		return nil
	}
	routeId, err := strconv.ParseInt(req.GetRouteId(), 10, 64)
	if err != nil {
		return nil
	}
	res := &models.RouteInfo{
		RouteId: routeId,
	}
	if req.GetAgentType() == DetailConst.QuickScanCode {
		return res
	}
	if startStationId, err := strconv.ParseInt(req.GetStartStationId(), 10, 64); err == nil {
		res.StartStationId = startStationId
	}
	if endStationId, err := strconv.ParseInt(req.GetEndStationId(), 10, 64); err == nil {
		res.EndStationId = endStationId
	}
	return res
}

// buildRouteInfo ...
func (c FormatDetail) buildRouteInfo(req *proto.IntercityEstimateDetailRequest, data *model.CommonData) *models.RouteInfo {
	if data == nil || data.Quotation == nil {
		return nil
	}

	res := &models.RouteInfo{
		RouteId:        data.Quotation.ComboId,
		StartStationId: util.Int64Ptr2Int64(data.Quotation.FromStationId),
		EndStationId:   util.Int64Ptr2Int64(data.Quotation.DestStationId),
	}
	if len(req.GetStartStationId()) > 0 {
		startStationId := cast.ToInt64(req.GetStartStationId())
		if startStationId != 0 {
			res.CorrectStation = res.StartStationId != startStationId || res.CorrectStation
			res.StartStationId = startStationId
		}
	}
	if len(req.GetEndStationId()) > 0 {
		endStationId := cast.ToInt64(req.GetEndStationId())
		if endStationId != 0 {
			res.CorrectStation = res.EndStationId != endStationId || res.CorrectStation
			res.EndStationId = endStationId
		}
	}
	return res
}

// preBuild ...
func preBuild(ctx context.Context, req *proto.IntercityEstimateDetailRequest) (*model.CommonData, error) {
	userLogic := internal.NewUserInfo(req)
	userInfo, err := userLogic.Do(ctx)
	if err != nil {
		return nil, err
	}

	passengerLogic := internal.NewPassengerInfo(req)
	passengerInfo, err := passengerLogic.Do(ctx)
	if err != nil {
		return nil, err
	}

	res := &model.CommonData{
		UserInfo:      userInfo,
		PassengerInfo: passengerInfo,
	}

	if req.GetAgentType() != DetailConst.FormatScanCode && req.GetAgentType() != DetailConst.QuickScanCode {
		quotationLogic := internal.NewQuotationInfo(req)
		quotationInfo, err := quotationLogic.Do(ctx)
		if err != nil {
			return nil, err
		}
		res.Quotation = quotationInfo
		res.NTuple = quotationInfo.GetNTuple()
	}
	return res, nil
}
func (c ScanCode) buildAreaInfoV2(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.AreaInfo {
	if commonData == nil {
		return nil
	}

	res := &models.AreaInfo{
		District: req.GetDistrict(),
	}
	if cityId, err := strconv.ParseInt(req.GetCityId(), 10, 64); err == nil {
		res.City = int32(cityId)
	}
	return res
}

func (c FormatDetail) buildAreaInfoV2(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.AreaInfo {
	if commonData == nil || commonData.Quotation == nil || commonData.NTuple == nil {
		return nil
	}
	return &models.AreaInfo{
		City:      int32(commonData.Quotation.Area),
		FromName:  req.GetStartPoiName(),
		FromLng:   cast.ToFloat64(req.GetStartLng()),
		FromLat:   cast.ToFloat64(req.GetStartLat()),
		ToName:    req.GetEndPoiName(),
		ToLat:     cast.ToFloat64(req.GetEndLat()),
		ToLng:     cast.ToFloat64(req.GetEndLng()),
		FromPoiID: req.GetStartPoiId(),
		ToPoiID:   req.GetEndPoiId(),
	}
}

func (c ScanCode) buildUserOption(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.UserOption {
	if commonData == nil {
		return nil
	}

	res := &models.UserOption{
		OrderType:              1,
		CallCarType:            0,
		CallCarPhone:           "",
		CarpoolSeatNum:         0,
		DepartureRange:         nil,
		StopoverPoints:         nil,
		MultiRequireProduct:    "",
		IsGuide:                nil,
		BusServiceShiftId:      req.BusServiceShiftId,
		AgentType:              req.GetAgentType(),
		CarPlate:               req.GetCarPlate(),
		BusCardSelectedBatchId: req.BusCardSelectedBatchId,
	}
	if len(req.GetAdultPrice()) > 0 {
		if adultPrice, err := strconv.ParseFloat(req.GetAdultPrice(), 64); err == nil {
			res.AdultPrice = adultPrice
		}
	}

	if len(req.GetDepartureTime()) > 0 {
		if departure_time, err := strconv.ParseInt(req.GetDepartureTime(), 10, 64); err == nil {
			res.DepartureTime = departure_time
		}
	}
	return res
}

// buildUserOption ...
func (c FormatDetail) buildUserOption(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.UserOption {
	if commonData == nil || commonData.Quotation == nil || commonData.NTuple == nil {
		return nil
	}

	return &models.UserOption{
		CallCarType:            0,
		CallCarPhone:           "",
		CarpoolSeatNum:         0,
		OrderType:              commonData.NTuple.OrderType,
		DepartureTime:          commonData.Quotation.DepartureTime,
		DepartureRange:         nil,
		PaymentsType:           int32(commonData.Quotation.DefaultPayType),
		StopoverPoints:         nil,
		MultiRequireProduct:    "",
		IsGuide:                nil,
		BusServiceShiftId:      commonData.Quotation.ShiftId,
		BusCardSelectedBatchId: req.BusCardSelectedBatchId,
	}
}

func getSupportScanCode(req *proto.IntercityEstimateDetailRequest) bool {
	return req.GetAgentType() == DetailConst.FormatScanCode || req.GetAgentType() == DetailConst.QuickScanCode
}

func getSupportCorrectStationId(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) bool {
	param := ApolloModel.NewUser("").
		With("phone", commonData.UserInfo.Phone).
		With("access_key_id", strconv.Itoa(int(req.GetAccessKeyId()))).
		With("app_version", req.GetAppVersion())
	toggle, err := ApolloSDK.FeatureToggle("gs_intercity_relocate_station", param)
	if err != nil || !toggle.IsAllow() || req.GetAgentType() == DetailConst.QuickScanCode {
		return false
	}
	return true
}

func buildStationInfo(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.StationInfo {
	if commonData == nil {
		return nil
	}
	res := &models.StationInfo{
		IsScanCode:              getSupportScanCode(req),
		SupportCorrectStationId: getSupportCorrectStationId(req, commonData),
	}
	if startCity, err := strconv.ParseInt(req.GetCityId(), 10, 64); err == nil {
		res.StartCity = int32(startCity)
	}
	if startStationId, err := strconv.ParseInt(req.GetStartStationId(), 10, 64); err == nil {
		res.StartStationId = startStationId
	}
	if endStationId, err := strconv.ParseInt(req.GetEndStationId(), 10, 64); err == nil {
		res.EndStationId = endStationId
	}

	res.StartPoiName = req.GetStartPoiName()
	res.EndPoiName = req.GetEndPoiName()
	res.StartPoiId = req.GetStartPoiId()
	res.EndPoiId = req.GetEndPoiId()
	return res
}

// buildPassengerInfo ...
func buildPassengerInfo(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.PassengerInfoV2 {
	if commonData == nil || commonData.UserInfo == nil {
		return nil
	}

	return &models.PassengerInfoV2{
		UID:      int64(commonData.UserInfo.UID),
		PID:      int64(commonData.UserInfo.PID),
		Phone:    commonData.UserInfo.Phone,
		Role:     commonData.UserInfo.Role,
		Channel:  commonData.UserInfo.Channel,
		UserType: 0,
		OriginID: commonData.UserInfo.OriginId,
		Token:    req.Token,
	}
}

// buildGEOInfo ...
func (a FormatDetail) buildGEOInfo(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.GEOInfo {
	if req == nil || commonData == nil || commonData.Quotation == nil || commonData.NTuple == nil {
		return nil
	}

	return &models.GEOInfo{
		MapType:           req.MapType,
		CurrLat:           commonData.Quotation.FromLat,
		CurrLng:           commonData.Quotation.FromLng,
		FromLat:           commonData.Quotation.FromLat,
		FromLng:           commonData.Quotation.FromLng,
		FromPOIID:         "",
		FromPOIType:       "",
		FromAddress:       "",
		FromName:          "",
		ToLat:             commonData.Quotation.ToLat,
		ToLng:             commonData.Quotation.ToLng,
		ToPOIID:           "",
		ToPOIType:         "",
		ToAddress:         "",
		ToName:            "",
		StopoverPointInfo: "",
	}
}

func (a ScanCode) buildGEOInfo(req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) *models.GEOInfo {
	if req == nil || commonData == nil {
		return nil
	}
	res := &models.GEOInfo{
		MapType:           req.MapType,
		FromPOIID:         "",
		FromPOIType:       "",
		FromAddress:       "",
		FromName:          "",
		ToPOIID:           "",
		ToPOIType:         "",
		ToAddress:         "",
		ToName:            "",
		StopoverPointInfo: "",
	}
	return res
}

// buildClientInfo ...
func buildClientInfo(req *proto.IntercityEstimateDetailRequest) *models.ClientInfo {
	if req == nil {
		return nil
	}
	guideTraceId := ""
	isBestShift := int32(0)
	if req.GuideTraceId != nil {
		guideTraceId = *req.GuideTraceId
	}
	if req.IsBestShift != nil {
		isBestShift = *req.IsBestShift
	}

	return &models.ClientInfo{
		AppVersion:      req.AppVersion,
		AccessKeyID:     int32(req.AccessKeyId),
		Channel:         req.Channel,
		ClientType:      0,
		Lang:            req.Lang,
		PlatformType:    0,
		TerminalID:      0,
		OriginID:        0,
		Xpsid:           util.StringPtr2String(req.Xpsid),
		XpsidRoot:       util.StringPtr2String(req.XpsidRoot),
		Imei:            "",
		MenuID:          "dache_anycar",
		PageType:        39,
		SourceID:        0,
		TabList:         "",
		BargainFrom:     "",
		IsScanCode:      "",
		ScanCodeShiftId: "",
		GuideTraceId:    guideTraceId,
		IsBestShift:     isBestShift,
	}
}

// getReplaceDDS ...
func getReplaceDDS(baseReq *models.BaseReqData, commonData *model.CommonData) biz_runtime.OptionProcessProduct {
	if commonData == nil || commonData.Quotation == nil || baseReq == nil {
		return nil
	}

	return option_gen_product.NewStationServiceByQuotation(baseReq, commonData.Quotation, commonData.NTuple)
}

func newDispatcher(req *proto.IntercityEstimateDetailRequest) Detail {
	switch req.GetAgentType() {
	case DetailConst.FormatScanCode:
		return ScanCode{}
	case DetailConst.QuickScanCode:
		return ScanCode{}
	default:
		return FormatDetail{}
	}
}

func getReplacePrice(baseReq *models.BaseReqData, commonData *model.CommonData) biz_runtime.OptionProcessPrice {
	agentType := baseReq.CommonBizInfo.AgentType
	if agentType == DetailConst.QuickScanCode || agentType == DetailConst.FormatScanCode {
		return option_gen_price.NewBypassPriceInterception()
	}
	return nil
}
