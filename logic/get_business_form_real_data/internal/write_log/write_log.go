package write_log

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_business_form_real_data/model"
)

func WritePublicLog(ctx context.Context, baseReq *model.BaseReq, runtimeData *model.RunTimeData) public_log.WritePublicFunc {
	return func() {
		if baseReq == nil || runtimeData == nil {
			return
		}

		logInfo := make(map[string]interface{})

		if baseReq.Params != nil {
			logInfo["access_key_id"] = baseReq.Params.AccessKeyID
			logInfo["app_version"] = baseReq.Params.AppVersion
			logInfo["lang"] = baseReq.Params.Lang
		}

		if baseReq.AreaInfo != nil {
			logInfo["city"] = baseReq.AreaInfo.City
		}

		enterGlobalSceneExpect := runtimeData.EnterpriseGlobalSceneExpect
		if enterGlobalSceneExpect != nil && enterGlobalSceneExpect.GlobalExpectInfo != nil && enterGlobalSceneExpect.GlobalExpectInfo.GetExpectInfo() != nil {
			logInfo["global_scene_expect_etp"] = enterGlobalSceneExpect.GlobalExpectInfo.GetExpectInfo().Etp
			logInfo["global_scene_expect_ets"] = enterGlobalSceneExpect.GlobalExpectInfo.GetExpectInfo().Ets
			logInfo["global_scene_expect_answer_rate"] = enterGlobalSceneExpect.GlobalExpectInfo.GetExpectInfo().AnswerRate
			if enterGlobalSceneExpect.GlobalExpectInfo.GetExpectInfo().QueueInfo != nil {
				logInfo["global_scene_expect_etq"] = enterGlobalSceneExpect.GlobalExpectInfo.GetExpectInfo().QueueInfo.Etq
			}
		}

		log.Public.Public(ctx, public_log.GBusinessRealData, logInfo)
	}
}
