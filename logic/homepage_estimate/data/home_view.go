package data

import (
	"context"

	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/callcar_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_carpool"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_unione"
)

type Render struct {
}

func NewRender(ctx context.Context) (r *Render) {
	// 初始化配置
	r = &Render{}
	return
}

func (r *Render) PreBuilder(ctx context.Context, products []*biz_runtime.ProductInfoFull) {
	var fastPrice float64
	for _, pFull := range products {
		if pFull.GetProductCategory() == ProductCategory.ProductCategoryFast {
			fastPrice = pFull.GetEstimateFee()
		}
	}
	for _, pFull := range products {
		pFull.BaseReqData.CommonBizInfo.FastCarPrice = fastPrice
	}

}

func (r *Render) RenderByProduct(ctx context.Context, product *biz_runtime.ProductInfoFull) (resp *proto.CallCarEstimateData) {
	if product == nil || product.Product == nil {
		return nil
	}
	prov := &CallCarEstimateAdapter{ProductInfoFull: product}

	resp = &proto.CallCarEstimateData{
		EstimateId:      product.Product.EstimateID,
		ProductCategory: product.Product.ProductCategory,
	}
	r.buildScenes(resp, product)

	// 车型信息
	resp.CarTitle = car_info.GetCarName(ctx, prov)
	resp.CarIcon = car_info.GetCarNormalIcon(ctx, prov)

	// 价格信息
	resp.FeeMsg, resp.FeeAmount, _ = fee_info_render.GetFeeInfo(ctx, prov)
	resp.FeeDescList = getPriceInfoDescList(ctx, prov, product.GetPageType())
	resp.MultiPriceList = fee_info_render.GetMultiPriceDescSation(ctx, prov)

	// 其他信息
	resp.CarpoolSeatList = callcar_estimate.GetCarpoolSeatNum(ctx, prov)

	return resp
}

func (r *Render) buildScenes(resp *proto.CallCarEstimateData, product *biz_runtime.ProductInfoFull) {
	resp.ExtraMap = &proto.NewFormExtraMap{
		BusinessId:   product.Product.BusinessID,
		RequireLevel: product.Product.RequireLevel,
		ComboType:    product.Product.ComboType,
		LevelType:    product.Product.LevelType,
	}
}

func (r *Render) ExtendDataRender(ctx context.Context, resp *proto.HomePageCallCarEstimateData) {
	// 费用详情页
	feeDetailUrl := fee_detail_info.GetDetailUrl(ctx)
	resp.FeeDetailUrl = &feeDetailUrl
}

func getPriceInfoDescList(ctx context.Context, prov *CallCarEstimateAdapter, pageType int32) []*proto.NewFormFeeDesc {
	if prov.GetProductCategory() == ProductCategory.ProductCategoryCarpoolStation {
		// 两口价v3
		return category_carpool.GetPriceInfoDescList(ctx, prov, pageType)
	} else if prov.GetProductId() == product_id.ProductIdUniOne {
		// 出租车
		return category_unione.GetPriceInfoDescList(ctx, prov)
	}
	return fee_info_render.GetPriceInfoDescList(ctx, prov)
}
