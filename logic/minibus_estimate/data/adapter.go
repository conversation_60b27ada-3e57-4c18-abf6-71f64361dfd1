package data

import (
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
)

// MiniBusAdapter 追加适配器
type MiniBusAdapter struct {
	*biz_runtime.ProductInfoFull
}

func (a *MiniBusAdapter) GetProductCheckStatus() int {
	return a.GetBizInfo().CheckStatus
}

func (a *MiniBusAdapter) GetMultiRequireProduct() []models.RequireProduct {
	if a.BaseReqData == nil {
		return nil
	}

	return a.BaseReqData.CommonBizInfo.MultiRequireProduct
}

func (a *MiniBusAdapter) GetCurrentPaymentType() int {
	opts := a.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (a *MiniBusAdapter) GetAllPaymentOptions() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := a.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}
