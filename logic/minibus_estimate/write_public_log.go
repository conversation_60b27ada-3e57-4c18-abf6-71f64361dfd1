package minibus_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const OperaKeyEstimateData = "g_order_cap_multi_estimate_price"

func AddPubliclog(ctx context.Context, products []*biz_runtime.ProductInfoFull, req *proto.MiniBusEstimateRequest) {

	if util.IsPressureTraffic(ctx) {
		return
	}

	for _, product := range products {
		if product == nil || product.Product == nil {
			continue
		}
		logInfo := make(map[string]interface{})
		logInfo["estimate_trace_id"] = util.GetTraceIDFromCtxWithoutCheck(ctx)
		logInfo["estimate_id"] = product.GetEstimateID()

		if req != nil {
			logInfo["xpsid"] = req.Xpsid
			logInfo["xpsid_root"] = req.XpsidRoot
		}

		//地理位置信息
		areaInfo := product.GetAreaInfo()
		logInfo["area"] = areaInfo.City
		logInfo["to_area"] = areaInfo.ToArea
		logInfo["flat"] = areaInfo.FromLat
		logInfo["flng"] = areaInfo.FromLng
		logInfo["tlat"] = areaInfo.ToLat
		logInfo["tlng"] = areaInfo.ToLng
		logInfo["district"] = areaInfo.District
		logInfo["current_lat"] = areaInfo.CurLat
		logInfo["current_lng"] = areaInfo.CurLng
		logInfo["county"] = areaInfo.FromCounty
		logInfo["to_county"] = areaInfo.ToCounty
		logInfo["from_name"] = util.StringEscape(areaInfo.FromName)
		logInfo["to_name"] = util.StringEscape(areaInfo.ToName)

		//端信息
		commonInfo := product.GetClientInfo()
		logInfo["app_version"] = commonInfo.AppVersion
		logInfo["client_type"] = commonInfo.ClientType
		logInfo["access_key_id"] = commonInfo.AccessKeyID
		logInfo["channel"] = commonInfo.Channel
		logInfo["page_type"] = commonInfo.PageType
		logInfo["lang"] = commonInfo.Lang

		//产品信息
		logInfo["product_category"] = product.GetProductCategory()
		logInfo["require_level"] = product.Product.RequireLevel
		logInfo["combo_type"] = product.Product.ComboType
		logInfo["carpool_type"] = product.Product.CarpoolType
		logInfo["product_id"] = product.Product.ProductID
		if product.GetBizInfo() != nil {
			logInfo["combo_id"] = product.GetBizInfo().ComboID
		}

		//用户信息
		logInfo["pid"] = product.GetUserInfo().PID
		if product.Product.BizInfo != nil {
			logInfo["carpool_seat_num"] = product.Product.BizInfo.CarpoolSeatNum
			if product.Product.BizInfo.MiniBusPreMatch != nil && product.Product.BizInfo.MiniBusPreMatch.GetEtpInfo() != nil {
				logInfo["etp_time_duration"] = product.Product.BizInfo.MiniBusPreMatch.GetEtpInfo().EtpTimeDuration
				if respTime, ok := product.Product.BizInfo.MiniBusPreMatch.ExtMap["wait_answer_time"]; ok {
					logInfo["candidate_ets"] = respTime
				}
			}
			if product.Product.BizInfo.MiniBusPreMatch != nil && product.Product.BizInfo.MiniBusPreMatch.ExtMap != nil {
				if longTripId, ok := product.Product.BizInfo.MiniBusPreMatch.ExtMap["long_trip_id"]; ok {
					logInfo["long_trip_id"] = longTripId
				}
			}
		}
		logInfo["from_type"] = req.FromType

		log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
	}

}
