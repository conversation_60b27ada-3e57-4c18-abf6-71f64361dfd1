package prepay_interoperation_product

import (
	"context"
	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/passenger-common/sdk/apollo"
	"github.com/spf13/cast"
)

type InteroperableProduct struct {
	isAllow                          bool
	GroupName                        string
	InteroperableAccountProductLists [][]int
}

func NewInteroperableProduct(ctx context.Context, orderInfo *dos.OrderInfo) *InteroperableProduct {
	toggleParams := map[string]string{
		"key":           orderInfo.PassengerPhone,
		"access_key_id": orderInfo.PAccessKeyId,
		"channel":       orderInfo.Channel,
		"city":          orderInfo.Area,
		"phone":         orderInfo.PassengerPhone,
	}
	apolloResult := apollo.FeatureToggle("gs_broadcast_Interoperable_product_id_lists", orderInfo.PassengerPhone, toggleParams)
	if !apolloResult.IsAllow() {
		return &InteroperableProduct{}
	}

	productIdLists := [][]int{}
	productIdListsStr := apolloResult.GetAssignment().GetParameter("product_id_lists", "[]")
	err := json.Unmarshal([]byte(productIdListsStr), &productIdLists)
	_ = err

	return &InteroperableProduct{
		isAllow:                          true,
		GroupName:                        apolloResult.GetAssignment().GetGroupName(),
		InteroperableAccountProductLists: productIdLists,
	}

}

func (p *InteroperableProduct) getInteroperableAccountProductLists() [][]int {
	return p.InteroperableAccountProductLists
}

func (p *InteroperableProduct) getInteroperableAccountProductList(prepayProductId string) []int {
	productIdLists := p.InteroperableAccountProductLists
	if len(productIdLists) == 0 {
		return []int{}
	}

	for _, productIdList := range productIdLists {
		if len(productIdList) == 0 {
			continue
		}
		if util.InArrayInt(cast.ToInt(prepayProductId), productIdList) {
			return productIdList
		}
	}
	return []int{}
}

func (p *InteroperableProduct) getInteroperableProductGroupName() string {
	return p.GroupName
}
