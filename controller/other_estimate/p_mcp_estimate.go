package other_estimate

import (
	"context"
	"errors"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/mcp_estimate"
)

type PMCPEstimateController struct {
}

func (c *PMCPEstimateController) PMCPEstimate(ctx context.Context, req *proto.PMCPEstimateReq) (rsp *proto.PMCPEstimateResponse) {
	rsp = new(proto.PMCPEstimateResponse)
	var (
		bizError = BizError.Success
		err      error
	)
	defer func() {
		if rsp.Data == nil {
			rsp.Data = &proto.MCPEstimateData{}
		}
		rsp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			rsp.Errno = int32(bizError.Errno())
			rsp.Errmsg = bizError.Error()
		}
	}()

	// 参数校验
	err = mcp_estimate.CheckParams(ctx, req)
	if err != nil {
		bizError = BizError.ErrInvalidArgument
		return
	}

	// 初始化业务逻辑
	pg, err := mcp_estimate.InitLogic(ctx, req)
	if err != nil {
		bizError = BizError.ErrSystem
		var shadow BizError.BizError
		if errors.As(err, &shadow) {
			bizError = shadow
		}
		return
	}

	// 执行业务逻辑
	rsp.Data, err = pg.DoBizLogic(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		var shadow BizError.BizError
		if errors.As(err, &shadow) {
			bizError = shadow
		}
		return
	}

	return
}
