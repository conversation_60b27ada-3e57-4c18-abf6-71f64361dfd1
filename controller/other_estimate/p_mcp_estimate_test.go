package other_estimate

import (
	"context"
	"errors"
	"testing"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/mcp_estimate"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

// mockCommonDependencies Mock通用依赖项
func mockCommonDependencies() {
	// Mock日志相关
	mockey.Mock((*ddlog.DiLogHandle).Errorf).Return().Build()
	mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
	mockey.Mock((*ddlog.DiLogHandle).Infof).Return().Build()
	
	// Mock trace ID获取
	mockey.Mock(util.GetTraceIDFromCtxWithoutCheck).Return("test_trace_id").Build()
}

// mockValidPMCPEstimateReq 生成有效的请求参数
func mockValidPMCPEstimateReq() *proto.PMCPEstimateReq {
	return &proto.PMCPEstimateReq{
		Uid:         123456789,
		Pid:         987654321,
		Phone:       "13800138000",
		AppVersion:  "1.0.0",
		AccessKeyId: 1001,
		Channel:     1,
		Lang:        "zh-CN",
		FromLat:     39.908823,
		FromLng:     116.397470,
		FromName:    "天安门广场",
		ToLat:       39.918058,
		ToLng:       116.397026,
		ToName:      "故宫博物院",
	}
}

// mockValidMCPEstimateData 生成有效的响应数据
func mockValidMCPEstimateData() *proto.MCPEstimateData {
	return &proto.MCPEstimateData{
		EstimateTraceId: "test_trace_id",
		Products: []*proto.MCPProduct{
			{
				EstimateId:       "test_estimate_id_001",
				ProductCategory:  1,
				OrderType:        0,
				ProductId:        101,
				BusinessId:       201,
				RequireLevel:     5,
				LevelType:        1,
				ComboType:        0,
				IsSpecialPrice:   false,
				CarpoolType:      0,
				CarTitle:         "快车",
				DynamicTotalFee:  15.5,
				CapPrice:         20.0,
				HighwayFee:       2.0,
				PreTotalFee:      18.0,
				BasicTotalFee:    12.0,
				EstimateFee:      15.5,
				DynamicDiffPrice: 3.5,
				CountPriceType:   1,
				DynamicTimes:     1.2,
			},
		},
	}
}

func TestPMCPEstimateController_PMCPEstimate(t *testing.T) {
	ctx := context.Background()
	controller := &PMCPEstimateController{}

	t.Run("正常场景-成功返回预估数据", func(t *testing.T) {
		mockey.PatchConvey("正常场景-成功返回预估数据", t, func() {
			// Mock通用依赖
			mockCommonDependencies()
			
			// 准备测试数据
			req := mockValidPMCPEstimateReq()
			expectedData := mockValidMCPEstimateData()
			
			// Mock业务逻辑
			mockBizLogic := &mcp_estimate.BizLogic{}
			
			// Mock参数校验成功
			mockey.Mock(mcp_estimate.CheckParams).Return(nil).Build()
			
			// Mock初始化业务逻辑成功
			mockey.Mock(mcp_estimate.InitLogic).Return(mockBizLogic, nil).Build()
			
			// Mock业务逻辑执行成功
			mockey.Mock((*mcp_estimate.BizLogic).DoBizLogic).Return(expectedData, nil).Build()
			
			// 执行测试
			resp := controller.PMCPEstimate(ctx, req)
			
			// 验证结果
			assert.Equal(t, int32(BizError.ErrnoSuccess), resp.Errno, "错误码应该为成功")
			assert.Empty(t, resp.Errmsg, "错误信息应该为空")
			assert.NotNil(t, resp.Data, "响应数据不应该为空")
			assert.Equal(t, "test_trace_id", resp.Data.EstimateTraceId, "trace ID应该正确设置")
			assert.Len(t, resp.Data.Products, 1, "应该返回1个产品")
			assert.Equal(t, "test_estimate_id_001", resp.Data.Products[0].EstimateId, "产品估价ID应该正确")
		})
	})

	t.Run("参数校验失败场景", func(t *testing.T) {
		mockey.PatchConvey("参数校验失败场景", t, func() {
			// Mock通用依赖
			mockCommonDependencies()
			
			// 准备无效请求数据
			req := &proto.PMCPEstimateReq{
				Uid: 0, // 无效的UID
			}
			
			// Mock参数校验失败
			mockey.Mock(mcp_estimate.CheckParams).Return(errors.New("invalid uid: 0")).Build()
			
			// 执行测试
			resp := controller.PMCPEstimate(ctx, req)
			
			// 验证结果
			assert.Equal(t, int32(BizError.ErrnoInvalidArgument), resp.Errno, "错误码应该为参数错误")
			assert.NotEmpty(t, resp.Errmsg, "错误信息不应该为空")
			assert.NotNil(t, resp.Data, "响应数据不应该为空")
			assert.Equal(t, "test_trace_id", resp.Data.EstimateTraceId, "trace ID应该正确设置")
		})
	})

	t.Run("初始化业务逻辑失败-系统错误", func(t *testing.T) {
		mockey.PatchConvey("初始化业务逻辑失败-系统错误", t, func() {
			// Mock通用依赖
			mockCommonDependencies()
			
			// 准备测试数据
			req := mockValidPMCPEstimateReq()
			
			// Mock参数校验成功
			mockey.Mock(mcp_estimate.CheckParams).Return(nil).Build()
			
			// Mock初始化业务逻辑失败
			mockey.Mock(mcp_estimate.InitLogic).Return(nil, errors.New("system error")).Build()
			
			// 执行测试
			resp := controller.PMCPEstimate(ctx, req)
			
			// 验证结果
			assert.Equal(t, int32(BizError.ErrnoSystemError), resp.Errno, "错误码应该为系统错误")
			assert.NotEmpty(t, resp.Errmsg, "错误信息不应该为空")
			assert.NotNil(t, resp.Data, "响应数据不应该为空")
			assert.Equal(t, "test_trace_id", resp.Data.EstimateTraceId, "trace ID应该正确设置")
		})
	})

	t.Run("初始化业务逻辑失败-业务错误", func(t *testing.T) {
		mockey.PatchConvey("初始化业务逻辑失败-业务错误", t, func() {
			// Mock通用依赖
			mockCommonDependencies()
			
			// 准备测试数据
			req := mockValidPMCPEstimateReq()
			
			// Mock参数校验成功
			mockey.Mock(mcp_estimate.CheckParams).Return(nil).Build()
			
			// Mock初始化业务逻辑失败，返回业务错误
			mockey.Mock(mcp_estimate.InitLogic).Return(nil, BizError.ErrInvalidArgument).Build()
			
			// 执行测试
			resp := controller.PMCPEstimate(ctx, req)
			
			// 验证结果
			assert.Equal(t, int32(BizError.ErrnoInvalidArgument), resp.Errno, "错误码应该为参数错误")
			assert.NotEmpty(t, resp.Errmsg, "错误信息不应该为空")
			assert.NotNil(t, resp.Data, "响应数据不应该为空")
			assert.Equal(t, "test_trace_id", resp.Data.EstimateTraceId, "trace ID应该正确设置")
		})
	})

	t.Run("业务逻辑执行失败-系统错误", func(t *testing.T) {
		mockey.PatchConvey("业务逻辑执行失败-系统错误", t, func() {
			// Mock通用依赖
			mockCommonDependencies()
			
			// 准备测试数据
			req := mockValidPMCPEstimateReq()
			mockBizLogic := &mcp_estimate.BizLogic{}
			
			// Mock参数校验成功
			mockey.Mock(mcp_estimate.CheckParams).Return(nil).Build()
			
			// Mock初始化业务逻辑成功
			mockey.Mock(mcp_estimate.InitLogic).Return(mockBizLogic, nil).Build()
			
			// Mock业务逻辑执行失败
			mockey.Mock((*mcp_estimate.BizLogic).DoBizLogic).Return(nil, errors.New("business logic error")).Build()
			
			// 执行测试
			resp := controller.PMCPEstimate(ctx, req)
			
			// 验证结果
			assert.Equal(t, int32(BizError.ErrnoSystemError), resp.Errno, "错误码应该为系统错误")
			assert.NotEmpty(t, resp.Errmsg, "错误信息不应该为空")
			assert.NotNil(t, resp.Data, "响应数据不应该为空")
			assert.Equal(t, "test_trace_id", resp.Data.EstimateTraceId, "trace ID应该正确设置")
		})
	})

	t.Run("业务逻辑执行失败-业务错误", func(t *testing.T) {
		mockey.PatchConvey("业务逻辑执行失败-业务错误", t, func() {
			// Mock通用依赖
			mockCommonDependencies()
			
			// 准备测试数据
			req := mockValidPMCPEstimateReq()
			mockBizLogic := &mcp_estimate.BizLogic{}
			
			// Mock参数校验成功
			mockey.Mock(mcp_estimate.CheckParams).Return(nil).Build()
			
			// Mock初始化业务逻辑成功
			mockey.Mock(mcp_estimate.InitLogic).Return(mockBizLogic, nil).Build()
			
			// Mock业务逻辑执行失败，返回业务错误
			mockey.Mock((*mcp_estimate.BizLogic).DoBizLogic).Return(nil, BizError.ErrInvalidArgument).Build()
			
			// 执行测试
			resp := controller.PMCPEstimate(ctx, req)
			
			// 验证结果
			assert.Equal(t, int32(BizError.ErrnoInvalidArgument), resp.Errno, "错误码应该为参数错误")
			assert.NotEmpty(t, resp.Errmsg, "错误信息不应该为空")
			assert.NotNil(t, resp.Data, "响应数据不应该为空")
			assert.Equal(t, "test_trace_id", resp.Data.EstimateTraceId, "trace ID应该正确设置")
		})
	})

	t.Run("空数据场景-业务逻辑返回空数据", func(t *testing.T) {
		mockey.PatchConvey("空数据场景-业务逻辑返回空数据", t, func() {
			// Mock通用依赖
			mockCommonDependencies()
			
			// 准备测试数据
			req := mockValidPMCPEstimateReq()
			mockBizLogic := &mcp_estimate.BizLogic{}
			
			// Mock参数校验成功
			mockey.Mock(mcp_estimate.CheckParams).Return(nil).Build()
			
			// Mock初始化业务逻辑成功
			mockey.Mock(mcp_estimate.InitLogic).Return(mockBizLogic, nil).Build()
			
			// Mock业务逻辑执行成功但返回空数据
			mockey.Mock((*mcp_estimate.BizLogic).DoBizLogic).Return(nil, nil).Build()
			
			// 执行测试
			resp := controller.PMCPEstimate(ctx, req)
			
			// 验证结果
			assert.Equal(t, int32(BizError.ErrnoSuccess), resp.Errno, "错误码应该为成功")
			assert.Empty(t, resp.Errmsg, "错误信息应该为空")
			assert.NotNil(t, resp.Data, "响应数据不应该为空")
			assert.Equal(t, "test_trace_id", resp.Data.EstimateTraceId, "trace ID应该正确设置")
		})
	})

	t.Run("请求为nil场景", func(t *testing.T) {
		mockey.PatchConvey("请求为nil场景", t, func() {
			// Mock通用依赖
			mockCommonDependencies()
			
			// Mock参数校验失败
			mockey.Mock(mcp_estimate.CheckParams).Return(errors.New("request is nil")).Build()
			
			// 执行测试
			resp := controller.PMCPEstimate(ctx, nil)
			
			// 验证结果
			assert.Equal(t, int32(BizError.ErrnoInvalidArgument), resp.Errno, "错误码应该为参数错误")
			assert.NotEmpty(t, resp.Errmsg, "错误信息不应该为空")
			assert.NotNil(t, resp.Data, "响应数据不应该为空")
			assert.Equal(t, "test_trace_id", resp.Data.EstimateTraceId, "trace ID应该正确设置")
		})
	})
}
