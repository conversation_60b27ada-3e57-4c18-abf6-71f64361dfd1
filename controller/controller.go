package controller

import (
	"git.xiaojukeji.com/gulfstream/mamba/controller/charter"
	"git.xiaojukeji.com/gulfstream/mamba/controller/composite_travel"
	"git.xiaojukeji.com/gulfstream/mamba/controller/core_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/controller/order_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/controller/other_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/controller/side"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto" // 根据idl自动生成
)

var MambaServerImplement proto.MambaServer = &proto.MambaServerWrapper{Server: &Mamba{}}

// Mamba 实现 proto.MambaServer 接口
type Mamba struct {
	PingController

	core_estimate.AnyCarEstimateController
	core_estimate.AnyCarEstimateV3Controller
	core_estimate.AnyCarEstimateV4Controller
	other_estimate.PetAnyCarEstimateController
	core_estimate.CarpoolEstimateController
	core_estimate.EstimateV3Controller

	other_estimate.PCancelEstimateController
	other_estimate.PHelperEstimateController
	other_estimate.CombinedTravelEstimateController
	other_estimate.PBusinessEstimateController
	order_estimate.RouteEstimateController
	other_estimate.BargainEstimateController
	other_estimate.HomePageCallCarEstimateController
	other_estimate.HomeEstimateV2Controller
	other_estimate.CarpoolPriceController
	other_estimate.PBusinessAnyCarEstimateController
	other_estimate.PRecCarpoolEstimateController
	other_estimate.ServiceEstimateController
	other_estimate.PPBDAnyCarEstimateController
	other_estimate.PPBDEstimateController
	other_estimate.PGetEstimateDataWithoutRenderController
	other_estimate.PGetEstimateDataWithoutRenderV2Controller
	other_estimate.SFCEstimateController
	other_estimate.SFCEstimateModifyController
	other_estimate.SFCEstimateSimpleController
	other_estimate.IntercityEstimatePriceController
	other_estimate.PPickOnTimeEstimateController
	other_estimate.IntercityEstimateDetailController
	other_estimate.IntercityMutliEstimateController
	other_estimate.PreCancelEstimateController
	other_estimate.PInvitedEstimateController
	other_estimate.PAssistantEstimateController
	order_estimate.MultiPointEstimateController
	other_estimate.PDidiMiniEstimateController
	order_estimate.EstimateByOrder
	order_estimate.EstimateCarpoolOrder
	other_estimate.EstimateOrderWithoutRenderController
	other_estimate.EngageCarEstimateController
	other_estimate.ShuttleBusPageEstimateController
	other_estimate.ShuttleBusGuideBarEstimateController
	other_estimate.PetsTravelEstimateController
	other_estimate.CarpoolInvitationEstimateController
	other_estimate.OverseaEstimateController
	other_estimate.GetBusinessTailorService
	other_estimate.PMCPEstimateController

	CouponPriceController
	PGetEstimateDataByEidListController
	GetDynamicFeeDescListController

	PGetGuideInfoController
	GetIntercityBasicFeeLiteController
	PPassengerBeginChargeController

	side.GetFormRealDataController
	side.GetBusinessFormRealDataController
	side.GetAnycarEstimateCacheController
	side.DelAnycarEstimateCacheController

	other_estimate.BargainRangeEstimateController
	other_estimate.MiniBusEstimatePriceController
	other_estimate.StationBusInsteadOrderEstimateController

	composite_travel.PCompositeTravelController
	composite_travel.PCompositeTravelV2Controller
	composite_travel.PCompositeTravelPoiController
	composite_travel.PCompositeTravelV3Controller
	composite_travel.PCompositeTravelOptionsController

	PRecommendLandingController
	other_estimate.PbdStationBusDetailEstimateController
	other_estimate.PbdStationBusMultiEstimateController
	other_estimate.PbdStationBusMultiStationPriceController
	other_estimate.PbdStationBusOrderEstimateController

	charter.PCharterHomePageController
	charter.PCharterMultiEstimateController
	charter.PGetRentInfoController
	charter.PBusinessRentEstimateController

	MeetingCarEstimateController

	// 加入其他用户自定义controller以保证 Mamba 实现 proto.MambaServer 接口
	// XXXController
	// YYYController
}
