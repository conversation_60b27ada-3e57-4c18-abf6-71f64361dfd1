namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

struct OverseaEstimateReq {
    1: required string token // 用户认证token

    /**公参**/
    10: required i32    access_key_id
    11: required string appversion
    12: required string channel
    13: required i32    client_type
    14: required string lang
    15: required string map_type
    16: required i64    terminal_id
    17: required i32    platform_type
    18: optional string ddfp

    /**起终点相关**/
    30: required i32 from_area  // 起点
    31: required double from_lat
    32: required double from_lng
    33: required string from_poi_id
    34: required string from_poi_type
    35: required string from_address
    36: required string from_name
    37: required i32 to_area   // 终点
    38: required double to_lat
    39: required double to_lng
    40: required string to_poi_id
    41: required string to_poi_type
    42: required string to_address
    43: required string to_name
    44: required double lat   // 定位点
    45: required double lng
    46: required string choose_f_searchid //用户选择起点请求ID
    47: required string choose_t_searchid //用户选择终点请求ID

    /**二次预估信息**/
    60: optional string multi_require_product //用户勾选项
}

struct OverseaEstimateResponse{
    1: required i32 errno
    2: required string errmsg
    3: required OverseaEstimateData data
    4: required OverseaNoCarInfo no_car_info
}

struct OverseaNoCarInfo {
    1: required string img
    2: required string bottom_text
    3: required string button_text
    4: required string button_msg
}

struct OverseaEstimateData {
    1: required string estimate_trace_id
    2: required list<OverseaEstimateCard> estimate_data  //普通品类数据
    3: required string bottom_text  //运营方说明文案（呼叫按钮上方）
    4: required i32 is_support_multi_selection  //是否支持多勾
    5: required string fee_detail_url //费用详情
    6: required string button_text //呼叫按钮文案
    7: required ProtocolInfo protocol_info //授权提示
    8: required OverseaRecommendInfo recommend_info  //包框置顶品类数据
}

struct OverseaRecommendInfo {
    1: required OverseaThemeData theme_data  //包框信息
    2: required list<OverseaEstimateCard> estimate_data  //品类数据
}

struct OverseaThemeData {
    1: required list<string> bg_gradients  //背景色（支持渐变）
    2: required string title //主标题
    3: required string icon
    4: required string border_color //边框颜色
}

struct ProtocolInfo {
    1: required string protocol_text //授权提示文案
    2: required string link //跳转链接
    3: required ActionParams action_params  //端上透传参数
}

struct ActionParams {
    1: required i32 oversea_is_default_auth //是否默认授权，1-默认授权
}

struct OverseaEstimateCard {
    //基础数据
    1: required string estimate_id      //预估id
    2: required i64 product_category    //品类id
    3: required ProductNTuple extra_map      //品类n元组

    //费用
    10: required string fee_amount
    11: required string fee_msg

    //渲染
    20: required string car_title
    21: required list<SubTitle> sub_title_list  //字段废弃，改用car_tag_list
    22: required string car_icon       //车型分类（经济5座）
    23: required i32 is_selected
    24: optional list<NewFormFeeDesc>    fee_desc_list
    25: required list<SubTitle> car_tag_list
}