namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

include "anycar_estimate.idl"
include "bargain.idl"
include "business.idl"
include "carpool.idl"
include "charter.idl"
include "composite_travel.idl"
include "intercity.idl"
include "minibus.idl"
include "oversea.idl"
include "pbd_station_bus.idl"
include "sfc_estimate.idl"
include "station_bus.idl"
include "composite_travel_v3.idl"
include "composite_travel_options.idl"
include "meeting_car.idl"
include "p_get_guide_info.idl"

/*
 由于idl文件个数限制，尽量在已有idl文件中新增，谨慎新增idl文件
*/

/** IDL编写规范：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=********* */
struct MultiEstimatePriceRequest {
    /** 客户端参数**/
    1: required string    token
    2: required string    app_version
    3: required i32       access_key_id
    4: required i64       channel
    5: required i32       client_type
    6: required string    lang
    7: optional string    a3_token
    8: optional string    pixels
    9: required string    maptype
    10: optional string   imei
    11: optional string   suuid
    //12: optional string   terminal_id
    13: required i64      origin_id
    14: required i32      platform_type
    //15: optional string   openid
    //16: optional i32      guide_type
    17: optional string   from
    18: optional string   preferred_route_id
    //19: optional string   dialog_id
    //20: optional string   source_channel
    //21: optional double   screen_scale
    //22: optional i32      estimate_style_type // 预估表单样式，0:老样式，1:单行

    /**起终点相关信息**/
    30: required double     lat
    31: required double     lng
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_address
    37: required string     from_name
    38: required double     to_lat
    39: required double     to_lng
    40: required string     to_poi_id
    41: required string     to_poi_type
    42: required string     to_address
    43: required string     to_name
    44: required i32        dest_poi_code //非强依赖参数
    45: required string     dest_poi_tag //非强依赖参数

    /*订单属性等信息*/
    50: required string     menu_id
    51: required i32        page_type
    52: required string     departure_time  //时间戳
    53: required i32        order_type //预约单
    54: required i32        user_type // 用户类型 0表示普通用户，2表示企业用户
    55: optional i32        call_car_type
    56: optional i32        payments_type //支付类型
    57: optional string     multi_require_product //用户勾选项
    58: optional string     call_car_phone
    //60: optional i32        origin_page_type // 原始入口页面（如预约跳转接送机）
    61: optional string     stopover_points //途经点参数 json array 字符串
    62: optional i32        source_id

    /*业务信息*/
    //80: optional i32        shake_flag
    //81: optional string     pre_trace_id
    82: optional string     departure_range //城际拼车订单出发时间

    /*接送机相关*/
    120: optional string    flight_dep_code // 航班出发地三字码,如CTU
    121: optional string    flight_dep_terminal // 航班出发航站楼，如T2
    122: optional string    traffic_dep_time // 航班起飞时间字符串
    123: optional string    flight_arr_code // 航班落地三字码,如CTU
    124: optional string    flight_arr_terminal //航班落地航站楼，如T2
    125: optional string    traffic_arr_time // 航班到达时间字符串
    126: optional string    traffic_number // 航班号，如CA1405
    127: optional i16       airport_type // 接送机类型 1-接机，2-送机，端上入口，无入口传0
    128: optional i32       airport_id // 接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
    129: optional i32       shift_time // 用车偏移时间（接机时，单位：秒）

    /*活动相关*/
    150: optional i32       activity_id // 活动id，去掉原有x_activity_id

    /*B端车票*/
    //155: optional string       biz_ticket // 车票ID

    /*专车相关*/
    //160: optional i16       too_far_order_limit // 专车是否限制超远途订单

    /*特殊场景*/
    //170: optional i32       special_scene_param // 极端天气场景（如暴雨等）

    180: optional string    luxury_select_carlevels
    181: optional string    luxury_select_driver
    190: optional string    agent_type

    /*拼车相关*/
    200: optional i32      carpool_seat_num // 拼车座位数

    201: optional string   compare_estimate_trace_id
    202: optional string   compare_estimate_id
    203: optional string   combined_travel_biz_type // 组合出行业务类型

    210: optional i32      long_rent_type // 3 标识 客企包车 helper接口使用

    /*webx公参*/
    211: optional string    xpsid
    212: optional string    xpsid_root

    /* 座位细节信息 */
    213: optional string seat_detail_info

    /**** 提高扩展性需要字段 ******/
    214: optional string caller //通过caller配置限流
    215: optional list<string> field //获取信息key
}

/** 等待应答追加车型预估请求数据 */
struct AnyCarEstimateReq {
    1: required string token //token
    2: required string order_id //订单ID
    3: required string lang //desc="多语言"
    4: required i64 carpool_seat_num //拼成乐订单座位数
    5: required string app_version //端版本号
    6: required string datatype //数据类型
    7: required i64 channel //渠道标识
    8: required i32 access_key_id //终端来源标识：滴滴iPhone乘客端、滴滴安卓pad司机端、企业级服务端、open-api服务端等
    9: required i32 client_type
    10: optional i32 page_type // 可传，也可在接口内set
    11: optional string screen_pixels
    12: optional double screen_scale
    13: optional string multi_require_product //用户勾选项
    14: optional i32 origin_id //一些古早的保证预估能成功（主要是中台历史逻辑）的参数
    15: optional i32 platform_type //一些古早的保证预估能成功（主要是中台历史逻辑）的参数
    16: optional i32 user_type //用户类型，普通用户or企业用户：是开端的时候统一给端下发了，端缓存在本地的通用参数
    17: optional string map_type
    18: optional string event_key // 请求order_match的时机
    19: optional i32 font_sacle_type // 大字版标识

    /*webx公参*/
   100: optional string    xpsid
   101: optional string    xpsid_root
}

struct AnyCarEstimateData {
    1: required string estimate_trace_id //预估traceID
    2: required string estimate_id
    3: required list<AnyCarEstimateProduct> product_list //预估商品列表
    4: required string fee_detail_url //h5:费用详情
}

/** 等待应答追加车型预估返回数据 */
struct AnyCarEstimateRsp {
    1: required i32 errno // 错误码
    2: required string errmsg // 错误信息
    3: optional AnyCarEstimateData data // 预估数据
}

struct AnyCarPriceDescInfo {
    1: required string content
    2: required string left_icon
    3: required double amount
    4: required i32 type
}

/** 预估商品 */
struct AnyCarEstimateProduct {
    1: required i64 business_id //"业务ID"，由网关统一分配
    2: required i64 require_level //ProductID基础上分了子类，例如：豪华车已有require_level列表： 1000 ：豪华型 ， 1400：宝马 ， 1401：奥迪 ， 1500：奔驰， 1600：英菲尼迪
    3: required i64 combo_type //combo_type 枚举值说明： 0 无场景 1 包车 2 接机 3 送机 4 普通拼车场景（站点拼车场景） 11 mis把combo_type=11的计价规则分配给了企业专车一口价 12 有待补充 302 跨城拼车场景 303 区域一口价 306 顺路计价 309 快车拼车（多因素）一口价 310 保险查勘 314 线路一口价
    4: required i64 level_type //为了区分车型
    5: required string business_name //业务名称
    6: required double price //价格
    7: required double dynamic_total_fee //动态总价格
    8: required double exact_price //真实价格
    9: required double dynamic_times //动态耗时
    10: required  string price_desc //价格描述
    11: required  MixedFeeMsg mixed_fee_msg //混合费用信息
    12: required list<AnyCarPriceDescInfo> price_info_desc_list // 价格描述列表
    16: required  string time_desc //时间描述
    17: required  string dynamic_icon //动态icon
    18: required  string tag //标签
    19: required  string pay_tag //支付标签
    20: required i64 pay_flag //???
    21: required UserPayInfo user_pay_info //用户支付信息
    22: required list<SeatNum> seat_nums //座位信息
    23: required i64 is_selected //选择
    26: required string business_label_text //业务线label，预估返回的产品信息提供
    27: required string car_icon //汽车icon
    28: required string fee_detail_icon //费用明细图标
    29: required string estimate_id //预估ID
    30: required i64 disabled
    31: required string disabled_text
    32: required list<string> usage_info
    33: required i32 count_price_type
    34: required list<SubTitle> sub_title_list //子标题列表
    35: required i64 product_category //1滴滴专车 3滴滴快车,排除优享单 7优步快车，排除优享单 9	豪华车 20优步优享 99优享 308专车-(六座商务) 314	快车-特价车
    39: required bool is_dual_carpool_price //是否双边拼车
    40: required i16 carpool_price_type //拼车类型
    41: optional double carpool_fail_price //拼车未拼成价格
    42: optional string tips_icon // 提示图标
    43: optional i64 recommend_product//推荐品类, 默认勾选
    44: optional list<AnyCarEstimateProduct> sub_product_list//子车型品类
    45: optional list<MultiPriceDesc> multi_price_desc // 多口价价格描述
}

struct MultiPriceDesc {
    1: required string price_info //费用信息
    2: required string left_icon // icon
    3: required string content //券等价格描述
}

/** 混合费用信息 */
struct MixedFeeMsg {
    1: required string fee_msg //费用信息
    2: required string sub_fee_msg //子费用信息
}

/** 子标题列表 */
struct SubTitle {
    1: required string font_color //字体颜色
    2: required string background_color //背景颜色
    3: required string border_color //底框颜色
    4: required string background_transparency //透明度
    5: required string content //文案
    6: optional string key // 标识
    7: required string icon_url
    8: optional list<string> bg_gradients
}

/** 额外标签 */
struct ExtraTag {
    1: required string background_color //背景颜色
    2: required string text //文案
    3: required string text_color //文案颜色
}

/** tripc:依赖apollo */
struct TripCloudTitleInfo {
    1: required string background_color //背景颜色
    2: required string text //文案
    3: required string text_color //文案颜色
}

/** 座位信息 */
struct SeatNum {
    1: required string text //文案
    2: required i64 num //数量
    3: required i64 is_selected //选择
}

/** 用户支付信息 */
struct UserPayInfo {
    1: required i64 payment_id //付款编号
    2: required i64 business_const_set //常量设置
    3: required PaymentInfo payment_info//付款信息
}

/** 付款信息 */
struct PaymentInfo {
    1: required string content //内容
    2: required string font_color //字体颜色
    3: required string highlight_font_color //高亮字体
    4: required string background_color //背景颜色
    5: required string border_color //边框颜色
    6: required string background_transparency //背景透明度
}

/** 拼车座位模块 */
struct CarpoolSeatModule {
    1: required string title //标题
    2: required i16 select_value //选择的值
    3: required list<SeatConfig> seat_config //座位配置列表
}

//struct EstimateData {
//    1: required string intro_msg
//    2: required string fee_msg
//    3: required string fee_amount
//}
//
//struct EstimateResponse {
//    1: required list<EstimateData> estimate_data
//    2: required string estimate_trace_id
////    3: required i32 is_support_multi_selection //是否支持多选，预约单时不支持 0-不支持；1-支持
////    4: required list<CategoryInfo> category_info_list //分组配置
////    5: required SpecialRule special_rule //价格沟通组件文案
////    6: required string fee_detail_url //费用明细页地址
////    7: optional PluginPageInfo plugin_page_info //动调、春节服务费等发单拦截页
////    8: required UserPayInfo user_pay_info // 6.0支付方式并集
////    9: optional string pay_type_disable_msg // 用户所选支付方式与勾选表单支付方式不匹配
////    10: optional GuideInfo guide_info //弹窗信息
////    11: optional PromoteSalesRule promote_sales_rule //营销类沟通文案
////    12: optional BrandInfo brand_info //品牌信息
//}

//struct MultiEstimatePriceResp {
//    1: required i32 errno
//    2: required string errmsg
//    3: required EstimateResponse data
//}

/** 座位配置 */
struct SeatConfig {
    1: required string lable //文案
    2: required string value //值
}

/** Ping请求 */
struct PingReq {
    1: required string name // 姓名
}

/** Ping返回 */
struct PingRsp {
    1: required i32 errno     // 错误码
    2: required string errmsg // 错误信息
    3: required string data   // 返回值
}

/** 服务错误码, 会同步至idl/error/error.go文件中 */
enum ErrCode {
    /** 成功 */
    ErrOk = 0
    /** 参数错误 */
    PARAM_ERROR = 8304001
}

// 组合出行预估Res
struct CombinedTravelEstimateRes {
    1: required i32     errno   // 错误码
    2: required string  errmsg   // 错误信息
    3: optional CombinedTravelEstimateResData data  // 预估数据
}

// 组合出行预估ResData
struct CombinedTravelEstimateResData {
    1: required string estimate_trace_id    // 预估trace ID
    2: required list<CombinedTravelEstimateData> estimate_data // 预估数据
    3: required string fee_detail_url   // 费用详情页地址
}

// 组合出行预估EstimateData
struct CombinedTravelEstimateData {
    1: required CombinedTravelRequireProduct require_product
    2: required string  business_name   // 车型名称
    3: required i64     driver_metre    // 里程
    4: required i64     driver_minute   // 时间
    6: required double  price           // 应付价格
    7: required double  dynamic_total_fee   // 动态总价格
    8: required string  price_desc      // 应付价格描述
    9: required list<CombinedTravelPriceInfoDesc> price_info_desc_list // 价格描述列表
    10: required UserPayInfo user_pay_info // 用户支付信息
    11: required string fee_detail_icon // 费用明细图标
    12: required string car_icon        // 车图标
    13: required list<i64> route_id_list // 路线id列表
    14: required i32 is_selected // 是否勾选（默勾）
}

// 组合出行预估RequireProduct
struct CombinedTravelRequireProduct {
    1: required string  estimate_id // 预估ID
    2: required i64     business_id
    3: required i64     require_level
    4: required i64     combo_type
    5: required i64     level_type
    6: required i64     product_category
    7: required i64     product_id
    8: required bool    is_special_price
    9: required i16     order_type
}

// 组合出行返回PriceInfoDesc
struct CombinedTravelPriceInfoDesc {
    1: required string content
    2: required string left_icon
    3: required double amount
}

// 等待应答取消弹窗预估车型
struct CancelEstimateReq {
    1: required string token //token
    2: required string order_id //订单ID
    3: required string lang //desc="多语言"
    4: required i64 carpool_seat_num //拼成乐订单座位数
    5: required string app_version //端版本号
    6: required string datatype //数据类型
    7: required i64 channel //渠道标识
    8: required i32 access_key_id //终端来源标识：滴滴iPhone乘客端、滴滴安卓pad司机端、企业级服务端、open-api服务端等
    9: required i32 client_type
    10: optional i32 page_type // 可传，也可在接口内set
    11: optional i32 payments_type // 支付方式
}

struct CarpoolDoublePrice {
    1: required string fee_msg
    2: required string estimate_fee
}

struct CancelEstimateProduct {
    1: required string estimate_id
    2: required i64 business_id
    3: required i64 require_level
    4: required i64 combo_type
    5: required i64 level_type
    6: required i64 product_category
    7: required i64 carpool_price_type
    8: required bool is_dual_carpool_price
    9: required list<CarpoolDoublePrice> multi_fee_msg
    10: required i64 carpool_type
}

struct CancelEstimateData {
    1: required string estimate_trace_id // 预估traceID
    3: required list<CancelEstimateProduct> product_list // 预估商品列表
}

// 等待应答取消弹窗预估车型
struct CancelEstimateRsp {
    1: required i32 errno // 错误码
    2: required string errmsg // 错误信息
    3: optional CancelEstimateData data // 预估数据
}

struct StopoverPointInfo {
	1: required i32   stop_id
	2: required double lat
	3: required double lng
	4: required string  poi_id
	5: required string  name
	6: required string  address
}

struct B2BEstimateReq {
    /** 客户端参数**/
    1: required string    token
    2: required string    app_version
    3: required i32       access_key_id
    4: required i32       channel
    5: required i32       client_type
    6: required string    lang
    9: required string    maptype
    12: required string   terminal_id
    13: required i32      origin_id
    14: required i32      platform_type
    18: required string   preferred_route_id // 用户选择的路线ID
    19: required string   multi_require_product // 用户上次勾选

    /**起终点相关信息**/
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_address
    37: required string     from_name
    38: required double     to_lat // 终点定位纬度
    39: required double     to_lng // 终点定位经度
    40: required string     to_poi_id
    41: required string     to_poi_type
    42: required string     to_address
    43: required string     to_name

    /*订单属性等信息*/
    51: required i32        page_type               // 页面类型
    52: required i32        call_car_type           // 叫车类型
    53: required string     call_car_phone          // 代叫人手机号
    54: required i32        user_type               // 用户类型 0表示普通用户，2表示企业用户
    55: required string     departure_time          // 预计出发时间
    56: optional i16        carpool_seat_num        // 拼车人数 默认为1 可选1、2
    59: required i32        order_type              // 订单类型
    60: optional list<StopoverPointInfo> stopover_points

    /*接送机相关*/
    127: optional i16       airport_type            // 接送机类型 1-接机，2-送机，端上入口，无入口传0
    128: optional i32       airport_id              // 接机时为航班落地航站楼id，送机时为出发航站楼id，如1573

    /*豪华车*/
    180: optional string    luxury_select_carlevels // 豪华车已选车型
    181: optional string    luxury_select_driver    // 豪华车已选司机

    182: optional string    enable_cap_price        // 多因素一口价 字符串 0/1

    /*webx公参*/
    183: optional string    xpsid
    184: optional string    xpsid_root

    /*一口价*/
    185: optional string    enable_fast_cap_price   // 快车一口价 字符串 0/1
}
struct PBDEstimateReq {
    /** 客户端参数**/
    1: required string    token
    2: required string    app_version // 1.0.0
    3: required i32       access_key_id // 25 pbd
    4: required i32       channel
    5: required string    lang
    6: required i32       origin_id
    7: optional string    ext_pid // 腾讯出行透传ext_pid

    /**起终点相关信息**/
    20: required double     from_lat
    21: required double     from_lng
    22: required string     from_name
    23: required double     to_lat // 终点定位纬度
    24: required double     to_lng // 终点定位经度
    25: required string     to_name
    26: required i64        route_id
    27: optional string     stopover_points

    /*订单属性等信息*/
    52: required string     departure_time          // 预计出发时间 ""或"秒级时间戳"
    53: required i32        order_type              // 订单类型 0实时单/1预约单
    61: required i32        client_type
    62: required i32        user_type
    63: required i32        payments_type
    64: required string     departure_range          // 出发时间范围
    65: optional i32        carpool_seat_num        // 拼车座位数

    /*品类相关*/
    80: optional list<i32>  need_product_category    // 指定品类
}

struct B2BProduct {
    2:  required i32        product_category        // 品类ID
    3:  required i32        order_type              // 订单类型：0 是实时 1是预约
    4:  required i32        product_id              // 业务线ID
    5:  required i32        business_id             // 公共产品线ID
    6:  required i32        require_level           // 车型
    8:  required i32        level_type
    9:  required i32        combo_type
    11: required bool       is_special_price        // 一口价
    14: optional i32        airport_type            // 接送机类型 1-接机，2-送机
    15: optional i32        railway_type            // 接送站类型
    20: optional i32        carpool_seat_num        // 拼车座位数
    21: required i32        carpool_type            // 非0为拼车
    22: optional i64        combo_id                // 路线id
    23: required i32        long_rent_type          // 包车类型
}

struct SpsLikeWaitReward {
    1: required i64 amount
    2: required i64 wait_time
}

struct WaitSendCar {
    1: required i64 wait_time
}

struct MinuteValue {
    1: required i64 minute
    2: required string value
    3: required string value_now
}

struct Time {
    1: required i64 hour
    2: required list<MinuteValue> minute_list
}

struct B2BTimeSpan {
    1: required string title
    2: required list<Time> time
    3: optional list<InterCityTimeRange> time_range
    4: optional string day
    5: optional bool selected
}

struct B2BBiz {
    1: optional bool        is_show_taxi_fee            // 出租车是否展示预估价；true代表展示预估价；false代表不展示预估价
    2: optional string      designated_driver_text      // 指定司务员名称
    3: optional string      display_tags                // 偏好设置 + 乘客姓名等（x先生/播放音乐/标准防护）
    4: optional SpsLikeWaitReward wait_reward // 愿等礼
    5: optional i32 ets
    6: optional list<B2BTimeSpan> carpool_booking_time_span
    7: optional bool is_selected     // 是否勾选（默勾）
    8: optional i32 etp              // 接驾时间
    9: optional i32 queue_len        // 排队人数
    10: optional WaitSendCar wait_send_car //等必走
    11: optional string           custom_feature //个性化服务数据
    12: required i32              is_default_auth //用户是否授权
}

struct DisplayLine {
    1: required string name
    2: required double value
}

struct B2BBill {
    1: required i64                     driver_metre                    // 里程数 单位米
    2: required i64                     driver_minute                   // 预计时长 单位分支
    3: required double                  dynamic_total_fee               // 所有费用项合计，券前价
    4: required double                  total_fee                       // max(extra.SubTotal, b.MinimumPrice) 或者是b.FixFee，注意， 此处是没有动调的数据
    6: required double                  cap_price                       // 一口价，只有在一口价的场景会有
    7: required double                  highway_fee                     // 高速费
    8: required double                  limit_fee                       // 基础费
    9: required double                  cross_city_fee                  // 跨城费
    10: required map<string,double>     fee_detail_info                 // 费用明细
    11: required list<i64>              carpool_seat_config             // 拼车座位数
    12: required double                 carpool_fail_dynamic_total_fee  // 两口价拼车失败的券前价
    13: required i64                    is_has_dynamic                  // 是否有动调（暂时是0没有，1有）
    14: required double                 dynamic_diff_price              // 基于max(extra.SubTotal, b.MinimumPrice) 算出来的动调费
    15: required i64                    count_price_type                // 计价方式：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=372323632
    17: required double                 dynamic_times                   // 增加的倍数，实际倍数为 1 + DynamicTimes
    18: required i64                    dynamic_price                   // 动调价格
    20: required list<string>           route_id_list                   // 路线ID列表
    21: required double                 trip_cloud_discount_fee         // 网开台折扣
    22: required list<DisplayLine>      display_lines
    23: required double                 pre_total_fee                   // 快车综合预估价
    24: required double                 basic_total_fee                 // 可抵扣金额
    25: required string                 discount_desc                   // 优惠
    26: required double                 dynamic_member_reduce           // 会员溢价保护费
    27: required double                 designated_driver_fee           // 豪华车司务员
    28: required double                 taxi_sp_discount_fee            // 出租车愿等
    29: required double                 driver_realtime_total_fee       // 司机基础价格
}

struct CouponInfo {
    1: required string              batch_id // 批次号
    2: required string              coupon_id // 券id
    3: required i32                 amount // 抵扣的钱, 单位分
    4: required i32                 coupon_type // 立减 or 折扣, 见: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=*********
	5: optional string              coupon_tag    // 学生, 月卡, 城市卡, 呼返
	6: optional string              coupon_source // 来源: pope, 券包, 外部券
	7: optional string              max_amount    // 最大可抵扣(券的属性), 单位分
	8: optional i32                 discount     // 折扣系数 10/100
	9: optional string              coupon_amount // 非折扣券的券面额
	10: optional string             bind_time     //存量券绑定时间
	11: optional string             expire_time   //存量券过期时间
	12: optional i64                has_rule      //是否有券规则
	13: optional i64                diff_amount   //券本单排序的diff_amount
	14: optional map<string,string> extra_Info    //优惠的补充信息
}

struct B2BFee {
    1: required double estimate_fee                         // 预估价
    2: required double without_member_protect_fee           // 会员保护价格
    3: required CouponInfo coupon_info                      // 券信息
    4: required double pbd_call_return                      // pbd呼返补贴
    5: required double personal_estimate_fee                // 个人预付价格
    6: required double exact_estimate_fee                   // 原始预估价
}

struct B2BMember {
    1: required i32         use_dpa_selected // 是否使用dpa
    2: required i32         level_id         // 会员等级
    3: required string      level_name       // 等级名称（如"白金会员"）
    4: required double      protection_fee   // 会员保护费用
}

struct B2BPriceInfo {
    1: required bool is_carpool_success                     //是否拼成
    2: required double estimate_fee                         // 预估价
    3: required double without_member_protect_fee           // 会员保护价格
    4: required CouponInfo coupon_info                      // 券信息
    5: required double discount_fee                         // 和快车综合预估价的差值
    6: required double pbd_call_return                      // pbd呼返补贴
    7: required i32 pool_num // 拼到人数
}

struct B2BCarpoolFee {
    1: required i32 seat_num // 座位数
    2: required B2BPriceInfo price_info // 价格详细信息
}

struct B2BBizItem {
    1: required string                  estimate_id // 品类预估ID
    2: required B2BProduct              product     // 基础品类
    3: required B2BBill                 bill        // 账单信息
    4: required list<B2BFee>            fee         // 价格信息(企业级用)
    5: required B2BBiz                  biz         // 业务信息
    6: optional B2BMember               member      // 会员信息
    7: optional list<B2BCarpoolFee>     carpool_fee // 拼车价格信息（包含拼车1座两座拼成未拼成四项）（pbd用）
    8: optional B2BFee                  normal_fee  // 非拼车价格 （pbd用）
    9: optional NewFormUserPayInfo      user_pay_info
}

struct PluginPageInfo {
    1: required i32 type
    2: required string show_h5
    3: required string confirm_h5
}

struct B2BEstimateData {
    1: required string                  estimate_trace_id           // 预估TraceID
    2: required list<B2BBizItem>        products                    // 品类数据
    3: required bool                    is_support_multi_selection  // 是否支持车型多选
    4: optional PluginPageInfo          plugin_page_info            //动调、春节服务费等发单拦截页
    5: optional PaymentOptionModule     user_pay_info
}

struct B2BEstimateRsp {
    1: required i16 errno                   // 错误码
    2: required string errmsg               // 错误信息
    3: optional B2BEstimateData data        // 预估数据
}
/** 助手预估Res */
struct HelperEstimateRes {
    1: required i16     errno   // 错误码
    2: required string  errmsg   // 错误信息
    3: optional HelperEstimateResData data  // 预估数据
}

/** 助手预估ResData */
struct HelperEstimateResData {
    1: required string estimate_trace_id    // 预估trace ID
    2: required list<HelperEstimateData> estimate_data // 预估数据
}

struct seatDetailInfo {
    1: required i32     type
    2: required i32     passenger_count
    3: required i32     is_occupy_seat
}

/** 助手预估EstimateData */
struct HelperEstimateData {
    1: required string  estimate_id // 预估ID
    2: required i64     business_id
    3: required i64     require_level
    4: required i64     combo_type
    5: required i64     level_type
    6: required i64     product_category
    7: required i64     product_id
    8: required i64     route_type
    9: required i64     combo_id

    20: required double     fee_amount
    21: required CarpoolSeatData carpool_seat_data
    22: required MatchRoutesData match_routes_data
    23: required i32    need_auth // 本次路线是否需要实名认证 0:不需要 1：需要

    24: required i32    support_select_seat
    25: optional list<seatDetailInfo> seat_detail_info
    26: optional i32 objective_remain_num
}

/** 助手预估-拼车座位信息 */
struct CarpoolSeatData {
    1: required i32 select_value
    2: required i32 select_index
    3: required list<HelperSeatConfig> seat_config
}

struct HelperSeatConfig {
    1: required string lable //文案
    2: required i32 value //值
}

/** 助手预估-路线数据 */
struct MatchRoutesData {
    1: required string time_title // 选择派车时间
    2: required string sub_title  // 司机将在选择的时间内来接您
    3: required string left_text  // 最早接驾
    4: required string right_text // 最晚接驾
    5: required list<TimeSpan> time_span // 时间片
}

/** 助手预估时间选择数据 */
struct TimeSpan {
    1: required string title // 今天
    2: required string date  // 10月29日
    3: required list<Range> range
}

/** 助手预估时间选择数据2 */
struct Range {
    1: required string label // 00:05~06:00
    2: required string value // [1635503126,1635505200]
    3: required string tags
    4: required string tips
    5: required i32 remain_seats // 库存模式剩余座位数
}

struct B2BAnyCarEstimateReq {
    1: required string token //token
    2: required string order_id //订单ID
    3: required string lang //desc="多语言"
    5: required string app_version //端版本号
    6: required string datatype //数据类型
    7: required i64 channel //渠道标识
    8: required i32 access_key_id //终端来源标识：企业级服务端19
    9: required i32 client_type
    10: optional i32 page_type
    11: optional string  enable_cap_price // 多因素一口价 字符串 0/1
    12: optional i64     carpool_seat_num // 座位数
    13: optional string    enable_fast_cap_price   // 快车一口价 字符串 0/1

    // 不再从ufs获取
    14: required i32      origin_id
    15: optional string   map_type
    16: required i32      user_type // 用户类型，0表示普通用户，2表示企业用户
    17: required i32      platform_type


    /*webx公参*/
   100: optional string    xpsid
   101: optional string    xpsid_root
}
struct PBDAnyCarEstimateReq {
    1: required string token //token
    2: required string order_id //订单ID
    3: required string lang //desc="多语言"
    4: required string app_version //端版本号
    5: required i64 channel //渠道标识
    6: required i32 access_key_id //终端来源标识
    7: required i32 page_type //固定传36
    8: optional i64 carpool_seat_num
    9: required i32 client_type
    10: optional string ext_pid // 腾讯出行透传ext_pid
}
struct B2BAnyCarEstimateRsp {
    1: required i16 errno                   // 错误码
    2: required string errmsg               // 错误信息
    3: optional B2BEstimateData data
}

struct RecCarpoolEstimateReq {
    /** 用户相关 **/
    1: required string token
    2: required i32    user_type // 用户类型，0表示普通用户，2表示企业用户

    /** 端相关参数 **/
    3: required i32      page_type
    4: required string    app_version
    5: required i32       access_key_id
    6: required i32       channel
    7: required i32       client_type
    8: required string    lang
    9: required string    maptype
    10: required i32      platform_type
    11: required i32      origin_id
    12: required string   terminal_id

    /**起终点相关信息**/
    30: required double     lat
    31: required double     lng
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_address
    37: required string     from_name
    38: required double     to_lat
    39: required double     to_lng
    40: required string     to_poi_id
    41: required string     to_poi_type
    42: required string     to_address
    43: required string     to_name

    /*订单属性等信息*/
    50: required string     menu_id
    51: required string     departure_time  //时间戳
    52: required string     multi_require_product //用户勾选项
    53: required i32        payments_type // 用户选择的支付方式

    54: required string     tab_list //tab列表
    55: optional string     stopover_points //途经点参数 json array 字符串

     /*webx公参*/
   100: optional string    xpsid
   101: optional string    xpsid_root
}

struct NewFormFeeDesc {
	1: required string border_color // 边框RGB色值, 如#000000, 空串标识无边框
	2: required string content
	3: required string icon
    4: required double amount
    5: required i32    type
    6: optional string text_color
    7: optional string bg_color
    8: optional string highlight_color
    9: optional string custom_tag
    10: optional string key
}

struct NewFormMultiPrice{
	1: required string fee_msg
    2: required double fee_amount
	3: optional NewFormFeeDesc fee_desc
	// 价差信息
	4: optional string fee_diff_msg
	5: optional double fee_diff_amount
	6: optional NewFormFeeDesc fee_diff_desc
	7: required i32 is_large_font
	8: required i32 font_size
}

struct RecCarpoolExtraMap{
    1:required i32    product_id
    2:required i32    business_id
    3:required i32    combo_type
    4:required i32    require_level
    5:required i32    level_type
    6:required i32    count_price_type
    7:required i32    product_category
    8:required i32    page_type
}

struct AggregationRecCarpoolEstimateData {
    1: required string intro_msg // 品牌文案
    2: required string fee_msg // 价格信息
    3: required string estimate_id // 预估id
    4: required i32 category_id // 分组id
    5: required i16 select_type // 当前预估在表单是否被选中 0-未选中 1-被选中
    6: optional i32 seat_num //当前拼车的座位数
    7: required string fee_amount //价格
    8: optional list<string> route_id_list //路线ID
    9: optional list<NewFormFeeDesc>    fee_desc_list // 价格展示（复用新表单）
    10: required string extra_price_desc // 服务费
    // 发单参数
	11: required RecCarpoolExtraMap extra_map
    12: optional UserPayInfo user_pay_info
    13: required string rec_icon // 推荐icon（左）
    14:required i32    product_category
}

struct MultiAggregationRecCarpoolEstimateData { // 临时
    1: required string intro_msg // 品牌文案
    2: required string fee_msg // 价格信息
    3: required string estimate_id // 预估id
    4: required i32 category_id // 分组id
    5: required i16 select_type // 当前预估在表单是否被选中 0-未选中 1-被选中
    6: optional i32 seat_num //当前拼车的座位数
    7: required string fee_amount //价格
    8: optional list<string> route_id_list //路线ID
    9: optional list<NewFormFeeDesc>    fee_desc_list // 价格展示（复用新表单）
    10: required string extra_price_desc // 服务费
    // 发单参数
	11: required RecCarpoolExtraMap extra_map
    12: optional UserPayInfo user_pay_info
    13: optional list<AggregationRecCarpoolEstimateData> sub_product // 样式2 多一层嵌套 的 不拼车聚合
    14: required string rec_icon // 推荐icon（左）
    15:required i32    product_category

}
struct RecCarpoolEstimateData {
    1: required string intro_msg // 品牌文案
    2: required string fee_msg // 价格信息
    3: required string estimate_id // 预估id
    4: required i32 category_id // 分组id
    5: required i16 select_type // 当前预估在表单是否被选中 0-未选中 1-被选中
    6: optional i32 seat_num //当前拼车的座位数
    7: required string fee_amount //价格
    8: optional list<string> route_id_list //路线ID
    9: optional list<AggregationRecCarpoolEstimateData> sub_product // 聚合产品
    10: optional string bubble_tip // 冒泡提示
    11: optional list<NewFormFeeDesc>    fee_desc_list // 价格展示（复用新表单）
    12: optional list<NewFormMultiPrice> multi_price_list // 单品类多价格展示 (复用新表单)
    13: required string extra_price_desc // 服务费
    // 发单参数
	14: required RecCarpoolExtraMap extra_map
    15: optional UserPayInfo user_pay_info
    // 样式2的字段
    16: optional list<MultiAggregationRecCarpoolEstimateData> multi_product // 样式2 多一层嵌套 的 不拼车聚合
    17: required string rec_icon // 推荐icon（左）
    18:required i32    product_category

}

struct UserPayInfoItem {
    1: required string tag //支付方式id
    2: required string msg // 支付方式描述
    3: required string business_const_set // 企业成本中心设置
    4: optional i16 disabled // 是否可用
    5: required i16 isSelected // 是否选中
    6: optional string car_support_desc // 车型支持描述信息
}

struct RecCarpoolUserPayInfo {
    1: required string title // 标题
    2: required string sub_title // 副标题
    3: required list<UserPayInfoItem> payment_list // 支付列表
}

struct Category {
    1: required i32 category_id // 分组id
    2: required i16 seat_num // 座位信息
    3: required string seat_desc // 座位文案
    5: required i16 enable_display // 是否渲染该品类 0/1
}
struct RecCarpoolInfo {
    1: required string rec_icon
    2: required string rec_explain
    3: required string switch_link
    4: required string switch_name
}


struct RecCarpoolEstimateResponse {
    1: required list<RecCarpoolEstimateData> estimate_data
    2: required string estimate_trace_id
    3: required string fee_detail_url
    4: required list<Category> category_info_list
    5: required i32 is_support_multi_selection //是否支持多选，预约单时不支持 0:不支持；1:支持
    9: optional string seat_prefix_desc // 座位前缀文案
    10: required RecCarpoolUserPayInfo user_pay_info // 6.0支付方式并集
    12: required i32 form_style // 不拼座表单样式
    13: required RecCarpoolInfo rec_info // 推荐管理
}

struct RecCarpoolEstimateResp {
    1: required i16  errno
    2: required string  errmsg
    3: optional RecCarpoolEstimateResponse data
}

struct NewFormThemeData {
    1: optional i32    theme_type
    2: optional string theme_color//主题颜色
    3: optional list<string> selected_bg_gradients//选中品类背景渐变色
    4: optional list<string> outer_bg_gradients//品类外框背景渐变色
    5: optional string title //顶部推荐标题
    6: optional string icon //标题左侧Icon
    7: optional string right_text //右侧标题
    8: optional string right_icon //右侧icon
    9: optional string right_info_url //0.5盒子 右侧跳转链接
    10: optional i64 expire_time // 倒计时(秒)  (当前时间-券过期时间)/1000
    11: optional i64 disable_selected_bg // 是否展示背景色
    12: optional string text_color // 字体颜色
    13: optional string border_color // 字体颜色
    14: optional string left_icon // 左侧icon
}

struct NewFormPreferDataTag{
    1: required string content
    2: required string icon
}

struct NewFormPreferData{
    1: required string desc
    2: required string fee_msg
    3: required string info_url
    4: required string id
    5: required i32    count
    6: required i32    is_selected
    7: required list<NewFormPreferDataTag> tag_list
    8: optional string jump_url
}

struct NewFormLinkInfo {
    1: required string link_product
    2: required i32    is_selected
    3: required NewFormPreferData prefer_data
    4: required string fee_msg
    5: required list<NewFormFeeDesc> fee_desc_list
    6: required string info_url
    7: required i32 is_strength
    8: required i32 select_style // 1表示单勾
}

struct GroupSubTitle {
    1: required string          content
    2: required string          font_color
    3: required string          border_color
    4: required list<string>    bg_gradients
    5: required string          icon_url
    6: required string          link_url
    7: optional string          bg_url
    8: optional bool            disabled
}

struct ButtonStyle {
    1: required string border_color
    2: required string font_color
    3: required list<string> bg_gradients
}

struct NewFormBargainMsg {
    1: required string text
    2: required string guide_path
    3: required string bubble_img_url
    4: required string underline_color
    5: optional bool   show_plus_and_minus
}

struct bargainPopup {
    1: optional string title //弹框标题
    2: optional string sub_title_min //子标题-低于推荐价
    3: optional string sub_title_equal //子标题-等于推荐价
    4: optional string sub_title_max //子标题-高于推荐价
    5: optional list<i32> left_price_tag //左侧价格标签
    6: optional list<i32> right_price_tag //右侧价格标签
    7: optional string button_text // 按钮文案
    8: optional BargainFeeMarginV3 fee_margin //上下限信息
    9: optional NewFormFeeDesc fee_desc_content_template //费用描述信息的模版
    10: optional string fee_msg_template //预估价模版
}

struct BargainFeeMarginNode {
    1: optional string amount //金额
    2: optional string notice //提示
}

struct BargainFeeMarginV3 {
    1: optional BargainFeeMarginNode ceil //上限
    2: optional BargainFeeMarginNode floor1 //下限
    3: optional BargainFeeMarginNode floor2 //下下限
}

struct NewFormGroup {
    1: required i32 type //1单车型｜车大，2三方盒子，3出租车盒子，4高峰盒子，99导流, 101 司乘议价
    2: required list<string> products
    4: required i32 is_selected
    5: required string car_icon
    6: required string car_title
    7: optional string popup_title
    8: optional string unselect_popup_title // 未勾选的盒子标题
    9: optional string popup_sub_title
    10: optional string recommend_product //当用户没有选择出租车的时候，出租车盒子需要外露的一个单独的车型

    11: optional NewFormLinkInfo link_info //车大
    12: optional string guide_path //导流地址
    13: optional string button_text //导流按钮文案
    14: optional i32 box_id
    15: optional string box_desc
    16: optional string fee_desc
    17: optional string fee_desc_icon
    18: optional int32 jump_type
    20: required string group_id // group主键id 规则: type + boxid||product_category||subgroup_id
    21: optional GroupSubTitle sub_title // 盒子的标签
    22: optional ButtonStyle button_style  // 按钮样式，不设置默认原样式
    23: optional NewFormBargainMsg bargain_msg // 司乘议价砍价信息
    24: optional list<NewFormFeeDesc> fee_desc_list // 盒子外层费用描述
    25: optional string popup_toast

    26: optional i32    action_type
    27: optional i32    guide_style
    28: optional bargainPopup bargain_popup //司乘议价弹框信息
    29: optional string fee_msg // 盒子外层费用
    30: optional string fee_detail_url
    31: optional i32    style_type //字体样式 0 正常 1 大字
    32: optional i32    disable_shadow // 1 关闭阴影
    33: optional i32    is_diff_amount // 弃用 1 价差模式
    34: optional i32    is_hide_diff_desc // 弃用 1 价差模式下不展示绝对价格
    35: optional string guide_params
    36: optional string fee_msg_template //价格模版
}

struct NewFormLayout {
    1: required list<NewFormGroup> groups
    2: required i32 form_show_type
    3: required NewFormThemeData theme_data
    4: required i64 weight
    5: required double price
}

struct ServiceEstimateRequest {
    1:  required string token
    2:  required i32    access_key_id
    3:  required string app_version
    4:  required i32    channel
    5:  required i32    client_type
    6:  required string lang
    7:  required string map_type
    8:  required string terminal_id
    9:  required i32    platform_type


    /**起终点相关**/
    10: required double from_lat        // 起点
    11: required double from_lng
    12: required string from_poi_id
    13: required string from_poi_type
    14: required string from_address
    15: required string from_name
    16: required double to_lat          // 终点
    17: required double to_lng
    18: required string to_poi_id
    19: required string to_poi_type
    20: required string to_address
    21: required string to_name
    22: required double lat             // 定位点
    23: required double lng

    /**订单属性等信息**/
    30: required i32    order_type
    31: required string departure_time
    32: required i32    call_car_type
    33: required string call_car_phone

     /*webx公参*/
   100: optional string    xpsid
   101: optional string    xpsid_root
}

struct FeeDescItem {
    1: required string       content
    2: required string       icon
    3: required string       border_color
    4: required string       font_color
    5: required list<string> bg_gradients
}

struct ExtraMap {
    1: required i32 product_id
    2: required i32 business_id
    3: required i32 combo_type
    4: required i32 require_level
    5: required i32 level_type
}

struct ServiceEstimateData {
    1: required string estimate_id
    2: required i32    product_category
    3: required string fee_amount
    4: required string fee_msg
    5: optional list<FeeDescItem> fee_desc_list
    6: required ExtraMap extra_map
}

struct ServiceEstimateResponse {
    1: required list<ServiceEstimateData> estimate_data
    2: required string fee_detail_url
    3: required i32    is_show_call_car  // 1：展示代叫入口  0：不展示代叫入口
}

struct ServiceMultiEstimateResponse {
    1: required i32    errno
    2: required string errmsg
    3: optional ServiceEstimateResponse data
    4: required string trace_id
}

struct RouteEstimateRequest {
    1: required string          token
    2: required i64             origin_id
    3: required string          imei
    4: required i64             client_type
    5: required i64             user_type
    6: required i32             platform_type
    7: required i64             terminal_id
    8: required string          app_version
    9: required i32             access_key_id
    10: required string         lang

    11: required double         lat
    12: required double         lng
    13: required double         from_lat
    14: required double         from_lng
    15: required string         from_poi_id
    16: required string         from_poi_type
    17: required string         from_address
    18: required string         from_name
    19: required double         to_lat
    20: required double         to_lng
    21: required string         to_poi_id
    22: required string         to_poi_type
    23: required string         to_address
    24: required string         to_name
    25: required string         map_type

    26: required string         oid
    27: required string         stopover_points
    28: required list<string>   route_id_list
    29: optional i32            is_cap_price
    30: optional i32            is_use_discount
    31: optional i64            update_travel_scene_type

    100: optional string        caller
}

struct RouteEstimateResponse {
    1: required i32                     errno
    2: required string                  errmsg
    3: optional RouteEstimateData       data
}

struct RouteEstimateData {
    1: required string                  estimate_trace_id
    2: required list<RouteDetail>       route_detail
    3: optional map<string, string>     extra_info
}

struct RouteDetail {
    1: required string              estimate_id
    2: required string              route_id
    3: required double              estimate_fee
    4: required string              fee_msg
    5: required string              bubble_msg
    6: optional OriginBills         origin_bills
    7: required double              dynamic_total_fee
}

struct OriginBills {
    1: required i64 driver_minute
    2: required i64 driver_metre
    3: required i64 low_speed_time
    4: required string estimate_id
    5: required OrderResult passenger_order_result
    6: required OrderResult driver_order_result
    7: required map<string, string> multi_route_args
    8: required map<string, string> rb_args
    9: required string modify_dest_estimate_fixed_fees
}

struct OrderResult {
    1: required double total_fee // 最终计费结果
    2: required double cap_price // 一口价，只有在一口价的场景会有，其实和FixedFee没有区别
    3: required double pre_total_fee
    4: required i64 fence_id
    5: required map<string, double> fee_detail_info
    6: required BillExtra bill_extra
    7: required map<string, string> extra_info (go.type="map[string]interface{}",php.type="array")
}

struct BillExtra {
    1: required i64 company_carpool_flag
    2: optional double plan_intercept_diff_fee // 拟拦截费用
    3: required double realtime_price_back
    4: required double basic_total_fee
    5: required double pre_total_fee
    6: required double dynamic_total_fee
    7: required double total_fee_not_intercept
    8: required i64 free_commission
    9: required double free_commission_extra_fee
}

struct LeftInfo {
    // 车型数据
    10: required string car_name
    11: required string car_icon

    // 价格信息
    20: required string           fee_amount
    21: required string           fee_msg
    23: optional list<NewFormMultiPrice> multi_price_list
    24: optional list<NewFormFeeDesc>    fee_desc_list
}

struct ExtraMapV4 {
    1:required i64    product_id
    2:required i64    business_id
    3:required i64    combo_type
    4:required i32    require_level
    5:required i32    level_type
    6:required i64    combo_id
    7:required i64    route_type
    8:required i32    is_special_price
    9:required i32    count_price_type
   10:optional string port_type
   11:required i32    bargain_from_type
   12:required i32    is_default_auth
   13:required i32    etp
}

struct NewFormUnselectedData {
    1: optional list<NewFormFeeDesc>    fee_desc_list
    2: optional list<NewFormV4MultiPrice> multi_price_list
}

struct NewFormV4MultiPrice {
    1: required string fee_msg
    2: required double fee_amount
    3: optional NewFormFeeDesc fee_desc
    4: optional string fee_icon_url
}

struct NewFormExtraEstimateData {

	1: required string           fee_msg
	2: optional list<NewFormFeeDesc>    fee_desc_list

	3: required i32 count_down
	5: required map<string, string> extra_order_params

	6: required string left_down_icon_text
}

struct NewFormAutoDriveRedirectionInfo {
    1: required NewFormRedirectionInfo start_info //无人车上车点
    2: required NewFormRedirectionInfo end_info //无人车下车点
}

struct NewFormRedirectionInfo {
    1: optional string poi_id
    2: optional string displayname
    3: optional string address
    4: optional double lat
    5: optional double lng
    6: optional string coordinate_type
    7: optional i32 city_id
    8: optional string city_name
    9: optional string gap
}

struct RadioSetting {
    1: required string field //当前组件对应的字段
    2: required bool is_hold // 常驻 不依赖勾选
    3: required i32 selected_value
    4: required bool need_refresh  //切换勾选后 是否要重新预估
    5: required list<RadioOption> options
    6: required i32 assembly_style  //标识拼座组件在表单上的展示位置，0是左边，1是右边
}

struct RadioOption {
    1: required string label
    2: required i32 value
}

struct OrderOption {
    1: required string field
    2: required string selected_value
    3: required string default_value
    4: required string title
    5: required list<ModeListOption> mode_list
}
struct ModeListOption {
    1: required string title
    2: required string selected_title
    3: required string content
    4: required string value
}

struct GroupCarTag {
    1: required string          content               // 车型标签文案
    2: required string          font_color            // 字体未选中颜色
    3: required string          font_select_color     // 字体选中颜色
    4: required string          border_color          // 边框未选中颜色
    5: required string          border_select_color   // 边框选中颜色
}

struct NewCarpoolSeatModule {
    1: required string title
    2: required i16 max_count
    3: required i16 select_count
    4: required string seats_exceed_toast //座位数超选提示
}

struct Tag {
    1: required string content
    2: required string icon
}

struct NoticeInfo {
    1: optional list<Tag> tag_list
    2: optional string right_text
    3: optional string jump_url
    4: optional list<string> background_gradients
    5: optional string border_color
    6: optional i32 action_type
    7: optional string bubble_arrow_top
    8: optional OmegaItem omega
}

struct OmegaItem {
    1: optional OmegaValue show
    2: optional OmegaValue click
}

struct OmegaValue {
    1: required string key
    2: required map<string, i32> params
}

struct MapInfo {
    1: required string mapinfo_cache_token
    2: required bool show_mini_bus_station
    3: required string mapinfo_start_cache_token
    4: required string mapinfo_dest_cache_token
    5: required i32 best_view_type
    6: required string voy_route_id
    7: required i32 minibus_type //小巴类型：1-智能小巴（给端上跳转地图组件使用）
}

struct MapCurveInfo {
    1: required i32 is_draw_curve
    2: required string bubble_text
    3: required string jump_url
    4: optional i32 curve_type //曲线类型：0-不展示曲线；1-展示蓝灰曲线（小巴快线）；2-展示蓝色曲线（小巴普通&智能小巴）【na: 7.0.6, wx: 6.10.25以后使用】
}

struct OrderButtonInfo {
    1: required string expect_info_text
}

struct V4EstimateData {
	// 标识
	1: required string estimate_id
	2: required i64    product_category

	// 一些场景标识
	3: required i32  hit_dynamic_price
	4: required i32  hit_show_h5_type
	5: required bool is_tripcloud

	// 车型数据
	6: required string car_title
	7: required string car_icon

	// 价格信息
	8: required string           fee_amount
	9: required string           fee_msg
   10: optional list<NewFormFeeDesc>    fee_desc_list
   11: optional list<NewFormMultiPrice> multi_price_list
   27: optional NewFormUnselectedData   unselected_data
   37: optional string min_fee_amount
   38: optional string fee_range_template

	// 支付信息
	12: required NewFormUserPayInfo user_pay_info

	// 附加
	13: optional NewFormPreferData              prefer_data
	14: optional list<NewFormCarpoolSeatOption> carpool_seat_list
    15: optional list<string>                   route_id_list

	// 发单参数
	16: required ExtraMapV4 extra_map

	// 其他额外
	17: optional NewFormExtraEstimateData        extra_estimate_data // 木得办法
	18: optional NewFormAutoDriveRedirectionInfo auto_driving_address_info
	19: required i32                             is_selected //勾选状态
    20: optional string                          depart_tag
    21: optional string                          sub_intro_icon // 出租车盒子选中车型前的icon
    22: required RadioSetting                    radio_setting // 拼车选座组件/拼车顺路组件
    23: optional OrderOption                     order_option //市内拼车的顺路标签（实验，后续验证无收益记得下掉）

    24: optional string               car_sub_title   // 车型副标题
    25: optional string               tips_icon       // 车型提示icon
    26: optional GroupSubTitle        sub_title         // 车型标签
    28: optional i32                  is_hide_price //是否计算价格 0:默认,需要计算价格  1:不计算

    29: optional GroupCarTag          car_tag   // tp泛快车型标签
    30: optional double               need_pay_fee_amount //券前价，目前只有司乘议价会用到
    31: optional NewCarpoolSeatModule carpool_seat_module //小巴座位数组件
    32: optional NoticeInfo           notice_info // 品类下发横条+跳转组件
    36: optional MapInfo              map_info // 带个地图的数据，只小巴在用
    40: optional MapCurveInfo         map_curve_info // 地图曲线和气泡信息
}

struct V4Data {
    1:  required map<i64,V4EstimateData> estimate_data
    2:  required list<NewFormLayout> layout
    4:  required string fee_detail_url //费用明细页地址
    5:  required i32 is_support_multi_selection //是否支持多选，预约单时不支持 0:不支持；1:支持
    6:  optional PluginPageInfo plugin_page_info //动调、春节服务费等发单拦截页
    7:  optional string toast_tip //预估完成后提示文案
    8:  optional OrderButtonInfo order_button_info   // 发单按钮位置信息
    9:  optional map<string,i32> real_params
}

/** V4预估返回数据 */
struct PEstimateV4Response {
    1: required i32    errno // 错误码
    2: required string errmsg // 错误信息
    3: required string trace_id
    4: optional V4Data data // 预估数据
}


struct CouponPriceRequest {
    1: required string token
    2: required i32    access_key_id
    3: required string app_version
    4: required string channel
    5: required string lang
    6: required i32    from_area
    7: required double from_lat
    8: required double from_lng
    9: required double to_lat
   10: required double to_lng
   11: optional string estimate_id
   12: required double fee_amount
   13: optional i32    page_type    // 标识是等应答的请求，还有发单前预估的请求
   14: optional string oid          // 加密订单号。api用这个转成低位订单号后，作为key存储等应答确认改价后的最终价格
   15: optional i32    is_confirm   // 为1，标识是最后“确认改价”的请求
}

struct CouponEstimateData {
    1: required string estimate_fee     // 实付价金额
    2: required string fee_msg          // 实付价文案
    3: required i32    coupon_type      // 0 无可用券，1 立减券，2 折扣券
    4: required string coupon_discount  // 立减券对应立减金额 单位元；折扣券对应折扣*10
    5: required string discount_text    // 券文案
    6: required double discount_amount    // 券抵扣金额
}

struct CouponPriceResponse {
    1: required i32                errno
    2: required string             errmsg
    3: optional CouponEstimateData data
    4: required string             trace_id
}

struct PDidiMiniEstimateReq {
    /** 用户参数**/
    1:  required string    token

    /**起终点相关**/
    10: required double from_lat        // 起点
    11: required double from_lng
    12: required string from_poi_id
    13: required string from_poi_type
    14: required string from_address
    15: required string from_name
    16: required double to_lat          // 终点
    17: required double to_lng
    18: required string to_poi_id
    19: required string to_poi_type
    20: required string to_address
    21: required string to_name
    22: required double lat             // 定位点
    23: required double lng

    24: required string choose_f_searchid //用户选择起点请求ID
    25: required string choose_t_searchid //用户选择终点请求ID

    /** 客户端参数 **/
    32:  required i32    access_key_id
    33:  required string app_version
    34:  required i64    channel
    35:  required i32    client_type
    36:  required string lang
    37:  required string maptype
    38:  required i64    terminal_id
    39:  required i32    platform_type //1 IOS 2 android 3 webapp 4 oepenapi 5 b2b 6 guide
    40:  optional string ddfp
    41:  optional string screen_pixels
    42:  optional double screen_scale
    43:  required i64     origin_id //品牌ID:1滴滴； 2优步；3长平,默认滴滴

    /*webx公参*/
    100: optional string    xpsid
    101: optional string    xpsid_root
}

/** V4预估返回数据 */
struct PDidiMiniEstimateResp {
    1: required i32    errno // 错误码
    2: required string errmsg // 错误信息
    3: optional PDidiMiniEstimateData data // 预估数据
    4: required string trace_id // trace_id
}

struct DidiMiniEstimateData {
    1: required i32 require_level // 车型
    2: required i32 business_id // 业务线
    3: required i32 product_id // 产品线ID
    4: required i32 combo_type // combo_type
    5: required i32 product_category // 品类ID
    6: required i32 level_type
    7: required i32 carpool_type
    8: required string estimate_id // 预估id
    9: required float64 fee_amount // 单纯价格
    10: required string fee_msg // 价格文案
    11: required list<NewFormFeeDesc> fee_desc_list // 价格详情
    12: optional list<string>         route_id_list // 路线id
}

struct PDidiMiniEstimateData {
     1: required list<DidiMiniEstimateData> estimate_data // 预估价格和里程文案
     2: required string fee_detail_url // 费用详情H5
     3: required string estimate_trace_id // 预估trace_id
     4: required string animation_gif // 动画url
}

struct PickOnTimeEstimateReq {
    1: required string    token                 // 用户认证token
    2: required i32       user_type             // 1普通用户；2企业用户

    3: required string app_version              // 端版本
    4: required i32    access_key_id            // 端来源
    5: required string channel                  // 渠道号
    6: required i32    client_type              // 端类型
    7: required string lang                     // 端语种
    8: required i32    platform_type            // 端(平台)类型-2

   11: required string map_type                 // 地图类型
   12: required double lat                      // 定位点
   13: required double lng
   14: required double from_lat                 // 起点
   15: required double from_lng
   16: required string from_poi_id
   17: required string from_poi_type
   18: required string from_address
   19: required string from_name
   20: required string choose_f_searchid        // 用户选择起点请求ID
   21: required double to_lat                   // 终点
   22: required double to_lng
   23: required string to_poi_id
   24: required string to_poi_type
   25: required string to_address
   26: required string to_name
   27: required string choose_t_searchid        //用户选择终点请求ID
   28: required i32 order_type               //订单类型：0 是实时 1是预约
   29: required i32 call_car_type            //CallCarType 叫车类型：0为普通叫车；1为代叫车
   30: required i32 payments_type            //支付类型 2:个人付
   31: required string stopover_points        //途经点参数 json array 字符串
   32: required i32 page_type //场景标识
   33: required string order_id                 //订单号
   34: required string tab_id                 //tab标识
    /*webx公参*/
   100: optional string    xpsid
   101: optional string    xpsid_root
}


struct PickOnTimeEstimateData {
    1: required double estimate_fee // 预估金额
    2:required PNewOrderParams pneworder_params //发单参数 预估级别
    3: required string fee_detail_url // 费用详情H5
    4: required string           trace_id
}

struct PickOnTimeEstimateRsp {
    1: required i32              errno
    2: required string           errmsg
    3: optional PickOnTimeEstimateData data
}

struct PNewOrderParams {
    1: required string           agent_type
    2: required i32              type
    3: required i32              page_type
    4: required i64              is_support_multi_selection
    5: required string                             stopover_points
    6: required list<RequireProduct>               multi_require_product
    7: required string          estimate_trace_id
}
struct RequireProduct {
    1: required i64              business_id
    2: required string           require_level
    3: required i64              combo_type
    4: required string           estimate_id
    5: required i64              product_category
    6: required i32              pay_type
    7: required i32              level_type
    8: required i32              count_price_type
}

struct PGetFormRealDataReq {
    10: required string estimate_trace_id,                      // 预估trace_id

    1: required     i64        access_key_id,                   //区分客户端
    2: required     string     appversion,                     //appversion
    3: required     string     lang,                            //语言
    4: required     string     token,
    5: optional   i64 channel,                                 //渠道号


    13: required    i64       from_area,                       //出发地area
    6: required    double     from_lng,                        //出发地位置经度
    7: required    double     from_lat,                        //出发地当前位置纬度
    11: required   double     to_lng,                          //目的地当前位置经度
    12: required   double     to_lat,                          //目的地当前位置纬度
    15: optional   string     from_name,                       //出发地
    16: optional   string     to_name,                         //目的地

    17: optional   string multi_product,                       
    18: optional   string    oid   //等应答阶段的订单ID
    19: optional   i32  source_type // 0-默认表单；1-三方聚合表单；2-等应答表单
    20: optional   string trans_data //透传字段
    21: required   i32    client_type
    22: optional i32 order_type // 订单类型
    23: optional   BargainRangePriceInfo    bargain_range_price_info //自选车价格range信息
    24: optional  string route_id // 路线id
    25: optional  i32  ets // ets
    26: optional  i32  answer_rate // 应答率
    27: optional  i32  last_selected_count // 车型选中数量

    30: optional  i32  is_select_item // 是否勾选  0:否   1:是
    31: optional  i32  rec_form // 表单类型 1: 推荐表单
    32: optional  i32  rec_type // 顶部决策类型 0:首次调用，待决策 1:ets沟通 2:无车赔
    33: optional  i32  font_scale_type // 大字模式字段 0:正常 1:大字 2:超大字
    34: optional  i32  form_style_exp // 表单样式实验
    35: optional  string  box_info // 盒子信息
}
struct BargainRangePriceInfo{
    1: optional   i32    upper_slider_price //自选车价格range：滑动条上界
    2: optional   i32    lower_slider_price //自选车价格range：滑动条下界
    3: optional   i32    upper_price //自选车价格range：边界上限
    4: optional   i32    lower_price //自选车价格range：边界下限
    5: optional    double    fast_car_estimate_fee //快车预估价
    6: optional    double    sp_fast_car_estimate_fee //特惠快车预估价
    7: optional     double recommend_price //推荐价 表单展示的价格，来源：定价返回的推荐价下限。独立页需传
}

struct PriceDiffInfo {
    1: required string text
    2: required string icon
}

struct PopUp {
    1: required string title
    2: required string content
    3: optional string button_text
}

struct Omega {
    1: required string omega_event_id
    2: required map<string,string> omega_parameter
}

struct FormHeadRule {
    1: required i64 action_type // 点击后出发的动作 1: 半弹层
    2: optional string left_icon
    3: optional string title // left_icon的兜底文案
    4: required string content // 文字部分，富文本
    5: required  bool has_arrow // 末尾的>符号

    6: optional Omega omega_data // 端埋点
    7: optional Omega omega_data_ck // 端埋点

    8: optional PopUp pop_up // 半弹层
    9: required i32 style // 样式，1-ETS新样式 0旧样式
    10: optional FormHeadCompensationRule compensation_info
}

struct FormHeadCompensationRule {
    1: optional CompensationRightContent selected_data
    2: optional CompensationRightContent unselected_data
    3: optional list<i64>     compensation_product_list        // 无车赔生效车型
    4: optional Omega omega_data // 端埋点
    5: optional Omega omega_data_ck // 端埋点
}

struct CompensationRightContent {
   1: optional string right_text //右侧标题
   2: optional string right_icon //右侧icon
   3: optional string right_bg_img // 右侧背景图
}

struct BargainRangeAnswerRateInfo {
    1: required string answer_rate_text //应答率文案
    2: required string answer_rate_color //应答率颜色
    3: optional i32 answer_rate //应答率
}

struct FormRealProductInfo {
    1: optional string product_expect_text   // 品类预期文案
    2: optional i32    product_expect_value  // 品类预期值，端上获取后特殊逻辑做比较，不区分预期类型
}

struct FormRealData {
    1: required string filter_title
    2: optional PriceDiffInfo price_diff_info
    3: optional  string  match_card_title //弹框标题
    4: optional  FormHeadRule head_info
    5: optional BargainRangeAnswerRateInfo bargain_range_answer_rate_info // 自选车价格range应答率信息
    6: optional string  estimate_duration_info
   
    10: optional map<string,i32> extra_params
    11: optional StartMapInfo start_map_info
    12: optional map<string,FormRealProductInfo> product_info   // 品类信息
    13: optional string match_card_left_icon // 副标题icon
    14: optional RecBubble rec_bubble
    15: optional map<string,FormRealProductInfo>  box_etp_info
}

struct RecBubble {
    1: required string icon
    2: required string text
    3: required Omega omega_data
}

struct StartMapInfo {
    1: optional string text // 文案
    2: optional string icon
    3: optional string background_color // 背景色
    4: optional string font_color // 文案颜色
}

struct PGetFormRealDataResp {
    1: required i32 errno
    2: required string errmsg
    3: required FormRealData data
    4: required string trace_id
}

struct MultiPointEstimateRequest {
    1:  required    string                  caller
    2:  required    string                  token
    3:  required    string                  oid
    4:  required    list<Point>             point_list
    5:  required     i32                    access_key_id
    6:  required    string                  app_version
    7:  required    string                  lang
    8:  optional    list<StopOverPoints>    stopover_ponints

    9:  optional    string      map_type
    10: optional    i32         user_type
    11: optional    i32         origin_id
    12: optional    string      imei
    13: optional    i64         client_type
    14: optional    i32         platform_type
    15: optional    i64         terminal_id
}

struct StopOverPoints {
	1:  required i32        stop_id
	2:  required double     lat
	3:  required double     lng
	4:  required string     poi_id
	5:  required string     name
	6:  required string     address
}

struct Point {
    1:  required    double  from_lat
    2:  required    double  from_lng
    3:  required    string  from_poi_id
    4:  required    string  from_poi_type
    5:  required    double  to_lat
    6:  required    double  to_lng
    7:  required    string  to_poi_id
    8:  required    string  to_poi_type
    9:  required    string  route_id
}

struct MultiPointEstimateResponse {
    1: required i32                     errno
    2: required string                  errmsg
    3: optional MultiPointEstimateData  data
}

struct MultiPointEstimateData {
    1: required string                  estimate_trace_id
    2: required list<PointDetail>       point_detail
    3: optional double                  cur_order_estimate_fee
    4: optional map<string, string>     extra_info
}

struct PointDetail {
    1: required string              estimate_id
    3: required double              estimate_fee
}

struct PEstimateByOrderRequest {
    1: required string oid
    2: required string estimate_id
    3: required string token

    4: required string app_version
    5: required i32    access_key_id
    6: required string lang
    7: required i32    client_type
    8: required i64    origin_id
}

struct PEstimateByOrderResponse {
    1: required i32 errno
    2: required string errmsg
    3: required string trace_id
    4: optional PEstimateByOrderData data
}

struct PEstimateByOrderData {
    1: required string estimate_id
    2: required double estimate_fee
    3: required string fee_prefix
    4: required i32    fee_type
    5: required string fee_detail_url
    6: optional string discount_msg
}


struct GetRentInfoReq {
    /**通用参数**/
    1: required string token
    2: required i32 access_key_id
    3: required string app_version
    4: required string lang
    5: required i32 channel

    /**起终点相关**/
    21: required i32 from_area // 起点城市
    22: required double flat
    23: required double flng
    24: required string map_type

    31: required i64 timestamp
}

struct GetRentInfoRsp {
    1: required i32 errno
    2: required string errmsg
    3: required string trace_id
    4: required RentInfo data
}

struct RentInfo {
    1: required list<RentTabItem> tab_list
}

struct RentTabItem {
    1: required i32 source_id
    2: required i64 business_id
    3: required list<RentComboDetail> combo_list // 套餐信息
    4: required list<CityCategory> city_list
}

struct RentComboDetail {
    1: required i32 combo_id //套餐id
    2: required double combo_time //套餐时长：分钟
    3: required double combo_distance  //套餐里程：公里
    4: required i32 combo_start_time //套餐最早开始时间，单位小时
    5: required i32 combo_end_time //套餐最晚开始时间，单位小时
    6: required i32 combo_earliest_minutes //最早预约时间距离当前时间的分钟数
    7: required i32 combo_during_days //套餐选择持续天数
    8: required i32 combo_time_gap //时间选择步数
}

struct ShuttleBusGuideBarEstimateReq {
    1: required string  token     // 用户认证token
    2: required i32  user_type      // 1普通用户；2企业用户
    3: required string app_version // 端版本
    4: required i32    access_key_id // 端来源
    5: required string channel // 渠道号
    6: required i32    client_type // 端类型
    7: required string lang // 端语种
    8: required i32    platform_type // 端(平台)类型-2
    9: required i32    city_id       // 城市id

   11: required string map_type // 地图类型
   12: required double lat // 定位点
   13: required double lng
   14: required double from_lat // 起点
   15: required double from_lng
   18: optional string from_address
   19: optional string from_name
   21: required double to_lat // 终点
   22: required double to_lng
   25: required string to_address
   26: required string to_name
   27: required i32 carpool_seat_num     // 用户勾选座位数
   28: optional i32 page_type           // 页面来源
   29: required i32 order_type // 订单类型
   30: required i32 call_car_type // 代叫类型
   31: required string stopover_points // 途经点
}


struct Area {
    1: required string display_name     // 展示名称
    2: optional i32 station_id          // 站点id
    3: required double lat
    4: required double lng
    5: required i32 city_id
    6: required string address
}

struct FeeInfo {
    1: required int32 carpool_seat_num
    2: required double fee
}
struct ShuttleBusGuideBarData {
    1: required i32 carpool_type
    2: required i32 route_type
    3: required string estimate_id
    4: required i32 region_id
    5: optional i32 route_id            // 运营配置生成的路线
    6: required i32 require_level
    7: required i32 business_id
    8: required i32 product_id
    9: required i32 combo_type
    10: required i32 product_category
    11: required i32 distance_type      // 终点与下车点的距离类型， 1，<= X米；2，> X米
    12: required Area from_area
    13: required Area to_area
    14: required double unit_price           // 单价
    15: required double total_price           // 总价格
    16: required double estimate_fee            // 拼成的 对应人数的券后价
    17: required string estimate_trace_id   // 预估trace_id
    18: required i32 carpool_seat_num     // 用户勾选座位数
}


/**ShuttleBusGuideBarEstimateRsp 返回**/
struct ShuttleBusGuideBarEstimateRsp {
    1: required i32                     errno
    2: required string                  errmsg
    3: optional ShuttleBusGuideBarData  data
    4: required string                  trace_id
}


struct ShuttleBusPageEstimateReq {
    1: required string  token     // 用户认证token
    2: required i32  user_type      // 1普通用户；2企业用户
    3: required string app_version // 端版本
    4: required i32    access_key_id // 端来源
    5: required string channel // 渠道号
    6: required i32    client_type // 端类型
    7: required string lang // 端语种
    8: required i32    platform_type // 端(平台)类型-2
    9: required i32    source_type   // 请求来源,1、预估导流位跳转到落地页;2、扫码上车到落地页
   10: required i32    city_id       // 城市id

   11: required string map_type // 地图类型
   12: required double from_lat // 起点
   13: required double from_lng
   14: optional i32    distance_type //终点与下车点的距离类型， 1，<= X米；2，> X米

   28: optional i32 carpool_seat_num     // 用户勾选座位数
   29: required i32 region_id            // 区域id\景区id
   30: optional i32 start_station_id     // 起点站点id
   31: required i32 end_station_id       // 终点站点id
   32: optional i32 route_id             // 路线id
   33: optional i32 page_type           // 页面来源page_type=48
}

struct ShuttleBusButton {
    1: required string text
    2: required string sub_title
}

struct SeatData {
    1: required string left_text
    2: required i32 seat_max_num
    3: required i32 default_num
    4: required string max_remind_msg    // 最大座位数tips
    5: required string min_remind_msg    // 最小座位数tips
}

struct ShuttleBusStationData {
    1: required i32 station_id       // 站点id
    2: required double lat
    3: required double lng
    4: required string name          // 展示名称
    5: required string address
}

struct BaseInfo {
    1: required int32 city_id
    2: required string displayname   // 地名
    3: required int32 is_recommend_absorb  // 第一吸附点
    4: required double lat
    5: required double lng
    6: required string poi_id
    7: required int32 station_id
}

struct ContentInfo {
    1: required string content
    2: required string content_color
}

struct StartBottomCardInfo {
    1: required ContentInfo card_top   // 主标题
    2: required ContentInfo card_bottom // 副标题
}

struct ExtendInfo {
    1: required StartBottomCardInfo start_bottom_card_info
}

struct RecPoints {
    1: required BaseInfo base_info
    2: required ExtendInfo extend_info
}
struct BusinessPoiInfo {
    1: required list<RecPoints> recPoints
}

struct ShuttleBusPageData {
    1: required string communication_image      // 沟通图片
    2: required ShuttleBusStationData start_station_data  // 起点信息
    3: required ShuttleBusStationData end_station_data    // 终点信息
    4: required string start_and_end_icon                // 起终点icon
    5: optional BusinessPoiInfo businessPoiInfo // 切换起点列表,地图用来渲染切换起点页面数据
    6: required string walk_icon                // 步行导航图片
    7: optional string switch_point_icon        // 切换切点icon
    8: required string unit_price               // 单价
    9: required string  slogan               // 确定性沟通文案
    10: required string fee_detail_url          // 费用详情页链接
    11: required string total_price             // 总价
    12: required SeatData seat_data             // 座位数信息
    13: required ShuttleBusButton button_info         // 按钮信息
    14: required i32 require_level
    15: required i32 business_id
    16: required i32 product_id
    17: required i32 combo_type
    18: required i32 product_category
    19: required i32 carpool_type
    20: required i32 route_type
    21: required string estimate_id
    22: required i32 region_id

    23: optional i32 route_id                       // 运营配置生成的路线
    24: required list<string> route_id_list         // 地图画路线route_id
}


/**ShuttleBusPageEstimateRsp 返回**/
struct ShuttleBusPageEstimateRsp {
    1: required i32                     errno
    2: required string                  errmsg
    3: optional ShuttleBusPageData      data
    4: required string                  trace_id
}

struct EstimateOrderWithoutRenderReq {
    1: required i64 pid // pid
    3: required string phone //phone
    4: required string order_id //订单ID
    5: optional i32 access_key_id
    6: optional string app_version
    10: optional i32 page_type // 可传，也可在接口内set
    16: optional i32 user_type //用户类型，普通用户or企业用户：是开端的时候统一给端下发了，端缓存在本地的通用参数
    17: optional list<i64> need_product_category //上游指定的品类列表
}

/** 等待应答追加车型预估返回数据 */
struct EstimateOrderWithoutRenderRsp {
    1: required i32                     errno // 错误码
    2: required string                  errmsg // 错误信息
    3: optional EstimateOrderWithoutRenderData data // 预估数据
}

struct EstimateOrderWithoutRenderData {
    1:  required string estimate_trace_id
    2:  required map<string,WithoutRenderEstimateData> estimate_data
}

struct WithoutRenderEstimateData {
    1: required string               estimate_id

    /** n元组 **/
    2: required i64                  product_category
    3: required i64                  product_id
    4: required i64                  business_id
    5: required string               require_level
    6: required i64                  combo_type
    7: required i32                  level_type
    8: required i64                  carpool_type
    9: required i32                  count_price_type
   10: required i32                  is_special_price
   11: required i32                  spacious_car_alliance
   12: required i64                  combo_id
   13: required i64                  route_type
   14: required i32                  carpool_price_type
   15: required bool                 is_dual_carpool_price

    /** 价格相关**/
   20: required double               estimate_fee   // 预估价
   21: required double               dynamic_total_fee  // 券前价
   22: required double               cap_price  // 一口价
   23: required double               basic_total_fee // 账单basic_total_fee
   24: required double               dynamic_diff_price
   25: optional list<MultiPriceData> multi_price_data
   26: required double               exact_estimate_fee
   27: required double               pre_total_fee
   28: required double               dynamic_times
   29: required double               red_packet
   30: required double               total_fee
   31: required double               energy_consume_fee
   32: optional DiscountSet          discount_set

    /** 其他 **/
   41: optional list<string>         route_id_list
   42: required i64                  start_dest_distance   //单位米
   43: required i64                  driver_metre   //单位米
   44: required string               payment_id // 21 企业余额支付 2 个人支付
   45: required bool                 undisplay_in_layout // 追加列表不展示，仅弹窗展示该品类
   46: required i32                  etp // 小巴etp
   47: required i32                  sub_group_id
   48: required i32                  is_default_auth
   49: required i64                  start_dest_time
   50: required i32                  basic_rate
   51: required i32                  pay_type  //支付方式

}

struct PPassengerBeginChargeRequest {
    1: required string oid
    2: required string estimate_id
}

struct PPassengerBeginChargeResponse {
    1: required i32 errno
    2: required string errmsg
    3: required string trace_id
    4: optional PassengerBeginChargeData data
}

struct PassengerBeginChargeData {
    1: required BubbleInfo bubble_info
}

struct BubbleInfo {
    1: required RenderInfo render_info
    2: required OriginInfo origin_info
}

struct RenderInfo {
    1: required string bubble_title // 气泡第一行标题
    2: required string bubble_sub_title // 气泡第二行标题
    3: required string total_fee_text
}

struct OriginInfo {
    1: required string is_upgrade_flag // 是否是升舱场景， "0":不是，"1":是
}

struct EngageCarEstimateReq {

    1: required i64 uid             //用户uid
    2: required string lang         //语言
    3: required i32 access_key_id   //端来源
    4: required string app_version  //端版本号
    5: required i32 client_type     //客户端类型
    6: required i64 channel         //渠道号
    7: required i64 start_time      //出发时间戳
    8: required i32 from_city      //出发城市
    9: required string from_district_code    //出发区号id
    10: required i32 combo_id       //套餐id，批量查询时传0
    11: required i32 page_source      //页面id
    12: required i32 require_level   //车型分类

    20: optional string trans_data  //透传数据
}

struct EngageCarEstimateResponse{
    1: required i32 errno
    2: required string errmsg
    3: required EngageCarEstimateData data
}

struct EngageCarEstimateData {
    1: required string estimate_trace_id
    2: required list<EngageEstimateData> estimate_data
}

struct EngageEstimateData {
    1: required string estimate_id      //预估id
    //品类n元组信息
    2: required i32 product_category    //品类id
    3: required i32 product_id          //产品线id
    4: required i32 require_level       //车型分类（经济5座）
    //价格信息
    10: required i64 fee_amount         //预估价格,单位分
    11: required i64 combo_id           //套餐id
    12: required double combo_distance  //套餐里程
    13: required i64 combo_time         //套餐时长
    14: required i64 over_time_fee      //超时长费
    15: required i64 over_distance_fee  //超公里费，单位：元/公里
    16: required i64 over_time_unit     //超时长计价单位，x分钟
    17: required string package_conf    //套餐透传字段
    18: required string titan_id        //账单的titan_id
    19: required i64 combo_type         //

    20: optional string fee_desc_list    //价格详情
}

struct DynamicFeeDescListRequest {
    1: required string          token
    2: required string          product_list
    3: required i32             form_type

    4: required string          lang
    5: required i32             access_key_id
    6: required string          app_version
    7: required string          menu_id
    8: optional i32             form_style_exp
    9: optional i32             font_scale_type // 大字版标识
}

struct DynamicFeeDescListResponse {
    1: required i32                     errno
    2: required string                  errmsg
    3: required string                  trace_id

    4: required DynamicFeeDescListData  data
}

struct DynamicFeeDescListData {
    1: required list<NewFormFeeDesc>    fee_desc_list
}

struct PGetEstimateDataWithoutRenderReq {
    1: required i64    pid
    2: required i64    uid
    3: required string    phone
    4: required string    lang
    5: required string    app_version
    6: required i32       access_key_id

    /**起终点相关信息**/
    30: required double     lat
    31: required double     lng
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_address
    37: required string     from_name
    38: required double     to_lat
    39: required double     to_lng
    40: required string     to_poi_id
    41: required string     to_poi_type
    42: required string     to_address
    43: required string     to_name
    44: required i32        dest_poi_code //非强依赖参数
    45: required string     dest_poi_tag //非强依赖参数

    /*订单属性等信息*/
    50: required string     menu_id
    51: required i32        page_type
    52: required string     departure_time  //时间戳
    53: required i32        order_type //预约单
    54: required i32        user_type // 用户类型 0表示普通用户，2表示企业用户
    55: optional i32        call_car_type
    56: optional i32        payments_type //支付类型
    57: optional string     multi_require_product //用户勾选项
    58: optional string     call_car_phone
    61: optional string     stopover_points //途经点参数 json array 字符串
    62: optional i32        source_id

    /**** 提高扩展性需要字段 ******/
    214: optional string caller //通过caller配置限流
    215: optional list<string> field //获取信息key
}

struct PGetEstimateDataWithoutRenderResp {
    1: required i16  errno //错误编i号
    2: required string  errmsg //错误信息
    3: required string trace_id
    4: required  map<string,PGetEstimateDataWithoutRenderEstimateData> data //data
}

struct PGetEstimateDataWithoutRenderEstimateData {
    1: optional i64          product_category //品类
    2: optional CouponInfo coupon //优惠券
    3: optional double       estimate_fee //预估价格
    4: optional map<string,double> fee_detail_info // 费用列表
    5: optional double       dynamic_total_fee // 券前价
    6: optional double       driver_price  // 司机佣金
}

struct PGetEstimateDataByEidListReq {
    1: optional i64 uid
    2: optional i64 pid
    3: optional string phone
    4: optional string lang
    5: optional string app_version
    6: optional i32    access_key_id
    7: required list<string>  eid_list (validate="len($)>0")//预估id列表
    8: required list<string>  field (validate="len($)>0")//获取信息key
    9: required string caller (validate="len($)>0")//需要申请caller
}

struct PGetEstimateDataByEidListResp {
    1: required i16  errno //错误编i号
    2: required string  errmsg //错误信息
    3: required PGetEstimateDataByEidListResponse data //data
    4: required string trace_id

}

struct PGetEstimateDataByEidListResponse {
    1: required map<string,PGetEstimateDataByEidListData> estimate_data //预估数据
}

struct PGetEstimateDataByEidListData {
    1: optional i32                     product_category //品类
    2: optional string                  car_title //车型名称
    3: optional string                  car_icon //车型图标
    4: optional string                  fee_amount //13.34
    5: optional string                  fee_msg //预估13.34元
    6: optional bool                    is_tripcloud //
    7: optional list<SubTitle>          sub_title_list //
    8: optional list<NewFormMultiPrice> multi_price_list //多口价, 存在时展示优先级高于fee_msg
    9: optional list<NewFormFeeDesc>    fee_desc_list //多口价, 存在时展示优先级高于fee_msg

   10: optional string                  subsidy_popup_fee_msg_origin // 补贴弹窗券前价文案
   11: optional string                  subsidy_popup_fee_msg_final  // 补贴弹窗券后价文案
   12: optional string                  subsidy_popup_discount_desc  // 补贴弹窗优惠文案（富文本格式）
   13: optional string                  subsidy_popup_fee_diff_msg   // 补贴弹窗价差文案（券前-券前）
   14: optional string                  subsidy_popup_discount_desc_simple_text  // 补贴弹窗优惠文案（{}格式）
   15: optional map<string,double>      fee_detail_info //费用明细，治理做价差补偿用了
}

struct HomePageCallCarEstimateReq {
    1: required string token                 // 用户认证token
    2: required i32    user_type             // 1普通用户；2企业用户
    3: required string app_version           // 端版本
    4: required i32    access_key_id         // 端来源
    5: required string channel               // 渠道号
    6: required i32    client_type           // 端类型
    7: required string lang                  // 端语种
    8: required i32    platform_type         // 端(平台)类型-2
   11: required string map_type              // 地图类型
   12: required double lat                   // 定位点
   13: required double lng
   14: required double from_lat              // 起点
   15: required double from_lng
   16: required string from_poi_id
   17: required string from_poi_type
   18: required string from_address
   19: required string from_name
   21: required double to_lat                 // 终点
   22: required double to_lng
   23: required string to_poi_id
   24: required string to_poi_type
   25: required string to_address
   26: required string to_name
   27: required string coordinate_type       // 地图坐标系类型
   28: required string last_dest_poi_info_str // 端上末次冒泡的终点信息
   29: required i32    city_id
   30: required string biz_tag // xinliu  / default
   31: required string search_id
   32: required string to_poi_code
   33: required i32  to_is_recommend_absorb
   34: required i32    to_city_id
   35: required string to_city_name
   36: required string to_address_all

    /*webx公参*/
  100: optional string    xpsid
  101: optional string    xpsid_root
}


struct HomePageCallCarEstimateRsp {
    1: required i32                         errno
    2: required string                      errmsg
    3: optional HomePageCallCarEstimateData data
    4: required string                      trace_id
}

struct HomePageCallCarEstimateData {
    1: required DestinationInfo   destination_info
    2: required string            side_info
    3: required list<string>      car_title_list
    4: required string            fee_amount
    5: required string            fee_msg
    6: optional list<FeeDesc>     fee_desc_list
    7: required PayInfo           user_pay_info
    8: required list<ProductInfo> multi_require_product
    9: optional CallCarButtonInfo call_car_button_info
    10: optional LeftTagInfo      left_tag_info
    11: required string           des_type
    12: required string           user_tag
    13: optional string           fee_detail_url
    14: optional list<CallCarEstimateData>           estimate_data_list
}

struct CallCarEstimateData {
    1: required     string                         estimate_id
    2: required     i64                            product_category
    3: required     string                         car_title
    4: required     string                         car_icon
    5: required     string                         fee_amount
    6: required     string                         fee_msg
    7: optional     list<NewFormFeeDesc>            fee_desc_list
    8: optional     list<NewFormMultiPrice>         multi_price_list
    9: optional     list<NewFormCarpoolSeatOption>  carpool_seat_list
    10: optional    CallCarExtraEstimateData        extra_estimate_data
    11: optional    i32                             is_selected
	// 发单参数
    12: optional    NewFormExtraMap                 extra_map
}

struct CallCarExtraEstimateData {
    1: optional string left_down_icon_text
}

struct FeeDesc {
	1: required string border_color
	2: required string content
	3: required string icon
}

struct PayInfo  {
    1: required string pay_type
    2: required string business_const_set
}

struct ProductInfo  {
    1: required i64    product_id
    2: required i64    business_id
    3: required i64    combo_type
    4: required i64    require_level
    5: required i32    level_type
    6: required i64    combo_id
    7: required i64    route_type
    8: required bool   is_special_price
    9: required i32    count_price_type
   10: required string pay_type
   11: required string estimate_id
   12: required i64    product_category
   13: required string business_const_set
   14: optional i64    carpool_seat_num
}

struct LeftTagInfo {
    1: required i32    type
    2: required string icon
    3: required string text
    4: required list<string> textArr
    5: optional string arrow_pic
}

struct CallCarButtonInfo  {
	1: required string   bg_start_color
	2: required string   bg_end_color
	3: required string   text
	4: required string   text_color
	5: optional string   border_color
	6: optional CallCarLinkInfo call_car_link_info
}

struct CallCarLinkInfo {
    1: required string link_type
    2: required string link
    3: required string app_id
}

struct DestinationInfo  {
	1: required string text
	2: required double to_lng
	3: required double to_lat
	4: required string to_address
	5: required string to_name
	6: required string to_poi_id
	7: required i32 to_city
	8: required string to_poi_type
	9: required string city_name
}

struct HomePageCallCarEstimateRspV2 {
    1: required string                        trace_id
    2: required i32                           errno
    3: required string                        errmsg
    4: optional HomePageCallCarEstimateDataV2 data
}

struct HomePageCallCarEstimateDataV2 {
	1: required HomePageCallCarCard   card_info         // 卡片渲染结果
	2: required NewOrderPopUp         new_order_pop_up  // 发单弹窗渲染结果
    3: optional HomePCallCarButtonInfo button_info // 发单按钮渲染
    4: optional HomePDestinationInfo destination_info
}

struct HomePageCallCarCard {
   1: required string              title    // "去{「新城海大厦」}"
    2: required string              fee_msg  // "预估{color=#000000 font=5 size=20 text=6.3}元",
    3: optional list<HomePFeeDesc>       fee_desc_list
    4: optional HomePLeftTagInfo         left_tag_info
}

struct HomePLeftTagInfo {
    1: optional string icon
    2: optional list<HomePSceneTag> scene_tags
}

struct HomePSceneTag {
    1: optional string border_color
    2: optional string bg_color
    3: optional string font_color
    4: required string content
}

struct NewOrderPopUp {
     1: required string              call_car_tip    // "正在呼叫快车、特惠"
     2: required string              destination_display_name  // "光谷广场-地铁站"
     3: optional Omega               omega // 埋点数据
}

struct HomePFeeDesc {
    1: required string              content
    2: required string              border_color
    3: required string              icon
    4: required string              text_color
}


struct HomePNewOrderParams {
    1: required  string multi_require_product
    2: required  string            estimate_trace_id
    3: required  i32              is_support_multi_selection
}

struct HomePCallCarButtonInfo  {
	1: required string   bg_start_color
	2: required string   bg_end_color
	3: required string   text
	4: required string   text_color
	5: required string   border_color
    6: optional HomePNewOrderParams     p_new_order_params // 发单按钮参数
    7: required CallCarLinkInfo call_car_link_info
}

struct HomePDestinationInfo  {
    1: required string search_id
	2: required double to_lng
	3: required double to_lat
	4: required string to_address
	5: required string to_name
	6: required string to_poi_id
	7: required i32    to_city
	8: required string to_poi_type
	9: required string to_city_name
	10: required string to_poi_code
	11: required i32   to_is_recommend_absorb
	12: required string to_coordinate_type
	13: required string to_address_all
}

struct InvitedEstimateReq {
    1: required string    token                 // 用户认证token
    2: required i32       user_type             // 1普通用户；2企业用户

    3: required string app_version              // 端版本
    4: required i32    access_key_id            // 端来源
    5: required string channel                  // 渠道号
    6: required i32    client_type              // 端类型
    7: required string lang                     // 端语种
    8: required i32    platform_type            // 端(平台)类型-2

   9: required string map_type                 // 地图类型
   10: required double lat                      // 定位点
   11: required double lng
   12: required double from_lat                 // 起点
   13: required double from_lng
   14: required string from_poi_id
   15: required string from_poi_type
   16: required string from_address
   17: required string from_name
   18: required double to_lat                   // 终点
   19: required double to_lng
   20: required string to_poi_id
   21: required string to_poi_type
   22: required string to_address
   23: required string to_name
   24: required i32 page_type //场景标识
    /*webx公参*/
   100: optional string    xpsid
   101: optional string    xpsid_root
}

struct InvitedEstimateData {
    1: required coupon_info_data coupon_info // 优惠信息
    2: required string           trace_id //trce_id
}

struct InvitedEstimateRsp {
    1: required i32              errno
    2: required string           errmsg
    3: optional InvitedEstimateData data
}

struct coupon_info_data {
    1: required string           coupon_type // 3立减 100 抵扣
    2: required string           coupon_discount //优惠折扣，表示几折(0,10)，示例：1表示1折，1.5表示1.5折
    3: required double         coupon_amount //优惠金额
    4: required string  estimate_id //预估id
}

struct PetsTravelEstimateReq {
    1: required string token // 用户认证token

    /**公参**/
    10: required i32    access_key_id
    11: required string app_version
    12: required i64    channel
    13: required i32    client_type
    14: required string lang
    15: required string map_type
    16: required i64    terminal_id
    17: required i32    platform_type
    18: optional string ddfp
    19: required i32    user_type // 用户类型 0表示普通用户，2表示企业用户
    20: required i32    order_type
    21: required string departure_time
    22: optional string dchn

    /**起终点相关**/
    30: required i32 from_area  // 起点
    31: required double from_lat
    32: required double from_lng
    33: required string from_poi_id
    34: required string from_poi_type
    35: required string from_address
    36: required string from_name
    37: required i32 to_area   // 终点
    38: required double to_lat
    39: required double to_lng
    40: required string to_poi_id
    41: required string to_poi_type
    42: required string to_address
    43: required string to_name
    44: required double lat   // 定位点
    45: required double lng
    46: required string choose_f_searchid //用户选择起点请求ID
    47: required string choose_t_searchid //用户选择终点请求ID
    48: required string stopover_points //途经点信息
    49: optional string preferred_route_id //偏好路线id
    50: optional string route_preference_type  //偏好路线类型

    /**二次预估信息**/
    60: optional string multi_require_product //用户勾选项
    61: optional i32    pet_service_tag // 1000-不带航空舱（默认），1001-带普通宠物箱子

    /*webx公参*/
    100: optional string xpsid
    101: optional string xpsid_root
    102: optional string screen_pixels
    103: optional double screen_scale

    110: optional string trans_data // 透传数据
    111: required i32    from_type  (go.type="interface{}")//用来标识是否是再来一单场景，3-再来一单
    112: optional string    guide_trace_id // 导流来源的冒泡trace
}

struct PetsTravelEstimateResponse{
    1: required i32 errno
    2: required string errmsg
    3: required PetsTravelEstimateData data
}

struct PetsTravelEstimateData {
    1: required string estimate_trace_id
    2: required list<PetsTravelEstimateCard> estimate_data
    3: required ServiceInfo service_info
    4: required string fee_detail_url //费用详情
    5: optional BackButton back_button //返回按钮
    6: optional PetMetaData pet_meta_data //宠物档案元信息
}

struct PetMetaData {
    1: list<PetItem> pet_list  // 宠物信息列表
    2: Protocol protocol // 协议
    3: AddPet add_pet   // 新增宠物按钮文案
}

struct PetItem {
    1: i32 pet_type              // 宠物类型枚举
    2: string pet_type_name      // 宠物类型名称
    3: list<WeightCategory> weight_category_list  // 体重种类列表
    4: i32 show_type             // 显示类型
    5: PetAvatarStyle pet_avatar_style  // 宠物头像样式
    7: list<PetItem>  sub_pet_list // 子宠物列表
}

struct WeightCategory {
    1: i32 weight_category      // 体重种类
    2: string weight_category_desc  // 体重种类描述
    3: string weight_category_subtitle //体重描述
}

struct PetAvatarStyle {
    1: string grey_logo      // 灰色头像
    2: string highlight_logo // 高亮头像
}

struct AddPet{
    1: string button_txt // 按钮文案
    2: string title // 标题
    3: string pet_type_title // 宠物类型标题
    4: string pet_weight_title // 体重类型标题
    5: string pet_type_toast // 宠物类型提示
    6: string pet_weight_toast // 体重类型提示
}

struct Protocol{
    1: string protocol_txt // 协议描述
    2: string protocol_url // 协议url
}

struct PetsTravelEstimateCard {
    //基础数据
    1: required string estimate_id      //预估id
    2: required i64 product_category    //品类id
    3: required PetExtraMap extra_map      //品类n元组

    //费用
    10: required string fee_amount
    11: required string fee_msg
    12: required list<NewFormFeeDesc> fee_desc_list

    //渲染
    20: required string car_title
    21: required string sub_title
    22: required string car_icon       //车型分类（经济5座）
    23: required i32 is_selected
    24: required list<string> route_id_list

}

struct PetExtraMap {
    1: required i64       product_category
    2: required i32       level_type
    3: required i64       business_id
    4: required i64       combo_type
    5: required string    require_level
    6: required i64       product_id
    7: required i64       carpool_type
    8: required i64       is_pet_travel
}

struct ProductNTuple {
    1: required i64       product_category
    2: required i32       level_type
    3: required i64       business_id
    4: required i64       combo_type
    5: required string    require_level
    6: required i64       product_id
    7: required i64       carpool_type
}

struct ServiceInfo {
    1: required string icon
    2: required string title
    3: required list<ServiceList> pet_service_list
}

struct ServiceList {
    1: required i32 pet_service_tag  //1000-不带箱子 1001-带普通宠物箱子
    2: required string title
    3: required string sub_title
    4: required i32 selected
    5: required string icon
}

struct PMCPEstimateReq {
    /** 客户端参数**/
    1: required i64       uid
    2: required i64       pid
    3: required string    phone
    4: required string    app_version
    5: required i32       access_key_id
    6: required i32       channel
    7: required string    lang

    /**起终点相关信息**/
    8: required double     from_lat
    9: required double     from_lng
    10: required string     from_name
    11: required double     to_lat // 终点定位纬度
    12: required double     to_lng // 终点定位经度
    13: required string     to_name
    14: optional string     stopover_points
}


struct PMCPEstimateResponse {
    1: required i16                     errno       // 错误码
    2: required string                  errmsg      // 错误信息
    3: optional MCPEstimateData         data        // 产品数据
}

struct MCPEstimateData {
    1: required string            estimate_trace_id
    2: required list<MCPProduct>  products
}

struct MCPProduct {
    1:  required string         estimate_id             // 品类预估ID
    2:  required i32            product_category        // 品类ID
    3:  required i32            order_type              // 订单类型：0 是实时 1是预约
    4:  required i32            product_id              // 业务线ID
    5:  required i32            business_id             // 公共产品线ID
    6:  required i32            require_level           // 车型
    7:  required i32            level_type              // 车型等级类型
    8:  required i32            combo_type              // 组合类型
    9:  required bool           is_special_price        // 一口价
    10: optional i32            carpool_seat_num        // 拼车座位数
    11: required i32            carpool_type            // 非0为拼车
    12: optional i64            combo_id                // 路线id

    13: required double         dynamic_total_fee               // 所有费用项合计，券前价
    14: required double         cap_price                       // 一口价，只有在一口价的场景会有
    15: required double         highway_fee                     // 高速费
    16: required double         dynamic_diff_price              // 基于max(extra.SubTotal, b.MinimumPrice) 算出来的动调费
    17: required i64            count_price_type                // 计价方式
    18: required double         dynamic_times                   // 增加的倍数，实际倍数为 1 + DynamicTimes
    19: required double         pre_total_fee                   // 快车综合预估价
    20: required double         basic_total_fee                 // 可抵扣金额
    21: required double         estimate_fee                    // 预估价
    22: required string         car_title                       // 车型标题
}


/** Mamba服务 */
service Mamba {
    /** ！！！！！！！Ping接口，服务探活使用，请不要删除！！！！！！！ */
    PingRsp Ping(1:PingReq request) (
        connectTimeoutMsec="50"
        timeoutMsec="400"

        /** HTTP接口请求URI */
        path="/ping"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
    /** 多车型价格预估 */
    CarpoolEstimateResponse PCarpoolEstimatePrice(1:CarpoolEstimateRequest request) (
        connectTimeoutMsec="50"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pCarpoolEstimatePrice"
        /** HTTP请求方法，GET/POST */
        httpMethod="POST"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="json"
    )

    /** 城际独立页价格预估 */
    IntercityEstimateResp IntercityEstimatePrice(1:CarpoolEstimateRequest request) (
        connectTimeoutMsec="50"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pIntercityEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="POST"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="json"
    )

    /** 市内小巴价格预估 */
    MiniBusEstimateResp MiniBusEstimatePrice(1:MiniBusEstimateRequest request) (
        connectTimeoutMsec="50"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pMiniBusEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="POST"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="json"
    )

    /** 多车型价格预估 Deprecated */
    AnyCarEstimateRsp AnyCarEstimate(1:AnyCarEstimateReq request) (
        connectTimeoutMsec="50"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pAnycarEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )

    /** 多车型价格预估V3 */
    AnyCarEstimateV3Rsp AnyCarEstimateV3(1:AnyCarEstimateReq request) (
        connectTimeoutMsec="50"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pAnycarEstimateV3"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )

    /** 多车型价格预估V4 (带缓存) */
    AnyCarEstimateV4Resp AnyCarEstimateV4(1:AnyCarEstimateV4Req request) (
        connectTimeoutMsec="50"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pAnycarEstimateV4"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )

    /** 获取追加车型预估信息缓存 */
    AnycarEstimateCacheResp GetAnycarEstimateCache(1:AnycarEstimateCacheReq request) (
        connectTimeoutMsec="50"
        timeoutMsec="200"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pGetAnycarEstimateCache"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )

    /** 删除追加车型缓存 */
    AnycarEstimateDelCacheResp DelAnycarEstimateCache(1:AnycarEstimateCacheReq request) (
        connectTimeoutMsec="50"
        timeoutMsec="200"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pDelAnycarEstimateCache"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )

    /** 组合出行价格预估 */
    CombinedTravelEstimateRes CombinedTravelEstimate(1:MultiEstimatePriceRequest request) (
        connectTimeoutMsec="100"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pCombinedTravelEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="POST"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )

    /** 等待应答取消弹窗 拼车两口价预估 **/
    CancelEstimateRsp PCancelEstimate(1:CancelEstimateReq request) (
        connectTimeoutMsec="50"
        timeoutMsec="400"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pCancelEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )

    /** 企业级6.0预估 **/
    B2BEstimateRsp PBusinessEstimate(1:B2BEstimateReq request) (
        connectTimeoutMsec="100"
        timeoutMsec="2000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pBusinessEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
    /** 企业级6.0追加车型预估 **/
    B2BAnyCarEstimateRsp PBusinessAnyCarEstimate(1:B2BAnyCarEstimateReq request) (
        connectTimeoutMsec="100"
        timeoutMsec="2000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pBusinessAnyCarEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
    /** pbd6.0预估 **/
    B2BEstimateRsp PPbdEstimate(1:PBDEstimateReq request) (
        connectTimeoutMsec="100"
        timeoutMsec="2000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pPbdEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
    /** pbd6.0追加车型预估 **/
    B2BAnyCarEstimateRsp PPBDAnyCarEstimate(1:PBDAnyCarEstimateReq request) (
        connectTimeoutMsec="100"
        timeoutMsec="2000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pPbdAnyCarEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
    /** 发单助手预估 **/
    B2BEstimateRsp PAssistantEstimate(1:PBDEstimateReq request) (
        connectTimeoutMsec="100"
        timeoutMsec="2000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pAssistantEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="POST"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )

    /** 助手价格预估 */
    HelperEstimateRes PHelperEstimate(1:MultiEstimatePriceRequest request) (
        connectTimeoutMsec="100"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pHelperEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
    /** 区域接驳车落地页接口 */
    ShuttleBusPageEstimateRsp PShuttleBusPageEstimate(1:ShuttleBusPageEstimateReq request) (
        connectTimeoutMsec="100"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pShuttleBusPageEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
     /** 区域接驳车给预估导流位和等待应答固定卡片数据接口 */
    ShuttleBusGuideBarEstimateRsp PShuttleBusGuideBarEstimate(1:ShuttleBusGuideBarEstimateReq request) (
        connectTimeoutMsec="100"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pShuttleBusGuideBarEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
    /** 司乘议价预估 */
    BargainEstimateRsp PBargainEstimate(1: BargainEstimateReq req)(
        path = "/gulfstream/mamba/v1/pBargainEstimate"
        httpMethod = "GET"
        contentType = "form"
    )
    /** 首页一键叫预估 */
    HomePageCallCarEstimateRsp PHomePageCallCarEstimate(1: HomePageCallCarEstimateReq req)(
        connectTimeoutMsec="100"
        timeoutMsec="1000"
        path = "/gulfstream/mamba/v1/pHomePageCallCarEstimate"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 首页一键叫预估V2 */
    HomePageCallCarEstimateRspV2 PHomePageCallCarEstimateV2(1: HomePageCallCarEstimateReq req)(
        connectTimeoutMsec="100"
        timeoutMsec="1000"
        path = "/gulfstream/mamba/v1/pHomePageCallCarEstimateV2"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 拼车纯价格预估 */
    CarpoolPriceResponse PCarpoolPrice(1: CarpoolEstimateRequest req)(
        path = "/gulfstream/mamba/v1/internal/pCarpoolPrice"
        httpMethod = "GET"
        contentType = "form"
    );
    /** 默认拼表单预估 */
    RecCarpoolEstimateResp PRecCarpoolEstimate(1: RecCarpoolEstimateReq req)(
        connectTimeoutMsec="100"
        timeoutMsec="2000"
        path = "/gulfstream/mamba/v1/pRecCarpoolEstimate"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 邀约预估 */
    CarpoolInvitationEstimateResp PCarpoolInvitationEstimate(1: CarpoolInvitationEstimateRequest req)(
        connectTimeoutMsec="100"
        timeoutMsec="2000"
        path = "/gulfstream/mamba/v1/pCarpoolInvitationEstimate"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 获取券金额和实付价 */
    CouponPriceResponse PGetCouponPrice(1: CouponPriceRequest req)(
        path = "/gulfstream/mamba/v1/pGetCouponPrice"
        httpMethod = "GET"
        contentType = "form"
    );
     /** 保障车队预估 */
     ServiceMultiEstimateResponse PServiceEstimate(1: ServiceEstimateRequest req)(
         path = "/gulfstream/mamba/v1/pServiceEstimate"
         httpMethod = "GET"
         contentType = "form"
     );

    /** 顺风车预估 */
    SFCMultiEstimateResponse PSFCEstimate(1: SFCEstimateRequest req) (
        path = "/gulfstream/mamba/v1/pSFCEstimate"
        httpMethod = "GET"
        contentType = "form"
    );
     /** 顺风车修改时间后预估 */
    SFCModifyEstimateResponse pSFCModifyEstimate(1: SFCModifyEstimateRequest req) (
        path = "/gulfstream/mamba/v1/pSFCModifyEstimate"
        httpMethod = "GET"
        contentType = "form"
    );
    SFCSimpleEstimateResponse pSFCSimpleEstimate(1: SFCSimpleEstimateReq req) (
        path = "/gulfstream/mamba/v1/pSFCSimpleEstimate"
        httpMethod = "POST"
        contentType = "form"
    );

    /** 综合推荐 */
    CompositeTravelRes PCompositeTravel(1: CompositeTravelReq req)(
        path = "/gulfstream/mamba/v1/pCompositeTravel"
        httpMethod = "GET"
        contentType = "form"
    );

     /** 综合推荐 */
    CompositeTravelV2Res PCompositeTravelV2(1: CompositeTravelV2Req req)(
        path = "/gulfstream/mamba/v1/pCompositeTravelV2"
        httpMethod = "GET"
        contentType = "form"
    );

    /** poi综合推荐 */
    CompositeTravelPoiRes PCompositeTravelPoi(1: CompositeTravelPoiReq req)(
        path = "/gulfstream/mamba/v1/pCompositeTravelPoi"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 综合推荐9.0 */
    CompositeTravelV3Res PCompositeTravelV3(1: CompositeTravelV3Req req)(
        path = "/gulfstream/mamba/v1/pCompositeTravelV3"
        httpMethod = "GET"
        contentType = "form"
    );

    CompositeTravelV3Res PCompositeTravelV3Post(1: CompositeTravelV3Req req)(
        path = "/gulfstream/mamba/v1/pCompositeTravelV3"
        httpMethod = "POST"
        contentType = "form"
    );

    /** 聚合时间 */
    CompositeTravelOptionsRes PCompositeTravelOptions(1: CompositeTravelOptionsReq req)(
        path = "/gulfstream/mamba/v1/pCompositeTravelOptions"
        httpMethod = "GET"
        contentType = "form"
    );


     /** maas着陆 */
    pRecommendLandingRes PRecommendLanding(1: CompositeTravelV2Req req)(
        path = "/gulfstream/mamba/v1/pRecommendLanding"
        httpMethod = "GET"
        contentType = "form"
    );

     /** 动态获取车型费项 */
    DynamicFeeDescListResponse PGetDynamicFeeDescList(1: DynamicFeeDescListRequest req)(
        path = "/gulfstream/mamba/v1/pGetDynamicFeeDescList"
        httpMethod = "GET"
        contentType = "form"
    );

     /** 动态获取车型费项 */
    DynamicFeeDescListResponse PGetDynamicFeeDescListPost(1: DynamicFeeDescListRequest req)(
        path = "/gulfstream/mamba/v1/pGetDynamicFeeDescList"
        httpMethod = "POST"
        contentType = "form"
    );

     /** 必有车预估 */
    PickOnTimeEstimateRsp PPickOnTimeEstimate(1: PickOnTimeEstimateReq req)(
        path = "/gulfstream/mamba/v1/pPickOnTimeEstimate"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 等待应答卡片 */
    PGetGuideInfoRsp PGetGuideInfo(1: PGetGuideInfoReq req)(
        connectTimeoutMsec="50"
        timeoutMsec="1000"
        path = "/gulfstream/mamba/v1/internal/pGetGuideInfo"
        httpMethod = "GET"
        contentType = "form"
    )
    
     PGetGuideInfoRsp PGetGuideInfoPost(1: PGetGuideInfoReq req)(
            connectTimeoutMsec="50"
            timeoutMsec="1000"
            path = "/gulfstream/mamba/v1/internal/pGetGuideInfo"
            httpMethod = "POST"
            contentType = "json"
     )

     /** 获取页面实时信息 */
      PGetFormRealDataResp PGetFormRealData(1: PGetFormRealDataReq request) (
          connectTimeoutMsec="50"
          timeoutMsec="100"

          /** HTTP接口请求URI */
          path="/gulfstream/mamba/v1/pGetFormRealData"
          /** HTTP请求方法，GET/POST */
          httpMethod="GET"
          /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
          contentType="form"
      );


     /** 获取页面实时信息 */
      PGetFormRealDataResp PGetFormRealDataPost(1: PGetFormRealDataReq request) (
          connectTimeoutMsec="50"
          timeoutMsec="100"

          /** HTTP接口请求URI */
          path="/gulfstream/mamba/v1/pGetFormRealData"
          /** HTTP请求方法，GET/POST */
          httpMethod = "POST"
          /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
          contentType="form"
      );

     /** 通过EIDList获取车型数据 */
    PGetEstimateDataByEidListResp PGetEstimateDataByEidList(1: PGetEstimateDataByEidListReq req)(
        connectTimeoutMsec="100"
        timeoutMsec="200"
        path = "/gulfstream/mamba/v1/pGetEstimateDataByEidList"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 路线预估 */
    RouteEstimateResponse PRouteEstimate(1: RouteEstimateRequest req)(
        path = "/gulfstream/mamba/v1/pRouteEstimate"
        httpMethod = "GET"
        contentType = "form"

        connectTimeoutMsec="100"
        timeoutMsec="300"
    );

    /** 站点巴士批量列表页预估 */
    IntercityMultiEstimateResponse PIntercityMultiEstimateEstimate(1: IntercityMultiEstimateRequest req)(
        path = "/gulfstream/mamba/v1/pIntercityMultiEstimate"
        httpMethod = "POST"
        contentType = "json"
        connectTimeoutMsec="100"
        timeoutMsec="1000"
    );

    /** 站点巴士详细预估 */
    IntercityEstimateDetailResponse PIntercityEstimateDetail(1: IntercityEstimateDetailRequest req)(
        path = "/gulfstream/mamba/v1/pIntercityEstimateDetail"
        httpMethod = "GET"
        contentType = "form"

        connectTimeoutMsec="100"
        timeoutMsec="500"
    );

    /** 预约单取消挽留预估 */
    AnyCarEstimateV3Rsp POrderBookingPreCancelEstimate(1: AnyCarEstimateReq request) (
        connectTimeoutMsec="50"
        timeoutMsec="1000"

        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/pOrderBookingPreCancelEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    );

    /** 分享地址受邀预估 */
    InvitedEstimateRsp PInvitedEstimate(1: InvitedEstimateReq req)(
        path = "/gulfstream/mamba/v1/pInvitedEstimate"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 批量点预估 */
    MultiPointEstimateResponse PMultiPointEstimate(1: MultiPointEstimateRequest request)(
        path = "/gulfstream/mamba/v1/pMultiPointEstimate"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 获取预估未渲染数据 */
    PGetEstimateDataWithoutRenderResp PGetEstimateDataWithoutRender(1: MultiEstimatePriceRequest request)(
        path = "/gulfstream/mamba/v1/pGetEstimateDataWithoutRender"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 获取预估未渲染数据 */
    PGetEstimateDataWithoutRenderResp PGetEstimateDataWithoutRenderV2(1: PGetEstimateDataWithoutRenderReq request)(
        path = "/gulfstream/mamba/v1/internal/pGetEstimateDataWithoutRender"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 获取企业级预期信息（非渲染数据） */
    GetBusinessFormRealDataResponse PGetBusinessFormRealData(1: GetBusinessFormRealDataRequest request)(
        path = "/gulfstream/mamba/v1/internal/pGetBusinessFormRealData"
        httpMethod = "GET"
        contentType = "form"
    );

     /**自选车价格range预估 */
    BargainRangeEstimateRsp PBargainRangeEstimate(1: BargainRangeEstimateReq req)(
        path = "/gulfstream/mamba/v1/pBargainRangeEstimate"
        httpMethod = "GET"
        contentType = "form"
    );
    /**站点巴士代发单预估 */
    StationBusInsteadOrderEstimateRsp PStationBusInsteadOrderEstimate(1:  StationBusInsteadOrderEstimateReq req)(
        path = "/gulfstream/mamba/v1/pStationBusInsteadOrderEstimate"
        httpMethod = "GET"
        contentType = "form"
    );

     /**站点巴士轻量级获取基础定价 */
     IntercityBasicFeeLiteResponse PGetIntercityBasicFeeLite(1: IntercityBasicFeeLiteRequest request)(
        path = "/gulfstream/mamba/v1/pGetIntercityBasicFeeLite"
        httpMethod = "GET"
        contentType = "form"
     );

     /** 小神车独立页预估 */
     PDidiMiniEstimateResp PDidiMiniEstimate(1: PDidiMiniEstimateReq req)(
            path = "/gulfstream/mamba/v1/pDidiMiniEstimate"
            httpMethod = "GET"
            contentType = "form"
     );

    /** 平台包车预估 */
    EngageCarEstimateResponse PEngageCarEstimate(1: EngageCarEstimateReq req)(
        path = "/gulfstream/mamba/v1/pEngageCarEstimate"
        httpMethod = "GET"
        contentType = "form"
    );

    /** 宠物出行独立页预估 */
    PetsTravelEstimateResponse PPetsTravelEstimate(1: PetsTravelEstimateReq req)(
        path = "/gulfstream/mamba/v1/pPetsTravelEstimate"
        httpMethod = "POST"
        contentType = "json"
        connectTimeoutMsec="100"
        timeoutMsec="1000"
    );

    /** 境外打车独立页预估 */
    OverseaEstimateResponse POverseaEstimate(1: OverseaEstimateReq req)(
        path = "/gulfstream/mamba/v1/pOverseaEstimate"
        httpMethod = "POST"
        contentType = "json"
        connectTimeoutMsec="100"
        timeoutMsec="1000"
    );

      /** 行程中预估 */
      PEstimateByOrderResponse PEstimateByOrder(1: PEstimateByOrderRequest req)(
             path = "/gulfstream/mamba/v1/pEstimateByOrder"
             httpMethod = "GET"
             contentType = "form"
      );
      /** 订单预估无渲染 */
      EstimateOrderWithoutRenderRsp EstimateOrderWithoutRender(1: EstimateOrderWithoutRenderReq req)(
              path = "/gulfstream/mamba/v1/estimateOrderWithoutRender"
              httpMethod = "GET"
              contentType = "form"
      );
      /** pbd 站点巴士多站点价格预估 */
      PbdStationBusMultiStationPriceRsp PPbdStationBusMultiStationPrice(1: PbdStationBusMultiStationPriceReq req)(
         path = "/gulfstream/mamba/v1/PPbdStationBusMultiStationPrice"
         httpMethod = "GET"
         contentType = "form"
      );
      /** pbd 站点巴士下单预估 */
     PbdStationBusOrderEstimateRsp PPbdStationBusOrderEstimate(1: PbdStationBusOrderEstimateReq req)(
          path = "/gulfstream/mamba/v1/PPbdStationBusOrderEstimate"
          httpMethod = "GET"
          contentType = "form"
      );


     /** pbd 站点巴士多路线预估 */
      PbdStationBusMultiEstimateRsp PPbdStationBusMultiEstimate(1: PbdStationBusMultiEstimateReq req)(
         path = "/gulfstream/mamba/v1/PPbdStationBusMultiEstimate"
         httpMethod = "GET"
         contentType = "form"
      );

      /** pbd 站点巴士预估详情 */
      PbdStationBusDetailEstimateRsp PPbdStationBusDetailEstimate(1: PbdStationBusDetailEstimateReq req)(
          path = "/gulfstream/mamba/v1/PPbdStationBusDetailEstimate"
          httpMethod = "GET"
          contentType = "form"
      );

      /** 包车落地页 */
      CharterHomePageRsp PCharterHomePage(1: CharterHomePageReq req)(
            path = "/gulfstream/mamba/v1/pCharterHomePage"
            httpMethod = "GET"
            contentType = "form"
      );

      /** 包车预估页 */
      CharterMultiEstimateRsp PCharterMultiEstimate(1: CharterMultiEstimateReq req)(
            path = "/gulfstream/mamba/v1/pCharterMultiEstimate"
            httpMethod = "GET"
            contentType = "form"
      )

      /** 乘客开始计费透传 */
      PPassengerBeginChargeResponse PPassengerBeginCharge(1: PPassengerBeginChargeRequest req)(
           path = "/gulfstream/mamba/v1/pPassengerBeginCharge"
           httpMethod = "GET"
           contentType = "form"
      );
    /** 获取包车套餐+城市列表 */
    GetRentInfoRsp PGetRentInfo(1: GetRentInfoReq req)(
      path = "/gulfstream/mamba/v1/pGetRentInfo"
      httpMethod = "GET"
      contentType = "form"
    );
    /** 企业级包车预估 */
    B2BEstimateRsp PBusinessRentEstimate(1: BusinessRentEstimateReq req)(
      path = "/gulfstream/mamba/v1/pBusinessRentEstimate"
      httpMethod = "GET"
      contentType = "form"
    )
    /** 拼车行中价格预估 */
    PEstimateCarpoolOrderResponse PEstimateCarpoolMultiOrder(1: PEstimateCarpoolOrderRequest req)(
         path = "/gulfstream/mamba/v1/pEstimateCarpoolMultiOrder"
         httpMethod = "GET"
         contentType = "form"
    )
    PEstimateV3DataResponse PEstimateV3(1: PEstimateV3DataReq req) (
        path = "/gulfstream/mamba/v1/pEstimateV3"
        httpMethod = "POST"
        contentType = "json"
    )
    /** MCP预估  */
    PMCPEstimateResponse PMCPEstimate(1: PMCPEstimateReq req) (
        path = "/gulfstream/mamba/v1/internal/pMCPEstimate"
        httpMethod = "POST"
        contentType = "json"
    )
    /** 企业级专车乘车偏好页 */
    BusinessTailorServiceResponse pGetBusinessTailorService(1: LuxMultiEstimatePriceRequest req)(
        path = "/gulfstream/mamba/v1/pGetBusinessTailorService"
        httpMethod = "get"
        contentType = "form"
    )
    /** 宠物追加车型 */
    AnyCarEstimateV4Resp PetAnyCarEstimate(1:AnyCarEstimateV4Req request) (
        connectTimeoutMsec="50"
        timeoutMsec="1000"
        /** HTTP接口请求URI */
        path="/gulfstream/mamba/v1/petAnyCarEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="GET"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
    /** 会议用车预估接口 */
    MeetingCarEstimateRsp MeetingCarEstimate(1:MeetingCarEstimateReq request) (
        connectTimeoutMsec="50"
        timeoutMsec="1000"
        /** HTTP接口请求URI */
        path="/gulfstream/mamba/inner/v1/meetingCarEstimate"
        /** HTTP请求方法，GET/POST */
        httpMethod="POST"
        /** contentType主要支持两种: json和form，POST请求对应"json/form"，GET请求对应"form", 不能混淆 */
        contentType="form"
    )
}(
    version="1.0.161"
    servName="disf!biz-gs-pre_sale_core"
    servType="http"

    /** 连接超时, 重试相关, 在生成的dirpc sdk中起作用, 这几个参数可以在每个接口中被覆盖 */
    connectTimeoutMsec="50"
    timeoutMsec="400"
)
