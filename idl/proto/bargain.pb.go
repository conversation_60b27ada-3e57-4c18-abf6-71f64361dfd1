// Code generated by http-gen compiler. DO NOT EDIT.
// source: bargain.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*BargainEstimateReq)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainSpecialPriceText)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainSpecialPriceTextV2)(nil)))
	json.Pretouch(reflect.TypeOf((*SpecialPriceRuleInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainFeeMarginItem)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainFeeMargin)(nil)))
	json.Pretouch(reflect.TypeOf((*AnycarBargainFeeMargin)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainCommentTag)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainRecommendData)(nil)))
	json.Pretouch(reflect.TypeOf((*AnycarRecommendData)(nil)))
	json.Pretouch(reflect.TypeOf((*TopCommunicateData)(nil)))
	json.Pretouch(reflect.TypeOf((*RetainFrameData)(nil)))
	json.Pretouch(reflect.TypeOf((*RetainFrameContent)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainEstimateItem)(nil)))
	json.Pretouch(reflect.TypeOf((*TutorialInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainEstimateRsp)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainRangeEstimateReq)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainRangeEstimateRsp)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainRangeEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainRangeSpecialPriceRuleInfo)(nil)))
}

type BargainEstimateReq struct {
	Token           string  `json:"token" form:"token"`                 //用户认证token
	UserType        int32   `json:"user_type" form:"user_type"`         //1普通用户；2企业用户
	AppVersion      string  `json:"app_version" form:"app_version"`     //端版本
	AccessKeyId     int32   `json:"access_key_id" form:"access_key_id"` //端来源
	Channel         string  `json:"channel" form:"channel"`             //渠道号
	ClientType      int32   `json:"client_type" form:"client_type"`     //端类型
	Lang            string  `json:"lang" form:"lang"`                   //端语种
	PlatformType    int32   `json:"platform_type" form:"platform_type"` //端(平台)类型-2
	MapType         string  `json:"map_type" form:"map_type"`           //地图类型
	Lat             float64 `json:"lat" form:"lat"`                     //定位点
	Lng             float64 `json:"lng" form:"lng"`                     //定位点
	FromLat         float64 `json:"from_lat" form:"from_lat"`           //起点
	FromLng         float64 `json:"from_lng" form:"from_lng"`           //起点
	FromPoiId       string  `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType     string  `json:"from_poi_type" form:"from_poi_type"`
	FromAddress     string  `json:"from_address" form:"from_address"`
	FromName        string  `json:"from_name" form:"from_name"`
	ChooseFSearchid string  `json:"choose_f_searchid" form:"choose_f_searchid"` //用户选择起点请求ID
	ToLat           float64 `json:"to_lat" form:"to_lat"`                       //终点
	ToLng           float64 `json:"to_lng" form:"to_lng"`                       //终点
	ToPoiId         string  `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType       string  `json:"to_poi_type" form:"to_poi_type"`
	ToAddress       string  `json:"to_address" form:"to_address"`
	ToName          string  `json:"to_name" form:"to_name"`
	ChooseTSearchid string  `json:"choose_t_searchid" form:"choose_t_searchid"` //用户选择终点请求ID
	From            string  `json:"from" form:"from"`                           //来源标识：2.0:estimate ,1.0:home。1.0的获取有些问题，需要和前端沟通再使用
	OrderId         string  `json:"order_id" form:"order_id"`                   //订单号
	Xpsid           *string `json:"xpsid,omitempty" form:"xpsid"`
	XpsidRoot       *string `json:"xpsid_root,omitempty" form:"xpsid_root"`
}

func (x *BargainEstimateReq) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *BargainEstimateReq) GetUserType() (r int32) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *BargainEstimateReq) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *BargainEstimateReq) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *BargainEstimateReq) GetChannel() (r string) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *BargainEstimateReq) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *BargainEstimateReq) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *BargainEstimateReq) GetPlatformType() (r int32) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *BargainEstimateReq) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *BargainEstimateReq) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *BargainEstimateReq) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *BargainEstimateReq) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *BargainEstimateReq) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *BargainEstimateReq) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *BargainEstimateReq) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *BargainEstimateReq) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *BargainEstimateReq) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *BargainEstimateReq) GetChooseFSearchid() (r string) {
	if x != nil {
		return x.ChooseFSearchid
	}
	return r
}

func (x *BargainEstimateReq) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *BargainEstimateReq) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *BargainEstimateReq) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *BargainEstimateReq) GetToPoiType() (r string) {
	if x != nil {
		return x.ToPoiType
	}
	return r
}

func (x *BargainEstimateReq) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *BargainEstimateReq) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *BargainEstimateReq) GetChooseTSearchid() (r string) {
	if x != nil {
		return x.ChooseTSearchid
	}
	return r
}

func (x *BargainEstimateReq) GetFrom() (r string) {
	if x != nil {
		return x.From
	}
	return r
}

func (x *BargainEstimateReq) GetOrderId() (r string) {
	if x != nil {
		return x.OrderId
	}
	return r
}

func (x *BargainEstimateReq) GetXpsid() (r string) {
	if x != nil && x.Xpsid != nil {
		return *x.Xpsid
	}
	return r
}

func (x *BargainEstimateReq) GetXpsidRoot() (r string) {
	if x != nil && x.XpsidRoot != nil {
		return *x.XpsidRoot
	}
	return r
}

func (x *BargainEstimateReq) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *BargainEstimateReq) SetUserType(v int32) {
	if x != nil {
		x.UserType = v
	}
}

func (x *BargainEstimateReq) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *BargainEstimateReq) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *BargainEstimateReq) SetChannel(v string) {
	if x != nil {
		x.Channel = v
	}
}

func (x *BargainEstimateReq) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *BargainEstimateReq) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *BargainEstimateReq) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *BargainEstimateReq) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *BargainEstimateReq) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *BargainEstimateReq) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *BargainEstimateReq) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *BargainEstimateReq) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *BargainEstimateReq) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *BargainEstimateReq) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *BargainEstimateReq) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *BargainEstimateReq) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *BargainEstimateReq) SetChooseFSearchid(v string) {
	if x != nil {
		x.ChooseFSearchid = v
	}
}

func (x *BargainEstimateReq) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *BargainEstimateReq) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *BargainEstimateReq) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *BargainEstimateReq) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = v
	}
}

func (x *BargainEstimateReq) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *BargainEstimateReq) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *BargainEstimateReq) SetChooseTSearchid(v string) {
	if x != nil {
		x.ChooseTSearchid = v
	}
}

func (x *BargainEstimateReq) SetFrom(v string) {
	if x != nil {
		x.From = v
	}
}

func (x *BargainEstimateReq) SetOrderId(v string) {
	if x != nil {
		x.OrderId = v
	}
}

func (x *BargainEstimateReq) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = &v
	}
}

func (x *BargainEstimateReq) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = &v
	}
}

func (p *BargainEstimateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainEstimateReq(%+v)", *p)
}

type BargainSpecialPriceText struct {
	RuleType []int32 `json:"rule_type" form:"rule_type"` //规则
	Text     string  `json:"text" form:"text"`           //文案
}

func (x *BargainSpecialPriceText) GetRuleType() (r []int32) {
	if x != nil {
		return x.RuleType
	}
	return r
}

func (x *BargainSpecialPriceText) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *BargainSpecialPriceText) SetRuleType(v []int32) {
	if x != nil {
		x.RuleType = v
	}
}

func (x *BargainSpecialPriceText) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (p *BargainSpecialPriceText) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainSpecialPriceText(%+v)", *p)
}

type BargainSpecialPriceTextV2 struct {
	RuleInfo []*SpecialPriceRuleInfo `json:"rule_info,omitempty" form:"rule_info"` //规则文案信息
}

func (x *BargainSpecialPriceTextV2) GetRuleInfo() (r []*SpecialPriceRuleInfo) {
	if x != nil {
		return x.RuleInfo
	}
	return r
}

func (x *BargainSpecialPriceTextV2) SetRuleInfo(v []*SpecialPriceRuleInfo) {
	if x != nil {
		x.RuleInfo = v
	}
}

func (p *BargainSpecialPriceTextV2) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainSpecialPriceTextV2(%+v)", *p)
}

type SpecialPriceRuleInfo struct {
	RuleType []int32 `json:"rule_type" form:"rule_type"` //规则
	Text     string  `json:"text" form:"text"`           //文案
}

func (x *SpecialPriceRuleInfo) GetRuleType() (r []int32) {
	if x != nil {
		return x.RuleType
	}
	return r
}

func (x *SpecialPriceRuleInfo) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *SpecialPriceRuleInfo) SetRuleType(v []int32) {
	if x != nil {
		x.RuleType = v
	}
}

func (x *SpecialPriceRuleInfo) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (p *SpecialPriceRuleInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SpecialPriceRuleInfo(%+v)", *p)
}

type BargainFeeMarginItem struct {
	Amount string `json:"amount" form:"amount"` //具体金额
	Notice string `json:"notice" form:"notice"` //提示文案
}

func (x *BargainFeeMarginItem) GetAmount() (r string) {
	if x != nil {
		return x.Amount
	}
	return r
}

func (x *BargainFeeMarginItem) GetNotice() (r string) {
	if x != nil {
		return x.Notice
	}
	return r
}

func (x *BargainFeeMarginItem) SetAmount(v string) {
	if x != nil {
		x.Amount = v
	}
}

func (x *BargainFeeMarginItem) SetNotice(v string) {
	if x != nil {
		x.Notice = v
	}
}

func (p *BargainFeeMarginItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainFeeMarginItem(%+v)", *p)
}

type BargainFeeMargin struct {
	Ceil   *BargainFeeMarginItem `json:"ceil,omitempty" form:"ceil"`     //上限
	Floor1 *BargainFeeMarginItem `json:"floor1,omitempty" form:"floor1"` //下限
	Floor2 *BargainFeeMarginItem `json:"floor2,omitempty" form:"floor2"` //下下限
}

func (x *BargainFeeMargin) GetCeil() (r *BargainFeeMarginItem) {
	if x != nil {
		return x.Ceil
	}
	return r
}

func (x *BargainFeeMargin) GetFloor1() (r *BargainFeeMarginItem) {
	if x != nil {
		return x.Floor1
	}
	return r
}

func (x *BargainFeeMargin) GetFloor2() (r *BargainFeeMarginItem) {
	if x != nil {
		return x.Floor2
	}
	return r
}

func (x *BargainFeeMargin) SetCeil(v *BargainFeeMarginItem) {
	if x != nil {
		x.Ceil = v
	}
}

func (x *BargainFeeMargin) SetFloor1(v *BargainFeeMarginItem) {
	if x != nil {
		x.Floor1 = v
	}
}

func (x *BargainFeeMargin) SetFloor2(v *BargainFeeMarginItem) {
	if x != nil {
		x.Floor2 = v
	}
}

func (p *BargainFeeMargin) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainFeeMargin(%+v)", *p)
}

type AnycarBargainFeeMargin struct {
	Ceil       *BargainFeeMarginItem `json:"ceil,omitempty" form:"ceil"`               //上限
	Floor      *BargainFeeMarginItem `json:"floor,omitempty" form:"floor"`             //下限
	LowerFloor *BargainFeeMarginItem `json:"lower_floor,omitempty" form:"lower_floor"` //下下限
}

func (x *AnycarBargainFeeMargin) GetCeil() (r *BargainFeeMarginItem) {
	if x != nil {
		return x.Ceil
	}
	return r
}

func (x *AnycarBargainFeeMargin) GetFloor() (r *BargainFeeMarginItem) {
	if x != nil {
		return x.Floor
	}
	return r
}

func (x *AnycarBargainFeeMargin) GetLowerFloor() (r *BargainFeeMarginItem) {
	if x != nil {
		return x.LowerFloor
	}
	return r
}

func (x *AnycarBargainFeeMargin) SetCeil(v *BargainFeeMarginItem) {
	if x != nil {
		x.Ceil = v
	}
}

func (x *AnycarBargainFeeMargin) SetFloor(v *BargainFeeMarginItem) {
	if x != nil {
		x.Floor = v
	}
}

func (x *AnycarBargainFeeMargin) SetLowerFloor(v *BargainFeeMarginItem) {
	if x != nil {
		x.LowerFloor = v
	}
}

func (p *AnycarBargainFeeMargin) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnycarBargainFeeMargin(%+v)", *p)
}

type BargainCommentTag struct {
	Id   int32  `json:"id" form:"id"`
	Text string `json:"text" form:"text"`
}

func (x *BargainCommentTag) GetId() (r int32) {
	if x != nil {
		return x.Id
	}
	return r
}

func (x *BargainCommentTag) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *BargainCommentTag) SetId(v int32) {
	if x != nil {
		x.Id = v
	}
}

func (x *BargainCommentTag) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (p *BargainCommentTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainCommentTag(%+v)", *p)
}

type BargainRecommendData struct {
	RecommendPrice     string `json:"recommend_price" form:"recommend_price"`
	RecommendText      string `json:"recommend_text" form:"recommend_text"`
	RecommendRightIcon string `json:"recommend_right_icon" form:"recommend_right_icon"` //推荐价右icon
	RecommendStyle     string `json:"recommend_style" form:"recommend_style"`           //推荐价样式 1：光条特效
}

func (x *BargainRecommendData) GetRecommendPrice() (r string) {
	if x != nil {
		return x.RecommendPrice
	}
	return r
}

func (x *BargainRecommendData) GetRecommendText() (r string) {
	if x != nil {
		return x.RecommendText
	}
	return r
}

func (x *BargainRecommendData) GetRecommendRightIcon() (r string) {
	if x != nil {
		return x.RecommendRightIcon
	}
	return r
}

func (x *BargainRecommendData) GetRecommendStyle() (r string) {
	if x != nil {
		return x.RecommendStyle
	}
	return r
}

func (x *BargainRecommendData) SetRecommendPrice(v string) {
	if x != nil {
		x.RecommendPrice = v
	}
}

func (x *BargainRecommendData) SetRecommendText(v string) {
	if x != nil {
		x.RecommendText = v
	}
}

func (x *BargainRecommendData) SetRecommendRightIcon(v string) {
	if x != nil {
		x.RecommendRightIcon = v
	}
}

func (x *BargainRecommendData) SetRecommendStyle(v string) {
	if x != nil {
		x.RecommendStyle = v
	}
}

func (p *BargainRecommendData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainRecommendData(%+v)", *p)
}

type AnycarRecommendData struct {
	RecommendPrice string `json:"recommend_price" form:"recommend_price"`
}

func (x *AnycarRecommendData) GetRecommendPrice() (r string) {
	if x != nil {
		return x.RecommendPrice
	}
	return r
}

func (x *AnycarRecommendData) SetRecommendPrice(v string) {
	if x != nil {
		x.RecommendPrice = v
	}
}

func (p *AnycarRecommendData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnycarRecommendData(%+v)", *p)
}

type TopCommunicateData struct {
	Text   string `json:"text" form:"text"`
	ImgUrl string `json:"img_url" form:"img_url"`
}

func (x *TopCommunicateData) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *TopCommunicateData) GetImgUrl() (r string) {
	if x != nil {
		return x.ImgUrl
	}
	return r
}

func (x *TopCommunicateData) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *TopCommunicateData) SetImgUrl(v string) {
	if x != nil {
		x.ImgUrl = v
	}
}

func (p *TopCommunicateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TopCommunicateData(%+v)", *p)
}

type RetainFrameData struct {
	Coupon *RetainFrameContent `json:"coupon,omitempty" form:"coupon"`
	Normal *RetainFrameContent `json:"normal,omitempty" form:"normal"`
}

func (x *RetainFrameData) GetCoupon() (r *RetainFrameContent) {
	if x != nil {
		return x.Coupon
	}
	return r
}

func (x *RetainFrameData) GetNormal() (r *RetainFrameContent) {
	if x != nil {
		return x.Normal
	}
	return r
}

func (x *RetainFrameData) SetCoupon(v *RetainFrameContent) {
	if x != nil {
		x.Coupon = v
	}
}

func (x *RetainFrameData) SetNormal(v *RetainFrameContent) {
	if x != nil {
		x.Normal = v
	}
}

func (p *RetainFrameData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetainFrameData(%+v)", *p)
}

type RetainFrameContent struct {
	Title           string `json:"title" form:"title"`                         //标题
	SubTitle        string `json:"sub_title" form:"sub_title"`                 //子标题
	BgImgUrl        string `json:"bg_img_url" form:"bg_img_url"`               //背景图片地址
	LeftButtonText  string `json:"left_button_text" form:"left_button_text"`   //左按钮文案
	RightButtonText string `json:"right_button_text" form:"right_button_text"` //右按钮文案
}

func (x *RetainFrameContent) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *RetainFrameContent) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *RetainFrameContent) GetBgImgUrl() (r string) {
	if x != nil {
		return x.BgImgUrl
	}
	return r
}

func (x *RetainFrameContent) GetLeftButtonText() (r string) {
	if x != nil {
		return x.LeftButtonText
	}
	return r
}

func (x *RetainFrameContent) GetRightButtonText() (r string) {
	if x != nil {
		return x.RightButtonText
	}
	return r
}

func (x *RetainFrameContent) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *RetainFrameContent) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *RetainFrameContent) SetBgImgUrl(v string) {
	if x != nil {
		x.BgImgUrl = v
	}
}

func (x *RetainFrameContent) SetLeftButtonText(v string) {
	if x != nil {
		x.LeftButtonText = v
	}
}

func (x *RetainFrameContent) SetRightButtonText(v string) {
	if x != nil {
		x.RightButtonText = v
	}
}

func (p *RetainFrameContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetainFrameContent(%+v)", *p)
}

type BargainEstimateItem struct {
	IntroMsg            string                     `json:"intro_msg" form:"intro_msg"`                                   //预估价格和里程文案
	RequireLevel        int32                      `json:"require_level" form:"require_level"`                           //车型
	BusinessId          int32                      `json:"business_id" form:"business_id"`                               //业务线
	ProductId           int32                      `json:"product_id" form:"product_id"`                                 //产品线ID
	ComboType           int32                      `json:"combo_type" form:"combo_type"`                                 //combo_type
	ProductCategory     int32                      `json:"product_category" form:"product_category"`                     //品类ID
	EstimateId          string                     `json:"estimate_id" form:"estimate_id"`                               //预估id
	FeeAmount           string                     `json:"fee_amount" form:"fee_amount"`                                 //单纯价格
	FeeMargin           *BargainFeeMargin          `json:"fee_margin,omitempty" form:"fee_margin"`                       //费用边际
	CommentTags         []*BargainCommentTag       `json:"comment_tags,omitempty" form:"comment_tags"`                   //备注标签
	SpecialPriceText    *BargainSpecialPriceText   `json:"special_price_text,omitempty" form:"special_price_text"`       //特殊价格沟通，比如高速费
	EstimateText        string                     `json:"estimate_text" form:"estimate_text"`                           //新页面预估文案
	RecommendData       []*BargainRecommendData    `json:"recommend_data,omitempty" form:"recommend_data"`               //推荐价
	TutorialInfo        *TutorialInfo              `json:"tutorial_info,omitempty" form:"tutorial_info"`                 //新客蒙层样式信息
	TopCommunicateData  *TopCommunicateData        `json:"top_communicate_data,omitempty" form:"top_communicate_data"`   //价值透传文案
	RetainFrameData     *RetainFrameData           `json:"retain_frame_data,omitempty" form:"retain_frame_data"`         //离开挽留弹框文案
	SpecialPriceTextV2  *BargainSpecialPriceTextV2 `json:"special_price_text_v2,omitempty" form:"special_price_text_v2"` //特殊价格沟通V2，比如高速费
	AnycarFeeMargin     *AnycarBargainFeeMargin    `json:"anycar_fee_margin,omitempty" form:"anycar_fee_margin"`         //等待应答费用边际
	CouponAmount        float64                    `json:"coupon_amount" form:"coupon_amount"`                           //优惠总金额
	AnycarRecommendData []*AnycarRecommendData     `json:"anycar_recommend_data,omitempty" form:"anycar_recommend_data"` //等待应答推荐价
}

func (x *BargainEstimateItem) GetIntroMsg() (r string) {
	if x != nil {
		return x.IntroMsg
	}
	return r
}

func (x *BargainEstimateItem) GetRequireLevel() (r int32) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *BargainEstimateItem) GetBusinessId() (r int32) {
	if x != nil {
		return x.BusinessId
	}
	return r
}

func (x *BargainEstimateItem) GetProductId() (r int32) {
	if x != nil {
		return x.ProductId
	}
	return r
}

func (x *BargainEstimateItem) GetComboType() (r int32) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *BargainEstimateItem) GetProductCategory() (r int32) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *BargainEstimateItem) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *BargainEstimateItem) GetFeeAmount() (r string) {
	if x != nil {
		return x.FeeAmount
	}
	return r
}

func (x *BargainEstimateItem) GetFeeMargin() (r *BargainFeeMargin) {
	if x != nil {
		return x.FeeMargin
	}
	return r
}

func (x *BargainEstimateItem) GetCommentTags() (r []*BargainCommentTag) {
	if x != nil {
		return x.CommentTags
	}
	return r
}

func (x *BargainEstimateItem) GetSpecialPriceText() (r *BargainSpecialPriceText) {
	if x != nil {
		return x.SpecialPriceText
	}
	return r
}

func (x *BargainEstimateItem) GetEstimateText() (r string) {
	if x != nil {
		return x.EstimateText
	}
	return r
}

func (x *BargainEstimateItem) GetRecommendData() (r []*BargainRecommendData) {
	if x != nil {
		return x.RecommendData
	}
	return r
}

func (x *BargainEstimateItem) GetTutorialInfo() (r *TutorialInfo) {
	if x != nil {
		return x.TutorialInfo
	}
	return r
}

func (x *BargainEstimateItem) GetTopCommunicateData() (r *TopCommunicateData) {
	if x != nil {
		return x.TopCommunicateData
	}
	return r
}

func (x *BargainEstimateItem) GetRetainFrameData() (r *RetainFrameData) {
	if x != nil {
		return x.RetainFrameData
	}
	return r
}

func (x *BargainEstimateItem) GetSpecialPriceTextV2() (r *BargainSpecialPriceTextV2) {
	if x != nil {
		return x.SpecialPriceTextV2
	}
	return r
}

func (x *BargainEstimateItem) GetAnycarFeeMargin() (r *AnycarBargainFeeMargin) {
	if x != nil {
		return x.AnycarFeeMargin
	}
	return r
}

func (x *BargainEstimateItem) GetCouponAmount() (r float64) {
	if x != nil {
		return x.CouponAmount
	}
	return r
}

func (x *BargainEstimateItem) GetAnycarRecommendData() (r []*AnycarRecommendData) {
	if x != nil {
		return x.AnycarRecommendData
	}
	return r
}

func (x *BargainEstimateItem) SetIntroMsg(v string) {
	if x != nil {
		x.IntroMsg = v
	}
}

func (x *BargainEstimateItem) SetRequireLevel(v int32) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *BargainEstimateItem) SetBusinessId(v int32) {
	if x != nil {
		x.BusinessId = v
	}
}

func (x *BargainEstimateItem) SetProductId(v int32) {
	if x != nil {
		x.ProductId = v
	}
}

func (x *BargainEstimateItem) SetComboType(v int32) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *BargainEstimateItem) SetProductCategory(v int32) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *BargainEstimateItem) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *BargainEstimateItem) SetFeeAmount(v string) {
	if x != nil {
		x.FeeAmount = v
	}
}

func (x *BargainEstimateItem) SetFeeMargin(v *BargainFeeMargin) {
	if x != nil {
		x.FeeMargin = v
	}
}

func (x *BargainEstimateItem) SetCommentTags(v []*BargainCommentTag) {
	if x != nil {
		x.CommentTags = v
	}
}

func (x *BargainEstimateItem) SetSpecialPriceText(v *BargainSpecialPriceText) {
	if x != nil {
		x.SpecialPriceText = v
	}
}

func (x *BargainEstimateItem) SetEstimateText(v string) {
	if x != nil {
		x.EstimateText = v
	}
}

func (x *BargainEstimateItem) SetRecommendData(v []*BargainRecommendData) {
	if x != nil {
		x.RecommendData = v
	}
}

func (x *BargainEstimateItem) SetTutorialInfo(v *TutorialInfo) {
	if x != nil {
		x.TutorialInfo = v
	}
}

func (x *BargainEstimateItem) SetTopCommunicateData(v *TopCommunicateData) {
	if x != nil {
		x.TopCommunicateData = v
	}
}

func (x *BargainEstimateItem) SetRetainFrameData(v *RetainFrameData) {
	if x != nil {
		x.RetainFrameData = v
	}
}

func (x *BargainEstimateItem) SetSpecialPriceTextV2(v *BargainSpecialPriceTextV2) {
	if x != nil {
		x.SpecialPriceTextV2 = v
	}
}

func (x *BargainEstimateItem) SetAnycarFeeMargin(v *AnycarBargainFeeMargin) {
	if x != nil {
		x.AnycarFeeMargin = v
	}
}

func (x *BargainEstimateItem) SetCouponAmount(v float64) {
	if x != nil {
		x.CouponAmount = v
	}
}

func (x *BargainEstimateItem) SetAnycarRecommendData(v []*AnycarRecommendData) {
	if x != nil {
		x.AnycarRecommendData = v
	}
}

func (p *BargainEstimateItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainEstimateItem(%+v)", *p)
}

type TutorialInfo struct {
	ImgUrl string `json:"img_url" form:"img_url"` //图片地址
	Text   string `json:"text" form:"text"`       //标题
}

func (x *TutorialInfo) GetImgUrl() (r string) {
	if x != nil {
		return x.ImgUrl
	}
	return r
}

func (x *TutorialInfo) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *TutorialInfo) SetImgUrl(v string) {
	if x != nil {
		x.ImgUrl = v
	}
}

func (x *TutorialInfo) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (p *TutorialInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TutorialInfo(%+v)", *p)
}

type BargainEstimateData struct {
	EstimateData []*BargainEstimateItem `json:"estimate_data" form:"estimate_data"`   //预估价格和里程文案
	FeeDetailUrl string                 `json:"fee_detail_url" form:"fee_detail_url"` //费用详情H5
}

func (x *BargainEstimateData) GetEstimateData() (r []*BargainEstimateItem) {
	if x != nil {
		return x.EstimateData
	}
	return r
}

func (x *BargainEstimateData) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *BargainEstimateData) SetEstimateData(v []*BargainEstimateItem) {
	if x != nil {
		x.EstimateData = v
	}
}

func (x *BargainEstimateData) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (p *BargainEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainEstimateData(%+v)", *p)
}

type BargainEstimateRsp struct {
	Errno   int32                `json:"errno" form:"errno"`
	Errmsg  string               `json:"errmsg" form:"errmsg"`
	Data    *BargainEstimateData `json:"data,omitempty" form:"data"`
	TraceId string               `json:"trace_id" form:"trace_id"`
}

func (x *BargainEstimateRsp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *BargainEstimateRsp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *BargainEstimateRsp) GetData() (r *BargainEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *BargainEstimateRsp) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *BargainEstimateRsp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *BargainEstimateRsp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *BargainEstimateRsp) SetData(v *BargainEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (x *BargainEstimateRsp) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (p *BargainEstimateRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainEstimateRsp(%+v)", *p)
}

type BargainRangeEstimateReq struct {
	Token                string   `json:"token" form:"token"`                 //用户认证token
	UserType             int32    `json:"user_type" form:"user_type"`         //1普通用户；2企业用户
	AppVersion           string   `json:"app_version" form:"app_version"`     //端版本
	AccessKeyId          int32    `json:"access_key_id" form:"access_key_id"` //端来源
	Channel              string   `json:"channel" form:"channel"`             //渠道号
	ClientType           int32    `json:"client_type" form:"client_type"`     //端类型
	Lang                 string   `json:"lang" form:"lang"`                   //端语种
	PlatformType         int32    `json:"platform_type" form:"platform_type"` //端(平台)类型-2
	MapType              string   `json:"map_type" form:"map_type"`           //地图类型
	Lat                  float64  `json:"lat" form:"lat"`                     //定位点
	Lng                  float64  `json:"lng" form:"lng"`                     //定位点
	FromLat              float64  `json:"from_lat" form:"from_lat"`           //起点
	FromLng              float64  `json:"from_lng" form:"from_lng"`           //起点
	FromPoiId            string   `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType          string   `json:"from_poi_type" form:"from_poi_type"`
	FromAddress          string   `json:"from_address" form:"from_address"`
	FromName             string   `json:"from_name" form:"from_name"`
	ToLat                float64  `json:"to_lat" form:"to_lat"` //终点
	ToLng                float64  `json:"to_lng" form:"to_lng"` //终点
	ToPoiId              string   `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType            string   `json:"to_poi_type" form:"to_poi_type"`
	ToAddress            string   `json:"to_address" form:"to_address"`
	ToName               string   `json:"to_name" form:"to_name"`
	FastCarEstimateFee   *float64 `json:"fast_car_estimate_fee,omitempty" form:"fast_car_estimate_fee"`       //快车预估价
	EstimateId           *string  `json:"estimate_id,omitempty" form:"estimate_id"`                           //自选车价格range对应预估id
	EstimateTraceId      string   `json:"estimate_trace_id" form:"estimate_trace_id"`                         //预估traceid
	SpFastCarEstimateFee *float64 `json:"sp_fast_car_estimate_fee,omitempty" form:"sp_fast_car_estimate_fee"` //特惠快车预估价
	BasicFee             *float64 `json:"basic_fee,omitempty" form:"basic_fee"`                               //基础费（不含附加费，由定价引擎返回）
	Xpsid                *string  `json:"xpsid,omitempty" form:"xpsid"`
	XpsidRoot            *string  `json:"xpsid_root,omitempty" form:"xpsid_root"`
}

func (x *BargainRangeEstimateReq) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *BargainRangeEstimateReq) GetUserType() (r int32) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *BargainRangeEstimateReq) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *BargainRangeEstimateReq) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *BargainRangeEstimateReq) GetChannel() (r string) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *BargainRangeEstimateReq) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *BargainRangeEstimateReq) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *BargainRangeEstimateReq) GetPlatformType() (r int32) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *BargainRangeEstimateReq) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *BargainRangeEstimateReq) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *BargainRangeEstimateReq) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *BargainRangeEstimateReq) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *BargainRangeEstimateReq) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *BargainRangeEstimateReq) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *BargainRangeEstimateReq) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *BargainRangeEstimateReq) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *BargainRangeEstimateReq) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *BargainRangeEstimateReq) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *BargainRangeEstimateReq) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *BargainRangeEstimateReq) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *BargainRangeEstimateReq) GetToPoiType() (r string) {
	if x != nil {
		return x.ToPoiType
	}
	return r
}

func (x *BargainRangeEstimateReq) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *BargainRangeEstimateReq) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *BargainRangeEstimateReq) GetFastCarEstimateFee() (r float64) {
	if x != nil && x.FastCarEstimateFee != nil {
		return *x.FastCarEstimateFee
	}
	return r
}

func (x *BargainRangeEstimateReq) GetEstimateId() (r string) {
	if x != nil && x.EstimateId != nil {
		return *x.EstimateId
	}
	return r
}

func (x *BargainRangeEstimateReq) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *BargainRangeEstimateReq) GetSpFastCarEstimateFee() (r float64) {
	if x != nil && x.SpFastCarEstimateFee != nil {
		return *x.SpFastCarEstimateFee
	}
	return r
}

func (x *BargainRangeEstimateReq) GetBasicFee() (r float64) {
	if x != nil && x.BasicFee != nil {
		return *x.BasicFee
	}
	return r
}

func (x *BargainRangeEstimateReq) GetXpsid() (r string) {
	if x != nil && x.Xpsid != nil {
		return *x.Xpsid
	}
	return r
}

func (x *BargainRangeEstimateReq) GetXpsidRoot() (r string) {
	if x != nil && x.XpsidRoot != nil {
		return *x.XpsidRoot
	}
	return r
}

func (x *BargainRangeEstimateReq) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *BargainRangeEstimateReq) SetUserType(v int32) {
	if x != nil {
		x.UserType = v
	}
}

func (x *BargainRangeEstimateReq) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *BargainRangeEstimateReq) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *BargainRangeEstimateReq) SetChannel(v string) {
	if x != nil {
		x.Channel = v
	}
}

func (x *BargainRangeEstimateReq) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *BargainRangeEstimateReq) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *BargainRangeEstimateReq) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *BargainRangeEstimateReq) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *BargainRangeEstimateReq) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *BargainRangeEstimateReq) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *BargainRangeEstimateReq) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *BargainRangeEstimateReq) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *BargainRangeEstimateReq) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *BargainRangeEstimateReq) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *BargainRangeEstimateReq) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *BargainRangeEstimateReq) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *BargainRangeEstimateReq) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *BargainRangeEstimateReq) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *BargainRangeEstimateReq) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *BargainRangeEstimateReq) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = v
	}
}

func (x *BargainRangeEstimateReq) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *BargainRangeEstimateReq) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *BargainRangeEstimateReq) SetFastCarEstimateFee(v float64) {
	if x != nil {
		x.FastCarEstimateFee = &v
	}
}

func (x *BargainRangeEstimateReq) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = &v
	}
}

func (x *BargainRangeEstimateReq) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (x *BargainRangeEstimateReq) SetSpFastCarEstimateFee(v float64) {
	if x != nil {
		x.SpFastCarEstimateFee = &v
	}
}

func (x *BargainRangeEstimateReq) SetBasicFee(v float64) {
	if x != nil {
		x.BasicFee = &v
	}
}

func (x *BargainRangeEstimateReq) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = &v
	}
}

func (x *BargainRangeEstimateReq) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = &v
	}
}

func (p *BargainRangeEstimateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainRangeEstimateReq(%+v)", *p)
}

type BargainRangeEstimateRsp struct {
	Errno   int32                     `json:"errno" form:"errno"`
	Errmsg  string                    `json:"errmsg" form:"errmsg"`
	Data    *BargainRangeEstimateData `json:"data,omitempty" form:"data"`
	TraceId string                    `json:"trace_id" form:"trace_id"`
}

func (x *BargainRangeEstimateRsp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *BargainRangeEstimateRsp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *BargainRangeEstimateRsp) GetData() (r *BargainRangeEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *BargainRangeEstimateRsp) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *BargainRangeEstimateRsp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *BargainRangeEstimateRsp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *BargainRangeEstimateRsp) SetData(v *BargainRangeEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (x *BargainRangeEstimateRsp) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (p *BargainRangeEstimateRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainRangeEstimateRsp(%+v)", *p)
}

type BargainRangeEstimateData struct {
	BackButtonText       string                            `json:"back_button_text" form:"back_button_text"`                         //返回按钮文案
	AnswerRateText       string                            `json:"answer_rate_text" form:"answer_rate_text"`                         //应答率文案
	AnswerRateColor      string                            `json:"answer_rate_color" form:"answer_rate_color"`                       //应答率颜色
	LowPriceBubbleText   string                            `json:"low_price_bubble_text" form:"low_price_bubble_text"`               //低价气泡文案
	HighPriceBubbleText  string                            `json:"high_price_bubble_text" form:"high_price_bubble_text"`             //高价气泡文案
	PriceLimitUpperText  string                            `json:"price_limit_upper_text" form:"price_limit_upper_text"`             //价格上界文案
	SubTitle             string                            `json:"sub_title" form:"sub_title"`                                       //副标题
	SpecialPriceRuleInfo *BargainRangeSpecialPriceRuleInfo `json:"special_price_rule_info,omitempty" form:"special_price_rule_info"` //价格沟通
	BtnText              string                            `json:"btn_text" form:"btn_text"`                                         //按钮文案
	StepSize             int32                             `json:"step_size" form:"step_size"`                                       //步长
	PriceLimitUpper      int32                             `json:"price_limit_upper" form:"price_limit_upper"`                       //价格上界
	PriceLimitLower      int32                             `json:"price_limit_lower" form:"price_limit_lower"`                       //价格下界
	RecommendPriceUpper  int32                             `json:"recommend_price_upper" form:"recommend_price_upper"`               //推荐价上界
	RecommendPriceLower  int32                             `json:"recommend_price_lower" form:"recommend_price_lower"`               //推荐价下界
	WaitReplyPriceUpper  int32                             `json:"wait_reply_price_upper" form:"wait_reply_price_upper"`             //等待应答上上界
	FastCarEstimateFee   float64                           `json:"fast_car_estimate_fee" form:"fast_car_estimate_fee"`               //快车预估价
	SpFastCarEstimateFee float64                           `json:"sp_fast_car_estimate_fee" form:"sp_fast_car_estimate_fee"`         //特惠快车预估价
	MultiRequireProduct  string                            `json:"multi_require_product" form:"multi_require_product"`               //发单参数
	BackgroundImg        string                            `json:"background_img" form:"background_img"`                             //背景图片
}

func (x *BargainRangeEstimateData) GetBackButtonText() (r string) {
	if x != nil {
		return x.BackButtonText
	}
	return r
}

func (x *BargainRangeEstimateData) GetAnswerRateText() (r string) {
	if x != nil {
		return x.AnswerRateText
	}
	return r
}

func (x *BargainRangeEstimateData) GetAnswerRateColor() (r string) {
	if x != nil {
		return x.AnswerRateColor
	}
	return r
}

func (x *BargainRangeEstimateData) GetLowPriceBubbleText() (r string) {
	if x != nil {
		return x.LowPriceBubbleText
	}
	return r
}

func (x *BargainRangeEstimateData) GetHighPriceBubbleText() (r string) {
	if x != nil {
		return x.HighPriceBubbleText
	}
	return r
}

func (x *BargainRangeEstimateData) GetPriceLimitUpperText() (r string) {
	if x != nil {
		return x.PriceLimitUpperText
	}
	return r
}

func (x *BargainRangeEstimateData) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *BargainRangeEstimateData) GetSpecialPriceRuleInfo() (r *BargainRangeSpecialPriceRuleInfo) {
	if x != nil {
		return x.SpecialPriceRuleInfo
	}
	return r
}

func (x *BargainRangeEstimateData) GetBtnText() (r string) {
	if x != nil {
		return x.BtnText
	}
	return r
}

func (x *BargainRangeEstimateData) GetStepSize() (r int32) {
	if x != nil {
		return x.StepSize
	}
	return r
}

func (x *BargainRangeEstimateData) GetPriceLimitUpper() (r int32) {
	if x != nil {
		return x.PriceLimitUpper
	}
	return r
}

func (x *BargainRangeEstimateData) GetPriceLimitLower() (r int32) {
	if x != nil {
		return x.PriceLimitLower
	}
	return r
}

func (x *BargainRangeEstimateData) GetRecommendPriceUpper() (r int32) {
	if x != nil {
		return x.RecommendPriceUpper
	}
	return r
}

func (x *BargainRangeEstimateData) GetRecommendPriceLower() (r int32) {
	if x != nil {
		return x.RecommendPriceLower
	}
	return r
}

func (x *BargainRangeEstimateData) GetWaitReplyPriceUpper() (r int32) {
	if x != nil {
		return x.WaitReplyPriceUpper
	}
	return r
}

func (x *BargainRangeEstimateData) GetFastCarEstimateFee() (r float64) {
	if x != nil {
		return x.FastCarEstimateFee
	}
	return r
}

func (x *BargainRangeEstimateData) GetSpFastCarEstimateFee() (r float64) {
	if x != nil {
		return x.SpFastCarEstimateFee
	}
	return r
}

func (x *BargainRangeEstimateData) GetMultiRequireProduct() (r string) {
	if x != nil {
		return x.MultiRequireProduct
	}
	return r
}

func (x *BargainRangeEstimateData) GetBackgroundImg() (r string) {
	if x != nil {
		return x.BackgroundImg
	}
	return r
}

func (x *BargainRangeEstimateData) SetBackButtonText(v string) {
	if x != nil {
		x.BackButtonText = v
	}
}

func (x *BargainRangeEstimateData) SetAnswerRateText(v string) {
	if x != nil {
		x.AnswerRateText = v
	}
}

func (x *BargainRangeEstimateData) SetAnswerRateColor(v string) {
	if x != nil {
		x.AnswerRateColor = v
	}
}

func (x *BargainRangeEstimateData) SetLowPriceBubbleText(v string) {
	if x != nil {
		x.LowPriceBubbleText = v
	}
}

func (x *BargainRangeEstimateData) SetHighPriceBubbleText(v string) {
	if x != nil {
		x.HighPriceBubbleText = v
	}
}

func (x *BargainRangeEstimateData) SetPriceLimitUpperText(v string) {
	if x != nil {
		x.PriceLimitUpperText = v
	}
}

func (x *BargainRangeEstimateData) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *BargainRangeEstimateData) SetSpecialPriceRuleInfo(v *BargainRangeSpecialPriceRuleInfo) {
	if x != nil {
		x.SpecialPriceRuleInfo = v
	}
}

func (x *BargainRangeEstimateData) SetBtnText(v string) {
	if x != nil {
		x.BtnText = v
	}
}

func (x *BargainRangeEstimateData) SetStepSize(v int32) {
	if x != nil {
		x.StepSize = v
	}
}

func (x *BargainRangeEstimateData) SetPriceLimitUpper(v int32) {
	if x != nil {
		x.PriceLimitUpper = v
	}
}

func (x *BargainRangeEstimateData) SetPriceLimitLower(v int32) {
	if x != nil {
		x.PriceLimitLower = v
	}
}

func (x *BargainRangeEstimateData) SetRecommendPriceUpper(v int32) {
	if x != nil {
		x.RecommendPriceUpper = v
	}
}

func (x *BargainRangeEstimateData) SetRecommendPriceLower(v int32) {
	if x != nil {
		x.RecommendPriceLower = v
	}
}

func (x *BargainRangeEstimateData) SetWaitReplyPriceUpper(v int32) {
	if x != nil {
		x.WaitReplyPriceUpper = v
	}
}

func (x *BargainRangeEstimateData) SetFastCarEstimateFee(v float64) {
	if x != nil {
		x.FastCarEstimateFee = v
	}
}

func (x *BargainRangeEstimateData) SetSpFastCarEstimateFee(v float64) {
	if x != nil {
		x.SpFastCarEstimateFee = v
	}
}

func (x *BargainRangeEstimateData) SetMultiRequireProduct(v string) {
	if x != nil {
		x.MultiRequireProduct = v
	}
}

func (x *BargainRangeEstimateData) SetBackgroundImg(v string) {
	if x != nil {
		x.BackgroundImg = v
	}
}

func (p *BargainRangeEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainRangeEstimateData(%+v)", *p)
}

type BargainRangeSpecialPriceRuleInfo struct {
	RuleType   []int32 `json:"rule_type" form:"rule_type"`     //规则类型
	Text       string  `json:"text" form:"text"`               //文案
	EstimateId string  `json:"estimate_id" form:"estimate_id"` //文案
	Event      string  `json:"event" form:"event"`
}

func (x *BargainRangeSpecialPriceRuleInfo) GetRuleType() (r []int32) {
	if x != nil {
		return x.RuleType
	}
	return r
}

func (x *BargainRangeSpecialPriceRuleInfo) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *BargainRangeSpecialPriceRuleInfo) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *BargainRangeSpecialPriceRuleInfo) GetEvent() (r string) {
	if x != nil {
		return x.Event
	}
	return r
}

func (x *BargainRangeSpecialPriceRuleInfo) SetRuleType(v []int32) {
	if x != nil {
		x.RuleType = v
	}
}

func (x *BargainRangeSpecialPriceRuleInfo) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *BargainRangeSpecialPriceRuleInfo) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *BargainRangeSpecialPriceRuleInfo) SetEvent(v string) {
	if x != nil {
		x.Event = v
	}
}

func (p *BargainRangeSpecialPriceRuleInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainRangeSpecialPriceRuleInfo(%+v)", *p)
}
