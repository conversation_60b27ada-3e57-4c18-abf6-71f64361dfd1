// Code generated by http-gen compiler. DO NOT EDIT.
// source: anycar_estimate.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*AnyCarEstimateV4Req)(nil)))
	json.Pretouch(reflect.TypeOf((*AnyCarEstimateV4Resp)(nil)))
	json.Pretouch(reflect.TypeOf((*AnyCarEstimateAppendCarForm)(nil)))
	json.Pretouch(reflect.TypeOf((*AppendCarButton)(nil)))
	json.Pretouch(reflect.TypeOf((*AppendCarButtonStyle)(nil)))
	json.Pretouch(reflect.TypeOf((*TopButton)(nil)))
	json.Pretouch(reflect.TypeOf((*AuthButton)(nil)))
	json.Pretouch(reflect.TypeOf((*OmegaInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*AnyCarEstimateV3Rsp)(nil)))
	json.Pretouch(reflect.TypeOf((*NewFormEstimateResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*RawEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*NewFormEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*BargainRangePopup)(nil)))
	json.Pretouch(reflect.TypeOf((*SideExtra)(nil)))
	json.Pretouch(reflect.TypeOf((*BottomRule)(nil)))
	json.Pretouch(reflect.TypeOf((*BottomContent)(nil)))
	json.Pretouch(reflect.TypeOf((*NewFormExtraMap)(nil)))
	json.Pretouch(reflect.TypeOf((*NewFormCarpoolSeatOption)(nil)))
	json.Pretouch(reflect.TypeOf((*NewFormUserPayInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SceneInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*CarTag)(nil)))
	json.Pretouch(reflect.TypeOf((*MultiPriceData)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolOption)(nil)))
	json.Pretouch(reflect.TypeOf((*DiscountSet)(nil)))
	json.Pretouch(reflect.TypeOf((*EstimateCouponInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*EstimateSpsLikeWaitReward)(nil)))
	json.Pretouch(reflect.TypeOf((*AnycarEstimateCacheReq)(nil)))
	json.Pretouch(reflect.TypeOf((*AnycarEstimateCacheResp)(nil)))
	json.Pretouch(reflect.TypeOf((*AnycarEstimateDelCacheResp)(nil)))
}

type AnyCarEstimateV4Req struct {
	Token                 string   `json:"token" form:"token"`                           //token
	OrderId               string   `json:"order_id" form:"order_id"`                     //订单ID
	Lang                  string   `json:"lang" form:"lang"`                             //desc="多语言"
	CarpoolSeatNum        int64    `json:"carpool_seat_num" form:"carpool_seat_num"`     //拼成乐订单座位数
	AppVersion            string   `json:"app_version" form:"app_version"`               //端版本号
	Channel               int64    `json:"channel" form:"channel"`                       //渠道标识
	AccessKeyId           int32    `json:"access_key_id" form:"access_key_id"`           //终端来源标识：滴滴iPhone乘客端、滴滴安卓pad司机端、企业级服务端、open-api服务端等
	ClientType            int32    `json:"client_type" form:"client_type"`               //终端来源标识：滴滴iPhone乘客端、滴滴安卓pad司机端、企业级服务端、open-api服务端等
	PageType              *int32   `json:"page_type,omitempty" form:"page_type"`         //可传，也可在接口内set
	ScreenPixels          *string  `json:"screen_pixels,omitempty" form:"screen_pixels"` //可传，也可在接口内set
	ScreenScale           *float64 `json:"screen_scale,omitempty" form:"screen_scale"`
	MultiRequireProduct   *string  `json:"multi_require_product,omitempty" form:"multi_require_product"` //用户勾选项
	OriginId              *int32   `json:"origin_id,omitempty" form:"origin_id"`                         //一些古早的保证预估能成功（主要是中台历史逻辑）的参数
	PlatformType          *int32   `json:"platform_type,omitempty" form:"platform_type"`                 //一些古早的保证预估能成功（主要是中台历史逻辑）的参数
	UserType              *int32   `json:"user_type,omitempty" form:"user_type"`                         //用户类型，普通用户or企业用户：是开端的时候统一给端下发了，端缓存在本地的通用参数
	MapType               *string  `json:"map_type,omitempty" form:"map_type"`                           //用户类型，普通用户or企业用户：是开端的时候统一给端下发了，端缓存在本地的通用参数
	EventKey              *string  `json:"event_key,omitempty" form:"event_key"`                         //请求order_match的时机
	Phone                 string   `json:"phone" form:"phone"`                                           //请求order_match的时机
	Pid                   string   `json:"pid" form:"pid"`
	FormSourceType        string   `json:"form_source_type" form:"form_source_type"`                           //等应答页面区分：拼成乐pincheche_order_match, 平台platform_order_match todo 后续下掉
	WaitAnswerUpgradeDiff *int32   `json:"wait_answer_upgrade_diff,omitempty" form:"wait_answer_upgrade_diff"` //等应答比diff流量
	FontScaleType         *int32   `json:"font_scale_type,omitempty" form:"font_scale_type"`                   //大字版标识
	Xpsid                 *string  `json:"xpsid,omitempty" form:"xpsid"`
	XpsidRoot             *string  `json:"xpsid_root,omitempty" form:"xpsid_root"`
}

func (x *AnyCarEstimateV4Req) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetOrderId() (r string) {
	if x != nil {
		return x.OrderId
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetCarpoolSeatNum() (r int64) {
	if x != nil {
		return x.CarpoolSeatNum
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetChannel() (r int64) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetPageType() (r int32) {
	if x != nil && x.PageType != nil {
		return *x.PageType
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetScreenPixels() (r string) {
	if x != nil && x.ScreenPixels != nil {
		return *x.ScreenPixels
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetScreenScale() (r float64) {
	if x != nil && x.ScreenScale != nil {
		return *x.ScreenScale
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetMultiRequireProduct() (r string) {
	if x != nil && x.MultiRequireProduct != nil {
		return *x.MultiRequireProduct
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetOriginId() (r int32) {
	if x != nil && x.OriginId != nil {
		return *x.OriginId
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetPlatformType() (r int32) {
	if x != nil && x.PlatformType != nil {
		return *x.PlatformType
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetUserType() (r int32) {
	if x != nil && x.UserType != nil {
		return *x.UserType
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetMapType() (r string) {
	if x != nil && x.MapType != nil {
		return *x.MapType
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetEventKey() (r string) {
	if x != nil && x.EventKey != nil {
		return *x.EventKey
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetPhone() (r string) {
	if x != nil {
		return x.Phone
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetPid() (r string) {
	if x != nil {
		return x.Pid
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetFormSourceType() (r string) {
	if x != nil {
		return x.FormSourceType
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetWaitAnswerUpgradeDiff() (r int32) {
	if x != nil && x.WaitAnswerUpgradeDiff != nil {
		return *x.WaitAnswerUpgradeDiff
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetFontScaleType() (r int32) {
	if x != nil && x.FontScaleType != nil {
		return *x.FontScaleType
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetXpsid() (r string) {
	if x != nil && x.Xpsid != nil {
		return *x.Xpsid
	}
	return r
}

func (x *AnyCarEstimateV4Req) GetXpsidRoot() (r string) {
	if x != nil && x.XpsidRoot != nil {
		return *x.XpsidRoot
	}
	return r
}

func (x *AnyCarEstimateV4Req) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *AnyCarEstimateV4Req) SetOrderId(v string) {
	if x != nil {
		x.OrderId = v
	}
}

func (x *AnyCarEstimateV4Req) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *AnyCarEstimateV4Req) SetCarpoolSeatNum(v int64) {
	if x != nil {
		x.CarpoolSeatNum = v
	}
}

func (x *AnyCarEstimateV4Req) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *AnyCarEstimateV4Req) SetChannel(v int64) {
	if x != nil {
		x.Channel = v
	}
}

func (x *AnyCarEstimateV4Req) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *AnyCarEstimateV4Req) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *AnyCarEstimateV4Req) SetPageType(v int32) {
	if x != nil {
		x.PageType = &v
	}
}

func (x *AnyCarEstimateV4Req) SetScreenPixels(v string) {
	if x != nil {
		x.ScreenPixels = &v
	}
}

func (x *AnyCarEstimateV4Req) SetScreenScale(v float64) {
	if x != nil {
		x.ScreenScale = &v
	}
}

func (x *AnyCarEstimateV4Req) SetMultiRequireProduct(v string) {
	if x != nil {
		x.MultiRequireProduct = &v
	}
}

func (x *AnyCarEstimateV4Req) SetOriginId(v int32) {
	if x != nil {
		x.OriginId = &v
	}
}

func (x *AnyCarEstimateV4Req) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = &v
	}
}

func (x *AnyCarEstimateV4Req) SetUserType(v int32) {
	if x != nil {
		x.UserType = &v
	}
}

func (x *AnyCarEstimateV4Req) SetMapType(v string) {
	if x != nil {
		x.MapType = &v
	}
}

func (x *AnyCarEstimateV4Req) SetEventKey(v string) {
	if x != nil {
		x.EventKey = &v
	}
}

func (x *AnyCarEstimateV4Req) SetPhone(v string) {
	if x != nil {
		x.Phone = v
	}
}

func (x *AnyCarEstimateV4Req) SetPid(v string) {
	if x != nil {
		x.Pid = v
	}
}

func (x *AnyCarEstimateV4Req) SetFormSourceType(v string) {
	if x != nil {
		x.FormSourceType = v
	}
}

func (x *AnyCarEstimateV4Req) SetWaitAnswerUpgradeDiff(v int32) {
	if x != nil {
		x.WaitAnswerUpgradeDiff = &v
	}
}

func (x *AnyCarEstimateV4Req) SetFontScaleType(v int32) {
	if x != nil {
		x.FontScaleType = &v
	}
}

func (x *AnyCarEstimateV4Req) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = &v
	}
}

func (x *AnyCarEstimateV4Req) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = &v
	}
}

func (p *AnyCarEstimateV4Req) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnyCarEstimateV4Req(%+v)", *p)
}

type AnyCarEstimateV4Resp struct {
	Errno  int32                        `json:"errno" form:"errno"`         //错误码
	Errmsg string                       `json:"errmsg" form:"errmsg"`       //错误信息
	Data   *AnyCarEstimateAppendCarForm `json:"data,omitempty" form:"data"` //预估数据
}

func (x *AnyCarEstimateV4Resp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *AnyCarEstimateV4Resp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *AnyCarEstimateV4Resp) GetData() (r *AnyCarEstimateAppendCarForm) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *AnyCarEstimateV4Resp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *AnyCarEstimateV4Resp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *AnyCarEstimateV4Resp) SetData(v *AnyCarEstimateAppendCarForm) {
	if x != nil {
		x.Data = v
	}
}

func (p *AnyCarEstimateV4Resp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnyCarEstimateV4Resp(%+v)", *p)
}

type AnyCarEstimateAppendCarForm struct {
	Title          string                   `json:"title" form:"title"`                                 //追加车型title
	EstimateInfo   *NewFormEstimateResponse `json:"estimate_info,omitempty" form:"estimate_info"`       //预估信息
	OmegaInfo      *OmegaInfo               `json:"omega_info,omitempty" form:"omega_info"`             //埋点信息
	Button         *AppendCarButton         `json:"button,omitempty" form:"button"`                     //按钮信息
	AuthButton     *AuthButton              `json:"auth_button,omitempty" form:"auth_button"`           //授权信息
	TopButton      *TopButton               `json:"top_button,omitempty" form:"top_button"`             //顶部全选按钮
	ClickTitle     string                   `json:"click_title" form:"click_title"`                     //点击后需要替换的标题
	FeeMsgTemplate *string                  `json:"fee_msg_template,omitempty" form:"fee_msg_template"` //带币种金额展示模版
	OmegaControl   *string                  `json:"omega_control,omitempty" form:"omega_control"`       //埋点控制，控制品类的custom_tag埋点曝光
}

func (x *AnyCarEstimateAppendCarForm) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *AnyCarEstimateAppendCarForm) GetEstimateInfo() (r *NewFormEstimateResponse) {
	if x != nil {
		return x.EstimateInfo
	}
	return r
}

func (x *AnyCarEstimateAppendCarForm) GetOmegaInfo() (r *OmegaInfo) {
	if x != nil {
		return x.OmegaInfo
	}
	return r
}

func (x *AnyCarEstimateAppendCarForm) GetButton() (r *AppendCarButton) {
	if x != nil {
		return x.Button
	}
	return r
}

func (x *AnyCarEstimateAppendCarForm) GetAuthButton() (r *AuthButton) {
	if x != nil {
		return x.AuthButton
	}
	return r
}

func (x *AnyCarEstimateAppendCarForm) GetTopButton() (r *TopButton) {
	if x != nil {
		return x.TopButton
	}
	return r
}

func (x *AnyCarEstimateAppendCarForm) GetClickTitle() (r string) {
	if x != nil {
		return x.ClickTitle
	}
	return r
}

func (x *AnyCarEstimateAppendCarForm) GetFeeMsgTemplate() (r string) {
	if x != nil && x.FeeMsgTemplate != nil {
		return *x.FeeMsgTemplate
	}
	return r
}

func (x *AnyCarEstimateAppendCarForm) GetOmegaControl() (r string) {
	if x != nil && x.OmegaControl != nil {
		return *x.OmegaControl
	}
	return r
}

func (x *AnyCarEstimateAppendCarForm) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *AnyCarEstimateAppendCarForm) SetEstimateInfo(v *NewFormEstimateResponse) {
	if x != nil {
		x.EstimateInfo = v
	}
}

func (x *AnyCarEstimateAppendCarForm) SetOmegaInfo(v *OmegaInfo) {
	if x != nil {
		x.OmegaInfo = v
	}
}

func (x *AnyCarEstimateAppendCarForm) SetButton(v *AppendCarButton) {
	if x != nil {
		x.Button = v
	}
}

func (x *AnyCarEstimateAppendCarForm) SetAuthButton(v *AuthButton) {
	if x != nil {
		x.AuthButton = v
	}
}

func (x *AnyCarEstimateAppendCarForm) SetTopButton(v *TopButton) {
	if x != nil {
		x.TopButton = v
	}
}

func (x *AnyCarEstimateAppendCarForm) SetClickTitle(v string) {
	if x != nil {
		x.ClickTitle = v
	}
}

func (x *AnyCarEstimateAppendCarForm) SetFeeMsgTemplate(v string) {
	if x != nil {
		x.FeeMsgTemplate = &v
	}
}

func (x *AnyCarEstimateAppendCarForm) SetOmegaControl(v string) {
	if x != nil {
		x.OmegaControl = &v
	}
}

func (p *AnyCarEstimateAppendCarForm) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnyCarEstimateAppendCarForm(%+v)", *p)
}

type AppendCarButton struct {
	ActionType  int32                 `json:"action_type" form:"action_type"`             //点击行为
	Text        string                `json:"text" form:"text"`                           //标题
	ActionOmega *OmegaInfo            `json:"action_omega,omitempty" form:"action_omega"` //点击埋点
	Style       *AppendCarButtonStyle `json:"style,omitempty" form:"style"`               //样式
}

func (x *AppendCarButton) GetActionType() (r int32) {
	if x != nil {
		return x.ActionType
	}
	return r
}

func (x *AppendCarButton) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *AppendCarButton) GetActionOmega() (r *OmegaInfo) {
	if x != nil {
		return x.ActionOmega
	}
	return r
}

func (x *AppendCarButton) GetStyle() (r *AppendCarButtonStyle) {
	if x != nil {
		return x.Style
	}
	return r
}

func (x *AppendCarButton) SetActionType(v int32) {
	if x != nil {
		x.ActionType = v
	}
}

func (x *AppendCarButton) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *AppendCarButton) SetActionOmega(v *OmegaInfo) {
	if x != nil {
		x.ActionOmega = v
	}
}

func (x *AppendCarButton) SetStyle(v *AppendCarButtonStyle) {
	if x != nil {
		x.Style = v
	}
}

func (p *AppendCarButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppendCarButton(%+v)", *p)
}

type AppendCarButtonStyle struct {
	BorderColor     string   `json:"border_color" form:"border_color"`
	FontColor       string   `json:"font_color" form:"font_color"`
	BgGradientColor []string `json:"bg_gradient_color" form:"bg_gradient_color"`
	BoxShadowColor  string   `json:"box_shadow_color" form:"box_shadow_color"`
}

func (x *AppendCarButtonStyle) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *AppendCarButtonStyle) GetFontColor() (r string) {
	if x != nil {
		return x.FontColor
	}
	return r
}

func (x *AppendCarButtonStyle) GetBgGradientColor() (r []string) {
	if x != nil {
		return x.BgGradientColor
	}
	return r
}

func (x *AppendCarButtonStyle) GetBoxShadowColor() (r string) {
	if x != nil {
		return x.BoxShadowColor
	}
	return r
}

func (x *AppendCarButtonStyle) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *AppendCarButtonStyle) SetFontColor(v string) {
	if x != nil {
		x.FontColor = v
	}
}

func (x *AppendCarButtonStyle) SetBgGradientColor(v []string) {
	if x != nil {
		x.BgGradientColor = v
	}
}

func (x *AppendCarButtonStyle) SetBoxShadowColor(v string) {
	if x != nil {
		x.BoxShadowColor = v
	}
}

func (p *AppendCarButtonStyle) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppendCarButtonStyle(%+v)", *p)
}

type TopButton struct {
	ActionType int32  `json:"action_type" form:"action_type"`
	Text       string `json:"text" form:"text"`
}

func (x *TopButton) GetActionType() (r int32) {
	if x != nil {
		return x.ActionType
	}
	return r
}

func (x *TopButton) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *TopButton) SetActionType(v int32) {
	if x != nil {
		x.ActionType = v
	}
}

func (x *TopButton) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (p *TopButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TopButton(%+v)", *p)
}

// 授权按钮
type AuthButton struct {
	AuthButtonText string `json:"auth_button_text" form:"auth_button_text"` //授权按钮文案
}

func (x *AuthButton) GetAuthButtonText() (r string) {
	if x != nil {
		return x.AuthButtonText
	}
	return r
}

func (x *AuthButton) SetAuthButtonText(v string) {
	if x != nil {
		x.AuthButtonText = v
	}
}

func (p *AuthButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuthButton(%+v)", *p)
}

type OmegaInfo struct {
	Key    string                 `json:"key" form:"key"`
	Params map[string]interface{} `json:"params" form:"params"`
}

func (x *OmegaInfo) GetKey() (r string) {
	if x != nil {
		return x.Key
	}
	return r
}

func (x *OmegaInfo) GetParams() (r map[string]interface{}) {
	if x != nil {
		return x.Params
	}
	return r
}

func (x *OmegaInfo) SetKey(v string) {
	if x != nil {
		x.Key = v
	}
}

func (x *OmegaInfo) SetParams(v map[string]interface{}) {
	if x != nil {
		x.Params = v
	}
}

func (p *OmegaInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OmegaInfo(%+v)", *p)
}

/** 等待应答追加车型预估返回数据 */
type AnyCarEstimateV3Rsp struct {
	Errno  int32                    `json:"errno" form:"errno"`         //错误码
	Errmsg string                   `json:"errmsg" form:"errmsg"`       //错误信息
	Data   *NewFormEstimateResponse `json:"data,omitempty" form:"data"` //预估数据
}

func (x *AnyCarEstimateV3Rsp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *AnyCarEstimateV3Rsp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *AnyCarEstimateV3Rsp) GetData() (r *NewFormEstimateResponse) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *AnyCarEstimateV3Rsp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *AnyCarEstimateV3Rsp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *AnyCarEstimateV3Rsp) SetData(v *NewFormEstimateResponse) {
	if x != nil {
		x.Data = v
	}
}

func (p *AnyCarEstimateV3Rsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnyCarEstimateV3Rsp(%+v)", *p)
}

type NewFormEstimateResponse struct {
	EstimateTraceId string                         `json:"estimate_trace_id" form:"estimate_trace_id"`   //anycarV3上为当前的trace_id，在anycarV4上为非缓存那次的预估trace
	EstimateData    map[int64]*NewFormEstimateData `json:"estimate_data" form:"estimate_data"`           //anycarV3上为当前的trace_id，在anycarV4上为非缓存那次的预估trace
	FeeDetailUrl    string                         `json:"fee_detail_url" form:"fee_detail_url"`         //费用明细页地址
	Layout          []*NewFormLayout               `json:"layout" form:"layout"`                         //布局
	ToastTip        *string                        `json:"toast_tip,omitempty" form:"toast_tip"`         //预估完成后提示文案
	PNewOrderParams map[string]string              `json:"p_new_order_params" form:"p_new_order_params"` //发单参数 预估级别
	RawEstimateData map[int64]*RawEstimateData     `json:"raw_estimate_data" form:"raw_estimate_data"`   //预估纯数据字段
}

func (x *NewFormEstimateResponse) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *NewFormEstimateResponse) GetEstimateData() (r map[int64]*NewFormEstimateData) {
	if x != nil {
		return x.EstimateData
	}
	return r
}

func (x *NewFormEstimateResponse) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *NewFormEstimateResponse) GetLayout() (r []*NewFormLayout) {
	if x != nil {
		return x.Layout
	}
	return r
}

func (x *NewFormEstimateResponse) GetToastTip() (r string) {
	if x != nil && x.ToastTip != nil {
		return *x.ToastTip
	}
	return r
}

func (x *NewFormEstimateResponse) GetPNewOrderParams() (r map[string]string) {
	if x != nil {
		return x.PNewOrderParams
	}
	return r
}

func (x *NewFormEstimateResponse) GetRawEstimateData() (r map[int64]*RawEstimateData) {
	if x != nil {
		return x.RawEstimateData
	}
	return r
}

func (x *NewFormEstimateResponse) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (x *NewFormEstimateResponse) SetEstimateData(v map[int64]*NewFormEstimateData) {
	if x != nil {
		x.EstimateData = v
	}
}

func (x *NewFormEstimateResponse) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *NewFormEstimateResponse) SetLayout(v []*NewFormLayout) {
	if x != nil {
		x.Layout = v
	}
}

func (x *NewFormEstimateResponse) SetToastTip(v string) {
	if x != nil {
		x.ToastTip = &v
	}
}

func (x *NewFormEstimateResponse) SetPNewOrderParams(v map[string]string) {
	if x != nil {
		x.PNewOrderParams = v
	}
}

func (x *NewFormEstimateResponse) SetRawEstimateData(v map[int64]*RawEstimateData) {
	if x != nil {
		x.RawEstimateData = v
	}
}

func (p *NewFormEstimateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NewFormEstimateResponse(%+v)", *p)
}

type RawEstimateData struct {
	EstimateId          string            `json:"estimate_id" form:"estimate_id"`
	ProductCategory     int64             `json:"product_category" form:"product_category"`
	ProductId           int64             `json:"product_id" form:"product_id"`
	BusinessId          int64             `json:"business_id" form:"business_id"`
	RequireLevel        string            `json:"require_level" form:"require_level"`
	ComboType           int64             `json:"combo_type" form:"combo_type"`
	LevelType           int32             `json:"level_type" form:"level_type"`
	CarpoolType         int64             `json:"carpool_type" form:"carpool_type"`
	CountPriceType      int32             `json:"count_price_type" form:"count_price_type"`
	IsSpecialPrice      int32             `json:"is_special_price" form:"is_special_price"`
	SpaciousCarAlliance int32             `json:"spacious_car_alliance" form:"spacious_car_alliance"`
	ComboId             int64             `json:"combo_id" form:"combo_id"`
	RouteType           int64             `json:"route_type" form:"route_type"`
	CarpoolPriceType    int32             `json:"carpool_price_type" form:"carpool_price_type"`
	IsDualCarpoolPrice  bool              `json:"is_dual_carpool_price" form:"is_dual_carpool_price"`
	EstimateFee         float64           `json:"estimate_fee" form:"estimate_fee"`             //预估价
	DynamicTotalFee     float64           `json:"dynamic_total_fee" form:"dynamic_total_fee"`   //券前价
	CapPrice            float64           `json:"cap_price" form:"cap_price"`                   //一口价
	BasicTotalFee       float64           `json:"basic_total_fee" form:"basic_total_fee"`       //账单basic_total_fee
	DynamicDiffPrice    float64           `json:"dynamic_diff_price" form:"dynamic_diff_price"` //账单basic_total_fee
	MultiPriceData      []*MultiPriceData `json:"multi_price_data,omitempty" form:"multi_price_data"`
	ExactEstimateFee    float64           `json:"exact_estimate_fee" form:"exact_estimate_fee"`
	PreTotalFee         float64           `json:"pre_total_fee" form:"pre_total_fee"`
	DynamicTimes        float64           `json:"dynamic_times" form:"dynamic_times"`
	RedPacket           float64           `json:"red_packet" form:"red_packet"`
	DiscountSet         *DiscountSet      `json:"discount_set,omitempty" form:"discount_set"`
	RouteIdList         []string          `json:"route_id_list,omitempty" form:"route_id_list"`
	DriverMetre         int64             `json:"driver_metre" form:"driver_metre"`               //单位米
	PaymentId           string            `json:"payment_id" form:"payment_id"`                   //21 企业余额支付 2 个人支付
	UndisplayInLayout   bool              `json:"undisplay_in_layout" form:"undisplay_in_layout"` //追加列表不展示，仅弹窗展示该品类
	Etp                 int32             `json:"etp" form:"etp"`                                 //小巴etp
	SubGroupId          int32             `json:"sub_group_id" form:"sub_group_id"`               //小巴etp
	IsDefaultAuth       int32             `json:"is_default_auth" form:"is_default_auth"`
	RecPos              *int32            `json:"rec_pos,omitempty" form:"rec_pos"`
}

func (x *RawEstimateData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *RawEstimateData) GetProductCategory() (r int64) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *RawEstimateData) GetProductId() (r int64) {
	if x != nil {
		return x.ProductId
	}
	return r
}

func (x *RawEstimateData) GetBusinessId() (r int64) {
	if x != nil {
		return x.BusinessId
	}
	return r
}

func (x *RawEstimateData) GetRequireLevel() (r string) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *RawEstimateData) GetComboType() (r int64) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *RawEstimateData) GetLevelType() (r int32) {
	if x != nil {
		return x.LevelType
	}
	return r
}

func (x *RawEstimateData) GetCarpoolType() (r int64) {
	if x != nil {
		return x.CarpoolType
	}
	return r
}

func (x *RawEstimateData) GetCountPriceType() (r int32) {
	if x != nil {
		return x.CountPriceType
	}
	return r
}

func (x *RawEstimateData) GetIsSpecialPrice() (r int32) {
	if x != nil {
		return x.IsSpecialPrice
	}
	return r
}

func (x *RawEstimateData) GetSpaciousCarAlliance() (r int32) {
	if x != nil {
		return x.SpaciousCarAlliance
	}
	return r
}

func (x *RawEstimateData) GetComboId() (r int64) {
	if x != nil {
		return x.ComboId
	}
	return r
}

func (x *RawEstimateData) GetRouteType() (r int64) {
	if x != nil {
		return x.RouteType
	}
	return r
}

func (x *RawEstimateData) GetCarpoolPriceType() (r int32) {
	if x != nil {
		return x.CarpoolPriceType
	}
	return r
}

func (x *RawEstimateData) GetIsDualCarpoolPrice() (r bool) {
	if x != nil {
		return x.IsDualCarpoolPrice
	}
	return r
}

func (x *RawEstimateData) GetEstimateFee() (r float64) {
	if x != nil {
		return x.EstimateFee
	}
	return r
}

func (x *RawEstimateData) GetDynamicTotalFee() (r float64) {
	if x != nil {
		return x.DynamicTotalFee
	}
	return r
}

func (x *RawEstimateData) GetCapPrice() (r float64) {
	if x != nil {
		return x.CapPrice
	}
	return r
}

func (x *RawEstimateData) GetBasicTotalFee() (r float64) {
	if x != nil {
		return x.BasicTotalFee
	}
	return r
}

func (x *RawEstimateData) GetDynamicDiffPrice() (r float64) {
	if x != nil {
		return x.DynamicDiffPrice
	}
	return r
}

func (x *RawEstimateData) GetMultiPriceData() (r []*MultiPriceData) {
	if x != nil {
		return x.MultiPriceData
	}
	return r
}

func (x *RawEstimateData) GetExactEstimateFee() (r float64) {
	if x != nil {
		return x.ExactEstimateFee
	}
	return r
}

func (x *RawEstimateData) GetPreTotalFee() (r float64) {
	if x != nil {
		return x.PreTotalFee
	}
	return r
}

func (x *RawEstimateData) GetDynamicTimes() (r float64) {
	if x != nil {
		return x.DynamicTimes
	}
	return r
}

func (x *RawEstimateData) GetRedPacket() (r float64) {
	if x != nil {
		return x.RedPacket
	}
	return r
}

func (x *RawEstimateData) GetDiscountSet() (r *DiscountSet) {
	if x != nil {
		return x.DiscountSet
	}
	return r
}

func (x *RawEstimateData) GetRouteIdList() (r []string) {
	if x != nil {
		return x.RouteIdList
	}
	return r
}

func (x *RawEstimateData) GetDriverMetre() (r int64) {
	if x != nil {
		return x.DriverMetre
	}
	return r
}

func (x *RawEstimateData) GetPaymentId() (r string) {
	if x != nil {
		return x.PaymentId
	}
	return r
}

func (x *RawEstimateData) GetUndisplayInLayout() (r bool) {
	if x != nil {
		return x.UndisplayInLayout
	}
	return r
}

func (x *RawEstimateData) GetEtp() (r int32) {
	if x != nil {
		return x.Etp
	}
	return r
}

func (x *RawEstimateData) GetSubGroupId() (r int32) {
	if x != nil {
		return x.SubGroupId
	}
	return r
}

func (x *RawEstimateData) GetIsDefaultAuth() (r int32) {
	if x != nil {
		return x.IsDefaultAuth
	}
	return r
}

func (x *RawEstimateData) GetRecPos() (r int32) {
	if x != nil && x.RecPos != nil {
		return *x.RecPos
	}
	return r
}

func (x *RawEstimateData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *RawEstimateData) SetProductCategory(v int64) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *RawEstimateData) SetProductId(v int64) {
	if x != nil {
		x.ProductId = v
	}
}

func (x *RawEstimateData) SetBusinessId(v int64) {
	if x != nil {
		x.BusinessId = v
	}
}

func (x *RawEstimateData) SetRequireLevel(v string) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *RawEstimateData) SetComboType(v int64) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *RawEstimateData) SetLevelType(v int32) {
	if x != nil {
		x.LevelType = v
	}
}

func (x *RawEstimateData) SetCarpoolType(v int64) {
	if x != nil {
		x.CarpoolType = v
	}
}

func (x *RawEstimateData) SetCountPriceType(v int32) {
	if x != nil {
		x.CountPriceType = v
	}
}

func (x *RawEstimateData) SetIsSpecialPrice(v int32) {
	if x != nil {
		x.IsSpecialPrice = v
	}
}

func (x *RawEstimateData) SetSpaciousCarAlliance(v int32) {
	if x != nil {
		x.SpaciousCarAlliance = v
	}
}

func (x *RawEstimateData) SetComboId(v int64) {
	if x != nil {
		x.ComboId = v
	}
}

func (x *RawEstimateData) SetRouteType(v int64) {
	if x != nil {
		x.RouteType = v
	}
}

func (x *RawEstimateData) SetCarpoolPriceType(v int32) {
	if x != nil {
		x.CarpoolPriceType = v
	}
}

func (x *RawEstimateData) SetIsDualCarpoolPrice(v bool) {
	if x != nil {
		x.IsDualCarpoolPrice = v
	}
}

func (x *RawEstimateData) SetEstimateFee(v float64) {
	if x != nil {
		x.EstimateFee = v
	}
}

func (x *RawEstimateData) SetDynamicTotalFee(v float64) {
	if x != nil {
		x.DynamicTotalFee = v
	}
}

func (x *RawEstimateData) SetCapPrice(v float64) {
	if x != nil {
		x.CapPrice = v
	}
}

func (x *RawEstimateData) SetBasicTotalFee(v float64) {
	if x != nil {
		x.BasicTotalFee = v
	}
}

func (x *RawEstimateData) SetDynamicDiffPrice(v float64) {
	if x != nil {
		x.DynamicDiffPrice = v
	}
}

func (x *RawEstimateData) SetMultiPriceData(v []*MultiPriceData) {
	if x != nil {
		x.MultiPriceData = v
	}
}

func (x *RawEstimateData) SetExactEstimateFee(v float64) {
	if x != nil {
		x.ExactEstimateFee = v
	}
}

func (x *RawEstimateData) SetPreTotalFee(v float64) {
	if x != nil {
		x.PreTotalFee = v
	}
}

func (x *RawEstimateData) SetDynamicTimes(v float64) {
	if x != nil {
		x.DynamicTimes = v
	}
}

func (x *RawEstimateData) SetRedPacket(v float64) {
	if x != nil {
		x.RedPacket = v
	}
}

func (x *RawEstimateData) SetDiscountSet(v *DiscountSet) {
	if x != nil {
		x.DiscountSet = v
	}
}

func (x *RawEstimateData) SetRouteIdList(v []string) {
	if x != nil {
		x.RouteIdList = v
	}
}

func (x *RawEstimateData) SetDriverMetre(v int64) {
	if x != nil {
		x.DriverMetre = v
	}
}

func (x *RawEstimateData) SetPaymentId(v string) {
	if x != nil {
		x.PaymentId = v
	}
}

func (x *RawEstimateData) SetUndisplayInLayout(v bool) {
	if x != nil {
		x.UndisplayInLayout = v
	}
}

func (x *RawEstimateData) SetEtp(v int32) {
	if x != nil {
		x.Etp = v
	}
}

func (x *RawEstimateData) SetSubGroupId(v int32) {
	if x != nil {
		x.SubGroupId = v
	}
}

func (x *RawEstimateData) SetIsDefaultAuth(v int32) {
	if x != nil {
		x.IsDefaultAuth = v
	}
}

func (x *RawEstimateData) SetRecPos(v int32) {
	if x != nil {
		x.RecPos = &v
	}
}

func (p *RawEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RawEstimateData(%+v)", *p)
}

type NewFormEstimateData struct {
	EstimateId        string                      `json:"estimate_id" form:"estimate_id"` //标识
	ProductCategory   int64                       `json:"product_category" form:"product_category"`
	SceneInfo         *SceneInfo                  `json:"scene_info,omitempty" form:"scene_info"` //一些场景标识
	HitDynamicPrice   int32                       `json:"hit_dynamic_price" form:"hit_dynamic_price"`
	HitShowH5Type     int32                       `json:"hit_show_h5_type" form:"hit_show_h5_type"`
	IsTripcloud       bool                        `json:"is_tripcloud" form:"is_tripcloud"`
	LevelType         int32                       `json:"level_type" form:"level_type"`
	CarTitle          string                      `json:"car_title" form:"car_title"` //车型数据
	CarIcon           string                      `json:"car_icon" form:"car_icon"`
	TipsIcon          *string                     `json:"tips_icon,omitempty" form:"tips_icon"`         //车型提示icon
	SubTitleList      []*SubTitle                 `json:"sub_title_list" form:"sub_title_list"`         //车型提示icon
	CarTag            *CarTag                     `json:"car_tag,omitempty" form:"car_tag"`             //tp表单车型标签
	CarSubTitle       *string                     `json:"car_sub_title,omitempty" form:"car_sub_title"` //tp排队人数
	SubTitle          *GroupSubTitle              `json:"sub_title,omitempty" form:"sub_title"`         //单车型的标签
	EtpStr            *string                     `json:"etp_str,omitempty" form:"etp_str"`             //品类预期文案
	FeeAmount         string                      `json:"fee_amount" form:"fee_amount"`                 //价格信息
	FeeMsg            string                      `json:"fee_msg" form:"fee_msg"`
	FeeDescList       []*NewFormFeeDesc           `json:"fee_desc_list,omitempty" form:"fee_desc_list"`
	MultiPriceList    []*NewFormMultiPrice        `json:"multi_price_list,omitempty" form:"multi_price_list"`
	IsHidePrice       *int32                      `json:"is_hide_price,omitempty" form:"is_hide_price"`
	NeedPayFeeAmount  *float64                    `json:"need_pay_fee_amount,omitempty" form:"need_pay_fee_amount"` //券前价，目前只有司乘议价会用到
	UserPayInfo       *NewFormUserPayInfo         `json:"user_pay_info,omitempty" form:"user_pay_info"`             //支付信息
	ExtraMap          *NewFormExtraMap            `json:"extra_map,omitempty" form:"extra_map"`                     //发单参数
	IsSelected        int32                       `json:"is_selected" form:"is_selected"`                           //勾选状态
	DepartTag         *string                     `json:"depart_tag,omitempty" form:"depart_tag"`                   //勾选状态
	SubIntroIcon      *string                     `json:"sub_intro_icon,omitempty" form:"sub_intro_icon"`           //出租车盒子选中车型前的icon
	CarpoolSeatList   []*NewFormCarpoolSeatOption `json:"carpool_seat_list,omitempty" form:"carpool_seat_list"`     //出租车盒子选中车型前的icon
	RouteIdList       []string                    `json:"route_id_list,omitempty" form:"route_id_list"`
	SideExtra         *SideExtra                  `json:"side_extra,omitempty" form:"side_extra"`
	ExtraInfo         map[string]string           `json:"extra_info,omitempty" form:"extra_info"`
	PNewOrderParams   map[string]string           `json:"p_new_order_params,omitempty" form:"p_new_order_params"`   //发单参数 预估级别
	MinFeeAmount      *string                     `json:"min_fee_amount,omitempty" form:"min_fee_amount"`           //特快range左区间
	FeeRangeTemplate  *string                     `json:"fee_range_template,omitempty" form:"fee_range_template"`   //特快range模版
	FeeDiffAmount     *string                     `json:"fee_diff_amount,omitempty" form:"fee_diff_amount"`         //价格差
	FeeDiffMsg        *string                     `json:"fee_diff_msg,omitempty" form:"fee_diff_msg"`               //价格差描述
	FeeDiffDescList   []*NewFormFeeDesc           `json:"fee_diff_desc_list,omitempty" form:"fee_diff_desc_list"`   //价格差描述列表
	CarpoolSeatModule *NewCarpoolSeatModule       `json:"carpool_seat_module,omitempty" form:"carpool_seat_module"` //座位组件
	BargainRangePopup *BargainRangePopup          `json:"bargain_range_popup,omitempty" form:"bargain_range_popup"` //座位组件
	FeeMsgPrefixIcon  *string                     `json:"fee_msg_prefix_icon,omitempty" form:"fee_msg_prefix_icon"` //费用描述前置icon
}

func (x *NewFormEstimateData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *NewFormEstimateData) GetProductCategory() (r int64) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *NewFormEstimateData) GetSceneInfo() (r *SceneInfo) {
	if x != nil {
		return x.SceneInfo
	}
	return r
}

func (x *NewFormEstimateData) GetHitDynamicPrice() (r int32) {
	if x != nil {
		return x.HitDynamicPrice
	}
	return r
}

func (x *NewFormEstimateData) GetHitShowH5Type() (r int32) {
	if x != nil {
		return x.HitShowH5Type
	}
	return r
}

func (x *NewFormEstimateData) GetIsTripcloud() (r bool) {
	if x != nil {
		return x.IsTripcloud
	}
	return r
}

func (x *NewFormEstimateData) GetLevelType() (r int32) {
	if x != nil {
		return x.LevelType
	}
	return r
}

func (x *NewFormEstimateData) GetCarTitle() (r string) {
	if x != nil {
		return x.CarTitle
	}
	return r
}

func (x *NewFormEstimateData) GetCarIcon() (r string) {
	if x != nil {
		return x.CarIcon
	}
	return r
}

func (x *NewFormEstimateData) GetTipsIcon() (r string) {
	if x != nil && x.TipsIcon != nil {
		return *x.TipsIcon
	}
	return r
}

func (x *NewFormEstimateData) GetSubTitleList() (r []*SubTitle) {
	if x != nil {
		return x.SubTitleList
	}
	return r
}

func (x *NewFormEstimateData) GetCarTag() (r *CarTag) {
	if x != nil {
		return x.CarTag
	}
	return r
}

func (x *NewFormEstimateData) GetCarSubTitle() (r string) {
	if x != nil && x.CarSubTitle != nil {
		return *x.CarSubTitle
	}
	return r
}

func (x *NewFormEstimateData) GetSubTitle() (r *GroupSubTitle) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *NewFormEstimateData) GetEtpStr() (r string) {
	if x != nil && x.EtpStr != nil {
		return *x.EtpStr
	}
	return r
}

func (x *NewFormEstimateData) GetFeeAmount() (r string) {
	if x != nil {
		return x.FeeAmount
	}
	return r
}

func (x *NewFormEstimateData) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *NewFormEstimateData) GetFeeDescList() (r []*NewFormFeeDesc) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *NewFormEstimateData) GetMultiPriceList() (r []*NewFormMultiPrice) {
	if x != nil {
		return x.MultiPriceList
	}
	return r
}

func (x *NewFormEstimateData) GetIsHidePrice() (r int32) {
	if x != nil && x.IsHidePrice != nil {
		return *x.IsHidePrice
	}
	return r
}

func (x *NewFormEstimateData) GetNeedPayFeeAmount() (r float64) {
	if x != nil && x.NeedPayFeeAmount != nil {
		return *x.NeedPayFeeAmount
	}
	return r
}

func (x *NewFormEstimateData) GetUserPayInfo() (r *NewFormUserPayInfo) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *NewFormEstimateData) GetExtraMap() (r *NewFormExtraMap) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *NewFormEstimateData) GetIsSelected() (r int32) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *NewFormEstimateData) GetDepartTag() (r string) {
	if x != nil && x.DepartTag != nil {
		return *x.DepartTag
	}
	return r
}

func (x *NewFormEstimateData) GetSubIntroIcon() (r string) {
	if x != nil && x.SubIntroIcon != nil {
		return *x.SubIntroIcon
	}
	return r
}

func (x *NewFormEstimateData) GetCarpoolSeatList() (r []*NewFormCarpoolSeatOption) {
	if x != nil {
		return x.CarpoolSeatList
	}
	return r
}

func (x *NewFormEstimateData) GetRouteIdList() (r []string) {
	if x != nil {
		return x.RouteIdList
	}
	return r
}

func (x *NewFormEstimateData) GetSideExtra() (r *SideExtra) {
	if x != nil {
		return x.SideExtra
	}
	return r
}

func (x *NewFormEstimateData) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *NewFormEstimateData) GetPNewOrderParams() (r map[string]string) {
	if x != nil {
		return x.PNewOrderParams
	}
	return r
}

func (x *NewFormEstimateData) GetMinFeeAmount() (r string) {
	if x != nil && x.MinFeeAmount != nil {
		return *x.MinFeeAmount
	}
	return r
}

func (x *NewFormEstimateData) GetFeeRangeTemplate() (r string) {
	if x != nil && x.FeeRangeTemplate != nil {
		return *x.FeeRangeTemplate
	}
	return r
}

func (x *NewFormEstimateData) GetFeeDiffAmount() (r string) {
	if x != nil && x.FeeDiffAmount != nil {
		return *x.FeeDiffAmount
	}
	return r
}

func (x *NewFormEstimateData) GetFeeDiffMsg() (r string) {
	if x != nil && x.FeeDiffMsg != nil {
		return *x.FeeDiffMsg
	}
	return r
}

func (x *NewFormEstimateData) GetFeeDiffDescList() (r []*NewFormFeeDesc) {
	if x != nil {
		return x.FeeDiffDescList
	}
	return r
}

func (x *NewFormEstimateData) GetCarpoolSeatModule() (r *NewCarpoolSeatModule) {
	if x != nil {
		return x.CarpoolSeatModule
	}
	return r
}

func (x *NewFormEstimateData) GetBargainRangePopup() (r *BargainRangePopup) {
	if x != nil {
		return x.BargainRangePopup
	}
	return r
}

func (x *NewFormEstimateData) GetFeeMsgPrefixIcon() (r string) {
	if x != nil && x.FeeMsgPrefixIcon != nil {
		return *x.FeeMsgPrefixIcon
	}
	return r
}

func (x *NewFormEstimateData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *NewFormEstimateData) SetProductCategory(v int64) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *NewFormEstimateData) SetSceneInfo(v *SceneInfo) {
	if x != nil {
		x.SceneInfo = v
	}
}

func (x *NewFormEstimateData) SetHitDynamicPrice(v int32) {
	if x != nil {
		x.HitDynamicPrice = v
	}
}

func (x *NewFormEstimateData) SetHitShowH5Type(v int32) {
	if x != nil {
		x.HitShowH5Type = v
	}
}

func (x *NewFormEstimateData) SetIsTripcloud(v bool) {
	if x != nil {
		x.IsTripcloud = v
	}
}

func (x *NewFormEstimateData) SetLevelType(v int32) {
	if x != nil {
		x.LevelType = v
	}
}

func (x *NewFormEstimateData) SetCarTitle(v string) {
	if x != nil {
		x.CarTitle = v
	}
}

func (x *NewFormEstimateData) SetCarIcon(v string) {
	if x != nil {
		x.CarIcon = v
	}
}

func (x *NewFormEstimateData) SetTipsIcon(v string) {
	if x != nil {
		x.TipsIcon = &v
	}
}

func (x *NewFormEstimateData) SetSubTitleList(v []*SubTitle) {
	if x != nil {
		x.SubTitleList = v
	}
}

func (x *NewFormEstimateData) SetCarTag(v *CarTag) {
	if x != nil {
		x.CarTag = v
	}
}

func (x *NewFormEstimateData) SetCarSubTitle(v string) {
	if x != nil {
		x.CarSubTitle = &v
	}
}

func (x *NewFormEstimateData) SetSubTitle(v *GroupSubTitle) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *NewFormEstimateData) SetEtpStr(v string) {
	if x != nil {
		x.EtpStr = &v
	}
}

func (x *NewFormEstimateData) SetFeeAmount(v string) {
	if x != nil {
		x.FeeAmount = v
	}
}

func (x *NewFormEstimateData) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *NewFormEstimateData) SetFeeDescList(v []*NewFormFeeDesc) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *NewFormEstimateData) SetMultiPriceList(v []*NewFormMultiPrice) {
	if x != nil {
		x.MultiPriceList = v
	}
}

func (x *NewFormEstimateData) SetIsHidePrice(v int32) {
	if x != nil {
		x.IsHidePrice = &v
	}
}

func (x *NewFormEstimateData) SetNeedPayFeeAmount(v float64) {
	if x != nil {
		x.NeedPayFeeAmount = &v
	}
}

func (x *NewFormEstimateData) SetUserPayInfo(v *NewFormUserPayInfo) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *NewFormEstimateData) SetExtraMap(v *NewFormExtraMap) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *NewFormEstimateData) SetIsSelected(v int32) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *NewFormEstimateData) SetDepartTag(v string) {
	if x != nil {
		x.DepartTag = &v
	}
}

func (x *NewFormEstimateData) SetSubIntroIcon(v string) {
	if x != nil {
		x.SubIntroIcon = &v
	}
}

func (x *NewFormEstimateData) SetCarpoolSeatList(v []*NewFormCarpoolSeatOption) {
	if x != nil {
		x.CarpoolSeatList = v
	}
}

func (x *NewFormEstimateData) SetRouteIdList(v []string) {
	if x != nil {
		x.RouteIdList = v
	}
}

func (x *NewFormEstimateData) SetSideExtra(v *SideExtra) {
	if x != nil {
		x.SideExtra = v
	}
}

func (x *NewFormEstimateData) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (x *NewFormEstimateData) SetPNewOrderParams(v map[string]string) {
	if x != nil {
		x.PNewOrderParams = v
	}
}

func (x *NewFormEstimateData) SetMinFeeAmount(v string) {
	if x != nil {
		x.MinFeeAmount = &v
	}
}

func (x *NewFormEstimateData) SetFeeRangeTemplate(v string) {
	if x != nil {
		x.FeeRangeTemplate = &v
	}
}

func (x *NewFormEstimateData) SetFeeDiffAmount(v string) {
	if x != nil {
		x.FeeDiffAmount = &v
	}
}

func (x *NewFormEstimateData) SetFeeDiffMsg(v string) {
	if x != nil {
		x.FeeDiffMsg = &v
	}
}

func (x *NewFormEstimateData) SetFeeDiffDescList(v []*NewFormFeeDesc) {
	if x != nil {
		x.FeeDiffDescList = v
	}
}

func (x *NewFormEstimateData) SetCarpoolSeatModule(v *NewCarpoolSeatModule) {
	if x != nil {
		x.CarpoolSeatModule = v
	}
}

func (x *NewFormEstimateData) SetBargainRangePopup(v *BargainRangePopup) {
	if x != nil {
		x.BargainRangePopup = v
	}
}

func (x *NewFormEstimateData) SetFeeMsgPrefixIcon(v string) {
	if x != nil {
		x.FeeMsgPrefixIcon = &v
	}
}

func (p *NewFormEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NewFormEstimateData(%+v)", *p)
}

type BargainRangePopup struct {
	PriceLimitUpper      float64  `json:"price_limit_upper" form:"price_limit_upper"`                     //价格滑动条上界
	PriceLimitLower      float64  `json:"price_limit_lower" form:"price_limit_lower"`                     //价格滑动条下界
	RecommendPriceUpper  float64  `json:"recommend_price_upper" form:"recommend_price_upper"`             //推荐价上界
	RecommendPriceLower  float64  `json:"recommend_price_lower" form:"recommend_price_lower"`             //推荐价下界
	WaitReplyPriceUpper  *float64 `json:"wait_reply_price_upper,omitempty" form:"wait_reply_price_upper"` //等应答最高价
	FastCarEstimateFee   float64  `json:"fast_car_estimate_fee" form:"fast_car_estimate_fee"`             //快车预估实付价格
	SpFastCarEstimateFee float64  `json:"sp_fast_car_estimate_fee" form:"sp_fast_car_estimate_fee"`       //特惠快车预估实付价格
	Title                string   `json:"title" form:"title"`                                             //弹窗标题
	SubTitle             string   `json:"sub_title" form:"sub_title"`                                     //弹窗副标题
	PriceLimitUpperText  string   `json:"price_limit_upper_text" form:"price_limit_upper_text"`           //价格上界锚点文案
	LowPriceBubbleText   string   `json:"low_price_bubble_text" form:"low_price_bubble_text"`             //价低文案提示
	HighPriceBubbleText  string   `json:"high_price_bubble_text" form:"high_price_bubble_text"`           //加价文案提示
	BtnText              string   `json:"btn_text" form:"btn_text"`                                       //确认出价文案
	UnderlineColor       string   `json:"underline_color" form:"underline_color"`                         //表单价格下划线颜色
	BackgroundImg        string   `json:"background_img" form:"background_img"`                           //弹窗背景图
	RedPacketTitle       string   `json:"red_packet_title" form:"red_packet_title"`                       //弹窗背景图
}

func (x *BargainRangePopup) GetPriceLimitUpper() (r float64) {
	if x != nil {
		return x.PriceLimitUpper
	}
	return r
}

func (x *BargainRangePopup) GetPriceLimitLower() (r float64) {
	if x != nil {
		return x.PriceLimitLower
	}
	return r
}

func (x *BargainRangePopup) GetRecommendPriceUpper() (r float64) {
	if x != nil {
		return x.RecommendPriceUpper
	}
	return r
}

func (x *BargainRangePopup) GetRecommendPriceLower() (r float64) {
	if x != nil {
		return x.RecommendPriceLower
	}
	return r
}

func (x *BargainRangePopup) GetWaitReplyPriceUpper() (r float64) {
	if x != nil && x.WaitReplyPriceUpper != nil {
		return *x.WaitReplyPriceUpper
	}
	return r
}

func (x *BargainRangePopup) GetFastCarEstimateFee() (r float64) {
	if x != nil {
		return x.FastCarEstimateFee
	}
	return r
}

func (x *BargainRangePopup) GetSpFastCarEstimateFee() (r float64) {
	if x != nil {
		return x.SpFastCarEstimateFee
	}
	return r
}

func (x *BargainRangePopup) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *BargainRangePopup) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *BargainRangePopup) GetPriceLimitUpperText() (r string) {
	if x != nil {
		return x.PriceLimitUpperText
	}
	return r
}

func (x *BargainRangePopup) GetLowPriceBubbleText() (r string) {
	if x != nil {
		return x.LowPriceBubbleText
	}
	return r
}

func (x *BargainRangePopup) GetHighPriceBubbleText() (r string) {
	if x != nil {
		return x.HighPriceBubbleText
	}
	return r
}

func (x *BargainRangePopup) GetBtnText() (r string) {
	if x != nil {
		return x.BtnText
	}
	return r
}

func (x *BargainRangePopup) GetUnderlineColor() (r string) {
	if x != nil {
		return x.UnderlineColor
	}
	return r
}

func (x *BargainRangePopup) GetBackgroundImg() (r string) {
	if x != nil {
		return x.BackgroundImg
	}
	return r
}

func (x *BargainRangePopup) GetRedPacketTitle() (r string) {
	if x != nil {
		return x.RedPacketTitle
	}
	return r
}

func (x *BargainRangePopup) SetPriceLimitUpper(v float64) {
	if x != nil {
		x.PriceLimitUpper = v
	}
}

func (x *BargainRangePopup) SetPriceLimitLower(v float64) {
	if x != nil {
		x.PriceLimitLower = v
	}
}

func (x *BargainRangePopup) SetRecommendPriceUpper(v float64) {
	if x != nil {
		x.RecommendPriceUpper = v
	}
}

func (x *BargainRangePopup) SetRecommendPriceLower(v float64) {
	if x != nil {
		x.RecommendPriceLower = v
	}
}

func (x *BargainRangePopup) SetWaitReplyPriceUpper(v float64) {
	if x != nil {
		x.WaitReplyPriceUpper = &v
	}
}

func (x *BargainRangePopup) SetFastCarEstimateFee(v float64) {
	if x != nil {
		x.FastCarEstimateFee = v
	}
}

func (x *BargainRangePopup) SetSpFastCarEstimateFee(v float64) {
	if x != nil {
		x.SpFastCarEstimateFee = v
	}
}

func (x *BargainRangePopup) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *BargainRangePopup) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *BargainRangePopup) SetPriceLimitUpperText(v string) {
	if x != nil {
		x.PriceLimitUpperText = v
	}
}

func (x *BargainRangePopup) SetLowPriceBubbleText(v string) {
	if x != nil {
		x.LowPriceBubbleText = v
	}
}

func (x *BargainRangePopup) SetHighPriceBubbleText(v string) {
	if x != nil {
		x.HighPriceBubbleText = v
	}
}

func (x *BargainRangePopup) SetBtnText(v string) {
	if x != nil {
		x.BtnText = v
	}
}

func (x *BargainRangePopup) SetUnderlineColor(v string) {
	if x != nil {
		x.UnderlineColor = v
	}
}

func (x *BargainRangePopup) SetBackgroundImg(v string) {
	if x != nil {
		x.BackgroundImg = v
	}
}

func (x *BargainRangePopup) SetRedPacketTitle(v string) {
	if x != nil {
		x.RedPacketTitle = v
	}
}

func (p *BargainRangePopup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BargainRangePopup(%+v)", *p)
}

type SideExtra struct {
	RecommendBubble string         `json:"recommend_bubble" form:"recommend_bubble"`         //推荐气泡文案
	BottomRule      *BottomRule    `json:"bottom_rule,omitempty" form:"bottom_rule"`         //三方盒子出底部协议导流
	RightSubTitle   *GroupSubTitle `json:"right_sub_title,omitempty" form:"right_sub_title"` //车型右侧标签
	SubTitleList    []*SubTitle    `json:"sub_title_list,omitempty" form:"sub_title_list"`   //车型标签
}

func (x *SideExtra) GetRecommendBubble() (r string) {
	if x != nil {
		return x.RecommendBubble
	}
	return r
}

func (x *SideExtra) GetBottomRule() (r *BottomRule) {
	if x != nil {
		return x.BottomRule
	}
	return r
}

func (x *SideExtra) GetRightSubTitle() (r *GroupSubTitle) {
	if x != nil {
		return x.RightSubTitle
	}
	return r
}

func (x *SideExtra) GetSubTitleList() (r []*SubTitle) {
	if x != nil {
		return x.SubTitleList
	}
	return r
}

func (x *SideExtra) SetRecommendBubble(v string) {
	if x != nil {
		x.RecommendBubble = v
	}
}

func (x *SideExtra) SetBottomRule(v *BottomRule) {
	if x != nil {
		x.BottomRule = v
	}
}

func (x *SideExtra) SetRightSubTitle(v *GroupSubTitle) {
	if x != nil {
		x.RightSubTitle = v
	}
}

func (x *SideExtra) SetSubTitleList(v []*SubTitle) {
	if x != nil {
		x.SubTitleList = v
	}
}

func (p *SideExtra) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SideExtra(%+v)", *p)
}

type BottomRule struct {
	Event        string         `json:"event" form:"event"`                   //活动事件标识，比如 special_rule
	EventSubList []int32        `json:"event_sub_list" form:"event_sub_list"` //规则列表，之前的rule_type
	Weight       int32          `json:"weight" form:"weight"`                 //权重
	Content      *BottomContent `json:"content,omitempty" form:"content"`     //具体内容
}

func (x *BottomRule) GetEvent() (r string) {
	if x != nil {
		return x.Event
	}
	return r
}

func (x *BottomRule) GetEventSubList() (r []int32) {
	if x != nil {
		return x.EventSubList
	}
	return r
}

func (x *BottomRule) GetWeight() (r int32) {
	if x != nil {
		return x.Weight
	}
	return r
}

func (x *BottomRule) GetContent() (r *BottomContent) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *BottomRule) SetEvent(v string) {
	if x != nil {
		x.Event = v
	}
}

func (x *BottomRule) SetEventSubList(v []int32) {
	if x != nil {
		x.EventSubList = v
	}
}

func (x *BottomRule) SetWeight(v int32) {
	if x != nil {
		x.Weight = v
	}
}

func (x *BottomRule) SetContent(v *BottomContent) {
	if x != nil {
		x.Content = v
	}
}

func (p *BottomRule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BottomRule(%+v)", *p)
}

type BottomContent struct {
	Text                string            `json:"text" form:"text"`                                 //标题 = 主标题 + 副标题
	TextColor           string            `json:"text_color" form:"text_color"`                     //标题颜色
	TextHighlightColor  string            `json:"text_highlight_color" form:"text_highlight_color"` //高亮颜色
	LinkUrl             string            `json:"link_url" form:"link_url"`                         //跳转链接
	BackgroundGradients []string          `json:"background_gradients" form:"background_gradients"` //背景色：无值背景透明，有1个纯色，多个渐变
	LinkQuerys          map[string]string `json:"link_querys" form:"link_querys"`                   //跳转链接参数
}

func (x *BottomContent) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *BottomContent) GetTextColor() (r string) {
	if x != nil {
		return x.TextColor
	}
	return r
}

func (x *BottomContent) GetTextHighlightColor() (r string) {
	if x != nil {
		return x.TextHighlightColor
	}
	return r
}

func (x *BottomContent) GetLinkUrl() (r string) {
	if x != nil {
		return x.LinkUrl
	}
	return r
}

func (x *BottomContent) GetBackgroundGradients() (r []string) {
	if x != nil {
		return x.BackgroundGradients
	}
	return r
}

func (x *BottomContent) GetLinkQuerys() (r map[string]string) {
	if x != nil {
		return x.LinkQuerys
	}
	return r
}

func (x *BottomContent) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *BottomContent) SetTextColor(v string) {
	if x != nil {
		x.TextColor = v
	}
}

func (x *BottomContent) SetTextHighlightColor(v string) {
	if x != nil {
		x.TextHighlightColor = v
	}
}

func (x *BottomContent) SetLinkUrl(v string) {
	if x != nil {
		x.LinkUrl = v
	}
}

func (x *BottomContent) SetBackgroundGradients(v []string) {
	if x != nil {
		x.BackgroundGradients = v
	}
}

func (x *BottomContent) SetLinkQuerys(v map[string]string) {
	if x != nil {
		x.LinkQuerys = v
	}
}

func (p *BottomContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BottomContent(%+v)", *p)
}

// 发单参数
type NewFormExtraMap struct {
	LevelType     int32  `json:"level_type" form:"level_type"`
	BusinessId    int64  `json:"business_id" form:"business_id"`
	ComboType     int64  `json:"combo_type" form:"combo_type"`
	RequireLevel  string `json:"require_level" form:"require_level"`
	IsDefaultAuth int32  `json:"is_default_auth" form:"is_default_auth"`
	SubGroupId    int32  `json:"sub_group_id" form:"sub_group_id"`
	CarpoolType   int32  `json:"carpool_type" form:"carpool_type"`
}

func (x *NewFormExtraMap) GetLevelType() (r int32) {
	if x != nil {
		return x.LevelType
	}
	return r
}

func (x *NewFormExtraMap) GetBusinessId() (r int64) {
	if x != nil {
		return x.BusinessId
	}
	return r
}

func (x *NewFormExtraMap) GetComboType() (r int64) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *NewFormExtraMap) GetRequireLevel() (r string) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *NewFormExtraMap) GetIsDefaultAuth() (r int32) {
	if x != nil {
		return x.IsDefaultAuth
	}
	return r
}

func (x *NewFormExtraMap) GetSubGroupId() (r int32) {
	if x != nil {
		return x.SubGroupId
	}
	return r
}

func (x *NewFormExtraMap) GetCarpoolType() (r int32) {
	if x != nil {
		return x.CarpoolType
	}
	return r
}

func (x *NewFormExtraMap) SetLevelType(v int32) {
	if x != nil {
		x.LevelType = v
	}
}

func (x *NewFormExtraMap) SetBusinessId(v int64) {
	if x != nil {
		x.BusinessId = v
	}
}

func (x *NewFormExtraMap) SetComboType(v int64) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *NewFormExtraMap) SetRequireLevel(v string) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *NewFormExtraMap) SetIsDefaultAuth(v int32) {
	if x != nil {
		x.IsDefaultAuth = v
	}
}

func (x *NewFormExtraMap) SetSubGroupId(v int32) {
	if x != nil {
		x.SubGroupId = v
	}
}

func (x *NewFormExtraMap) SetCarpoolType(v int32) {
	if x != nil {
		x.CarpoolType = v
	}
}

func (p *NewFormExtraMap) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NewFormExtraMap(%+v)", *p)
}

type NewFormCarpoolSeatOption struct {
	Label    string `json:"label" form:"label"`
	Value    int32  `json:"value" form:"value"`
	Selected bool   `json:"selected" form:"selected"`
}

func (x *NewFormCarpoolSeatOption) GetLabel() (r string) {
	if x != nil {
		return x.Label
	}
	return r
}

func (x *NewFormCarpoolSeatOption) GetValue() (r int32) {
	if x != nil {
		return x.Value
	}
	return r
}

func (x *NewFormCarpoolSeatOption) GetSelected() (r bool) {
	if x != nil {
		return x.Selected
	}
	return r
}

func (x *NewFormCarpoolSeatOption) SetLabel(v string) {
	if x != nil {
		x.Label = v
	}
}

func (x *NewFormCarpoolSeatOption) SetValue(v int32) {
	if x != nil {
		x.Value = v
	}
}

func (x *NewFormCarpoolSeatOption) SetSelected(v bool) {
	if x != nil {
		x.Selected = v
	}
}

func (p *NewFormCarpoolSeatOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NewFormCarpoolSeatOption(%+v)", *p)
}

type NewFormUserPayInfo struct {
	PaymentId        string  `json:"payment_id" form:"payment_id"`
	BusinessConstSet string  `json:"business_const_set" form:"business_const_set"`   //??
	FeeMsgPrefix     *string `json:"fee_msg_prefix,omitempty" form:"fee_msg_prefix"` //??
}

func (x *NewFormUserPayInfo) GetPaymentId() (r string) {
	if x != nil {
		return x.PaymentId
	}
	return r
}

func (x *NewFormUserPayInfo) GetBusinessConstSet() (r string) {
	if x != nil {
		return x.BusinessConstSet
	}
	return r
}

func (x *NewFormUserPayInfo) GetFeeMsgPrefix() (r string) {
	if x != nil && x.FeeMsgPrefix != nil {
		return *x.FeeMsgPrefix
	}
	return r
}

func (x *NewFormUserPayInfo) SetPaymentId(v string) {
	if x != nil {
		x.PaymentId = v
	}
}

func (x *NewFormUserPayInfo) SetBusinessConstSet(v string) {
	if x != nil {
		x.BusinessConstSet = v
	}
}

func (x *NewFormUserPayInfo) SetFeeMsgPrefix(v string) {
	if x != nil {
		x.FeeMsgPrefix = &v
	}
}

func (p *NewFormUserPayInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NewFormUserPayInfo(%+v)", *p)
}

type SceneInfo struct {
	BusinessId         int64   `json:"business_id" form:"business_id"`                     //"业务ID"，由网关统一分配
	RequireLevel       string  `json:"require_level" form:"require_level"`                 //ProductID基础上分了子类，例如：豪华车已有require_level列表： 1000 ：豪华型 ， 1400：宝马 ， 1401：奥迪 ， 1500：奔驰， 1600：英菲尼迪
	ComboType          int64   `json:"combo_type" form:"combo_type"`                       //combo_type 枚举值说明： 0 无场景 1 包车 2 接机 3 送机 4 普通拼车场景（站点拼车场景） 11 mis把combo_type=11的计价规则分配给了企业专车一口价 12 有待补充 302 跨城拼车场景 303 区域一口价 306 顺路计价 309 快车拼车（多因素）一口价 310 保险查勘 314 线路一口价
	LevelType          int32   `json:"level_type" form:"level_type"`                       //为了区分车型
	IsDualCarpoolPrice bool    `json:"is_dual_carpool_price" form:"is_dual_carpool_price"` //是否双边拼车
	CarpoolPriceType   int32   `json:"carpool_price_type" form:"carpool_price_type"`       //拼车类型
	CarpoolType        int64   `json:"carpool_type" form:"carpool_type"`                   //拼车类型
	CountPriceType     int32   `json:"count_price_type" form:"count_price_type"`           //拼车类型
	DynamicTimes       float64 `json:"dynamic_times" form:"dynamic_times"`                 //增加的倍数，实际倍数为 1 + DynamicTimes
	DynamicTotalFee    float64 `json:"dynamic_total_fee" form:"dynamic_total_fee"`         //所有费用项合计，券前价
}

func (x *SceneInfo) GetBusinessId() (r int64) {
	if x != nil {
		return x.BusinessId
	}
	return r
}

func (x *SceneInfo) GetRequireLevel() (r string) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *SceneInfo) GetComboType() (r int64) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *SceneInfo) GetLevelType() (r int32) {
	if x != nil {
		return x.LevelType
	}
	return r
}

func (x *SceneInfo) GetIsDualCarpoolPrice() (r bool) {
	if x != nil {
		return x.IsDualCarpoolPrice
	}
	return r
}

func (x *SceneInfo) GetCarpoolPriceType() (r int32) {
	if x != nil {
		return x.CarpoolPriceType
	}
	return r
}

func (x *SceneInfo) GetCarpoolType() (r int64) {
	if x != nil {
		return x.CarpoolType
	}
	return r
}

func (x *SceneInfo) GetCountPriceType() (r int32) {
	if x != nil {
		return x.CountPriceType
	}
	return r
}

func (x *SceneInfo) GetDynamicTimes() (r float64) {
	if x != nil {
		return x.DynamicTimes
	}
	return r
}

func (x *SceneInfo) GetDynamicTotalFee() (r float64) {
	if x != nil {
		return x.DynamicTotalFee
	}
	return r
}

func (x *SceneInfo) SetBusinessId(v int64) {
	if x != nil {
		x.BusinessId = v
	}
}

func (x *SceneInfo) SetRequireLevel(v string) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *SceneInfo) SetComboType(v int64) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *SceneInfo) SetLevelType(v int32) {
	if x != nil {
		x.LevelType = v
	}
}

func (x *SceneInfo) SetIsDualCarpoolPrice(v bool) {
	if x != nil {
		x.IsDualCarpoolPrice = v
	}
}

func (x *SceneInfo) SetCarpoolPriceType(v int32) {
	if x != nil {
		x.CarpoolPriceType = v
	}
}

func (x *SceneInfo) SetCarpoolType(v int64) {
	if x != nil {
		x.CarpoolType = v
	}
}

func (x *SceneInfo) SetCountPriceType(v int32) {
	if x != nil {
		x.CountPriceType = v
	}
}

func (x *SceneInfo) SetDynamicTimes(v float64) {
	if x != nil {
		x.DynamicTimes = v
	}
}

func (x *SceneInfo) SetDynamicTotalFee(v float64) {
	if x != nil {
		x.DynamicTotalFee = v
	}
}

func (p *SceneInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SceneInfo(%+v)", *p)
}

type CarTag struct {
	Content           string `json:"content" form:"content"`                         //车型标签文案
	FontSelectColor   string `json:"font_select_color" form:"font_select_color"`     //字体选中颜色
	FontColor         string `json:"font_color" form:"font_color"`                   //字体未选中颜色
	BorderColor       string `json:"border_color" form:"border_color"`               //边框未选中颜色
	BorderSelectColor string `json:"border_select_color" form:"border_select_color"` //边框选中颜色
}

func (x *CarTag) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *CarTag) GetFontSelectColor() (r string) {
	if x != nil {
		return x.FontSelectColor
	}
	return r
}

func (x *CarTag) GetFontColor() (r string) {
	if x != nil {
		return x.FontColor
	}
	return r
}

func (x *CarTag) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *CarTag) GetBorderSelectColor() (r string) {
	if x != nil {
		return x.BorderSelectColor
	}
	return r
}

func (x *CarTag) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (x *CarTag) SetFontSelectColor(v string) {
	if x != nil {
		x.FontSelectColor = v
	}
}

func (x *CarTag) SetFontColor(v string) {
	if x != nil {
		x.FontColor = v
	}
}

func (x *CarTag) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *CarTag) SetBorderSelectColor(v string) {
	if x != nil {
		x.BorderSelectColor = v
	}
}

func (p *CarTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarTag(%+v)", *p)
}

type MultiPriceData struct {
	EstimateFee     float64        `json:"estimate_fee" form:"estimate_fee"`
	DynamicTotalFee float64        `json:"dynamic_total_fee" form:"dynamic_total_fee"`
	CapPrice        float64        `json:"cap_price" form:"cap_price"`
	DiscountSet     *DiscountSet   `json:"discount_set,omitempty" form:"discount_set"`
	Option          *CarpoolOption `json:"option,omitempty" form:"option"`
}

func (x *MultiPriceData) GetEstimateFee() (r float64) {
	if x != nil {
		return x.EstimateFee
	}
	return r
}

func (x *MultiPriceData) GetDynamicTotalFee() (r float64) {
	if x != nil {
		return x.DynamicTotalFee
	}
	return r
}

func (x *MultiPriceData) GetCapPrice() (r float64) {
	if x != nil {
		return x.CapPrice
	}
	return r
}

func (x *MultiPriceData) GetDiscountSet() (r *DiscountSet) {
	if x != nil {
		return x.DiscountSet
	}
	return r
}

func (x *MultiPriceData) GetOption() (r *CarpoolOption) {
	if x != nil {
		return x.Option
	}
	return r
}

func (x *MultiPriceData) SetEstimateFee(v float64) {
	if x != nil {
		x.EstimateFee = v
	}
}

func (x *MultiPriceData) SetDynamicTotalFee(v float64) {
	if x != nil {
		x.DynamicTotalFee = v
	}
}

func (x *MultiPriceData) SetCapPrice(v float64) {
	if x != nil {
		x.CapPrice = v
	}
}

func (x *MultiPriceData) SetDiscountSet(v *DiscountSet) {
	if x != nil {
		x.DiscountSet = v
	}
}

func (x *MultiPriceData) SetOption(v *CarpoolOption) {
	if x != nil {
		x.Option = v
	}
}

func (p *MultiPriceData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MultiPriceData(%+v)", *p)
}

type CarpoolOption struct {
	IsCarpoolSuccess bool  `json:"is_carpool_success" form:"is_carpool_success"`
	PoolNum          int32 `json:"pool_num" form:"pool_num"`
	SeatNum          int32 `json:"seat_num" form:"seat_num"`
}

func (x *CarpoolOption) GetIsCarpoolSuccess() (r bool) {
	if x != nil {
		return x.IsCarpoolSuccess
	}
	return r
}

func (x *CarpoolOption) GetPoolNum() (r int32) {
	if x != nil {
		return x.PoolNum
	}
	return r
}

func (x *CarpoolOption) GetSeatNum() (r int32) {
	if x != nil {
		return x.SeatNum
	}
	return r
}

func (x *CarpoolOption) SetIsCarpoolSuccess(v bool) {
	if x != nil {
		x.IsCarpoolSuccess = v
	}
}

func (x *CarpoolOption) SetPoolNum(v int32) {
	if x != nil {
		x.PoolNum = v
	}
}

func (x *CarpoolOption) SetSeatNum(v int32) {
	if x != nil {
		x.SeatNum = v
	}
}

func (p *CarpoolOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolOption(%+v)", *p)
}

type DiscountSet struct {
	Coupon             *EstimateCouponInfo        `json:"coupon,omitempty" form:"coupon"`
	SpsLikeWaiteReward *EstimateSpsLikeWaitReward `json:"sps_like_waite_reward,omitempty" form:"sps_like_waite_reward"`
}

func (x *DiscountSet) GetCoupon() (r *EstimateCouponInfo) {
	if x != nil {
		return x.Coupon
	}
	return r
}

func (x *DiscountSet) GetSpsLikeWaiteReward() (r *EstimateSpsLikeWaitReward) {
	if x != nil {
		return x.SpsLikeWaiteReward
	}
	return r
}

func (x *DiscountSet) SetCoupon(v *EstimateCouponInfo) {
	if x != nil {
		x.Coupon = v
	}
}

func (x *DiscountSet) SetSpsLikeWaiteReward(v *EstimateSpsLikeWaitReward) {
	if x != nil {
		x.SpsLikeWaiteReward = v
	}
}

func (p *DiscountSet) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DiscountSet(%+v)", *p)
}

type EstimateCouponInfo struct {
	BatchId    string `json:"batch_id" form:"batch_id"`
	CouponId   string `json:"coupon_id" form:"coupon_id"`
	Amount     int32  `json:"amount" form:"amount"`           //单位分
	CouponType string `json:"coupon_type" form:"coupon_type"` //单位分
	CustomTag  string `json:"custom_tag" form:"custom_tag"`
}

func (x *EstimateCouponInfo) GetBatchId() (r string) {
	if x != nil {
		return x.BatchId
	}
	return r
}

func (x *EstimateCouponInfo) GetCouponId() (r string) {
	if x != nil {
		return x.CouponId
	}
	return r
}

func (x *EstimateCouponInfo) GetAmount() (r int32) {
	if x != nil {
		return x.Amount
	}
	return r
}

func (x *EstimateCouponInfo) GetCouponType() (r string) {
	if x != nil {
		return x.CouponType
	}
	return r
}

func (x *EstimateCouponInfo) GetCustomTag() (r string) {
	if x != nil {
		return x.CustomTag
	}
	return r
}

func (x *EstimateCouponInfo) SetBatchId(v string) {
	if x != nil {
		x.BatchId = v
	}
}

func (x *EstimateCouponInfo) SetCouponId(v string) {
	if x != nil {
		x.CouponId = v
	}
}

func (x *EstimateCouponInfo) SetAmount(v int32) {
	if x != nil {
		x.Amount = v
	}
}

func (x *EstimateCouponInfo) SetCouponType(v string) {
	if x != nil {
		x.CouponType = v
	}
}

func (x *EstimateCouponInfo) SetCustomTag(v string) {
	if x != nil {
		x.CustomTag = v
	}
}

func (p *EstimateCouponInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EstimateCouponInfo(%+v)", *p)
}

type EstimateSpsLikeWaitReward struct {
	WaitTime int64 `json:"wait_time" form:"wait_time"`
	Amount   int64 `json:"amount" form:"amount"`
}

func (x *EstimateSpsLikeWaitReward) GetWaitTime() (r int64) {
	if x != nil {
		return x.WaitTime
	}
	return r
}

func (x *EstimateSpsLikeWaitReward) GetAmount() (r int64) {
	if x != nil {
		return x.Amount
	}
	return r
}

func (x *EstimateSpsLikeWaitReward) SetWaitTime(v int64) {
	if x != nil {
		x.WaitTime = v
	}
}

func (x *EstimateSpsLikeWaitReward) SetAmount(v int64) {
	if x != nil {
		x.Amount = v
	}
}

func (p *EstimateSpsLikeWaitReward) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EstimateSpsLikeWaitReward(%+v)", *p)
}

type AnycarEstimateCacheReq struct {
	Caller string `json:"caller" form:"caller"`
	Oid    string `json:"oid" form:"oid"` //低位订单号
}

func (x *AnycarEstimateCacheReq) GetCaller() (r string) {
	if x != nil {
		return x.Caller
	}
	return r
}

func (x *AnycarEstimateCacheReq) GetOid() (r string) {
	if x != nil {
		return x.Oid
	}
	return r
}

func (x *AnycarEstimateCacheReq) SetCaller(v string) {
	if x != nil {
		x.Caller = v
	}
}

func (x *AnycarEstimateCacheReq) SetOid(v string) {
	if x != nil {
		x.Oid = v
	}
}

func (p *AnycarEstimateCacheReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnycarEstimateCacheReq(%+v)", *p)
}

type AnycarEstimateCacheResp struct {
	Errno        int32                    `json:"errno" form:"errno"`                           //错误码
	Errmsg       string                   `json:"errmsg" form:"errmsg"`                         //错误信息
	EstimateInfo *NewFormEstimateResponse `json:"estimate_info,omitempty" form:"estimate_info"` //预估信息
}

func (x *AnycarEstimateCacheResp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *AnycarEstimateCacheResp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *AnycarEstimateCacheResp) GetEstimateInfo() (r *NewFormEstimateResponse) {
	if x != nil {
		return x.EstimateInfo
	}
	return r
}

func (x *AnycarEstimateCacheResp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *AnycarEstimateCacheResp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *AnycarEstimateCacheResp) SetEstimateInfo(v *NewFormEstimateResponse) {
	if x != nil {
		x.EstimateInfo = v
	}
}

func (p *AnycarEstimateCacheResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnycarEstimateCacheResp(%+v)", *p)
}

type AnycarEstimateDelCacheResp struct {
	Errno  int32  `json:"errno" form:"errno"`   //错误码
	Errmsg string `json:"errmsg" form:"errmsg"` //错误信息
}

func (x *AnycarEstimateDelCacheResp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *AnycarEstimateDelCacheResp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *AnycarEstimateDelCacheResp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *AnycarEstimateDelCacheResp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (p *AnycarEstimateDelCacheResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnycarEstimateDelCacheResp(%+v)", *p)
}
