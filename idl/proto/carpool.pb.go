// Code generated by http-gen compiler. DO NOT EDIT.
// source: carpool.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*CarpoolEstimateRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*TransData)(nil)))
	json.Pretouch(reflect.TypeOf((*Coupon)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolScenePrice)(nil)))
	json.Pretouch(reflect.TypeOf((*EtsTimeWindow)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolPriceData)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolPriceResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolEstimateResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*BackButton)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolEstimateResponseData)(nil)))
	json.Pretouch(reflect.TypeOf((*IntroMsgTag)(nil)))
	json.Pretouch(reflect.TypeOf((*PinchecheData)(nil)))
	json.Pretouch(reflect.TypeOf((*PinchecheDataV2)(nil)))
	json.Pretouch(reflect.TypeOf((*PinchecheEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolLowPriceSceneInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolLowPriceSceneItem)(nil)))
	json.Pretouch(reflect.TypeOf((*SimpleJumpButton)(nil)))
	json.Pretouch(reflect.TypeOf((*TabBlank)(nil)))
	json.Pretouch(reflect.TypeOf((*TagWithIconAndBorder)(nil)))
	json.Pretouch(reflect.TypeOf((*HyberTextButton)(nil)))
	json.Pretouch(reflect.TypeOf((*PriceWithSingleDesc)(nil)))
	json.Pretouch(reflect.TypeOf((*CheckBoxExtendForPincheche)(nil)))
	json.Pretouch(reflect.TypeOf((*CheckBoxExtendPinchecheDisplay)(nil)))
	json.Pretouch(reflect.TypeOf((*CheckBoxExtendForIntercity)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolSeatOption)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolSeatOptionModule)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolBookingModule)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolBookingModuleTimeSpan)(nil)))
	json.Pretouch(reflect.TypeOf((*PaymentOption)(nil)))
	json.Pretouch(reflect.TypeOf((*PaymentOptionModule)(nil)))
	json.Pretouch(reflect.TypeOf((*PinchecheNewOrderParam)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityFeeInfoItem)(nil)))
	json.Pretouch(reflect.TypeOf((*InterCityCarpoolSeatModule)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolLowPriceTimeRange)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolLowPriceBookingModule)(nil)))
	json.Pretouch(reflect.TypeOf((*PinchecheTimeSpan)(nil)))
	json.Pretouch(reflect.TypeOf((*HourTime)(nil)))
	json.Pretouch(reflect.TypeOf((*MinuteTime)(nil)))
	json.Pretouch(reflect.TypeOf((*InterCityTimeRange)(nil)))
	json.Pretouch(reflect.TypeOf((*InterCityTimeSpan)(nil)))
	json.Pretouch(reflect.TypeOf((*InterCityCarpoolBookingModule)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityData)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityNewOrderParam)(nil)))
	json.Pretouch(reflect.TypeOf((*InterCityCarpoolBookingSku)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercitySkuData)(nil)))
	json.Pretouch(reflect.TypeOf((*Station)(nil)))
	json.Pretouch(reflect.TypeOf((*BaseStation)(nil)))
	json.Pretouch(reflect.TypeOf((*StationDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*StationInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercitySkuStationData)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolInvitationEstimateRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolInvitationEstimateResp)(nil)))
	json.Pretouch(reflect.TypeOf((*InvitationEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*InvitationSeatOptionModule)(nil)))
	json.Pretouch(reflect.TypeOf((*InvitationTravelData)(nil)))
	json.Pretouch(reflect.TypeOf((*ButtonItem)(nil)))
	json.Pretouch(reflect.TypeOf((*InvitationBookingModule)(nil)))
	json.Pretouch(reflect.TypeOf((*PEstimateCarpoolOrderRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*PEstimateCarpoolOrderResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolDescInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*DescSubTitle)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolDiscountInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*CarpoolSuccessInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityServiceInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityEstimateInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityAnycarEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityAnycarSideExtra)(nil)))
}

type OrderType int32

const (
	OrderType_NormalOrder  OrderType = 0 //
	OrderType_BookingOrder OrderType = 1 //
)

var (
	OrderType_name = map[int32]string{
		0: "NormalOrder",
		1: "BookingOrder",
	}
	OrderType_value = map[string]int32{
		"NormalOrder":  0,
		"BookingOrder": 1,
	}
)

func OneOfOrderType(i int32) bool {
	_, ok := OrderType_name[i]
	return ok
}

type UserType int32

const (
	UserType_None         UserType = 0 //
	UserType_NormalUser   UserType = 1 //
	UserType_BusinessUser UserType = 2 //
)

var (
	UserType_name = map[int32]string{
		0: "None",
		1: "NormalUser",
		2: "BusinessUser",
	}
	UserType_value = map[string]int32{
		"None":         0,
		"NormalUser":   1,
		"BusinessUser": 2,
	}
)

func OneOfUserType(i int32) bool {
	_, ok := UserType_name[i]
	return ok
}

type TabType int32

const (
	TabType_Blank               TabType = 0 //
	TabType_Pincheche           TabType = 1 //
	TabType_InterCity           TabType = 2 //
	TabType_InterCitySku        TabType = 3 //
	TabType_InterCitySkuStation TabType = 4 //
	TabType_PinchecheNewForm    TabType = 5 //
	TabType_InterCityAnycar     TabType = 6 //
)

var (
	TabType_name = map[int32]string{
		0: "Blank",
		1: "Pincheche",
		2: "InterCity",
		3: "InterCitySku",
		4: "InterCitySkuStation",
		5: "PinchecheNewForm",
		6: "InterCityAnycar",
	}
	TabType_value = map[string]int32{
		"Blank":               0,
		"Pincheche":           1,
		"InterCity":           2,
		"InterCitySku":        3,
		"InterCitySkuStation": 4,
		"PinchecheNewForm":    5,
		"InterCityAnycar":     6,
	}
)

func OneOfTabType(i int32) bool {
	_, ok := TabType_name[i]
	return ok
}

// 新表单拼车预估请求
type CarpoolEstimateRequest struct {
	Token                     string    `json:"token" form:"token"`                 //用户认证token
	UserType                  UserType  `json:"user_type" form:"user_type"`         //1普通用户；2企业用户
	AppVersion                string    `json:"app_version" form:"app_version"`     //端版本
	AccessKeyId               int32     `json:"access_key_id" form:"access_key_id"` //端来源
	Channel                   string    `json:"channel" form:"channel"`             //渠道号
	ClientType                int32     `json:"client_type" form:"client_type"`     //端类型
	Lang                      string    `json:"lang" form:"lang"`                   //端语种
	PlatformType              int32     `json:"platform_type" form:"platform_type"` //端(平台)类型-2
	MapType                   string    `json:"map_type" form:"map_type"`           //地图类型
	Lat                       float64   `json:"lat" form:"lat"`                     //定位点
	Lng                       float64   `json:"lng" form:"lng"`                     //定位点
	FromLat                   float64   `json:"from_lat" form:"from_lat"`           //起点
	FromLng                   float64   `json:"from_lng" form:"from_lng"`           //起点
	FromPoiId                 string    `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType               string    `json:"from_poi_type" form:"from_poi_type"`
	FromAddress               string    `json:"from_address" form:"from_address"`
	FromName                  string    `json:"from_name" form:"from_name"`
	ChooseFSearchid           string    `json:"choose_f_searchid" form:"choose_f_searchid"` //用户选择起点请求ID
	ToLat                     float64   `json:"to_lat" form:"to_lat"`                       //终点
	ToLng                     float64   `json:"to_lng" form:"to_lng"`                       //终点
	ToPoiId                   string    `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType                 string    `json:"to_poi_type" form:"to_poi_type"`
	ToAddress                 string    `json:"to_address" form:"to_address"`
	ToName                    string    `json:"to_name" form:"to_name"`
	ChooseTSearchid           string    `json:"choose_t_searchid" form:"choose_t_searchid"`         //用户选择终点请求ID
	PreTraceId                string    `json:"pre_trace_id" form:"pre_trace_id"`                   //前一次预估trace
	DepartureRange            string    `json:"departure_range" form:"departure_range"`             //选择的出发时间段
	CarpoolSeatNum            int32     `json:"carpool_seat_num" form:"carpool_seat_num"`           //选择的座位数
	PaymentsType              int32     `json:"payments_type" form:"payments_type"`                 //选择的支付方式
	OrderType                 OrderType `json:"order_type" form:"order_type"`                       //这是一个冗余字段, 其实是不需要的, 可以用 departure_range 推断出
	StopoverPoints            *string   `json:"stopover_points,omitempty" form:"stopover_points"`   //途经点json数组
	TabList                   *string   `json:"tab_list,omitempty" form:"tab_list"`                 //当前tab列表
	DepartureTime             string    `json:"departure_time" form:"departure_time"`               //当时只有班车时间点模式二次勾选会用
	SeatDetailInfo            *string   `json:"seat_detail_info,omitempty" form:"seat_detail_info"` //座位细节信息
	StartStationId            int64     `json:"start_station_id" form:"start_station_id"`           //上车站点id
	EndStationId              int64     `json:"end_station_id" form:"end_station_id"`               //下车站点id
	RouteId                   int64     `json:"route_id" form:"route_id"`                           //路线id
	A3Token                   *string   `json:"a3_token,omitempty" form:"a3_token"`                 //无用
	TransData                 *string   `json:"trans_data,omitempty" form:"trans_data"`             //透传数据
	Xpsid                     *string   `json:"xpsid,omitempty" form:"xpsid"`
	XpsidRoot                 *string   `json:"xpsid_root,omitempty" form:"xpsid_root"`
	IsGuide                   *int32    `json:"is_guide,omitempty" form:"is_guide"` //是否来自导流位
	Dchn                      *string   `json:"dchn,omitempty" form:"dchn"`         //运营投放来源标识
	AgentType                 *string   `json:"agent_type,omitempty" form:"agent_type"`
	BusServiceShiftId         *string   `json:"bus_service_shift_id,omitempty" form:"bus_service_shift_id"`                   //班次号
	BizSceneType              *string   `json:"biz_scene_type,omitempty" form:"biz_scene_type"`                               //rebook 改签
	PreRebookTicketIds        *string   `json:"pre_rebook_ticket_ids,omitempty" form:"pre_rebook_ticket_ids"`                 //车票id列表
	PreOrderId                *string   `json:"pre_order_id,omitempty" form:"pre_order_id"`                                   //上一笔订单id
	SelectedBusServiceShiftId *string   `json:"selected_bus_service_shift_id,omitempty" form:"selected_bus_service_shift_id"` //勾选的班次id
	SelectedBusServiceDay     *string   `json:"selected_bus_service_day,omitempty" form:"selected_bus_service_day"`           //勾选的时间
}

func (x *CarpoolEstimateRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *CarpoolEstimateRequest) GetUserType() (r UserType) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *CarpoolEstimateRequest) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetChannel() (r string) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *CarpoolEstimateRequest) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *CarpoolEstimateRequest) GetPlatformType() (r int32) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *CarpoolEstimateRequest) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *CarpoolEstimateRequest) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *CarpoolEstimateRequest) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *CarpoolEstimateRequest) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *CarpoolEstimateRequest) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *CarpoolEstimateRequest) GetChooseFSearchid() (r string) {
	if x != nil {
		return x.ChooseFSearchid
	}
	return r
}

func (x *CarpoolEstimateRequest) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *CarpoolEstimateRequest) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *CarpoolEstimateRequest) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetToPoiType() (r string) {
	if x != nil {
		return x.ToPoiType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *CarpoolEstimateRequest) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *CarpoolEstimateRequest) GetChooseTSearchid() (r string) {
	if x != nil {
		return x.ChooseTSearchid
	}
	return r
}

func (x *CarpoolEstimateRequest) GetPreTraceId() (r string) {
	if x != nil {
		return x.PreTraceId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetDepartureRange() (r string) {
	if x != nil {
		return x.DepartureRange
	}
	return r
}

func (x *CarpoolEstimateRequest) GetCarpoolSeatNum() (r int32) {
	if x != nil {
		return x.CarpoolSeatNum
	}
	return r
}

func (x *CarpoolEstimateRequest) GetPaymentsType() (r int32) {
	if x != nil {
		return x.PaymentsType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetOrderType() (r OrderType) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetStopoverPoints() (r string) {
	if x != nil && x.StopoverPoints != nil {
		return *x.StopoverPoints
	}
	return r
}

func (x *CarpoolEstimateRequest) GetTabList() (r string) {
	if x != nil && x.TabList != nil {
		return *x.TabList
	}
	return r
}

func (x *CarpoolEstimateRequest) GetDepartureTime() (r string) {
	if x != nil {
		return x.DepartureTime
	}
	return r
}

func (x *CarpoolEstimateRequest) GetSeatDetailInfo() (r string) {
	if x != nil && x.SeatDetailInfo != nil {
		return *x.SeatDetailInfo
	}
	return r
}

func (x *CarpoolEstimateRequest) GetStartStationId() (r int64) {
	if x != nil {
		return x.StartStationId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetEndStationId() (r int64) {
	if x != nil {
		return x.EndStationId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetRouteId() (r int64) {
	if x != nil {
		return x.RouteId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetA3Token() (r string) {
	if x != nil && x.A3Token != nil {
		return *x.A3Token
	}
	return r
}

func (x *CarpoolEstimateRequest) GetTransData() (r string) {
	if x != nil && x.TransData != nil {
		return *x.TransData
	}
	return r
}

func (x *CarpoolEstimateRequest) GetXpsid() (r string) {
	if x != nil && x.Xpsid != nil {
		return *x.Xpsid
	}
	return r
}

func (x *CarpoolEstimateRequest) GetXpsidRoot() (r string) {
	if x != nil && x.XpsidRoot != nil {
		return *x.XpsidRoot
	}
	return r
}

func (x *CarpoolEstimateRequest) GetIsGuide() (r int32) {
	if x != nil && x.IsGuide != nil {
		return *x.IsGuide
	}
	return r
}

func (x *CarpoolEstimateRequest) GetDchn() (r string) {
	if x != nil && x.Dchn != nil {
		return *x.Dchn
	}
	return r
}

func (x *CarpoolEstimateRequest) GetAgentType() (r string) {
	if x != nil && x.AgentType != nil {
		return *x.AgentType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetBusServiceShiftId() (r string) {
	if x != nil && x.BusServiceShiftId != nil {
		return *x.BusServiceShiftId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetBizSceneType() (r string) {
	if x != nil && x.BizSceneType != nil {
		return *x.BizSceneType
	}
	return r
}

func (x *CarpoolEstimateRequest) GetPreRebookTicketIds() (r string) {
	if x != nil && x.PreRebookTicketIds != nil {
		return *x.PreRebookTicketIds
	}
	return r
}

func (x *CarpoolEstimateRequest) GetPreOrderId() (r string) {
	if x != nil && x.PreOrderId != nil {
		return *x.PreOrderId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetSelectedBusServiceShiftId() (r string) {
	if x != nil && x.SelectedBusServiceShiftId != nil {
		return *x.SelectedBusServiceShiftId
	}
	return r
}

func (x *CarpoolEstimateRequest) GetSelectedBusServiceDay() (r string) {
	if x != nil && x.SelectedBusServiceDay != nil {
		return *x.SelectedBusServiceDay
	}
	return r
}

func (x *CarpoolEstimateRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *CarpoolEstimateRequest) SetUserType(v UserType) {
	if x != nil {
		x.UserType = v
	}
}

func (x *CarpoolEstimateRequest) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *CarpoolEstimateRequest) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *CarpoolEstimateRequest) SetChannel(v string) {
	if x != nil {
		x.Channel = v
	}
}

func (x *CarpoolEstimateRequest) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *CarpoolEstimateRequest) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *CarpoolEstimateRequest) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *CarpoolEstimateRequest) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *CarpoolEstimateRequest) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *CarpoolEstimateRequest) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *CarpoolEstimateRequest) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *CarpoolEstimateRequest) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *CarpoolEstimateRequest) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *CarpoolEstimateRequest) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *CarpoolEstimateRequest) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *CarpoolEstimateRequest) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *CarpoolEstimateRequest) SetChooseFSearchid(v string) {
	if x != nil {
		x.ChooseFSearchid = v
	}
}

func (x *CarpoolEstimateRequest) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *CarpoolEstimateRequest) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *CarpoolEstimateRequest) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *CarpoolEstimateRequest) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = v
	}
}

func (x *CarpoolEstimateRequest) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *CarpoolEstimateRequest) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *CarpoolEstimateRequest) SetChooseTSearchid(v string) {
	if x != nil {
		x.ChooseTSearchid = v
	}
}

func (x *CarpoolEstimateRequest) SetPreTraceId(v string) {
	if x != nil {
		x.PreTraceId = v
	}
}

func (x *CarpoolEstimateRequest) SetDepartureRange(v string) {
	if x != nil {
		x.DepartureRange = v
	}
}

func (x *CarpoolEstimateRequest) SetCarpoolSeatNum(v int32) {
	if x != nil {
		x.CarpoolSeatNum = v
	}
}

func (x *CarpoolEstimateRequest) SetPaymentsType(v int32) {
	if x != nil {
		x.PaymentsType = v
	}
}

func (x *CarpoolEstimateRequest) SetOrderType(v OrderType) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *CarpoolEstimateRequest) SetStopoverPoints(v string) {
	if x != nil {
		x.StopoverPoints = &v
	}
}

func (x *CarpoolEstimateRequest) SetTabList(v string) {
	if x != nil {
		x.TabList = &v
	}
}

func (x *CarpoolEstimateRequest) SetDepartureTime(v string) {
	if x != nil {
		x.DepartureTime = v
	}
}

func (x *CarpoolEstimateRequest) SetSeatDetailInfo(v string) {
	if x != nil {
		x.SeatDetailInfo = &v
	}
}

func (x *CarpoolEstimateRequest) SetStartStationId(v int64) {
	if x != nil {
		x.StartStationId = v
	}
}

func (x *CarpoolEstimateRequest) SetEndStationId(v int64) {
	if x != nil {
		x.EndStationId = v
	}
}

func (x *CarpoolEstimateRequest) SetRouteId(v int64) {
	if x != nil {
		x.RouteId = v
	}
}

func (x *CarpoolEstimateRequest) SetA3Token(v string) {
	if x != nil {
		x.A3Token = &v
	}
}

func (x *CarpoolEstimateRequest) SetTransData(v string) {
	if x != nil {
		x.TransData = &v
	}
}

func (x *CarpoolEstimateRequest) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = &v
	}
}

func (x *CarpoolEstimateRequest) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = &v
	}
}

func (x *CarpoolEstimateRequest) SetIsGuide(v int32) {
	if x != nil {
		x.IsGuide = &v
	}
}

func (x *CarpoolEstimateRequest) SetDchn(v string) {
	if x != nil {
		x.Dchn = &v
	}
}

func (x *CarpoolEstimateRequest) SetAgentType(v string) {
	if x != nil {
		x.AgentType = &v
	}
}

func (x *CarpoolEstimateRequest) SetBusServiceShiftId(v string) {
	if x != nil {
		x.BusServiceShiftId = &v
	}
}

func (x *CarpoolEstimateRequest) SetBizSceneType(v string) {
	if x != nil {
		x.BizSceneType = &v
	}
}

func (x *CarpoolEstimateRequest) SetPreRebookTicketIds(v string) {
	if x != nil {
		x.PreRebookTicketIds = &v
	}
}

func (x *CarpoolEstimateRequest) SetPreOrderId(v string) {
	if x != nil {
		x.PreOrderId = &v
	}
}

func (x *CarpoolEstimateRequest) SetSelectedBusServiceShiftId(v string) {
	if x != nil {
		x.SelectedBusServiceShiftId = &v
	}
}

func (x *CarpoolEstimateRequest) SetSelectedBusServiceDay(v string) {
	if x != nil {
		x.SelectedBusServiceDay = &v
	}
}

func (p *CarpoolEstimateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolEstimateRequest(%+v)", *p)
}

type TransData struct {
	PageType     int32   `json:"page_type" form:"page_type"`
	FromType     int16   `json:"from_type" form:"from_type"`
	GuideTraceId string  `json:"guide_trace_id" form:"guide_trace_id"`
	Dchn         *string `json:"dchn,omitempty" form:"dchn"`
}

func (x *TransData) GetPageType() (r int32) {
	if x != nil {
		return x.PageType
	}
	return r
}

func (x *TransData) GetFromType() (r int16) {
	if x != nil {
		return x.FromType
	}
	return r
}

func (x *TransData) GetGuideTraceId() (r string) {
	if x != nil {
		return x.GuideTraceId
	}
	return r
}

func (x *TransData) GetDchn() (r string) {
	if x != nil && x.Dchn != nil {
		return *x.Dchn
	}
	return r
}

func (x *TransData) SetPageType(v int32) {
	if x != nil {
		x.PageType = v
	}
}

func (x *TransData) SetFromType(v int16) {
	if x != nil {
		x.FromType = v
	}
}

func (x *TransData) SetGuideTraceId(v string) {
	if x != nil {
		x.GuideTraceId = v
	}
}

func (x *TransData) SetDchn(v string) {
	if x != nil {
		x.Dchn = &v
	}
}

func (p *TransData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TransData(%+v)", *p)
}

/*
1. 复合字段, 用optional; 基础字段, 用required; 让出零值
2. 特别需要过滤字段的, 再使用optional
3. required, optional不表示字段的必要性
*/
type Coupon struct {
	Tag          string  `json:"tag" form:"tag"`
	Amount       float64 `json:"amount" form:"amount"`
	Discount     int32   `json:"discount" form:"discount"`
	CouponSource string  `json:"coupon_source" form:"coupon_source"`
}

func (x *Coupon) GetTag() (r string) {
	if x != nil {
		return x.Tag
	}
	return r
}

func (x *Coupon) GetAmount() (r float64) {
	if x != nil {
		return x.Amount
	}
	return r
}

func (x *Coupon) GetDiscount() (r int32) {
	if x != nil {
		return x.Discount
	}
	return r
}

func (x *Coupon) GetCouponSource() (r string) {
	if x != nil {
		return x.CouponSource
	}
	return r
}

func (x *Coupon) SetTag(v string) {
	if x != nil {
		x.Tag = v
	}
}

func (x *Coupon) SetAmount(v float64) {
	if x != nil {
		x.Amount = v
	}
}

func (x *Coupon) SetDiscount(v int32) {
	if x != nil {
		x.Discount = v
	}
}

func (x *Coupon) SetCouponSource(v string) {
	if x != nil {
		x.CouponSource = v
	}
}

func (p *Coupon) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Coupon(%+v)", *p)
}

type CarpoolScenePrice struct {
	EstimateFee     float64            `json:"estimate_fee" form:"estimate_fee"`
	DynamicTotalFee float64            `json:"dynamic_total_fee" form:"dynamic_total_fee"`
	PreTotalFee     float64            `json:"pre_total_fee" form:"pre_total_fee"`
	CapPrice        float64            `json:"cap_price" form:"cap_price"`
	CouponInfo      *Coupon            `json:"coupon_info,omitempty" form:"coupon_info"`
	FeeDetailInfo   map[string]float64 `json:"fee_detail_info" form:"fee_detail_info"`
	SceneMark       map[string]string  `json:"scene_mark" form:"scene_mark"`
}

func (x *CarpoolScenePrice) GetEstimateFee() (r float64) {
	if x != nil {
		return x.EstimateFee
	}
	return r
}

func (x *CarpoolScenePrice) GetDynamicTotalFee() (r float64) {
	if x != nil {
		return x.DynamicTotalFee
	}
	return r
}

func (x *CarpoolScenePrice) GetPreTotalFee() (r float64) {
	if x != nil {
		return x.PreTotalFee
	}
	return r
}

func (x *CarpoolScenePrice) GetCapPrice() (r float64) {
	if x != nil {
		return x.CapPrice
	}
	return r
}

func (x *CarpoolScenePrice) GetCouponInfo() (r *Coupon) {
	if x != nil {
		return x.CouponInfo
	}
	return r
}

func (x *CarpoolScenePrice) GetFeeDetailInfo() (r map[string]float64) {
	if x != nil {
		return x.FeeDetailInfo
	}
	return r
}

func (x *CarpoolScenePrice) GetSceneMark() (r map[string]string) {
	if x != nil {
		return x.SceneMark
	}
	return r
}

func (x *CarpoolScenePrice) SetEstimateFee(v float64) {
	if x != nil {
		x.EstimateFee = v
	}
}

func (x *CarpoolScenePrice) SetDynamicTotalFee(v float64) {
	if x != nil {
		x.DynamicTotalFee = v
	}
}

func (x *CarpoolScenePrice) SetPreTotalFee(v float64) {
	if x != nil {
		x.PreTotalFee = v
	}
}

func (x *CarpoolScenePrice) SetCapPrice(v float64) {
	if x != nil {
		x.CapPrice = v
	}
}

func (x *CarpoolScenePrice) SetCouponInfo(v *Coupon) {
	if x != nil {
		x.CouponInfo = v
	}
}

func (x *CarpoolScenePrice) SetFeeDetailInfo(v map[string]float64) {
	if x != nil {
		x.FeeDetailInfo = v
	}
}

func (x *CarpoolScenePrice) SetSceneMark(v map[string]string) {
	if x != nil {
		x.SceneMark = v
	}
}

func (p *CarpoolScenePrice) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolScenePrice(%+v)", *p)
}

type EtsTimeWindow struct {
	BegTime int64 `json:"beg_time" form:"beg_time"`
	EndTime int64 `json:"end_time" form:"end_time"`
}

func (x *EtsTimeWindow) GetBegTime() (r int64) {
	if x != nil {
		return x.BegTime
	}
	return r
}

func (x *EtsTimeWindow) GetEndTime() (r int64) {
	if x != nil {
		return x.EndTime
	}
	return r
}

func (x *EtsTimeWindow) SetBegTime(v int64) {
	if x != nil {
		x.BegTime = v
	}
}

func (x *EtsTimeWindow) SetEndTime(v int64) {
	if x != nil {
		x.EndTime = v
	}
}

func (p *EtsTimeWindow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EtsTimeWindow(%+v)", *p)
}

type CarpoolPriceData struct {
	EstimateId         string               `json:"estimate_id" form:"estimate_id"`
	ProductCategory    int64                `json:"product_category" form:"product_category"`
	CountPriceType     int32                `json:"count_price_type" form:"count_price_type"`
	ProductId          int64                `json:"product_id" form:"product_id"`
	ComboType          int64                `json:"combo_type" form:"combo_type"`
	RequireLevel       string               `json:"require_level" form:"require_level"`
	CarpoolType        int64                `json:"carpool_type" form:"carpool_type"`
	IsDualCarpoolPrice bool                 `json:"is_dual_carpool_price" form:"is_dual_carpool_price"`
	CarpoolPriceType   int32                `json:"carpool_price_type" form:"carpool_price_type"`
	DriverMetre        int64                `json:"driver_metre" form:"driver_metre"`
	DriverMinute       int64                `json:"driver_minute" form:"driver_minute"`
	PaymentType        int32                `json:"payment_type" form:"payment_type"`
	EtsTimeWindow      *EtsTimeWindow       `json:"ets_time_window,omitempty" form:"ets_time_window"`
	CarpoolPriceList   []*CarpoolScenePrice `json:"carpool_price_list" form:"carpool_price_list"`
	ComboId            int64                `json:"combo_id" form:"combo_id"`
}

func (x *CarpoolPriceData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *CarpoolPriceData) GetProductCategory() (r int64) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *CarpoolPriceData) GetCountPriceType() (r int32) {
	if x != nil {
		return x.CountPriceType
	}
	return r
}

func (x *CarpoolPriceData) GetProductId() (r int64) {
	if x != nil {
		return x.ProductId
	}
	return r
}

func (x *CarpoolPriceData) GetComboType() (r int64) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *CarpoolPriceData) GetRequireLevel() (r string) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *CarpoolPriceData) GetCarpoolType() (r int64) {
	if x != nil {
		return x.CarpoolType
	}
	return r
}

func (x *CarpoolPriceData) GetIsDualCarpoolPrice() (r bool) {
	if x != nil {
		return x.IsDualCarpoolPrice
	}
	return r
}

func (x *CarpoolPriceData) GetCarpoolPriceType() (r int32) {
	if x != nil {
		return x.CarpoolPriceType
	}
	return r
}

func (x *CarpoolPriceData) GetDriverMetre() (r int64) {
	if x != nil {
		return x.DriverMetre
	}
	return r
}

func (x *CarpoolPriceData) GetDriverMinute() (r int64) {
	if x != nil {
		return x.DriverMinute
	}
	return r
}

func (x *CarpoolPriceData) GetPaymentType() (r int32) {
	if x != nil {
		return x.PaymentType
	}
	return r
}

func (x *CarpoolPriceData) GetEtsTimeWindow() (r *EtsTimeWindow) {
	if x != nil {
		return x.EtsTimeWindow
	}
	return r
}

func (x *CarpoolPriceData) GetCarpoolPriceList() (r []*CarpoolScenePrice) {
	if x != nil {
		return x.CarpoolPriceList
	}
	return r
}

func (x *CarpoolPriceData) GetComboId() (r int64) {
	if x != nil {
		return x.ComboId
	}
	return r
}

func (x *CarpoolPriceData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *CarpoolPriceData) SetProductCategory(v int64) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *CarpoolPriceData) SetCountPriceType(v int32) {
	if x != nil {
		x.CountPriceType = v
	}
}

func (x *CarpoolPriceData) SetProductId(v int64) {
	if x != nil {
		x.ProductId = v
	}
}

func (x *CarpoolPriceData) SetComboType(v int64) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *CarpoolPriceData) SetRequireLevel(v string) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *CarpoolPriceData) SetCarpoolType(v int64) {
	if x != nil {
		x.CarpoolType = v
	}
}

func (x *CarpoolPriceData) SetIsDualCarpoolPrice(v bool) {
	if x != nil {
		x.IsDualCarpoolPrice = v
	}
}

func (x *CarpoolPriceData) SetCarpoolPriceType(v int32) {
	if x != nil {
		x.CarpoolPriceType = v
	}
}

func (x *CarpoolPriceData) SetDriverMetre(v int64) {
	if x != nil {
		x.DriverMetre = v
	}
}

func (x *CarpoolPriceData) SetDriverMinute(v int64) {
	if x != nil {
		x.DriverMinute = v
	}
}

func (x *CarpoolPriceData) SetPaymentType(v int32) {
	if x != nil {
		x.PaymentType = v
	}
}

func (x *CarpoolPriceData) SetEtsTimeWindow(v *EtsTimeWindow) {
	if x != nil {
		x.EtsTimeWindow = v
	}
}

func (x *CarpoolPriceData) SetCarpoolPriceList(v []*CarpoolScenePrice) {
	if x != nil {
		x.CarpoolPriceList = v
	}
}

func (x *CarpoolPriceData) SetComboId(v int64) {
	if x != nil {
		x.ComboId = v
	}
}

func (p *CarpoolPriceData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolPriceData(%+v)", *p)
}

// 拼车预估返回
type CarpoolPriceResponse struct {
	Errno   int32               `json:"errno" form:"errno"`   //枚举
	Errmsg  string              `json:"errmsg" form:"errmsg"` //枚举
	Data    []*CarpoolPriceData `json:"data" form:"data"`
	TraceId string              `json:"trace_id" form:"trace_id"`
}

func (x *CarpoolPriceResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *CarpoolPriceResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *CarpoolPriceResponse) GetData() (r []*CarpoolPriceData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *CarpoolPriceResponse) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *CarpoolPriceResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *CarpoolPriceResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *CarpoolPriceResponse) SetData(v []*CarpoolPriceData) {
	if x != nil {
		x.Data = v
	}
}

func (x *CarpoolPriceResponse) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (p *CarpoolPriceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolPriceResponse(%+v)", *p)
}

// 拼车预估返回
type CarpoolEstimateResponse struct {
	Errno   int32                        `json:"errno" form:"errno"`   //枚举
	Errmsg  string                       `json:"errmsg" form:"errmsg"` //枚举
	Data    *CarpoolEstimateResponseData `json:"data,omitempty" form:"data"`
	TraceId string                       `json:"trace_id" form:"trace_id"`
}

func (x *CarpoolEstimateResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *CarpoolEstimateResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *CarpoolEstimateResponse) GetData() (r *CarpoolEstimateResponseData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *CarpoolEstimateResponse) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *CarpoolEstimateResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *CarpoolEstimateResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *CarpoolEstimateResponse) SetData(v *CarpoolEstimateResponseData) {
	if x != nil {
		x.Data = v
	}
}

func (x *CarpoolEstimateResponse) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (p *CarpoolEstimateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolEstimateResponse(%+v)", *p)
}

// 拼成乐返回按钮
type BackButton struct {
	Text                string   `json:"text" form:"text"`
	TextColor           string   `json:"text_color" form:"text_color"`
	LeftIcon            string   `json:"left_icon" form:"left_icon"`
	BorderColor         string   `json:"border_color" form:"border_color"`
	JumpTo              string   `json:"jump_to" form:"jump_to"`
	BackgroundGradients []string `json:"background_gradients" form:"background_gradients"`
}

func (x *BackButton) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *BackButton) GetTextColor() (r string) {
	if x != nil {
		return x.TextColor
	}
	return r
}

func (x *BackButton) GetLeftIcon() (r string) {
	if x != nil {
		return x.LeftIcon
	}
	return r
}

func (x *BackButton) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *BackButton) GetJumpTo() (r string) {
	if x != nil {
		return x.JumpTo
	}
	return r
}

func (x *BackButton) GetBackgroundGradients() (r []string) {
	if x != nil {
		return x.BackgroundGradients
	}
	return r
}

func (x *BackButton) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *BackButton) SetTextColor(v string) {
	if x != nil {
		x.TextColor = v
	}
}

func (x *BackButton) SetLeftIcon(v string) {
	if x != nil {
		x.LeftIcon = v
	}
}

func (x *BackButton) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *BackButton) SetJumpTo(v string) {
	if x != nil {
		x.JumpTo = v
	}
}

func (x *BackButton) SetBackgroundGradients(v []string) {
	if x != nil {
		x.BackgroundGradients = v
	}
}

func (p *BackButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BackButton(%+v)", *p)
}

type CarpoolEstimateResponseData struct {
	TabType             TabType                  `json:"tab_type" form:"tab_type"`
	EstimateTraceId     string                   `json:"estimate_trace_id" form:"estimate_trace_id"`
	Pincheche           *PinchecheData           `json:"pincheche,omitempty" form:"pincheche"`
	Intercity           *IntercityData           `json:"intercity,omitempty" form:"intercity"`
	TabBlank            *TabBlank                `json:"tab_blank,omitempty" form:"tab_blank"`
	IntercitySku        *IntercitySkuStationData `json:"intercity_sku,omitempty" form:"intercity_sku"`
	IntercitySkuStation *IntercitySkuStationData `json:"intercity_sku_station,omitempty" form:"intercity_sku_station"`
	PluginPageInfo      *PluginPageInfo          `json:"plugin_page_info,omitempty" form:"plugin_page_info"`     //动调、春节服务费等拦截页
	BackButton          *BackButton              `json:"back_button,omitempty" form:"back_button"`               //返回按钮
	ForceNoticeToast    *string                  `json:"force_notice_toast,omitempty" form:"force_notice_toast"` //返回按钮
	PinchecheV2         *PinchecheDataV2         `json:"pincheche_v2,omitempty" form:"pincheche_v2"`
}

func (x *CarpoolEstimateResponseData) GetTabType() (r TabType) {
	if x != nil {
		return x.TabType
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetPincheche() (r *PinchecheData) {
	if x != nil {
		return x.Pincheche
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetIntercity() (r *IntercityData) {
	if x != nil {
		return x.Intercity
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetTabBlank() (r *TabBlank) {
	if x != nil {
		return x.TabBlank
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetIntercitySku() (r *IntercitySkuStationData) {
	if x != nil {
		return x.IntercitySku
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetIntercitySkuStation() (r *IntercitySkuStationData) {
	if x != nil {
		return x.IntercitySkuStation
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetPluginPageInfo() (r *PluginPageInfo) {
	if x != nil {
		return x.PluginPageInfo
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetBackButton() (r *BackButton) {
	if x != nil {
		return x.BackButton
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetForceNoticeToast() (r string) {
	if x != nil && x.ForceNoticeToast != nil {
		return *x.ForceNoticeToast
	}
	return r
}

func (x *CarpoolEstimateResponseData) GetPinchecheV2() (r *PinchecheDataV2) {
	if x != nil {
		return x.PinchecheV2
	}
	return r
}

func (x *CarpoolEstimateResponseData) SetTabType(v TabType) {
	if x != nil {
		x.TabType = v
	}
}

func (x *CarpoolEstimateResponseData) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (x *CarpoolEstimateResponseData) SetPincheche(v *PinchecheData) {
	if x != nil {
		x.Pincheche = v
	}
}

func (x *CarpoolEstimateResponseData) SetIntercity(v *IntercityData) {
	if x != nil {
		x.Intercity = v
	}
}

func (x *CarpoolEstimateResponseData) SetTabBlank(v *TabBlank) {
	if x != nil {
		x.TabBlank = v
	}
}

func (x *CarpoolEstimateResponseData) SetIntercitySku(v *IntercitySkuStationData) {
	if x != nil {
		x.IntercitySku = v
	}
}

func (x *CarpoolEstimateResponseData) SetIntercitySkuStation(v *IntercitySkuStationData) {
	if x != nil {
		x.IntercitySkuStation = v
	}
}

func (x *CarpoolEstimateResponseData) SetPluginPageInfo(v *PluginPageInfo) {
	if x != nil {
		x.PluginPageInfo = v
	}
}

func (x *CarpoolEstimateResponseData) SetBackButton(v *BackButton) {
	if x != nil {
		x.BackButton = v
	}
}

func (x *CarpoolEstimateResponseData) SetForceNoticeToast(v string) {
	if x != nil {
		x.ForceNoticeToast = &v
	}
}

func (x *CarpoolEstimateResponseData) SetPinchecheV2(v *PinchecheDataV2) {
	if x != nil {
		x.PinchecheV2 = v
	}
}

func (p *CarpoolEstimateResponseData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolEstimateResponseData(%+v)", *p)
}

type IntroMsgTag struct {
	Content       string `json:"content" form:"content"`
	BorderColor   string `json:"border_color" form:"border_color"`
	FontTextColor string `json:"font_text_color" form:"font_text_color"`
	ContentType   int32  `json:"content_type" form:"content_type"`
}

func (x *IntroMsgTag) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *IntroMsgTag) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *IntroMsgTag) GetFontTextColor() (r string) {
	if x != nil {
		return x.FontTextColor
	}
	return r
}

func (x *IntroMsgTag) GetContentType() (r int32) {
	if x != nil {
		return x.ContentType
	}
	return r
}

func (x *IntroMsgTag) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (x *IntroMsgTag) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *IntroMsgTag) SetFontTextColor(v string) {
	if x != nil {
		x.FontTextColor = v
	}
}

func (x *IntroMsgTag) SetContentType(v int32) {
	if x != nil {
		x.ContentType = v
	}
}

func (p *IntroMsgTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntroMsgTag(%+v)", *p)
}

type PinchecheData struct {
	EstimateId        string                   `json:"estimate_id" form:"estimate_id"`
	IntroMsg          string                   `json:"intro_msg" form:"intro_msg"`                       //产品介绍语
	ExtraIntroTag     *HyberTextButton         `json:"extra_intro_tag,omitempty" form:"extra_intro_tag"` //产品介绍语
	MultiPriceDesc    []*PriceWithSingleDesc   `json:"multi_price_desc" form:"multi_price_desc"`
	ExtraPriceDesc    string                   `json:"extra_price_desc" form:"extra_price_desc"`
	CarpoolSeatModule *CarpoolSeatOptionModule `json:"carpool_seat_module,omitempty" form:"carpool_seat_module"`
	CarpoolBooking    *CarpoolBookingModule    `json:"carpool_booking,omitempty" form:"carpool_booking"`
	UserPayInfo       *PaymentOptionModule     `json:"user_pay_info,omitempty" form:"user_pay_info"`
	ExtraMap          *PinchecheNewOrderParam  `json:"extra_map,omitempty" form:"extra_map"`
	ExtraPriceTag     string                   `json:"extra_price_tag" form:"extra_price_tag"`
	IntroMsgTag       *IntroMsgTag             `json:"intro_msg_tag,omitempty" form:"intro_msg_tag"`
	FeeDetailUrl      string                   `json:"fee_detail_url" form:"fee_detail_url"`
	ConfirmButton     string                   `json:"confirm_button" form:"confirm_button"` //Deprecated: 应该端上处理?
}

func (x *PinchecheData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *PinchecheData) GetIntroMsg() (r string) {
	if x != nil {
		return x.IntroMsg
	}
	return r
}

func (x *PinchecheData) GetExtraIntroTag() (r *HyberTextButton) {
	if x != nil {
		return x.ExtraIntroTag
	}
	return r
}

func (x *PinchecheData) GetMultiPriceDesc() (r []*PriceWithSingleDesc) {
	if x != nil {
		return x.MultiPriceDesc
	}
	return r
}

func (x *PinchecheData) GetExtraPriceDesc() (r string) {
	if x != nil {
		return x.ExtraPriceDesc
	}
	return r
}

func (x *PinchecheData) GetCarpoolSeatModule() (r *CarpoolSeatOptionModule) {
	if x != nil {
		return x.CarpoolSeatModule
	}
	return r
}

func (x *PinchecheData) GetCarpoolBooking() (r *CarpoolBookingModule) {
	if x != nil {
		return x.CarpoolBooking
	}
	return r
}

func (x *PinchecheData) GetUserPayInfo() (r *PaymentOptionModule) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *PinchecheData) GetExtraMap() (r *PinchecheNewOrderParam) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *PinchecheData) GetExtraPriceTag() (r string) {
	if x != nil {
		return x.ExtraPriceTag
	}
	return r
}

func (x *PinchecheData) GetIntroMsgTag() (r *IntroMsgTag) {
	if x != nil {
		return x.IntroMsgTag
	}
	return r
}

func (x *PinchecheData) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *PinchecheData) GetConfirmButton() (r string) {
	if x != nil {
		return x.ConfirmButton
	}
	return r
}

func (x *PinchecheData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *PinchecheData) SetIntroMsg(v string) {
	if x != nil {
		x.IntroMsg = v
	}
}

func (x *PinchecheData) SetExtraIntroTag(v *HyberTextButton) {
	if x != nil {
		x.ExtraIntroTag = v
	}
}

func (x *PinchecheData) SetMultiPriceDesc(v []*PriceWithSingleDesc) {
	if x != nil {
		x.MultiPriceDesc = v
	}
}

func (x *PinchecheData) SetExtraPriceDesc(v string) {
	if x != nil {
		x.ExtraPriceDesc = v
	}
}

func (x *PinchecheData) SetCarpoolSeatModule(v *CarpoolSeatOptionModule) {
	if x != nil {
		x.CarpoolSeatModule = v
	}
}

func (x *PinchecheData) SetCarpoolBooking(v *CarpoolBookingModule) {
	if x != nil {
		x.CarpoolBooking = v
	}
}

func (x *PinchecheData) SetUserPayInfo(v *PaymentOptionModule) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *PinchecheData) SetExtraMap(v *PinchecheNewOrderParam) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *PinchecheData) SetExtraPriceTag(v string) {
	if x != nil {
		x.ExtraPriceTag = v
	}
}

func (x *PinchecheData) SetIntroMsgTag(v *IntroMsgTag) {
	if x != nil {
		x.IntroMsgTag = v
	}
}

func (x *PinchecheData) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *PinchecheData) SetConfirmButton(v string) {
	if x != nil {
		x.ConfirmButton = v
	}
}

func (p *PinchecheData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PinchecheData(%+v)", *p)
}

type PinchecheDataV2 struct {
	EstimateData      []*PinchecheEstimateData `json:"estimate_data" form:"estimate_data"`
	CarpoolSeatModule *CarpoolSeatOptionModule `json:"carpool_seat_module,omitempty" form:"carpool_seat_module"`
	UserPayInfo       *PaymentOptionModule     `json:"user_pay_info,omitempty" form:"user_pay_info"`
	ExtraMap          *PinchecheNewOrderParam  `json:"extra_map,omitempty" form:"extra_map"`
	FeeDetailUrl      string                   `json:"fee_detail_url" form:"fee_detail_url"`
}

func (x *PinchecheDataV2) GetEstimateData() (r []*PinchecheEstimateData) {
	if x != nil {
		return x.EstimateData
	}
	return r
}

func (x *PinchecheDataV2) GetCarpoolSeatModule() (r *CarpoolSeatOptionModule) {
	if x != nil {
		return x.CarpoolSeatModule
	}
	return r
}

func (x *PinchecheDataV2) GetUserPayInfo() (r *PaymentOptionModule) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *PinchecheDataV2) GetExtraMap() (r *PinchecheNewOrderParam) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *PinchecheDataV2) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *PinchecheDataV2) SetEstimateData(v []*PinchecheEstimateData) {
	if x != nil {
		x.EstimateData = v
	}
}

func (x *PinchecheDataV2) SetCarpoolSeatModule(v *CarpoolSeatOptionModule) {
	if x != nil {
		x.CarpoolSeatModule = v
	}
}

func (x *PinchecheDataV2) SetUserPayInfo(v *PaymentOptionModule) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *PinchecheDataV2) SetExtraMap(v *PinchecheNewOrderParam) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *PinchecheDataV2) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (p *PinchecheDataV2) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PinchecheDataV2(%+v)", *p)
}

type PinchecheEstimateData struct {
	EstimateId          string                        `json:"estimate_id" form:"estimate_id"`
	IntroImg            string                        `json:"intro_img" form:"intro_img"`                                   //标题icon 【现在出发】【预约出发】
	CarpoolSceneInfo    *CarpoolLowPriceSceneInfo     `json:"carpool_scene_info,omitempty" form:"carpool_scene_info"`       //价格描述&副标题等数据
	ConfirmButton       string                        `json:"confirm_button" form:"confirm_button"`                         //确认按钮
	CarpoolBookingOuter []*CarpoolLowPriceTimeRange   `json:"carpool_booking_outer,omitempty" form:"carpool_booking_outer"` //【预约单】外层展示的2-3个时间片
	CarpoolBooking      *CarpoolLowPriceBookingModule `json:"carpool_booking,omitempty" form:"carpool_booking"`             //【预约单】三天的时间片
	UserPayInfo         *NewFormUserPayInfo           `json:"user_pay_info,omitempty" form:"user_pay_info"`                 //支付方式
	BottomText          *string                       `json:"bottom_text,omitempty" form:"bottom_text"`                     //【预约单】"其他出发时间"
	IsSelect            int32                         `json:"is_select" form:"is_select"`                                   //是否选中
	Style               int32                         `json:"style" form:"style"`                                           //实时数据&预约数据样式
}

func (x *PinchecheEstimateData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *PinchecheEstimateData) GetIntroImg() (r string) {
	if x != nil {
		return x.IntroImg
	}
	return r
}

func (x *PinchecheEstimateData) GetCarpoolSceneInfo() (r *CarpoolLowPriceSceneInfo) {
	if x != nil {
		return x.CarpoolSceneInfo
	}
	return r
}

func (x *PinchecheEstimateData) GetConfirmButton() (r string) {
	if x != nil {
		return x.ConfirmButton
	}
	return r
}

func (x *PinchecheEstimateData) GetCarpoolBookingOuter() (r []*CarpoolLowPriceTimeRange) {
	if x != nil {
		return x.CarpoolBookingOuter
	}
	return r
}

func (x *PinchecheEstimateData) GetCarpoolBooking() (r *CarpoolLowPriceBookingModule) {
	if x != nil {
		return x.CarpoolBooking
	}
	return r
}

func (x *PinchecheEstimateData) GetUserPayInfo() (r *NewFormUserPayInfo) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *PinchecheEstimateData) GetBottomText() (r string) {
	if x != nil && x.BottomText != nil {
		return *x.BottomText
	}
	return r
}

func (x *PinchecheEstimateData) GetIsSelect() (r int32) {
	if x != nil {
		return x.IsSelect
	}
	return r
}

func (x *PinchecheEstimateData) GetStyle() (r int32) {
	if x != nil {
		return x.Style
	}
	return r
}

func (x *PinchecheEstimateData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *PinchecheEstimateData) SetIntroImg(v string) {
	if x != nil {
		x.IntroImg = v
	}
}

func (x *PinchecheEstimateData) SetCarpoolSceneInfo(v *CarpoolLowPriceSceneInfo) {
	if x != nil {
		x.CarpoolSceneInfo = v
	}
}

func (x *PinchecheEstimateData) SetConfirmButton(v string) {
	if x != nil {
		x.ConfirmButton = v
	}
}

func (x *PinchecheEstimateData) SetCarpoolBookingOuter(v []*CarpoolLowPriceTimeRange) {
	if x != nil {
		x.CarpoolBookingOuter = v
	}
}

func (x *PinchecheEstimateData) SetCarpoolBooking(v *CarpoolLowPriceBookingModule) {
	if x != nil {
		x.CarpoolBooking = v
	}
}

func (x *PinchecheEstimateData) SetUserPayInfo(v *NewFormUserPayInfo) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *PinchecheEstimateData) SetBottomText(v string) {
	if x != nil {
		x.BottomText = &v
	}
}

func (x *PinchecheEstimateData) SetIsSelect(v int32) {
	if x != nil {
		x.IsSelect = v
	}
}

func (x *PinchecheEstimateData) SetStyle(v int32) {
	if x != nil {
		x.Style = v
	}
}

func (p *PinchecheEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PinchecheEstimateData(%+v)", *p)
}

type CarpoolLowPriceSceneInfo struct {
	SceneInfoSelected   *CarpoolLowPriceSceneItem `json:"scene_info_selected,omitempty" form:"scene_info_selected"`     //选中态的数据
	SceneInfoUnselected *CarpoolLowPriceSceneItem `json:"scene_info_unselected,omitempty" form:"scene_info_unselected"` //未选中态
}

func (x *CarpoolLowPriceSceneInfo) GetSceneInfoSelected() (r *CarpoolLowPriceSceneItem) {
	if x != nil {
		return x.SceneInfoSelected
	}
	return r
}

func (x *CarpoolLowPriceSceneInfo) GetSceneInfoUnselected() (r *CarpoolLowPriceSceneItem) {
	if x != nil {
		return x.SceneInfoUnselected
	}
	return r
}

func (x *CarpoolLowPriceSceneInfo) SetSceneInfoSelected(v *CarpoolLowPriceSceneItem) {
	if x != nil {
		x.SceneInfoSelected = v
	}
}

func (x *CarpoolLowPriceSceneInfo) SetSceneInfoUnselected(v *CarpoolLowPriceSceneItem) {
	if x != nil {
		x.SceneInfoUnselected = v
	}
}

func (p *CarpoolLowPriceSceneInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolLowPriceSceneInfo(%+v)", *p)
}

type CarpoolLowPriceSceneItem struct {
	ExtraIntroTag  *HyberTextButton       `json:"extra_intro_tag,omitempty" form:"extra_intro_tag"` //左副标题
	MultiPriceDesc []*PriceWithSingleDesc `json:"multi_price_desc" form:"multi_price_desc"`         //价格描述
	ExtraPriceTag  string                 `json:"extra_price_tag" form:"extra_price_tag"`           //“惊喜沟通”
}

func (x *CarpoolLowPriceSceneItem) GetExtraIntroTag() (r *HyberTextButton) {
	if x != nil {
		return x.ExtraIntroTag
	}
	return r
}

func (x *CarpoolLowPriceSceneItem) GetMultiPriceDesc() (r []*PriceWithSingleDesc) {
	if x != nil {
		return x.MultiPriceDesc
	}
	return r
}

func (x *CarpoolLowPriceSceneItem) GetExtraPriceTag() (r string) {
	if x != nil {
		return x.ExtraPriceTag
	}
	return r
}

func (x *CarpoolLowPriceSceneItem) SetExtraIntroTag(v *HyberTextButton) {
	if x != nil {
		x.ExtraIntroTag = v
	}
}

func (x *CarpoolLowPriceSceneItem) SetMultiPriceDesc(v []*PriceWithSingleDesc) {
	if x != nil {
		x.MultiPriceDesc = v
	}
}

func (x *CarpoolLowPriceSceneItem) SetExtraPriceTag(v string) {
	if x != nil {
		x.ExtraPriceTag = v
	}
}

func (p *CarpoolLowPriceSceneItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolLowPriceSceneItem(%+v)", *p)
}

type SimpleJumpButton struct {
	Text   string `json:"text" form:"text"`
	JumpTo string `json:"jump_to" form:"jump_to"`
}

func (x *SimpleJumpButton) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *SimpleJumpButton) GetJumpTo() (r string) {
	if x != nil {
		return x.JumpTo
	}
	return r
}

func (x *SimpleJumpButton) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *SimpleJumpButton) SetJumpTo(v string) {
	if x != nil {
		x.JumpTo = v
	}
}

func (p *SimpleJumpButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SimpleJumpButton(%+v)", *p)
}

type TabBlank struct {
	Icon   string            `json:"icon" form:"icon"`
	Text   string            `json:"text" form:"text"`
	Button *SimpleJumpButton `json:"button,omitempty" form:"button"`
}

func (x *TabBlank) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *TabBlank) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *TabBlank) GetButton() (r *SimpleJumpButton) {
	if x != nil {
		return x.Button
	}
	return r
}

func (x *TabBlank) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *TabBlank) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *TabBlank) SetButton(v *SimpleJumpButton) {
	if x != nil {
		x.Button = v
	}
}

func (p *TabBlank) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TabBlank(%+v)", *p)
}

type TagWithIconAndBorder struct {
	Icon        string `json:"icon" form:"icon"`
	Content     string `json:"content" form:"content"`
	BorderColor string `json:"border_color" form:"border_color"`
	TextColor   string `json:"text_color" form:"text_color"`
}

func (x *TagWithIconAndBorder) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *TagWithIconAndBorder) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *TagWithIconAndBorder) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *TagWithIconAndBorder) GetTextColor() (r string) {
	if x != nil {
		return x.TextColor
	}
	return r
}

func (x *TagWithIconAndBorder) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *TagWithIconAndBorder) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (x *TagWithIconAndBorder) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *TagWithIconAndBorder) SetTextColor(v string) {
	if x != nil {
		x.TextColor = v
	}
}

func (p *TagWithIconAndBorder) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TagWithIconAndBorder(%+v)", *p)
}

type HyberTextButton struct {
	Icon        string `json:"icon" form:"icon"`
	Content     string `json:"content" form:"content"`
	BorderColor string `json:"border_color" form:"border_color"`
	LinkUrl     string `json:"link_url" form:"link_url"`
}

func (x *HyberTextButton) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *HyberTextButton) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *HyberTextButton) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *HyberTextButton) GetLinkUrl() (r string) {
	if x != nil {
		return x.LinkUrl
	}
	return r
}

func (x *HyberTextButton) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *HyberTextButton) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (x *HyberTextButton) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *HyberTextButton) SetLinkUrl(v string) {
	if x != nil {
		x.LinkUrl = v
	}
}

func (p *HyberTextButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HyberTextButton(%+v)", *p)
}

type PriceWithSingleDesc struct {
	FeeMsg  string                `json:"fee_msg" form:"fee_msg"`
	FeeDesc *TagWithIconAndBorder `json:"fee_desc,omitempty" form:"fee_desc"`
}

func (x *PriceWithSingleDesc) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *PriceWithSingleDesc) GetFeeDesc() (r *TagWithIconAndBorder) {
	if x != nil {
		return x.FeeDesc
	}
	return r
}

func (x *PriceWithSingleDesc) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *PriceWithSingleDesc) SetFeeDesc(v *TagWithIconAndBorder) {
	if x != nil {
		x.FeeDesc = v
	}
}

func (p *PriceWithSingleDesc) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PriceWithSingleDesc(%+v)", *p)
}

type CheckBoxExtendForPincheche struct {
	Icon     string                          `json:"icon" form:"icon"`
	Msg      string                          `json:"msg" form:"msg"`
	Selected bool                            `json:"selected" form:"selected"`
	Value    map[string]string               `json:"value,omitempty" form:"value"`     //(go.type="map[string]interface{}")
	Display  *CheckBoxExtendPinchecheDisplay `json:"display,omitempty" form:"display"` //(go.type="map[string]interface{}")
}

func (x *CheckBoxExtendForPincheche) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *CheckBoxExtendForPincheche) GetMsg() (r string) {
	if x != nil {
		return x.Msg
	}
	return r
}

func (x *CheckBoxExtendForPincheche) GetSelected() (r bool) {
	if x != nil {
		return x.Selected
	}
	return r
}

func (x *CheckBoxExtendForPincheche) GetValue() (r map[string]string) {
	if x != nil {
		return x.Value
	}
	return r
}

func (x *CheckBoxExtendForPincheche) GetDisplay() (r *CheckBoxExtendPinchecheDisplay) {
	if x != nil {
		return x.Display
	}
	return r
}

func (x *CheckBoxExtendForPincheche) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *CheckBoxExtendForPincheche) SetMsg(v string) {
	if x != nil {
		x.Msg = v
	}
}

func (x *CheckBoxExtendForPincheche) SetSelected(v bool) {
	if x != nil {
		x.Selected = v
	}
}

func (x *CheckBoxExtendForPincheche) SetValue(v map[string]string) {
	if x != nil {
		x.Value = v
	}
}

func (x *CheckBoxExtendForPincheche) SetDisplay(v *CheckBoxExtendPinchecheDisplay) {
	if x != nil {
		x.Display = v
	}
}

func (p *CheckBoxExtendForPincheche) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckBoxExtendForPincheche(%+v)", *p)
}

type CheckBoxExtendPinchecheDisplay struct {
	IntroMsg      string           `json:"intro_msg" form:"intro_msg"`                       //产品介绍语
	ExtraIntroTag *HyberTextButton `json:"extra_intro_tag,omitempty" form:"extra_intro_tag"` //产品介绍语
}

func (x *CheckBoxExtendPinchecheDisplay) GetIntroMsg() (r string) {
	if x != nil {
		return x.IntroMsg
	}
	return r
}

func (x *CheckBoxExtendPinchecheDisplay) GetExtraIntroTag() (r *HyberTextButton) {
	if x != nil {
		return x.ExtraIntroTag
	}
	return r
}

func (x *CheckBoxExtendPinchecheDisplay) SetIntroMsg(v string) {
	if x != nil {
		x.IntroMsg = v
	}
}

func (x *CheckBoxExtendPinchecheDisplay) SetExtraIntroTag(v *HyberTextButton) {
	if x != nil {
		x.ExtraIntroTag = v
	}
}

func (p *CheckBoxExtendPinchecheDisplay) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckBoxExtendPinchecheDisplay(%+v)", *p)
}

type CheckBoxExtendForIntercity struct {
	Icon     string            `json:"icon" form:"icon"`
	Msg      string            `json:"msg" form:"msg"`
	Selected bool              `json:"selected" form:"selected"`
	Enabled  bool              `json:"enabled" form:"enabled"`
	Value    map[string]string `json:"value,omitempty" form:"value"`     //(go.type="map[string]interface{}")
	Display  map[string]string `json:"display,omitempty" form:"display"` //(go.type="map[string]interface{}")
}

func (x *CheckBoxExtendForIntercity) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *CheckBoxExtendForIntercity) GetMsg() (r string) {
	if x != nil {
		return x.Msg
	}
	return r
}

func (x *CheckBoxExtendForIntercity) GetSelected() (r bool) {
	if x != nil {
		return x.Selected
	}
	return r
}

func (x *CheckBoxExtendForIntercity) GetEnabled() (r bool) {
	if x != nil {
		return x.Enabled
	}
	return r
}

func (x *CheckBoxExtendForIntercity) GetValue() (r map[string]string) {
	if x != nil {
		return x.Value
	}
	return r
}

func (x *CheckBoxExtendForIntercity) GetDisplay() (r map[string]string) {
	if x != nil {
		return x.Display
	}
	return r
}

func (x *CheckBoxExtendForIntercity) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *CheckBoxExtendForIntercity) SetMsg(v string) {
	if x != nil {
		x.Msg = v
	}
}

func (x *CheckBoxExtendForIntercity) SetSelected(v bool) {
	if x != nil {
		x.Selected = v
	}
}

func (x *CheckBoxExtendForIntercity) SetEnabled(v bool) {
	if x != nil {
		x.Enabled = v
	}
}

func (x *CheckBoxExtendForIntercity) SetValue(v map[string]string) {
	if x != nil {
		x.Value = v
	}
}

func (x *CheckBoxExtendForIntercity) SetDisplay(v map[string]string) {
	if x != nil {
		x.Display = v
	}
}

func (p *CheckBoxExtendForIntercity) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckBoxExtendForIntercity(%+v)", *p)
}

type CarpoolSeatOption struct {
	Label    string `json:"label" form:"label"`
	Value    int32  `json:"value" form:"value"`
	Selected bool   `json:"selected" form:"selected"`
	Disable  bool   `json:"disable" form:"disable"`
}

func (x *CarpoolSeatOption) GetLabel() (r string) {
	if x != nil {
		return x.Label
	}
	return r
}

func (x *CarpoolSeatOption) GetValue() (r int32) {
	if x != nil {
		return x.Value
	}
	return r
}

func (x *CarpoolSeatOption) GetSelected() (r bool) {
	if x != nil {
		return x.Selected
	}
	return r
}

func (x *CarpoolSeatOption) GetDisable() (r bool) {
	if x != nil {
		return x.Disable
	}
	return r
}

func (x *CarpoolSeatOption) SetLabel(v string) {
	if x != nil {
		x.Label = v
	}
}

func (x *CarpoolSeatOption) SetValue(v int32) {
	if x != nil {
		x.Value = v
	}
}

func (x *CarpoolSeatOption) SetSelected(v bool) {
	if x != nil {
		x.Selected = v
	}
}

func (x *CarpoolSeatOption) SetDisable(v bool) {
	if x != nil {
		x.Disable = v
	}
}

func (p *CarpoolSeatOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolSeatOption(%+v)", *p)
}

type CarpoolSeatOptionModule struct {
	Title      string               `json:"title" form:"title"`
	OptionList []*CarpoolSeatOption `json:"option_list" form:"option_list"`
}

func (x *CarpoolSeatOptionModule) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *CarpoolSeatOptionModule) GetOptionList() (r []*CarpoolSeatOption) {
	if x != nil {
		return x.OptionList
	}
	return r
}

func (x *CarpoolSeatOptionModule) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *CarpoolSeatOptionModule) SetOptionList(v []*CarpoolSeatOption) {
	if x != nil {
		x.OptionList = v
	}
}

func (p *CarpoolSeatOptionModule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolSeatOptionModule(%+v)", *p)
}

type CarpoolBookingModule struct {
	Title            string                        `json:"title" form:"title"`       //弹层页面展示的文字内容
	Subtitle         string                        `json:"subtitle" form:"subtitle"` //弹层页面展示的文字内容
	AppointMaxDays   int32                         `json:"appoint_max_days" form:"appoint_max_days"`
	BetweenMinutes   int32                         `json:"between_minutes" form:"between_minutes"`
	DepartMinMinutes int32                         `json:"depart_min_minutes" form:"depart_min_minutes"`
	BeginTimeRound   int32                         `json:"begin_time_round" form:"begin_time_round"`
	TimeSpan         *CarpoolBookingModuleTimeSpan `json:"time_span,omitempty" form:"time_span"`
	NeedConfirmTime  bool                          `json:"need_confirm_time" form:"need_confirm_time"`
}

func (x *CarpoolBookingModule) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *CarpoolBookingModule) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *CarpoolBookingModule) GetAppointMaxDays() (r int32) {
	if x != nil {
		return x.AppointMaxDays
	}
	return r
}

func (x *CarpoolBookingModule) GetBetweenMinutes() (r int32) {
	if x != nil {
		return x.BetweenMinutes
	}
	return r
}

func (x *CarpoolBookingModule) GetDepartMinMinutes() (r int32) {
	if x != nil {
		return x.DepartMinMinutes
	}
	return r
}

func (x *CarpoolBookingModule) GetBeginTimeRound() (r int32) {
	if x != nil {
		return x.BeginTimeRound
	}
	return r
}

func (x *CarpoolBookingModule) GetTimeSpan() (r *CarpoolBookingModuleTimeSpan) {
	if x != nil {
		return x.TimeSpan
	}
	return r
}

func (x *CarpoolBookingModule) GetNeedConfirmTime() (r bool) {
	if x != nil {
		return x.NeedConfirmTime
	}
	return r
}

func (x *CarpoolBookingModule) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *CarpoolBookingModule) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *CarpoolBookingModule) SetAppointMaxDays(v int32) {
	if x != nil {
		x.AppointMaxDays = v
	}
}

func (x *CarpoolBookingModule) SetBetweenMinutes(v int32) {
	if x != nil {
		x.BetweenMinutes = v
	}
}

func (x *CarpoolBookingModule) SetDepartMinMinutes(v int32) {
	if x != nil {
		x.DepartMinMinutes = v
	}
}

func (x *CarpoolBookingModule) SetBeginTimeRound(v int32) {
	if x != nil {
		x.BeginTimeRound = v
	}
}

func (x *CarpoolBookingModule) SetTimeSpan(v *CarpoolBookingModuleTimeSpan) {
	if x != nil {
		x.TimeSpan = v
	}
}

func (x *CarpoolBookingModule) SetNeedConfirmTime(v bool) {
	if x != nil {
		x.NeedConfirmTime = v
	}
}

func (p *CarpoolBookingModule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolBookingModule(%+v)", *p)
}

type CarpoolBookingModuleTimeSpan struct {
	FromHour int32 `json:"from_hour" form:"from_hour"`
	ToHour   int32 `json:"to_hour" form:"to_hour"`
}

func (x *CarpoolBookingModuleTimeSpan) GetFromHour() (r int32) {
	if x != nil {
		return x.FromHour
	}
	return r
}

func (x *CarpoolBookingModuleTimeSpan) GetToHour() (r int32) {
	if x != nil {
		return x.ToHour
	}
	return r
}

func (x *CarpoolBookingModuleTimeSpan) SetFromHour(v int32) {
	if x != nil {
		x.FromHour = v
	}
}

func (x *CarpoolBookingModuleTimeSpan) SetToHour(v int32) {
	if x != nil {
		x.ToHour = v
	}
}

func (p *CarpoolBookingModuleTimeSpan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolBookingModuleTimeSpan(%+v)", *p)
}

type PaymentOption struct {
	Msg              string `json:"msg" form:"msg"`
	Tag              int32  `json:"tag" form:"tag"`
	Selected         bool   `json:"selected" form:"selected"`
	IsSelected       int32  `json:"isSelected" form:"isSelected"` //??
	BusinessConstSet string `json:"business_const_set" form:"business_const_set"`
}

func (x *PaymentOption) GetMsg() (r string) {
	if x != nil {
		return x.Msg
	}
	return r
}

func (x *PaymentOption) GetTag() (r int32) {
	if x != nil {
		return x.Tag
	}
	return r
}

func (x *PaymentOption) GetSelected() (r bool) {
	if x != nil {
		return x.Selected
	}
	return r
}

func (x *PaymentOption) GetIsSelected() (r int32) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *PaymentOption) GetBusinessConstSet() (r string) {
	if x != nil {
		return x.BusinessConstSet
	}
	return r
}

func (x *PaymentOption) SetMsg(v string) {
	if x != nil {
		x.Msg = v
	}
}

func (x *PaymentOption) SetTag(v int32) {
	if x != nil {
		x.Tag = v
	}
}

func (x *PaymentOption) SetSelected(v bool) {
	if x != nil {
		x.Selected = v
	}
}

func (x *PaymentOption) SetIsSelected(v int32) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *PaymentOption) SetBusinessConstSet(v string) {
	if x != nil {
		x.BusinessConstSet = v
	}
}

func (p *PaymentOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PaymentOption(%+v)", *p)
}

type PaymentOptionModule struct {
	Title       string           `json:"title" form:"title"`         //选择支付方式弹层的title
	SubTitle    string           `json:"sub_title" form:"sub_title"` //选择支付方式弹层的title
	PaymentList []*PaymentOption `json:"payment_list,omitempty" form:"payment_list"`
}

func (x *PaymentOptionModule) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *PaymentOptionModule) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *PaymentOptionModule) GetPaymentList() (r []*PaymentOption) {
	if x != nil {
		return x.PaymentList
	}
	return r
}

func (x *PaymentOptionModule) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *PaymentOptionModule) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *PaymentOptionModule) SetPaymentList(v []*PaymentOption) {
	if x != nil {
		x.PaymentList = v
	}
}

func (p *PaymentOptionModule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PaymentOptionModule(%+v)", *p)
}

type PinchecheNewOrderParam struct {
	BusinessId      int32 `json:"business_id" form:"business_id"`
	RequireLevel    int32 `json:"require_level" form:"require_level"` //3: required i32 combo_id
	ComboType       int32 `json:"combo_type" form:"combo_type"`
	ProductCategory int32 `json:"product_category" form:"product_category"`
	LevelType       int32 `json:"level_type" form:"level_type"`
	CountPriceType  int32 `json:"count_price_type" form:"count_price_type"`
	PageType        int32 `json:"page_type" form:"page_type"`
}

func (x *PinchecheNewOrderParam) GetBusinessId() (r int32) {
	if x != nil {
		return x.BusinessId
	}
	return r
}

func (x *PinchecheNewOrderParam) GetRequireLevel() (r int32) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *PinchecheNewOrderParam) GetComboType() (r int32) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *PinchecheNewOrderParam) GetProductCategory() (r int32) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *PinchecheNewOrderParam) GetLevelType() (r int32) {
	if x != nil {
		return x.LevelType
	}
	return r
}

func (x *PinchecheNewOrderParam) GetCountPriceType() (r int32) {
	if x != nil {
		return x.CountPriceType
	}
	return r
}

func (x *PinchecheNewOrderParam) GetPageType() (r int32) {
	if x != nil {
		return x.PageType
	}
	return r
}

func (x *PinchecheNewOrderParam) SetBusinessId(v int32) {
	if x != nil {
		x.BusinessId = v
	}
}

func (x *PinchecheNewOrderParam) SetRequireLevel(v int32) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *PinchecheNewOrderParam) SetComboType(v int32) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *PinchecheNewOrderParam) SetProductCategory(v int32) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *PinchecheNewOrderParam) SetLevelType(v int32) {
	if x != nil {
		x.LevelType = v
	}
}

func (x *PinchecheNewOrderParam) SetCountPriceType(v int32) {
	if x != nil {
		x.CountPriceType = v
	}
}

func (x *PinchecheNewOrderParam) SetPageType(v int32) {
	if x != nil {
		x.PageType = v
	}
}

func (p *PinchecheNewOrderParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PinchecheNewOrderParam(%+v)", *p)
}

type IntercityFeeInfoItem struct {
	Content       string `json:"content" form:"content"`                 //文字支持高亮
	LeftIcon      string `json:"left_icon" form:"left_icon"`             //左侧icon
	FontColor     string `json:"font_color" form:"font_color"`           //// ex：抵{18}元；font_color表示 18的颜色
	BgFillColor   string `json:"bg_fill_color" form:"bg_fill_color"`     //背景颜色
	BorderColor   string `json:"border_color" form:"border_color"`       //边框颜色
	FontTextColor string `json:"font_text_color" form:"font_text_color"` //ex：抵{18}元；font_text_color表示 抵和元的颜色
}

func (x *IntercityFeeInfoItem) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *IntercityFeeInfoItem) GetLeftIcon() (r string) {
	if x != nil {
		return x.LeftIcon
	}
	return r
}

func (x *IntercityFeeInfoItem) GetFontColor() (r string) {
	if x != nil {
		return x.FontColor
	}
	return r
}

func (x *IntercityFeeInfoItem) GetBgFillColor() (r string) {
	if x != nil {
		return x.BgFillColor
	}
	return r
}

func (x *IntercityFeeInfoItem) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *IntercityFeeInfoItem) GetFontTextColor() (r string) {
	if x != nil {
		return x.FontTextColor
	}
	return r
}

func (x *IntercityFeeInfoItem) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (x *IntercityFeeInfoItem) SetLeftIcon(v string) {
	if x != nil {
		x.LeftIcon = v
	}
}

func (x *IntercityFeeInfoItem) SetFontColor(v string) {
	if x != nil {
		x.FontColor = v
	}
}

func (x *IntercityFeeInfoItem) SetBgFillColor(v string) {
	if x != nil {
		x.BgFillColor = v
	}
}

func (x *IntercityFeeInfoItem) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *IntercityFeeInfoItem) SetFontTextColor(v string) {
	if x != nil {
		x.FontTextColor = v
	}
}

func (p *IntercityFeeInfoItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityFeeInfoItem(%+v)", *p)
}

type InterCityCarpoolSeatModule struct {
	Title                  string `json:"title" form:"title"`                                         //乘车人数
	MaxCount               int32  `json:"max_count" form:"max_count"`                                 //3
	SelectCount            int32  `json:"select_count" form:"select_count"`                           //二次预估选中的人数
	SelectCountChildTicket int32  `json:"select_count_child_ticket" form:"select_count_child_ticket"` //儿童票模式下的对端渲染数量
	SeatsExceedToast       string `json:"seats_exceed_toast" form:"seats_exceed_toast"`               //"最大不可超过2人
}

func (x *InterCityCarpoolSeatModule) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *InterCityCarpoolSeatModule) GetMaxCount() (r int32) {
	if x != nil {
		return x.MaxCount
	}
	return r
}

func (x *InterCityCarpoolSeatModule) GetSelectCount() (r int32) {
	if x != nil {
		return x.SelectCount
	}
	return r
}

func (x *InterCityCarpoolSeatModule) GetSelectCountChildTicket() (r int32) {
	if x != nil {
		return x.SelectCountChildTicket
	}
	return r
}

func (x *InterCityCarpoolSeatModule) GetSeatsExceedToast() (r string) {
	if x != nil {
		return x.SeatsExceedToast
	}
	return r
}

func (x *InterCityCarpoolSeatModule) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *InterCityCarpoolSeatModule) SetMaxCount(v int32) {
	if x != nil {
		x.MaxCount = v
	}
}

func (x *InterCityCarpoolSeatModule) SetSelectCount(v int32) {
	if x != nil {
		x.SelectCount = v
	}
}

func (x *InterCityCarpoolSeatModule) SetSelectCountChildTicket(v int32) {
	if x != nil {
		x.SelectCountChildTicket = v
	}
}

func (x *InterCityCarpoolSeatModule) SetSeatsExceedToast(v string) {
	if x != nil {
		x.SeatsExceedToast = v
	}
}

func (p *InterCityCarpoolSeatModule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterCityCarpoolSeatModule(%+v)", *p)
}

type CarpoolLowPriceTimeRange struct {
	Value      string                `json:"value" form:"value"`             //[1640124000,1640125800]
	Msg        string                `json:"msg" form:"msg"`                 //今天{12:00-12:30}
	OuterMsg   string                `json:"outer_msg" form:"outer_msg"`     //"12:00-12:30",用于外层拼装展示文案
	Tag        *TagWithIconAndBorder `json:"tag,omitempty" form:"tag"`       //附加描述【无车赔】等
	OrderType  int32                 `json:"order_type" form:"order_type"`   //实时&预约单
	ButtonText string                `json:"button_text" form:"button_text"` //确认时间片的按钮文案：今天12:00-12:30出发
	Selected   bool                  `json:"selected" form:"selected"`       //是否勾选
}

func (x *CarpoolLowPriceTimeRange) GetValue() (r string) {
	if x != nil {
		return x.Value
	}
	return r
}

func (x *CarpoolLowPriceTimeRange) GetMsg() (r string) {
	if x != nil {
		return x.Msg
	}
	return r
}

func (x *CarpoolLowPriceTimeRange) GetOuterMsg() (r string) {
	if x != nil {
		return x.OuterMsg
	}
	return r
}

func (x *CarpoolLowPriceTimeRange) GetTag() (r *TagWithIconAndBorder) {
	if x != nil {
		return x.Tag
	}
	return r
}

func (x *CarpoolLowPriceTimeRange) GetOrderType() (r int32) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *CarpoolLowPriceTimeRange) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *CarpoolLowPriceTimeRange) GetSelected() (r bool) {
	if x != nil {
		return x.Selected
	}
	return r
}

func (x *CarpoolLowPriceTimeRange) SetValue(v string) {
	if x != nil {
		x.Value = v
	}
}

func (x *CarpoolLowPriceTimeRange) SetMsg(v string) {
	if x != nil {
		x.Msg = v
	}
}

func (x *CarpoolLowPriceTimeRange) SetOuterMsg(v string) {
	if x != nil {
		x.OuterMsg = v
	}
}

func (x *CarpoolLowPriceTimeRange) SetTag(v *TagWithIconAndBorder) {
	if x != nil {
		x.Tag = v
	}
}

func (x *CarpoolLowPriceTimeRange) SetOrderType(v int32) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *CarpoolLowPriceTimeRange) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (x *CarpoolLowPriceTimeRange) SetSelected(v bool) {
	if x != nil {
		x.Selected = v
	}
}

func (p *CarpoolLowPriceTimeRange) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolLowPriceTimeRange(%+v)", *p)
}

type CarpoolLowPriceBookingModule struct {
	Title      string               `json:"title" form:"title"`
	Subtitle   string               `json:"subtitle" form:"subtitle"`
	TimeSpan   []*PinchecheTimeSpan `json:"time_span" form:"time_span"`
	ButtonText string               `json:"button_text" form:"button_text"` //确认时间片的按钮文案
}

func (x *CarpoolLowPriceBookingModule) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *CarpoolLowPriceBookingModule) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *CarpoolLowPriceBookingModule) GetTimeSpan() (r []*PinchecheTimeSpan) {
	if x != nil {
		return x.TimeSpan
	}
	return r
}

func (x *CarpoolLowPriceBookingModule) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *CarpoolLowPriceBookingModule) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *CarpoolLowPriceBookingModule) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *CarpoolLowPriceBookingModule) SetTimeSpan(v []*PinchecheTimeSpan) {
	if x != nil {
		x.TimeSpan = v
	}
}

func (x *CarpoolLowPriceBookingModule) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (p *CarpoolLowPriceBookingModule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolLowPriceBookingModule(%+v)", *p)
}

type PinchecheTimeSpan struct {
	Title string      `json:"title" form:"title"`
	Date  string      `json:"date" form:"date"`
	Time  []*HourTime `json:"time" form:"time"`
}

func (x *PinchecheTimeSpan) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *PinchecheTimeSpan) GetDate() (r string) {
	if x != nil {
		return x.Date
	}
	return r
}

func (x *PinchecheTimeSpan) GetTime() (r []*HourTime) {
	if x != nil {
		return x.Time
	}
	return r
}

func (x *PinchecheTimeSpan) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *PinchecheTimeSpan) SetDate(v string) {
	if x != nil {
		x.Date = v
	}
}

func (x *PinchecheTimeSpan) SetTime(v []*HourTime) {
	if x != nil {
		x.Time = v
	}
}

func (p *PinchecheTimeSpan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PinchecheTimeSpan(%+v)", *p)
}

type HourTime struct {
	Text       string        `json:"text" form:"text"`
	Hour       string        `json:"hour" form:"hour"`
	MinuteList []*MinuteTime `json:"minute_list" form:"minute_list"`
}

func (x *HourTime) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *HourTime) GetHour() (r string) {
	if x != nil {
		return x.Hour
	}
	return r
}

func (x *HourTime) GetMinuteList() (r []*MinuteTime) {
	if x != nil {
		return x.MinuteList
	}
	return r
}

func (x *HourTime) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *HourTime) SetHour(v string) {
	if x != nil {
		x.Hour = v
	}
}

func (x *HourTime) SetMinuteList(v []*MinuteTime) {
	if x != nil {
		x.MinuteList = v
	}
}

func (p *HourTime) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HourTime(%+v)", *p)
}

type MinuteTime struct {
	Minute     string `json:"minute" form:"minute"`
	Value      string `json:"value" form:"value"`
	Selected   bool   `json:"selected" form:"selected"`
	OrderType  int32  `json:"order_type" form:"order_type"`
	ButtonText string `json:"button_text" form:"button_text"`
}

func (x *MinuteTime) GetMinute() (r string) {
	if x != nil {
		return x.Minute
	}
	return r
}

func (x *MinuteTime) GetValue() (r string) {
	if x != nil {
		return x.Value
	}
	return r
}

func (x *MinuteTime) GetSelected() (r bool) {
	if x != nil {
		return x.Selected
	}
	return r
}

func (x *MinuteTime) GetOrderType() (r int32) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *MinuteTime) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *MinuteTime) SetMinute(v string) {
	if x != nil {
		x.Minute = v
	}
}

func (x *MinuteTime) SetValue(v string) {
	if x != nil {
		x.Value = v
	}
}

func (x *MinuteTime) SetSelected(v bool) {
	if x != nil {
		x.Selected = v
	}
}

func (x *MinuteTime) SetOrderType(v int32) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *MinuteTime) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (p *MinuteTime) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MinuteTime(%+v)", *p)
}

type InterCityTimeRange struct {
	Value             string `json:"value" form:"value"`                               //[1640124000,1640125800]
	Msg               string `json:"msg" form:"msg"`                                   //12:00-12:30出发
	OuterMsg          string `json:"outer_msg" form:"outer_msg"`                       //"12:00-12:30",用于外层拼装展示文案 : 明天 12:00-12:30
	OrderType         int32  `json:"order_type" form:"order_type"`                     //当前时间片对应的订单类型
	SkuDesc           string `json:"sku_desc" form:"sku_desc"`                         //库存模式剩余座位数
	Icon              string `json:"icon" form:"icon"`                                 //库存模式图片链接（推荐、预约优惠，来自于 intercity_sku-time_span）
	Available         bool   `json:"available" form:"available"`                       //当前时间片是否可用
	BusServiceShiftId string `json:"bus_service_shift_id" form:"bus_service_shift_id"` //大巴班次id 因为要获取退票规则所以就加了一个返回
	Selected          bool   `json:"selected" form:"selected"`                         //是否勾选该班次
}

func (x *InterCityTimeRange) GetValue() (r string) {
	if x != nil {
		return x.Value
	}
	return r
}

func (x *InterCityTimeRange) GetMsg() (r string) {
	if x != nil {
		return x.Msg
	}
	return r
}

func (x *InterCityTimeRange) GetOuterMsg() (r string) {
	if x != nil {
		return x.OuterMsg
	}
	return r
}

func (x *InterCityTimeRange) GetOrderType() (r int32) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *InterCityTimeRange) GetSkuDesc() (r string) {
	if x != nil {
		return x.SkuDesc
	}
	return r
}

func (x *InterCityTimeRange) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *InterCityTimeRange) GetAvailable() (r bool) {
	if x != nil {
		return x.Available
	}
	return r
}

func (x *InterCityTimeRange) GetBusServiceShiftId() (r string) {
	if x != nil {
		return x.BusServiceShiftId
	}
	return r
}

func (x *InterCityTimeRange) GetSelected() (r bool) {
	if x != nil {
		return x.Selected
	}
	return r
}

func (x *InterCityTimeRange) SetValue(v string) {
	if x != nil {
		x.Value = v
	}
}

func (x *InterCityTimeRange) SetMsg(v string) {
	if x != nil {
		x.Msg = v
	}
}

func (x *InterCityTimeRange) SetOuterMsg(v string) {
	if x != nil {
		x.OuterMsg = v
	}
}

func (x *InterCityTimeRange) SetOrderType(v int32) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *InterCityTimeRange) SetSkuDesc(v string) {
	if x != nil {
		x.SkuDesc = v
	}
}

func (x *InterCityTimeRange) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *InterCityTimeRange) SetAvailable(v bool) {
	if x != nil {
		x.Available = v
	}
}

func (x *InterCityTimeRange) SetBusServiceShiftId(v string) {
	if x != nil {
		x.BusServiceShiftId = v
	}
}

func (x *InterCityTimeRange) SetSelected(v bool) {
	if x != nil {
		x.Selected = v
	}
}

func (p *InterCityTimeRange) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterCityTimeRange(%+v)", *p)
}

type InterCityTimeSpan struct {
	Date     string                `json:"date" form:"date"`         //今天
	Range    []*InterCityTimeRange `json:"range" form:"range"`       //今天
	Day      string                `json:"day" form:"day"`           //"8.15"
	Selected bool                  `json:"selected" form:"selected"` //是否选择当前日期
}

func (x *InterCityTimeSpan) GetDate() (r string) {
	if x != nil {
		return x.Date
	}
	return r
}

func (x *InterCityTimeSpan) GetRange() (r []*InterCityTimeRange) {
	if x != nil {
		return x.Range
	}
	return r
}

func (x *InterCityTimeSpan) GetDay() (r string) {
	if x != nil {
		return x.Day
	}
	return r
}

func (x *InterCityTimeSpan) GetSelected() (r bool) {
	if x != nil {
		return x.Selected
	}
	return r
}

func (x *InterCityTimeSpan) SetDate(v string) {
	if x != nil {
		x.Date = v
	}
}

func (x *InterCityTimeSpan) SetRange(v []*InterCityTimeRange) {
	if x != nil {
		x.Range = v
	}
}

func (x *InterCityTimeSpan) SetDay(v string) {
	if x != nil {
		x.Day = v
	}
}

func (x *InterCityTimeSpan) SetSelected(v bool) {
	if x != nil {
		x.Selected = v
	}
}

func (p *InterCityTimeSpan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterCityTimeSpan(%+v)", *p)
}

type InterCityCarpoolBookingModule struct {
	Title                  string               `json:"title" form:"title"`
	Subtitle               string               `json:"subtitle" form:"subtitle"`
	TimeSpan               []*InterCityTimeSpan `json:"time_span" form:"time_span"`
	FirstSpanFit           bool                 `json:"first_span_fit" form:"first_span_fit"`                       //第一个时间片是否可以直接发单
	ButtonText             string               `json:"button_text" form:"button_text"`                             //确认时间片的按钮文案
	Icon                   string               `json:"icon" form:"icon"`                                           //图片链接
	NoDisplayBookingSelect bool                 `json:"no_display_booking_select" form:"no_display_booking_select"` //时间段选择是否可用
}

func (x *InterCityCarpoolBookingModule) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *InterCityCarpoolBookingModule) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *InterCityCarpoolBookingModule) GetTimeSpan() (r []*InterCityTimeSpan) {
	if x != nil {
		return x.TimeSpan
	}
	return r
}

func (x *InterCityCarpoolBookingModule) GetFirstSpanFit() (r bool) {
	if x != nil {
		return x.FirstSpanFit
	}
	return r
}

func (x *InterCityCarpoolBookingModule) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *InterCityCarpoolBookingModule) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *InterCityCarpoolBookingModule) GetNoDisplayBookingSelect() (r bool) {
	if x != nil {
		return x.NoDisplayBookingSelect
	}
	return r
}

func (x *InterCityCarpoolBookingModule) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *InterCityCarpoolBookingModule) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *InterCityCarpoolBookingModule) SetTimeSpan(v []*InterCityTimeSpan) {
	if x != nil {
		x.TimeSpan = v
	}
}

func (x *InterCityCarpoolBookingModule) SetFirstSpanFit(v bool) {
	if x != nil {
		x.FirstSpanFit = v
	}
}

func (x *InterCityCarpoolBookingModule) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (x *InterCityCarpoolBookingModule) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *InterCityCarpoolBookingModule) SetNoDisplayBookingSelect(v bool) {
	if x != nil {
		x.NoDisplayBookingSelect = v
	}
}

func (p *InterCityCarpoolBookingModule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterCityCarpoolBookingModule(%+v)", *p)
}

type IntercityData struct {
	EstimateId        string                         `json:"estimate_id" form:"estimate_id"`
	RouteType         int64                          `json:"route_type" form:"route_type"`                     //2: required i64 business_id //为Android临时保留，他们下线难度较高，后续再跟进迁移到extra_map的逻辑
	IntroMsg          string                         `json:"intro_msg" form:"intro_msg"`                       //产品介绍语
	ExtraIntroTag     *TagWithIconAndBorder          `json:"extra_intro_tag,omitempty" form:"extra_intro_tag"` //产品介绍语
	FeeMsg            string                         `json:"fee_msg" form:"fee_msg"`
	FeeDescList       []*IntercityFeeInfoItem        `json:"fee_desc_list" form:"fee_desc_list"`
	ExtraPriceDesc    string                         `json:"extra_price_desc" form:"extra_price_desc"`
	ExtraPriceTag     string                         `json:"extra_price_tag" form:"extra_price_tag"`
	CarpoolSeatModule *InterCityCarpoolSeatModule    `json:"carpool_seat_module,omitempty" form:"carpool_seat_module"`
	CarpoolBooking    *InterCityCarpoolBookingModule `json:"carpool_booking,omitempty" form:"carpool_booking"`
	UserPayInfo       *PaymentOptionModule           `json:"user_pay_info,omitempty" form:"user_pay_info"`
	ExtraMap          *IntercityNewOrderParam        `json:"extra_map,omitempty" form:"extra_map"`
	FeeDetailUrl      string                         `json:"fee_detail_url" form:"fee_detail_url"`
	ConfirmButton     string                         `json:"confirm_button" form:"confirm_button"`
	IntroMsgTag       *IntroMsgTag                   `json:"intro_msg_tag,omitempty" form:"intro_msg_tag"`
}

func (x *IntercityData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *IntercityData) GetRouteType() (r int64) {
	if x != nil {
		return x.RouteType
	}
	return r
}

func (x *IntercityData) GetIntroMsg() (r string) {
	if x != nil {
		return x.IntroMsg
	}
	return r
}

func (x *IntercityData) GetExtraIntroTag() (r *TagWithIconAndBorder) {
	if x != nil {
		return x.ExtraIntroTag
	}
	return r
}

func (x *IntercityData) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *IntercityData) GetFeeDescList() (r []*IntercityFeeInfoItem) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *IntercityData) GetExtraPriceDesc() (r string) {
	if x != nil {
		return x.ExtraPriceDesc
	}
	return r
}

func (x *IntercityData) GetExtraPriceTag() (r string) {
	if x != nil {
		return x.ExtraPriceTag
	}
	return r
}

func (x *IntercityData) GetCarpoolSeatModule() (r *InterCityCarpoolSeatModule) {
	if x != nil {
		return x.CarpoolSeatModule
	}
	return r
}

func (x *IntercityData) GetCarpoolBooking() (r *InterCityCarpoolBookingModule) {
	if x != nil {
		return x.CarpoolBooking
	}
	return r
}

func (x *IntercityData) GetUserPayInfo() (r *PaymentOptionModule) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *IntercityData) GetExtraMap() (r *IntercityNewOrderParam) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *IntercityData) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *IntercityData) GetConfirmButton() (r string) {
	if x != nil {
		return x.ConfirmButton
	}
	return r
}

func (x *IntercityData) GetIntroMsgTag() (r *IntroMsgTag) {
	if x != nil {
		return x.IntroMsgTag
	}
	return r
}

func (x *IntercityData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *IntercityData) SetRouteType(v int64) {
	if x != nil {
		x.RouteType = v
	}
}

func (x *IntercityData) SetIntroMsg(v string) {
	if x != nil {
		x.IntroMsg = v
	}
}

func (x *IntercityData) SetExtraIntroTag(v *TagWithIconAndBorder) {
	if x != nil {
		x.ExtraIntroTag = v
	}
}

func (x *IntercityData) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *IntercityData) SetFeeDescList(v []*IntercityFeeInfoItem) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *IntercityData) SetExtraPriceDesc(v string) {
	if x != nil {
		x.ExtraPriceDesc = v
	}
}

func (x *IntercityData) SetExtraPriceTag(v string) {
	if x != nil {
		x.ExtraPriceTag = v
	}
}

func (x *IntercityData) SetCarpoolSeatModule(v *InterCityCarpoolSeatModule) {
	if x != nil {
		x.CarpoolSeatModule = v
	}
}

func (x *IntercityData) SetCarpoolBooking(v *InterCityCarpoolBookingModule) {
	if x != nil {
		x.CarpoolBooking = v
	}
}

func (x *IntercityData) SetUserPayInfo(v *PaymentOptionModule) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *IntercityData) SetExtraMap(v *IntercityNewOrderParam) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *IntercityData) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *IntercityData) SetConfirmButton(v string) {
	if x != nil {
		x.ConfirmButton = v
	}
}

func (x *IntercityData) SetIntroMsgTag(v *IntroMsgTag) {
	if x != nil {
		x.IntroMsgTag = v
	}
}

func (p *IntercityData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityData(%+v)", *p)
}

type IntercityNewOrderParam struct {
	ProductCategory int64  `json:"product_category" form:"product_category"`
	ComboId         int64  `json:"combo_id" form:"combo_id"`
	ComboType       int64  `json:"combo_type" form:"combo_type"`
	RequireLevel    int64  `json:"require_level" form:"require_level"`
	BusinessId      int64  `json:"business_id" form:"business_id"`
	PageType        int32  `json:"page_type" form:"page_type"`
	RouteType       int32  `json:"route_type" form:"route_type"`
	CarpoolSeatNum  *int32 `json:"carpool_seat_num,omitempty" form:"carpool_seat_num"`
}

func (x *IntercityNewOrderParam) GetProductCategory() (r int64) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *IntercityNewOrderParam) GetComboId() (r int64) {
	if x != nil {
		return x.ComboId
	}
	return r
}

func (x *IntercityNewOrderParam) GetComboType() (r int64) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *IntercityNewOrderParam) GetRequireLevel() (r int64) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *IntercityNewOrderParam) GetBusinessId() (r int64) {
	if x != nil {
		return x.BusinessId
	}
	return r
}

func (x *IntercityNewOrderParam) GetPageType() (r int32) {
	if x != nil {
		return x.PageType
	}
	return r
}

func (x *IntercityNewOrderParam) GetRouteType() (r int32) {
	if x != nil {
		return x.RouteType
	}
	return r
}

func (x *IntercityNewOrderParam) GetCarpoolSeatNum() (r int32) {
	if x != nil && x.CarpoolSeatNum != nil {
		return *x.CarpoolSeatNum
	}
	return r
}

func (x *IntercityNewOrderParam) SetProductCategory(v int64) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *IntercityNewOrderParam) SetComboId(v int64) {
	if x != nil {
		x.ComboId = v
	}
}

func (x *IntercityNewOrderParam) SetComboType(v int64) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *IntercityNewOrderParam) SetRequireLevel(v int64) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *IntercityNewOrderParam) SetBusinessId(v int64) {
	if x != nil {
		x.BusinessId = v
	}
}

func (x *IntercityNewOrderParam) SetPageType(v int32) {
	if x != nil {
		x.PageType = v
	}
}

func (x *IntercityNewOrderParam) SetRouteType(v int32) {
	if x != nil {
		x.RouteType = v
	}
}

func (x *IntercityNewOrderParam) SetCarpoolSeatNum(v int32) {
	if x != nil {
		x.CarpoolSeatNum = &v
	}
}

func (p *IntercityNewOrderParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityNewOrderParam(%+v)", *p)
}

type InterCityCarpoolBookingSku struct {
	BottomText string                `json:"bottom_text" form:"bottom_text"`
	SignalText string                `json:"signal_text" form:"signal_text"`
	TimeSpan   []*InterCityTimeRange `json:"time_span" form:"time_span"`
}

func (x *InterCityCarpoolBookingSku) GetBottomText() (r string) {
	if x != nil {
		return x.BottomText
	}
	return r
}

func (x *InterCityCarpoolBookingSku) GetSignalText() (r string) {
	if x != nil {
		return x.SignalText
	}
	return r
}

func (x *InterCityCarpoolBookingSku) GetTimeSpan() (r []*InterCityTimeRange) {
	if x != nil {
		return x.TimeSpan
	}
	return r
}

func (x *InterCityCarpoolBookingSku) SetBottomText(v string) {
	if x != nil {
		x.BottomText = v
	}
}

func (x *InterCityCarpoolBookingSku) SetSignalText(v string) {
	if x != nil {
		x.SignalText = v
	}
}

func (x *InterCityCarpoolBookingSku) SetTimeSpan(v []*InterCityTimeRange) {
	if x != nil {
		x.TimeSpan = v
	}
}

func (p *InterCityCarpoolBookingSku) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InterCityCarpoolBookingSku(%+v)", *p)
}

type IntercitySkuData struct {
	EstimateId          string                         `json:"estimate_id" form:"estimate_id"`                               //预估id
	CarIcon             string                         `json:"car_icon" form:"car_icon"`                                     //左边那个车图链接
	SubTitle            string                         `json:"sub_title" form:"sub_title"`                                   //提前预约 准时接驾
	IntroImage          string                         `json:"intro_image" form:"intro_image"`                               //这是定时发车的那张图的链接
	FeeMsg              string                         `json:"fee_msg" form:"fee_msg"`                                       //XX元
	FeeDescList         []*TagWithIconAndBorder        `json:"fee_desc_list" form:"fee_desc_list"`                           //券-xx元
	CarpoolSeatModule   *InterCityCarpoolSeatModule    `json:"carpool_seat_module,omitempty" form:"carpool_seat_module"`     //座位数
	ExtraMap            *IntercityNewOrderParam        `json:"extra_map,omitempty" form:"extra_map"`                         //发单信息集合
	FeeDetailUrl        string                         `json:"fee_detail_url" form:"fee_detail_url"`                         //费用详情页链接
	ConfirmButton       string                         `json:"confirm_button" form:"confirm_button"`                         //预约座位
	CarpoolBooking      *InterCityCarpoolBookingModule `json:"carpool_booking,omitempty" form:"carpool_booking"`             //弹起时间片相关
	CarpoolBookingOuter *InterCityCarpoolBookingSku    `json:"carpool_booking_outer,omitempty" form:"carpool_booking_outer"` //外露时间片相关
	UserPayInfo         *PaymentOptionModule           `json:"user_pay_info,omitempty" form:"user_pay_info"`                 //外露时间片相关
	SupportSelectSeat   int32                          `json:"support_select_seat" form:"support_select_seat"`               //是否支持选座弹层
}

func (x *IntercitySkuData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *IntercitySkuData) GetCarIcon() (r string) {
	if x != nil {
		return x.CarIcon
	}
	return r
}

func (x *IntercitySkuData) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *IntercitySkuData) GetIntroImage() (r string) {
	if x != nil {
		return x.IntroImage
	}
	return r
}

func (x *IntercitySkuData) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *IntercitySkuData) GetFeeDescList() (r []*TagWithIconAndBorder) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *IntercitySkuData) GetCarpoolSeatModule() (r *InterCityCarpoolSeatModule) {
	if x != nil {
		return x.CarpoolSeatModule
	}
	return r
}

func (x *IntercitySkuData) GetExtraMap() (r *IntercityNewOrderParam) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *IntercitySkuData) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *IntercitySkuData) GetConfirmButton() (r string) {
	if x != nil {
		return x.ConfirmButton
	}
	return r
}

func (x *IntercitySkuData) GetCarpoolBooking() (r *InterCityCarpoolBookingModule) {
	if x != nil {
		return x.CarpoolBooking
	}
	return r
}

func (x *IntercitySkuData) GetCarpoolBookingOuter() (r *InterCityCarpoolBookingSku) {
	if x != nil {
		return x.CarpoolBookingOuter
	}
	return r
}

func (x *IntercitySkuData) GetUserPayInfo() (r *PaymentOptionModule) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *IntercitySkuData) GetSupportSelectSeat() (r int32) {
	if x != nil {
		return x.SupportSelectSeat
	}
	return r
}

func (x *IntercitySkuData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *IntercitySkuData) SetCarIcon(v string) {
	if x != nil {
		x.CarIcon = v
	}
}

func (x *IntercitySkuData) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *IntercitySkuData) SetIntroImage(v string) {
	if x != nil {
		x.IntroImage = v
	}
}

func (x *IntercitySkuData) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *IntercitySkuData) SetFeeDescList(v []*TagWithIconAndBorder) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *IntercitySkuData) SetCarpoolSeatModule(v *InterCityCarpoolSeatModule) {
	if x != nil {
		x.CarpoolSeatModule = v
	}
}

func (x *IntercitySkuData) SetExtraMap(v *IntercityNewOrderParam) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *IntercitySkuData) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *IntercitySkuData) SetConfirmButton(v string) {
	if x != nil {
		x.ConfirmButton = v
	}
}

func (x *IntercitySkuData) SetCarpoolBooking(v *InterCityCarpoolBookingModule) {
	if x != nil {
		x.CarpoolBooking = v
	}
}

func (x *IntercitySkuData) SetCarpoolBookingOuter(v *InterCityCarpoolBookingSku) {
	if x != nil {
		x.CarpoolBookingOuter = v
	}
}

func (x *IntercitySkuData) SetUserPayInfo(v *PaymentOptionModule) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *IntercitySkuData) SetSupportSelectSeat(v int32) {
	if x != nil {
		x.SupportSelectSeat = v
	}
}

func (p *IntercitySkuData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercitySkuData(%+v)", *p)
}

type Station struct {
	Content     string `json:"content" form:"content"`           //文案
	Id          int32  `json:"id" form:"id"`                     //站点id
	Lat         string `json:"lat" form:"lat"`                   //维度
	Lng         string `json:"lng" form:"lng"`                   //经度
	DisplayName string `json:"display_name" form:"display_name"` //地图上展示的名字
	Distance    string `json:"distance" form:"distance"`         //距起终点XXm
}

func (x *Station) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *Station) GetId() (r int32) {
	if x != nil {
		return x.Id
	}
	return r
}

func (x *Station) GetLat() (r string) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *Station) GetLng() (r string) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *Station) GetDisplayName() (r string) {
	if x != nil {
		return x.DisplayName
	}
	return r
}

func (x *Station) GetDistance() (r string) {
	if x != nil {
		return x.Distance
	}
	return r
}

func (x *Station) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (x *Station) SetId(v int32) {
	if x != nil {
		x.Id = v
	}
}

func (x *Station) SetLat(v string) {
	if x != nil {
		x.Lat = v
	}
}

func (x *Station) SetLng(v string) {
	if x != nil {
		x.Lng = v
	}
}

func (x *Station) SetDisplayName(v string) {
	if x != nil {
		x.DisplayName = v
	}
}

func (x *Station) SetDistance(v string) {
	if x != nil {
		x.Distance = v
	}
}

func (p *Station) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Station(%+v)", *p)
}

type BaseStation struct {
	Tag         string `json:"tag" form:"tag"`                   //图片链接
	Name        string `json:"name" form:"name"`                 //站点展示文案
	Lat         string `json:"lat" form:"lat"`                   //维度
	Lng         string `json:"lng" form:"lng"`                   //经度
	DisplayName string `json:"display_name" form:"display_name"` //地图上展示的名字
	Distance    string `json:"distance" form:"distance"`         //距起终点XXm
	Icon        string `json:"icon" form:"icon"`                 //圆圈链接
	Id          int32  `json:"id" form:"id"`                     //站点id
	Type        int32  `json:"type" form:"type"`                 //1标识上车站点 2 标识下车站点
}

func (x *BaseStation) GetTag() (r string) {
	if x != nil {
		return x.Tag
	}
	return r
}

func (x *BaseStation) GetName() (r string) {
	if x != nil {
		return x.Name
	}
	return r
}

func (x *BaseStation) GetLat() (r string) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *BaseStation) GetLng() (r string) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *BaseStation) GetDisplayName() (r string) {
	if x != nil {
		return x.DisplayName
	}
	return r
}

func (x *BaseStation) GetDistance() (r string) {
	if x != nil {
		return x.Distance
	}
	return r
}

func (x *BaseStation) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *BaseStation) GetId() (r int32) {
	if x != nil {
		return x.Id
	}
	return r
}

func (x *BaseStation) GetType() (r int32) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *BaseStation) SetTag(v string) {
	if x != nil {
		x.Tag = v
	}
}

func (x *BaseStation) SetName(v string) {
	if x != nil {
		x.Name = v
	}
}

func (x *BaseStation) SetLat(v string) {
	if x != nil {
		x.Lat = v
	}
}

func (x *BaseStation) SetLng(v string) {
	if x != nil {
		x.Lng = v
	}
}

func (x *BaseStation) SetDisplayName(v string) {
	if x != nil {
		x.DisplayName = v
	}
}

func (x *BaseStation) SetDistance(v string) {
	if x != nil {
		x.Distance = v
	}
}

func (x *BaseStation) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *BaseStation) SetId(v int32) {
	if x != nil {
		x.Id = v
	}
}

func (x *BaseStation) SetType(v int32) {
	if x != nil {
		x.Type = v
	}
}

func (p *BaseStation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BaseStation(%+v)", *p)
}

type StationDetail struct {
	Title       string         `json:"title" form:"title"`               //主标题
	Icon        string         `json:"icon" form:"icon"`                 //主标题左边图片里阿尼额
	StationList []*BaseStation `json:"station_list" form:"station_list"` //站点列表
}

func (x *StationDetail) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *StationDetail) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *StationDetail) GetStationList() (r []*BaseStation) {
	if x != nil {
		return x.StationList
	}
	return r
}

func (x *StationDetail) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *StationDetail) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *StationDetail) SetStationList(v []*BaseStation) {
	if x != nil {
		x.StationList = v
	}
}

func (p *StationDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StationDetail(%+v)", *p)
}

type StationInfo struct {
	Start       *Station       `json:"start,omitempty" form:"start"`               //起点命中站点信息
	End         *Station       `json:"end,omitempty" form:"end"`                   //终点命中站点信息
	Detail      *StationDetail `json:"detail,omitempty" form:"detail"`             //全部站点弹层内部信息
	EstimateMsg *string        `json:"estimate_msg,omitempty" form:"estimate_msg"` //预计站点间的站点数和时间
}

func (x *StationInfo) GetStart() (r *Station) {
	if x != nil {
		return x.Start
	}
	return r
}

func (x *StationInfo) GetEnd() (r *Station) {
	if x != nil {
		return x.End
	}
	return r
}

func (x *StationInfo) GetDetail() (r *StationDetail) {
	if x != nil {
		return x.Detail
	}
	return r
}

func (x *StationInfo) GetEstimateMsg() (r string) {
	if x != nil && x.EstimateMsg != nil {
		return *x.EstimateMsg
	}
	return r
}

func (x *StationInfo) SetStart(v *Station) {
	if x != nil {
		x.Start = v
	}
}

func (x *StationInfo) SetEnd(v *Station) {
	if x != nil {
		x.End = v
	}
}

func (x *StationInfo) SetDetail(v *StationDetail) {
	if x != nil {
		x.Detail = v
	}
}

func (x *StationInfo) SetEstimateMsg(v string) {
	if x != nil {
		x.EstimateMsg = &v
	}
}

func (p *StationInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StationInfo(%+v)", *p)
}

type IntercitySkuStationData struct {
	EstimateId              string                         `json:"estimate_id" form:"estimate_id"`                                         //预估id
	CarIcon                 string                         `json:"car_icon" form:"car_icon"`                                               //左边那个车图链接
	SubIcon                 string                         `json:"sub_icon" form:"sub_icon"`                                               //副标题左边的图片链接
	SubTitle                string                         `json:"sub_title" form:"sub_title"`                                             //副标题：粤运集团承运
	IntroImage              string                         `json:"intro_image" form:"intro_image"`                                         //这是站点巴士的那张图的链接
	FeeMsg                  string                         `json:"fee_msg" form:"fee_msg"`                                                 //XX元
	RightIcon               string                         `json:"right_icon" form:"right_icon"`                                           //右侧图片链接
	RightTag                string                         `json:"right_tag" form:"right_tag"`                                             //右侧图片下方文案
	FeeDescList             []*TagWithIconAndBorder        `json:"fee_desc_list" form:"fee_desc_list"`                                     //券-xx元
	CarpoolSeatModule       *InterCityCarpoolSeatModule    `json:"carpool_seat_module,omitempty" form:"carpool_seat_module"`               //座位数
	ExtraMap                *IntercityNewOrderParam        `json:"extra_map,omitempty" form:"extra_map"`                                   //发单信息集合
	FeeDetailUrl            string                         `json:"fee_detail_url" form:"fee_detail_url"`                                   //费用详情页链接
	StationInfo             *StationInfo                   `json:"station_info,omitempty" form:"station_info"`                             //站点信息
	ConfirmButton           string                         `json:"confirm_button" form:"confirm_button"`                                   //预约座位
	ConfirmSubText          string                         `json:"confirm_sub_text" form:"confirm_sub_text"`                               //按钮二行小字
	CarpoolBooking          *InterCityCarpoolBookingModule `json:"carpool_booking,omitempty" form:"carpool_booking"`                       //弹起时间片相关
	CarpoolBookingOuter     *InterCityCarpoolBookingSku    `json:"carpool_booking_outer,omitempty" form:"carpool_booking_outer"`           //外露时间片相关
	UserPayInfo             *PaymentOptionModule           `json:"user_pay_info,omitempty" form:"user_pay_info"`                           //外露时间片相关
	SupportSelectSeat       int32                          `json:"support_select_seat" form:"support_select_seat"`                         //是否支持选座弹层
	IsSelected              bool                           `json:"is_selected" form:"is_selected"`                                         //卡片是否被勾选
	StyleType               StyleType                      `json:"style_type" form:"style_type"`                                           //渲染样式
	BorderColor             string                         `json:"border_color" form:"border_color"`                                       //选中态包框颜色
	CornerImage             string                         `json:"corner_image" form:"corner_image"`                                       //选中态角标样式
	HitShowH5Type           int32                          `json:"hit_show_h5_type" form:"hit_show_h5_type"`                               //h5跳转 春节服务费等
	LoadingButton           *string                        `json:"loading_button,omitempty" form:"loading_button"`                         //确认后加载中的按钮
	RebookFee               string                         `json:"rebook_fee" form:"rebook_fee"`                                           //改签费用
	ServiceInfo             *IntercityServiceInfo          `json:"service_info,omitempty" form:"service_info"`                             //服务商信息，也就是城际anycar品类信息
	IsSupportMultiSelection *int32                         `json:"is_support_multi_selection,omitempty" form:"is_support_multi_selection"` //是否支持多选，预约单时不支持 0:不支持；1:支持
}

func (x *IntercitySkuStationData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *IntercitySkuStationData) GetCarIcon() (r string) {
	if x != nil {
		return x.CarIcon
	}
	return r
}

func (x *IntercitySkuStationData) GetSubIcon() (r string) {
	if x != nil {
		return x.SubIcon
	}
	return r
}

func (x *IntercitySkuStationData) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *IntercitySkuStationData) GetIntroImage() (r string) {
	if x != nil {
		return x.IntroImage
	}
	return r
}

func (x *IntercitySkuStationData) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *IntercitySkuStationData) GetRightIcon() (r string) {
	if x != nil {
		return x.RightIcon
	}
	return r
}

func (x *IntercitySkuStationData) GetRightTag() (r string) {
	if x != nil {
		return x.RightTag
	}
	return r
}

func (x *IntercitySkuStationData) GetFeeDescList() (r []*TagWithIconAndBorder) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *IntercitySkuStationData) GetCarpoolSeatModule() (r *InterCityCarpoolSeatModule) {
	if x != nil {
		return x.CarpoolSeatModule
	}
	return r
}

func (x *IntercitySkuStationData) GetExtraMap() (r *IntercityNewOrderParam) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *IntercitySkuStationData) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *IntercitySkuStationData) GetStationInfo() (r *StationInfo) {
	if x != nil {
		return x.StationInfo
	}
	return r
}

func (x *IntercitySkuStationData) GetConfirmButton() (r string) {
	if x != nil {
		return x.ConfirmButton
	}
	return r
}

func (x *IntercitySkuStationData) GetConfirmSubText() (r string) {
	if x != nil {
		return x.ConfirmSubText
	}
	return r
}

func (x *IntercitySkuStationData) GetCarpoolBooking() (r *InterCityCarpoolBookingModule) {
	if x != nil {
		return x.CarpoolBooking
	}
	return r
}

func (x *IntercitySkuStationData) GetCarpoolBookingOuter() (r *InterCityCarpoolBookingSku) {
	if x != nil {
		return x.CarpoolBookingOuter
	}
	return r
}

func (x *IntercitySkuStationData) GetUserPayInfo() (r *PaymentOptionModule) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *IntercitySkuStationData) GetSupportSelectSeat() (r int32) {
	if x != nil {
		return x.SupportSelectSeat
	}
	return r
}

func (x *IntercitySkuStationData) GetIsSelected() (r bool) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *IntercitySkuStationData) GetStyleType() (r StyleType) {
	if x != nil {
		return x.StyleType
	}
	return r
}

func (x *IntercitySkuStationData) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *IntercitySkuStationData) GetCornerImage() (r string) {
	if x != nil {
		return x.CornerImage
	}
	return r
}

func (x *IntercitySkuStationData) GetHitShowH5Type() (r int32) {
	if x != nil {
		return x.HitShowH5Type
	}
	return r
}

func (x *IntercitySkuStationData) GetLoadingButton() (r string) {
	if x != nil && x.LoadingButton != nil {
		return *x.LoadingButton
	}
	return r
}

func (x *IntercitySkuStationData) GetRebookFee() (r string) {
	if x != nil {
		return x.RebookFee
	}
	return r
}

func (x *IntercitySkuStationData) GetServiceInfo() (r *IntercityServiceInfo) {
	if x != nil {
		return x.ServiceInfo
	}
	return r
}

func (x *IntercitySkuStationData) GetIsSupportMultiSelection() (r int32) {
	if x != nil && x.IsSupportMultiSelection != nil {
		return *x.IsSupportMultiSelection
	}
	return r
}

func (x *IntercitySkuStationData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *IntercitySkuStationData) SetCarIcon(v string) {
	if x != nil {
		x.CarIcon = v
	}
}

func (x *IntercitySkuStationData) SetSubIcon(v string) {
	if x != nil {
		x.SubIcon = v
	}
}

func (x *IntercitySkuStationData) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *IntercitySkuStationData) SetIntroImage(v string) {
	if x != nil {
		x.IntroImage = v
	}
}

func (x *IntercitySkuStationData) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *IntercitySkuStationData) SetRightIcon(v string) {
	if x != nil {
		x.RightIcon = v
	}
}

func (x *IntercitySkuStationData) SetRightTag(v string) {
	if x != nil {
		x.RightTag = v
	}
}

func (x *IntercitySkuStationData) SetFeeDescList(v []*TagWithIconAndBorder) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *IntercitySkuStationData) SetCarpoolSeatModule(v *InterCityCarpoolSeatModule) {
	if x != nil {
		x.CarpoolSeatModule = v
	}
}

func (x *IntercitySkuStationData) SetExtraMap(v *IntercityNewOrderParam) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *IntercitySkuStationData) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *IntercitySkuStationData) SetStationInfo(v *StationInfo) {
	if x != nil {
		x.StationInfo = v
	}
}

func (x *IntercitySkuStationData) SetConfirmButton(v string) {
	if x != nil {
		x.ConfirmButton = v
	}
}

func (x *IntercitySkuStationData) SetConfirmSubText(v string) {
	if x != nil {
		x.ConfirmSubText = v
	}
}

func (x *IntercitySkuStationData) SetCarpoolBooking(v *InterCityCarpoolBookingModule) {
	if x != nil {
		x.CarpoolBooking = v
	}
}

func (x *IntercitySkuStationData) SetCarpoolBookingOuter(v *InterCityCarpoolBookingSku) {
	if x != nil {
		x.CarpoolBookingOuter = v
	}
}

func (x *IntercitySkuStationData) SetUserPayInfo(v *PaymentOptionModule) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *IntercitySkuStationData) SetSupportSelectSeat(v int32) {
	if x != nil {
		x.SupportSelectSeat = v
	}
}

func (x *IntercitySkuStationData) SetIsSelected(v bool) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *IntercitySkuStationData) SetStyleType(v StyleType) {
	if x != nil {
		x.StyleType = v
	}
}

func (x *IntercitySkuStationData) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *IntercitySkuStationData) SetCornerImage(v string) {
	if x != nil {
		x.CornerImage = v
	}
}

func (x *IntercitySkuStationData) SetHitShowH5Type(v int32) {
	if x != nil {
		x.HitShowH5Type = v
	}
}

func (x *IntercitySkuStationData) SetLoadingButton(v string) {
	if x != nil {
		x.LoadingButton = &v
	}
}

func (x *IntercitySkuStationData) SetRebookFee(v string) {
	if x != nil {
		x.RebookFee = v
	}
}

func (x *IntercitySkuStationData) SetServiceInfo(v *IntercityServiceInfo) {
	if x != nil {
		x.ServiceInfo = v
	}
}

func (x *IntercitySkuStationData) SetIsSupportMultiSelection(v int32) {
	if x != nil {
		x.IsSupportMultiSelection = &v
	}
}

func (p *IntercitySkuStationData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercitySkuStationData(%+v)", *p)
}

// 请求
type CarpoolInvitationEstimateRequest struct {
	Token           string   `json:"token" form:"token"`                 //用户认证token
	UserType        UserType `json:"user_type" form:"user_type"`         //1普通用户；2企业用户
	AppVersion      string   `json:"app_version" form:"app_version"`     //端版本
	AccessKeyId     int32    `json:"access_key_id" form:"access_key_id"` //端来源
	Channel         string   `json:"channel" form:"channel"`             //渠道号
	ClientType      int32    `json:"client_type" form:"client_type"`     //端类型
	Lang            string   `json:"lang" form:"lang"`                   //端语种
	PlatformType    int32    `json:"platform_type" form:"platform_type"` //端(平台)类型-2
	PaymentsType    int32    `json:"payments_type" form:"payments_type"` //选择的支付方式
	InviteOid       string   `json:"invite_oid" form:"invite_oid"`       //邀约者加密订单号
	MapType         string   `json:"map_type" form:"map_type"`           //地图类型
	Lat             float64  `json:"lat" form:"lat"`                     //定位点
	Lng             float64  `json:"lng" form:"lng"`                     //定位点
	FromLat         float64  `json:"from_lat" form:"from_lat"`           //起点
	FromLng         float64  `json:"from_lng" form:"from_lng"`           //起点
	FromPoiId       string   `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType     string   `json:"from_poi_type" form:"from_poi_type"`
	FromAddress     string   `json:"from_address" form:"from_address"`
	FromName        string   `json:"from_name" form:"from_name"`
	ChooseFSearchid string   `json:"choose_f_searchid" form:"choose_f_searchid"` //用户选择起点请求ID
	ToLat           float64  `json:"to_lat" form:"to_lat"`                       //终点
	ToLng           float64  `json:"to_lng" form:"to_lng"`                       //终点
	ToPoiId         string   `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType       string   `json:"to_poi_type" form:"to_poi_type"`
	ToAddress       string   `json:"to_address" form:"to_address"`
	ToName          string   `json:"to_name" form:"to_name"`
	ChooseTSearchid string   `json:"choose_t_searchid" form:"choose_t_searchid"` //用户选择终点请求ID
	DepartureRange  string   `json:"departure_range" form:"departure_range"`     //选择的出发时间段
	CarpoolSeatNum  int32    `json:"carpool_seat_num" form:"carpool_seat_num"`   //选择的座位数
	EstimateType    int32    `json:"estimate_type" form:"estimate_type"`         //0:普通预估 & 1：缓存预估
	Xpsid           *string  `json:"xpsid,omitempty" form:"xpsid"`
	XpsidRoot       *string  `json:"xpsid_root,omitempty" form:"xpsid_root"`
}

func (x *CarpoolInvitationEstimateRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetUserType() (r UserType) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetChannel() (r string) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetPlatformType() (r int32) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetPaymentsType() (r int32) {
	if x != nil {
		return x.PaymentsType
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetInviteOid() (r string) {
	if x != nil {
		return x.InviteOid
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetChooseFSearchid() (r string) {
	if x != nil {
		return x.ChooseFSearchid
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetToPoiType() (r string) {
	if x != nil {
		return x.ToPoiType
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetChooseTSearchid() (r string) {
	if x != nil {
		return x.ChooseTSearchid
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetDepartureRange() (r string) {
	if x != nil {
		return x.DepartureRange
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetCarpoolSeatNum() (r int32) {
	if x != nil {
		return x.CarpoolSeatNum
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetEstimateType() (r int32) {
	if x != nil {
		return x.EstimateType
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetXpsid() (r string) {
	if x != nil && x.Xpsid != nil {
		return *x.Xpsid
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) GetXpsidRoot() (r string) {
	if x != nil && x.XpsidRoot != nil {
		return *x.XpsidRoot
	}
	return r
}

func (x *CarpoolInvitationEstimateRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetUserType(v UserType) {
	if x != nil {
		x.UserType = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetChannel(v string) {
	if x != nil {
		x.Channel = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetPaymentsType(v int32) {
	if x != nil {
		x.PaymentsType = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetInviteOid(v string) {
	if x != nil {
		x.InviteOid = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetChooseFSearchid(v string) {
	if x != nil {
		x.ChooseFSearchid = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetChooseTSearchid(v string) {
	if x != nil {
		x.ChooseTSearchid = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetDepartureRange(v string) {
	if x != nil {
		x.DepartureRange = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetCarpoolSeatNum(v int32) {
	if x != nil {
		x.CarpoolSeatNum = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetEstimateType(v int32) {
	if x != nil {
		x.EstimateType = v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = &v
	}
}

func (x *CarpoolInvitationEstimateRequest) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = &v
	}
}

func (p *CarpoolInvitationEstimateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolInvitationEstimateRequest(%+v)", *p)
}

// 响应
type CarpoolInvitationEstimateResp struct {
	Errno   int32                   `json:"errno" form:"errno"`
	Errmsg  string                  `json:"errmsg" form:"errmsg"`
	TraceId string                  `json:"trace_id" form:"trace_id"`
	Data    *InvitationEstimateData `json:"data,omitempty" form:"data"`
}

func (x *CarpoolInvitationEstimateResp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *CarpoolInvitationEstimateResp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *CarpoolInvitationEstimateResp) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *CarpoolInvitationEstimateResp) GetData() (r *InvitationEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *CarpoolInvitationEstimateResp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *CarpoolInvitationEstimateResp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *CarpoolInvitationEstimateResp) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (x *CarpoolInvitationEstimateResp) SetData(v *InvitationEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (p *CarpoolInvitationEstimateResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolInvitationEstimateResp(%+v)", *p)
}

type InvitationEstimateData struct {
	EstimateId        string                      `json:"estimate_id" form:"estimate_id"`
	Title             string                      `json:"title" form:"title"`
	SubTitle          string                      `json:"sub_title" form:"sub_title"`
	HeadImg           string                      `json:"head_img" form:"head_img"`
	IntroMsg          string                      `json:"intro_msg" form:"intro_msg"`
	IntroUrl          string                      `json:"intro_url" form:"intro_url"`
	MatchRateIcon     string                      `json:"match_rate_icon" form:"match_rate_icon"`
	MatchRateMsg      string                      `json:"match_rate_msg" form:"match_rate_msg"`
	EstimateTips      string                      `json:"estimate_tips" form:"estimate_tips"`
	MultiPriceDesc    []string                    `json:"multi_price_desc,omitempty" form:"multi_price_desc"`
	TravelInfo        *InvitationTravelData       `json:"travel_info,omitempty" form:"travel_info"`
	CarpoolSeatModule *InvitationSeatOptionModule `json:"carpool_seat_module,omitempty" form:"carpool_seat_module"`
	ExtraMap          *PinchecheNewOrderParam     `json:"extra_map,omitempty" form:"extra_map"`
	CarpoolBooking    *InvitationBookingModule    `json:"carpool_booking,omitempty" form:"carpool_booking"`
	FeeDetailUrl      string                      `json:"fee_detail_url" form:"fee_detail_url"`
	ButtonList        []*ButtonItem               `json:"button_list,omitempty" form:"button_list"`
	PassThrough       map[string]interface{}      `json:"pass_through" form:"pass_through"`       //透传数据
	OmegaInfo         map[string]interface{}      `json:"omega_info,omitempty" form:"omega_info"` //透传数据
}

func (x *InvitationEstimateData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *InvitationEstimateData) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *InvitationEstimateData) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *InvitationEstimateData) GetHeadImg() (r string) {
	if x != nil {
		return x.HeadImg
	}
	return r
}

func (x *InvitationEstimateData) GetIntroMsg() (r string) {
	if x != nil {
		return x.IntroMsg
	}
	return r
}

func (x *InvitationEstimateData) GetIntroUrl() (r string) {
	if x != nil {
		return x.IntroUrl
	}
	return r
}

func (x *InvitationEstimateData) GetMatchRateIcon() (r string) {
	if x != nil {
		return x.MatchRateIcon
	}
	return r
}

func (x *InvitationEstimateData) GetMatchRateMsg() (r string) {
	if x != nil {
		return x.MatchRateMsg
	}
	return r
}

func (x *InvitationEstimateData) GetEstimateTips() (r string) {
	if x != nil {
		return x.EstimateTips
	}
	return r
}

func (x *InvitationEstimateData) GetMultiPriceDesc() (r []string) {
	if x != nil {
		return x.MultiPriceDesc
	}
	return r
}

func (x *InvitationEstimateData) GetTravelInfo() (r *InvitationTravelData) {
	if x != nil {
		return x.TravelInfo
	}
	return r
}

func (x *InvitationEstimateData) GetCarpoolSeatModule() (r *InvitationSeatOptionModule) {
	if x != nil {
		return x.CarpoolSeatModule
	}
	return r
}

func (x *InvitationEstimateData) GetExtraMap() (r *PinchecheNewOrderParam) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *InvitationEstimateData) GetCarpoolBooking() (r *InvitationBookingModule) {
	if x != nil {
		return x.CarpoolBooking
	}
	return r
}

func (x *InvitationEstimateData) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *InvitationEstimateData) GetButtonList() (r []*ButtonItem) {
	if x != nil {
		return x.ButtonList
	}
	return r
}

func (x *InvitationEstimateData) GetPassThrough() (r map[string]interface{}) {
	if x != nil {
		return x.PassThrough
	}
	return r
}

func (x *InvitationEstimateData) GetOmegaInfo() (r map[string]interface{}) {
	if x != nil {
		return x.OmegaInfo
	}
	return r
}

func (x *InvitationEstimateData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *InvitationEstimateData) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *InvitationEstimateData) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *InvitationEstimateData) SetHeadImg(v string) {
	if x != nil {
		x.HeadImg = v
	}
}

func (x *InvitationEstimateData) SetIntroMsg(v string) {
	if x != nil {
		x.IntroMsg = v
	}
}

func (x *InvitationEstimateData) SetIntroUrl(v string) {
	if x != nil {
		x.IntroUrl = v
	}
}

func (x *InvitationEstimateData) SetMatchRateIcon(v string) {
	if x != nil {
		x.MatchRateIcon = v
	}
}

func (x *InvitationEstimateData) SetMatchRateMsg(v string) {
	if x != nil {
		x.MatchRateMsg = v
	}
}

func (x *InvitationEstimateData) SetEstimateTips(v string) {
	if x != nil {
		x.EstimateTips = v
	}
}

func (x *InvitationEstimateData) SetMultiPriceDesc(v []string) {
	if x != nil {
		x.MultiPriceDesc = v
	}
}

func (x *InvitationEstimateData) SetTravelInfo(v *InvitationTravelData) {
	if x != nil {
		x.TravelInfo = v
	}
}

func (x *InvitationEstimateData) SetCarpoolSeatModule(v *InvitationSeatOptionModule) {
	if x != nil {
		x.CarpoolSeatModule = v
	}
}

func (x *InvitationEstimateData) SetExtraMap(v *PinchecheNewOrderParam) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *InvitationEstimateData) SetCarpoolBooking(v *InvitationBookingModule) {
	if x != nil {
		x.CarpoolBooking = v
	}
}

func (x *InvitationEstimateData) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *InvitationEstimateData) SetButtonList(v []*ButtonItem) {
	if x != nil {
		x.ButtonList = v
	}
}

func (x *InvitationEstimateData) SetPassThrough(v map[string]interface{}) {
	if x != nil {
		x.PassThrough = v
	}
}

func (x *InvitationEstimateData) SetOmegaInfo(v map[string]interface{}) {
	if x != nil {
		x.OmegaInfo = v
	}
}

func (p *InvitationEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InvitationEstimateData(%+v)", *p)
}

type InvitationSeatOptionModule struct {
	Title          string               `json:"title" form:"title"`
	OptionList     []*CarpoolSeatOption `json:"option_list" form:"option_list"`
	SeatsExceedMsg string               `json:"seats_exceed_msg" form:"seats_exceed_msg"`
}

func (x *InvitationSeatOptionModule) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *InvitationSeatOptionModule) GetOptionList() (r []*CarpoolSeatOption) {
	if x != nil {
		return x.OptionList
	}
	return r
}

func (x *InvitationSeatOptionModule) GetSeatsExceedMsg() (r string) {
	if x != nil {
		return x.SeatsExceedMsg
	}
	return r
}

func (x *InvitationSeatOptionModule) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *InvitationSeatOptionModule) SetOptionList(v []*CarpoolSeatOption) {
	if x != nil {
		x.OptionList = v
	}
}

func (x *InvitationSeatOptionModule) SetSeatsExceedMsg(v string) {
	if x != nil {
		x.SeatsExceedMsg = v
	}
}

func (p *InvitationSeatOptionModule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InvitationSeatOptionModule(%+v)", *p)
}

type InvitationTravelData struct {
	DepartureText string `json:"departure_text" form:"departure_text"`
	StartName     string `json:"start_name" form:"start_name"`
	DestName      string `json:"dest_name" form:"dest_name"`
}

func (x *InvitationTravelData) GetDepartureText() (r string) {
	if x != nil {
		return x.DepartureText
	}
	return r
}

func (x *InvitationTravelData) GetStartName() (r string) {
	if x != nil {
		return x.StartName
	}
	return r
}

func (x *InvitationTravelData) GetDestName() (r string) {
	if x != nil {
		return x.DestName
	}
	return r
}

func (x *InvitationTravelData) SetDepartureText(v string) {
	if x != nil {
		x.DepartureText = v
	}
}

func (x *InvitationTravelData) SetStartName(v string) {
	if x != nil {
		x.StartName = v
	}
}

func (x *InvitationTravelData) SetDestName(v string) {
	if x != nil {
		x.DestName = v
	}
}

func (p *InvitationTravelData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InvitationTravelData(%+v)", *p)
}

type ButtonItem struct {
	Type       int32                  `json:"type" form:"type"`
	Icon       string                 `json:"icon" form:"icon"`
	Content    string                 `json:"content" form:"content"`
	StartColor string                 `json:"start_color" form:"start_color"`
	EndColor   string                 `json:"end_color" form:"end_color"`
	Params     map[string]interface{} `json:"params,omitempty" form:"params"`
}

func (x *ButtonItem) GetType() (r int32) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *ButtonItem) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *ButtonItem) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *ButtonItem) GetStartColor() (r string) {
	if x != nil {
		return x.StartColor
	}
	return r
}

func (x *ButtonItem) GetEndColor() (r string) {
	if x != nil {
		return x.EndColor
	}
	return r
}

func (x *ButtonItem) GetParams() (r map[string]interface{}) {
	if x != nil {
		return x.Params
	}
	return r
}

func (x *ButtonItem) SetType(v int32) {
	if x != nil {
		x.Type = v
	}
}

func (x *ButtonItem) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *ButtonItem) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (x *ButtonItem) SetStartColor(v string) {
	if x != nil {
		x.StartColor = v
	}
}

func (x *ButtonItem) SetEndColor(v string) {
	if x != nil {
		x.EndColor = v
	}
}

func (x *ButtonItem) SetParams(v map[string]interface{}) {
	if x != nil {
		x.Params = v
	}
}

func (p *ButtonItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ButtonItem(%+v)", *p)
}

type InvitationBookingModule struct {
	Title      string               `json:"title" form:"title"`
	Subtitle   string               `json:"subtitle" form:"subtitle"`
	TimeSpan   []*PinchecheTimeSpan `json:"time_span" form:"time_span"`
	ButtonText string               `json:"button_text" form:"button_text"` //确认时间片的按钮文案
	BottomText string               `json:"bottom_text" form:"bottom_text"` //"不修改，仍和拼友同时出发"
}

func (x *InvitationBookingModule) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *InvitationBookingModule) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *InvitationBookingModule) GetTimeSpan() (r []*PinchecheTimeSpan) {
	if x != nil {
		return x.TimeSpan
	}
	return r
}

func (x *InvitationBookingModule) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *InvitationBookingModule) GetBottomText() (r string) {
	if x != nil {
		return x.BottomText
	}
	return r
}

func (x *InvitationBookingModule) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *InvitationBookingModule) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *InvitationBookingModule) SetTimeSpan(v []*PinchecheTimeSpan) {
	if x != nil {
		x.TimeSpan = v
	}
}

func (x *InvitationBookingModule) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (x *InvitationBookingModule) SetBottomText(v string) {
	if x != nil {
		x.BottomText = v
	}
}

func (p *InvitationBookingModule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InvitationBookingModule(%+v)", *p)
}

type PEstimateCarpoolOrderRequest struct {
	DriverId           string                         `json:"driver_id" form:"driver_id"`
	TravelId           string                         `json:"travel_id" form:"travel_id"`
	OrderList          []string                       `json:"order_list" form:"order_list"`
	CarpoolSuccessInfo map[string]*CarpoolSuccessInfo `json:"carpool_success_info" form:"carpool_success_info"`
}

func (x *PEstimateCarpoolOrderRequest) GetDriverId() (r string) {
	if x != nil {
		return x.DriverId
	}
	return r
}

func (x *PEstimateCarpoolOrderRequest) GetTravelId() (r string) {
	if x != nil {
		return x.TravelId
	}
	return r
}

func (x *PEstimateCarpoolOrderRequest) GetOrderList() (r []string) {
	if x != nil {
		return x.OrderList
	}
	return r
}

func (x *PEstimateCarpoolOrderRequest) GetCarpoolSuccessInfo() (r map[string]*CarpoolSuccessInfo) {
	if x != nil {
		return x.CarpoolSuccessInfo
	}
	return r
}

func (x *PEstimateCarpoolOrderRequest) SetDriverId(v string) {
	if x != nil {
		x.DriverId = v
	}
}

func (x *PEstimateCarpoolOrderRequest) SetTravelId(v string) {
	if x != nil {
		x.TravelId = v
	}
}

func (x *PEstimateCarpoolOrderRequest) SetOrderList(v []string) {
	if x != nil {
		x.OrderList = v
	}
}

func (x *PEstimateCarpoolOrderRequest) SetCarpoolSuccessInfo(v map[string]*CarpoolSuccessInfo) {
	if x != nil {
		x.CarpoolSuccessInfo = v
	}
}

func (p *PEstimateCarpoolOrderRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PEstimateCarpoolOrderRequest(%+v)", *p)
}

type PEstimateCarpoolOrderResponse struct {
	Errno           int32                             `json:"errno" form:"errno"`
	Errmsg          string                            `json:"errmsg" form:"errmsg"`
	TraceId         string                            `json:"trace_id" form:"trace_id"`
	CarpoolDescInfo map[string]*CarpoolDescInfo       `json:"carpool_desc_info,omitempty" form:"carpool_desc_info"`
	DiscountInfo    map[string][]*CarpoolDiscountInfo `json:"discount_info,omitempty" form:"discount_info"`
}

func (x *PEstimateCarpoolOrderResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *PEstimateCarpoolOrderResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *PEstimateCarpoolOrderResponse) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *PEstimateCarpoolOrderResponse) GetCarpoolDescInfo() (r map[string]*CarpoolDescInfo) {
	if x != nil {
		return x.CarpoolDescInfo
	}
	return r
}

func (x *PEstimateCarpoolOrderResponse) GetDiscountInfo() (r map[string][]*CarpoolDiscountInfo) {
	if x != nil {
		return x.DiscountInfo
	}
	return r
}

func (x *PEstimateCarpoolOrderResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *PEstimateCarpoolOrderResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *PEstimateCarpoolOrderResponse) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (x *PEstimateCarpoolOrderResponse) SetCarpoolDescInfo(v map[string]*CarpoolDescInfo) {
	if x != nil {
		x.CarpoolDescInfo = v
	}
}

func (x *PEstimateCarpoolOrderResponse) SetDiscountInfo(v map[string][]*CarpoolDiscountInfo) {
	if x != nil {
		x.DiscountInfo = v
	}
}

func (p *PEstimateCarpoolOrderResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PEstimateCarpoolOrderResponse(%+v)", *p)
}

type CarpoolDescInfo struct {
	Title    string  `json:"title" form:"title"`
	SubTitle *string `json:"sub_title,omitempty" form:"sub_title"`
}

func (x *CarpoolDescInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *CarpoolDescInfo) GetSubTitle() (r string) {
	if x != nil && x.SubTitle != nil {
		return *x.SubTitle
	}
	return r
}

func (x *CarpoolDescInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *CarpoolDescInfo) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = &v
	}
}

func (p *CarpoolDescInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolDescInfo(%+v)", *p)
}

type DescSubTitle struct {
	Title string `json:"title" form:"title"`
	Icon  string `json:"icon" form:"icon"`
}

func (x *DescSubTitle) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *DescSubTitle) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *DescSubTitle) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *DescSubTitle) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (p *DescSubTitle) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescSubTitle(%+v)", *p)
}

type CarpoolDiscountInfo struct {
	DiscountType   string `json:"discount_type" form:"discount_type"`
	DiscountAmount int32  `json:"discount_amount" form:"discount_amount"`
}

func (x *CarpoolDiscountInfo) GetDiscountType() (r string) {
	if x != nil {
		return x.DiscountType
	}
	return r
}

func (x *CarpoolDiscountInfo) GetDiscountAmount() (r int32) {
	if x != nil {
		return x.DiscountAmount
	}
	return r
}

func (x *CarpoolDiscountInfo) SetDiscountType(v string) {
	if x != nil {
		x.DiscountType = v
	}
}

func (x *CarpoolDiscountInfo) SetDiscountAmount(v int32) {
	if x != nil {
		x.DiscountAmount = v
	}
}

func (p *CarpoolDiscountInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolDiscountInfo(%+v)", *p)
}

type CarpoolSuccessInfo struct {
	IsCarpoolSuccess bool  `json:"is_carpool_success" form:"is_carpool_success"`
	ChargePoolNum    int32 `json:"charge_pool_num" form:"charge_pool_num"`
}

func (x *CarpoolSuccessInfo) GetIsCarpoolSuccess() (r bool) {
	if x != nil {
		return x.IsCarpoolSuccess
	}
	return r
}

func (x *CarpoolSuccessInfo) GetChargePoolNum() (r int32) {
	if x != nil {
		return x.ChargePoolNum
	}
	return r
}

func (x *CarpoolSuccessInfo) SetIsCarpoolSuccess(v bool) {
	if x != nil {
		x.IsCarpoolSuccess = v
	}
}

func (x *CarpoolSuccessInfo) SetChargePoolNum(v int32) {
	if x != nil {
		x.ChargePoolNum = v
	}
}

func (p *CarpoolSuccessInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarpoolSuccessInfo(%+v)", *p)
}

type IntercityServiceInfo struct {
	EstimateInfo *IntercityEstimateInfo `json:"estimate_info,omitempty" form:"estimate_info"` //预估多呼品类信息
	Title        string                 `json:"title" form:"title"`                           //主标题：有3个服务商可选
	SubTitle     string                 `json:"sub_title" form:"sub_title"`                   //副标题：预计%s元
	DisableToast string                 `json:"disable_toast" form:"disable_toast"`           //未选中品类时点击确定的toast
}

func (x *IntercityServiceInfo) GetEstimateInfo() (r *IntercityEstimateInfo) {
	if x != nil {
		return x.EstimateInfo
	}
	return r
}

func (x *IntercityServiceInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *IntercityServiceInfo) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *IntercityServiceInfo) GetDisableToast() (r string) {
	if x != nil {
		return x.DisableToast
	}
	return r
}

func (x *IntercityServiceInfo) SetEstimateInfo(v *IntercityEstimateInfo) {
	if x != nil {
		x.EstimateInfo = v
	}
}

func (x *IntercityServiceInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *IntercityServiceInfo) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *IntercityServiceInfo) SetDisableToast(v string) {
	if x != nil {
		x.DisableToast = v
	}
}

func (p *IntercityServiceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityServiceInfo(%+v)", *p)
}

type IntercityEstimateInfo struct {
	EstimateData map[int64]*IntercityAnycarEstimateData `json:"estimate_data" form:"estimate_data"`
	FeeDetailUrl string                                 `json:"fee_detail_url" form:"fee_detail_url"` //费用明细页地址
	Layout       []*NewFormLayout                       `json:"layout" form:"layout"`                 //布局
}

func (x *IntercityEstimateInfo) GetEstimateData() (r map[int64]*IntercityAnycarEstimateData) {
	if x != nil {
		return x.EstimateData
	}
	return r
}

func (x *IntercityEstimateInfo) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *IntercityEstimateInfo) GetLayout() (r []*NewFormLayout) {
	if x != nil {
		return x.Layout
	}
	return r
}

func (x *IntercityEstimateInfo) SetEstimateData(v map[int64]*IntercityAnycarEstimateData) {
	if x != nil {
		x.EstimateData = v
	}
}

func (x *IntercityEstimateInfo) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *IntercityEstimateInfo) SetLayout(v []*NewFormLayout) {
	if x != nil {
		x.Layout = v
	}
}

func (p *IntercityEstimateInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityEstimateInfo(%+v)", *p)
}

type IntercityAnycarEstimateData struct {
	EstimateId      string                    `json:"estimate_id" form:"estimate_id"`
	ProductCategory int64                     `json:"product_category" form:"product_category"`
	CarTitle        string                    `json:"car_title" form:"car_title"` //车型数据
	CarIcon         string                    `json:"car_icon" form:"car_icon"`
	FeeAmount       string                    `json:"fee_amount" form:"fee_amount"` //价格信息
	FeeMsg          string                    `json:"fee_msg" form:"fee_msg"`
	FeeDescList     []*NewFormFeeDesc         `json:"fee_desc_list,omitempty" form:"fee_desc_list"`
	ExtraMap        *IntercityNewOrderParam   `json:"extra_map,omitempty" form:"extra_map"`
	IsSelected      int32                     `json:"is_selected" form:"is_selected"`         //勾选状态
	SideExtra       *IntercityAnycarSideExtra `json:"side_extra,omitempty" form:"side_extra"` //勾选状态
}

func (x *IntercityAnycarEstimateData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *IntercityAnycarEstimateData) GetProductCategory() (r int64) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *IntercityAnycarEstimateData) GetCarTitle() (r string) {
	if x != nil {
		return x.CarTitle
	}
	return r
}

func (x *IntercityAnycarEstimateData) GetCarIcon() (r string) {
	if x != nil {
		return x.CarIcon
	}
	return r
}

func (x *IntercityAnycarEstimateData) GetFeeAmount() (r string) {
	if x != nil {
		return x.FeeAmount
	}
	return r
}

func (x *IntercityAnycarEstimateData) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *IntercityAnycarEstimateData) GetFeeDescList() (r []*NewFormFeeDesc) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *IntercityAnycarEstimateData) GetExtraMap() (r *IntercityNewOrderParam) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *IntercityAnycarEstimateData) GetIsSelected() (r int32) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *IntercityAnycarEstimateData) GetSideExtra() (r *IntercityAnycarSideExtra) {
	if x != nil {
		return x.SideExtra
	}
	return r
}

func (x *IntercityAnycarEstimateData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *IntercityAnycarEstimateData) SetProductCategory(v int64) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *IntercityAnycarEstimateData) SetCarTitle(v string) {
	if x != nil {
		x.CarTitle = v
	}
}

func (x *IntercityAnycarEstimateData) SetCarIcon(v string) {
	if x != nil {
		x.CarIcon = v
	}
}

func (x *IntercityAnycarEstimateData) SetFeeAmount(v string) {
	if x != nil {
		x.FeeAmount = v
	}
}

func (x *IntercityAnycarEstimateData) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *IntercityAnycarEstimateData) SetFeeDescList(v []*NewFormFeeDesc) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *IntercityAnycarEstimateData) SetExtraMap(v *IntercityNewOrderParam) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *IntercityAnycarEstimateData) SetIsSelected(v int32) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *IntercityAnycarEstimateData) SetSideExtra(v *IntercityAnycarSideExtra) {
	if x != nil {
		x.SideExtra = v
	}
}

func (p *IntercityAnycarEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityAnycarEstimateData(%+v)", *p)
}

type IntercityAnycarSideExtra struct {
	SubTitleList []*GroupSubTitle `json:"sub_title_list" form:"sub_title_list"` //车型标签
}

func (x *IntercityAnycarSideExtra) GetSubTitleList() (r []*GroupSubTitle) {
	if x != nil {
		return x.SubTitleList
	}
	return r
}

func (x *IntercityAnycarSideExtra) SetSubTitleList(v []*GroupSubTitle) {
	if x != nil {
		x.SubTitleList = v
	}
}

func (p *IntercityAnycarSideExtra) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityAnycarSideExtra(%+v)", *p)
}
