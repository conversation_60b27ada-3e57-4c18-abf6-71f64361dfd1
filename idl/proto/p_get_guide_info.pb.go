// Code generated by http-gen compiler. DO NOT EDIT.
// source: p_get_guide_info.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*PGetGuideInfoReq)(nil)))
	json.Pretouch(reflect.TypeOf((*SimpleOrderInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*PGetGuideInfoRsp)(nil)))
	json.Pretouch(reflect.TypeOf((*GuideInfoData)(nil)))
	json.Pretouch(reflect.TypeOf((*PredictManageCard)(nil)))
	json.Pretouch(reflect.TypeOf((*PopData)(nil)))
	json.Pretouch(reflect.TypeOf((*CarData)(nil)))
}

type PGetGuideInfoReq struct {
	MainEstimateTraceId string           `json:"main_estimate_trace_id" form:"main_estimate_trace_id"`
	AccessKeyId         int32            `json:"access_key_id" form:"access_key_id"`
	AppVersion          string           `json:"app_version" form:"app_version"`
	OrderId             string           `json:"order_id" form:"order_id"`
	Lang                string           `json:"lang" form:"lang"`
	FromCityId          int32            `json:"from_city_id" form:"from_city_id"`
	FromLat             float64          `json:"from_lat" form:"from_lat"`
	FromLng             float64          `json:"from_lng" form:"from_lng"`
	FromAddress         string           `json:"from_address" form:"from_address"`
	FromCountyId        int64            `json:"from_county_id" form:"from_county_id"`
	ToLat               float64          `json:"to_lat" form:"to_lat"`
	ToLng               float64          `json:"to_lng" form:"to_lng"`
	ToCityId            int32            `json:"to_city_id" form:"to_city_id"`
	ToAddress           string           `json:"to_address" form:"to_address"`
	MapType             string           `json:"map_type" form:"map_type"`
	ToCountyId          int64            `json:"to_county_id" form:"to_county_id"`
	Uid                 int64            `json:"uid" form:"uid"`
	Pid                 int64            `json:"pid" form:"pid"`
	Phone               string           `json:"phone" form:"phone"`
	SimpleOrderInfo     *SimpleOrderInfo `json:"simple_order_info,omitempty" form:"simple_order_info"`
}

func (x *PGetGuideInfoReq) GetMainEstimateTraceId() (r string) {
	if x != nil {
		return x.MainEstimateTraceId
	}
	return r
}

func (x *PGetGuideInfoReq) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *PGetGuideInfoReq) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *PGetGuideInfoReq) GetOrderId() (r string) {
	if x != nil {
		return x.OrderId
	}
	return r
}

func (x *PGetGuideInfoReq) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *PGetGuideInfoReq) GetFromCityId() (r int32) {
	if x != nil {
		return x.FromCityId
	}
	return r
}

func (x *PGetGuideInfoReq) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *PGetGuideInfoReq) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *PGetGuideInfoReq) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *PGetGuideInfoReq) GetFromCountyId() (r int64) {
	if x != nil {
		return x.FromCountyId
	}
	return r
}

func (x *PGetGuideInfoReq) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *PGetGuideInfoReq) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *PGetGuideInfoReq) GetToCityId() (r int32) {
	if x != nil {
		return x.ToCityId
	}
	return r
}

func (x *PGetGuideInfoReq) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *PGetGuideInfoReq) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *PGetGuideInfoReq) GetToCountyId() (r int64) {
	if x != nil {
		return x.ToCountyId
	}
	return r
}

func (x *PGetGuideInfoReq) GetUid() (r int64) {
	if x != nil {
		return x.Uid
	}
	return r
}

func (x *PGetGuideInfoReq) GetPid() (r int64) {
	if x != nil {
		return x.Pid
	}
	return r
}

func (x *PGetGuideInfoReq) GetPhone() (r string) {
	if x != nil {
		return x.Phone
	}
	return r
}

func (x *PGetGuideInfoReq) GetSimpleOrderInfo() (r *SimpleOrderInfo) {
	if x != nil {
		return x.SimpleOrderInfo
	}
	return r
}

func (x *PGetGuideInfoReq) SetMainEstimateTraceId(v string) {
	if x != nil {
		x.MainEstimateTraceId = v
	}
}

func (x *PGetGuideInfoReq) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *PGetGuideInfoReq) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *PGetGuideInfoReq) SetOrderId(v string) {
	if x != nil {
		x.OrderId = v
	}
}

func (x *PGetGuideInfoReq) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *PGetGuideInfoReq) SetFromCityId(v int32) {
	if x != nil {
		x.FromCityId = v
	}
}

func (x *PGetGuideInfoReq) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *PGetGuideInfoReq) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *PGetGuideInfoReq) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *PGetGuideInfoReq) SetFromCountyId(v int64) {
	if x != nil {
		x.FromCountyId = v
	}
}

func (x *PGetGuideInfoReq) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *PGetGuideInfoReq) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *PGetGuideInfoReq) SetToCityId(v int32) {
	if x != nil {
		x.ToCityId = v
	}
}

func (x *PGetGuideInfoReq) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *PGetGuideInfoReq) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *PGetGuideInfoReq) SetToCountyId(v int64) {
	if x != nil {
		x.ToCountyId = v
	}
}

func (x *PGetGuideInfoReq) SetUid(v int64) {
	if x != nil {
		x.Uid = v
	}
}

func (x *PGetGuideInfoReq) SetPid(v int64) {
	if x != nil {
		x.Pid = v
	}
}

func (x *PGetGuideInfoReq) SetPhone(v string) {
	if x != nil {
		x.Phone = v
	}
}

func (x *PGetGuideInfoReq) SetSimpleOrderInfo(v *SimpleOrderInfo) {
	if x != nil {
		x.SimpleOrderInfo = v
	}
}

func (p *PGetGuideInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PGetGuideInfoReq(%+v)", *p)
}

func (p *PGetGuideInfoReq) Validate() error {
	if !(len(p.GetOrderId()) > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "OrderId", "PGetGuideInfoReq")
	}
	return nil
}

type SimpleOrderInfo struct {
	OrderId       string `json:"order_id" form:"order_id"`
	IsAnycar      string `json:"is_anycar" form:"is_anycar"`
	EstimateId    string `json:"estimate_id" form:"estimate_id"`
	ProductId     string `json:"product_id" form:"product_id"`
	RequireLevel  string `json:"require_level" form:"require_level"`
	ComboType     string `json:"combo_type" form:"combo_type"`
	County        string `json:"county" form:"county"`
	LocalCurrency string `json:"local_currency" form:"local_currency"`
	ExtendFeature string `json:"extend_feature" form:"extend_feature"`
}

func (x *SimpleOrderInfo) GetOrderId() (r string) {
	if x != nil {
		return x.OrderId
	}
	return r
}

func (x *SimpleOrderInfo) GetIsAnycar() (r string) {
	if x != nil {
		return x.IsAnycar
	}
	return r
}

func (x *SimpleOrderInfo) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *SimpleOrderInfo) GetProductId() (r string) {
	if x != nil {
		return x.ProductId
	}
	return r
}

func (x *SimpleOrderInfo) GetRequireLevel() (r string) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *SimpleOrderInfo) GetComboType() (r string) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *SimpleOrderInfo) GetCounty() (r string) {
	if x != nil {
		return x.County
	}
	return r
}

func (x *SimpleOrderInfo) GetLocalCurrency() (r string) {
	if x != nil {
		return x.LocalCurrency
	}
	return r
}

func (x *SimpleOrderInfo) GetExtendFeature() (r string) {
	if x != nil {
		return x.ExtendFeature
	}
	return r
}

func (x *SimpleOrderInfo) SetOrderId(v string) {
	if x != nil {
		x.OrderId = v
	}
}

func (x *SimpleOrderInfo) SetIsAnycar(v string) {
	if x != nil {
		x.IsAnycar = v
	}
}

func (x *SimpleOrderInfo) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *SimpleOrderInfo) SetProductId(v string) {
	if x != nil {
		x.ProductId = v
	}
}

func (x *SimpleOrderInfo) SetRequireLevel(v string) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *SimpleOrderInfo) SetComboType(v string) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *SimpleOrderInfo) SetCounty(v string) {
	if x != nil {
		x.County = v
	}
}

func (x *SimpleOrderInfo) SetLocalCurrency(v string) {
	if x != nil {
		x.LocalCurrency = v
	}
}

func (x *SimpleOrderInfo) SetExtendFeature(v string) {
	if x != nil {
		x.ExtendFeature = v
	}
}

func (p *SimpleOrderInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SimpleOrderInfo(%+v)", *p)
}

type PGetGuideInfoRsp struct {
	Errno   int32          `json:"errno" form:"errno"`
	Errmsg  string         `json:"errmsg" form:"errmsg"`
	Data    *GuideInfoData `json:"data,omitempty" form:"data"`
	TraceId string         `json:"trace_id" form:"trace_id"`
}

func (x *PGetGuideInfoRsp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *PGetGuideInfoRsp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *PGetGuideInfoRsp) GetData() (r *GuideInfoData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *PGetGuideInfoRsp) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *PGetGuideInfoRsp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *PGetGuideInfoRsp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *PGetGuideInfoRsp) SetData(v *GuideInfoData) {
	if x != nil {
		x.Data = v
	}
}

func (x *PGetGuideInfoRsp) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (p *PGetGuideInfoRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PGetGuideInfoRsp(%+v)", *p)
}

type GuideInfoData struct {
	PredictManageCardV2 *PredictManageCard `json:"predict_manage_card_v2,omitempty" form:"predict_manage_card_v2"` //预期管理卡片
}

func (x *GuideInfoData) GetPredictManageCardV2() (r *PredictManageCard) {
	if x != nil {
		return x.PredictManageCardV2
	}
	return r
}

func (x *GuideInfoData) SetPredictManageCardV2(v *PredictManageCard) {
	if x != nil {
		x.PredictManageCardV2 = v
	}
}

func (p *GuideInfoData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GuideInfoData(%+v)", *p)
}

type PredictManageCard struct {
	SubTitleList1 []string   `json:"sub_title_list1" form:"sub_title_list1"` //"已选车型"  "预估xx-xx元"
	CarList       []*PopData `json:"car_list" form:"car_list"`               //"已选车型"  "预估xx-xx元"
}

func (x *PredictManageCard) GetSubTitleList1() (r []string) {
	if x != nil {
		return x.SubTitleList1
	}
	return r
}

func (x *PredictManageCard) GetCarList() (r []*PopData) {
	if x != nil {
		return x.CarList
	}
	return r
}

func (x *PredictManageCard) SetSubTitleList1(v []string) {
	if x != nil {
		x.SubTitleList1 = v
	}
}

func (x *PredictManageCard) SetCarList(v []*PopData) {
	if x != nil {
		x.CarList = v
	}
}

func (p *PredictManageCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PredictManageCard(%+v)", *p)
}

type PopData struct {
	Type      int32      `json:"type" form:"type"`             //2
	Tag       string     `json:"tag" form:"tag"`               //all_list
	MainTitle string     `json:"main_title" form:"main_title"` //正在同时呼叫10种车型
	SubTitle  string     `json:"sub_title" form:"sub_title"`   //节假日服务费
	CarList   []*CarData `json:"car_list" form:"car_list"`     //节假日服务费
}

func (x *PopData) GetType() (r int32) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *PopData) GetTag() (r string) {
	if x != nil {
		return x.Tag
	}
	return r
}

func (x *PopData) GetMainTitle() (r string) {
	if x != nil {
		return x.MainTitle
	}
	return r
}

func (x *PopData) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *PopData) GetCarList() (r []*CarData) {
	if x != nil {
		return x.CarList
	}
	return r
}

func (x *PopData) SetType(v int32) {
	if x != nil {
		x.Type = v
	}
}

func (x *PopData) SetTag(v string) {
	if x != nil {
		x.Tag = v
	}
}

func (x *PopData) SetMainTitle(v string) {
	if x != nil {
		x.MainTitle = v
	}
}

func (x *PopData) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *PopData) SetCarList(v []*CarData) {
	if x != nil {
		x.CarList = v
	}
}

func (p *PopData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PopData(%+v)", *p)
}

type CarData struct {
	GroupKey        string    `json:"group_key" form:"group_key"` //extend_feature
	LeftInfo        *LeftInfo `json:"left_info,omitempty" form:"left_info"`
	ProductNums     *int32    `json:"product_nums,omitempty" form:"product_nums"`
	SubGroupId      *int32    `json:"sub_group_id,omitempty" form:"sub_group_id"`
	ProductCategory int64     `json:"product_category" form:"product_category"`
}

func (x *CarData) GetGroupKey() (r string) {
	if x != nil {
		return x.GroupKey
	}
	return r
}

func (x *CarData) GetLeftInfo() (r *LeftInfo) {
	if x != nil {
		return x.LeftInfo
	}
	return r
}

func (x *CarData) GetProductNums() (r int32) {
	if x != nil && x.ProductNums != nil {
		return *x.ProductNums
	}
	return r
}

func (x *CarData) GetSubGroupId() (r int32) {
	if x != nil && x.SubGroupId != nil {
		return *x.SubGroupId
	}
	return r
}

func (x *CarData) GetProductCategory() (r int64) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *CarData) SetGroupKey(v string) {
	if x != nil {
		x.GroupKey = v
	}
}

func (x *CarData) SetLeftInfo(v *LeftInfo) {
	if x != nil {
		x.LeftInfo = v
	}
}

func (x *CarData) SetProductNums(v int32) {
	if x != nil {
		x.ProductNums = &v
	}
}

func (x *CarData) SetSubGroupId(v int32) {
	if x != nil {
		x.SubGroupId = &v
	}
}

func (x *CarData) SetProductCategory(v int64) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (p *CarData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CarData(%+v)", *p)
}
