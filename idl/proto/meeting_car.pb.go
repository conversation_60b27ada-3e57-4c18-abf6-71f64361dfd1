// Code generated by http-gen compiler. DO NOT EDIT.
// source: meeting_car.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*MeetingCarEstimateReq)(nil)))
	json.Pretouch(reflect.TypeOf((*MeetingCarEstimateRsp)(nil)))
	json.Pretouch(reflect.TypeOf((*EstimateEntry)(nil)))
	json.Pretouch(reflect.TypeOf((*MeetingCarEstimateData)(nil)))
}

type FailReason int32

const (
	FailReason_SUCCESS        FailReason = 0 //
	FailReason_NOT_OPEN_CITY  FailReason = 1 //
	FailReason_NOT_PRICE_RULE FailReason = 2 //
)

var (
	FailReason_name = map[int32]string{
		0: "SUCCESS",
		1: "NOT_OPEN_CITY",
		2: "NOT_PRICE_RULE",
	}
	FailReason_value = map[string]int32{
		"SUCCESS":        0,
		"NOT_OPEN_CITY":  1,
		"NOT_PRICE_RULE": 2,
	}
)

func OneOfFailReason(i int32) bool {
	_, ok := FailReason_name[i]
	return ok
}

type MeetingCarEstimateReq struct {
	Entries     []*EstimateEntry `json:"entries" form:"entries"`
	AccessKeyId int32            `json:"access_key_id" form:"access_key_id"`
	AppVersion  string           `json:"app_version" form:"app_version"`
	Token       string           `json:"token" form:"token"`
}

func (x *MeetingCarEstimateReq) GetEntries() (r []*EstimateEntry) {
	if x != nil {
		return x.Entries
	}
	return r
}

func (x *MeetingCarEstimateReq) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *MeetingCarEstimateReq) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *MeetingCarEstimateReq) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *MeetingCarEstimateReq) SetEntries(v []*EstimateEntry) {
	if x != nil {
		x.Entries = v
	}
}

func (x *MeetingCarEstimateReq) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *MeetingCarEstimateReq) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *MeetingCarEstimateReq) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (p *MeetingCarEstimateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MeetingCarEstimateReq(%+v)", *p)
}

type MeetingCarEstimateRsp struct {
	Errno   int32                     `json:"errno" form:"errno"`
	Errmsg  string                    `json:"errmsg" form:"errmsg"`
	TraceId string                    `json:"trace_id" form:"trace_id"`
	Data    []*MeetingCarEstimateData `json:"data" form:"data"`
}

func (x *MeetingCarEstimateRsp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *MeetingCarEstimateRsp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *MeetingCarEstimateRsp) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *MeetingCarEstimateRsp) GetData() (r []*MeetingCarEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *MeetingCarEstimateRsp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *MeetingCarEstimateRsp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *MeetingCarEstimateRsp) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (x *MeetingCarEstimateRsp) SetData(v []*MeetingCarEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (p *MeetingCarEstimateRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MeetingCarEstimateRsp(%+v)", *p)
}

type EstimateEntry struct {
	Flat                float64 `json:"flat" form:"flat"`
	Flng                float64 `json:"flng" form:"flng"`
	FromName            string  `json:"from_name" form:"from_name"`
	Tlat                float64 `json:"tlat" form:"tlat"`
	Tlng                float64 `json:"tlng" form:"tlng"`
	ToName              string  `json:"to_name" form:"to_name"`
	ProductCategory     int32   `json:"product_category" form:"product_category"`           //用户选择的产品品类
	ComboType           int32   `json:"combo_type" form:"combo_type"`                       //用户选择的场景类型
	ComboId             int32   `json:"combo_id" form:"combo_id"`                           //用户选择的套餐类型
	DepartureTime       int64   `json:"departure_time" form:"departure_time"`               //出发时间-时间戳秒
	ProductId           int32   `json:"product_id" form:"product_id"`                       //产品线
	ServiceFeePassenger int64   `json:"service_fee_passenger" form:"service_fee_passenger"` //产品线
	ServiceFeeDriver    int64   `json:"service_fee_driver" form:"service_fee_driver"`
}

func (x *EstimateEntry) GetFlat() (r float64) {
	if x != nil {
		return x.Flat
	}
	return r
}

func (x *EstimateEntry) GetFlng() (r float64) {
	if x != nil {
		return x.Flng
	}
	return r
}

func (x *EstimateEntry) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *EstimateEntry) GetTlat() (r float64) {
	if x != nil {
		return x.Tlat
	}
	return r
}

func (x *EstimateEntry) GetTlng() (r float64) {
	if x != nil {
		return x.Tlng
	}
	return r
}

func (x *EstimateEntry) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *EstimateEntry) GetProductCategory() (r int32) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *EstimateEntry) GetComboType() (r int32) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *EstimateEntry) GetComboId() (r int32) {
	if x != nil {
		return x.ComboId
	}
	return r
}

func (x *EstimateEntry) GetDepartureTime() (r int64) {
	if x != nil {
		return x.DepartureTime
	}
	return r
}

func (x *EstimateEntry) GetProductId() (r int32) {
	if x != nil {
		return x.ProductId
	}
	return r
}

func (x *EstimateEntry) GetServiceFeePassenger() (r int64) {
	if x != nil {
		return x.ServiceFeePassenger
	}
	return r
}

func (x *EstimateEntry) GetServiceFeeDriver() (r int64) {
	if x != nil {
		return x.ServiceFeeDriver
	}
	return r
}

func (x *EstimateEntry) SetFlat(v float64) {
	if x != nil {
		x.Flat = v
	}
}

func (x *EstimateEntry) SetFlng(v float64) {
	if x != nil {
		x.Flng = v
	}
}

func (x *EstimateEntry) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *EstimateEntry) SetTlat(v float64) {
	if x != nil {
		x.Tlat = v
	}
}

func (x *EstimateEntry) SetTlng(v float64) {
	if x != nil {
		x.Tlng = v
	}
}

func (x *EstimateEntry) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *EstimateEntry) SetProductCategory(v int32) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *EstimateEntry) SetComboType(v int32) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *EstimateEntry) SetComboId(v int32) {
	if x != nil {
		x.ComboId = v
	}
}

func (x *EstimateEntry) SetDepartureTime(v int64) {
	if x != nil {
		x.DepartureTime = v
	}
}

func (x *EstimateEntry) SetProductId(v int32) {
	if x != nil {
		x.ProductId = v
	}
}

func (x *EstimateEntry) SetServiceFeePassenger(v int64) {
	if x != nil {
		x.ServiceFeePassenger = v
	}
}

func (x *EstimateEntry) SetServiceFeeDriver(v int64) {
	if x != nil {
		x.ServiceFeeDriver = v
	}
}

func (p *EstimateEntry) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EstimateEntry(%+v)", *p)
}

type MeetingCarEstimateData struct {
	EstimateId          string     `json:"estimate_id" form:"estimate_id"`
	ServiceFeePassenger int64      `json:"service_fee_passenger" form:"service_fee_passenger"`
	ServiceFeeDriver    int64      `json:"service_fee_driver" form:"service_fee_driver"`
	EstimateFeeTotal    int64      `json:"estimate_fee_total" form:"estimate_fee_total"`
	CharterFeeDriver    int64      `json:"charter_fee_driver" form:"charter_fee_driver"`
	RequireLevel        string     `json:"require_level" form:"require_level"`
	BusinessId          int32      `json:"business_id" form:"business_id"`
	DisplayNameCn       string     `json:"display_name_cn" form:"display_name_cn"`
	FeeDetail           string     `json:"fee_detail" form:"fee_detail"`
	Reason              FailReason `json:"reason" form:"reason"` //预估失败原因
}

func (x *MeetingCarEstimateData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *MeetingCarEstimateData) GetServiceFeePassenger() (r int64) {
	if x != nil {
		return x.ServiceFeePassenger
	}
	return r
}

func (x *MeetingCarEstimateData) GetServiceFeeDriver() (r int64) {
	if x != nil {
		return x.ServiceFeeDriver
	}
	return r
}

func (x *MeetingCarEstimateData) GetEstimateFeeTotal() (r int64) {
	if x != nil {
		return x.EstimateFeeTotal
	}
	return r
}

func (x *MeetingCarEstimateData) GetCharterFeeDriver() (r int64) {
	if x != nil {
		return x.CharterFeeDriver
	}
	return r
}

func (x *MeetingCarEstimateData) GetRequireLevel() (r string) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *MeetingCarEstimateData) GetBusinessId() (r int32) {
	if x != nil {
		return x.BusinessId
	}
	return r
}

func (x *MeetingCarEstimateData) GetDisplayNameCn() (r string) {
	if x != nil {
		return x.DisplayNameCn
	}
	return r
}

func (x *MeetingCarEstimateData) GetFeeDetail() (r string) {
	if x != nil {
		return x.FeeDetail
	}
	return r
}

func (x *MeetingCarEstimateData) GetReason() (r FailReason) {
	if x != nil {
		return x.Reason
	}
	return r
}

func (x *MeetingCarEstimateData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *MeetingCarEstimateData) SetServiceFeePassenger(v int64) {
	if x != nil {
		x.ServiceFeePassenger = v
	}
}

func (x *MeetingCarEstimateData) SetServiceFeeDriver(v int64) {
	if x != nil {
		x.ServiceFeeDriver = v
	}
}

func (x *MeetingCarEstimateData) SetEstimateFeeTotal(v int64) {
	if x != nil {
		x.EstimateFeeTotal = v
	}
}

func (x *MeetingCarEstimateData) SetCharterFeeDriver(v int64) {
	if x != nil {
		x.CharterFeeDriver = v
	}
}

func (x *MeetingCarEstimateData) SetRequireLevel(v string) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *MeetingCarEstimateData) SetBusinessId(v int32) {
	if x != nil {
		x.BusinessId = v
	}
}

func (x *MeetingCarEstimateData) SetDisplayNameCn(v string) {
	if x != nil {
		x.DisplayNameCn = v
	}
}

func (x *MeetingCarEstimateData) SetFeeDetail(v string) {
	if x != nil {
		x.FeeDetail = v
	}
}

func (x *MeetingCarEstimateData) SetReason(v FailReason) {
	if x != nil {
		x.Reason = v
	}
}

func (p *MeetingCarEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MeetingCarEstimateData(%+v)", *p)
}
