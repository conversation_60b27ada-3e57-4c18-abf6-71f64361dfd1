// Code generated by http-gen compiler. DO NOT EDIT.
// source: intercity.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*IntercityEstimateDetailRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityEstimateDetailResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityEstimateDetailData)(nil)))
	json.Pretouch(reflect.TypeOf((*BusShiftCard)(nil)))
	json.Pretouch(reflect.TypeOf((*BusShiftCardOmegaInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*BusShiftCardOmegaParam)(nil)))
	json.Pretouch(reflect.TypeOf((*BusShiftCardSubMsg)(nil)))
	json.Pretouch(reflect.TypeOf((*BusShiftCardStationInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*BusShiftCardDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*BusShiftCardStation)(nil)))
	json.Pretouch(reflect.TypeOf((*BusShiftCardTag)(nil)))
	json.Pretouch(reflect.TypeOf((*JumpButton)(nil)))
	json.Pretouch(reflect.TypeOf((*RuleMsgInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*RebookRefundRuleInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*HeadCard)(nil)))
	json.Pretouch(reflect.TypeOf((*StationFullInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*StationDataInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*MiddleStation)(nil)))
	json.Pretouch(reflect.TypeOf((*PopupDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*StationItem)(nil)))
	json.Pretouch(reflect.TypeOf((*StorePassengerInfoCard)(nil)))
	json.Pretouch(reflect.TypeOf((*FormDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*SelectItem)(nil)))
	json.Pretouch(reflect.TypeOf((*RealNameSeatInfoCard)(nil)))
	json.Pretouch(reflect.TypeOf((*AlertInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SeatLimit)(nil)))
	json.Pretouch(reflect.TypeOf((*RuleLimit)(nil)))
	json.Pretouch(reflect.TypeOf((*ChoosePassengerDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*SelectedPassengerDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*PassengerItem)(nil)))
	json.Pretouch(reflect.TypeOf((*NoRealNamePassengerDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*SeatInfoCard)(nil)))
	json.Pretouch(reflect.TypeOf((*SeatDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*RuleCard)(nil)))
	json.Pretouch(reflect.TypeOf((*BottomCard)(nil)))
	json.Pretouch(reflect.TypeOf((*BottomTerms)(nil)))
	json.Pretouch(reflect.TypeOf((*TermsPopup)(nil)))
	json.Pretouch(reflect.TypeOf((*EstimateInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*Button)(nil)))
	json.Pretouch(reflect.TypeOf((*OrderParams)(nil)))
	json.Pretouch(reflect.TypeOf((*PriceCard)(nil)))
	json.Pretouch(reflect.TypeOf((*PriceCardItem)(nil)))
	json.Pretouch(reflect.TypeOf((*PriceItem)(nil)))
	json.Pretouch(reflect.TypeOf((*StationPopUp)(nil)))
	json.Pretouch(reflect.TypeOf((*NTuple)(nil)))
	json.Pretouch(reflect.TypeOf((*FeeParams)(nil)))
	json.Pretouch(reflect.TypeOf((*ExtraInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*TravelCardInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*BusCardItem)(nil)))
	json.Pretouch(reflect.TypeOf((*TopLeftCornerTag)(nil)))
	json.Pretouch(reflect.TypeOf((*CardTip)(nil)))
	json.Pretouch(reflect.TypeOf((*BusDetailOmegaInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*BusDetailOmegaShow)(nil)))
	json.Pretouch(reflect.TypeOf((*BusDetailOmegaParams)(nil)))
	json.Pretouch(reflect.TypeOf((*BusDetailOmegaCommonParams)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityEstimateRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityEstimateResp)(nil)))
	json.Pretouch(reflect.TypeOf((*StrategyButton)(nil)))
	json.Pretouch(reflect.TypeOf((*PopUps)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*EstimateCardData)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityMultiEstimateRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityMultiEstimateResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityMultiEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*OmegaParams)(nil)))
	json.Pretouch(reflect.TypeOf((*SortList)(nil)))
	json.Pretouch(reflect.TypeOf((*SearchBox)(nil)))
	json.Pretouch(reflect.TypeOf((*SimpleButton)(nil)))
	json.Pretouch(reflect.TypeOf((*OptimizedData)(nil)))
	json.Pretouch(reflect.TypeOf((*IconCard)(nil)))
	json.Pretouch(reflect.TypeOf((*GuideData)(nil)))
	json.Pretouch(reflect.TypeOf((*ScreenData)(nil)))
	json.Pretouch(reflect.TypeOf((*CalendarItem)(nil)))
	json.Pretouch(reflect.TypeOf((*Day)(nil)))
	json.Pretouch(reflect.TypeOf((*DayList)(nil)))
	json.Pretouch(reflect.TypeOf((*StationData)(nil)))
	json.Pretouch(reflect.TypeOf((*StationDataV2)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityCounty)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityStation)(nil)))
	json.Pretouch(reflect.TypeOf((*StationTime)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityTime)(nil)))
	json.Pretouch(reflect.TypeOf((*DisabledInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SubMsg)(nil)))
	json.Pretouch(reflect.TypeOf((*ButtonInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityStationDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*Detail)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityStationInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*EstimateCard)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityBasicFeeLiteRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityBasicFeeLiteResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*IntercityBasicFeeLiteData)(nil)))
}

type StyleType int32

const (
	StyleType_Blank               StyleType = 0 //
	StyleType_InterCitySkuStation StyleType = 1 //
	StyleType_InterCitySku        StyleType = 2 //
)

var (
	StyleType_name = map[int32]string{
		0: "Blank",
		1: "InterCitySkuStation",
		2: "InterCitySku",
	}
	StyleType_value = map[string]int32{
		"Blank":               0,
		"InterCitySkuStation": 1,
		"InterCitySku":        2,
	}
)

func OneOfStyleType(i int32) bool {
	_, ok := StyleType_name[i]
	return ok
}

type IntercityEstimateDetailRequest struct {
	Token                     string  `json:"token" form:"token"`
	AccessKeyId               int64   `json:"access_key_id" form:"access_key_id"`
	AppVersion                string  `json:"app_version" form:"app_version"`
	Lang                      string  `json:"lang" form:"lang"`
	MapType                   string  `json:"map_type" form:"map_type"`
	UserType                  int32   `json:"user_type" form:"user_type"`
	Channel                   int64   `json:"channel" form:"channel"`
	EstimateId                string  `json:"estimate_id" form:"estimate_id"`
	PassengerInfo             string  `json:"passenger_info" form:"passenger_info"`
	MaxInventory              int32   `json:"max_inventory" form:"max_inventory"`
	CarryChildrenMaxInventory int32   `json:"carry_children_max_inventory" form:"carry_children_max_inventory"`
	StartStationId            *string `json:"start_station_id,omitempty" form:"start_station_id"`
	EndStationId              *string `json:"end_station_id,omitempty" form:"end_station_id"`
	AdultPrice                *string `json:"adult_price,omitempty" form:"adult_price"`
	BusServiceShiftId         *string `json:"bus_service_shift_id,omitempty" form:"bus_service_shift_id"`
	RouteId                   *string `json:"route_id,omitempty" form:"route_id"`
	AgentType                 *string `json:"agent_type,omitempty" form:"agent_type"`
	CarPlate                  *string `json:"car_plate,omitempty" form:"car_plate"`
	CityId                    *string `json:"city_id,omitempty" form:"city_id"`
	District                  *string `json:"district,omitempty" form:"district"`
	DepartureTime             *string `json:"departure_time,omitempty" form:"departure_time"`
	BusCardSelectedBatchId    int32   `json:"bus_card_selected_batch_id" form:"bus_card_selected_batch_id"` //用户选择的出行卡批次
	GuideTraceId              *string `json:"guide_trace_id,omitempty" form:"guide_trace_id"`               //用户选择的出行卡批次
	IsBestShift               *int32  `json:"is_best_shift,omitempty" form:"is_best_shift"`                 //导流位跳转表明是否最优班次
	StartPoiId                string  `json:"start_poi_id" form:"start_poi_id"`                             //25 - 36 渲染新的bus_shift_card中的跳转链接用到
	StartPoiName              string  `json:"start_poi_name" form:"start_poi_name"`
	StartPoiCity              int64   `json:"start_poi_city" form:"start_poi_city"`
	StartPoiCityName          string  `json:"start_poi_city_name" form:"start_poi_city_name"`
	StartLat                  string  `json:"start_lat" form:"start_lat"`
	StartLng                  string  `json:"start_lng" form:"start_lng"`
	EndPoiId                  string  `json:"end_poi_id" form:"end_poi_id"`
	EndPoiName                string  `json:"end_poi_name" form:"end_poi_name"`
	EndPoiCity                int64   `json:"end_poi_city" form:"end_poi_city"`
	EndPoiCityName            string  `json:"end_poi_city_name" form:"end_poi_city_name"`
	EndLat                    string  `json:"end_lat" form:"end_lat"`
	EndLng                    string  `json:"end_lng" form:"end_lng"`
	Xpsid                     *string `json:"xpsid,omitempty" form:"xpsid"`
	XpsidRoot                 *string `json:"xpsid_root,omitempty" form:"xpsid_root"`
	Dchn                      *string `json:"dchn,omitempty" form:"dchn"`
	ClientType                int32   `json:"client_type" form:"client_type"`
}

func (x *IntercityEstimateDetailRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetAccessKeyId() (r int64) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetUserType() (r int32) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetChannel() (r int64) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetPassengerInfo() (r string) {
	if x != nil {
		return x.PassengerInfo
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetMaxInventory() (r int32) {
	if x != nil {
		return x.MaxInventory
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetCarryChildrenMaxInventory() (r int32) {
	if x != nil {
		return x.CarryChildrenMaxInventory
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetStartStationId() (r string) {
	if x != nil && x.StartStationId != nil {
		return *x.StartStationId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetEndStationId() (r string) {
	if x != nil && x.EndStationId != nil {
		return *x.EndStationId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetAdultPrice() (r string) {
	if x != nil && x.AdultPrice != nil {
		return *x.AdultPrice
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetBusServiceShiftId() (r string) {
	if x != nil && x.BusServiceShiftId != nil {
		return *x.BusServiceShiftId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetRouteId() (r string) {
	if x != nil && x.RouteId != nil {
		return *x.RouteId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetAgentType() (r string) {
	if x != nil && x.AgentType != nil {
		return *x.AgentType
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetCarPlate() (r string) {
	if x != nil && x.CarPlate != nil {
		return *x.CarPlate
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetCityId() (r string) {
	if x != nil && x.CityId != nil {
		return *x.CityId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetDistrict() (r string) {
	if x != nil && x.District != nil {
		return *x.District
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetDepartureTime() (r string) {
	if x != nil && x.DepartureTime != nil {
		return *x.DepartureTime
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetBusCardSelectedBatchId() (r int32) {
	if x != nil {
		return x.BusCardSelectedBatchId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetGuideTraceId() (r string) {
	if x != nil && x.GuideTraceId != nil {
		return *x.GuideTraceId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetIsBestShift() (r int32) {
	if x != nil && x.IsBestShift != nil {
		return *x.IsBestShift
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetStartPoiId() (r string) {
	if x != nil {
		return x.StartPoiId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetStartPoiName() (r string) {
	if x != nil {
		return x.StartPoiName
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetStartPoiCity() (r int64) {
	if x != nil {
		return x.StartPoiCity
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetStartPoiCityName() (r string) {
	if x != nil {
		return x.StartPoiCityName
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetStartLat() (r string) {
	if x != nil {
		return x.StartLat
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetStartLng() (r string) {
	if x != nil {
		return x.StartLng
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetEndPoiId() (r string) {
	if x != nil {
		return x.EndPoiId
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetEndPoiName() (r string) {
	if x != nil {
		return x.EndPoiName
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetEndPoiCity() (r int64) {
	if x != nil {
		return x.EndPoiCity
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetEndPoiCityName() (r string) {
	if x != nil {
		return x.EndPoiCityName
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetEndLat() (r string) {
	if x != nil {
		return x.EndLat
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetEndLng() (r string) {
	if x != nil {
		return x.EndLng
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetXpsid() (r string) {
	if x != nil && x.Xpsid != nil {
		return *x.Xpsid
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetXpsidRoot() (r string) {
	if x != nil && x.XpsidRoot != nil {
		return *x.XpsidRoot
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetDchn() (r string) {
	if x != nil && x.Dchn != nil {
		return *x.Dchn
	}
	return r
}

func (x *IntercityEstimateDetailRequest) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *IntercityEstimateDetailRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *IntercityEstimateDetailRequest) SetAccessKeyId(v int64) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *IntercityEstimateDetailRequest) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *IntercityEstimateDetailRequest) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *IntercityEstimateDetailRequest) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *IntercityEstimateDetailRequest) SetUserType(v int32) {
	if x != nil {
		x.UserType = v
	}
}

func (x *IntercityEstimateDetailRequest) SetChannel(v int64) {
	if x != nil {
		x.Channel = v
	}
}

func (x *IntercityEstimateDetailRequest) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *IntercityEstimateDetailRequest) SetPassengerInfo(v string) {
	if x != nil {
		x.PassengerInfo = v
	}
}

func (x *IntercityEstimateDetailRequest) SetMaxInventory(v int32) {
	if x != nil {
		x.MaxInventory = v
	}
}

func (x *IntercityEstimateDetailRequest) SetCarryChildrenMaxInventory(v int32) {
	if x != nil {
		x.CarryChildrenMaxInventory = v
	}
}

func (x *IntercityEstimateDetailRequest) SetStartStationId(v string) {
	if x != nil {
		x.StartStationId = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetEndStationId(v string) {
	if x != nil {
		x.EndStationId = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetAdultPrice(v string) {
	if x != nil {
		x.AdultPrice = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetBusServiceShiftId(v string) {
	if x != nil {
		x.BusServiceShiftId = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetRouteId(v string) {
	if x != nil {
		x.RouteId = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetAgentType(v string) {
	if x != nil {
		x.AgentType = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetCarPlate(v string) {
	if x != nil {
		x.CarPlate = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetCityId(v string) {
	if x != nil {
		x.CityId = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetDistrict(v string) {
	if x != nil {
		x.District = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetDepartureTime(v string) {
	if x != nil {
		x.DepartureTime = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetBusCardSelectedBatchId(v int32) {
	if x != nil {
		x.BusCardSelectedBatchId = v
	}
}

func (x *IntercityEstimateDetailRequest) SetGuideTraceId(v string) {
	if x != nil {
		x.GuideTraceId = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetIsBestShift(v int32) {
	if x != nil {
		x.IsBestShift = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetStartPoiId(v string) {
	if x != nil {
		x.StartPoiId = v
	}
}

func (x *IntercityEstimateDetailRequest) SetStartPoiName(v string) {
	if x != nil {
		x.StartPoiName = v
	}
}

func (x *IntercityEstimateDetailRequest) SetStartPoiCity(v int64) {
	if x != nil {
		x.StartPoiCity = v
	}
}

func (x *IntercityEstimateDetailRequest) SetStartPoiCityName(v string) {
	if x != nil {
		x.StartPoiCityName = v
	}
}

func (x *IntercityEstimateDetailRequest) SetStartLat(v string) {
	if x != nil {
		x.StartLat = v
	}
}

func (x *IntercityEstimateDetailRequest) SetStartLng(v string) {
	if x != nil {
		x.StartLng = v
	}
}

func (x *IntercityEstimateDetailRequest) SetEndPoiId(v string) {
	if x != nil {
		x.EndPoiId = v
	}
}

func (x *IntercityEstimateDetailRequest) SetEndPoiName(v string) {
	if x != nil {
		x.EndPoiName = v
	}
}

func (x *IntercityEstimateDetailRequest) SetEndPoiCity(v int64) {
	if x != nil {
		x.EndPoiCity = v
	}
}

func (x *IntercityEstimateDetailRequest) SetEndPoiCityName(v string) {
	if x != nil {
		x.EndPoiCityName = v
	}
}

func (x *IntercityEstimateDetailRequest) SetEndLat(v string) {
	if x != nil {
		x.EndLat = v
	}
}

func (x *IntercityEstimateDetailRequest) SetEndLng(v string) {
	if x != nil {
		x.EndLng = v
	}
}

func (x *IntercityEstimateDetailRequest) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetDchn(v string) {
	if x != nil {
		x.Dchn = &v
	}
}

func (x *IntercityEstimateDetailRequest) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (p *IntercityEstimateDetailRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityEstimateDetailRequest(%+v)", *p)
}

type IntercityEstimateDetailResponse struct {
	Errno  int32                        `json:"errno" form:"errno"`
	Errmsg string                       `json:"errmsg" form:"errmsg"`
	Data   *IntercityEstimateDetailData `json:"data,omitempty" form:"data"`
}

func (x *IntercityEstimateDetailResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *IntercityEstimateDetailResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *IntercityEstimateDetailResponse) GetData() (r *IntercityEstimateDetailData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *IntercityEstimateDetailResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *IntercityEstimateDetailResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *IntercityEstimateDetailResponse) SetData(v *IntercityEstimateDetailData) {
	if x != nil {
		x.Data = v
	}
}

func (p *IntercityEstimateDetailResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityEstimateDetailResponse(%+v)", *p)
}

type IntercityEstimateDetailData struct {
	EstimateTraceId        string                  `json:"estimate_trace_id" form:"estimate_trace_id"`
	Mode                   int32                   `json:"mode" form:"mode"`
	HeadCard               *HeadCard               `json:"head_card,omitempty" form:"head_card"`
	StorePassengerInfoCard *StorePassengerInfoCard `json:"store_passenger_info_card,omitempty" form:"store_passenger_info_card"`
	RealNameSeatInfoCard   *RealNameSeatInfoCard   `json:"real_name_seat_info_card,omitempty" form:"real_name_seat_info_card"`
	SeatInfoCard           *SeatInfoCard           `json:"seat_info_card,omitempty" form:"seat_info_card"`
	RuleCard               *RuleCard               `json:"rule_card,omitempty" form:"rule_card"`
	BottomCard             *BottomCard             `json:"bottom_card,omitempty" form:"bottom_card"`
	OrderParams            *OrderParams            `json:"order_params,omitempty" form:"order_params"`
	PriceCard              *PriceCard              `json:"price_card,omitempty" form:"price_card"`
	StationPopup           *StationPopUp           `json:"station_popup,omitempty" form:"station_popup"`
	ExtraInfo              *ExtraInfo              `json:"extra_info,omitempty" form:"extra_info"`
	TravelCardInfo         *TravelCardInfo         `json:"travel_card_info,omitempty" form:"travel_card_info"` //出行卡信息
	OmegaInfo              *BusDetailOmegaInfo     `json:"omega_info,omitempty" form:"omega_info"`             //埋点
	BusShiftCard           *BusShiftCard           `json:"bus_shift_card,omitempty" form:"bus_shift_card"`     //埋点
	IsBestShift            *int32                  `json:"is_best_shift,omitempty" form:"is_best_shift"`
}

func (x *IntercityEstimateDetailData) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *IntercityEstimateDetailData) GetMode() (r int32) {
	if x != nil {
		return x.Mode
	}
	return r
}

func (x *IntercityEstimateDetailData) GetHeadCard() (r *HeadCard) {
	if x != nil {
		return x.HeadCard
	}
	return r
}

func (x *IntercityEstimateDetailData) GetStorePassengerInfoCard() (r *StorePassengerInfoCard) {
	if x != nil {
		return x.StorePassengerInfoCard
	}
	return r
}

func (x *IntercityEstimateDetailData) GetRealNameSeatInfoCard() (r *RealNameSeatInfoCard) {
	if x != nil {
		return x.RealNameSeatInfoCard
	}
	return r
}

func (x *IntercityEstimateDetailData) GetSeatInfoCard() (r *SeatInfoCard) {
	if x != nil {
		return x.SeatInfoCard
	}
	return r
}

func (x *IntercityEstimateDetailData) GetRuleCard() (r *RuleCard) {
	if x != nil {
		return x.RuleCard
	}
	return r
}

func (x *IntercityEstimateDetailData) GetBottomCard() (r *BottomCard) {
	if x != nil {
		return x.BottomCard
	}
	return r
}

func (x *IntercityEstimateDetailData) GetOrderParams() (r *OrderParams) {
	if x != nil {
		return x.OrderParams
	}
	return r
}

func (x *IntercityEstimateDetailData) GetPriceCard() (r *PriceCard) {
	if x != nil {
		return x.PriceCard
	}
	return r
}

func (x *IntercityEstimateDetailData) GetStationPopup() (r *StationPopUp) {
	if x != nil {
		return x.StationPopup
	}
	return r
}

func (x *IntercityEstimateDetailData) GetExtraInfo() (r *ExtraInfo) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *IntercityEstimateDetailData) GetTravelCardInfo() (r *TravelCardInfo) {
	if x != nil {
		return x.TravelCardInfo
	}
	return r
}

func (x *IntercityEstimateDetailData) GetOmegaInfo() (r *BusDetailOmegaInfo) {
	if x != nil {
		return x.OmegaInfo
	}
	return r
}

func (x *IntercityEstimateDetailData) GetBusShiftCard() (r *BusShiftCard) {
	if x != nil {
		return x.BusShiftCard
	}
	return r
}

func (x *IntercityEstimateDetailData) GetIsBestShift() (r int32) {
	if x != nil && x.IsBestShift != nil {
		return *x.IsBestShift
	}
	return r
}

func (x *IntercityEstimateDetailData) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (x *IntercityEstimateDetailData) SetMode(v int32) {
	if x != nil {
		x.Mode = v
	}
}

func (x *IntercityEstimateDetailData) SetHeadCard(v *HeadCard) {
	if x != nil {
		x.HeadCard = v
	}
}

func (x *IntercityEstimateDetailData) SetStorePassengerInfoCard(v *StorePassengerInfoCard) {
	if x != nil {
		x.StorePassengerInfoCard = v
	}
}

func (x *IntercityEstimateDetailData) SetRealNameSeatInfoCard(v *RealNameSeatInfoCard) {
	if x != nil {
		x.RealNameSeatInfoCard = v
	}
}

func (x *IntercityEstimateDetailData) SetSeatInfoCard(v *SeatInfoCard) {
	if x != nil {
		x.SeatInfoCard = v
	}
}

func (x *IntercityEstimateDetailData) SetRuleCard(v *RuleCard) {
	if x != nil {
		x.RuleCard = v
	}
}

func (x *IntercityEstimateDetailData) SetBottomCard(v *BottomCard) {
	if x != nil {
		x.BottomCard = v
	}
}

func (x *IntercityEstimateDetailData) SetOrderParams(v *OrderParams) {
	if x != nil {
		x.OrderParams = v
	}
}

func (x *IntercityEstimateDetailData) SetPriceCard(v *PriceCard) {
	if x != nil {
		x.PriceCard = v
	}
}

func (x *IntercityEstimateDetailData) SetStationPopup(v *StationPopUp) {
	if x != nil {
		x.StationPopup = v
	}
}

func (x *IntercityEstimateDetailData) SetExtraInfo(v *ExtraInfo) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (x *IntercityEstimateDetailData) SetTravelCardInfo(v *TravelCardInfo) {
	if x != nil {
		x.TravelCardInfo = v
	}
}

func (x *IntercityEstimateDetailData) SetOmegaInfo(v *BusDetailOmegaInfo) {
	if x != nil {
		x.OmegaInfo = v
	}
}

func (x *IntercityEstimateDetailData) SetBusShiftCard(v *BusShiftCard) {
	if x != nil {
		x.BusShiftCard = v
	}
}

func (x *IntercityEstimateDetailData) SetIsBestShift(v int32) {
	if x != nil {
		x.IsBestShift = &v
	}
}

func (p *IntercityEstimateDetailData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityEstimateDetailData(%+v)", *p)
}

type BusShiftCard struct {
	TimeMsg     string                   `json:"time_msg" form:"time_msg"`
	StationInfo *BusShiftCardStationInfo `json:"station_info,omitempty" form:"station_info"`
	SubTagList  []*BusShiftCardSubMsg    `json:"sub_tag_list" form:"sub_tag_list"`
	JumpButton  *JumpButton              `json:"jump_button,omitempty" form:"jump_button"`
	OmegaInfo   *BusShiftCardOmegaInfo   `json:"omega_info,omitempty" form:"omega_info"`
}

func (x *BusShiftCard) GetTimeMsg() (r string) {
	if x != nil {
		return x.TimeMsg
	}
	return r
}

func (x *BusShiftCard) GetStationInfo() (r *BusShiftCardStationInfo) {
	if x != nil {
		return x.StationInfo
	}
	return r
}

func (x *BusShiftCard) GetSubTagList() (r []*BusShiftCardSubMsg) {
	if x != nil {
		return x.SubTagList
	}
	return r
}

func (x *BusShiftCard) GetJumpButton() (r *JumpButton) {
	if x != nil {
		return x.JumpButton
	}
	return r
}

func (x *BusShiftCard) GetOmegaInfo() (r *BusShiftCardOmegaInfo) {
	if x != nil {
		return x.OmegaInfo
	}
	return r
}

func (x *BusShiftCard) SetTimeMsg(v string) {
	if x != nil {
		x.TimeMsg = v
	}
}

func (x *BusShiftCard) SetStationInfo(v *BusShiftCardStationInfo) {
	if x != nil {
		x.StationInfo = v
	}
}

func (x *BusShiftCard) SetSubTagList(v []*BusShiftCardSubMsg) {
	if x != nil {
		x.SubTagList = v
	}
}

func (x *BusShiftCard) SetJumpButton(v *JumpButton) {
	if x != nil {
		x.JumpButton = v
	}
}

func (x *BusShiftCard) SetOmegaInfo(v *BusShiftCardOmegaInfo) {
	if x != nil {
		x.OmegaInfo = v
	}
}

func (p *BusShiftCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusShiftCard(%+v)", *p)
}

type BusShiftCardOmegaInfo struct {
	CommonParams *BusShiftCardOmegaParam `json:"common_params,omitempty" form:"common_params"`
}

func (x *BusShiftCardOmegaInfo) GetCommonParams() (r *BusShiftCardOmegaParam) {
	if x != nil {
		return x.CommonParams
	}
	return r
}

func (x *BusShiftCardOmegaInfo) SetCommonParams(v *BusShiftCardOmegaParam) {
	if x != nil {
		x.CommonParams = v
	}
}

func (p *BusShiftCardOmegaInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusShiftCardOmegaInfo(%+v)", *p)
}

type BusShiftCardOmegaParam struct {
	GuideTraceId string `json:"guide_trace_id" form:"guide_trace_id"`
	IsBestShift  int32  `json:"is_best_shift" form:"is_best_shift"`
}

func (x *BusShiftCardOmegaParam) GetGuideTraceId() (r string) {
	if x != nil {
		return x.GuideTraceId
	}
	return r
}

func (x *BusShiftCardOmegaParam) GetIsBestShift() (r int32) {
	if x != nil {
		return x.IsBestShift
	}
	return r
}

func (x *BusShiftCardOmegaParam) SetGuideTraceId(v string) {
	if x != nil {
		x.GuideTraceId = v
	}
}

func (x *BusShiftCardOmegaParam) SetIsBestShift(v int32) {
	if x != nil {
		x.IsBestShift = v
	}
}

func (p *BusShiftCardOmegaParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusShiftCardOmegaParam(%+v)", *p)
}

type BusShiftCardSubMsg struct {
	Color   string `json:"color" form:"color"`
	Content string `json:"content" form:"content"`
}

func (x *BusShiftCardSubMsg) GetColor() (r string) {
	if x != nil {
		return x.Color
	}
	return r
}

func (x *BusShiftCardSubMsg) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *BusShiftCardSubMsg) SetColor(v string) {
	if x != nil {
		x.Color = v
	}
}

func (x *BusShiftCardSubMsg) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (p *BusShiftCardSubMsg) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusShiftCardSubMsg(%+v)", *p)
}

type BusShiftCardStationInfo struct {
	Start  *BusShiftCardStation `json:"start,omitempty" form:"start"`
	End    *BusShiftCardStation `json:"end,omitempty" form:"end"`
	Detail *BusShiftCardDetail  `json:"detail,omitempty" form:"detail"`
}

func (x *BusShiftCardStationInfo) GetStart() (r *BusShiftCardStation) {
	if x != nil {
		return x.Start
	}
	return r
}

func (x *BusShiftCardStationInfo) GetEnd() (r *BusShiftCardStation) {
	if x != nil {
		return x.End
	}
	return r
}

func (x *BusShiftCardStationInfo) GetDetail() (r *BusShiftCardDetail) {
	if x != nil {
		return x.Detail
	}
	return r
}

func (x *BusShiftCardStationInfo) SetStart(v *BusShiftCardStation) {
	if x != nil {
		x.Start = v
	}
}

func (x *BusShiftCardStationInfo) SetEnd(v *BusShiftCardStation) {
	if x != nil {
		x.End = v
	}
}

func (x *BusShiftCardStationInfo) SetDetail(v *BusShiftCardDetail) {
	if x != nil {
		x.Detail = v
	}
}

func (p *BusShiftCardStationInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusShiftCardStationInfo(%+v)", *p)
}

type BusShiftCardDetail struct {
	Title    string `json:"title" form:"title"`
	SubTitle string `json:"sub_title" form:"sub_title"`
}

func (x *BusShiftCardDetail) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *BusShiftCardDetail) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *BusShiftCardDetail) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *BusShiftCardDetail) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (p *BusShiftCardDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusShiftCardDetail(%+v)", *p)
}

type BusShiftCardStation struct {
	Name      string           `json:"name" form:"name"`
	StationId int32            `json:"station_id" form:"station_id"`
	Lat       string           `json:"lat" form:"lat"`
	Lng       string           `json:"lng" form:"lng"`
	ImgUrl    string           `json:"img_url" form:"img_url"`
	Tag       *BusShiftCardTag `json:"tag,omitempty" form:"tag"`
}

func (x *BusShiftCardStation) GetName() (r string) {
	if x != nil {
		return x.Name
	}
	return r
}

func (x *BusShiftCardStation) GetStationId() (r int32) {
	if x != nil {
		return x.StationId
	}
	return r
}

func (x *BusShiftCardStation) GetLat() (r string) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *BusShiftCardStation) GetLng() (r string) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *BusShiftCardStation) GetImgUrl() (r string) {
	if x != nil {
		return x.ImgUrl
	}
	return r
}

func (x *BusShiftCardStation) GetTag() (r *BusShiftCardTag) {
	if x != nil {
		return x.Tag
	}
	return r
}

func (x *BusShiftCardStation) SetName(v string) {
	if x != nil {
		x.Name = v
	}
}

func (x *BusShiftCardStation) SetStationId(v int32) {
	if x != nil {
		x.StationId = v
	}
}

func (x *BusShiftCardStation) SetLat(v string) {
	if x != nil {
		x.Lat = v
	}
}

func (x *BusShiftCardStation) SetLng(v string) {
	if x != nil {
		x.Lng = v
	}
}

func (x *BusShiftCardStation) SetImgUrl(v string) {
	if x != nil {
		x.ImgUrl = v
	}
}

func (x *BusShiftCardStation) SetTag(v *BusShiftCardTag) {
	if x != nil {
		x.Tag = v
	}
}

func (p *BusShiftCardStation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusShiftCardStation(%+v)", *p)
}

type BusShiftCardTag struct {
	Icon    string `json:"icon" form:"icon"`
	Content string `json:"content" form:"content"`
}

func (x *BusShiftCardTag) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *BusShiftCardTag) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *BusShiftCardTag) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *BusShiftCardTag) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (p *BusShiftCardTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusShiftCardTag(%+v)", *p)
}

type JumpButton struct {
	Url      *string `json:"url,omitempty" form:"url"`
	Text     string  `json:"text" form:"text"`
	Icon     string  `json:"icon" form:"icon"`
	GrayIcon string  `json:"gray_icon" form:"gray_icon"`
}

func (x *JumpButton) GetUrl() (r string) {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return r
}

func (x *JumpButton) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *JumpButton) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *JumpButton) GetGrayIcon() (r string) {
	if x != nil {
		return x.GrayIcon
	}
	return r
}

func (x *JumpButton) SetUrl(v string) {
	if x != nil {
		x.Url = &v
	}
}

func (x *JumpButton) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *JumpButton) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *JumpButton) SetGrayIcon(v string) {
	if x != nil {
		x.GrayIcon = v
	}
}

func (p *JumpButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("JumpButton(%+v)", *p)
}

type RuleMsgInfo struct {
	Text      string  `json:"text" form:"text"`
	Icon      *string `json:"icon,omitempty" form:"icon"`
	TextColor *string `json:"text_color,omitempty" form:"text_color"`
}

func (x *RuleMsgInfo) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *RuleMsgInfo) GetIcon() (r string) {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return r
}

func (x *RuleMsgInfo) GetTextColor() (r string) {
	if x != nil && x.TextColor != nil {
		return *x.TextColor
	}
	return r
}

func (x *RuleMsgInfo) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *RuleMsgInfo) SetIcon(v string) {
	if x != nil {
		x.Icon = &v
	}
}

func (x *RuleMsgInfo) SetTextColor(v string) {
	if x != nil {
		x.TextColor = &v
	}
}

func (p *RuleMsgInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuleMsgInfo(%+v)", *p)
}

type RebookRefundRuleInfo struct {
	RuleMsg             []*RuleMsgInfo `json:"rule_msg,omitempty" form:"rule_msg"`
	PurchaseRulesButton *Button        `json:"purchase_rules_button,omitempty" form:"purchase_rules_button"`
}

func (x *RebookRefundRuleInfo) GetRuleMsg() (r []*RuleMsgInfo) {
	if x != nil {
		return x.RuleMsg
	}
	return r
}

func (x *RebookRefundRuleInfo) GetPurchaseRulesButton() (r *Button) {
	if x != nil {
		return x.PurchaseRulesButton
	}
	return r
}

func (x *RebookRefundRuleInfo) SetRuleMsg(v []*RuleMsgInfo) {
	if x != nil {
		x.RuleMsg = v
	}
}

func (x *RebookRefundRuleInfo) SetPurchaseRulesButton(v *Button) {
	if x != nil {
		x.PurchaseRulesButton = v
	}
}

func (p *RebookRefundRuleInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RebookRefundRuleInfo(%+v)", *p)
}

type HeadCard struct {
	Icon                 string                `json:"icon" form:"icon"`
	Title                string                `json:"title" form:"title"`
	BackgroundImg        string                `json:"background_img" form:"background_img"`
	RebookButton         *Button               `json:"rebook_button,omitempty" form:"rebook_button"`
	StationInfo          *StationFullInfo      `json:"station_info,omitempty" form:"station_info"`
	CarPlate             *string               `json:"car_plate,omitempty" form:"car_plate"`
	TemplateName         string                `json:"template_name" form:"template_name"`
	TitleImgNew          string                `json:"title_img_new" form:"title_img_new"`
	RebookRefundRuleInfo *RebookRefundRuleInfo `json:"rebook_refund_rule_info,omitempty" form:"rebook_refund_rule_info"`
}

func (x *HeadCard) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *HeadCard) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *HeadCard) GetBackgroundImg() (r string) {
	if x != nil {
		return x.BackgroundImg
	}
	return r
}

func (x *HeadCard) GetRebookButton() (r *Button) {
	if x != nil {
		return x.RebookButton
	}
	return r
}

func (x *HeadCard) GetStationInfo() (r *StationFullInfo) {
	if x != nil {
		return x.StationInfo
	}
	return r
}

func (x *HeadCard) GetCarPlate() (r string) {
	if x != nil && x.CarPlate != nil {
		return *x.CarPlate
	}
	return r
}

func (x *HeadCard) GetTemplateName() (r string) {
	if x != nil {
		return x.TemplateName
	}
	return r
}

func (x *HeadCard) GetTitleImgNew() (r string) {
	if x != nil {
		return x.TitleImgNew
	}
	return r
}

func (x *HeadCard) GetRebookRefundRuleInfo() (r *RebookRefundRuleInfo) {
	if x != nil {
		return x.RebookRefundRuleInfo
	}
	return r
}

func (x *HeadCard) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *HeadCard) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *HeadCard) SetBackgroundImg(v string) {
	if x != nil {
		x.BackgroundImg = v
	}
}

func (x *HeadCard) SetRebookButton(v *Button) {
	if x != nil {
		x.RebookButton = v
	}
}

func (x *HeadCard) SetStationInfo(v *StationFullInfo) {
	if x != nil {
		x.StationInfo = v
	}
}

func (x *HeadCard) SetCarPlate(v string) {
	if x != nil {
		x.CarPlate = &v
	}
}

func (x *HeadCard) SetTemplateName(v string) {
	if x != nil {
		x.TemplateName = v
	}
}

func (x *HeadCard) SetTitleImgNew(v string) {
	if x != nil {
		x.TitleImgNew = v
	}
}

func (x *HeadCard) SetRebookRefundRuleInfo(v *RebookRefundRuleInfo) {
	if x != nil {
		x.RebookRefundRuleInfo = v
	}
}

func (p *HeadCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HeadCard(%+v)", *p)
}

type StationFullInfo struct {
	Title         string           `json:"title" form:"title"`
	StartStation  *StationDataInfo `json:"start_station,omitempty" form:"start_station"`
	EndStation    *StationDataInfo `json:"end_station,omitempty" form:"end_station"`
	MiddleStation *MiddleStation   `json:"middle_station,omitempty" form:"middle_station"`
	SubTitle      string           `json:"sub_title" form:"sub_title"`
}

func (x *StationFullInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *StationFullInfo) GetStartStation() (r *StationDataInfo) {
	if x != nil {
		return x.StartStation
	}
	return r
}

func (x *StationFullInfo) GetEndStation() (r *StationDataInfo) {
	if x != nil {
		return x.EndStation
	}
	return r
}

func (x *StationFullInfo) GetMiddleStation() (r *MiddleStation) {
	if x != nil {
		return x.MiddleStation
	}
	return r
}

func (x *StationFullInfo) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *StationFullInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *StationFullInfo) SetStartStation(v *StationDataInfo) {
	if x != nil {
		x.StartStation = v
	}
}

func (x *StationFullInfo) SetEndStation(v *StationDataInfo) {
	if x != nil {
		x.EndStation = v
	}
}

func (x *StationFullInfo) SetMiddleStation(v *MiddleStation) {
	if x != nil {
		x.MiddleStation = v
	}
}

func (x *StationFullInfo) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (p *StationFullInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StationFullInfo(%+v)", *p)
}

type StationDataInfo struct {
	DisplayName string   `json:"display_name" form:"display_name"`
	Lat         *float64 `json:"lat,omitempty" form:"lat"`
	Lng         *float64 `json:"lng,omitempty" form:"lng"`
}

func (x *StationDataInfo) GetDisplayName() (r string) {
	if x != nil {
		return x.DisplayName
	}
	return r
}

func (x *StationDataInfo) GetLat() (r float64) {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return r
}

func (x *StationDataInfo) GetLng() (r float64) {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return r
}

func (x *StationDataInfo) SetDisplayName(v string) {
	if x != nil {
		x.DisplayName = v
	}
}

func (x *StationDataInfo) SetLat(v float64) {
	if x != nil {
		x.Lat = &v
	}
}

func (x *StationDataInfo) SetLng(v float64) {
	if x != nil {
		x.Lng = &v
	}
}

func (p *StationDataInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StationDataInfo(%+v)", *p)
}

type MiddleStation struct {
	Title       string       `json:"title" form:"title"`
	PopupDetail *PopupDetail `json:"popup_detail,omitempty" form:"popup_detail"`
}

func (x *MiddleStation) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *MiddleStation) GetPopupDetail() (r *PopupDetail) {
	if x != nil {
		return x.PopupDetail
	}
	return r
}

func (x *MiddleStation) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *MiddleStation) SetPopupDetail(v *PopupDetail) {
	if x != nil {
		x.PopupDetail = v
	}
}

func (p *MiddleStation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MiddleStation(%+v)", *p)
}

type PopupDetail struct {
	Title       string         `json:"title" form:"title"`
	Subtitle    string         `json:"subtitle" form:"subtitle"`
	StationList []*StationItem `json:"station_list" form:"station_list"`
}

func (x *PopupDetail) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *PopupDetail) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *PopupDetail) GetStationList() (r []*StationItem) {
	if x != nil {
		return x.StationList
	}
	return r
}

func (x *PopupDetail) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *PopupDetail) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *PopupDetail) SetStationList(v []*StationItem) {
	if x != nil {
		x.StationList = v
	}
}

func (p *PopupDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PopupDetail(%+v)", *p)
}

type StationItem struct {
	Icon         string `json:"icon" form:"icon"`
	DisplayName  string `json:"display_name" form:"display_name"`
	DepartureMsg string `json:"departure_msg" form:"departure_msg"`
	Type         int32  `json:"type" form:"type"`
}

func (x *StationItem) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *StationItem) GetDisplayName() (r string) {
	if x != nil {
		return x.DisplayName
	}
	return r
}

func (x *StationItem) GetDepartureMsg() (r string) {
	if x != nil {
		return x.DepartureMsg
	}
	return r
}

func (x *StationItem) GetType() (r int32) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *StationItem) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *StationItem) SetDisplayName(v string) {
	if x != nil {
		x.DisplayName = v
	}
}

func (x *StationItem) SetDepartureMsg(v string) {
	if x != nil {
		x.DepartureMsg = v
	}
}

func (x *StationItem) SetType(v int32) {
	if x != nil {
		x.Type = v
	}
}

func (p *StationItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StationItem(%+v)", *p)
}

type StorePassengerInfoCard struct {
	Title             string        `json:"title" form:"title"`
	FormDetail        []*FormDetail `json:"form_detail" form:"form_detail"`
	ConfirmButton     *Button       `json:"confirm_button,omitempty" form:"confirm_button"`
	Subtitle          *string       `json:"subtitle,omitempty" form:"subtitle"`
	ChildAgeLimit     *int32        `json:"child_age_limit,omitempty" form:"child_age_limit"`
	AlertInfo         *AlertInfo    `json:"alert_info,omitempty" form:"alert_info"`
	GetIdentityButton *JumpButton   `json:"get_identity_button,omitempty" form:"get_identity_button"` //能否从用户中心获取实名信息
}

func (x *StorePassengerInfoCard) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *StorePassengerInfoCard) GetFormDetail() (r []*FormDetail) {
	if x != nil {
		return x.FormDetail
	}
	return r
}

func (x *StorePassengerInfoCard) GetConfirmButton() (r *Button) {
	if x != nil {
		return x.ConfirmButton
	}
	return r
}

func (x *StorePassengerInfoCard) GetSubtitle() (r string) {
	if x != nil && x.Subtitle != nil {
		return *x.Subtitle
	}
	return r
}

func (x *StorePassengerInfoCard) GetChildAgeLimit() (r int32) {
	if x != nil && x.ChildAgeLimit != nil {
		return *x.ChildAgeLimit
	}
	return r
}

func (x *StorePassengerInfoCard) GetAlertInfo() (r *AlertInfo) {
	if x != nil {
		return x.AlertInfo
	}
	return r
}

func (x *StorePassengerInfoCard) GetGetIdentityButton() (r *JumpButton) {
	if x != nil {
		return x.GetIdentityButton
	}
	return r
}

func (x *StorePassengerInfoCard) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *StorePassengerInfoCard) SetFormDetail(v []*FormDetail) {
	if x != nil {
		x.FormDetail = v
	}
}

func (x *StorePassengerInfoCard) SetConfirmButton(v *Button) {
	if x != nil {
		x.ConfirmButton = v
	}
}

func (x *StorePassengerInfoCard) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = &v
	}
}

func (x *StorePassengerInfoCard) SetChildAgeLimit(v int32) {
	if x != nil {
		x.ChildAgeLimit = &v
	}
}

func (x *StorePassengerInfoCard) SetAlertInfo(v *AlertInfo) {
	if x != nil {
		x.AlertInfo = v
	}
}

func (x *StorePassengerInfoCard) SetGetIdentityButton(v *JumpButton) {
	if x != nil {
		x.GetIdentityButton = v
	}
}

func (p *StorePassengerInfoCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StorePassengerInfoCard(%+v)", *p)
}

type FormDetail struct {
	Key          string        `json:"key" form:"key"`
	Type         int32         `json:"type" form:"type"`
	Title        string        `json:"title" form:"title"`
	DefaultValue *string       `json:"default_value,omitempty" form:"default_value"`
	DefaultIndex *int32        `json:"default_index,omitempty" form:"default_index"`
	SelectItem   []*SelectItem `json:"select_item,omitempty" form:"select_item"`
}

func (x *FormDetail) GetKey() (r string) {
	if x != nil {
		return x.Key
	}
	return r
}

func (x *FormDetail) GetType() (r int32) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *FormDetail) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *FormDetail) GetDefaultValue() (r string) {
	if x != nil && x.DefaultValue != nil {
		return *x.DefaultValue
	}
	return r
}

func (x *FormDetail) GetDefaultIndex() (r int32) {
	if x != nil && x.DefaultIndex != nil {
		return *x.DefaultIndex
	}
	return r
}

func (x *FormDetail) GetSelectItem() (r []*SelectItem) {
	if x != nil {
		return x.SelectItem
	}
	return r
}

func (x *FormDetail) SetKey(v string) {
	if x != nil {
		x.Key = v
	}
}

func (x *FormDetail) SetType(v int32) {
	if x != nil {
		x.Type = v
	}
}

func (x *FormDetail) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *FormDetail) SetDefaultValue(v string) {
	if x != nil {
		x.DefaultValue = &v
	}
}

func (x *FormDetail) SetDefaultIndex(v int32) {
	if x != nil {
		x.DefaultIndex = &v
	}
}

func (x *FormDetail) SetSelectItem(v []*SelectItem) {
	if x != nil {
		x.SelectItem = v
	}
}

func (p *FormDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FormDetail(%+v)", *p)
}

type SelectItem struct {
	DisplayName  string  `json:"display_name" form:"display_name"`
	TicketType   *int32  `json:"ticket_type,omitempty" form:"ticket_type"`
	IdentityType *int32  `json:"identity_type,omitempty" form:"identity_type"`
	BubbleText   *string `json:"bubble_text,omitempty" form:"bubble_text"` //点击后展示的气泡文案
}

func (x *SelectItem) GetDisplayName() (r string) {
	if x != nil {
		return x.DisplayName
	}
	return r
}

func (x *SelectItem) GetTicketType() (r int32) {
	if x != nil && x.TicketType != nil {
		return *x.TicketType
	}
	return r
}

func (x *SelectItem) GetIdentityType() (r int32) {
	if x != nil && x.IdentityType != nil {
		return *x.IdentityType
	}
	return r
}

func (x *SelectItem) GetBubbleText() (r string) {
	if x != nil && x.BubbleText != nil {
		return *x.BubbleText
	}
	return r
}

func (x *SelectItem) SetDisplayName(v string) {
	if x != nil {
		x.DisplayName = v
	}
}

func (x *SelectItem) SetTicketType(v int32) {
	if x != nil {
		x.TicketType = &v
	}
}

func (x *SelectItem) SetIdentityType(v int32) {
	if x != nil {
		x.IdentityType = &v
	}
}

func (x *SelectItem) SetBubbleText(v string) {
	if x != nil {
		x.BubbleText = &v
	}
}

func (p *SelectItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SelectItem(%+v)", *p)
}

type RealNameSeatInfoCard struct {
	SeatLimit                 *SeatLimit                 `json:"seat_limit,omitempty" form:"seat_limit"`
	ChoosePassengerDetail     *ChoosePassengerDetail     `json:"choose_passenger_detail,omitempty" form:"choose_passenger_detail"`
	SelectedPassengerDetail   *SelectedPassengerDetail   `json:"selected_passenger_detail,omitempty" form:"selected_passenger_detail"`
	NoRealNamePassengerDetail *NoRealNamePassengerDetail `json:"no_real_name_passenger_detail,omitempty" form:"no_real_name_passenger_detail"`
}

func (x *RealNameSeatInfoCard) GetSeatLimit() (r *SeatLimit) {
	if x != nil {
		return x.SeatLimit
	}
	return r
}

func (x *RealNameSeatInfoCard) GetChoosePassengerDetail() (r *ChoosePassengerDetail) {
	if x != nil {
		return x.ChoosePassengerDetail
	}
	return r
}

func (x *RealNameSeatInfoCard) GetSelectedPassengerDetail() (r *SelectedPassengerDetail) {
	if x != nil {
		return x.SelectedPassengerDetail
	}
	return r
}

func (x *RealNameSeatInfoCard) GetNoRealNamePassengerDetail() (r *NoRealNamePassengerDetail) {
	if x != nil {
		return x.NoRealNamePassengerDetail
	}
	return r
}

func (x *RealNameSeatInfoCard) SetSeatLimit(v *SeatLimit) {
	if x != nil {
		x.SeatLimit = v
	}
}

func (x *RealNameSeatInfoCard) SetChoosePassengerDetail(v *ChoosePassengerDetail) {
	if x != nil {
		x.ChoosePassengerDetail = v
	}
}

func (x *RealNameSeatInfoCard) SetSelectedPassengerDetail(v *SelectedPassengerDetail) {
	if x != nil {
		x.SelectedPassengerDetail = v
	}
}

func (x *RealNameSeatInfoCard) SetNoRealNamePassengerDetail(v *NoRealNamePassengerDetail) {
	if x != nil {
		x.NoRealNamePassengerDetail = v
	}
}

func (p *RealNameSeatInfoCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RealNameSeatInfoCard(%+v)", *p)
}

type AlertInfo struct {
	Title       string `json:"title" form:"title"`
	Description string `json:"description" form:"description"`
	LeftButton  string `json:"left_button" form:"left_button"`
	RightButton string `json:"right_button" form:"right_button"`
}

func (x *AlertInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *AlertInfo) GetDescription() (r string) {
	if x != nil {
		return x.Description
	}
	return r
}

func (x *AlertInfo) GetLeftButton() (r string) {
	if x != nil {
		return x.LeftButton
	}
	return r
}

func (x *AlertInfo) GetRightButton() (r string) {
	if x != nil {
		return x.RightButton
	}
	return r
}

func (x *AlertInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *AlertInfo) SetDescription(v string) {
	if x != nil {
		x.Description = v
	}
}

func (x *AlertInfo) SetLeftButton(v string) {
	if x != nil {
		x.LeftButton = v
	}
}

func (x *AlertInfo) SetRightButton(v string) {
	if x != nil {
		x.RightButton = v
	}
}

func (p *AlertInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AlertInfo(%+v)", *p)
}

type SeatLimit struct {
	CarryChildrenLimit *RuleLimit `json:"carry_children_limit,omitempty" form:"carry_children_limit"`
	PassengerLimit     *RuleLimit `json:"passenger_limit,omitempty" form:"passenger_limit"`
}

func (x *SeatLimit) GetCarryChildrenLimit() (r *RuleLimit) {
	if x != nil {
		return x.CarryChildrenLimit
	}
	return r
}

func (x *SeatLimit) GetPassengerLimit() (r *RuleLimit) {
	if x != nil {
		return x.PassengerLimit
	}
	return r
}

func (x *SeatLimit) SetCarryChildrenLimit(v *RuleLimit) {
	if x != nil {
		x.CarryChildrenLimit = v
	}
}

func (x *SeatLimit) SetPassengerLimit(v *RuleLimit) {
	if x != nil {
		x.PassengerLimit = v
	}
}

func (p *SeatLimit) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SeatLimit(%+v)", *p)
}

type RuleLimit struct {
	MaxInventory int32   `json:"max_inventory" form:"max_inventory"`
	Toast        string  `json:"toast" form:"toast"`
	TicketTypes  []int32 `json:"ticket_types" form:"ticket_types"`
}

func (x *RuleLimit) GetMaxInventory() (r int32) {
	if x != nil {
		return x.MaxInventory
	}
	return r
}

func (x *RuleLimit) GetToast() (r string) {
	if x != nil {
		return x.Toast
	}
	return r
}

func (x *RuleLimit) GetTicketTypes() (r []int32) {
	if x != nil {
		return x.TicketTypes
	}
	return r
}

func (x *RuleLimit) SetMaxInventory(v int32) {
	if x != nil {
		x.MaxInventory = v
	}
}

func (x *RuleLimit) SetToast(v string) {
	if x != nil {
		x.Toast = v
	}
}

func (x *RuleLimit) SetTicketTypes(v []int32) {
	if x != nil {
		x.TicketTypes = v
	}
}

func (p *RuleLimit) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuleLimit(%+v)", *p)
}

type ChoosePassengerDetail struct {
	Title              string           `json:"title" form:"title"`
	PassengerList      []*PassengerItem `json:"passenger_list" form:"passenger_list"`
	AddPassengerButton *Button          `json:"add_passenger_button,omitempty" form:"add_passenger_button"`
	MoreButton         *Button          `json:"more_button,omitempty" form:"more_button"`
}

func (x *ChoosePassengerDetail) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *ChoosePassengerDetail) GetPassengerList() (r []*PassengerItem) {
	if x != nil {
		return x.PassengerList
	}
	return r
}

func (x *ChoosePassengerDetail) GetAddPassengerButton() (r *Button) {
	if x != nil {
		return x.AddPassengerButton
	}
	return r
}

func (x *ChoosePassengerDetail) GetMoreButton() (r *Button) {
	if x != nil {
		return x.MoreButton
	}
	return r
}

func (x *ChoosePassengerDetail) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *ChoosePassengerDetail) SetPassengerList(v []*PassengerItem) {
	if x != nil {
		x.PassengerList = v
	}
}

func (x *ChoosePassengerDetail) SetAddPassengerButton(v *Button) {
	if x != nil {
		x.AddPassengerButton = v
	}
}

func (x *ChoosePassengerDetail) SetMoreButton(v *Button) {
	if x != nil {
		x.MoreButton = v
	}
}

func (p *ChoosePassengerDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChoosePassengerDetail(%+v)", *p)
}

type SelectedPassengerDetail struct {
	Title               string           `json:"title" form:"title"`
	PassengerList       []*PassengerItem `json:"passenger_list" form:"passenger_list"`
	Subtitle            *string          `json:"subtitle,omitempty" form:"subtitle"`
	PassengerListButton *Button          `json:"passenger_list_button,omitempty" form:"passenger_list_button"`
}

func (x *SelectedPassengerDetail) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *SelectedPassengerDetail) GetPassengerList() (r []*PassengerItem) {
	if x != nil {
		return x.PassengerList
	}
	return r
}

func (x *SelectedPassengerDetail) GetSubtitle() (r string) {
	if x != nil && x.Subtitle != nil {
		return *x.Subtitle
	}
	return r
}

func (x *SelectedPassengerDetail) GetPassengerListButton() (r *Button) {
	if x != nil {
		return x.PassengerListButton
	}
	return r
}

func (x *SelectedPassengerDetail) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *SelectedPassengerDetail) SetPassengerList(v []*PassengerItem) {
	if x != nil {
		x.PassengerList = v
	}
}

func (x *SelectedPassengerDetail) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = &v
	}
}

func (x *SelectedPassengerDetail) SetPassengerListButton(v *Button) {
	if x != nil {
		x.PassengerListButton = v
	}
}

func (p *SelectedPassengerDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SelectedPassengerDetail(%+v)", *p)
}

type PassengerItem struct {
	PassengerName  *string                 `json:"passenger_name,omitempty" form:"passenger_name"`
	TicketType     int32                   `json:"ticket_type" form:"ticket_type"`
	IdentityId     *string                 `json:"identity_id,omitempty" form:"identity_id"`
	IdentityType   *int32                  `json:"identity_type,omitempty" form:"identity_type"`
	EncryptionId   *string                 `json:"encryption_id,omitempty" form:"encryption_id"`
	PassengerCount *int32                  `json:"passenger_count,omitempty" form:"passenger_count"`
	IsSelected     *int32                  `json:"is_selected,omitempty" form:"is_selected"`
	SubtitleList   []*NewFormFeeDesc       `json:"subtitle_list,omitempty" form:"subtitle_list"`
	LinkUrl        *string                 `json:"link_url,omitempty" form:"link_url"`
	Disable        *int32                  `json:"disable,omitempty" form:"disable"`
	DisableToast   *string                 `json:"disable_toast,omitempty" form:"disable_toast"`
	Title          *string                 `json:"title,omitempty" form:"title"`
	Subtitle       *string                 `json:"subtitle,omitempty" form:"subtitle"`
	Weight         *int32                  `json:"weight,omitempty" form:"weight"`
	FeeMsg         *string                 `json:"fee_msg,omitempty" form:"fee_msg"`
	FeeDescList    []*TagWithIconAndBorder `json:"fee_desc_list,omitempty" form:"fee_desc_list"`
	DiscountMsg    *NewFormFeeDesc         `json:"discount_msg,omitempty" form:"discount_msg"`
}

func (x *PassengerItem) GetPassengerName() (r string) {
	if x != nil && x.PassengerName != nil {
		return *x.PassengerName
	}
	return r
}

func (x *PassengerItem) GetTicketType() (r int32) {
	if x != nil {
		return x.TicketType
	}
	return r
}

func (x *PassengerItem) GetIdentityId() (r string) {
	if x != nil && x.IdentityId != nil {
		return *x.IdentityId
	}
	return r
}

func (x *PassengerItem) GetIdentityType() (r int32) {
	if x != nil && x.IdentityType != nil {
		return *x.IdentityType
	}
	return r
}

func (x *PassengerItem) GetEncryptionId() (r string) {
	if x != nil && x.EncryptionId != nil {
		return *x.EncryptionId
	}
	return r
}

func (x *PassengerItem) GetPassengerCount() (r int32) {
	if x != nil && x.PassengerCount != nil {
		return *x.PassengerCount
	}
	return r
}

func (x *PassengerItem) GetIsSelected() (r int32) {
	if x != nil && x.IsSelected != nil {
		return *x.IsSelected
	}
	return r
}

func (x *PassengerItem) GetSubtitleList() (r []*NewFormFeeDesc) {
	if x != nil {
		return x.SubtitleList
	}
	return r
}

func (x *PassengerItem) GetLinkUrl() (r string) {
	if x != nil && x.LinkUrl != nil {
		return *x.LinkUrl
	}
	return r
}

func (x *PassengerItem) GetDisable() (r int32) {
	if x != nil && x.Disable != nil {
		return *x.Disable
	}
	return r
}

func (x *PassengerItem) GetDisableToast() (r string) {
	if x != nil && x.DisableToast != nil {
		return *x.DisableToast
	}
	return r
}

func (x *PassengerItem) GetTitle() (r string) {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return r
}

func (x *PassengerItem) GetSubtitle() (r string) {
	if x != nil && x.Subtitle != nil {
		return *x.Subtitle
	}
	return r
}

func (x *PassengerItem) GetWeight() (r int32) {
	if x != nil && x.Weight != nil {
		return *x.Weight
	}
	return r
}

func (x *PassengerItem) GetFeeMsg() (r string) {
	if x != nil && x.FeeMsg != nil {
		return *x.FeeMsg
	}
	return r
}

func (x *PassengerItem) GetFeeDescList() (r []*TagWithIconAndBorder) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *PassengerItem) GetDiscountMsg() (r *NewFormFeeDesc) {
	if x != nil {
		return x.DiscountMsg
	}
	return r
}

func (x *PassengerItem) SetPassengerName(v string) {
	if x != nil {
		x.PassengerName = &v
	}
}

func (x *PassengerItem) SetTicketType(v int32) {
	if x != nil {
		x.TicketType = v
	}
}

func (x *PassengerItem) SetIdentityId(v string) {
	if x != nil {
		x.IdentityId = &v
	}
}

func (x *PassengerItem) SetIdentityType(v int32) {
	if x != nil {
		x.IdentityType = &v
	}
}

func (x *PassengerItem) SetEncryptionId(v string) {
	if x != nil {
		x.EncryptionId = &v
	}
}

func (x *PassengerItem) SetPassengerCount(v int32) {
	if x != nil {
		x.PassengerCount = &v
	}
}

func (x *PassengerItem) SetIsSelected(v int32) {
	if x != nil {
		x.IsSelected = &v
	}
}

func (x *PassengerItem) SetSubtitleList(v []*NewFormFeeDesc) {
	if x != nil {
		x.SubtitleList = v
	}
}

func (x *PassengerItem) SetLinkUrl(v string) {
	if x != nil {
		x.LinkUrl = &v
	}
}

func (x *PassengerItem) SetDisable(v int32) {
	if x != nil {
		x.Disable = &v
	}
}

func (x *PassengerItem) SetDisableToast(v string) {
	if x != nil {
		x.DisableToast = &v
	}
}

func (x *PassengerItem) SetTitle(v string) {
	if x != nil {
		x.Title = &v
	}
}

func (x *PassengerItem) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = &v
	}
}

func (x *PassengerItem) SetWeight(v int32) {
	if x != nil {
		x.Weight = &v
	}
}

func (x *PassengerItem) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = &v
	}
}

func (x *PassengerItem) SetFeeDescList(v []*TagWithIconAndBorder) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *PassengerItem) SetDiscountMsg(v *NewFormFeeDesc) {
	if x != nil {
		x.DiscountMsg = v
	}
}

func (p *PassengerItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassengerItem(%+v)", *p)
}

type NoRealNamePassengerDetail struct {
	PassengerList []*PassengerItem `json:"passenger_list" form:"passenger_list"`
}

func (x *NoRealNamePassengerDetail) GetPassengerList() (r []*PassengerItem) {
	if x != nil {
		return x.PassengerList
	}
	return r
}

func (x *NoRealNamePassengerDetail) SetPassengerList(v []*PassengerItem) {
	if x != nil {
		x.PassengerList = v
	}
}

func (p *NoRealNamePassengerDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NoRealNamePassengerDetail(%+v)", *p)
}

type SeatInfoCard struct {
	SeatLimit     *SeatLimit    `json:"seat_limit,omitempty" form:"seat_limit"`
	Title         string        `json:"title" form:"title"`
	Subtitle      string        `json:"subtitle" form:"subtitle"`
	PassengerList []*SeatDetail `json:"passenger_list" form:"passenger_list"`
}

func (x *SeatInfoCard) GetSeatLimit() (r *SeatLimit) {
	if x != nil {
		return x.SeatLimit
	}
	return r
}

func (x *SeatInfoCard) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *SeatInfoCard) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *SeatInfoCard) GetPassengerList() (r []*SeatDetail) {
	if x != nil {
		return x.PassengerList
	}
	return r
}

func (x *SeatInfoCard) SetSeatLimit(v *SeatLimit) {
	if x != nil {
		x.SeatLimit = v
	}
}

func (x *SeatInfoCard) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *SeatInfoCard) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *SeatInfoCard) SetPassengerList(v []*SeatDetail) {
	if x != nil {
		x.PassengerList = v
	}
}

func (p *SeatInfoCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SeatInfoCard(%+v)", *p)
}

type SeatDetail struct {
	TicketType     int32             `json:"ticket_type" form:"ticket_type"`
	Title          string            `json:"title" form:"title"`
	Subtitle       string            `json:"subtitle" form:"subtitle"`
	Icon           string            `json:"icon" form:"icon"`
	PassengerCount int32             `json:"passenger_count" form:"passenger_count"`
	BubbleText     *string           `json:"bubble_text,omitempty" form:"bubble_text"`     //点击后展示的气泡文案
	SubtitleList   []*NewFormFeeDesc `json:"subtitle_list,omitempty" form:"subtitle_list"` //点击后展示的气泡文案
}

func (x *SeatDetail) GetTicketType() (r int32) {
	if x != nil {
		return x.TicketType
	}
	return r
}

func (x *SeatDetail) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *SeatDetail) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *SeatDetail) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *SeatDetail) GetPassengerCount() (r int32) {
	if x != nil {
		return x.PassengerCount
	}
	return r
}

func (x *SeatDetail) GetBubbleText() (r string) {
	if x != nil && x.BubbleText != nil {
		return *x.BubbleText
	}
	return r
}

func (x *SeatDetail) GetSubtitleList() (r []*NewFormFeeDesc) {
	if x != nil {
		return x.SubtitleList
	}
	return r
}

func (x *SeatDetail) SetTicketType(v int32) {
	if x != nil {
		x.TicketType = v
	}
}

func (x *SeatDetail) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *SeatDetail) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *SeatDetail) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *SeatDetail) SetPassengerCount(v int32) {
	if x != nil {
		x.PassengerCount = v
	}
}

func (x *SeatDetail) SetBubbleText(v string) {
	if x != nil {
		x.BubbleText = &v
	}
}

func (x *SeatDetail) SetSubtitleList(v []*NewFormFeeDesc) {
	if x != nil {
		x.SubtitleList = v
	}
}

func (p *SeatDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SeatDetail(%+v)", *p)
}

type RuleCard struct {
	RuleList []string `json:"rule_list" form:"rule_list"`
	Title    *string  `json:"title,omitempty" form:"title"`
}

func (x *RuleCard) GetRuleList() (r []string) {
	if x != nil {
		return x.RuleList
	}
	return r
}

func (x *RuleCard) GetTitle() (r string) {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return r
}

func (x *RuleCard) SetRuleList(v []string) {
	if x != nil {
		x.RuleList = v
	}
}

func (x *RuleCard) SetTitle(v string) {
	if x != nil {
		x.Title = &v
	}
}

func (p *RuleCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuleCard(%+v)", *p)
}

type BottomCard struct {
	EstimateInfo *EstimateInfo  `json:"estimate_info,omitempty" form:"estimate_info"`
	TermsList    []*BottomTerms `json:"terms_list" form:"terms_list"`
}

func (x *BottomCard) GetEstimateInfo() (r *EstimateInfo) {
	if x != nil {
		return x.EstimateInfo
	}
	return r
}

func (x *BottomCard) GetTermsList() (r []*BottomTerms) {
	if x != nil {
		return x.TermsList
	}
	return r
}

func (x *BottomCard) SetEstimateInfo(v *EstimateInfo) {
	if x != nil {
		x.EstimateInfo = v
	}
}

func (x *BottomCard) SetTermsList(v []*BottomTerms) {
	if x != nil {
		x.TermsList = v
	}
}

func (p *BottomCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BottomCard(%+v)", *p)
}

type BottomTerms struct {
	HasCheckBox int32       `json:"has_check_box" form:"has_check_box"`
	Desc        string      `json:"desc" form:"desc"`
	BgColor     string      `json:"bg_color" form:"bg_color"`
	Popup       *TermsPopup `json:"popup,omitempty" form:"popup"`
}

func (x *BottomTerms) GetHasCheckBox() (r int32) {
	if x != nil {
		return x.HasCheckBox
	}
	return r
}

func (x *BottomTerms) GetDesc() (r string) {
	if x != nil {
		return x.Desc
	}
	return r
}

func (x *BottomTerms) GetBgColor() (r string) {
	if x != nil {
		return x.BgColor
	}
	return r
}

func (x *BottomTerms) GetPopup() (r *TermsPopup) {
	if x != nil {
		return x.Popup
	}
	return r
}

func (x *BottomTerms) SetHasCheckBox(v int32) {
	if x != nil {
		x.HasCheckBox = v
	}
}

func (x *BottomTerms) SetDesc(v string) {
	if x != nil {
		x.Desc = v
	}
}

func (x *BottomTerms) SetBgColor(v string) {
	if x != nil {
		x.BgColor = v
	}
}

func (x *BottomTerms) SetPopup(v *TermsPopup) {
	if x != nil {
		x.Popup = v
	}
}

func (p *BottomTerms) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BottomTerms(%+v)", *p)
}

type TermsPopup struct {
	Title           string `json:"title" form:"title"`
	SubTitle        string `json:"sub_title" form:"sub_title"`
	LeftButtonText  string `json:"left_button_text" form:"left_button_text"`
	RightButtonText string `json:"right_button_text" form:"right_button_text"`
}

func (x *TermsPopup) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *TermsPopup) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *TermsPopup) GetLeftButtonText() (r string) {
	if x != nil {
		return x.LeftButtonText
	}
	return r
}

func (x *TermsPopup) GetRightButtonText() (r string) {
	if x != nil {
		return x.RightButtonText
	}
	return r
}

func (x *TermsPopup) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *TermsPopup) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *TermsPopup) SetLeftButtonText(v string) {
	if x != nil {
		x.LeftButtonText = v
	}
}

func (x *TermsPopup) SetRightButtonText(v string) {
	if x != nil {
		x.RightButtonText = v
	}
}

func (p *TermsPopup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TermsPopup(%+v)", *p)
}

type EstimateInfo struct {
	EstimateId        string                  `json:"estimate_id" form:"estimate_id"`
	FeeAmount         string                  `json:"fee_amount" form:"fee_amount"`
	FeeMsg            string                  `json:"fee_msg" form:"fee_msg"`
	FeeDescList       []*TagWithIconAndBorder `json:"fee_desc_list,omitempty" form:"fee_desc_list"`
	FeeDetailUrl      string                  `json:"fee_detail_url" form:"fee_detail_url"`
	ExtraMap          *IntercityNewOrderParam `json:"extra_map,omitempty" form:"extra_map"`
	ConfirmButtonText string                  `json:"confirm_button_text" form:"confirm_button_text"`
	Disable           int32                   `json:"disable" form:"disable"`
	DisableToast      *string                 `json:"disable_toast,omitempty" form:"disable_toast"`
}

func (x *EstimateInfo) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *EstimateInfo) GetFeeAmount() (r string) {
	if x != nil {
		return x.FeeAmount
	}
	return r
}

func (x *EstimateInfo) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *EstimateInfo) GetFeeDescList() (r []*TagWithIconAndBorder) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *EstimateInfo) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *EstimateInfo) GetExtraMap() (r *IntercityNewOrderParam) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *EstimateInfo) GetConfirmButtonText() (r string) {
	if x != nil {
		return x.ConfirmButtonText
	}
	return r
}

func (x *EstimateInfo) GetDisable() (r int32) {
	if x != nil {
		return x.Disable
	}
	return r
}

func (x *EstimateInfo) GetDisableToast() (r string) {
	if x != nil && x.DisableToast != nil {
		return *x.DisableToast
	}
	return r
}

func (x *EstimateInfo) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *EstimateInfo) SetFeeAmount(v string) {
	if x != nil {
		x.FeeAmount = v
	}
}

func (x *EstimateInfo) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *EstimateInfo) SetFeeDescList(v []*TagWithIconAndBorder) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *EstimateInfo) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *EstimateInfo) SetExtraMap(v *IntercityNewOrderParam) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *EstimateInfo) SetConfirmButtonText(v string) {
	if x != nil {
		x.ConfirmButtonText = v
	}
}

func (x *EstimateInfo) SetDisable(v int32) {
	if x != nil {
		x.Disable = v
	}
}

func (x *EstimateInfo) SetDisableToast(v string) {
	if x != nil {
		x.DisableToast = &v
	}
}

func (p *EstimateInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EstimateInfo(%+v)", *p)
}

type Button struct {
	Text                string   `json:"text" form:"text"`
	FontColor           *string  `json:"font_color,omitempty" form:"font_color"`
	LinkUrl             *string  `json:"link_url,omitempty" form:"link_url"`
	BackgroundGradients []string `json:"background_gradients,omitempty" form:"background_gradients"`
	ActionType          *int32   `json:"action_type,omitempty" form:"action_type"`
}

func (x *Button) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *Button) GetFontColor() (r string) {
	if x != nil && x.FontColor != nil {
		return *x.FontColor
	}
	return r
}

func (x *Button) GetLinkUrl() (r string) {
	if x != nil && x.LinkUrl != nil {
		return *x.LinkUrl
	}
	return r
}

func (x *Button) GetBackgroundGradients() (r []string) {
	if x != nil {
		return x.BackgroundGradients
	}
	return r
}

func (x *Button) GetActionType() (r int32) {
	if x != nil && x.ActionType != nil {
		return *x.ActionType
	}
	return r
}

func (x *Button) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *Button) SetFontColor(v string) {
	if x != nil {
		x.FontColor = &v
	}
}

func (x *Button) SetLinkUrl(v string) {
	if x != nil {
		x.LinkUrl = &v
	}
}

func (x *Button) SetBackgroundGradients(v []string) {
	if x != nil {
		x.BackgroundGradients = v
	}
}

func (x *Button) SetActionType(v int32) {
	if x != nil {
		x.ActionType = &v
	}
}

func (p *Button) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Button(%+v)", *p)
}

type OrderParams struct {
	BusServiceShiftId string  `json:"bus_service_shift_id" form:"bus_service_shift_id"`
	DepartureTime     string  `json:"departure_time" form:"departure_time"`
	Type              int32   `json:"type" form:"type"`
	ComboId           int32   `json:"combo_id" form:"combo_id"`
	FromStationId     int32   `json:"from_station_id" form:"from_station_id"`
	ToStationId       int32   `json:"to_station_id" form:"to_station_id"`
	AgentType         string  `json:"agent_type" form:"agent_type"`
	StartingPoiId     *string `json:"starting_poi_id,omitempty" form:"starting_poi_id"`
	FromName          *string `json:"fromName,omitempty" form:"fromName"`
	Area              *int32  `json:"area,omitempty" form:"area"`
	Flat              *string `json:"flat,omitempty" form:"flat"`
	Flng              *string `json:"flng,omitempty" form:"flng"`
	DestPoiId         *string `json:"dest_poi_id,omitempty" form:"dest_poi_id"`
	ToName            *string `json:"toName,omitempty" form:"toName"`
	ToArea            *int32  `json:"to_area,omitempty" form:"to_area"`
	Tlat              *string `json:"tlat,omitempty" form:"tlat"`
	Tlng              *string `json:"tlng,omitempty" form:"tlng"`
}

func (x *OrderParams) GetBusServiceShiftId() (r string) {
	if x != nil {
		return x.BusServiceShiftId
	}
	return r
}

func (x *OrderParams) GetDepartureTime() (r string) {
	if x != nil {
		return x.DepartureTime
	}
	return r
}

func (x *OrderParams) GetType() (r int32) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *OrderParams) GetComboId() (r int32) {
	if x != nil {
		return x.ComboId
	}
	return r
}

func (x *OrderParams) GetFromStationId() (r int32) {
	if x != nil {
		return x.FromStationId
	}
	return r
}

func (x *OrderParams) GetToStationId() (r int32) {
	if x != nil {
		return x.ToStationId
	}
	return r
}

func (x *OrderParams) GetAgentType() (r string) {
	if x != nil {
		return x.AgentType
	}
	return r
}

func (x *OrderParams) GetStartingPoiId() (r string) {
	if x != nil && x.StartingPoiId != nil {
		return *x.StartingPoiId
	}
	return r
}

func (x *OrderParams) GetFromName() (r string) {
	if x != nil && x.FromName != nil {
		return *x.FromName
	}
	return r
}

func (x *OrderParams) GetArea() (r int32) {
	if x != nil && x.Area != nil {
		return *x.Area
	}
	return r
}

func (x *OrderParams) GetFlat() (r string) {
	if x != nil && x.Flat != nil {
		return *x.Flat
	}
	return r
}

func (x *OrderParams) GetFlng() (r string) {
	if x != nil && x.Flng != nil {
		return *x.Flng
	}
	return r
}

func (x *OrderParams) GetDestPoiId() (r string) {
	if x != nil && x.DestPoiId != nil {
		return *x.DestPoiId
	}
	return r
}

func (x *OrderParams) GetToName() (r string) {
	if x != nil && x.ToName != nil {
		return *x.ToName
	}
	return r
}

func (x *OrderParams) GetToArea() (r int32) {
	if x != nil && x.ToArea != nil {
		return *x.ToArea
	}
	return r
}

func (x *OrderParams) GetTlat() (r string) {
	if x != nil && x.Tlat != nil {
		return *x.Tlat
	}
	return r
}

func (x *OrderParams) GetTlng() (r string) {
	if x != nil && x.Tlng != nil {
		return *x.Tlng
	}
	return r
}

func (x *OrderParams) SetBusServiceShiftId(v string) {
	if x != nil {
		x.BusServiceShiftId = v
	}
}

func (x *OrderParams) SetDepartureTime(v string) {
	if x != nil {
		x.DepartureTime = v
	}
}

func (x *OrderParams) SetType(v int32) {
	if x != nil {
		x.Type = v
	}
}

func (x *OrderParams) SetComboId(v int32) {
	if x != nil {
		x.ComboId = v
	}
}

func (x *OrderParams) SetFromStationId(v int32) {
	if x != nil {
		x.FromStationId = v
	}
}

func (x *OrderParams) SetToStationId(v int32) {
	if x != nil {
		x.ToStationId = v
	}
}

func (x *OrderParams) SetAgentType(v string) {
	if x != nil {
		x.AgentType = v
	}
}

func (x *OrderParams) SetStartingPoiId(v string) {
	if x != nil {
		x.StartingPoiId = &v
	}
}

func (x *OrderParams) SetFromName(v string) {
	if x != nil {
		x.FromName = &v
	}
}

func (x *OrderParams) SetArea(v int32) {
	if x != nil {
		x.Area = &v
	}
}

func (x *OrderParams) SetFlat(v string) {
	if x != nil {
		x.Flat = &v
	}
}

func (x *OrderParams) SetFlng(v string) {
	if x != nil {
		x.Flng = &v
	}
}

func (x *OrderParams) SetDestPoiId(v string) {
	if x != nil {
		x.DestPoiId = &v
	}
}

func (x *OrderParams) SetToName(v string) {
	if x != nil {
		x.ToName = &v
	}
}

func (x *OrderParams) SetToArea(v int32) {
	if x != nil {
		x.ToArea = &v
	}
}

func (x *OrderParams) SetTlat(v string) {
	if x != nil {
		x.Tlat = &v
	}
}

func (x *OrderParams) SetTlng(v string) {
	if x != nil {
		x.Tlng = &v
	}
}

func (p *OrderParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderParams(%+v)", *p)
}

type PriceCard struct {
	OuterPriceCard *PriceCardItem `json:"outer_price_card,omitempty" form:"outer_price_card"`
	ExtraPriceCard *PriceCardItem `json:"extra_price_card,omitempty" form:"extra_price_card"`
}

func (x *PriceCard) GetOuterPriceCard() (r *PriceCardItem) {
	if x != nil {
		return x.OuterPriceCard
	}
	return r
}

func (x *PriceCard) GetExtraPriceCard() (r *PriceCardItem) {
	if x != nil {
		return x.ExtraPriceCard
	}
	return r
}

func (x *PriceCard) SetOuterPriceCard(v *PriceCardItem) {
	if x != nil {
		x.OuterPriceCard = v
	}
}

func (x *PriceCard) SetExtraPriceCard(v *PriceCardItem) {
	if x != nil {
		x.ExtraPriceCard = v
	}
}

func (p *PriceCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PriceCard(%+v)", *p)
}

type PriceCardItem struct {
	ShowTieredPrice bool         `json:"show_tiered_price" form:"show_tiered_price"`
	IntroMsg        *string      `json:"intro_msg,omitempty" form:"intro_msg"`
	PriceItems      []*PriceItem `json:"price_items" form:"price_items"`
}

func (x *PriceCardItem) GetShowTieredPrice() (r bool) {
	if x != nil {
		return x.ShowTieredPrice
	}
	return r
}

func (x *PriceCardItem) GetIntroMsg() (r string) {
	if x != nil && x.IntroMsg != nil {
		return *x.IntroMsg
	}
	return r
}

func (x *PriceCardItem) GetPriceItems() (r []*PriceItem) {
	if x != nil {
		return x.PriceItems
	}
	return r
}

func (x *PriceCardItem) SetShowTieredPrice(v bool) {
	if x != nil {
		x.ShowTieredPrice = v
	}
}

func (x *PriceCardItem) SetIntroMsg(v string) {
	if x != nil {
		x.IntroMsg = &v
	}
}

func (x *PriceCardItem) SetPriceItems(v []*PriceItem) {
	if x != nil {
		x.PriceItems = v
	}
}

func (p *PriceCardItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PriceCardItem(%+v)", *p)
}

type PriceItem struct {
	FeeAmount float64 `json:"fee_amount" form:"fee_amount"`
	FeeMsg    string  `json:"fee_msg" form:"fee_msg"`
}

func (x *PriceItem) GetFeeAmount() (r float64) {
	if x != nil {
		return x.FeeAmount
	}
	return r
}

func (x *PriceItem) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *PriceItem) SetFeeAmount(v float64) {
	if x != nil {
		x.FeeAmount = v
	}
}

func (x *PriceItem) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (p *PriceItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PriceItem(%+v)", *p)
}

type StationPopUp struct {
	StartStations []*IntercityStationDetail `json:"start_stations" form:"start_stations"`
	EndStations   []*IntercityStationDetail `json:"end_stations" form:"end_stations"`
	ButtonText    string                    `json:"button_text" form:"button_text"`
	FeeParams     *FeeParams                `json:"fee_params,omitempty" form:"fee_params"`
	StartTitle    string                    `json:"start_title" form:"start_title"`
	EndTitle      string                    `json:"end_title" form:"end_title"`
}

func (x *StationPopUp) GetStartStations() (r []*IntercityStationDetail) {
	if x != nil {
		return x.StartStations
	}
	return r
}

func (x *StationPopUp) GetEndStations() (r []*IntercityStationDetail) {
	if x != nil {
		return x.EndStations
	}
	return r
}

func (x *StationPopUp) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *StationPopUp) GetFeeParams() (r *FeeParams) {
	if x != nil {
		return x.FeeParams
	}
	return r
}

func (x *StationPopUp) GetStartTitle() (r string) {
	if x != nil {
		return x.StartTitle
	}
	return r
}

func (x *StationPopUp) GetEndTitle() (r string) {
	if x != nil {
		return x.EndTitle
	}
	return r
}

func (x *StationPopUp) SetStartStations(v []*IntercityStationDetail) {
	if x != nil {
		x.StartStations = v
	}
}

func (x *StationPopUp) SetEndStations(v []*IntercityStationDetail) {
	if x != nil {
		x.EndStations = v
	}
}

func (x *StationPopUp) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (x *StationPopUp) SetFeeParams(v *FeeParams) {
	if x != nil {
		x.FeeParams = v
	}
}

func (x *StationPopUp) SetStartTitle(v string) {
	if x != nil {
		x.StartTitle = v
	}
}

func (x *StationPopUp) SetEndTitle(v string) {
	if x != nil {
		x.EndTitle = v
	}
}

func (p *StationPopUp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StationPopUp(%+v)", *p)
}

type NTuple struct {
	ProductId int64  `json:"product_id" form:"product_id"`
	ComboType int64  `json:"combo_type" form:"combo_type"`
	CarLevel  string `json:"car_level" form:"car_level"`
}

func (x *NTuple) GetProductId() (r int64) {
	if x != nil {
		return x.ProductId
	}
	return r
}

func (x *NTuple) GetComboType() (r int64) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *NTuple) GetCarLevel() (r string) {
	if x != nil {
		return x.CarLevel
	}
	return r
}

func (x *NTuple) SetProductId(v int64) {
	if x != nil {
		x.ProductId = v
	}
}

func (x *NTuple) SetComboType(v int64) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *NTuple) SetCarLevel(v string) {
	if x != nil {
		x.CarLevel = v
	}
}

func (p *NTuple) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NTuple(%+v)", *p)
}

type FeeParams struct {
	District          string `json:"district" form:"district"`
	NtupleInfo        string `json:"ntuple_info" form:"ntuple_info"`
	BusServiceShiftId string `json:"bus_service_shift_id" form:"bus_service_shift_id"`
	RouteId           string `json:"route_id" form:"route_id"`
	FromStationId     int32  `json:"from_station_id" form:"from_station_id"`
	ToStationId       int32  `json:"to_station_id" form:"to_station_id"`
}

func (x *FeeParams) GetDistrict() (r string) {
	if x != nil {
		return x.District
	}
	return r
}

func (x *FeeParams) GetNtupleInfo() (r string) {
	if x != nil {
		return x.NtupleInfo
	}
	return r
}

func (x *FeeParams) GetBusServiceShiftId() (r string) {
	if x != nil {
		return x.BusServiceShiftId
	}
	return r
}

func (x *FeeParams) GetRouteId() (r string) {
	if x != nil {
		return x.RouteId
	}
	return r
}

func (x *FeeParams) GetFromStationId() (r int32) {
	if x != nil {
		return x.FromStationId
	}
	return r
}

func (x *FeeParams) GetToStationId() (r int32) {
	if x != nil {
		return x.ToStationId
	}
	return r
}

func (x *FeeParams) SetDistrict(v string) {
	if x != nil {
		x.District = v
	}
}

func (x *FeeParams) SetNtupleInfo(v string) {
	if x != nil {
		x.NtupleInfo = v
	}
}

func (x *FeeParams) SetBusServiceShiftId(v string) {
	if x != nil {
		x.BusServiceShiftId = v
	}
}

func (x *FeeParams) SetRouteId(v string) {
	if x != nil {
		x.RouteId = v
	}
}

func (x *FeeParams) SetFromStationId(v int32) {
	if x != nil {
		x.FromStationId = v
	}
}

func (x *FeeParams) SetToStationId(v int32) {
	if x != nil {
		x.ToStationId = v
	}
}

func (p *FeeParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeParams(%+v)", *p)
}

type ExtraInfo struct {
	JumpUrl *string `json:"jump_url,omitempty" form:"jump_url"`
}

func (x *ExtraInfo) GetJumpUrl() (r string) {
	if x != nil && x.JumpUrl != nil {
		return *x.JumpUrl
	}
	return r
}

func (x *ExtraInfo) SetJumpUrl(v string) {
	if x != nil {
		x.JumpUrl = &v
	}
}

func (p *ExtraInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExtraInfo(%+v)", *p)
}

type TravelCardInfo struct {
	BgImg     string              `json:"bg_img" form:"bg_img"`                   //背景图片
	TitleImg  string              `json:"title_img" form:"title_img"`             //标题图片
	TitleDesc string              `json:"title_desc" form:"title_desc"`           //标题描述
	SubTitle  string              `json:"sub_title" form:"sub_title"`             //子标题
	CardList  []*BusCardItem      `json:"card_list,omitempty" form:"card_list"`   //卡列表
	CardTip   *CardTip            `json:"card_tip,omitempty" form:"card_tip"`     //出行卡提示
	OmegaInfo *BusDetailOmegaInfo `json:"omega_info,omitempty" form:"omega_info"` //埋点
}

func (x *TravelCardInfo) GetBgImg() (r string) {
	if x != nil {
		return x.BgImg
	}
	return r
}

func (x *TravelCardInfo) GetTitleImg() (r string) {
	if x != nil {
		return x.TitleImg
	}
	return r
}

func (x *TravelCardInfo) GetTitleDesc() (r string) {
	if x != nil {
		return x.TitleDesc
	}
	return r
}

func (x *TravelCardInfo) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *TravelCardInfo) GetCardList() (r []*BusCardItem) {
	if x != nil {
		return x.CardList
	}
	return r
}

func (x *TravelCardInfo) GetCardTip() (r *CardTip) {
	if x != nil {
		return x.CardTip
	}
	return r
}

func (x *TravelCardInfo) GetOmegaInfo() (r *BusDetailOmegaInfo) {
	if x != nil {
		return x.OmegaInfo
	}
	return r
}

func (x *TravelCardInfo) SetBgImg(v string) {
	if x != nil {
		x.BgImg = v
	}
}

func (x *TravelCardInfo) SetTitleImg(v string) {
	if x != nil {
		x.TitleImg = v
	}
}

func (x *TravelCardInfo) SetTitleDesc(v string) {
	if x != nil {
		x.TitleDesc = v
	}
}

func (x *TravelCardInfo) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *TravelCardInfo) SetCardList(v []*BusCardItem) {
	if x != nil {
		x.CardList = v
	}
}

func (x *TravelCardInfo) SetCardTip(v *CardTip) {
	if x != nil {
		x.CardTip = v
	}
}

func (x *TravelCardInfo) SetOmegaInfo(v *BusDetailOmegaInfo) {
	if x != nil {
		x.OmegaInfo = v
	}
}

func (p *TravelCardInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TravelCardInfo(%+v)", *p)
}

type BusCardItem struct {
	CardDesc         string            `json:"card_desc" form:"card_desc"`                               //出行卡描述
	RuleDesc         string            `json:"rule_desc" form:"rule_desc"`                               //rule描述
	PriceDesc        string            `json:"price_desc" form:"price_desc"`                             //价格描述
	IsSelect         int32             `json:"is_select" form:"is_select"`                               //是否选中
	BgImg            string            `json:"bg_img" form:"bg_img"`                                     //背景图片
	BatchId          int32             `json:"batch_id" form:"batch_id"`                                 //卡批次号
	TopLeftCornerTag *TopLeftCornerTag `json:"top_left_corner_tag,omitempty" form:"top_left_corner_tag"` //左上角tag
	ProductId        int32             `json:"product_id" form:"product_id"`                             //卡批次号
}

func (x *BusCardItem) GetCardDesc() (r string) {
	if x != nil {
		return x.CardDesc
	}
	return r
}

func (x *BusCardItem) GetRuleDesc() (r string) {
	if x != nil {
		return x.RuleDesc
	}
	return r
}

func (x *BusCardItem) GetPriceDesc() (r string) {
	if x != nil {
		return x.PriceDesc
	}
	return r
}

func (x *BusCardItem) GetIsSelect() (r int32) {
	if x != nil {
		return x.IsSelect
	}
	return r
}

func (x *BusCardItem) GetBgImg() (r string) {
	if x != nil {
		return x.BgImg
	}
	return r
}

func (x *BusCardItem) GetBatchId() (r int32) {
	if x != nil {
		return x.BatchId
	}
	return r
}

func (x *BusCardItem) GetTopLeftCornerTag() (r *TopLeftCornerTag) {
	if x != nil {
		return x.TopLeftCornerTag
	}
	return r
}

func (x *BusCardItem) GetProductId() (r int32) {
	if x != nil {
		return x.ProductId
	}
	return r
}

func (x *BusCardItem) SetCardDesc(v string) {
	if x != nil {
		x.CardDesc = v
	}
}

func (x *BusCardItem) SetRuleDesc(v string) {
	if x != nil {
		x.RuleDesc = v
	}
}

func (x *BusCardItem) SetPriceDesc(v string) {
	if x != nil {
		x.PriceDesc = v
	}
}

func (x *BusCardItem) SetIsSelect(v int32) {
	if x != nil {
		x.IsSelect = v
	}
}

func (x *BusCardItem) SetBgImg(v string) {
	if x != nil {
		x.BgImg = v
	}
}

func (x *BusCardItem) SetBatchId(v int32) {
	if x != nil {
		x.BatchId = v
	}
}

func (x *BusCardItem) SetTopLeftCornerTag(v *TopLeftCornerTag) {
	if x != nil {
		x.TopLeftCornerTag = v
	}
}

func (x *BusCardItem) SetProductId(v int32) {
	if x != nil {
		x.ProductId = v
	}
}

func (p *BusCardItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusCardItem(%+v)", *p)
}

type TopLeftCornerTag struct {
	Desc      string `json:"desc" form:"desc"`             //描述
	TextColor string `json:"text_color" form:"text_color"` //字体颜色
	BgColor   string `json:"bg_color" form:"bg_color"`     //背景颜色
}

func (x *TopLeftCornerTag) GetDesc() (r string) {
	if x != nil {
		return x.Desc
	}
	return r
}

func (x *TopLeftCornerTag) GetTextColor() (r string) {
	if x != nil {
		return x.TextColor
	}
	return r
}

func (x *TopLeftCornerTag) GetBgColor() (r string) {
	if x != nil {
		return x.BgColor
	}
	return r
}

func (x *TopLeftCornerTag) SetDesc(v string) {
	if x != nil {
		x.Desc = v
	}
}

func (x *TopLeftCornerTag) SetTextColor(v string) {
	if x != nil {
		x.TextColor = v
	}
}

func (x *TopLeftCornerTag) SetBgColor(v string) {
	if x != nil {
		x.BgColor = v
	}
}

func (p *TopLeftCornerTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TopLeftCornerTag(%+v)", *p)
}

type CardTip struct {
	Title       string   `json:"title" form:"title"`                         //标题
	SubTitle    string   `json:"sub_title" form:"sub_title"`                 //子标题
	RightImg    string   `json:"right_img" form:"right_img"`                 //右图片
	BgGradients []string `json:"bg_gradients,omitempty" form:"bg_gradients"` //背景渐变色
}

func (x *CardTip) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *CardTip) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *CardTip) GetRightImg() (r string) {
	if x != nil {
		return x.RightImg
	}
	return r
}

func (x *CardTip) GetBgGradients() (r []string) {
	if x != nil {
		return x.BgGradients
	}
	return r
}

func (x *CardTip) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *CardTip) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *CardTip) SetRightImg(v string) {
	if x != nil {
		x.RightImg = v
	}
}

func (x *CardTip) SetBgGradients(v []string) {
	if x != nil {
		x.BgGradients = v
	}
}

func (p *CardTip) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CardTip(%+v)", *p)
}

type BusDetailOmegaInfo struct {
	CommonParams   *BusDetailOmegaCommonParams `json:"common_params,omitempty" form:"common_params"`
	Show           *BusDetailOmegaShow         `json:"show,omitempty" form:"show"`
	RouteSceneType int32                       `json:"route_scene_type" form:"route_scene_type"` //路线场景类型  1：多点到门
}

func (x *BusDetailOmegaInfo) GetCommonParams() (r *BusDetailOmegaCommonParams) {
	if x != nil {
		return x.CommonParams
	}
	return r
}

func (x *BusDetailOmegaInfo) GetShow() (r *BusDetailOmegaShow) {
	if x != nil {
		return x.Show
	}
	return r
}

func (x *BusDetailOmegaInfo) GetRouteSceneType() (r int32) {
	if x != nil {
		return x.RouteSceneType
	}
	return r
}

func (x *BusDetailOmegaInfo) SetCommonParams(v *BusDetailOmegaCommonParams) {
	if x != nil {
		x.CommonParams = v
	}
}

func (x *BusDetailOmegaInfo) SetShow(v *BusDetailOmegaShow) {
	if x != nil {
		x.Show = v
	}
}

func (x *BusDetailOmegaInfo) SetRouteSceneType(v int32) {
	if x != nil {
		x.RouteSceneType = v
	}
}

func (p *BusDetailOmegaInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusDetailOmegaInfo(%+v)", *p)
}

type BusDetailOmegaShow struct {
	Key    string                `json:"key" form:"key"`
	Params *BusDetailOmegaParams `json:"params,omitempty" form:"params"`
}

func (x *BusDetailOmegaShow) GetKey() (r string) {
	if x != nil {
		return x.Key
	}
	return r
}

func (x *BusDetailOmegaShow) GetParams() (r *BusDetailOmegaParams) {
	if x != nil {
		return x.Params
	}
	return r
}

func (x *BusDetailOmegaShow) SetKey(v string) {
	if x != nil {
		x.Key = v
	}
}

func (x *BusDetailOmegaShow) SetParams(v *BusDetailOmegaParams) {
	if x != nil {
		x.Params = v
	}
}

func (p *BusDetailOmegaShow) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusDetailOmegaShow(%+v)", *p)
}

type BusDetailOmegaParams struct {
	CardList    string `json:"card_list" form:"card_list"`
	AccessKeyId int32  `json:"access_key_id" form:"access_key_id"`
}

func (x *BusDetailOmegaParams) GetCardList() (r string) {
	if x != nil {
		return x.CardList
	}
	return r
}

func (x *BusDetailOmegaParams) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *BusDetailOmegaParams) SetCardList(v string) {
	if x != nil {
		x.CardList = v
	}
}

func (x *BusDetailOmegaParams) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (p *BusDetailOmegaParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusDetailOmegaParams(%+v)", *p)
}

type BusDetailOmegaCommonParams struct {
	PageType int32 `json:"page_type" form:"page_type"`
}

func (x *BusDetailOmegaCommonParams) GetPageType() (r int32) {
	if x != nil {
		return x.PageType
	}
	return r
}

func (x *BusDetailOmegaCommonParams) SetPageType(v int32) {
	if x != nil {
		x.PageType = v
	}
}

func (p *BusDetailOmegaCommonParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusDetailOmegaCommonParams(%+v)", *p)
}

type IntercityEstimateRequest struct {
	Token             string    `json:"token" form:"token"`                 //用户认证token
	UserType          UserType  `json:"user_type" form:"user_type"`         //1普通用户；2企业用户
	AppVersion        string    `json:"app_version" form:"app_version"`     //端版本
	AccessKeyId       int32     `json:"access_key_id" form:"access_key_id"` //端来源
	Channel           string    `json:"channel" form:"channel"`             //渠道号
	ClientType        int32     `json:"client_type" form:"client_type"`     //端类型
	Lang              string    `json:"lang" form:"lang"`                   //端语种
	PlatformType      int32     `json:"platform_type" form:"platform_type"` //端(平台)类型-2
	MapType           string    `json:"map_type" form:"map_type"`           //地图类型
	Lat               float64   `json:"lat" form:"lat"`                     //定位点
	Lng               float64   `json:"lng" form:"lng"`                     //定位点
	FromLat           float64   `json:"from_lat" form:"from_lat"`           //起点
	FromLng           float64   `json:"from_lng" form:"from_lng"`           //起点
	FromPoiId         string    `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType       string    `json:"from_poi_type" form:"from_poi_type"`
	FromAddress       string    `json:"from_address" form:"from_address"`
	FromName          string    `json:"from_name" form:"from_name"`
	ChooseFSearchid   string    `json:"choose_f_searchid" form:"choose_f_searchid"` //用户选择起点请求ID
	ToLat             float64   `json:"to_lat" form:"to_lat"`                       //终点
	ToLng             float64   `json:"to_lng" form:"to_lng"`                       //终点
	ToPoiId           string    `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType         string    `json:"to_poi_type" form:"to_poi_type"`
	ToAddress         string    `json:"to_address" form:"to_address"`
	ToName            string    `json:"to_name" form:"to_name"`
	ChooseTSearchid   string    `json:"choose_t_searchid" form:"choose_t_searchid"`         //用户选择终点请求ID
	DepartureRange    string    `json:"departure_range" form:"departure_range"`             //选择的出发时间段
	CarpoolSeatNum    int32     `json:"carpool_seat_num" form:"carpool_seat_num"`           //选择的座位数
	PaymentsType      int32     `json:"payments_type" form:"payments_type"`                 //选择的支付方式
	OrderType         OrderType `json:"order_type" form:"order_type"`                       //这是一个冗余字段, 其实是不需要的, 可以用 departure_range 推断出
	DepartureTime     string    `json:"departure_time" form:"departure_time"`               //当时只有班车时间点模式二次勾选会用
	StartStationId    int64     `json:"start_station_id" form:"start_station_id"`           //上车站点id
	EndStationId      int64     `json:"end_station_id" form:"end_station_id"`               //下车站点id
	RouteId           int64     `json:"route_id" form:"route_id"`                           //路线id
	SeatDetailInfo    *string   `json:"seat_detail_info,omitempty" form:"seat_detail_info"` //座位细节信息
	A3Token           *string   `json:"a3_token,omitempty" form:"a3_token"`                 //无用
	Xpsid             *string   `json:"xpsid,omitempty" form:"xpsid"`
	XpsidRoot         *string   `json:"xpsid_root,omitempty" form:"xpsid_root"`
	Dchn              string    `json:"dchn" form:"dchn"`                                           //运营投放标识
	AgentType         *string   `json:"agent_type,omitempty" form:"agent_type"`                     //运营投放标识
	BusServiceShiftId *string   `json:"bus_service_shift_id,omitempty" form:"bus_service_shift_id"` //班次号
	Scene             *string   `json:"scene,omitempty" form:"scene"`                               //rebook:改签
	TicketDetailInfo  *string   `json:"ticket_detail_info,omitempty" form:"ticket_detail_info"`     //车票信息
	OrderId           *string   `json:"order_id,omitempty" form:"order_id"`                         //上一笔订单id
}

func (x *IntercityEstimateRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *IntercityEstimateRequest) GetUserType() (r UserType) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *IntercityEstimateRequest) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *IntercityEstimateRequest) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *IntercityEstimateRequest) GetChannel() (r string) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *IntercityEstimateRequest) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *IntercityEstimateRequest) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *IntercityEstimateRequest) GetPlatformType() (r int32) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *IntercityEstimateRequest) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *IntercityEstimateRequest) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *IntercityEstimateRequest) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *IntercityEstimateRequest) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *IntercityEstimateRequest) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *IntercityEstimateRequest) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *IntercityEstimateRequest) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *IntercityEstimateRequest) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *IntercityEstimateRequest) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *IntercityEstimateRequest) GetChooseFSearchid() (r string) {
	if x != nil {
		return x.ChooseFSearchid
	}
	return r
}

func (x *IntercityEstimateRequest) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *IntercityEstimateRequest) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *IntercityEstimateRequest) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *IntercityEstimateRequest) GetToPoiType() (r string) {
	if x != nil {
		return x.ToPoiType
	}
	return r
}

func (x *IntercityEstimateRequest) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *IntercityEstimateRequest) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *IntercityEstimateRequest) GetChooseTSearchid() (r string) {
	if x != nil {
		return x.ChooseTSearchid
	}
	return r
}

func (x *IntercityEstimateRequest) GetDepartureRange() (r string) {
	if x != nil {
		return x.DepartureRange
	}
	return r
}

func (x *IntercityEstimateRequest) GetCarpoolSeatNum() (r int32) {
	if x != nil {
		return x.CarpoolSeatNum
	}
	return r
}

func (x *IntercityEstimateRequest) GetPaymentsType() (r int32) {
	if x != nil {
		return x.PaymentsType
	}
	return r
}

func (x *IntercityEstimateRequest) GetOrderType() (r OrderType) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *IntercityEstimateRequest) GetDepartureTime() (r string) {
	if x != nil {
		return x.DepartureTime
	}
	return r
}

func (x *IntercityEstimateRequest) GetStartStationId() (r int64) {
	if x != nil {
		return x.StartStationId
	}
	return r
}

func (x *IntercityEstimateRequest) GetEndStationId() (r int64) {
	if x != nil {
		return x.EndStationId
	}
	return r
}

func (x *IntercityEstimateRequest) GetRouteId() (r int64) {
	if x != nil {
		return x.RouteId
	}
	return r
}

func (x *IntercityEstimateRequest) GetSeatDetailInfo() (r string) {
	if x != nil && x.SeatDetailInfo != nil {
		return *x.SeatDetailInfo
	}
	return r
}

func (x *IntercityEstimateRequest) GetA3Token() (r string) {
	if x != nil && x.A3Token != nil {
		return *x.A3Token
	}
	return r
}

func (x *IntercityEstimateRequest) GetXpsid() (r string) {
	if x != nil && x.Xpsid != nil {
		return *x.Xpsid
	}
	return r
}

func (x *IntercityEstimateRequest) GetXpsidRoot() (r string) {
	if x != nil && x.XpsidRoot != nil {
		return *x.XpsidRoot
	}
	return r
}

func (x *IntercityEstimateRequest) GetDchn() (r string) {
	if x != nil {
		return x.Dchn
	}
	return r
}

func (x *IntercityEstimateRequest) GetAgentType() (r string) {
	if x != nil && x.AgentType != nil {
		return *x.AgentType
	}
	return r
}

func (x *IntercityEstimateRequest) GetBusServiceShiftId() (r string) {
	if x != nil && x.BusServiceShiftId != nil {
		return *x.BusServiceShiftId
	}
	return r
}

func (x *IntercityEstimateRequest) GetScene() (r string) {
	if x != nil && x.Scene != nil {
		return *x.Scene
	}
	return r
}

func (x *IntercityEstimateRequest) GetTicketDetailInfo() (r string) {
	if x != nil && x.TicketDetailInfo != nil {
		return *x.TicketDetailInfo
	}
	return r
}

func (x *IntercityEstimateRequest) GetOrderId() (r string) {
	if x != nil && x.OrderId != nil {
		return *x.OrderId
	}
	return r
}

func (x *IntercityEstimateRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *IntercityEstimateRequest) SetUserType(v UserType) {
	if x != nil {
		x.UserType = v
	}
}

func (x *IntercityEstimateRequest) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *IntercityEstimateRequest) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *IntercityEstimateRequest) SetChannel(v string) {
	if x != nil {
		x.Channel = v
	}
}

func (x *IntercityEstimateRequest) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *IntercityEstimateRequest) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *IntercityEstimateRequest) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *IntercityEstimateRequest) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *IntercityEstimateRequest) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *IntercityEstimateRequest) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *IntercityEstimateRequest) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *IntercityEstimateRequest) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *IntercityEstimateRequest) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *IntercityEstimateRequest) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *IntercityEstimateRequest) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *IntercityEstimateRequest) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *IntercityEstimateRequest) SetChooseFSearchid(v string) {
	if x != nil {
		x.ChooseFSearchid = v
	}
}

func (x *IntercityEstimateRequest) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *IntercityEstimateRequest) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *IntercityEstimateRequest) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *IntercityEstimateRequest) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = v
	}
}

func (x *IntercityEstimateRequest) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *IntercityEstimateRequest) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *IntercityEstimateRequest) SetChooseTSearchid(v string) {
	if x != nil {
		x.ChooseTSearchid = v
	}
}

func (x *IntercityEstimateRequest) SetDepartureRange(v string) {
	if x != nil {
		x.DepartureRange = v
	}
}

func (x *IntercityEstimateRequest) SetCarpoolSeatNum(v int32) {
	if x != nil {
		x.CarpoolSeatNum = v
	}
}

func (x *IntercityEstimateRequest) SetPaymentsType(v int32) {
	if x != nil {
		x.PaymentsType = v
	}
}

func (x *IntercityEstimateRequest) SetOrderType(v OrderType) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *IntercityEstimateRequest) SetDepartureTime(v string) {
	if x != nil {
		x.DepartureTime = v
	}
}

func (x *IntercityEstimateRequest) SetStartStationId(v int64) {
	if x != nil {
		x.StartStationId = v
	}
}

func (x *IntercityEstimateRequest) SetEndStationId(v int64) {
	if x != nil {
		x.EndStationId = v
	}
}

func (x *IntercityEstimateRequest) SetRouteId(v int64) {
	if x != nil {
		x.RouteId = v
	}
}

func (x *IntercityEstimateRequest) SetSeatDetailInfo(v string) {
	if x != nil {
		x.SeatDetailInfo = &v
	}
}

func (x *IntercityEstimateRequest) SetA3Token(v string) {
	if x != nil {
		x.A3Token = &v
	}
}

func (x *IntercityEstimateRequest) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = &v
	}
}

func (x *IntercityEstimateRequest) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = &v
	}
}

func (x *IntercityEstimateRequest) SetDchn(v string) {
	if x != nil {
		x.Dchn = v
	}
}

func (x *IntercityEstimateRequest) SetAgentType(v string) {
	if x != nil {
		x.AgentType = &v
	}
}

func (x *IntercityEstimateRequest) SetBusServiceShiftId(v string) {
	if x != nil {
		x.BusServiceShiftId = &v
	}
}

func (x *IntercityEstimateRequest) SetScene(v string) {
	if x != nil {
		x.Scene = &v
	}
}

func (x *IntercityEstimateRequest) SetTicketDetailInfo(v string) {
	if x != nil {
		x.TicketDetailInfo = &v
	}
}

func (x *IntercityEstimateRequest) SetOrderId(v string) {
	if x != nil {
		x.OrderId = &v
	}
}

func (p *IntercityEstimateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityEstimateRequest(%+v)", *p)
}

type IntercityEstimateResp struct {
	Errno  int32                  `json:"errno" form:"errno"`
	Errmsg string                 `json:"errmsg" form:"errmsg"`
	Data   *IntercityEstimateData `json:"data,omitempty" form:"data"`
}

func (x *IntercityEstimateResp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *IntercityEstimateResp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *IntercityEstimateResp) GetData() (r *IntercityEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *IntercityEstimateResp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *IntercityEstimateResp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *IntercityEstimateResp) SetData(v *IntercityEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (p *IntercityEstimateResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityEstimateResp(%+v)", *p)
}

type StrategyButton struct {
	ActionType string `json:"action_type" form:"action_type"`
	BgColor    string `json:"bg_color" form:"bg_color"`
	Text       string `json:"text" form:"text"`
	TextColor  string `json:"text_color" form:"text_color"`
}

func (x *StrategyButton) GetActionType() (r string) {
	if x != nil {
		return x.ActionType
	}
	return r
}

func (x *StrategyButton) GetBgColor() (r string) {
	if x != nil {
		return x.BgColor
	}
	return r
}

func (x *StrategyButton) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *StrategyButton) GetTextColor() (r string) {
	if x != nil {
		return x.TextColor
	}
	return r
}

func (x *StrategyButton) SetActionType(v string) {
	if x != nil {
		x.ActionType = v
	}
}

func (x *StrategyButton) SetBgColor(v string) {
	if x != nil {
		x.BgColor = v
	}
}

func (x *StrategyButton) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *StrategyButton) SetTextColor(v string) {
	if x != nil {
		x.TextColor = v
	}
}

func (p *StrategyButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StrategyButton(%+v)", *p)
}

type PopUps struct {
	ImgUrl          string          `json:"img_url" form:"img_url"`
	Title           string          `json:"title" form:"title"`
	StrategyButtons *StrategyButton `json:"strategy_buttons,omitempty" form:"strategy_buttons"`
}

func (x *PopUps) GetImgUrl() (r string) {
	if x != nil {
		return x.ImgUrl
	}
	return r
}

func (x *PopUps) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *PopUps) GetStrategyButtons() (r *StrategyButton) {
	if x != nil {
		return x.StrategyButtons
	}
	return r
}

func (x *PopUps) SetImgUrl(v string) {
	if x != nil {
		x.ImgUrl = v
	}
}

func (x *PopUps) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *PopUps) SetStrategyButtons(v *StrategyButton) {
	if x != nil {
		x.StrategyButtons = v
	}
}

func (p *PopUps) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PopUps(%+v)", *p)
}

type IntercityEstimateData struct {
	EstimateCardData  []*IntercitySkuStationData  `json:"estimate_card_data" form:"estimate_card_data"`
	HeadImgUrl        string                      `json:"head_img_url" form:"head_img_url"`
	HeadDetailUrl     string                      `json:"head_detail_url" form:"head_detail_url"`
	BarrageText       string                      `json:"barrage_text" form:"barrage_text"`
	EstimateTraceId   string                      `json:"estimate_trace_id" form:"estimate_trace_id"`
	ErrorUrl          *string                     `json:"error_url,omitempty" form:"error_url"`                     //错误页链接？
	CarpoolSeatModule *InterCityCarpoolSeatModule `json:"carpool_seat_module,omitempty" form:"carpool_seat_module"` //座位数
	UserPayInfo       *PaymentOptionModule        `json:"user_pay_info,omitempty" form:"user_pay_info"`             //座位数
	FeeDetailUrl      string                      `json:"fee_detail_url" form:"fee_detail_url"`                     //费用详情页链接
	ForceNoticeToast  *string                     `json:"force_notice_toast,omitempty" form:"force_notice_toast"`   //费用详情页链接
	PluginPageInfo    *PluginPageInfo             `json:"plugin_page_info,omitempty" form:"plugin_page_info"`       //动调、春节服务费等拦截页
	PopUps            *PopUps                     `json:"pop_ups,omitempty" form:"pop_ups"`                         //弹窗
}

func (x *IntercityEstimateData) GetEstimateCardData() (r []*IntercitySkuStationData) {
	if x != nil {
		return x.EstimateCardData
	}
	return r
}

func (x *IntercityEstimateData) GetHeadImgUrl() (r string) {
	if x != nil {
		return x.HeadImgUrl
	}
	return r
}

func (x *IntercityEstimateData) GetHeadDetailUrl() (r string) {
	if x != nil {
		return x.HeadDetailUrl
	}
	return r
}

func (x *IntercityEstimateData) GetBarrageText() (r string) {
	if x != nil {
		return x.BarrageText
	}
	return r
}

func (x *IntercityEstimateData) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *IntercityEstimateData) GetErrorUrl() (r string) {
	if x != nil && x.ErrorUrl != nil {
		return *x.ErrorUrl
	}
	return r
}

func (x *IntercityEstimateData) GetCarpoolSeatModule() (r *InterCityCarpoolSeatModule) {
	if x != nil {
		return x.CarpoolSeatModule
	}
	return r
}

func (x *IntercityEstimateData) GetUserPayInfo() (r *PaymentOptionModule) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *IntercityEstimateData) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *IntercityEstimateData) GetForceNoticeToast() (r string) {
	if x != nil && x.ForceNoticeToast != nil {
		return *x.ForceNoticeToast
	}
	return r
}

func (x *IntercityEstimateData) GetPluginPageInfo() (r *PluginPageInfo) {
	if x != nil {
		return x.PluginPageInfo
	}
	return r
}

func (x *IntercityEstimateData) GetPopUps() (r *PopUps) {
	if x != nil {
		return x.PopUps
	}
	return r
}

func (x *IntercityEstimateData) SetEstimateCardData(v []*IntercitySkuStationData) {
	if x != nil {
		x.EstimateCardData = v
	}
}

func (x *IntercityEstimateData) SetHeadImgUrl(v string) {
	if x != nil {
		x.HeadImgUrl = v
	}
}

func (x *IntercityEstimateData) SetHeadDetailUrl(v string) {
	if x != nil {
		x.HeadDetailUrl = v
	}
}

func (x *IntercityEstimateData) SetBarrageText(v string) {
	if x != nil {
		x.BarrageText = v
	}
}

func (x *IntercityEstimateData) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (x *IntercityEstimateData) SetErrorUrl(v string) {
	if x != nil {
		x.ErrorUrl = &v
	}
}

func (x *IntercityEstimateData) SetCarpoolSeatModule(v *InterCityCarpoolSeatModule) {
	if x != nil {
		x.CarpoolSeatModule = v
	}
}

func (x *IntercityEstimateData) SetUserPayInfo(v *PaymentOptionModule) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *IntercityEstimateData) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *IntercityEstimateData) SetForceNoticeToast(v string) {
	if x != nil {
		x.ForceNoticeToast = &v
	}
}

func (x *IntercityEstimateData) SetPluginPageInfo(v *PluginPageInfo) {
	if x != nil {
		x.PluginPageInfo = v
	}
}

func (x *IntercityEstimateData) SetPopUps(v *PopUps) {
	if x != nil {
		x.PopUps = v
	}
}

func (p *IntercityEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityEstimateData(%+v)", *p)
}

type EstimateCardData struct {
	EstimateId          string                         `json:"estimate_id" form:"estimate_id"`                               //预估id
	CarIcon             string                         `json:"car_icon" form:"car_icon"`                                     //左边那个车图链接
	SubIcon             string                         `json:"sub_icon" form:"sub_icon"`                                     //副标题左边的图片链接
	SubTitle            string                         `json:"sub_title" form:"sub_title"`                                   //副标题：粤运集团承运
	IntroImage          string                         `json:"intro_image" form:"intro_image"`                               //这是站点巴士的那张图的链接
	FeeMsg              string                         `json:"fee_msg" form:"fee_msg"`                                       //XX元
	RightIcon           string                         `json:"right_icon" form:"right_icon"`                                 //右侧图片链接
	RightTag            string                         `json:"right_tag" form:"right_tag"`                                   //右侧图片下方文案
	FeeDescList         []*TagWithIconAndBorder        `json:"fee_desc_list" form:"fee_desc_list"`                           //券-xx元
	ExtraMap            *IntercityNewOrderParam        `json:"extra_map,omitempty" form:"extra_map"`                         //发单信息集合
	StationInfo         *StationInfo                   `json:"station_info,omitempty" form:"station_info"`                   //站点信息
	ConfirmButton       string                         `json:"confirm_button" form:"confirm_button"`                         //预约座位
	ConfirmSubText      string                         `json:"confirm_sub_text" form:"confirm_sub_text"`                     //按钮二行小字
	CarpoolBooking      *InterCityCarpoolBookingModule `json:"carpool_booking,omitempty" form:"carpool_booking"`             //弹起时间片相关
	CarpoolBookingOuter *InterCityCarpoolBookingSku    `json:"carpool_booking_outer,omitempty" form:"carpool_booking_outer"` //外露时间片相关
	IsSelected          bool                           `json:"is_selected" form:"is_selected"`                               //卡片是否被勾选
	StyleType           StyleType                      `json:"style_type" form:"style_type"`                                 //渲染样式
	BorderColor         string                         `json:"border_color" form:"border_color"`                             //选中态包框颜色
	CornerImage         string                         `json:"corner_image" form:"corner_image"`                             //选中态角标样式
	SupportSelectSeat   int32                          `json:"support_select_seat" form:"support_select_seat"`               //是否支持选座弹层
	UserPayInfo         *UserPayInfo                   `json:"user_pay_info,omitempty" form:"user_pay_info"`                 //支付信息
}

func (x *EstimateCardData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *EstimateCardData) GetCarIcon() (r string) {
	if x != nil {
		return x.CarIcon
	}
	return r
}

func (x *EstimateCardData) GetSubIcon() (r string) {
	if x != nil {
		return x.SubIcon
	}
	return r
}

func (x *EstimateCardData) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *EstimateCardData) GetIntroImage() (r string) {
	if x != nil {
		return x.IntroImage
	}
	return r
}

func (x *EstimateCardData) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *EstimateCardData) GetRightIcon() (r string) {
	if x != nil {
		return x.RightIcon
	}
	return r
}

func (x *EstimateCardData) GetRightTag() (r string) {
	if x != nil {
		return x.RightTag
	}
	return r
}

func (x *EstimateCardData) GetFeeDescList() (r []*TagWithIconAndBorder) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *EstimateCardData) GetExtraMap() (r *IntercityNewOrderParam) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *EstimateCardData) GetStationInfo() (r *StationInfo) {
	if x != nil {
		return x.StationInfo
	}
	return r
}

func (x *EstimateCardData) GetConfirmButton() (r string) {
	if x != nil {
		return x.ConfirmButton
	}
	return r
}

func (x *EstimateCardData) GetConfirmSubText() (r string) {
	if x != nil {
		return x.ConfirmSubText
	}
	return r
}

func (x *EstimateCardData) GetCarpoolBooking() (r *InterCityCarpoolBookingModule) {
	if x != nil {
		return x.CarpoolBooking
	}
	return r
}

func (x *EstimateCardData) GetCarpoolBookingOuter() (r *InterCityCarpoolBookingSku) {
	if x != nil {
		return x.CarpoolBookingOuter
	}
	return r
}

func (x *EstimateCardData) GetIsSelected() (r bool) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *EstimateCardData) GetStyleType() (r StyleType) {
	if x != nil {
		return x.StyleType
	}
	return r
}

func (x *EstimateCardData) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *EstimateCardData) GetCornerImage() (r string) {
	if x != nil {
		return x.CornerImage
	}
	return r
}

func (x *EstimateCardData) GetSupportSelectSeat() (r int32) {
	if x != nil {
		return x.SupportSelectSeat
	}
	return r
}

func (x *EstimateCardData) GetUserPayInfo() (r *UserPayInfo) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *EstimateCardData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *EstimateCardData) SetCarIcon(v string) {
	if x != nil {
		x.CarIcon = v
	}
}

func (x *EstimateCardData) SetSubIcon(v string) {
	if x != nil {
		x.SubIcon = v
	}
}

func (x *EstimateCardData) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *EstimateCardData) SetIntroImage(v string) {
	if x != nil {
		x.IntroImage = v
	}
}

func (x *EstimateCardData) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *EstimateCardData) SetRightIcon(v string) {
	if x != nil {
		x.RightIcon = v
	}
}

func (x *EstimateCardData) SetRightTag(v string) {
	if x != nil {
		x.RightTag = v
	}
}

func (x *EstimateCardData) SetFeeDescList(v []*TagWithIconAndBorder) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *EstimateCardData) SetExtraMap(v *IntercityNewOrderParam) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *EstimateCardData) SetStationInfo(v *StationInfo) {
	if x != nil {
		x.StationInfo = v
	}
}

func (x *EstimateCardData) SetConfirmButton(v string) {
	if x != nil {
		x.ConfirmButton = v
	}
}

func (x *EstimateCardData) SetConfirmSubText(v string) {
	if x != nil {
		x.ConfirmSubText = v
	}
}

func (x *EstimateCardData) SetCarpoolBooking(v *InterCityCarpoolBookingModule) {
	if x != nil {
		x.CarpoolBooking = v
	}
}

func (x *EstimateCardData) SetCarpoolBookingOuter(v *InterCityCarpoolBookingSku) {
	if x != nil {
		x.CarpoolBookingOuter = v
	}
}

func (x *EstimateCardData) SetIsSelected(v bool) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *EstimateCardData) SetStyleType(v StyleType) {
	if x != nil {
		x.StyleType = v
	}
}

func (x *EstimateCardData) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *EstimateCardData) SetCornerImage(v string) {
	if x != nil {
		x.CornerImage = v
	}
}

func (x *EstimateCardData) SetSupportSelectSeat(v int32) {
	if x != nil {
		x.SupportSelectSeat = v
	}
}

func (x *EstimateCardData) SetUserPayInfo(v *UserPayInfo) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (p *EstimateCardData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EstimateCardData(%+v)", *p)
}

type IntercityMultiEstimateRequest struct {
	Token          string   `json:"token" form:"token"`
	Lang           string   `json:"lang" form:"lang"`
	AccessKeyId    int32    `json:"access_key_id" form:"access_key_id"`
	Xpsid          string   `json:"xpsid" form:"xpsid"`
	XpsidRoot      string   `json:"xpsid_root" form:"xpsid_root"`
	AppVersion     string   `json:"app_version" form:"app_version"`
	ClientType     int32    `json:"client_type" form:"client_type"`
	Dchn           string   `json:"dchn" form:"dchn"`
	RouteId        int32    `json:"route_id" form:"route_id"`
	StartStationId int64    `json:"start_station_id" form:"start_station_id"`
	EndStationId   int64    `json:"end_station_id" form:"end_station_id"`
	StartCity      int32    `json:"start_city" form:"start_city"`
	EndCity        int32    `json:"end_city" form:"end_city"`
	StartTime      int32    `json:"start_time" form:"start_time"`
	EndTime        int32    `json:"end_time" form:"end_time"`
	DayTime        int32    `json:"day_time" form:"day_time"`
	LastShiftId    string   `json:"last_shift_id" form:"last_shift_id"`
	StartCountyId  int32    `json:"start_county_id" form:"start_county_id"` //起点区县ID
	EndCountyId    int32    `json:"end_county_id" form:"end_county_id"`     //终点区县ID
	SupportCounty  int32    `json:"support_county" form:"support_county"`   //是否支持区县能力，传1标识下发区县相关数据，其他场景均下发旧结构数据
	Refresh        int32    `json:"refresh" form:"refresh"`                 //是否支持区县能力，传1标识下发区县相关数据，其他场景均下发旧结构数据
	StartTimeV2    int64    `json:"start_time_v2" form:"start_time_v2"`
	EndTimeV2      int64    `json:"end_time_v2" form:"end_time_v2"`
	TransData      *string  `json:"trans_data,omitempty" form:"trans_data"`   //透传数据
	FromLng        *float64 `json:"from_lng,omitempty" form:"from_lng"`       //起点经度
	FromLat        *float64 `json:"from_lat,omitempty" form:"from_lat"`       //起点纬度
	ToLng          *float64 `json:"to_lng,omitempty" form:"to_lng"`           //终点经度
	ToLat          *float64 `json:"to_lat,omitempty" form:"to_lat"`           //终点纬度
	CurrentLat     *string  `json:"current_lat,omitempty" form:"current_lat"` //定位纬度
	CurrentLng     *string  `json:"current_lng,omitempty" form:"current_lng"` //定位经度
	Channel        int64    `json:"channel" form:"channel"`                   //定位经度
	StartPoiName   string   `json:"start_poi_name" form:"start_poi_name"`     //起点名
	EndPoiName     string   `json:"end_poi_name" form:"end_poi_name"`         //终点名
	IsDrn          int32    `json:"is_drn" form:"is_drn"`                     //前端是否走drn
	ProductId      *int32   `json:"product_id,omitempty" form:"product_id"`   //前端是否走drn
	OpenSource     *string  `json:"open_source,omitempty" form:"open_source"`
	Sort           *int32   `json:"sort,omitempty" form:"sort"`                     //班次列表页排序类型 1：综合排序，2：按距离排序
	IsEstimateV2   *bool    `json:"is_estimate_v2,omitempty" form:"is_estimate_v2"` //h5切量，版本标识入参，切量成功后下掉
}

func (x *IntercityMultiEstimateRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetXpsid() (r string) {
	if x != nil {
		return x.Xpsid
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetXpsidRoot() (r string) {
	if x != nil {
		return x.XpsidRoot
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetDchn() (r string) {
	if x != nil {
		return x.Dchn
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetRouteId() (r int32) {
	if x != nil {
		return x.RouteId
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetStartStationId() (r int64) {
	if x != nil {
		return x.StartStationId
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetEndStationId() (r int64) {
	if x != nil {
		return x.EndStationId
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetStartCity() (r int32) {
	if x != nil {
		return x.StartCity
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetEndCity() (r int32) {
	if x != nil {
		return x.EndCity
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetStartTime() (r int32) {
	if x != nil {
		return x.StartTime
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetEndTime() (r int32) {
	if x != nil {
		return x.EndTime
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetDayTime() (r int32) {
	if x != nil {
		return x.DayTime
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetLastShiftId() (r string) {
	if x != nil {
		return x.LastShiftId
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetStartCountyId() (r int32) {
	if x != nil {
		return x.StartCountyId
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetEndCountyId() (r int32) {
	if x != nil {
		return x.EndCountyId
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetSupportCounty() (r int32) {
	if x != nil {
		return x.SupportCounty
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetRefresh() (r int32) {
	if x != nil {
		return x.Refresh
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetStartTimeV2() (r int64) {
	if x != nil {
		return x.StartTimeV2
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetEndTimeV2() (r int64) {
	if x != nil {
		return x.EndTimeV2
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetTransData() (r string) {
	if x != nil && x.TransData != nil {
		return *x.TransData
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetFromLng() (r float64) {
	if x != nil && x.FromLng != nil {
		return *x.FromLng
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetFromLat() (r float64) {
	if x != nil && x.FromLat != nil {
		return *x.FromLat
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetToLng() (r float64) {
	if x != nil && x.ToLng != nil {
		return *x.ToLng
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetToLat() (r float64) {
	if x != nil && x.ToLat != nil {
		return *x.ToLat
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetCurrentLat() (r string) {
	if x != nil && x.CurrentLat != nil {
		return *x.CurrentLat
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetCurrentLng() (r string) {
	if x != nil && x.CurrentLng != nil {
		return *x.CurrentLng
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetChannel() (r int64) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetStartPoiName() (r string) {
	if x != nil {
		return x.StartPoiName
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetEndPoiName() (r string) {
	if x != nil {
		return x.EndPoiName
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetIsDrn() (r int32) {
	if x != nil {
		return x.IsDrn
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetProductId() (r int32) {
	if x != nil && x.ProductId != nil {
		return *x.ProductId
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetOpenSource() (r string) {
	if x != nil && x.OpenSource != nil {
		return *x.OpenSource
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetSort() (r int32) {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return r
}

func (x *IntercityMultiEstimateRequest) GetIsEstimateV2() (r bool) {
	if x != nil && x.IsEstimateV2 != nil {
		return *x.IsEstimateV2
	}
	return r
}

func (x *IntercityMultiEstimateRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *IntercityMultiEstimateRequest) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *IntercityMultiEstimateRequest) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *IntercityMultiEstimateRequest) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = v
	}
}

func (x *IntercityMultiEstimateRequest) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = v
	}
}

func (x *IntercityMultiEstimateRequest) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *IntercityMultiEstimateRequest) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *IntercityMultiEstimateRequest) SetDchn(v string) {
	if x != nil {
		x.Dchn = v
	}
}

func (x *IntercityMultiEstimateRequest) SetRouteId(v int32) {
	if x != nil {
		x.RouteId = v
	}
}

func (x *IntercityMultiEstimateRequest) SetStartStationId(v int64) {
	if x != nil {
		x.StartStationId = v
	}
}

func (x *IntercityMultiEstimateRequest) SetEndStationId(v int64) {
	if x != nil {
		x.EndStationId = v
	}
}

func (x *IntercityMultiEstimateRequest) SetStartCity(v int32) {
	if x != nil {
		x.StartCity = v
	}
}

func (x *IntercityMultiEstimateRequest) SetEndCity(v int32) {
	if x != nil {
		x.EndCity = v
	}
}

func (x *IntercityMultiEstimateRequest) SetStartTime(v int32) {
	if x != nil {
		x.StartTime = v
	}
}

func (x *IntercityMultiEstimateRequest) SetEndTime(v int32) {
	if x != nil {
		x.EndTime = v
	}
}

func (x *IntercityMultiEstimateRequest) SetDayTime(v int32) {
	if x != nil {
		x.DayTime = v
	}
}

func (x *IntercityMultiEstimateRequest) SetLastShiftId(v string) {
	if x != nil {
		x.LastShiftId = v
	}
}

func (x *IntercityMultiEstimateRequest) SetStartCountyId(v int32) {
	if x != nil {
		x.StartCountyId = v
	}
}

func (x *IntercityMultiEstimateRequest) SetEndCountyId(v int32) {
	if x != nil {
		x.EndCountyId = v
	}
}

func (x *IntercityMultiEstimateRequest) SetSupportCounty(v int32) {
	if x != nil {
		x.SupportCounty = v
	}
}

func (x *IntercityMultiEstimateRequest) SetRefresh(v int32) {
	if x != nil {
		x.Refresh = v
	}
}

func (x *IntercityMultiEstimateRequest) SetStartTimeV2(v int64) {
	if x != nil {
		x.StartTimeV2 = v
	}
}

func (x *IntercityMultiEstimateRequest) SetEndTimeV2(v int64) {
	if x != nil {
		x.EndTimeV2 = v
	}
}

func (x *IntercityMultiEstimateRequest) SetTransData(v string) {
	if x != nil {
		x.TransData = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetToLng(v float64) {
	if x != nil {
		x.ToLng = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetToLat(v float64) {
	if x != nil {
		x.ToLat = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetCurrentLat(v string) {
	if x != nil {
		x.CurrentLat = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetCurrentLng(v string) {
	if x != nil {
		x.CurrentLng = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetChannel(v int64) {
	if x != nil {
		x.Channel = v
	}
}

func (x *IntercityMultiEstimateRequest) SetStartPoiName(v string) {
	if x != nil {
		x.StartPoiName = v
	}
}

func (x *IntercityMultiEstimateRequest) SetEndPoiName(v string) {
	if x != nil {
		x.EndPoiName = v
	}
}

func (x *IntercityMultiEstimateRequest) SetIsDrn(v int32) {
	if x != nil {
		x.IsDrn = v
	}
}

func (x *IntercityMultiEstimateRequest) SetProductId(v int32) {
	if x != nil {
		x.ProductId = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetOpenSource(v string) {
	if x != nil {
		x.OpenSource = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetSort(v int32) {
	if x != nil {
		x.Sort = &v
	}
}

func (x *IntercityMultiEstimateRequest) SetIsEstimateV2(v bool) {
	if x != nil {
		x.IsEstimateV2 = &v
	}
}

func (p *IntercityMultiEstimateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityMultiEstimateRequest(%+v)", *p)
}

type IntercityMultiEstimateResponse struct {
	Errno  int32                       `json:"errno" form:"errno"`
	Errmsg string                      `json:"errmsg" form:"errmsg"`
	Data   *IntercityMultiEstimateData `json:"data,omitempty" form:"data"`
}

func (x *IntercityMultiEstimateResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *IntercityMultiEstimateResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *IntercityMultiEstimateResponse) GetData() (r *IntercityMultiEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *IntercityMultiEstimateResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *IntercityMultiEstimateResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *IntercityMultiEstimateResponse) SetData(v *IntercityMultiEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (p *IntercityMultiEstimateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityMultiEstimateResponse(%+v)", *p)
}

type IntercityMultiEstimateData struct {
	Title                 string          `json:"title" form:"title"`
	Refresh               int32           `json:"refresh" form:"refresh"`
	ScreenData            *ScreenData     `json:"screen_data,omitempty" form:"screen_data"`
	EstimateCard          []*EstimateCard `json:"estimate_card" form:"estimate_card"`
	EstimateCardRecommend []*EstimateCard `json:"estimate_card_recommend" form:"estimate_card_recommend"`
	DisabledInfo          *DisabledInfo   `json:"disabled_info,omitempty" form:"disabled_info"`
	EstimateTraceId       string          `json:"estimate_trace_id" form:"estimate_trace_id"`
	LastItem              bool            `json:"last_item" form:"last_item"`
	LastShiftId           string          `json:"last_shift_id" form:"last_shift_id"`
	Guide                 *GuideData      `json:"guide,omitempty" form:"guide"`
	OptimizedData         *OptimizedData  `json:"optimized_data,omitempty" form:"optimized_data"`
	PageTitle             string          `json:"page_title" form:"page_title"`
	Calendar              []*CalendarItem `json:"calendar,omitempty" form:"calendar"`                 //日历弹层
	StartSearchBox        *SearchBox      `json:"start_search_box,omitempty" form:"start_search_box"` //搜索框兜底
	EndSearchBox          *SearchBox      `json:"end_search_box,omitempty" form:"end_search_box"`
	OmegaParams           *OmegaParams    `json:"omega_params,omitempty" form:"omega_params"` //埋点信息
	SortList              []*SortList     `json:"sort_list,omitempty" form:"sort_list"`       //班次列表页面返回给h5的排序信息
}

func (x *IntercityMultiEstimateData) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *IntercityMultiEstimateData) GetRefresh() (r int32) {
	if x != nil {
		return x.Refresh
	}
	return r
}

func (x *IntercityMultiEstimateData) GetScreenData() (r *ScreenData) {
	if x != nil {
		return x.ScreenData
	}
	return r
}

func (x *IntercityMultiEstimateData) GetEstimateCard() (r []*EstimateCard) {
	if x != nil {
		return x.EstimateCard
	}
	return r
}

func (x *IntercityMultiEstimateData) GetEstimateCardRecommend() (r []*EstimateCard) {
	if x != nil {
		return x.EstimateCardRecommend
	}
	return r
}

func (x *IntercityMultiEstimateData) GetDisabledInfo() (r *DisabledInfo) {
	if x != nil {
		return x.DisabledInfo
	}
	return r
}

func (x *IntercityMultiEstimateData) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *IntercityMultiEstimateData) GetLastItem() (r bool) {
	if x != nil {
		return x.LastItem
	}
	return r
}

func (x *IntercityMultiEstimateData) GetLastShiftId() (r string) {
	if x != nil {
		return x.LastShiftId
	}
	return r
}

func (x *IntercityMultiEstimateData) GetGuide() (r *GuideData) {
	if x != nil {
		return x.Guide
	}
	return r
}

func (x *IntercityMultiEstimateData) GetOptimizedData() (r *OptimizedData) {
	if x != nil {
		return x.OptimizedData
	}
	return r
}

func (x *IntercityMultiEstimateData) GetPageTitle() (r string) {
	if x != nil {
		return x.PageTitle
	}
	return r
}

func (x *IntercityMultiEstimateData) GetCalendar() (r []*CalendarItem) {
	if x != nil {
		return x.Calendar
	}
	return r
}

func (x *IntercityMultiEstimateData) GetStartSearchBox() (r *SearchBox) {
	if x != nil {
		return x.StartSearchBox
	}
	return r
}

func (x *IntercityMultiEstimateData) GetEndSearchBox() (r *SearchBox) {
	if x != nil {
		return x.EndSearchBox
	}
	return r
}

func (x *IntercityMultiEstimateData) GetOmegaParams() (r *OmegaParams) {
	if x != nil {
		return x.OmegaParams
	}
	return r
}

func (x *IntercityMultiEstimateData) GetSortList() (r []*SortList) {
	if x != nil {
		return x.SortList
	}
	return r
}

func (x *IntercityMultiEstimateData) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *IntercityMultiEstimateData) SetRefresh(v int32) {
	if x != nil {
		x.Refresh = v
	}
}

func (x *IntercityMultiEstimateData) SetScreenData(v *ScreenData) {
	if x != nil {
		x.ScreenData = v
	}
}

func (x *IntercityMultiEstimateData) SetEstimateCard(v []*EstimateCard) {
	if x != nil {
		x.EstimateCard = v
	}
}

func (x *IntercityMultiEstimateData) SetEstimateCardRecommend(v []*EstimateCard) {
	if x != nil {
		x.EstimateCardRecommend = v
	}
}

func (x *IntercityMultiEstimateData) SetDisabledInfo(v *DisabledInfo) {
	if x != nil {
		x.DisabledInfo = v
	}
}

func (x *IntercityMultiEstimateData) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (x *IntercityMultiEstimateData) SetLastItem(v bool) {
	if x != nil {
		x.LastItem = v
	}
}

func (x *IntercityMultiEstimateData) SetLastShiftId(v string) {
	if x != nil {
		x.LastShiftId = v
	}
}

func (x *IntercityMultiEstimateData) SetGuide(v *GuideData) {
	if x != nil {
		x.Guide = v
	}
}

func (x *IntercityMultiEstimateData) SetOptimizedData(v *OptimizedData) {
	if x != nil {
		x.OptimizedData = v
	}
}

func (x *IntercityMultiEstimateData) SetPageTitle(v string) {
	if x != nil {
		x.PageTitle = v
	}
}

func (x *IntercityMultiEstimateData) SetCalendar(v []*CalendarItem) {
	if x != nil {
		x.Calendar = v
	}
}

func (x *IntercityMultiEstimateData) SetStartSearchBox(v *SearchBox) {
	if x != nil {
		x.StartSearchBox = v
	}
}

func (x *IntercityMultiEstimateData) SetEndSearchBox(v *SearchBox) {
	if x != nil {
		x.EndSearchBox = v
	}
}

func (x *IntercityMultiEstimateData) SetOmegaParams(v *OmegaParams) {
	if x != nil {
		x.OmegaParams = v
	}
}

func (x *IntercityMultiEstimateData) SetSortList(v []*SortList) {
	if x != nil {
		x.SortList = v
	}
}

func (p *IntercityMultiEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityMultiEstimateData(%+v)", *p)
}

type OmegaParams struct {
	PageStatus          string `json:"page_status" form:"page_status"`                   //应页面状态
	RecommendedStrategy string `json:"recommended_strategy" form:"recommended_strategy"` //推荐召回策略，1：召回Xkm距离，2：召回城与城
}

func (x *OmegaParams) GetPageStatus() (r string) {
	if x != nil {
		return x.PageStatus
	}
	return r
}

func (x *OmegaParams) GetRecommendedStrategy() (r string) {
	if x != nil {
		return x.RecommendedStrategy
	}
	return r
}

func (x *OmegaParams) SetPageStatus(v string) {
	if x != nil {
		x.PageStatus = v
	}
}

func (x *OmegaParams) SetRecommendedStrategy(v string) {
	if x != nil {
		x.RecommendedStrategy = v
	}
}

func (p *OmegaParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OmegaParams(%+v)", *p)
}

type SortList struct {
	Text     string `json:"text" form:"text"`           //文案控制：【综合排序】、【距离最近】
	SortType string `json:"sort_type" form:"sort_type"` //排序类型，1：综合排序，2：距离最近
}

func (x *SortList) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *SortList) GetSortType() (r string) {
	if x != nil {
		return x.SortType
	}
	return r
}

func (x *SortList) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *SortList) SetSortType(v string) {
	if x != nil {
		x.SortType = v
	}
}

func (p *SortList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SortList(%+v)", *p)
}

type SearchBox struct {
	CityName    string  `json:"city_name" form:"city_name"`
	CountyName  string  `json:"county_name" form:"county_name"`
	StationName string  `json:"station_name" form:"station_name"`
	CityId      int32   `json:"city_id" form:"city_id"`
	CountyId    int32   `json:"county_id" form:"county_id"`
	StationId   int32   `json:"station_id" form:"station_id"`
	Lat         float64 `json:"lat" form:"lat"`
	Lng         float64 `json:"lng" form:"lng"`
}

func (x *SearchBox) GetCityName() (r string) {
	if x != nil {
		return x.CityName
	}
	return r
}

func (x *SearchBox) GetCountyName() (r string) {
	if x != nil {
		return x.CountyName
	}
	return r
}

func (x *SearchBox) GetStationName() (r string) {
	if x != nil {
		return x.StationName
	}
	return r
}

func (x *SearchBox) GetCityId() (r int32) {
	if x != nil {
		return x.CityId
	}
	return r
}

func (x *SearchBox) GetCountyId() (r int32) {
	if x != nil {
		return x.CountyId
	}
	return r
}

func (x *SearchBox) GetStationId() (r int32) {
	if x != nil {
		return x.StationId
	}
	return r
}

func (x *SearchBox) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *SearchBox) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *SearchBox) SetCityName(v string) {
	if x != nil {
		x.CityName = v
	}
}

func (x *SearchBox) SetCountyName(v string) {
	if x != nil {
		x.CountyName = v
	}
}

func (x *SearchBox) SetStationName(v string) {
	if x != nil {
		x.StationName = v
	}
}

func (x *SearchBox) SetCityId(v int32) {
	if x != nil {
		x.CityId = v
	}
}

func (x *SearchBox) SetCountyId(v int32) {
	if x != nil {
		x.CountyId = v
	}
}

func (x *SearchBox) SetStationId(v int32) {
	if x != nil {
		x.StationId = v
	}
}

func (x *SearchBox) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *SearchBox) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (p *SearchBox) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchBox(%+v)", *p)
}

type SimpleButton struct {
	Text    string  `json:"text" form:"text"`
	Color   string  `json:"color" form:"color"`
	JumpUrl *string `json:"jump_url,omitempty" form:"jump_url"`
}

func (x *SimpleButton) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *SimpleButton) GetColor() (r string) {
	if x != nil {
		return x.Color
	}
	return r
}

func (x *SimpleButton) GetJumpUrl() (r string) {
	if x != nil && x.JumpUrl != nil {
		return *x.JumpUrl
	}
	return r
}

func (x *SimpleButton) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *SimpleButton) SetColor(v string) {
	if x != nil {
		x.Color = v
	}
}

func (x *SimpleButton) SetJumpUrl(v string) {
	if x != nil {
		x.JumpUrl = &v
	}
}

func (p *SimpleButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SimpleButton(%+v)", *p)
}

type OptimizedData struct {
	LineText   string        `json:"line_text" form:"line_text"`
	IconCard   *IconCard     `json:"icon_card,omitempty" form:"icon_card"`
	Button     *SimpleButton `json:"button,omitempty" form:"button"`
	JumpButton *SimpleButton `json:"jump_button,omitempty" form:"jump_button"`
}

func (x *OptimizedData) GetLineText() (r string) {
	if x != nil {
		return x.LineText
	}
	return r
}

func (x *OptimizedData) GetIconCard() (r *IconCard) {
	if x != nil {
		return x.IconCard
	}
	return r
}

func (x *OptimizedData) GetButton() (r *SimpleButton) {
	if x != nil {
		return x.Button
	}
	return r
}

func (x *OptimizedData) GetJumpButton() (r *SimpleButton) {
	if x != nil {
		return x.JumpButton
	}
	return r
}

func (x *OptimizedData) SetLineText(v string) {
	if x != nil {
		x.LineText = v
	}
}

func (x *OptimizedData) SetIconCard(v *IconCard) {
	if x != nil {
		x.IconCard = v
	}
}

func (x *OptimizedData) SetButton(v *SimpleButton) {
	if x != nil {
		x.Button = v
	}
}

func (x *OptimizedData) SetJumpButton(v *SimpleButton) {
	if x != nil {
		x.JumpButton = v
	}
}

func (p *OptimizedData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OptimizedData(%+v)", *p)
}

type IconCard struct {
	ImgLink string   `json:"img_link" form:"img_link"`
	Text    []string `json:"text" form:"text"`
}

func (x *IconCard) GetImgLink() (r string) {
	if x != nil {
		return x.ImgLink
	}
	return r
}

func (x *IconCard) GetText() (r []string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *IconCard) SetImgLink(v string) {
	if x != nil {
		x.ImgLink = v
	}
}

func (x *IconCard) SetText(v []string) {
	if x != nil {
		x.Text = v
	}
}

func (p *IconCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IconCard(%+v)", *p)
}

type GuideData struct {
	Image       string `json:"image" form:"image"`
	Title       string `json:"title" form:"title"`
	SubTitle    string `json:"sub_title" form:"sub_title"`
	ButtonText  string `json:"button_text" form:"button_text"`
	ButtonColor string `json:"button_color" form:"button_color"`
	Duration    string `json:"duration" form:"duration"`
}

func (x *GuideData) GetImage() (r string) {
	if x != nil {
		return x.Image
	}
	return r
}

func (x *GuideData) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *GuideData) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *GuideData) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *GuideData) GetButtonColor() (r string) {
	if x != nil {
		return x.ButtonColor
	}
	return r
}

func (x *GuideData) GetDuration() (r string) {
	if x != nil {
		return x.Duration
	}
	return r
}

func (x *GuideData) SetImage(v string) {
	if x != nil {
		x.Image = v
	}
}

func (x *GuideData) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *GuideData) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *GuideData) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (x *GuideData) SetButtonColor(v string) {
	if x != nil {
		x.ButtonColor = v
	}
}

func (x *GuideData) SetDuration(v string) {
	if x != nil {
		x.Duration = v
	}
}

func (p *GuideData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GuideData(%+v)", *p)
}

type ScreenData struct {
	Day            *Day           `json:"day,omitempty" form:"day"`
	BeginStation   *StationData   `json:"begin_station,omitempty" form:"begin_station"`
	EndStation     *StationData   `json:"end_station,omitempty" form:"end_station"`
	StationTime    *StationTime   `json:"station_time,omitempty" form:"station_time"`
	BeginStationV2 *StationDataV2 `json:"begin_station_v2,omitempty" form:"begin_station_v2"` //带区县的站点数据
	EndStationV2   *StationDataV2 `json:"end_station_v2,omitempty" form:"end_station_v2"`     //带区县的站点数据
	StationTimeV2  *StationTime   `json:"station_time_v2,omitempty" form:"station_time_v2"`   //带区县的站点数据
}

func (x *ScreenData) GetDay() (r *Day) {
	if x != nil {
		return x.Day
	}
	return r
}

func (x *ScreenData) GetBeginStation() (r *StationData) {
	if x != nil {
		return x.BeginStation
	}
	return r
}

func (x *ScreenData) GetEndStation() (r *StationData) {
	if x != nil {
		return x.EndStation
	}
	return r
}

func (x *ScreenData) GetStationTime() (r *StationTime) {
	if x != nil {
		return x.StationTime
	}
	return r
}

func (x *ScreenData) GetBeginStationV2() (r *StationDataV2) {
	if x != nil {
		return x.BeginStationV2
	}
	return r
}

func (x *ScreenData) GetEndStationV2() (r *StationDataV2) {
	if x != nil {
		return x.EndStationV2
	}
	return r
}

func (x *ScreenData) GetStationTimeV2() (r *StationTime) {
	if x != nil {
		return x.StationTimeV2
	}
	return r
}

func (x *ScreenData) SetDay(v *Day) {
	if x != nil {
		x.Day = v
	}
}

func (x *ScreenData) SetBeginStation(v *StationData) {
	if x != nil {
		x.BeginStation = v
	}
}

func (x *ScreenData) SetEndStation(v *StationData) {
	if x != nil {
		x.EndStation = v
	}
}

func (x *ScreenData) SetStationTime(v *StationTime) {
	if x != nil {
		x.StationTime = v
	}
}

func (x *ScreenData) SetBeginStationV2(v *StationDataV2) {
	if x != nil {
		x.BeginStationV2 = v
	}
}

func (x *ScreenData) SetEndStationV2(v *StationDataV2) {
	if x != nil {
		x.EndStationV2 = v
	}
}

func (x *ScreenData) SetStationTimeV2(v *StationTime) {
	if x != nil {
		x.StationTimeV2 = v
	}
}

func (p *ScreenData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ScreenData(%+v)", *p)
}

type CalendarItem struct {
	Status int32 `json:"status" form:"status"`
	Date   int64 `json:"date" form:"date"`
}

func (x *CalendarItem) GetStatus() (r int32) {
	if x != nil {
		return x.Status
	}
	return r
}

func (x *CalendarItem) GetDate() (r int64) {
	if x != nil {
		return x.Date
	}
	return r
}

func (x *CalendarItem) SetStatus(v int32) {
	if x != nil {
		x.Status = v
	}
}

func (x *CalendarItem) SetDate(v int64) {
	if x != nil {
		x.Date = v
	}
}

func (p *CalendarItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CalendarItem(%+v)", *p)
}

type Day struct {
	CheckedIndex int32      `json:"checked_index" form:"checked_index"`
	DayList      []*DayList `json:"day_list" form:"day_list"`
}

func (x *Day) GetCheckedIndex() (r int32) {
	if x != nil {
		return x.CheckedIndex
	}
	return r
}

func (x *Day) GetDayList() (r []*DayList) {
	if x != nil {
		return x.DayList
	}
	return r
}

func (x *Day) SetCheckedIndex(v int32) {
	if x != nil {
		x.CheckedIndex = v
	}
}

func (x *Day) SetDayList(v []*DayList) {
	if x != nil {
		x.DayList = v
	}
}

func (p *Day) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Day(%+v)", *p)
}

type DayList struct {
	Date string `json:"date" form:"date"`
	Name string `json:"name" form:"name"`
	Time int64  `json:"time" form:"time"`
	Text string `json:"text" form:"text"`
}

func (x *DayList) GetDate() (r string) {
	if x != nil {
		return x.Date
	}
	return r
}

func (x *DayList) GetName() (r string) {
	if x != nil {
		return x.Name
	}
	return r
}

func (x *DayList) GetTime() (r int64) {
	if x != nil {
		return x.Time
	}
	return r
}

func (x *DayList) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *DayList) SetDate(v string) {
	if x != nil {
		x.Date = v
	}
}

func (x *DayList) SetName(v string) {
	if x != nil {
		x.Name = v
	}
}

func (x *DayList) SetTime(v int64) {
	if x != nil {
		x.Time = v
	}
}

func (x *DayList) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (p *DayList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DayList(%+v)", *p)
}

type StationData struct {
	CheckedId int32               `json:"checked_id" form:"checked_id"`
	Stations  []*IntercityStation `json:"stations" form:"stations"`
}

func (x *StationData) GetCheckedId() (r int32) {
	if x != nil {
		return x.CheckedId
	}
	return r
}

func (x *StationData) GetStations() (r []*IntercityStation) {
	if x != nil {
		return x.Stations
	}
	return r
}

func (x *StationData) SetCheckedId(v int32) {
	if x != nil {
		x.CheckedId = v
	}
}

func (x *StationData) SetStations(v []*IntercityStation) {
	if x != nil {
		x.Stations = v
	}
}

func (p *StationData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StationData(%+v)", *p)
}

type StationDataV2 struct {
	CheckedId       int32              `json:"checked_id" form:"checked_id"`               //站点选中下标
	CountyCheckedId int32              `json:"county_checked_id" form:"county_checked_id"` //区县选中下标
	Counties        []*IntercityCounty `json:"counties" form:"counties"`                   //区县选中下标
	CityName        string             `json:"city_name" form:"city_name"`                 //城市名称
}

func (x *StationDataV2) GetCheckedId() (r int32) {
	if x != nil {
		return x.CheckedId
	}
	return r
}

func (x *StationDataV2) GetCountyCheckedId() (r int32) {
	if x != nil {
		return x.CountyCheckedId
	}
	return r
}

func (x *StationDataV2) GetCounties() (r []*IntercityCounty) {
	if x != nil {
		return x.Counties
	}
	return r
}

func (x *StationDataV2) GetCityName() (r string) {
	if x != nil {
		return x.CityName
	}
	return r
}

func (x *StationDataV2) SetCheckedId(v int32) {
	if x != nil {
		x.CheckedId = v
	}
}

func (x *StationDataV2) SetCountyCheckedId(v int32) {
	if x != nil {
		x.CountyCheckedId = v
	}
}

func (x *StationDataV2) SetCounties(v []*IntercityCounty) {
	if x != nil {
		x.Counties = v
	}
}

func (x *StationDataV2) SetCityName(v string) {
	if x != nil {
		x.CityName = v
	}
}

func (p *StationDataV2) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StationDataV2(%+v)", *p)
}

type IntercityCounty struct {
	Name     string              `json:"name" form:"name"`
	CountyId int64               `json:"county_id" form:"county_id"`
	Stations []*IntercityStation `json:"stations" form:"stations"`
}

func (x *IntercityCounty) GetName() (r string) {
	if x != nil {
		return x.Name
	}
	return r
}

func (x *IntercityCounty) GetCountyId() (r int64) {
	if x != nil {
		return x.CountyId
	}
	return r
}

func (x *IntercityCounty) GetStations() (r []*IntercityStation) {
	if x != nil {
		return x.Stations
	}
	return r
}

func (x *IntercityCounty) SetName(v string) {
	if x != nil {
		x.Name = v
	}
}

func (x *IntercityCounty) SetCountyId(v int64) {
	if x != nil {
		x.CountyId = v
	}
}

func (x *IntercityCounty) SetStations(v []*IntercityStation) {
	if x != nil {
		x.Stations = v
	}
}

func (p *IntercityCounty) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityCounty(%+v)", *p)
}

type IntercityStation struct {
	Name      string        `json:"name" form:"name"`
	StationId int64         `json:"station_id" form:"station_id"`
	Lat       string        `json:"lat" form:"lat"`
	Lng       string        `json:"lng" form:"lng"`
	ImgUrl    string        `json:"img_url" form:"img_url"`
	Tag       *DisabledInfo `json:"tag,omitempty" form:"tag"`
}

func (x *IntercityStation) GetName() (r string) {
	if x != nil {
		return x.Name
	}
	return r
}

func (x *IntercityStation) GetStationId() (r int64) {
	if x != nil {
		return x.StationId
	}
	return r
}

func (x *IntercityStation) GetLat() (r string) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *IntercityStation) GetLng() (r string) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *IntercityStation) GetImgUrl() (r string) {
	if x != nil {
		return x.ImgUrl
	}
	return r
}

func (x *IntercityStation) GetTag() (r *DisabledInfo) {
	if x != nil {
		return x.Tag
	}
	return r
}

func (x *IntercityStation) SetName(v string) {
	if x != nil {
		x.Name = v
	}
}

func (x *IntercityStation) SetStationId(v int64) {
	if x != nil {
		x.StationId = v
	}
}

func (x *IntercityStation) SetLat(v string) {
	if x != nil {
		x.Lat = v
	}
}

func (x *IntercityStation) SetLng(v string) {
	if x != nil {
		x.Lng = v
	}
}

func (x *IntercityStation) SetImgUrl(v string) {
	if x != nil {
		x.ImgUrl = v
	}
}

func (x *IntercityStation) SetTag(v *DisabledInfo) {
	if x != nil {
		x.Tag = v
	}
}

func (p *IntercityStation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityStation(%+v)", *p)
}

type StationTime struct {
	CheckedId int32            `json:"checked_id" form:"checked_id"`
	TimeList  []*IntercityTime `json:"time_list" form:"time_list"`
}

func (x *StationTime) GetCheckedId() (r int32) {
	if x != nil {
		return x.CheckedId
	}
	return r
}

func (x *StationTime) GetTimeList() (r []*IntercityTime) {
	if x != nil {
		return x.TimeList
	}
	return r
}

func (x *StationTime) SetCheckedId(v int32) {
	if x != nil {
		x.CheckedId = v
	}
}

func (x *StationTime) SetTimeList(v []*IntercityTime) {
	if x != nil {
		x.TimeList = v
	}
}

func (p *StationTime) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StationTime(%+v)", *p)
}

type IntercityTime struct {
	Name      string `json:"name" form:"name"`
	StartTime int64  `json:"start_time" form:"start_time"`
	EndTime   int64  `json:"end_time" form:"end_time"`
}

func (x *IntercityTime) GetName() (r string) {
	if x != nil {
		return x.Name
	}
	return r
}

func (x *IntercityTime) GetStartTime() (r int64) {
	if x != nil {
		return x.StartTime
	}
	return r
}

func (x *IntercityTime) GetEndTime() (r int64) {
	if x != nil {
		return x.EndTime
	}
	return r
}

func (x *IntercityTime) SetName(v string) {
	if x != nil {
		x.Name = v
	}
}

func (x *IntercityTime) SetStartTime(v int64) {
	if x != nil {
		x.StartTime = v
	}
}

func (x *IntercityTime) SetEndTime(v int64) {
	if x != nil {
		x.EndTime = v
	}
}

func (p *IntercityTime) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityTime(%+v)", *p)
}

type DisabledInfo struct {
	Icon    string `json:"icon" form:"icon"`
	Content string `json:"content" form:"content"`
}

func (x *DisabledInfo) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *DisabledInfo) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *DisabledInfo) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *DisabledInfo) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (p *DisabledInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DisabledInfo(%+v)", *p)
}

type SubMsg struct {
	Color   string `json:"color" form:"color"`
	Content string `json:"content" form:"content"`
}

func (x *SubMsg) GetColor() (r string) {
	if x != nil {
		return x.Color
	}
	return r
}

func (x *SubMsg) GetContent() (r string) {
	if x != nil {
		return x.Content
	}
	return r
}

func (x *SubMsg) SetColor(v string) {
	if x != nil {
		x.Color = v
	}
}

func (x *SubMsg) SetContent(v string) {
	if x != nil {
		x.Content = v
	}
}

func (p *SubMsg) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubMsg(%+v)", *p)
}

type ButtonInfo struct {
	Text        string            `json:"text" form:"text"`
	LinkUrl     string            `json:"link_url" form:"link_url"`
	LinkType    int32             `json:"link_type" form:"link_type"`
	Type        int32             `json:"type" form:"type"`
	LinkParams  map[string]string `json:"link_params" form:"link_params"`
	NewJumpMark bool              `json:"new_jump_mark" form:"new_jump_mark"`
}

func (x *ButtonInfo) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *ButtonInfo) GetLinkUrl() (r string) {
	if x != nil {
		return x.LinkUrl
	}
	return r
}

func (x *ButtonInfo) GetLinkType() (r int32) {
	if x != nil {
		return x.LinkType
	}
	return r
}

func (x *ButtonInfo) GetType() (r int32) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *ButtonInfo) GetLinkParams() (r map[string]string) {
	if x != nil {
		return x.LinkParams
	}
	return r
}

func (x *ButtonInfo) GetNewJumpMark() (r bool) {
	if x != nil {
		return x.NewJumpMark
	}
	return r
}

func (x *ButtonInfo) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *ButtonInfo) SetLinkUrl(v string) {
	if x != nil {
		x.LinkUrl = v
	}
}

func (x *ButtonInfo) SetLinkType(v int32) {
	if x != nil {
		x.LinkType = v
	}
}

func (x *ButtonInfo) SetType(v int32) {
	if x != nil {
		x.Type = v
	}
}

func (x *ButtonInfo) SetLinkParams(v map[string]string) {
	if x != nil {
		x.LinkParams = v
	}
}

func (x *ButtonInfo) SetNewJumpMark(v bool) {
	if x != nil {
		x.NewJumpMark = v
	}
}

func (p *ButtonInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ButtonInfo(%+v)", *p)
}

type IntercityStationDetail struct {
	Name        string `json:"name" form:"name"`
	BeginMsg    string `json:"begin_msg" form:"begin_msg"`
	Lat         string `json:"lat" form:"lat"`
	Lng         string `json:"lng" form:"lng"`
	Icon        string `json:"icon" form:"icon"`
	Id          int32  `json:"id" form:"id"`
	Type        int32  `json:"type" form:"type"`
	Tag         string `json:"tag" form:"tag"`
	DisplayName string `json:"display_name" form:"display_name"`
	Distance    string `json:"distance" form:"distance"`
	IsSelected  int32  `json:"is_selected" form:"is_selected"`
}

func (x *IntercityStationDetail) GetName() (r string) {
	if x != nil {
		return x.Name
	}
	return r
}

func (x *IntercityStationDetail) GetBeginMsg() (r string) {
	if x != nil {
		return x.BeginMsg
	}
	return r
}

func (x *IntercityStationDetail) GetLat() (r string) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *IntercityStationDetail) GetLng() (r string) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *IntercityStationDetail) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *IntercityStationDetail) GetId() (r int32) {
	if x != nil {
		return x.Id
	}
	return r
}

func (x *IntercityStationDetail) GetType() (r int32) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *IntercityStationDetail) GetTag() (r string) {
	if x != nil {
		return x.Tag
	}
	return r
}

func (x *IntercityStationDetail) GetDisplayName() (r string) {
	if x != nil {
		return x.DisplayName
	}
	return r
}

func (x *IntercityStationDetail) GetDistance() (r string) {
	if x != nil {
		return x.Distance
	}
	return r
}

func (x *IntercityStationDetail) GetIsSelected() (r int32) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *IntercityStationDetail) SetName(v string) {
	if x != nil {
		x.Name = v
	}
}

func (x *IntercityStationDetail) SetBeginMsg(v string) {
	if x != nil {
		x.BeginMsg = v
	}
}

func (x *IntercityStationDetail) SetLat(v string) {
	if x != nil {
		x.Lat = v
	}
}

func (x *IntercityStationDetail) SetLng(v string) {
	if x != nil {
		x.Lng = v
	}
}

func (x *IntercityStationDetail) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *IntercityStationDetail) SetId(v int32) {
	if x != nil {
		x.Id = v
	}
}

func (x *IntercityStationDetail) SetType(v int32) {
	if x != nil {
		x.Type = v
	}
}

func (x *IntercityStationDetail) SetTag(v string) {
	if x != nil {
		x.Tag = v
	}
}

func (x *IntercityStationDetail) SetDisplayName(v string) {
	if x != nil {
		x.DisplayName = v
	}
}

func (x *IntercityStationDetail) SetDistance(v string) {
	if x != nil {
		x.Distance = v
	}
}

func (x *IntercityStationDetail) SetIsSelected(v int32) {
	if x != nil {
		x.IsSelected = v
	}
}

func (p *IntercityStationDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityStationDetail(%+v)", *p)
}

type Detail struct {
	Title              string                    `json:"title" form:"title"`
	SubTitle           string                    `json:"sub_title" form:"sub_title"`
	StationList        []*IntercityStationDetail `json:"station_list" form:"station_list"`
	ButtonText         string                    `json:"button_text" form:"button_text"`
	MapButtonText      string                    `json:"map_button_text" form:"map_button_text"`
	RouteId            int64                     `json:"route_id" form:"route_id"`
	ShiftType          int32                     `json:"shift_type" form:"shift_type"`
	CorrectStationLink string                    `json:"correct_station_link" form:"correct_station_link"`
	RouteSceneType     int32                     `json:"route_scene_type" form:"route_scene_type"`
}

func (x *Detail) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *Detail) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *Detail) GetStationList() (r []*IntercityStationDetail) {
	if x != nil {
		return x.StationList
	}
	return r
}

func (x *Detail) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *Detail) GetMapButtonText() (r string) {
	if x != nil {
		return x.MapButtonText
	}
	return r
}

func (x *Detail) GetRouteId() (r int64) {
	if x != nil {
		return x.RouteId
	}
	return r
}

func (x *Detail) GetShiftType() (r int32) {
	if x != nil {
		return x.ShiftType
	}
	return r
}

func (x *Detail) GetCorrectStationLink() (r string) {
	if x != nil {
		return x.CorrectStationLink
	}
	return r
}

func (x *Detail) GetRouteSceneType() (r int32) {
	if x != nil {
		return x.RouteSceneType
	}
	return r
}

func (x *Detail) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *Detail) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *Detail) SetStationList(v []*IntercityStationDetail) {
	if x != nil {
		x.StationList = v
	}
}

func (x *Detail) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (x *Detail) SetMapButtonText(v string) {
	if x != nil {
		x.MapButtonText = v
	}
}

func (x *Detail) SetRouteId(v int64) {
	if x != nil {
		x.RouteId = v
	}
}

func (x *Detail) SetShiftType(v int32) {
	if x != nil {
		x.ShiftType = v
	}
}

func (x *Detail) SetCorrectStationLink(v string) {
	if x != nil {
		x.CorrectStationLink = v
	}
}

func (x *Detail) SetRouteSceneType(v int32) {
	if x != nil {
		x.RouteSceneType = v
	}
}

func (p *Detail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Detail(%+v)", *p)
}

type IntercityStationInfo struct {
	Start  *IntercityStation `json:"start,omitempty" form:"start"`
	End    *IntercityStation `json:"end,omitempty" form:"end"`
	Detail *Detail           `json:"detail,omitempty" form:"detail"`
}

func (x *IntercityStationInfo) GetStart() (r *IntercityStation) {
	if x != nil {
		return x.Start
	}
	return r
}

func (x *IntercityStationInfo) GetEnd() (r *IntercityStation) {
	if x != nil {
		return x.End
	}
	return r
}

func (x *IntercityStationInfo) GetDetail() (r *Detail) {
	if x != nil {
		return x.Detail
	}
	return r
}

func (x *IntercityStationInfo) SetStart(v *IntercityStation) {
	if x != nil {
		x.Start = v
	}
}

func (x *IntercityStationInfo) SetEnd(v *IntercityStation) {
	if x != nil {
		x.End = v
	}
}

func (x *IntercityStationInfo) SetDetail(v *Detail) {
	if x != nil {
		x.Detail = v
	}
}

func (p *IntercityStationInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityStationInfo(%+v)", *p)
}

type EstimateCard struct {
	EstimateId         string                  `json:"estimate_id" form:"estimate_id"`
	TimeMsg            string                  `json:"time_msg" form:"time_msg"`
	SubMsg             *SubMsg                 `json:"sub_msg,omitempty" form:"sub_msg"`
	SubTitle           string                  `json:"sub_title" form:"sub_title"`
	FeeMsg             string                  `json:"fee_msg" form:"fee_msg"`
	FeeDescList        []*TagWithIconAndBorder `json:"fee_desc_list" form:"fee_desc_list"`
	StationInfo        *IntercityStationInfo   `json:"station_info,omitempty" form:"station_info"`
	ButtonInfo         *ButtonInfo             `json:"button_info,omitempty" form:"button_info"`
	BusShiftId         string                  `json:"bus_shift_id" form:"bus_shift_id"`
	SubTagList         []*SubMsg               `json:"sub_tag_list" form:"sub_tag_list"`
	SubTitleList       []string                `json:"sub_title_list" form:"sub_title_list"`
	LeftTopTitle       *string                 `json:"left_top_title,omitempty" form:"left_top_title"`
	TimeMsgDescription string                  `json:"time_msg_description" form:"time_msg_description"`
	Background         *string                 `json:"background,omitempty" form:"background"`
}

func (x *EstimateCard) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *EstimateCard) GetTimeMsg() (r string) {
	if x != nil {
		return x.TimeMsg
	}
	return r
}

func (x *EstimateCard) GetSubMsg() (r *SubMsg) {
	if x != nil {
		return x.SubMsg
	}
	return r
}

func (x *EstimateCard) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *EstimateCard) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *EstimateCard) GetFeeDescList() (r []*TagWithIconAndBorder) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *EstimateCard) GetStationInfo() (r *IntercityStationInfo) {
	if x != nil {
		return x.StationInfo
	}
	return r
}

func (x *EstimateCard) GetButtonInfo() (r *ButtonInfo) {
	if x != nil {
		return x.ButtonInfo
	}
	return r
}

func (x *EstimateCard) GetBusShiftId() (r string) {
	if x != nil {
		return x.BusShiftId
	}
	return r
}

func (x *EstimateCard) GetSubTagList() (r []*SubMsg) {
	if x != nil {
		return x.SubTagList
	}
	return r
}

func (x *EstimateCard) GetSubTitleList() (r []string) {
	if x != nil {
		return x.SubTitleList
	}
	return r
}

func (x *EstimateCard) GetLeftTopTitle() (r string) {
	if x != nil && x.LeftTopTitle != nil {
		return *x.LeftTopTitle
	}
	return r
}

func (x *EstimateCard) GetTimeMsgDescription() (r string) {
	if x != nil {
		return x.TimeMsgDescription
	}
	return r
}

func (x *EstimateCard) GetBackground() (r string) {
	if x != nil && x.Background != nil {
		return *x.Background
	}
	return r
}

func (x *EstimateCard) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *EstimateCard) SetTimeMsg(v string) {
	if x != nil {
		x.TimeMsg = v
	}
}

func (x *EstimateCard) SetSubMsg(v *SubMsg) {
	if x != nil {
		x.SubMsg = v
	}
}

func (x *EstimateCard) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *EstimateCard) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *EstimateCard) SetFeeDescList(v []*TagWithIconAndBorder) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *EstimateCard) SetStationInfo(v *IntercityStationInfo) {
	if x != nil {
		x.StationInfo = v
	}
}

func (x *EstimateCard) SetButtonInfo(v *ButtonInfo) {
	if x != nil {
		x.ButtonInfo = v
	}
}

func (x *EstimateCard) SetBusShiftId(v string) {
	if x != nil {
		x.BusShiftId = v
	}
}

func (x *EstimateCard) SetSubTagList(v []*SubMsg) {
	if x != nil {
		x.SubTagList = v
	}
}

func (x *EstimateCard) SetSubTitleList(v []string) {
	if x != nil {
		x.SubTitleList = v
	}
}

func (x *EstimateCard) SetLeftTopTitle(v string) {
	if x != nil {
		x.LeftTopTitle = &v
	}
}

func (x *EstimateCard) SetTimeMsgDescription(v string) {
	if x != nil {
		x.TimeMsgDescription = v
	}
}

func (x *EstimateCard) SetBackground(v string) {
	if x != nil {
		x.Background = &v
	}
}

func (p *EstimateCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EstimateCard(%+v)", *p)
}

type IntercityBasicFeeLiteRequest struct {
	StartStationId    string `json:"start_station_id" form:"start_station_id"`
	EndStationId      string `json:"end_station_id" form:"end_station_id"`
	BusServiceShiftId string `json:"bus_service_shift_id" form:"bus_service_shift_id"`
	RouteId           string `json:"route_id" form:"route_id"`
	AgentType         string `json:"agent_type" form:"agent_type"`
	NtupleInfo        string `json:"ntuple_info" form:"ntuple_info"`
	District          string `json:"district" form:"district"`
}

func (x *IntercityBasicFeeLiteRequest) GetStartStationId() (r string) {
	if x != nil {
		return x.StartStationId
	}
	return r
}

func (x *IntercityBasicFeeLiteRequest) GetEndStationId() (r string) {
	if x != nil {
		return x.EndStationId
	}
	return r
}

func (x *IntercityBasicFeeLiteRequest) GetBusServiceShiftId() (r string) {
	if x != nil {
		return x.BusServiceShiftId
	}
	return r
}

func (x *IntercityBasicFeeLiteRequest) GetRouteId() (r string) {
	if x != nil {
		return x.RouteId
	}
	return r
}

func (x *IntercityBasicFeeLiteRequest) GetAgentType() (r string) {
	if x != nil {
		return x.AgentType
	}
	return r
}

func (x *IntercityBasicFeeLiteRequest) GetNtupleInfo() (r string) {
	if x != nil {
		return x.NtupleInfo
	}
	return r
}

func (x *IntercityBasicFeeLiteRequest) GetDistrict() (r string) {
	if x != nil {
		return x.District
	}
	return r
}

func (x *IntercityBasicFeeLiteRequest) SetStartStationId(v string) {
	if x != nil {
		x.StartStationId = v
	}
}

func (x *IntercityBasicFeeLiteRequest) SetEndStationId(v string) {
	if x != nil {
		x.EndStationId = v
	}
}

func (x *IntercityBasicFeeLiteRequest) SetBusServiceShiftId(v string) {
	if x != nil {
		x.BusServiceShiftId = v
	}
}

func (x *IntercityBasicFeeLiteRequest) SetRouteId(v string) {
	if x != nil {
		x.RouteId = v
	}
}

func (x *IntercityBasicFeeLiteRequest) SetAgentType(v string) {
	if x != nil {
		x.AgentType = v
	}
}

func (x *IntercityBasicFeeLiteRequest) SetNtupleInfo(v string) {
	if x != nil {
		x.NtupleInfo = v
	}
}

func (x *IntercityBasicFeeLiteRequest) SetDistrict(v string) {
	if x != nil {
		x.District = v
	}
}

func (p *IntercityBasicFeeLiteRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityBasicFeeLiteRequest(%+v)", *p)
}

type IntercityBasicFeeLiteResponse struct {
	Errno   int32                      `json:"errno" form:"errno"`
	Errmsg  string                     `json:"errmsg" form:"errmsg"`
	TraceId string                     `json:"trace_id" form:"trace_id"`
	Data    *IntercityBasicFeeLiteData `json:"data,omitempty" form:"data"`
}

func (x *IntercityBasicFeeLiteResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *IntercityBasicFeeLiteResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *IntercityBasicFeeLiteResponse) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *IntercityBasicFeeLiteResponse) GetData() (r *IntercityBasicFeeLiteData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *IntercityBasicFeeLiteResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *IntercityBasicFeeLiteResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *IntercityBasicFeeLiteResponse) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (x *IntercityBasicFeeLiteResponse) SetData(v *IntercityBasicFeeLiteData) {
	if x != nil {
		x.Data = v
	}
}

func (p *IntercityBasicFeeLiteResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityBasicFeeLiteResponse(%+v)", *p)
}

type IntercityBasicFeeLiteData struct {
	FeeMsg    string  `json:"fee_msg" form:"fee_msg"`
	FeeAmount float64 `json:"fee_amount" form:"fee_amount"`
}

func (x *IntercityBasicFeeLiteData) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *IntercityBasicFeeLiteData) GetFeeAmount() (r float64) {
	if x != nil {
		return x.FeeAmount
	}
	return r
}

func (x *IntercityBasicFeeLiteData) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *IntercityBasicFeeLiteData) SetFeeAmount(v float64) {
	if x != nil {
		x.FeeAmount = v
	}
}

func (p *IntercityBasicFeeLiteData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntercityBasicFeeLiteData(%+v)", *p)
}
