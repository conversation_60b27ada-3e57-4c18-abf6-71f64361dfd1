package multi_route_tip_type

import (
	"context"

	commonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"git.xiaojukeji.com/nuwa/trace"
	jsoniter "github.com/json-iterator/go"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type MultiRouteTipTypeProvider interface {
	render.ProductProvider
	GetProductId() int64
	GetComboType() int64
}

const (
	kFlowerBusinessId = 427
)

func GetMultiRouteTipType(ctx context.Context, prov MultiRouteTipTypeProvider) *string {
	var (
		productList = make([]int64, 0)
	)

	config := apollo.GetConfig(ctx, "tripcloud_feature_config", "tc_saas_product_list")["honghu_saas"]
	if config == "" {
		return nil
	}
	err := jsoniter.Unmarshal([]byte(config), &productList)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "GetMultiRouteTipType unmarshal product list error: %v", err)
		return nil
	}

	if util.InArrayInt64(prov.GetProductId(), productList) {
		return nil
	}

	if prov.GetBusinessID() == kFlowerBusinessId {
		return nil
	}

	return buildRouteTipType(ctx, prov)
}

func buildRouteTipType(ctx context.Context, prov MultiRouteTipTypeProvider) *string {
	if prov.GetBusinessID() == 307 {
		return util.String2PtrString("taxi")
	}

	if tripcloud.IsTripcloudProductID(commonConsts.ProductID(prov.GetProductId())) {
		return util.String2PtrString("trip")
	}

	if prov.GetComboType() == 4 {
		return util.String2PtrString("carpool")
	}

	return nil
}
