package is_hide_price

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type Provider interface {
	render.ProductProvider
	render.ApolloProvider
}

func GetResult(ctx context.Context, prov Provider) *int32 {
	var needCalculate int32
	// 1:需要计算价格区间
	uidKey, params := prov.GetApolloParams(biz_runtime.WithUIDKey)
	useNew := taxi.UsingNewBox(ctx, cast.ToString(uidKey), params)
	if prov.GetSubGroupId() == consts.SubGroupIdTaxiPricingBox &&
		prov.GetLevelType() != 5 && !useNew {
		// 打表计价 且不过实验
		needCalculate = 1
		return &needCalculate
	}
	// 0:端上不需要计算价格区间
	return &needCalculate
}
