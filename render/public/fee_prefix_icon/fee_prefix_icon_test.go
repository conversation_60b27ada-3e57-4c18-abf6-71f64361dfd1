package fee_prefix_icon

import (
	"context"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

// MockProvider 实现 FeeMsgPrefixProvider 接口
type MockProvider struct {
	dynamicDiffPrice float64
	dupsDynamicRaise float64
	dynamicTimes     float64
	dupsDynamicTimes float64
	isHitDynamicAB   bool
}

func (m *MockProvider) IsHitDynamicIconAb(ctx context.Context) bool {
	return m.isHitDynamicAB
}

func (m *MockProvider) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return &PriceApi.EstimateNewFormBillInfo{
		DynamicDiffPrice: m.dynamicDiffPrice,
		DupsDynamicRaise: m.dupsDynamicRaise,
		DynamicTimes:     m.dynamicTimes,
		DupsDynamicTimes: m.dupsDynamicTimes,
	}
}

// 实现BillInfoProvider接口的其他必要方法
func (m *MockProvider) GetBillFeeDetailInfo() map[string]float64 {
	return nil
}

func (m *MockProvider) GetBillDriverMetre() int64 {
	return 0
}

func (m *MockProvider) GetBillInfoCurrency() string {
	return ""
}

func (m *MockProvider) GetEstimateFee() float64 {
	return 0
}

func (m *MockProvider) GetCarpoolFailEstimateFee() (float64, bool) {
	return 0, false
}

func (m *MockProvider) GetCapPrice() float64 {
	return 0
}

func (m *MockProvider) GetMemberDynamicProtectFee() float64 {
	return 0
}

func (m *MockProvider) GetDisplayLines() map[string]*PriceApi.DisplayLine {
	return nil
}

func (m *MockProvider) GetPersonalEstimateFee() float64 {
	return 0
}

func (m *MockProvider) GetDynamicTotalFee() float64 {
	return 0
}

func (m *MockProvider) GetTotalFeeWithoutDiscount() float64 {
	return 0
}

func TestGetFeeMsgPrefixIcon(t *testing.T) {
	ctx := context.Background()

	t.Run("无动态加价时返回nil", func(t *testing.T) {
		provider := &MockProvider{
			dynamicDiffPrice: 0,
			dupsDynamicRaise: 0,
			dynamicTimes:     0,
			dupsDynamicTimes: 0,
			isHitDynamicAB:   true,
		}

		result := GetFeeMsgPrefixIcon(ctx, provider)
		assert.Nil(t, result, "当无动态加价时应返回nil")
	})

	t.Run("有普通动态加价但AB未命中时返回nil", func(t *testing.T) {
		provider := &MockProvider{
			dynamicDiffPrice: 10,
			dupsDynamicRaise: 0,
			dynamicTimes:     1.5,
			dupsDynamicTimes: 0,
			isHitDynamicAB:   false,
		}

		result := GetFeeMsgPrefixIcon(ctx, provider)
		assert.Nil(t, result, "当AB未命中时应返回nil")
	})

	t.Run("有动态加价且AB命中时返回icon", func(t *testing.T) {
		provider := &MockProvider{
			dynamicDiffPrice: 10,
			dupsDynamicRaise: 0,
			dynamicTimes:     1.5,
			dupsDynamicTimes: 0,
			isHitDynamicAB:   true,
		}

		expectedIcon := "test_icon"
		patch := mockey.Mock(dcmp.GetDcmpPlainContent).Return(expectedIcon).Build()
		defer patch.UnPatch()

		result := GetFeeMsgPrefixIcon(ctx, provider)
		assert.NotNil(t, result, "当AB命中时不应返回nil")
		assert.Equal(t, expectedIcon, *result, "返回的icon应匹配预期值")
	})

	t.Run("有DUPS动态加价且AB命中时返回icon", func(t *testing.T) {
		provider := &MockProvider{
			dynamicDiffPrice: 0,
			dupsDynamicRaise: 10,
			dynamicTimes:     0,
			dupsDynamicTimes: 1.5,
			isHitDynamicAB:   true,
		}

		expectedIcon := "test_icon"
		patch := mockey.Mock(dcmp.GetDcmpPlainContent).Return(expectedIcon).Build()
		defer patch.UnPatch()

		result := GetFeeMsgPrefixIcon(ctx, provider)
		assert.NotNil(t, result, "当AB命中时不应返回nil")
		assert.Equal(t, expectedIcon, *result, "返回的icon应匹配预期值")
	})
}
