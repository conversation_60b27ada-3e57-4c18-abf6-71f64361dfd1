package carpool_booking_time_module

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/tidwall/gjson"

	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	timeRangeMsgSuffix = " 接您"
	DefaultSkuDesc     = "default"
)

type InterCityCarpoolBookingTimeSpan interface {
	GetTimeSpan() []*EstimateDecision.TimeSpanV2
	GetSkuInfo() map[string]struct { // 库存模式信息
		Status int32
		Seat   int
	}
	GetVersion() string
	GetProductCategory() int64
	GetProductId() int64
	GetAccessKeyID() int
	render.ApolloProvider
	GetAgentType() string
}

type SkuDesc struct {
	Threshold     int    `json:"threshold"`
	LessSkuDesc   string `json:"less_sku_desc"`
	EnoughSkuDesc string `json:"enough_sku_desc"`
}

func GetSkuDesc(ctx context.Context, productCategory int64) *SkuDesc {
	var (
		SKUDescTemp string
		skuDesc     = &SkuDesc{}
	)
	skuTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_sku-time_span")
	skuDescStr := gjson.Get(skuTemplate, "sku_desc_pc").String()
	SKUDescTemp = gjson.Get(skuDescStr, strconv.Itoa(int(productCategory))).String()
	if len(SKUDescTemp) == 0 {
		SKUDescTemp = gjson.Get(skuDescStr, DefaultSkuDesc).String()
	}
	json.Unmarshal([]byte(SKUDescTemp), skuDesc)
	return skuDesc
}

func InterCityCarpoolBookingTime(ctx context.Context, span InterCityCarpoolBookingTimeSpan, isSku int16) (resp *proto.InterCityCarpoolBookingModule) {
	if span.GetAgentType() == consts.AgentTypeKeqiScan {
		return
	}
	var (
		timeSpan = span.GetTimeSpan()
		skuInfo  = span.GetSkuInfo()
	)
	resp = &proto.InterCityCarpoolBookingModule{}
	if len(timeSpan) <= 0 {
		return resp
	}
	skuDesc := GetSkuDesc(ctx, span.GetProductCategory())
	respTimeSpan := make([]*proto.InterCityTimeSpan, 0)
	for _, spanVal := range timeSpan {
		timeRangeList := make([]*proto.InterCityTimeRange, 0)
		for _, rangeValue := range spanVal.Range {
			var msg string
			if isAddSymbol(span.GetAccessKeyID(), span.GetVersion()) {
				msg = "{" + rangeValue.Label + "}" + timeRangeMsgSuffix
			} else {
				msg = rangeValue.Label + timeRangeMsgSuffix
			}
			timeRange := &proto.InterCityTimeRange{
				Value:     rangeValue.Value,
				Msg:       msg,
				OuterMsg:  rangeValue.Label,
				OrderType: order.OrderTypeBooking,
				Available: true,
			}
			// 库存模式
			if isSku == 1 {
				if skuInfo != nil {
					if _, b := skuInfo[rangeValue.Value]; b {
						if skuInfo[rangeValue.Value].Seat > skuDesc.Threshold {
							// 车辆充足
							timeRange.SkuDesc = skuDesc.EnoughSkuDesc
						} else {
							// 车辆紧张
							timeRange.SkuDesc = skuDesc.LessSkuDesc
						}
						// 添加"预约优惠"标识
						timeSliceIcon := getPreferentialIcon(ctx, span, rangeValue)
						if timeSliceIcon != "" {
							timeRange.Icon = timeSliceIcon
						}
					}
				}
			}
			timeRangeList = append(timeRangeList, timeRange)
		}

		if len(timeRangeList) > 0 {
			span := &proto.InterCityTimeSpan{
				Date:  spanVal.Title,
				Range: timeRangeList,
				Day:   spanVal.Date,
			}
			respTimeSpan = append(respTimeSpan, span)
		}
	}

	if len(respTimeSpan) > 0 {
		resp.Title = dcmp.GetJSONContentWithPath(ctx, "inter_city_carpool-booking_time", nil, "title")
		resp.Subtitle = dcmp.GetJSONContentWithPath(ctx, "inter_city_carpool-booking_time", nil, "sub_title")
		resp.ButtonText = dcmp.GetJSONContentWithPath(ctx, "inter_city_carpool-booking_time", nil, "confirm_button")
		resp.TimeSpan = respTimeSpan

		firstSpan := respTimeSpan[0].Range[0]
		timeList := make([]int64, 0)
		if err := json.Unmarshal([]byte(firstSpan.Value), &timeList); err == nil {
			if len(timeList) == 2 && time.Now().Unix() >= timeList[0] {
				resp.FirstSpanFit = true
				//重写第一个时间片的信息
				originMsgSlice := strings.Split(firstSpan.Msg, "~")
				originOuterMsgSlice := strings.Split(firstSpan.OuterMsg, "~")
				if len(originMsgSlice) == 2 && len(originOuterMsgSlice) == 2 {
					if isAddSymbol(span.GetAccessKeyID(), span.GetVersion()) {
						respTimeSpan[0].Range[0].Msg = "{" + "现在~" + originMsgSlice[1]
					} else {
						respTimeSpan[0].Range[0].Msg = "现在~" + originMsgSlice[1]
					}

					respTimeSpan[0].Range[0].OuterMsg = "现在~" + originOuterMsgSlice[1]
				}
			}
		}
	}

	return resp
}

func IntercityCarpoolBookingTimeAnycar(ctx context.Context, span InterCityCarpoolBookingTimeSpan) (resp *proto.InterCityCarpoolBookingModule) {
	if span.GetAgentType() == consts.AgentTypeKeqiScan {
		return
	}
	var (
		timeSpan = span.GetTimeSpan()
		skuInfo  = span.GetSkuInfo()
	)
	resp = &proto.InterCityCarpoolBookingModule{}
	if len(timeSpan) <= 0 {
		return resp
	}
	skuDesc := GetSkuDesc(ctx, span.GetProductCategory())
	respTimeSpan := make([]*proto.InterCityTimeSpan, 0, 0)
	now := time.Now()
	// 循环每天
	for _, spanVal := range timeSpan {
		timeRangeList := make([]*proto.InterCityTimeRange, 0, 0)
		// 循环一天的每个时间片
		for _, rangeValue := range spanVal.Range {
			var orderType int32
			if util.IsCurrentTimeSpanRangeValue(rangeValue.Value, now) {
				orderType = order.OrderTypeNow
			} else {
				orderType = order.OrderTypeBooking
			}
			timeRange := &proto.InterCityTimeRange{
				Value:     rangeValue.Value,
				Msg:       "{" + rangeValue.Label + "}" + timeRangeMsgSuffix,
				OuterMsg:  rangeValue.Label,
				OrderType: orderType,
				Available: true,
			}
			// 识别第一个时间片
			// 都是库存模式
			if skuInfo != nil {
				if _, b := skuInfo[rangeValue.Value]; b {
					if skuInfo[rangeValue.Value].Seat > skuDesc.Threshold {
						// 车辆充足
						timeRange.SkuDesc = skuDesc.EnoughSkuDesc
					} else {
						// 车辆紧张
						timeRange.SkuDesc = skuDesc.LessSkuDesc
					}
					// 添加"预约优惠"标识
					timeSliceIcon := getPreferentialIcon(ctx, span, rangeValue)
					if timeSliceIcon != "" {
						timeRange.Icon = timeSliceIcon
					}
				}
			}
			timeRangeList = append(timeRangeList, timeRange)
		}
		if len(timeRangeList) > 0 {
			span := &proto.InterCityTimeSpan{
				Date:  spanVal.Title,
				Range: timeRangeList,
				Day:   spanVal.Date,
			}
			respTimeSpan = append(respTimeSpan, span)
		}
	}

	if len(respTimeSpan) > 0 {
		resp.Title = dcmp.GetJSONContentWithPath(ctx, "inter_city_carpool-booking_time", nil, "title")
		resp.Subtitle = dcmp.GetJSONContentWithPath(ctx, "inter_city_carpool-booking_time", nil, "sub_title")
		resp.ButtonText = dcmp.GetJSONContentWithPath(ctx, "inter_city_carpool-booking_time", nil, "confirm_button")
		resp.TimeSpan = respTimeSpan

		firstSpan := respTimeSpan[0].Range[0]
		timeList := make([]int64, 0)
		if err := json.Unmarshal([]byte(firstSpan.Value), &timeList); err == nil {
			if len(timeList) == 2 && time.Now().Unix() >= timeList[0] {
				resp.FirstSpanFit = true
				//重写第一个时间片的信息
				originMsgSlice := strings.Split(firstSpan.Msg, "~")
				originOuterMsgSlice := strings.Split(firstSpan.OuterMsg, "~")
				if len(originMsgSlice) == 2 && len(originOuterMsgSlice) == 2 {
					if isAddSymbol(span.GetAccessKeyID(), span.GetVersion()) {
						respTimeSpan[0].Range[0].Msg = "{" + "现在~" + originMsgSlice[1]
					} else {
						respTimeSpan[0].Range[0].Msg = "现在~" + originMsgSlice[1]
					}

					respTimeSpan[0].Range[0].OuterMsg = "现在~" + originOuterMsgSlice[1]
				}
			}
		}
	}

	return resp
}

func isAddSymbol(accessKeyID int, version string) bool {
	if accessKeyID == 9 || accessKeyID == 22 {
		return true
	}
	if accessKeyID == 1 || accessKeyID == 2 {
		if util.CompareAppVersion(version, "6.6.5") == 1 {
			return true
		}
	}
	return false
}

// getPreferentialIcon 获取添加"预约优惠"标识
func getPreferentialIcon(ctx context.Context, span InterCityCarpoolBookingTimeSpan, item *EstimateDecision.SpanItem) string {

	pidKey, params := span.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithRouteGroup, biz_runtime.WithComboID, biz_runtime.WithProductID)
	ok, toggle := apollo.FeatureExp(ctx, "intercity_carpool_subscribe_preferential_toggle", pidKey, params)
	if !ok {
		return ""
	}

	ok, assign := apollo.FeatureExp(ctx, "yuantu_reservation_discount", pidKey, params)
	// 满足开关，满足实验后，获取开关的时间间隔，判断时间片是否出icon
	if ok && "treatment_group" == assign.GetGroupName() {
		// 获取时间间隔(s)
		timeInterval := cast.ToInt64(toggle.GetParameter("time_interval", "0")) * 60

		var timeRange []int64
		err := json.Unmarshal([]byte(item.Value), &timeRange)
		if err != nil {
			return ""
		}

		// 若左时间片 - 当前时间 > 时间间隔，就出icon
		if timeRange[0]-time.Now().Unix() > timeInterval {
			timeSliceIcon := toggle.GetParameter("time_slice_icon", "")
			return timeSliceIcon
		}
	}
	return ""
}
