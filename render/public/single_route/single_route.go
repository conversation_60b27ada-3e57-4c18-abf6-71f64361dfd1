package single_route

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"github.com/spf13/cast"
)

type SingleRoute interface {
	GetLang() string
	GetAppVersion() string
	GetAccessKeyId() int32

	render.ProductProvider
	render.ApolloProvider
}

func GetIsSingleRoute(ctx context.Context, prov SingleRoute) *int32 {
	var isSingleRoute int32
	if showLessRouteLine(ctx, prov) {
		isSingleRoute = 1
	} else {
		isSingleRoute = 0
	}
	return &isSingleRoute
}

func showLessRouteLine(ctx context.Context, prov SingleRoute) bool {
	apolloKey, apolloParams := prov.GetApolloParams(biz_runtime.WithUIDKey)
	apolloParams["product_id"] = cast.ToString(prov.GetProductId())
	toggleAllow, _ := apollo.FeatureExp(ctx, "show_less_route_line_control", apolloKey, apolloParams)
	return toggleAllow
}
