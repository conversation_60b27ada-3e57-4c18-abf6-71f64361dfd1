package radio_setting

import (
	"context"
	"strconv"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	jsoniter "github.com/json-iterator/go"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const productCategoryCarpoolStation = 3
const productCategoryCarpoolInterNew = 80
const productCategoryCarpoolFlatRateBySeat = 69
const layoutPackageBoxPreferentialMarketing = 8
const layoutPackageBoxPreferentialRecommendMarketing = 9
const productCategoryTaxiCarpool = 195
const labelLocation = "dual_price_answer_socket_switch"
const radioSettingDcmpKey = "radio_setting-carpool_seat_num"
const SinkFormExpName = "athena_form_anycar_sinking_switch"

type RadioSettingProvider interface {
	GetProductCategory() int64
	GetUserSelectSeatNum() int32
	GetAthenaTopRecRes() *AthenaApiv3.TopRecRes

	render.ApolloProvider
}

type RadioSettingDcmpInfo struct {
	Field       string `json:"field"`
	IsHold      bool   `json:"is_hold"`
	NeedRefresh bool   `json:"need_refresh"`
	Label       string `json:"label"`
}

func GetOrderOption(ctx context.Context, prov RadioSettingProvider) *proto.RadioSetting {
	var (
		radioSetting  = &proto.RadioSetting{}
		maxSeatNum    = 2
		options       = make([]*proto.RadioOption, 0)
		userSelectNum = prov.GetUserSelectSeatNum()
		style         = 0
	)

	if !util.InArrayInt64(prov.GetProductCategory(), []int64{productCategoryCarpoolStation, productCategoryCarpoolInterNew, productCategoryCarpoolFlatRateBySeat, productCategoryTaxiCarpool}) {
		return nil
	}

	if userSelectNum <= 0 {
		userSelectNum = 1
	}

	// 读取dcmp配置
	var settingTemplate = &RadioSettingDcmpInfo{}
	err := jsoniter.UnmarshalFromString(dcmp.GetDcmpContent(ctx, radioSettingDcmpKey, nil), settingTemplate)
	if err != nil {
		log.Trace.Warnf(ctx, "radio_setting_config", "Unmarshal radio_setting_config error : %v", err)
	}

	radioSetting.Field = settingTemplate.Field
	radioSetting.IsHold = settingTemplate.IsHold
	radioSetting.NeedRefresh = settingTemplate.NeedRefresh
	radioSetting.SelectedValue = userSelectNum

	for i := 1; i <= maxSeatNum; i++ {
		replace := make(map[string]string)
		replace["num"] = strconv.Itoa(i)
		op := &proto.RadioOption{
			Label: util.ReplaceTag(ctx, settingTemplate.Label, replace),
			Value: int32(i),
		}
		options = append(options, op)
	}

	radioSetting.Options = options

	pid, apolloParams := prov.GetApolloParams(biz_runtime.WithPIDKey)
	// 拼座标签 位置
	if productCategoryCarpoolStation == prov.GetProductCategory() && apollo.FeatureToggle(ctx, labelLocation, pid, apolloParams) {
		style = 1
	}

	// 下沉表单
	allow, expParams := apollo.GetParameters(SinkFormExpName, pid, apolloParams)
	topRecRes := prov.GetAthenaTopRecRes()
	if allow && expParams != nil && topRecRes != nil && len(topRecRes.TopRecList) > 0 {
		topPcids := make([]int64, 0)
		for _, item := range topRecRes.TopRecList {
			topPcids = append(topPcids, int64(item.GetCategory()))
		}

		if util.InArrayInt64(prov.GetProductCategory(), topPcids) && util.InArrayInt32(topRecRes.GetStyle(), []int32{layoutPackageBoxPreferentialMarketing, layoutPackageBoxPreferentialRecommendMarketing}) {
			style = 2
		}

	}

	radioSetting.AssemblyStyle = int32(style)
	return radioSetting
}
