package notice_info

import (
	"context"
	"encoding/json"
	"net/url"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	apolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	miniBusNoticeInfoDcmpKey            = "estimate_form_v3_new-notice_info"
	miniBusLongTripNoticeInfoNewDcmpKey = "estimate_form_v3_new-long_trip_notice_info"
	miniBusNoticeInfoNewDcmpKey         = "estimate_form_v3_new-notice_info_new"
	voyagerNoticeInfoNewDcmpKey         = "estimate_form_v3_new-voy_notice_info"
)

type NoticeInfoProvider interface {
	GetCarpoolType() int64
	GetEtpTimeDuration() int64
	GetExtMap() map[string]string
	GetMiniBusDistLimit() int64
	IsOnlyMinibus() bool
	GetBaseReqData() *models.BaseReqData
	GetProductCategory() int64
	GetBillExtraMap() map[string]interface{}
}

type NoticeDcmpInfo struct {
	RightText string `json:"right_text"`
	JumpUrl   string `json:"jump_url"`
	DistLimit string `json:"dist_limit"`
	EtpMin    string `json:"etp_min"`
}

type NoticeDcmpInfoNew struct {
	Content             string   `json:"content"`
	Icon                string   `json:"icon"`
	BackgroundGradients []string `json:"background_gradients"`
	BorderColor         string   `json:"border_color"`
	RightText           string   `json:"right_text"`
	JumpUrl             string   `json:"jump_url"`
	BubbleArrowTop      string   `json:"bubble_arrow_top"`
}

type VoyNoticeInfo struct {
	TagList []struct {
		Content string `json:"content"`
		Icon    string `json:"icon"`
	} `json:"tag_list"`
	BackgroundGradients []string `json:"background_gradients"`
	BorderColor         string   `json:"border_color"`
	RightText           string   `json:"right_text"`
	JumpUrl             string   `json:"jump_url"`
	BubbleArrowTop      string   `json:"bubble_arrow_top"`
	ActionType          int32    `json:"action_type"`
}

type LongTripConfig struct {
	StartFenceName string `json:"start_fence_name"`
	DestFenceName  string `json:"dest_fence_name"`
	ApolloID       string `json:"apolloId"`
}

func GetNoticeInfo(ctx context.Context, prov NoticeInfoProvider) *proto.NoticeInfo {
	if carpool.IsMiniBus(int(prov.GetCarpoolType())) {
		return MiniBusNoticeInfo(ctx, prov)
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdAutopilotByTC {
		return VoyagerNoticeInfo(ctx, prov)
	}

	return nil
}

func MiniBusNoticeInfo(ctx context.Context, prov NoticeInfoProvider) *proto.NoticeInfo {
	if NewMiniBusBuild(ctx, prov) {
		toggle := apollo.FeatureToggle(ctx, "minibus_long_trip_open_city_switch", util.ToString(prov.GetBaseReqData().PassengerInfo.PID), map[string]string{
			"pid":           util.ToString(prov.GetBaseReqData().PassengerInfo.PID),
			"access_key_id": util.ToString(prov.GetBaseReqData().CommonInfo.AccessKeyID),
			"app_version":   prov.GetBaseReqData().CommonInfo.AppVersion,
			"page_type":     util.ToString(prov.GetBaseReqData().CommonInfo.PageType),
		})
		if toggle {
			extMap := prov.GetExtMap()
			if extMap != nil {
				if _, ok := extMap["long_trip_id"]; ok {
					// 命中小巴专线模式
					return getLongTripNoticeInfo(ctx, prov)
				}
			}
			// 命中普通小巴新版（富文本）
			return getNewNoticeInfo(ctx, prov)

		}

		return getNormalNoticeInfo(ctx, prov)
	}

	return getOldNoticeInfo(ctx, prov)
}

func VoyagerNoticeInfo(ctx context.Context, prov NoticeInfoProvider) *proto.NoticeInfo {
	if prov.GetBillExtraMap() == nil {
		return nil
	}

	toggle := apollo.FeatureToggle(ctx, "zidongjiashi_choose_cell_open", util.ToString(prov.GetBaseReqData().PassengerInfo.PID), map[string]string{
		"pid":           util.ToString(prov.GetBaseReqData().PassengerInfo.PID),
		"phone":         prov.GetBaseReqData().PassengerInfo.Phone,
		"tab_id":        prov.GetBaseReqData().CommonInfo.TabId,
		"access_key_id": util.ToString(prov.GetBaseReqData().CommonInfo.AccessKeyID),
		"app_version":   prov.GetBaseReqData().CommonInfo.AppVersion,
	})
	if !toggle {
		return nil
	}

	return getVoyagerNoticeInfo(ctx, prov)
}

func getVoyagerNoticeInfo(ctx context.Context, prov NoticeInfoProvider) *proto.NoticeInfo {
	historyExtraMap := prov.GetBillExtraMap()
	voyRouteId, ok := historyExtraMap["voy_route_id"]
	if !ok {
		return nil
	}

	dcmpInfo := &VoyNoticeInfo{}
	raw := dcmp.GetDcmpContent(ctx, voyagerNoticeInfoNewDcmpKey, nil)
	if err := json.Unmarshal([]byte(raw), dcmpInfo); err != nil {
		return nil
	}

	noticeInfo := &proto.NoticeInfo{}
	noticeInfo.SetJumpUrl(UrlAppendParams(dcmpInfo.JumpUrl, map[string]string{
		"starting_lng": util.ToString(prov.GetBaseReqData().AreaInfo.FromLng),
		"starting_lat": util.ToString(prov.GetBaseReqData().AreaInfo.FromLat),
		"dest_lng":     util.ToString(prov.GetBaseReqData().AreaInfo.ToLng),
		"dest_lat":     util.ToString(prov.GetBaseReqData().AreaInfo.ToLat),
		"city_id":      util.ToString(prov.GetBaseReqData().AreaInfo.City),
		"from_address": prov.GetBaseReqData().AreaInfo.FromAddress,
		"from_name":    prov.GetBaseReqData().AreaInfo.FromName,
		"to_address":   prov.GetBaseReqData().AreaInfo.ToAddress,
		"to_name":      prov.GetBaseReqData().AreaInfo.ToName,
		"voy_route_id": util.ToString(voyRouteId),
	}))
	noticeInfo.SetRightText(dcmpInfo.RightText)
	noticeInfo.SetBackgroundGradients(dcmpInfo.BackgroundGradients)
	noticeInfo.SetBorderColor(dcmpInfo.BorderColor)
	noticeInfo.SetBubbleArrowTop(dcmpInfo.BubbleArrowTop)
	noticeInfo.SetActionType(dcmpInfo.ActionType)

	tagList := make([]*proto.Tag, 0)
	for _, tag := range dcmpInfo.TagList {
		tagList = append(tagList, &proto.Tag{
			Content: tag.Content,
			Icon:    tag.Icon,
		})
	}
	noticeInfo.SetTagList(tagList)
	return noticeInfo
}

func getLongTripNoticeInfo(ctx context.Context, prov NoticeInfoProvider) *proto.NoticeInfo {
	dcmpInfo := &NoticeDcmpInfoNew{}
	raw := dcmp.GetDcmpContent(ctx, miniBusLongTripNoticeInfoNewDcmpKey, nil)
	if err := json.Unmarshal([]byte(raw), dcmpInfo); err != nil {
		return nil
	}

	configs, err := apollo.GetConfigsByNamespaceAndConditions(ctx, "minibus_long_trip_open_city", apolloModel.NewCondition(map[string]string{
		"apolloId": prov.GetExtMap()["long_trip_id"],
	}))
	if len(configs) == 0 {
		return nil
	}
	routeInfo := make([]*LongTripConfig, 0)
	if err = json.Unmarshal(configs, &routeInfo); err != nil {
		return nil
	}

	noticeInfo := &proto.NoticeInfo{}
	noticeInfo.SetRightText(dcmpInfo.RightText)
	noticeInfo.SetJumpUrl(UrlAppendParams(dcmpInfo.JumpUrl, map[string]string{
		"uid":           util.ToString(prov.GetBaseReqData().PassengerInfo.UID),
		"lang":          prov.GetBaseReqData().CommonInfo.Lang,
		"app_version":   prov.GetBaseReqData().CommonInfo.AppVersion,
		"access_key_id": util.ToString(prov.GetBaseReqData().CommonInfo.AccessKeyID),
		"city_id":       util.ToString(prov.GetBaseReqData().AreaInfo.City),
		"long_trip_id":  prov.GetExtMap()["long_trip_id"],
		"page_from":     "1",
	}))
	noticeInfo.SetBackgroundGradients(dcmpInfo.BackgroundGradients)
	noticeInfo.SetBorderColor(dcmpInfo.BorderColor)
	noticeInfo.SetBubbleArrowTop(dcmpInfo.BubbleArrowTop)
	noticeInfo.SetActionType(1) // 0: tab，1: h5

	tagList := make([]*proto.Tag, 0)
	tagList = append(tagList, &proto.Tag{
		Content: util.ReplaceTag(ctx, dcmpInfo.Content, map[string]string{
			"start_name": routeInfo[0].StartFenceName,
			"dest_name":  routeInfo[0].DestFenceName,
		}),
		Icon: dcmpInfo.Icon,
	})
	noticeInfo.SetTagList(tagList)

	// 埋点透传
	omega := &proto.OmegaItem{
		Show: &proto.OmegaValue{
			Key: "wyc_lcar_pas_anycar_bubblecard_sw",
			Params: map[string]int32{
				"long_trip_id": util.ToInt32(prov.GetExtMap()["long_trip_id"]),
				"model_type":   1,
			},
		},
		Click: &proto.OmegaValue{
			Key: "wyc_lcar_pas_anycar_bubblecard_ck",
			Params: map[string]int32{
				"long_trip_id": util.ToInt32(prov.GetExtMap()["long_trip_id"]),
				"model_type":   1,
			},
		},
	}
	noticeInfo.SetOmega(omega)

	return noticeInfo
}

func getNewNoticeInfo(ctx context.Context, prov NoticeInfoProvider) *proto.NoticeInfo {
	dcmpInfo := &NoticeDcmpInfoNew{}
	dcmpConf := dcmp.GetDcmpContent(ctx, miniBusNoticeInfoNewDcmpKey, nil)
	if err := json.Unmarshal([]byte(dcmpConf), dcmpInfo); err != nil {
		return nil
	}

	noticeInfo := &proto.NoticeInfo{}
	if !prov.IsOnlyMinibus() {
		// 多tp才展示更多班次
		noticeInfo.SetRightText(dcmpInfo.RightText)
		noticeInfo.SetJumpUrl(dcmpInfo.JumpUrl)
	}
	noticeInfo.SetBackgroundGradients(dcmpInfo.BackgroundGradients)
	noticeInfo.SetBorderColor(dcmpInfo.BorderColor)
	noticeInfo.SetBubbleArrowTop(dcmpInfo.BubbleArrowTop)
	noticeInfo.SetActionType(0) // 0: tab，1: h5

	tagList := make([]*proto.Tag, 0)
	tagList = append(tagList, &proto.Tag{
		Content: dcmpInfo.Content,
		Icon:    dcmpInfo.Icon,
	})
	noticeInfo.SetTagList(tagList)

	// 埋点透传
	omega := &proto.OmegaItem{
		Show: &proto.OmegaValue{
			Key: "wyc_lcar_pas_anycar_bubblecard_sw",
			Params: map[string]int32{
				"model_type": 2,
			},
		},
		Click: &proto.OmegaValue{
			Key: "wyc_lcar_pas_anycar_bubblecard_ck",
			Params: map[string]int32{
				"model_type": 2,
			},
		},
	}
	noticeInfo.SetOmega(omega)

	return noticeInfo
}

func getNormalNoticeInfo(ctx context.Context, prov NoticeInfoProvider) *proto.NoticeInfo {
	var noticeInfo = &proto.NoticeInfo{}
	dcmpConf := dcmp.GetDcmpContent(ctx, miniBusNoticeInfoDcmpKey, nil)
	var dcmpInfo = &NoticeDcmpInfo{}
	err := json.Unmarshal([]byte(dcmpConf), dcmpInfo)
	if err != nil {
		return nil
	}

	if !prov.IsOnlyMinibus() {
		noticeInfo.SetRightText(dcmpInfo.RightText)
		noticeInfo.SetJumpUrl(dcmpInfo.JumpUrl)
	}

	var tagList = make([]*proto.Tag, 0)

	if "" != dcmpInfo.DistLimit {
		replace := make(map[string]string)
		replace["dist_limit"] = strconv.FormatInt(prov.GetMiniBusDistLimit(), 10)
		tagList = append(tagList, &proto.Tag{
			Content: util.ReplaceTag(ctx, dcmpInfo.DistLimit, replace),
		})
	}
	noticeInfo.TagList = tagList
	return noticeInfo
}

func getOldNoticeInfo(ctx context.Context, prov NoticeInfoProvider) *proto.NoticeInfo {
	var noticeInfo = &proto.NoticeInfo{}
	dcmpConf := dcmp.GetDcmpContent(ctx, miniBusNoticeInfoDcmpKey, nil)
	var dcmpInfo = &NoticeDcmpInfo{}
	err := json.Unmarshal([]byte(dcmpConf), dcmpInfo)
	if err != nil {
		return nil
	}

	if !prov.IsOnlyMinibus() {
		noticeInfo.SetRightText(dcmpInfo.RightText)
		noticeInfo.SetJumpUrl(dcmpInfo.JumpUrl)
	}

	var tagList = make([]*proto.Tag, 0)

	if "" != dcmpInfo.DistLimit {
		replace := make(map[string]string)
		replace["dist_limit"] = strconv.FormatInt(prov.GetMiniBusDistLimit(), 10)
		replace["etp_min"] = strconv.FormatInt(convertETSToMinutes(prov.GetEtpTimeDuration()), 10)
		tagList = append(tagList, &proto.Tag{
			Content: util.ReplaceTag(ctx, dcmpInfo.DistLimit, replace),
		})
	}
	noticeInfo.TagList = tagList
	return noticeInfo
}

func UrlAppendParams(baseUrl string, params map[string]string) string {
	parsedUrl, err := url.Parse(baseUrl)
	if err != nil {
		return ""
	}

	query := parsedUrl.Query()
	for key, value := range params {
		query.Add(key, value)
	}

	parsedUrl.RawQuery = query.Encode()

	return parsedUrl.String()
}

func NewMiniBusBuild(ctx context.Context, prov NoticeInfoProvider) bool {
	toggle := apollo.FeatureToggle(ctx, "gs_wait_minibus_open", util.ToString(prov.GetBaseReqData().PassengerInfo.PID), map[string]string{
		"phone":         util.ToString(prov.GetBaseReqData().PassengerInfo.Phone),
		"city":          util.ToString(prov.GetBaseReqData().AreaInfo.City),
		"access_key_id": util.ToString(prov.GetBaseReqData().CommonInfo.AccessKeyID),
		"app_version":   prov.GetBaseReqData().CommonInfo.AppVersion,
	})
	if toggle {
		return true
	}

	return false
}

func convertETSToMinutes(ets int64) int64 {
	// 计算总分钟数
	minutes := ets / 60

	// 如果有余数，则舍入到下一个整分钟
	if ets%60 > 0 {
		minutes++
	}

	return minutes
}
