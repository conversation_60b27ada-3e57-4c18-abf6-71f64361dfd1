package carpool_seat_module_with_children_ticket

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_seat_module_render"
)

type carpoolSeatModule struct {
	product *biz_runtime.ProductInfoFull
}

func NewCarpoolSeatModule(product *biz_runtime.ProductInfoFull) *carpoolSeatModule {
	return &carpoolSeatModule{
		product: product,
	}
}

func (c *carpoolSeatModule) GetCarpoolSeatModule(ctx context.Context, module carpool_seat_module_render.InterCityCarpoolSeatModule) *proto.InterCityCarpoolSeatModule {
	if c.product == nil {
		return nil
	}

	carpoolSeatModuleOrigin := carpool_seat_module_render.InterCityCarpool(ctx, module)

	if c.product.BaseReqData != nil && c.product.BaseReqData.CommonBizInfo.GetCarryChildTicket() != nil {
		// 携童不占座重写
		carryChildInfo := c.product.BaseReqData.CommonBizInfo.GetCarryChildTicket()
		if c.product.GetBizInfo() != nil && c.product.GetBizInfo().RouteDetail != nil && carryChildInfo.OccupySeat != 1 {
			return c.rewriteCarpoolSeatModuleOrigin(ctx, carpoolSeatModuleOrigin)
		} else {
			carpoolSeatModuleOrigin.SelectCountChildTicket = carpoolSeatModuleOrigin.SelectCount
		}
	}

	if c.product.BaseReqData != nil &&
		c.product.BaseReqData.CommonBizInfo.IsAllowHomeOwnerTicket() {
		carpoolSeatModuleOrigin.SelectCountChildTicket = carpoolSeatModuleOrigin.SelectCount
	}

	return carpoolSeatModuleOrigin
}

// childOccupySeat 判断携童是否占座
func (c *carpoolSeatModule) childOccupySeat(ctx context.Context) bool {
	if c.product == nil {
		return true
	}

	if c.product.GetBizInfo() != nil &&
		c.product.GetBizInfo().RouteDetail != nil &&
		c.product.GetBizInfo().RouteDetail.RouteExtendInfo != nil &&
		c.product.GetBizInfo().RouteDetail.RouteExtendInfo.ChildOccupySeat != nil {
		return *c.product.GetBizInfo().RouteDetail.RouteExtendInfo.ChildOccupySeat
	}

	// 默认携童占座
	return true
}

// getRemainCarryChildNum 获取携童剩余票数
func (c *carpoolSeatModule) getRemainCarryChildNum() int {
	if c.product == nil {
		return 0
	}

	var (
		remainCarryChildNum int
		err                 error
	)

	if c.product.GetBizInfo() != nil &&
		c.product.GetBizInfo().StationInventoryInfo != nil &&
		c.product.GetBizInfo().StationInventoryInfo.SelectInfo.ExtraInfo != nil {
		val, ok := c.product.GetBizInfo().StationInventoryInfo.SelectInfo.ExtraInfo["remain_carry_child_num"]
		if ok {
			remainCarryChildNum, err = strconv.Atoi(val)
			if err != nil || remainCarryChildNum <= 0 {
				return 0
			}

			return remainCarryChildNum
		}
	}

	return remainCarryChildNum
}

func (c *carpoolSeatModule) rewriteCarpoolSeatModuleOrigin(ctx context.Context, carpoolSeatModuleOrigin *proto.InterCityCarpoolSeatModule) *proto.InterCityCarpoolSeatModule {
	if carpoolSeatModuleOrigin == nil {
		return carpoolSeatModuleOrigin
	}

	remainCarryChildNum := c.getRemainCarryChildNum()
	carryChildNum := c.getCarryChildNum()

	carpoolSeatModuleOrigin.SelectCountChildTicket = carpoolSeatModuleOrigin.SelectCount + carryChildNum
	carpoolSeatModuleOrigin.MaxCount += int32(remainCarryChildNum)
	toastTpl := dcmp.GetSubContent(ctx, "inter_city_carpool-carpool_seat_module", "seats_exceed_toast")
	carpoolSeatModuleOrigin.SeatsExceedToast = dcmp.TranslateTemplate(toastTpl, map[string]string{"max_num": strconv.FormatInt(int64(carpoolSeatModuleOrigin.MaxCount), 10)})

	return carpoolSeatModuleOrigin
}

func (c *carpoolSeatModule) getCarryChildNum() int32 {
	if c.product == nil || c.product.GetBizInfo() == nil {
		return 0
	}

	for _, item := range c.product.GetBizInfo().SeatDetailInfo {
		if item.PassengerType == seat_selection_consts.CarryChildren.ToInt32() && item.PassengerCount > 0 {
			return item.PassengerCount
		}
	}

	return 0
}
