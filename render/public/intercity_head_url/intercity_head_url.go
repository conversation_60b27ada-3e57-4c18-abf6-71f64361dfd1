package intercity_head_url

import (
	"context"
	"encoding/json"
	"strconv"

	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type HeadInfoProvider interface {
	GetRouteGroup() int64
	GetAccessKeyID() int
}

const (
	INTER_CITY_CONF_BANNER_NS = "intercity_carpool_estimate_banner_conf"
)

type HeadInfo struct {
	NativePic       string `json:"native_pic"`
	NativeClickPic  string `json:"native_click_url"`
	AppletsPic      string `json:"applets_pic"`
	AppletsClickPic string `json:"applets_click_url"`
}

// 城际头图获取
func GetHeadInfo(ctx context.Context, prov HeadInfoProvider) (headImgUrl string, headDetailUrl string) {
	headImgUrl = ""
	headDetailUrl = ""
	// 兜底头图
	m := map[string]string{"group_id": strconv.Itoa(0)}
	defaultConfig, _ := apollo.GetConfigsByNamespaceAndConditions(ctx, INTER_CITY_CONF_BANNER_NS, model.NewCondition(m))
	m = map[string]string{"group_id": strconv.Itoa(int(prov.GetRouteGroup()))}
	configs, _ := apollo.GetConfigsByNamespaceAndConditions(ctx, INTER_CITY_CONF_BANNER_NS, model.NewCondition(m))
	if len(configs) == 0 {
		configs = defaultConfig
	}
	headInfos := []HeadInfo{}
	if len(configs) > 0 {
		_ = json.Unmarshal(configs, &headInfos)
	}
	if len(headInfos) > 0 {
		if prov.GetAccessKeyID() == bizCommonConsts.AccessKeyIDDiDiIos || prov.GetAccessKeyID() == bizCommonConsts.AccessKeyIDDiDiAndroid || prov.GetAccessKeyID() == bizCommonConsts.AccessKeyIDHarmonyOS {
			headDetailUrl = headInfos[0].NativeClickPic
			headImgUrl = headInfos[0].NativePic

		} else {
			headDetailUrl = headInfos[0].AppletsClickPic
			headImgUrl = headInfos[0].AppletsPic
		}
	}

	return headImgUrl, headDetailUrl

}
