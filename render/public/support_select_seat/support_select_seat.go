package support_select_seat

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

// GetSupportSelectSeat 该品类是否支持选座弹层
func GetSupportSelectSeat(ctx context.Context, product *biz_runtime.ProductInfoFull) int32 {
	if product == nil || product.BaseReqData == nil {
		return 0
	}

	if !apollo.FeatureToggle(ctx, "gs_child_ticket_switch", strconv.Itoa(int(product.BaseReqData.PassengerInfo.UID)),
		product.BaseReqData.GetApolloParam()) {
		return 0
	}

	if product.BaseReqData.CommonBizInfo.IsAllowHomeOwnerTicket() {
		return 1
	}

	if product.BaseReqData.CommonBizInfo.GetChildTicket() != nil {
		return 1
	}

	if product.BaseReqData.CommonBizInfo.GetCarryChildTicket() != nil {
		return 1
	}

	return 0
}
