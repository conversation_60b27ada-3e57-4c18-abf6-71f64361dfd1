package layout

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	layoutConst "git.xiaojukeji.com/gulfstream/mamba/render/private/anycar_v3/layout/consts"
	CommonBuilder "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/builder"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/sort_strategy"
)

type Layout struct {
	BaseReqData   *models.BaseReqData
	EstimateData  map[int64]*LayoutConsts.SimpleEstimateData
	ProductsMap   map[int64]*biz_runtime.ProductInfoFull
	SubProductMap map[int32]map[int64]*biz_runtime.ProductInfoFull
	IsUsed        map[int64]bool
	ThemeData     *proto.NewFormThemeData

	AllProductsMap map[int64]*biz_runtime.ProductInfoFull // 所有品类数据

	GuidePcIds []int32 // 导流车型
}

func NewLayout(ctx context.Context, b *models.BaseReqData, e map[int64]*LayoutConsts.SimpleEstimateData, p map[int64]*biz_runtime.ProductInfoFull) *Layout {
	r := &Layout{
		BaseReqData:    b,
		EstimateData:   e,
		ProductsMap:    p,
		SubProductMap:  make(map[int32]map[int64]*biz_runtime.ProductInfoFull),
		AllProductsMap: make(map[int64]*biz_runtime.ProductInfoFull),
	}

	// 构建theme_data
	r.buildThemeCommon(ctx)

	// 根据sub_group_id区分
	r.distinguishBySubGroupId()

	// 导流车型
	for _, product := range r.ProductsMap {
		r.GuidePcIds = product.BaseReqData.CommonBizInfo.GuidePcIds
		break
	}

	return r
}

func (l *Layout) BuildCommonLayout(ctx context.Context, layoutList []func(context.Context) CommonBuilder.IBuilder) []*proto.NewFormLayout {
	var (
		layouts     []*proto.NewFormLayout
		builderList []CommonBuilder.IBuilder
	)

	// 构建盒子
	for _, layoutItem := range layoutList {
		if iBuilder := layoutItem(ctx); iBuilder != nil {
			builderList = append(builderList, iBuilder)
		}
	}

	// 构建单车型
	for _, product := range l.ProductsMap {
		builderList = append(builderList, CommonBuilder.NewSingleBuilder(product, l.BaseReqData.CommonBizInfo.TopRec, l.BaseReqData, l.ThemeData))
	}

	productSort := l.BaseReqData.CommonBizInfo.ProductSortInfo

	if productSort != nil && productSort.SortType != nil && *productSort.SortType == consts.PriceSortStrategy {
		// 价格排序
		layouts = sort_strategy.NewPriceSort(ctx, l.BaseReqData, l.AllProductsMap).BuildLayoutSort(ctx, builderList)
	} else {
		// 默认排序
		layouts = sort_strategy.NewDefaultSort(ctx, l.BaseReqData).BuildLayoutSort(ctx, builderList)
	}

	// 返回
	return layouts
}

// GetGuiderLayoutBuilder 导流样式
func (l *Layout) GetGuiderLayoutBuilder(ctx context.Context) CommonBuilder.IBuilder {
	var (
		hitBargain  bool
		bargainItem *biz_runtime.ProductInfoFull
	)

	if len(l.GuidePcIds) == 0 {
		return nil
	}

	productItem, ok := l.ProductsMap[int64(l.GuidePcIds[0])]
	if !ok {
		return nil
	}

	// 看起来下面是优先司乘议价
	if util.InArrayInt32(estimate_pc_id.EstimatePcIdBargain, l.GuidePcIds) {
		hitBargain = true
		if bargain, exist := l.ProductsMap[estimate_pc_id.EstimatePcIdBargain]; exist {
			bargainItem = bargain
		}
	}

	// 导流样式中如果Athena返回了司乘议价，则优先渲染司乘议价
	var b CommonBuilder.IBuilder
	if hitBargain {
		b = CommonBuilder.NewBargainBuilder(ctx, bargainItem, l.EstimateData, l.BaseReqData, l.ThemeData)
	} else {
		b = CommonBuilder.NewGuideCarBuilder(l.GuidePcIds[0], productItem, l.EstimateData, l.BaseReqData, l.ThemeData)
	}

	// 删除导流品类 不再单品类构建
	for _, pcID := range l.GuidePcIds {
		delete(l.ProductsMap, int64(pcID))
	}
	return b
}

// GetdUnitaxiBoxBuilder 出租车盒子
func (l *Layout) GetdUnitaxiBoxBuilder(ctx context.Context) CommonBuilder.IBuilder {
	return CommonBuilder.NewUniTaxiBoxBuilder(l.SubProductMap[consts.SubGroupIdUnitaxi], l.EstimateData, l.BaseReqData)
}

// GetTaxiPricingBoxBuilder 出租车计价盒子
func (l *Layout) GetTaxiPricingBoxBuilder(ctx context.Context) CommonBuilder.IBuilder {
	return CommonBuilder.NewTaxiPricingBoxBuilder(l.SubProductMap[consts.SubGroupIdTaxiPricingBox], l.EstimateData, l.BaseReqData)
}

func (l *Layout) GetBargainNormalBuilder(ctx context.Context) CommonBuilder.IBuilder {

	if len(l.ProductsMap) <= 0 || l.ProductsMap[estimate_pc_id.EstimatePcIdBargain] == nil {
		return nil
	}
	if util.InArrayInt32(estimate_pc_id.EstimatePcIdBargain, l.GuidePcIds) {
		//自选车在导流位不渲染
		return nil
	}
	p := l.ProductsMap[estimate_pc_id.EstimatePcIdBargain]
	delete(l.ProductsMap, int64(estimate_pc_id.EstimatePcIdBargain))

	// 司乘议价
	return CommonBuilder.NewBargainNormalBuilder(p, l.BaseReqData.CommonBizInfo.TopRec, l.BaseReqData, l.ThemeData)
}

// GetShortDistanceCarAllianceBuilder 三方盒子
func (l *Layout) GetShortDistanceCarAllianceBuilder(ctx context.Context) CommonBuilder.IBuilder {
	return CommonBuilder.NewThirtyBuilder(l.SubProductMap[consts.SubGroupIdShortDistance], l.EstimateData, l.BaseReqData)
}

// GetSpaciousCarLayoutBuilder 车大
func (l *Layout) GetSpaciousCarLayoutBuilder(ctx context.Context) CommonBuilder.IBuilder {
	fastItem, ok1 := l.ProductsMap[estimate_pc_id.EstimatePcIdFastCar]
	spacious, ok2 := l.ProductsMap[estimate_pc_id.EstimatePcIdSpaciousCar]
	if !ok1 || !ok2 {
		return nil
	}

	if fastItem == nil || spacious == nil {
		return nil
	}

	// 删除车大 不再单品类构建
	delete(l.ProductsMap, int64(estimate_pc_id.EstimatePcIdSpaciousCar))
	// 删除快车 不再单品类构建
	delete(l.ProductsMap, int64(estimate_pc_id.EstimatePcIdFastCar))

	return CommonBuilder.NewSpaciousBuilder(spacious, fastItem, l.BaseReqData, l.EstimateData, l.ThemeData)
}

// GetFarMustCheapLayoutBuilder 远必省
func (l *Layout) GetFarMustCheapLayoutBuilder(ctx context.Context) CommonBuilder.IBuilder {
	return CommonBuilder.NewFarMustCheapBuilder(l.SubProductMap[consts.SubGroupIdFarMustCheap], l.EstimateData, l.BaseReqData)
}

// GetWellWorthPriceLayoutBuilder 超值达
func (l *Layout) GetWellWorthPriceLayoutBuilder(ctx context.Context) CommonBuilder.IBuilder {
	return CommonBuilder.NewWellWorthPriceBuilder(l.SubProductMap[consts.SubGroupIdChaoZhiDa], l.EstimateData, l.BaseReqData)
}

func (l *Layout) GetDiscountAllianceBuilder(ctx context.Context) CommonBuilder.IBuilder {
	return CommonBuilder.NewDiscountAllianceBuilder(l.SubProductMap[consts.SubGroupIdDiscountAlliance], l.EstimateData, l.BaseReqData)
}

// GetHongKongThirtyNormalBuilder 香港三方-经济型盒子
func (l *Layout) GetHongKongThirtyNormalBuilder(ctx context.Context) CommonBuilder.IBuilder {
	l._buildHongKongThirtyBoxProductsMap(ctx, consts.SubGroupIdHKThirtyNormal)
	return CommonBuilder.NewHongKongThirtyBuilder(consts.SubGroupIdHKThirtyNormal, l.SubProductMap[consts.SubGroupIdHKThirtyNormal], l.EstimateData, l.BaseReqData)
}

// GetHongKongThirtyComfortBuilder 香港三方盒子
func (l *Layout) GetHongKongThirtyComfortBuilder(ctx context.Context) CommonBuilder.IBuilder {
	l._buildHongKongThirtyBoxProductsMap(ctx, consts.SubGroupIdHKThirtyComfort)
	return CommonBuilder.NewHongKongThirtyBuilder(consts.SubGroupIdHKThirtyComfort, l.SubProductMap[consts.SubGroupIdHKThirtyComfort], l.EstimateData, l.BaseReqData)
}

func (l *Layout) _buildHongKongThirtyBoxProductsMap(ctx context.Context, subGroupID int32) {
	pcIDToInfo, ok := l.SubProductMap[subGroupID]
	if !ok || len(pcIDToInfo) <= 0 {
		return
	}

	// 香港盒子条件
	allow := apollo.FeatureToggle(ctx, "hk_third_box_toggle",
		cast.ToString(l.BaseReqData.PassengerInfo.PID), map[string]string{
			"sub_group_id":  cast.ToString(subGroupID),
			"fn":            "append_order",
			"order_type":    cast.ToString(l.BaseReqData.CommonInfo.OrderType),
			"product_count": cast.ToString(len(pcIDToInfo)),
		})
	if allow {
		return
	}

	// 不进盒子
	for pcID, info := range pcIDToInfo {
		l.ProductsMap[pcID] = info
	}
	l.SubProductMap[subGroupID] = make(map[int64]*biz_runtime.ProductInfoFull)
}

// distinguishBySubGroupId 通过sub_group_id区分聚合成型
func (l *Layout) distinguishBySubGroupId() {
	for pcId, item := range l.ProductsMap {
		l.AllProductsMap[pcId] = item
		if item.Product.SubGroupId == 0 {
			continue
		}

		if box, ok := l.SubProductMap[item.Product.SubGroupId]; ok {
			if len(box) == 0 {
				box = make(map[int64]*biz_runtime.ProductInfoFull)
			}
			box[pcId] = item
		} else {
			l.SubProductMap[item.Product.SubGroupId] = make(map[int64]*biz_runtime.ProductInfoFull)
			l.SubProductMap[item.Product.SubGroupId][pcId] = item
		}

		delete(l.ProductsMap, pcId)
	}
}

func (l *Layout) buildThemeCommon(ctx context.Context) {
	var (
		themeStyle int32
		title      string
		rightText  string
		themeData  *proto.NewFormThemeData
	)

	if l.BaseReqData == nil || l.BaseReqData.CommonBizInfo.TopData == nil ||
		l.BaseReqData.CommonBizInfo.TopRec == nil ||
		len(l.BaseReqData.CommonBizInfo.TopRec.Text) == 0 {
		return
	}

	themeStyle = l.BaseReqData.CommonBizInfo.TopRec.Style
	title = l.BaseReqData.CommonBizInfo.TopRec.Text
	if l.BaseReqData.CommonBizInfo.TopRec.SubText != nil {
		rightText = *l.BaseReqData.CommonBizInfo.TopRec.SubText
	}

	apolloParam := l.BaseReqData.GetApolloParam()
	enableNormativeTheme := apollo.FeatureToggle(ctx, "enable_formative_theme", "", apolloParam)
	switch themeStyle {
	case consts.ThemeStyleNormative:
		themeType := int32(consts.ThemeTypeNormative)
		if enableNormativeTheme {
			themeData = &proto.NewFormThemeData{
				Title:     &title,
				RightText: &rightText,
				ThemeType: &themeType,
			}
		}
	case consts.ThemeStyleDefaultSelectedCompensation:
		themeData = l.buildThemeCompensation(ctx)
	default:
		themeData = l.buildThemeCommonOld(ctx)
	}

	if themeData != nil && enableNormativeTheme {
		themeData = l.normalizeTheme(ctx, themeData)
	}

	l.ThemeData = themeData
}

func (l *Layout) buildThemeCommonOld(ctx context.Context) *proto.NewFormThemeData {
	var (
		isBargainRec bool
	)
	if l.BaseReqData.CommonBizInfo.TopRec.Style == 0 {
		return nil
	}

	bargainSceneRec, ok := l.BaseReqData.CommonBizInfo.TopRec.ExtraInfo["bargain_scene_recommend"]
	if ok && bargainSceneRec == "1" {
		isBargainRec = true
	}

	if isBargainRec {
		return l.buildBargainTheme(ctx)
	}

	if util.InArrayInt32(l.BaseReqData.CommonBizInfo.TopRec.Style, []int32{
		consts.ThemeStyleUnbalance,
		consts.ThemeStylePreferentialMarketing,
		consts.ThemeStylePreferentialRecommendMarketing,
	}) {
		themeData := l.buildThemeStyle(ctx)
		if themeData != nil {
			return themeData
		}
	}

	dialogID, ok := l.BaseReqData.CommonBizInfo.TopRec.ExtraInfo["dialog_id"]
	if ok && dialogID != "" {
		themeData := l.buildThemeByDialogId(ctx)
		if themeData != nil {
			return themeData
		}
	}

	return l.buildThemeFinal(ctx)
}

func (l *Layout) buildThemeCompensation(ctx context.Context) *proto.NewFormThemeData {
	defaultSelectedCompensation := l.BaseReqData.CommonBizInfo.DefaultSelectedCompensation
	if defaultSelectedCompensation == nil || defaultSelectedCompensation.Contents == nil {
		return nil
	}

	tags := map[string]string{
		"compensation_time": l.BaseReqData.CommonBizInfo.DefaultSelectedCompensation.Contents.CompensationTimeStr,
		"amount":            util.FormatPriceFloor(float64(l.BaseReqData.CommonBizInfo.DefaultSelectedCompensation.Contents.CouponAmount)/float64(100), 1),
	}

	return &proto.NewFormThemeData{
		Title:     util.StringPtr(dcmp.GetDcmpContent(ctx, "estimate_form_v3-compensation_theme_title", tags)),
		ThemeType: &l.BaseReqData.CommonBizInfo.TopRec.Style,
	}
}

func (l *Layout) normalizeTheme(ctx context.Context, themeData *proto.NewFormThemeData) *proto.NewFormThemeData {
	if *themeData.ThemeType == consts.ThemeTypeMarketing {
		return themeData
	}

	var normalizedThemeData = &proto.NewFormThemeData{}
	normalizedThemeData.Title = themeData.Title
	if *themeData.ThemeType == consts.ThemeTypeCountDown {
		normalizedThemeData.RightText = themeData.RightText
		normalizedThemeData.ExpireTime = themeData.ExpireTime
	}

	conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-normative_theme", util.ToString(*themeData.ThemeType))
	if len(conf) <= 0 {
		return nil
	}

	var bgGradients []string
	themeType := util.ToInt32(consts.ThemeTypeNormative)
	textColor := conf["text_color"].String()
	borderColor := conf["border_color"].String()
	disableBg := util.ToInt64(conf["disable_selected_bg"].Int())
	for _, bgGradient := range conf["outer_bg_gradients"].Array() {
		bgGradients = append(bgGradients, bgGradient.String())
	}
	leftIcon := conf["left_icon"].String()

	normalizedThemeData.ThemeType = &themeType
	normalizedThemeData.TextColor = &textColor
	normalizedThemeData.BorderColor = &borderColor
	normalizedThemeData.DisableSelectedBg = &disableBg
	normalizedThemeData.LeftIcon = &leftIcon
	normalizedThemeData.OuterBgGradients = bgGradients
	return normalizedThemeData
}

func (l *Layout) buildBargainTheme(ctx context.Context) *proto.NewFormThemeData {
	var (
		title       string
		icon        string
		rightIcon   string
		bgGradients = make([]string, 0)
		themeType   int32
	)

	themeType = consts.ThemeTypeBargain
	// 兜底使用dcmp中的文案
	if conf := dcmp.GetJSONMap(ctx, "bargain-estimate_form_guide_info_v2", "outer"); len(conf) > 0 {
		title = conf["title"].String()
		icon = conf["left_icon"].String()
		rightIcon = conf["right_icon"].String()
		for _, bgGradient := range conf["bg_gradients"].Array() {
			bgGradients = append(bgGradients, bgGradient.String())
		}
	}
	// 使用athena的文案进行覆盖
	topRec := l.BaseReqData.CommonBizInfo.TopRec
	if topRec.Text != "" {
		title = topRec.Text
	}

	iconLeft, ok := topRec.ExtraInfo["icon_left"]
	if ok && iconLeft != "" {
		icon = iconLeft
	}

	iconRight, ok := topRec.ExtraInfo["icon_right"]
	if ok && iconRight != "" {
		rightIcon = iconRight
	}

	return &proto.NewFormThemeData{
		Title:            &title,
		Icon:             &icon,
		RightIcon:        &rightIcon,
		OuterBgGradients: bgGradients,
		ThemeType:        &themeType,
	}
}

func (l *Layout) buildThemeStyle(ctx context.Context) *proto.NewFormThemeData {
	var (
		style             int32
		dialogID          int32
		themeType         int32
		selectBgGradients []string
		outBgGradients    []string
		themeData         = &proto.NewFormThemeData{}
	)

	topRec := l.BaseReqData.CommonBizInfo.TopRec
	style = topRec.Style
	dialogIDStr, ok := topRec.ExtraInfo["dialog_id"]
	if ok {
		dialogID = util.ToInt32(dialogIDStr)
	}

	confIndex := fmt.Sprintf("%d_%d", style, dialogID)
	conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-dialog_theme", confIndex)
	if len(conf) == 0 {
		return nil
	}

	rightIcon := conf["right_icon"].String()
	themeColor := conf["theme_color"].String()
	for _, selectBgGradient := range conf["selected_bg_gradients"].Array() {
		selectBgGradients = append(selectBgGradients, selectBgGradient.String())
	}
	for _, outBgGradient := range conf["out_bg_gradients"].Array() {
		outBgGradients = append(outBgGradients, outBgGradient.String())
	}

	themeData = &proto.NewFormThemeData{
		Title:               &topRec.Text,
		Icon:                &topRec.Icon,
		RightIcon:           &rightIcon,
		ThemeColor:          &themeColor,
		SelectedBgGradients: selectBgGradients,
		OuterBgGradients:    outBgGradients,
	}

	switch style {
	case consts.ThemeStyleUnbalance:
	case consts.ThemeStylePreferentialMarketing:
		themeType = 1
		break
	case consts.ThemeStylePreferentialRecommendMarketing:
		themeType = consts.ThemeTypeMarketing
		break
	}

	// 营销主题屏蔽athena返回的默认text
	if themeType == consts.ThemeTypeMarketing {
		emptyText := ""
		themeData.Title = &emptyText
	}

	if themeType == 0 {
		return nil
	}

	themeData.ThemeType = &themeType

	return themeData
}

func (l *Layout) buildThemeByDialogId(ctx context.Context) *proto.NewFormThemeData {
	if l.BaseReqData.CommonBizInfo.TopRec == nil || l.BaseReqData.CommonBizInfo.TopRec.ExtraInfo == nil {
		return nil
	}

	dialogId, ok := l.BaseReqData.CommonBizInfo.TopRec.ExtraInfo["dialog_id"]
	if !ok {
		return nil
	}

	conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-dialog_theme", dialogId)
	if len(conf) <= 0 {
		return nil
	}

	var (
		themeData         *proto.NewFormThemeData
		selectBgGradients []string
		outBgGradients    []string
	)
	topRec := l.BaseReqData.CommonBizInfo.TopRec
	themeColor := conf["theme_color"].String()
	for _, selectBgGradient := range conf["selected_bg_gradients"].Array() {
		selectBgGradients = append(selectBgGradients, selectBgGradient.String())
	}
	for _, outBgGradient := range conf["out_bg_gradients"].Array() {
		outBgGradients = append(outBgGradients, outBgGradient.String())
	}
	switch topRec.Style {
	// 0是无样式  默认1  自选车2
	case 6: // 倒计时样式
		themeData = &proto.NewFormThemeData{
			Title:               &topRec.Text,
			Icon:                &topRec.Icon,
			ThemeColor:          &themeColor,
			SelectedBgGradients: selectBgGradients,
			OuterBgGradients:    outBgGradients,
			ThemeType:           &topRec.Style,
		}
		expireTime, ok1 := topRec.ExtraInfo["expire_time"]
		if ok1 && expireTime != "" && *topRec.SubText != "" {
			layout := "2006-01-02 15:04:05"
			parsedTime, err := time.ParseInLocation(layout, expireTime, time.Local)
			if err == nil {
				expireTmp := parsedTime.Unix()
				timeDiff := expireTmp - time.Now().Unix()
				if timeDiff > 0 {
					themeData.ExpireTime = &timeDiff
					themeData.RightText = topRec.SubText
				}
			}
		}
		return themeData
	default:
		return nil
	}
}

func (l *Layout) buildThemeFinal(ctx context.Context) *proto.NewFormThemeData {
	// 渲染置顶样式
	var (
		selectedBgGradients layoutConst.BgGradients
		outerBgGradients    layoutConst.BgGradients
		defaultThemeType    int32
		tag                 = "buildThemeCommon"
	)
	config := apollo.GetConfig(ctx, layoutConst.PeakGroupBoxNamespace, layoutConst.NewGroupBox)

	themeColor := config["theme_color"]
	if val, ok := config["selected_bg_gradients"]; ok {
		err := json.Unmarshal([]byte(val), &selectedBgGradients)
		if err != nil {
			log.Trace.Warnf(ctx, tag, "selected_bg_gradients unmarshal fail, err:%v", err)
		}
	}
	if val, ok := config["out_bg_gradients"]; ok {
		err := json.Unmarshal([]byte(val), &outerBgGradients)
		if err != nil {
			log.Trace.Warnf(ctx, tag, "out_bg_gradients unmarshal fail, err:%v", err)
		}
	}

	topRec := l.BaseReqData.CommonBizInfo.TopRec

	defaultThemeType = layoutConst.DefaultThemeType

	return &proto.NewFormThemeData{
		Title:      &(topRec.Text),
		Icon:       &(topRec.Icon),
		RightText:  topRec.SubText,
		RightIcon:  topRec.SubIcon,
		ThemeColor: &themeColor,
		SelectedBgGradients: []string{
			selectedBgGradients.StartColor,
			selectedBgGradients.EndColor,
		},
		ThemeType: &defaultThemeType,
		OuterBgGradients: []string{
			outerBgGradients.StartColor,
			outerBgGradients.EndColor,
		},
	}
}
