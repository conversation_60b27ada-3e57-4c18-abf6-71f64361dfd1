package builder

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	BizConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	feeEngineConsts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	layoutConst "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
)

type IBuilder interface {

	// Access builder准入
	Access() bool

	GetName() string
	PreBuild(context.Context)
	BuildGroups(context.Context) []*proto.NewFormGroup
	BuildFormShowType(context.Context) int32
	BuildTheme(context.Context) *proto.NewFormThemeData
	BuildWeight(context.Context) int64
}

type baseBuilder struct {
	weight       int64
	formShowType int32
	groups       []*proto.NewFormGroup
	reqData      *models.BaseReqData
	estimateData map[int64]*layoutConst.SimpleEstimateData
	themeData    *proto.NewFormThemeData
}

func (b *baseBuilder) getCityId() int32 {
	return b.reqData.AreaInfo.Area
}

func (b *baseBuilder) athenaRecommendAreaToFormShowType(recPos int32) int32 {
	if consts.RecPosRecommendArea == recPos {
		return int32(consts.RecommendArea)
	} else if consts.RecPosTopArea == recPos {
		return int32(consts.TopArea)
	} else {
		return int32(consts.OtherArea)
	}
}

func (b *baseBuilder) BuildGroupID(athenaGroupType int, productCategory int64) string {
	return fmt.Sprintf("%d_%d", athenaGroupType, productCategory)
}

func (b *baseBuilder) buildCommonWeight(ctx context.Context) {
	weightMap := b.getWeightConfig(ctx)

	switch b.formShowType {
	case consts.TopArea:
		b.weight += consts.HighWeight
		break
	case consts.RecommendArea:
		b.weight += consts.MidWeight
		break
	case consts.OtherArea:
		b.weight += consts.LowWeight
		break
	default:
		b.weight += consts.LowWeight
	}

	for _, group := range b.groups {
		if group.Type == consts.GuideGroupType2 {
			b.weight += consts.MidThousandWeight
		} else if group.Type == consts.GuideGroupType {
			b.weight += consts.LowThousandWeight
		}

		if b.buildBubbleRank(ctx) && group.IsSelected == consts.CHECK {
			b.weight += consts.LowThousandWeight
		}

		if len(group.Products) != 0 {
			if w, ok := weightMap[group.Products[0]]; ok {
				b.weight += w
			}
		}
	}
}

func (b *baseBuilder) getWeightConfig(ctx context.Context) map[string]int64 {
	var (
		tag     = "getWeightConfig"
		carSort []*consts.CarSortConf
		weights map[string]int64
	)

	weights = make(map[string]int64)
	configs, err := apollo.GetConfigsByNamespace(ctx, consts.CarSortNamespace)
	if err != nil {
		log.Trace.Warnf(ctx, tag, "get config fail with err %v", err)
		return weights
	}

	err = json.Unmarshal(configs, &carSort)
	if err != nil {
		log.Trace.Warnf(ctx, tag, "unmarshal sorted list conf fail with err %v", err)
		return weights
	}

	if carSort == nil || len(carSort) == 0 || len(carSort[0].ProductsSort) == 0 {
		log.Trace.Warnf(ctx, tag, "car sort is nil")
		return weights
	}

	num := len(carSort[0].ProductsSort)
	for index, p := range carSort[0].ProductsSort {
		weights[strconv.Itoa(p.ProductCategory)] = int64(consts.LowHundredWeight + (num - index))
	}

	return weights
}

func (b *baseBuilder) buildBubbleRank(ctx context.Context) bool {
	var (
		params map[string]string
	)

	params = map[string]string{
		"key":           fmt.Sprintf("%d", b.reqData.PassengerInfo.UID),
		"phone":         b.reqData.PassengerInfo.Phone,
		"city":          fmt.Sprintf("%d", b.reqData.AreaInfo.City),
		"pid":           fmt.Sprintf("%d", b.reqData.PassengerInfo.PID),
		"access_key_id": fmt.Sprintf("%d", b.reqData.CommonInfo.AccessKeyID),
		"app_version":   b.reqData.CommonInfo.AppVersion,
		"lang":          b.reqData.CommonInfo.Lang,
	}

	apolloRes, assigment := apollo.FeatureExp(ctx, consts.WYCBubbleRank, "", params)
	if apolloRes {
		if assigment.GetGroupName() == consts.TreatmentGroup {
			return true
		} else if assigment.GetGroupName() == consts.ControlGroup {
			return false
		}
	}

	return true
}

func (b *baseBuilder) getFeeDescList(ctx context.Context, checkPCIDs []int64, feeTypes []int32) []*proto.NewFormFeeDesc {
	var (
		res     = make([]*proto.NewFormFeeDesc, 0)
		descMax = make(map[int32]*proto.NewFormFeeDesc)
	)

	for _, pcID := range checkPCIDs {
		if len(b.estimateData) == 0 {
			return res
		}
		if estimateData, ok := b.estimateData[pcID]; ok && estimateData != nil {
			feeDescMaxList := b.distinguishFeeDesc(estimateData.FeeDescList)
			for feeType, curDescMax := range feeDescMaxList {
				if descMax[feeType] != nil {
					if curDescMax.Amount > descMax[feeType].Amount {
						descMax[feeType] = curDescMax
					}
				} else {
					descMax[feeType] = curDescMax
				}
			}
		}
	}

	for _, feeType := range feeTypes {
		if Max := descMax[feeType]; Max != nil && len(res) < 2 {
			if len(checkPCIDs) > 1 {
				Max = b.renderRewrite(ctx, Max, feeType)
			}

			res = append(res, Max)
		}
	}

	return res
}

func (b *baseBuilder) distinguishFeeDesc(feeDescList []*proto.NewFormFeeDesc) map[int32]*proto.NewFormFeeDesc {
	res := make(map[int32]*proto.NewFormFeeDesc)

	for _, feeDesc := range feeDescList {
		if max, ok := res[feeDesc.Type]; ok {
			if max.Amount < feeDesc.Amount {
				res[feeDesc.Type] = feeDesc
			}
		} else {
			res[feeDesc.Type] = feeDesc
		}
	}

	return res
}

func getFeeDescFromDcmp(ctx context.Context, key, path string, tag map[string]string) *proto.NewFormFeeDesc {
	if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
		return &proto.NewFormFeeDesc{
			Icon:           conf["icon"].String(),
			Content:        util.ReplaceTag(ctx, conf["content"].String(), tag),
			BorderColor:    conf["border_color"].String(),
			TextColor:      util.StringPtr(conf["border_color"].String()),
			BgColor:        util.StringPtr(conf["bg_color"].String()),
			HighlightColor: util.StringPtr(conf["highlight_color"].String()),
		}
	}
	return nil
}

func (b *baseBuilder) renderRewrite(ctx context.Context, max *proto.NewFormFeeDesc, feeType int32) *proto.NewFormFeeDesc {
	path := ""
	tag := map[string]string{
		"num": util.FormatPrice(max.Amount, 2),
	}

	if feeType == feeEngineConsts.TypeBusinessPay {
		path = feeEngineConsts.FeeBusinessMaxAnyCar
	} else if feeType == feeEngineConsts.TypeIncrement {
		path = feeEngineConsts.FeeIncrMaxAnyCar
	} else if feeType == feeEngineConsts.TypeDecrement {
		path = feeEngineConsts.FeeDecrMaxAnyCar
	} else if feeType == feeEngineConsts.TypeDynamic {
		path = feeEngineConsts.FeeDynamicMaxAnyCar
	} else {
		return max
	}

	if conf := dcmp.GetJSONMap(ctx, feeEngineConsts.DynamicBoxFeeDetailInfo, path); len(conf) > 0 {
		// 如果是出租车动调信息则只需要替换icon
		if path == feeEngineConsts.FeeDynamicMaxAnyCar {
			return &proto.NewFormFeeDesc{
				BorderColor:    max.BorderColor,
				Content:        max.Content,
				Icon:           conf["icon"].String(),
				Amount:         max.Amount,
				Type:           max.Type,
				TextColor:      max.TextColor,
				BgColor:        max.BgColor,
				HighlightColor: max.HighlightColor,
			}
		}
		return &proto.NewFormFeeDesc{
			BorderColor: conf["border_color"].String(),
			Content:     util.ReplaceTag(ctx, conf["content"].String(), tag),
			Icon:        conf["icon"].String(),
		}
	}
	return nil
}

// GetThemeDataSingleCar 等8.5上线再用
func (b *baseBuilder) GetThemeDataSingleCar(ctx context.Context, pcID int64) *proto.NewFormThemeData {
	var (
		topList []int64
	)

	if b.reqData == nil || b.reqData.CommonBizInfo.TopData == nil ||
		b.reqData.CommonBizInfo.TopRec == nil ||
		len(b.reqData.CommonBizInfo.TopRec.Text) == 0 {
		return nil
	}

	topList = b.reqData.CommonBizInfo.TopData.TopPcIdList

	if !util.InArrayInt64(pcID, topList) {
		return nil
	}

	return b.themeData
}

func (b *baseBuilder) GetDisableShadow(ctx context.Context) bool {
	if b.themeData != nil && b.themeData.ThemeType != nil {
		return *b.themeData.ThemeType == BizConsts.ThemeTypeNormative
	}

	return false
}

func (b *baseBuilder) showHolidayFeeDesc(ctx context.Context, product *biz_runtime.ProductInfoFull) bool {
	apolloKey, apolloParam := product.GetApolloParams(biz_runtime.WithPIDKey)
	apolloParam["page_type"] = util.ToString(b.reqData.CommonInfo.PageType)
	apolloParam["order_type"] = util.ToString(b.reqData.CommonInfo.OrderType)
	isAllow := apollo.FeatureToggle(ctx, "gs_holiday_fee_fee_desc", apolloKey, apolloParam)
	return isAllow
}

func (b *baseBuilder) isBoxSubTitleShowPrice(ctx context.Context, product *biz_runtime.ProductInfoFull) bool {
	apolloKey, apolloParam := product.GetApolloParams(biz_runtime.WithUIDKey)
	return apollo.FeatureToggle(ctx, "box_sub_title_show_price", apolloKey, apolloParam)
}
