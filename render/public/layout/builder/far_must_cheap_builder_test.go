package builder

import (
	"context"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_carpool"
	"github.com/agiledragon/gomonkey/v2"
)

func TestFarMustCheapBuilder_getBoxDesc(t *testing.T) {
	type fields struct {
		baseBuilder  baseBuilder
		subProjects  map[int64]*biz_runtime.ProductInfoFull
		pcIDs        []string
		iPcIDs       []int64
		currencyUnit string
		isSelected   int32
		recPos       int32
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *string
	}{
		// TODO: Add test cases.
		{
			name: "a",
			fields: fields{
				baseBuilder: baseBuilder{
					reqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							FontScaleType: 0,
						},
					},
				},
			},
		},
		{
			name: "b",
			fields: fields{
				baseBuilder: baseBuilder{
					reqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							FontScaleType: 2,
						},
					},
				},
			},
		},
	}

	patch := gomonkey.NewPatches()
	defer patch.Reset() // 测试结束后自动恢复

	patch.ApplyFuncReturn(category_carpool.GetDualCarpoolFeeDescListV3, []*proto.NewFormFeeDesc{})
	patch.ApplyFuncReturn(dcmp.GetJSONContentWithPath, "")

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := &FarMustCheapBuilder{
				baseBuilder:  tt.fields.baseBuilder,
				subProjects:  tt.fields.subProjects,
				pcIDs:        tt.fields.pcIDs,
				iPcIDs:       tt.fields.iPcIDs,
				currencyUnit: tt.fields.currencyUnit,
				isSelected:   tt.fields.isSelected,
				recPos:       tt.fields.recPos,
			}
			f.getBoxDesc(tt.args.ctx)
		})
	}
}
