package builder

import (
	"context"
	"reflect"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/tidwall/gjson"
)

func TestThirtyBuilder_BuildGroups(t *testing.T) {
	type fields struct {
		baseBuilder     baseBuilder
		subProjects     map[int64]*biz_runtime.ProductInfoFull
		pcIDs           []string
		checkPCIDs      []int64
		hasDiscount     bool
		hasRedPacketFee bool
		currencyUnit    string
		redPackFee      float64
		hasSpCar        bool
		hasSelfOperate  bool
		isSelected      int32
		recPos          int32
		conf            map[string]gjson.Result
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*proto.NewFormGroup
	}{
		// TODO: Add test cases.
		{
			name: "a",
			fields: fields{
				subProjects: map[int64]*biz_runtime.ProductInfoFull{
					1: &biz_runtime.ProductInfoFull{},
				},
				baseBuilder: baseBuilder{
					reqData: &models.BaseReqData{},
				},
			},
		},
		{
			name: "b",
			fields: fields{
				subProjects: map[int64]*biz_runtime.ProductInfoFull{
					1: &biz_runtime.ProductInfoFull{},
				},
				baseBuilder: baseBuilder{
					reqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							FontScaleType: 1,
						},
					},
				},
			},
		},
	}

	patch := gomonkey.NewPatches()
	defer patch.Reset() // 测试结束后自动恢复

	patch.ApplyFuncReturn(apollo.FeatureToggle, true)
	a := "a"
	patch.ApplyFuncReturn(dcmp.GetStringDcmpContentByPath, &a)

	patch.ApplyMethodReturn(&biz_runtime.ProductInfoFull{}, "GetApolloParams", "", map[string]string{})
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tr := &ThirtyBuilder{
				baseBuilder:     tt.fields.baseBuilder,
				subProjects:     tt.fields.subProjects,
				pcIDs:           tt.fields.pcIDs,
				checkPCIDs:      tt.fields.checkPCIDs,
				hasDiscount:     tt.fields.hasDiscount,
				hasRedPacketFee: tt.fields.hasRedPacketFee,
				currencyUnit:    tt.fields.currencyUnit,
				redPackFee:      tt.fields.redPackFee,
				hasSpCar:        tt.fields.hasSpCar,
				hasSelfOperate:  tt.fields.hasSelfOperate,
				isSelected:      tt.fields.isSelected,
				recPos:          tt.fields.recPos,
				conf:            tt.fields.conf,
			}

			if got := tr.BuildGroups(tt.args.ctx); !reflect.DeepEqual(got, tt.want) {
			}
		})
	}
}
