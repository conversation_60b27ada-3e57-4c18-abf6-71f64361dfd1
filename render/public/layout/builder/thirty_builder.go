package builder

import (
	"context"
	"fmt"
	"strconv"

	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"github.com/tidwall/gjson"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"

	commonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	fee_consts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic"
	LayoutUtil "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/builder/util"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
)

const thirdPartyLayoutInfoDcmpkey = "anycar_estimate-sp_alliance_all"

type ThirtyBuilder struct {
	baseBuilder
	subProjects     map[int64]*biz_runtime.ProductInfoFull
	pcIDs           []string
	checkPCIDs      []int64
	hasDiscount     bool // 是否有优惠
	hasRedPacketFee bool // 是否包含红包费
	currencyUnit    string
	redPackFee      float64 // 红包费
	hasSpCar        bool    // 是否包含特惠快车
	hasSelfOperate  bool    // 是否包含小猪自营
	isSelected      int32   // 是否存在被选中
	recPos          int32
	conf            map[string]gjson.Result
}

func NewThirtyBuilder(subProjects map[int64]*biz_runtime.ProductInfoFull, estimateData map[int64]*LayoutConsts.SimpleEstimateData, data *models.BaseReqData) IBuilder {
	return &ThirtyBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateData,
		},
		subProjects: subProjects,
	}
}

func (t *ThirtyBuilder) GetName() string {
	return "ThirtyBuilder"
}

func (t *ThirtyBuilder) Access() bool {
	if len(t.subProjects) == 0 {
		return false
	}

	return true
}

func (t *ThirtyBuilder) PreBuild(ctx context.Context) {
	t.setBaseData(ctx)
	t.setRecPos()
}

func (t *ThirtyBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups      []*proto.NewFormGroup
		productItem *biz_runtime.ProductInfoFull
	)
	group := &proto.NewFormGroup{
		Products:           t.pcIDs,
		Type:               int32(consts.ShortDistanceGroupType),
		GroupId:            t.BuildGroupID(commonConsts.ShortDistanceType, commonConsts.SubGroupIdShortDistance),
		CarIcon:            *dcmp.GetStringDcmpContentByPath(t.conf, "car_icon"),
		CarTitle:           *dcmp.GetStringDcmpContentByPath(t.conf, "car_title"),
		PopupTitle:         t.getPopupTitle(ctx),
		UnselectPopupTitle: dcmp.GetStringDcmpContentByPath(t.conf, "unselect_popup_title"),
		FeeDescIcon:        t.getFeeDescIcon(ctx),
		IsSelected:         t.isSelected,
		FeeDesc:            t.getFeeDesc(ctx),
		FeeDescList:        t.getFeeDescList(ctx, t.checkPCIDs, []int32{fee_consts.TypeDynamic, fee_consts.TypeIncrement, fee_consts.TypeDecrement, fee_consts.TypeBusinessPay, fee_consts.TypeZiyoubao}),
	}
	params := t.reqData.GetApolloParam()
	if apollo.FeatureToggle(ctx, "build_box_desc", params["phone"], params) {
		group.PopupTitle = t.getPopupSubTitle(ctx, "popup_title_new")
		// 如果是大字版，下发查看详情
		if t.reqData.CommonInfo.FontScaleType != commonConsts.NormalFontSize {
			group.BoxDesc = dcmp.GetStringDcmpContentByPath(t.conf, "font_scale_box_desc")
		} else {
			group.BoxDesc = t.getPopupSubTitle(ctx, "box_desc")
		}
	}

	for _, productInfo := range t.subProjects {
		productItem = productInfo
		break
	}

	if t.isBoxSubTitleShowPrice(ctx, productItem) {
		group.PopupSubTitle = util.String2PtrString("%s")
	} else {
		if len(t.subProjects) > 1 {
			group.PopupSubTitle = t.getPopupSubTitle(ctx, "popup_sub_title")
		} else if len(t.subProjects) == 1 {
			group.PopupSubTitle = t.getPopupSubTitle(ctx, "popup_sub_title_for_one")
		}
	}

	groups = append(groups, group)
	t.groups = groups
	return t.groups
}

func (t *ThirtyBuilder) BuildFormShowType(ctx context.Context) int32 {
	t.formShowType = t.athenaRecommendAreaToFormShowType(t.recPos)
	return t.formShowType
}

func (t *ThirtyBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	return nil
}

func (t *ThirtyBuilder) getFeeDesc(ctx context.Context) *string {
	var (
		s       string
		replace map[string]string
	)

	replace = make(map[string]string)
	if t.hasRedPacketFee {
		str := *dcmp.GetStringDcmpContentByPath(t.conf, "red_packet")
		replace["currency_symbol"] = t.currencyUnit
		replace["num"] = fmt.Sprintf("%f", t.redPackFee)
		price.CheckSingle(ctx, "ThirtyBuilder", "getFeeDesc", "redPackFee", t.redPackFee)
		s = util.ReplaceTag(ctx, str, replace)
	}

	return &s
}

func (t *ThirtyBuilder) getPopupTitle(ctx context.Context) *string {
	return dcmp.GetStringDcmpContentByPath(t.conf, "popup_title")
}

func (t *ThirtyBuilder) getPopupSubTitle(ctx context.Context, path string) *string {
	var (
		s       string
		replace map[string]string
	)

	replace = make(map[string]string)
	s = *dcmp.GetStringDcmpContentByPath(t.conf, path)

	replace["num"] = "1"
	s = util.ReplaceTag(ctx, s, replace)

	return &s
}

func (t *ThirtyBuilder) getFeeDescIcon(ctx context.Context) *string {
	var (
		s string
	)

	if !t.hasRedPacketFee {
		s = *dcmp.GetStringDcmpContentByPath(t.conf, "fee_desc_icon")
	}

	return &s
}

func (t *ThirtyBuilder) setRecPos() {
	if item, ok := t.reqData.CommonBizInfo.SubGroup2RecPos[int32(commonConsts.SubGroupIdShortDistance)]; ok {
		t.recPos = item
	} else {
		// 兜底推荐区域
		t.recPos = consts.RecPosRecommendArea
	}
}

func (t *ThirtyBuilder) setBaseData(ctx context.Context) {
	var (
		pcIDs       []string
		iPCIDs      []int64
		checkPCIDs  []int64
		pcIDDefault int64
		isSelected  bool
		pcIDMap     = make(map[string]float64)
	)

	dcmpKey := "default"
	for pcID, product := range t.subProjects {
		pcIDs = append(pcIDs, strconv.FormatInt(pcID, 10))
		iPCIDs = append(iPCIDs, pcID)
		pcIDMap[util.ToString(pcID)] = product.GetEstimateFee()

		if 0 == pcIDDefault {
			pcIDDefault = product.GetProductCategory()
		}

		t.currencyUnit = product.GetBillInfoCurrency()

		if consts.CHECK == product.Product.BizInfo.CheckStatus {
			checkPCIDs = append(checkPCIDs, pcID)
			isSelected = true
		}

		// 存在特惠快车或特惠独立业务线
		if pcID == estimate_pc_id.EstimatePcIdFastSpecialRate || pcID == estimate_pc_id.EstimatePcIdSpecialRate {
			t.hasSpCar = true
		}

		// 存在小猪自营品类
		for _, city := range dcmp.GetJSONResultWithPath(ctx, thirdPartyLayoutInfoDcmpkey, nil, "with_selfoperated_city").Array() {
			if product.GetCityID() == util.ToInt(city.Int()) {
				t.hasSelfOperate = true
				break
			}
		}

		// 存在节假日费
		feeDesc := common_logic.GetHolidayFee(ctx, product.GetBillFeeDetailInfo())
		if feeDesc != nil {
			t.hasRedPacketFee = true
			t.redPackFee = feeDesc.Amount
		}

		// 存在优惠
		if common_logic.IsSetDiscountSet(product) {
			t.hasDiscount = true
		}
	}

	if t.hasSpCar {
		dcmpKey = "with_sp"
	} else if t.hasSelfOperate {
		dcmpKey = "with_selfoperated"
	}

	t.conf = dcmp.GetJSONMap(ctx, thirdPartyLayoutInfoDcmpkey, dcmpKey)

	if t.isHitThirdPartyV2(ctx) {
		for k, v := range dcmp.GetJSONMap(ctx, thirdPartyLayoutInfoDcmpkey, "with_thirdpartyv2") {
			t.conf[k] = v
		}
	}

	t.pcIDs = LayoutUtil.SortByValue(pcIDMap)

	if len(checkPCIDs) <= 0 {
		checkPCIDs = iPCIDs
	}
	t.checkPCIDs = checkPCIDs

	if isSelected {
		t.isSelected = consts.CHECK
	}
}

func (t *ThirtyBuilder) BuildWeight(ctx context.Context) int64 {
	t.buildCommonWeight(ctx)
	return t.weight
}

func (t *ThirtyBuilder) isHitThirdPartyV2(ctx context.Context) bool {
	experimentLayer := dcmp.GetJSONContentWithPath(ctx, "config_text-feature_layer_config", nil, "is_hit_thirdparty_v2")
	if "" == experimentLayer {
		return false
	}

	apoParams := t.reqData.GetApolloParam()

	result := apollo.GetHitToggleByNamespace(ctx, apoParams["pid"], experimentLayer, apoParams)
	if result == nil || result.GetAssignment() == nil {
		return false
	}

	hit := result.GetAssignment().GetParameter("is_hit_thirdparty_v2", "0")

	return "1" == hit

}
