package builder

import (
	"context"
	"strconv"

	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	fee_consts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/builder/util"
)

const (
	iosAppVersion = "6.9.17"
)

type TaxiPricingBoxBuilder struct {
	baseBuilder
	subProjects     map[int64]*biz_runtime.ProductInfoFull
	subGroupRecMap  map[int32]*AthenaApiv3.SubGroupRecInfo
	isSelected      int32
	outShowCategory int32
	recPos          int32
	conf            *taxi.V3LayoutConf

	pcIDs             []string
	checkedPcIDs      []int64
	levelType5PcIDs   []int64
	topSubGroupIdList []int64
	pcIDList          []int64
}

func NewTaxiPricingBoxBuilder(subProjects map[int64]*biz_runtime.ProductInfoFull,
	estimateData map[int64]*LayoutConsts.SimpleEstimateData, data *models.BaseReqData) IBuilder {
	return &TaxiPricingBoxBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateData,
		},
		subProjects: subProjects,
	}
}

func (t *TaxiPricingBoxBuilder) GetName() string {
	return "TaxiPricingBoxBuilder"
}

func (t *TaxiPricingBoxBuilder) Access() bool {
	if len(t.subProjects) == 0 {
		return false
	}

	if t.baseBuilder.reqData == nil || t.baseBuilder.reqData.CommonInfo.PricingBoxData == nil {
		return false
	}

	if t.baseBuilder.reqData.CommonInfo.OrderType == order.OrderTypeBooking {
		return false
	}

	conf := taxi.GetLayoutConf(context.TODO())
	if conf == nil {
		return false
	}
	t.conf = conf

	return true
}

func (t *TaxiPricingBoxBuilder) PreBuild(ctx context.Context) {
	t.setPCIDs()
	t.setOutShowCategory()
	t.setRecPos()

}

func (t *TaxiPricingBoxBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups []*proto.NewFormGroup
		group  *proto.NewFormGroup
	)

	group = &proto.NewFormGroup{
		Type:          int32(consts.TaxiPricingBoxGroupType),
		Products:      t.pcIDs,
		GroupId:       t.BuildGroupID(consts.TaxiPricingType, consts.SubGroupIdTaxiPricingBox),
		IsSelected:    t.isSelected,
		CarIcon:       util2.GetTaxiGroupIcon(ctx, util2.TaxiPricingBoxLayout, t.getCityId(), t.conf.Icon),
		CarTitle:      t.getCarTitle(ctx),
		PopupTitle:    &t.conf.PopupTitle,
		PopupSubTitle: &t.conf.PopupSubTitle,
		PopupToast:    &t.conf.Toast,
		FeeDescIcon:   t.getFeeDescIcon(ctx),
		FeeDescList:   t.get(ctx),
	}
	groups = append(groups, group)

	t.groups = groups
	return t.groups
}

func (t *TaxiPricingBoxBuilder) get(ctx context.Context) []*proto.NewFormFeeDesc {
	feeTypes := []int32{fee_consts.TypeIncrement, fee_consts.TypeNeutral, fee_consts.TypeDecrement}
	needAdd := true
	if t.reqData.CommonInfo.AccessKeyID == 1 && util.CompareAppVersion(t.reqData.CommonInfo.AppVersion, iosAppVersion) <= 0 {
		needAdd = false
	}
	if needAdd {
		feeTypes = append(feeTypes, fee_consts.TypeDynamic)
	}
	// if t.isSelected == consts.Checked {
	// 	// 有选中
	// 	return t.getFeeDescList(ctx, t.checkedPcIDs, feeTypes)
	// }

	// 其他情况: 只处理可算预估价的
	return t.getFeeDescList(ctx, t.pcIDList, feeTypes)
}

func (t *TaxiPricingBoxBuilder) BuildFormShowType(ctx context.Context) int32 {

	t.formShowType = t.athenaRecommendAreaToFormShowType(t.recPos)
	return t.formShowType
}

func (t *TaxiPricingBoxBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	return nil
}

func (t *TaxiPricingBoxBuilder) getFeeDescIcon(ctx context.Context) *string {
	s := dcmp.GetJSONContentWithPath(ctx, "anycar_estimate-taxi_group", nil, "fee_desc_icon")
	return &s
}

func (t *TaxiPricingBoxBuilder) getCarTitle(ctx context.Context) string {
	var hasFastTaxi bool

	for _, pcID := range t.pcIDs {
		if util.ToInt(pcID) == estimate_pc_id.EstimatePcIdFastTaxi {
			hasFastTaxi = true
		}
	}

	if hasFastTaxi && t.reqData.CommonInfo.Lang == "zh-CN" {
		carTitle := car_info.GetCarNameByCondition(ctx, estimate_pc_id.EstimatePcIdFastTaxi, util.ToInt(t.reqData.AreaInfo.City), t.reqData.CommonInfo.Lang)
		if carTitle != "" {
			return carTitle
		}
	}

	return t.conf.Title
}

func (t *TaxiPricingBoxBuilder) setPCIDs() {
	var (
		pcIDs           []string
		checkedPcIDs    []int64
		levelType5PcIDs []int64
	)

	t.pcIDList = make([]int64, 0, len(t.subProjects))

	for pcID, product := range t.subProjects {
		pcIDs = append(pcIDs, strconv.FormatInt(pcID, 10))

		if consts.CHECK == product.Product.BizInfo.CheckStatus {
			t.isSelected = consts.CHECK
			checkedPcIDs = append(checkedPcIDs, pcID)
		}

		if product.GetLevelType() == 5 {
			levelType5PcIDs = append(levelType5PcIDs, pcID)

		}
		t.pcIDList = append(t.pcIDList, pcID)
	}

	t.pcIDs = pcIDs
	t.checkedPcIDs = checkedPcIDs
	t.levelType5PcIDs = levelType5PcIDs

	if t.reqData != nil && t.reqData.CommonBizInfo.TopData != nil {
		t.topSubGroupIdList = t.reqData.CommonBizInfo.TopData.TopSubGroupIdList
	}
}

func (t *TaxiPricingBoxBuilder) setOutShowCategory() {
	if item, ok := t.subGroupRecMap[int32(consts.SubGroupIdTaxiPricingBox)]; ok {
		t.outShowCategory = item.GetOutshowCategory()
	}
}

func (t *TaxiPricingBoxBuilder) setRecPos() {
	// 置顶
	if util.InArrayInt64(consts.SubGroupIdTaxiPricingBox, t.topSubGroupIdList) {
		t.recPos = consts.RecPosTopArea
	} else if item, ok := t.reqData.CommonBizInfo.SubGroup2RecPos[consts.SubGroupIdTaxiPricingBox]; ok {
		t.recPos = item
	} else {
		// 兜底推荐区域
		t.recPos = consts.RecPosRecommendArea
	}
}

func (t *TaxiPricingBoxBuilder) BuildWeight(ctx context.Context) int64 {
	t.buildCommonWeight(ctx)
	return t.weight
}
