package builder

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/bargain"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestBargainBuilder(t *testing.T) {
	// 关键：禁用内联优化
	t.<PERSON>llel()
	full := &biz_runtime.ProductInfoFull{}
	patch := gomonkey.NewPatches()
	defer patch.Reset()

	// Mock 成员方法
	patch.ApplyMethod(
		reflect.TypeOf(full),
		"GetBargainRecommendInfo",
		func(_ *biz_runtime.ProductInfoFull) *bargain.RecommendPriceInfo {
			return &bargain.RecommendPriceInfo{
				UpperLimit:      1,
				LowerLimit:      1,
				LowerLowerLimit: 1,
			}
		},
	)

	getRecommendLimitData(context.TODO(), full)
}
