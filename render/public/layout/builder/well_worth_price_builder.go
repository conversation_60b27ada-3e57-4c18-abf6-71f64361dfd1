package builder

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"

	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"

	commonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	fee_consts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
)

const (
	WellWorthPriceBoxBasePCID = 6001
)

type WellWorthPriceBuilder struct {
	baseBuilder
	subProjects  map[int64]*biz_runtime.ProductInfoFull
	pcIDs        []string
	iPcIDs       []int64
	currencyUnit string
	isSelected   int32
	recPos       int32
}

func NewWellWorthPriceBuilder(subProjects map[int64]*biz_runtime.ProductInfoFull, estimateData map[int64]*LayoutConsts.SimpleEstimateData, data *models.BaseReqData) IBuilder {
	return &WellWorthPriceBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateData,
		},
		subProjects: subProjects,
	}
}

func (f *WellWorthPriceBuilder) GetName() string {
	return "WellWorthPriceBuilder"
}

func (f *WellWorthPriceBuilder) Access() bool {
	if len(f.subProjects) == 0 {
		return false
	}

	return true
}

func (f *WellWorthPriceBuilder) PreBuild(ctx context.Context) {
	f.setBaseData(ctx)
	f.setRecPos()
}

func (f *WellWorthPriceBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups []*proto.NewFormGroup
	)

	group := &proto.NewFormGroup{
		Products:      f.pcIDs,
		Type:          int32(consts.FarMustCheapBoxGroupType),
		GroupId:       f.BuildGroupID(commonConsts.ShortDistanceType, commonConsts.SubGroupIdChaoZhiDa),
		CarIcon:       f.getDcmpContent(ctx, "car_icon"),
		CarTitle:      f.getDcmpContent(ctx, "car_title"),
		PopupTitle:    f.getPopupTitle(ctx),
		PopupSubTitle: f.getPopupSubTitle(ctx),
		FeeDetailUrl:  f.getFeeDetailUrl(ctx),
		FeeMsg:        f.getFeeMsg(ctx),
		IsSelected:    f.isSelected,
		BoxDesc:       f.getBoxDesc(ctx),
		FeeDescList:   f.getFeeDescList(ctx, []int64{f.iPcIDs[0]}, []int32{fee_consts.TypeDynamic, fee_consts.TypeIncrement, fee_consts.TypeDecrement, fee_consts.TypeBusinessPay, fee_consts.TypeZiyoubao}),
	}
	groups = append(groups, group)
	f.groups = groups
	return f.groups
}

func (f *WellWorthPriceBuilder) BuildFormShowType(ctx context.Context) int32 {
	f.formShowType = f.athenaRecommendAreaToFormShowType(f.recPos)
	return f.formShowType
}

func (f *WellWorthPriceBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	return nil
}

func (f *WellWorthPriceBuilder) getPopupTitle(ctx context.Context) *string {
	var (
		s string
	)
	s = f.getDcmpContent(ctx, "popup_title")
	return &s
}

func (f *WellWorthPriceBuilder) getPopupSubTitle(ctx context.Context) *string {
	var (
		s       string
		replace map[string]string
	)

	replace = make(map[string]string)
	s = f.getDcmpContent(ctx, "popup_sub_title")
	replace["num"] = strconv.Itoa(len(f.subProjects))
	s = util.ReplaceTag(ctx, s, replace)

	return &s
}

func (f *WellWorthPriceBuilder) getFeeDetailUrl(ctx context.Context) *string {
	var (
		feeDetial string
		replace   map[string]string
		basePCID  int64
	)

	for _, pcID := range f.iPcIDs {
		basePCID = pcID
		if pcID == WellWorthPriceBoxBasePCID {
			break
		}
	}

	feeDetial = f.getDcmpContent(ctx, "fee_detail_url")
	replace = make(map[string]string)
	replace["eid"] = f.subProjects[basePCID].GetEstimateID()
	feeDetial = util.ReplaceTag(ctx, feeDetial, replace)
	return &feeDetial
}

func (f *WellWorthPriceBuilder) getFeeMsg(ctx context.Context) *string {
	var (
		feeMsg  string
		replace map[string]string
	)

	feeMsg = f.getDcmpContent(ctx, "fee_msg")
	replace = make(map[string]string)

	//小数点位数实验
	estimateFeeAmount := f.subProjects[f.iPcIDs[0]].GetEstimateFee()
	price.CheckSingle(ctx, "WellWorthPriceBuilder", "getFeeMsg", "estimateFeeAmount", estimateFeeAmount)
	feeType := commonConsts.FeeTypeCapPrice
	params := f.reqData.GetApolloParam()
	key := params["pid"]
	feeAmount := util2.PriceFormat(ctx, params, key, estimateFeeAmount, feeType)

	//费项
	replace["fee_amount"] = feeAmount
	feeMsg = util.ReplaceTag(ctx, feeMsg, replace)
	return &feeMsg
}

func (f *WellWorthPriceBuilder) getBoxDesc(ctx context.Context) *string {
	var (
		boxDesc string
		replace map[string]string
	)
	boxDesc = f.getDcmpContent(ctx, "box_desc")
	replace = make(map[string]string)
	replace["num"] = strconv.Itoa(len(f.subProjects))
	boxDesc = util.ReplaceTag(ctx, boxDesc, replace)
	return &boxDesc

}

func (f *WellWorthPriceBuilder) setRecPos() {
	if recPos, ok := f.reqData.CommonBizInfo.SubGroup2RecPos[int32(commonConsts.SubGroupIdChaoZhiDa)]; ok {
		f.recPos = recPos
	} else {
		// 兜底推荐区域
		f.recPos = consts.RecPosRecommendArea
	}
}

func (f *WellWorthPriceBuilder) setBaseData(ctx context.Context) {
	var (
		pcIDs  []string
		iPcIDs []int64
	)

	f.isSelected = consts.UNCHECK
	for pcID, product := range f.subProjects {
		iPcIDs = append(iPcIDs, pcID)
		pcIDs = append(pcIDs, strconv.FormatInt(pcID, 10))
		f.currencyUnit = product.GetBillInfoCurrency()
		if f.isSelected != consts.CHECK && consts.CHECK == product.Product.BizInfo.CheckStatus {
			f.isSelected = consts.CHECK
		}
	}
	f.iPcIDs = iPcIDs
	f.pcIDs = pcIDs
}

func (f *WellWorthPriceBuilder) getDcmpContent(ctx context.Context, path string) string {
	var (
		str string
	)
	str = dcmp.GetJSONContentWithPath(ctx, "anycar_estimate-chao_zhi_da_layout", nil, path)
	return str
}

func (f *WellWorthPriceBuilder) BuildWeight(ctx context.Context) int64 {
	f.buildCommonWeight(ctx)
	return f.weight
}
