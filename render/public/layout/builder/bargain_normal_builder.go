package builder

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"

	commonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"

	"git.xiaojukeji.com/gulfstream/passenger-common/constant"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type BargainNormalBuilder struct {
	baseBuilder
	topRec *AthenaApiv3.TopRecRes
	recPos int32

	product *biz_runtime.ProductInfoFull
}

func NewBargainNormalBuilder(product *biz_runtime.ProductInfoFull, topRec *AthenaApiv3.TopRecRes, data *models.BaseReqData, themeData *proto.NewFormThemeData) IBuilder {

	bargainBuilder := &BargainNormalBuilder{
		baseBuilder: baseBuilder{
			reqData:   data,
			themeData: themeData,
		},
		topRec:  topRec,
		product: product,
	}

	return bargainBuilder
}

func (b *BargainNormalBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups []*proto.NewFormGroup
	)

	disableShadow := util.ToInt32(b.GetDisableShadow(ctx))
	group := &proto.NewFormGroup{
		Products:      []string{strconv.FormatInt(b.product.GetProductCategory(), 10)},
		Type:          bargainLayoutTypeMultiSelect,
		ActionType:    getActionType(),
		GroupId:       fmt.Sprintf("%d_%s", commonConsts.SingleType, strconv.FormatInt(b.product.GetProductCategory(), 10)),
		BargainMsg:    buildBargainMsg(ctx, b.product),
		BargainPopup:  buildBargainPopup(ctx, b.product),
		JumpType:      getJumpType(ctx),
		IsSelected:    int32(b.product.Product.BizInfo.CheckStatus),
		FeeDetailUrl:  convertStringToRef(dcmp.GetJSONContentWithPath(ctx, bargainInfoDcmpKey, nil, "fee_detail_url")),
		DisableShadow: &disableShadow,
	}

	groups = append(groups, group)
	b.groups = groups

	setUfsLimit(ctx, getRecommendLimitData(ctx, b.product))

	return b.groups
}

func getTopRecommendText(product *biz_runtime.ProductInfoFull) string {
	if product == nil {
		return ""
	}

	recommendInfo := product.GetCommonBizInfo().TopRec

	if recommendInfo != nil {
		if len(recommendInfo.GetExtraInfo()) > 0 {
			if buttonText, ok := recommendInfo.GetExtraInfo()["button_text"]; ok {
				return buttonText
			}
		}
	}

	return ""
}

func (b *BargainNormalBuilder) Access() bool {
	return b.product != nil && b.product.GetProductCategory() > 0
}

func (b *BargainNormalBuilder) GetName() string {
	return "BargainNormalLayout"
}

func (b *BargainNormalBuilder) PreBuild(ctx context.Context) {

	if b.reqData.CommonBizInfo.RecPosMap != nil {
		if item, ok := b.reqData.CommonBizInfo.RecPosMap[int32(b.product.GetProductCategory())]; ok {
			b.recPos = item
		} else {
			// 兜底推荐区域
			b.recPos = consts.RecPosRecommendArea
		}
	} else {
		// 兜底推荐区域
		b.recPos = consts.RecPosRecommendArea
	}
	return
}

func (b *BargainNormalBuilder) BuildFormShowType(ctx context.Context) int32 {
	b.formShowType = b.athenaRecommendAreaToFormShowType(b.recPos)

	return b.formShowType
}

func (b *BargainNormalBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	if b.reqData.CommonBizInfo.TopRec == nil || b.reqData.CommonBizInfo.TopRec.ExtraInfo == nil {
		return nil
	}

	bargainSceneRec, ok := b.reqData.CommonBizInfo.TopRec.ExtraInfo["bargain_scene_recommend"]
	if ok && bargainSceneRec == "1" {
		return b.themeData
	}

	return nil
}

func (b *BargainNormalBuilder) BuildWeight(ctx context.Context) int64 {
	b.buildCommonWeight(ctx)
	return b.weight
}

func setUfsLimit(ctx context.Context, recommendLimitData map[string]string) {
	limitData, err := json.Marshal(recommendLimitData)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "setFeature: %v", err)
		return
	}

	params := map[string]string{"trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx)}
	kv := map[string]string{"bargain_estimate.margin_fee": string(limitData)}
	if _, err = ufs.SetFeature(ctx, "passenger", params, kv); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "setFeature: %v", err)
	}
}

func getGuidePath(ctx context.Context, pcid int32, ackId int32) string {
	var guidePath string

	if util.InArrayInt32(ackId, []int32{constant.AndroidPassengerApp, constant.IOSPassengerApp}) {
		guidePath = GetGuidePath(ctx, guidePathDcmpKey, NA, int32(pcid))
	} else {
		guidePath = GetGuidePath(ctx, guidePathDcmpKey, webapp_path, int32(pcid))
	}

	return guidePath
}

func convertStringToRef(data string) *string {
	return &data
}
