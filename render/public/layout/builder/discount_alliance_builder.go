package builder

import (
	"context"
	LayoutUtil "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/builder/util"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	commonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	fee_consts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar_v3/layout/consts"
	"github.com/tidwall/gjson"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
)

const discountLayoutInfoDcmpkey = "estimate_form_v3-discount_alliance_layout"

type DiscountAllianceBuilder struct {
	baseBuilder
	subProjects  map[int64]*biz_runtime.ProductInfoFull
	pcIDs        []string
	iPcIDs       []int64
	currencyUnit string
	isSelected   int32
	recPos       int32
	conf         map[string]gjson.Result
	redPacketFee float64
	checkPcIDs   []int64
}

func NewDiscountAllianceBuilder(subProjects map[int64]*biz_runtime.ProductInfoFull, estimateData map[int64]*LayoutConsts.SimpleEstimateData, data *models.BaseReqData) IBuilder {
	return &DiscountAllianceBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateData,
		},
		subProjects: subProjects,
	}
}

func (d *DiscountAllianceBuilder) Access() bool {
	if len(d.subProjects) == 0 {
		return false
	}

	return true
}

func (d *DiscountAllianceBuilder) GetName() string {
	return "DiscountAllianceBuilder"
}

func (d *DiscountAllianceBuilder) PreBuild(ctx context.Context) {
	d.setBaseData(ctx)
	d.setRecPos()
}

func (d *DiscountAllianceBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups      []*proto.NewFormGroup
		productItem *biz_runtime.ProductInfoFull
	)

	popupSubTitle := dcmp.TranslateTemplate(*dcmp.GetStringDcmpContentByPath(d.conf, "popup_sub_title"), map[string]string{"num": "1"})
	group := &proto.NewFormGroup{
		Products:      d.pcIDs,
		Type:          int32(consts.ShortDistanceGroupType),
		GroupId:       d.BuildGroupID(commonConsts.ShortDistanceType, commonConsts.SubGroupIdDiscountAlliance),
		CarIcon:       *dcmp.GetStringDcmpContentByPath(d.conf, "car_icon"),
		CarTitle:      *dcmp.GetStringDcmpContentByPath(d.conf, "car_title"),
		PopupTitle:    dcmp.GetStringDcmpContentByPath(d.conf, "popup_title"),
		PopupSubTitle: &popupSubTitle,
		FeeDescIcon:   dcmp.GetStringDcmpContentByPath(d.conf, "fee_desc_icon"),
	}

	for _, productInfo := range d.subProjects {
		productItem = productInfo
		break
	}

	if d.showHolidayFeeDesc(ctx, productItem) && d.redPacketFee != 0 {
		empty := ""
		feeDesc := dcmp.TranslateTemplate(*dcmp.GetStringDcmpContentByPath(d.conf, "red_packet"), map[string]string{
			"num":             util.ToString(d.redPacketFee),
			"currency_symbol": "",
			"currency_unit":   d.currencyUnit,
		})
		group.FeeDescIcon = &empty
		group.FeeDesc = &feeDesc
	}

	var feeDescList []*proto.NewFormFeeDesc
	if d.isSelected == consts.CHECK {
		feeDescList = d.getFeeDescList(ctx, d.checkPcIDs, []int32{fee_consts.TypeIncrement, fee_consts.TypeDecrement, fee_consts.TypeBusinessPay, fee_consts.TypeZiyoubao})
	} else {
		feeDescList = d.getFeeDescList(ctx, d.iPcIDs, []int32{fee_consts.TypeIncrement, fee_consts.TypeDecrement, fee_consts.TypeBusinessPay, fee_consts.TypeZiyoubao})
	}

	group.FeeDescList = feeDescList
	groups = append(groups, group)
	d.groups = groups
	return d.groups
}

func (d *DiscountAllianceBuilder) BuildFormShowType(ctx context.Context) int32 {
	d.formShowType = d.athenaRecommendAreaToFormShowType(d.recPos)
	return d.formShowType
}

func (d *DiscountAllianceBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	return nil
}

func (d *DiscountAllianceBuilder) BuildWeight(ctx context.Context) int64 {
	d.buildCommonWeight(ctx)
	return d.weight
}

func (d *DiscountAllianceBuilder) setBaseData(ctx context.Context) {
	var (
		pcIDs          []string
		iPcIDs         []int64
		checkPcIDs     []int64
		hasSpCar       bool
		hasSelfOperate bool
		pcIDMap        = make(map[string]float64)
	)

	d.isSelected = consts.UNCHECK
	for pcID, product := range d.subProjects {
		iPcIDs = append(iPcIDs, pcID)
		pcIDs = append(pcIDs, strconv.FormatInt(pcID, 10))
		pcIDMap[util.ToString(pcID)] = product.GetEstimateFee()
		price.CheckSingle(ctx, "DiscountAllianceBuilder", "setBaseData", "EstimateFee", product.GetEstimateFee())
		d.currencyUnit = product.GetBillInfoCurrency()
		if d.isSelected != consts.CHECK && consts.CHECK == product.Product.BizInfo.CheckStatus {
			d.isSelected = consts.CHECK
			checkPcIDs = append(checkPcIDs, pcID)
		}

		if pcID == estimate_pc_id.EstimatePcIdFastSpecialRate || pcID == estimate_pc_id.EstimatePcIdSpecialRate {
			hasSpCar = true
		}

		for _, city := range dcmp.GetJSONResultWithPath(ctx, discountLayoutInfoDcmpkey, nil, "with_selfoperated_city").Array() {
			if product.GetCityID() == util.ToInt(city.Int()) {
				hasSelfOperate = true
				break
			}
		}

		d.redPacketFee = product.GetHolidayFee()
		price.CheckSingle(ctx, "DiscountAllianceBuilder", "setBaseData", "redPacketFee", d.redPacketFee)
	}

	d.iPcIDs = iPcIDs
	d.pcIDs = LayoutUtil.SortByValue(pcIDMap)
	d.checkPcIDs = checkPcIDs

	dcmpPath := "default"
	if hasSpCar {
		dcmpPath = "with_sp"
	} else if hasSelfOperate {
		dcmpPath = "with_selfoperated"
	}

	d.conf = dcmp.GetJSONMap(ctx, discountLayoutInfoDcmpkey, dcmpPath)

}

func (d *DiscountAllianceBuilder) setRecPos() {
	if pos, ok := d.reqData.CommonBizInfo.SubGroup2RecPos[int32(commonConsts.SubGroupIdDiscountAlliance)]; ok {
		d.recPos = pos
	} else {
		// 兜底推荐区域
		d.recPos = consts.RecPosRecommendArea
	}
}
