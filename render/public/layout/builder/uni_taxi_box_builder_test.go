package builder

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/tidwall/gjson"
)

func TestUniTaxiBoxBuilder_BuildGroups(t *testing.T) {
	tests := []struct {
		name         string
		baseReqData  *models.BaseReqData
		wantCarTitle string
	}{
		{
			name: "normal test hit city special config",
			baseReqData: &models.BaseReqData{
				AreaInfo: models.AreaInfo{
					City: 5,
				},
			},
			wantCarTitle: "杭州新出租",
		},
		{
			name: "normal test hit city default config",
			baseReqData: &models.BaseReqData{
				AreaInfo: models.AreaInfo{
					City: 1,
				},
			},
			wantCarTitle: "出租车",
		},
	}

	patch := gomonkey.NewPatches()
	defer patch.Reset() // 测试结束后自动恢复
	patch.ApplyFunc(dcmp.GetJSONMap,
		func(ctx context.Context, key string, path string) map[string]gjson.Result {
			dcmpConfig := "{\"5\":{\"car_icon\":\"https://s3-gz01.didistatic.com/packages-mait/img/CpMEvRAG4O1732675425064.png\",\"car_title\":\"杭州新出租\",\"popup_title\":\"已选%s个品牌\",\"unselect_popup_title\":\"有%s个品牌可选\",\"popup_sub_title\":\"%s\",\"fee_desc_icon\":\"https://pt-starimg.didistatic.com/static/starimg/img/koYaLjhSQm1638954388327.png\"},\"default\":{\"car_icon\":\"https://s3-gz01.didistatic.com/packages-mait/img/CpMEvRAG4O1732675425064.png\",\"car_title\":\"出租车\",\"popup_title\":\"已选%s个品牌\",\"unselect_popup_title\":\"有%s个品牌可选\",\"popup_sub_title\":\"%s\",\"fee_desc_icon\":\"https://pt-starimg.didistatic.com/static/starimg/img/koYaLjhSQm1638954388327.png\"}}"
			switch path {
			case "5":
				return gjson.Get(dcmpConfig, "5").Map()
			default:
				return gjson.Get(dcmpConfig, "default").Map()

			}
		})
	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			taxiBoxBuilder := NewUniTaxiBoxBuilder(nil, nil, tt.baseReqData)
			formGroup := taxiBoxBuilder.BuildGroups(ctx)
			assert.Equal(t, formGroup[0].CarTitle, tt.wantCarTitle)
		})
	}
}
