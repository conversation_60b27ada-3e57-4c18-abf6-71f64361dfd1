package util

import "sort"

func SortByValue(inputMap map[string]float64) []string {
	// 将键值对存入切片
	var keyValueSlice []struct {
		Key   string
		Value float64
	}

	for key, value := range inputMap {
		keyValueSlice = append(keyValueSlice, struct {
			Key   string
			Value float64
		}{Key: key, Value: value})
	}

	// 使用自定义排序函数按值升序排序
	sort.Slice(keyValueSlice, func(i, j int) bool {
		return keyValueSlice[i].Value < keyValueSlice[j].Value
	})

	// 构建结果切片
	result := make([]string, len(keyValueSlice))
	for i, keyValue := range keyValueSlice {
		result[i] = keyValue.Key
	}

	return result
}
