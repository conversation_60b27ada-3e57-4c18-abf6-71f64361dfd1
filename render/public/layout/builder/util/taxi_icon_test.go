package util

import (
	"context"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

func TestGetTaxiGroupIcon(t *testing.T) {
	Convey("get car icon by city and pos", t, func() {
		patches := gomonkey.NewPatches()
		defer patches.Reset()
		patches.ApplyFunc(apollo.GetConfigsByNamespaceAndConditions, func(ctx context.Context, namespace string, conditions *model.Condition) ([]byte, error) {
			return []byte("[{\"pos\":\"TaxiPricingBoxLayout\",\"city\":\"13\",\"car_icon\":\"test-icon\"}]"), nil
		})
		So(GetTaxiGroupIcon(context.TODO(), TaxiPricingBoxLayout, 13, ""), ShouldEqual, "test-icon")
	})
	Convey("get car icon by city and pos empty", t, func() {
		patches := gomonkey.NewPatches()
		defer patches.Reset()
		patches.ApplyFunc(apollo.GetConfigsByNamespaceAndConditions, func(ctx context.Context, namespace string, conditions *model.Condition) ([]byte, error) {
			return []byte(""), nil
		})
		So(GetTaxiGroupIcon(context.TODO(), TaxiPricingBoxLayout, 13, "test-icon"), ShouldEqual, "test-icon")
	})
}
