package builder

import (
	"context"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
)

func Test_getFeeDescFromDcmp(t *testing.T) {
	ctx := context.Background()
	patch := mockey.Mock(dcmp.GetJSONMap).Return(map[string]gjson.Result{
		"icon":            gjson.Parse(`"icon-url"`),
		"content":         gjson.Parse(`"desc-content"`),
		"border_color":    gjson.Parse(`"red"`),
		"bg_color":        gjson.Parse(`"white"`),
		"highlight_color": gjson.Parse(`"yellow"`),
	}).Build()
	defer patch.UnPatch()

	desc := getFeeDescFromDcmp(ctx, "some_key", "some_path", map[string]string{"tag": "val"})
	assert.NotNil(t, desc)
	assert.Equal(t, "icon-url", desc.Icon)
	assert.Equal(t, "desc-content", desc.Content)
	assert.Equal(t, "red", desc.BorderColor)
	assert.NotNil(t, desc.TextColor)
	assert.Equal(t, "red", *desc.TextColor)
	assert.NotNil(t, desc.BgColor)
	assert.Equal(t, "white", *desc.BgColor)
	assert.NotNil(t, desc.HighlightColor)
	assert.Equal(t, "yellow", *desc.HighlightColor)
}
