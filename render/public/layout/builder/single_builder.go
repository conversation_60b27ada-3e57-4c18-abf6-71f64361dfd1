package builder

import (
	"context"
	"strconv"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type SingleBuilder struct {
	baseBuilder
	product         *biz_runtime.ProductInfoFull
	productCategory int64
	topList         []int64
	recPos          int32
	isSelected      int32
	topRec          *AthenaApiv3.TopRecRes
}

func NewSingleBuilder(product *biz_runtime.ProductInfoFull, topRec *AthenaApiv3.TopRecRes, data *models.BaseReqData, themeData *proto.NewFormThemeData) IBuilder {
	return &SingleBuilder{
		baseBuilder: baseBuilder{
			reqData:   data,
			themeData: themeData,
		},
		product: product,
		topRec:  topRec,
	}
}

func (s *SingleBuilder) GetName() string {
	return "SingleBuilder"
}

func (s *SingleBuilder) Access() bool {
	return true
}

func (s *SingleBuilder) PreBuild(ctx context.Context) {
	s.productCategory = s.product.GetProductCategory()
	s.isSelected = int32(s.product.Product.BizInfo.CheckStatus)
	if s.product != nil && s.product.BaseReqData != nil && s.product.BaseReqData.CommonBizInfo.TopData != nil {
		s.topList = s.product.BaseReqData.CommonBizInfo.TopData.TopPcIdList
	}

	if s.reqData.CommonBizInfo.RecPosMap != nil {
		if item, ok := s.reqData.CommonBizInfo.RecPosMap[int32(s.productCategory)]; ok {
			s.recPos = item
		} else {
			// 兜底推荐区域
			s.recPos = consts.RecPosRecommendArea
		}
	} else {
		// 兜底推荐区域
		s.recPos = consts.RecPosRecommendArea
	}
}

func (s *SingleBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups []*proto.NewFormGroup
		group  *proto.NewFormGroup
	)

	group = &proto.NewFormGroup{
		Type:       int32(consts.SingleGroupType),
		Products:   []string{strconv.FormatInt(s.productCategory, 10)},
		GroupId:    s.BuildGroupID(consts.SingleType, s.productCategory),
		IsSelected: s.isSelected,
	}
	if carpool.TypeMiniBus == s.product.GetCarpoolType() {
		group.Type = int32(consts.MiniBusBoxType)
	}
	groups = append(groups, group)

	s.groups = groups
	return s.groups
}

func (s *SingleBuilder) BuildFormShowType(ctx context.Context) int32 {
	// 过滤置顶
	if util.InArrayInt64(s.productCategory, s.topList) {
		s.formShowType = consts.TopArea
		return s.formShowType
	}

	s.formShowType = s.athenaRecommendAreaToFormShowType(s.recPos)
	return s.formShowType
}

func (s *SingleBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	return s.GetThemeDataSingleCar(ctx, s.productCategory)
}

func (s *SingleBuilder) BuildWeight(ctx context.Context) int64 {
	s.buildCommonWeight(ctx)
	return s.weight
}
