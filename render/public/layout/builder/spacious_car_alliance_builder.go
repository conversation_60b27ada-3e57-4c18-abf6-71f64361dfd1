package builder

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	fee_consts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
)

const (
	spaciousSingleLayoutInfoDcmpKey = "estimate_form_v3-custom_service_single_spacious_car_alliance"
)

type SpaciousCarAllianceBuilder struct {
	baseBuilder
	product            *biz_runtime.ProductInfoFull
	linkToProduct      *biz_runtime.ProductInfoFull
	pcIDs              []string
	isSelected         int32
	linkFromSelected   int32
	hasCarSelectionFee bool
	topList            []int64
	linkToRecPos       int32
}

func NewSpaciousBuilder(product *biz_runtime.ProductInfoFull,
	linkToProduct *biz_runtime.ProductInfoFull,
	data *models.BaseReqData,
	estimateData map[int64]*LayoutConsts.SimpleEstimateData,
	themeData *proto.NewFormThemeData) IBuilder {
	return &SpaciousCarAllianceBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateData,
			themeData:    themeData,
		},
		product:       product,
		linkToProduct: linkToProduct,
	}
}

func (s *SpaciousCarAllianceBuilder) Access() bool {
	return true
}

func (s *SpaciousCarAllianceBuilder) GetName() string {
	return "SpaciousCarAllianceBuilder"
}

func (s *SpaciousCarAllianceBuilder) PreBuild(ctx context.Context) {
	var (
		pcIDs = make([]string, 0)
	)

	pcIDs = append(pcIDs, util.ToString(s.linkToProduct.GetProductCategory()))
	s.pcIDs = pcIDs
	s.isSelected = int32(s.linkToProduct.Product.BizInfo.CheckStatus)
	s.linkFromSelected = int32(s.product.Product.BizInfo.CheckStatus)
	s.hasCarSelectionFee = s.hitCarSelectionFee()
	if s.product != nil && s.product.BaseReqData != nil && s.product.BaseReqData.CommonBizInfo.TopData != nil {
		s.topList = s.product.BaseReqData.CommonBizInfo.TopData.TopPcIdList
	}

	if s.reqData.CommonBizInfo.RecPosMap != nil {
		if item, ok := s.reqData.CommonBizInfo.RecPosMap[int32(s.linkToProduct.GetProductCategory())]; ok {
			s.linkToRecPos = item
		} else {
			// 兜底推荐区域
			s.linkToRecPos = consts.RecPosRecommendArea
		}
	} else {
		// 兜底推荐区域
		s.linkToRecPos = consts.RecPosRecommendArea
	}
}

func (s *SpaciousCarAllianceBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups []*proto.NewFormGroup
	)
	group := &proto.NewFormGroup{
		Products: s.pcIDs,
		Type:     consts.SpaciousGroupType,
		GroupId:  s.BuildGroupID(consts.SingleType, s.linkToProduct.GetProductCategory()),
	}

	if s.hasCarSelectionFee {
		group.IsSelected = s.isSelected
		group.LinkInfo = s.getLinkInfo(ctx)
	}

	groups = append(groups, group)
	s.groups = groups
	return s.groups
}

func (s *SpaciousCarAllianceBuilder) BuildFormShowType(ctx context.Context) int32 {
	if util.InArrayInt64(s.product.GetProductCategory(), s.topList) ||
		util.InArrayInt64(s.linkToProduct.GetProductCategory(), s.topList) {
		return consts.TopArea
	}

	return s.athenaRecommendAreaToFormShowType(s.linkToRecPos)
}

func (s *SpaciousCarAllianceBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	item := s.GetThemeDataSingleCar(ctx, s.product.GetProductCategory())
	if item == nil {
		item = s.GetThemeDataSingleCar(ctx, s.linkToProduct.GetProductCategory())
	}

	return item
}

func (s *SpaciousCarAllianceBuilder) BuildWeight(ctx context.Context) int64 {
	s.buildCommonWeight(ctx)
	return s.weight
}

func (s *SpaciousCarAllianceBuilder) hitCarSelectionFee() bool {
	if s.product.GetBillFeeDetailInfo() == nil {
		return false
	}

	_, ok := s.product.GetBillFeeDetailInfo()["talos_spacious_car_selection_fee"]
	if ok {
		return true
	}

	return false
}

func (s *SpaciousCarAllianceBuilder) getLinkInfo(ctx context.Context) *proto.NewFormLinkInfo {
	// 单勾场景在高版本全量，不考虑车大非单勾场景
	return s.buildSingleStyleLinkInfo(ctx)
}

func (s *SpaciousCarAllianceBuilder) buildSingleStyleLinkInfo(ctx context.Context) *proto.NewFormLinkInfo {
	var (
		tagItem  = &proto.NewFormPreferDataTag{}
		tagList  = make([]*proto.NewFormPreferDataTag, 0)
		linkInfo = &proto.NewFormLinkInfo{}
	)

	tagItem = &proto.NewFormPreferDataTag{
		Icon:    dcmp.GetJSONContentWithPath(ctx, spaciousSingleLayoutInfoDcmpKey, nil, "tag_icon"),
		Content: dcmp.GetJSONContentWithPath(ctx, spaciousSingleLayoutInfoDcmpKey, nil, "tag_content"),
	}
	tagList = append(tagList, tagItem)

	linkInfo.PreferData = &proto.NewFormPreferData{
		Desc:    dcmp.GetJSONContentWithPath(ctx, spaciousSingleLayoutInfoDcmpKey, nil, "desc"),
		TagList: tagList,
	}
	linkInfo.InfoUrl = s.buildSpaciousV2Url(ctx, dcmp.GetJSONContentWithPath(ctx, spaciousSingleLayoutInfoDcmpKey, nil, "info_url"))
	linkInfo.IsSelected = s.linkFromSelected
	linkInfo.LinkProduct = util.ToString(s.product.GetProductCategory())
	linkInfo.IsStrength = 1
	linkInfo.SelectStyle = 1
	linkInfo.FeeMsg = s.buildSingleFeeMsg(ctx, dcmp.GetJSONContentWithPath(ctx, spaciousSingleLayoutInfoDcmpKey, nil, "fee_msg"))
	linkInfo.FeeDescList = s.getFeeDescList(ctx, []int64{s.product.GetProductCategory()}, []int32{fee_consts.TypeIncrement, fee_consts.TypeDecrement, fee_consts.TypeBusinessPay})

	return linkInfo
}

func (s *SpaciousCarAllianceBuilder) buildSpaciousV2Url(ctx context.Context, iconUrl string) string {
	apolloKey, apolloParam := s.product.GetApolloParams(biz_runtime.WithPIDKey)
	isAllow, params := apollo.GetParameters("gs_fee_detail_spacious_v2", apolloKey, apolloParam)
	if isAllow {
		eid := s.product.GetEstimateID()
		token := s.product.BaseReqData.PassengerInfo.Token
		urlV2, ok := params["info_url_v2"]
		if !ok {
			urlV2 = ""
		}
		if eid != "" && token != "" && urlV2 != "" {
			return urlV2 + "?estimate_id=" + eid + "&lang=" + apolloParam["lang"] +
				"&app_version=" + apolloParam["app_version"] + "&access_key_id=" + apolloParam["access_key_id"] +
				"&token=" + token
		}
	}

	return iconUrl
}

func (s *SpaciousCarAllianceBuilder) buildSingleFeeMsg(ctx context.Context, template string) string {
	feeAmount := s.product.GetPersonalEstimateFee()
	apolloKey, apolloParam := s.product.GetApolloParams(biz_runtime.WithPIDKey)
	fee := util2.PriceFormat(ctx, apolloParam, apolloKey, feeAmount, consts.FeeTypeDefault)
	price.CheckSingle(ctx, "SpaciousCarAllianceBuilder", "buildSingleFeeMsg", "feeAmount", feeAmount)
	return dcmp.TranslateTemplate(template, map[string]string{
		"fee_amount": fee,
	})
}
