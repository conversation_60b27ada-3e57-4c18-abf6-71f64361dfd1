package builder

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"

	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"

	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	commonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
)

const (
	topArea                      = 1
	recommendSupport             = 1
	layoutGroupsActionTypeOrder  = 2
	bargainLayoutTypeMultiSelect = 1
	bargainLayoutType            = 101
	lowLowLimit                  = "1"
	lowLimit                     = "2"
	upLimit                      = "999"
	guidePathDcmpKey             = "estimate_new_form-guide_path"
	bargainInfoDcmpKey           = "bargain-estimate_form_guide_info_v2"
	bargainRecommendV2Toggle     = "bargain_recommend_v2_switch"
)

type BargainBuilder struct {
	baseBuilder
	product           *biz_runtime.ProductInfoFull
	pcID              int64
	switchRecommendV2 bool
}

func NewBargainBuilder(ctx context.Context, product *biz_runtime.ProductInfoFull, estimateData map[int64]*LayoutConsts.SimpleEstimateData, data *models.BaseReqData, themeData *proto.NewFormThemeData) IBuilder {
	bargainBuilder := &BargainBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateData,
			themeData:    themeData,
		},
		product: product,
		pcID:    product.GetProductCategory(),
	}

	params := bargainBuilder.reqData.GetApolloParam()
	key := params["pid"]
	if apollo.FeatureToggle(ctx, bargainRecommendV2Toggle, key, params) {
		bargainBuilder.switchRecommendV2 = true
	}

	return bargainBuilder
}

func (b *BargainBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups []*proto.NewFormGroup
	)

	buttonText, buttonStyle := b.buildButtonInfo(ctx)
	disableShadow := util.ToInt32(b.GetDisableShadow(ctx))
	group := &proto.NewFormGroup{
		Products:      []string{strconv.FormatInt(b.pcID, 10)},
		Type:          b.getLayoutType(),
		ActionType:    getActionType(),
		GroupId:       fmt.Sprintf("%d_%s", commonConsts.SingleType, strconv.FormatInt(b.pcID, 10)),
		ButtonText:    buttonText,
		ButtonStyle:   buttonStyle,
		BargainMsg:    buildBargainMsg(ctx, b.product),
		BargainPopup:  buildBargainPopup(ctx, b.product),
		JumpType:      getJumpType(ctx),
		IsSelected:    0,
		FeeDetailUrl:  convertStringToRef(dcmp.GetJSONContentWithPath(ctx, bargainInfoDcmpKey, nil, "fee_detail_url")),
		DisableShadow: &disableShadow,
	}

	groups = append(groups, group)
	b.groups = groups

	setUfsLimit(ctx, getRecommendLimitData(ctx, b.product))

	return b.groups
}

func (b *BargainBuilder) buildButtonInfo(ctx context.Context) (*string, *proto.ButtonStyle) {
	var buttonText string
	var buttonStyle *proto.ButtonStyle
	if bargainLayoutTypeMultiSelect == b.getLayoutType() {
		return nil, nil
	}
	buttonDcmpPath := "call_button_normal"
	if b.getBargainSceneRecommend() == recommendSupport {
		buttonDcmpPath = "call_button_recommend"
		buttonText = getTopRecommendText(b.product)
	}

	buttonDcmpInfo := dcmp.GetJSONMap(ctx, bargainInfoDcmpKey, buttonDcmpPath)
	if buttonText == "" {
		buttonText = *dcmp.GetStringDcmpContentByPath(buttonDcmpInfo, "text")
	}

	bgGradients := make([]string, 0)
	if templ, ok := buttonDcmpInfo["bg_gradients"]; ok {
		_ = json.Unmarshal([]byte(templ.Raw), &bgGradients)
	}

	buttonStyle = &proto.ButtonStyle{
		BorderColor: *dcmp.GetStringDcmpContentByPath(buttonDcmpInfo, "border_color"),
		FontColor:   *dcmp.GetStringDcmpContentByPath(buttonDcmpInfo, "font_color"),
		BgGradients: bgGradients,
	}

	return &buttonText, buttonStyle
}

func (b *BargainBuilder) getBargainSceneRecommend() int {
	if b.product == nil {
		return 0
	}

	recommendInfoMap := b.product.GetCommonBizInfo().RecommendExtraInfoMap

	if info, ok := recommendInfoMap[int32(b.product.GetProductCategory())]; ok {
		if "" != info["bargain_scene_recommend"] {
			recommend, err := strconv.Atoi(info["bargain_scene_recommend"])
			if err != nil {
				return 0
			}
			return recommend
		}
	}

	return 0
}

func (b *BargainBuilder) getLayoutType() int32 {
	if b.product != nil && b.product.GetBizInfo() != nil && b.product.GetBizInfo().BargainIsMultiSelect {
		return bargainLayoutTypeMultiSelect
	}

	return bargainLayoutType
}

func getActionType() *int32 {
	var actionType int32
	actionType = layoutGroupsActionTypeOrder
	return &actionType
}

func buildBargainMsg(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.NewFormBargainMsg {

	var fee string
	if product.GetPrivateBizInfo().BargainData.FixPrice != nil {
		// 修改价格
		fee64 := cast.ToFloat64(product.GetPrivateBizInfo().BargainData.FixPrice.EstimateFee)
		price.CheckSingle(ctx, "buildBargainMsg", "layout_BargainMsg_FixPrice", "fee64", fee64)
		fee = cast.ToString(util.RoundAbs(fee64, 2))
	} else {
		price.CheckSingle(ctx, "buildBargainMsg", "layout_BargainMsg", "EstimateFee", product.GetEstimateFee(), price.WithCheckBelowZero())
		fee = cast.ToString(util.RoundAbs(product.GetEstimateFee(), 2))
	}

	buttonConfig := dcmp.GetJSONMap(ctx, bargainInfoDcmpKey, "bargain_button")

	var text string
	var replace map[string]string
	if product.GetPrivateBizInfo().BargainData.SenseConfig != nil && len(product.GetPrivateBizInfo().BargainData.SenseConfig.PriceLeftText) > 0 {
		text = product.GetPrivateBizInfo().BargainData.SenseConfig.PriceLeftText
		replace = map[string]string{
			"num": cast.ToString(fee),
		}
	} else {
		text = *dcmp.GetStringDcmpContentByPath(buttonConfig, "text")
		replace = map[string]string{
			"amount": cast.ToString(fee),
		}
	}

	showPlusMinus := isShowPlusMinus(ctx, product)
	return &proto.NewFormBargainMsg{
		Text:             util.ReplaceTag(ctx, text, replace),
		GuidePath:        getGuidePath(ctx, int32(product.GetProductCategory()), product.GetAccessKeyId()),
		UnderlineColor:   *dcmp.GetStringDcmpContentByPath(buttonConfig, "underline_color"),
		ShowPlusAndMinus: &showPlusMinus,
	}
}

func buildBargainPopup(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.BargainPopup {
	popUpInfo := dcmp.GetJSONMap(ctx, bargainInfoDcmpKey, "popup")
	limitData := getRecommendLimitData(ctx, product)

	ceil := buildMarginNode(limitData["ceil"], *dcmp.GetStringDcmpContentByPath(popUpInfo, "ceil_notice"))
	floor1 := buildMarginNode(limitData["floor1"], *dcmp.GetStringDcmpContentByPath(popUpInfo, "floor1_notice"))
	floor2 := buildMarginNode(limitData["floor2"], *dcmp.GetStringDcmpContentByPath(popUpInfo, "floor2_notice"))

	feeMargin := &proto.BargainFeeMarginV3{
		Ceil:   ceil,
		Floor1: floor1,
		Floor2: floor2,
	}

	descTemplate := &proto.NewFormFeeDesc{}
	if templ, ok := popUpInfo["fee_desc_content_template"]; ok {
		err := json.Unmarshal([]byte(templ.Raw), descTemplate)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "set fee_desc_content_template err: %v", err)
		}
	}

	return &proto.BargainPopup{
		Title:                  dcmp.GetStringDcmpContentByPath(popUpInfo, "title"),
		SubTitleMin:            dcmp.GetStringDcmpContentByPath(popUpInfo, "sub_title_min"),
		SubTitleEqual:          dcmp.GetStringDcmpContentByPath(popUpInfo, "sub_title_equal"),
		SubTitleMax:            dcmp.GetStringDcmpContentByPath(popUpInfo, "sub_title_max"),
		LeftPriceTag:           getArrDcmpContentByPath(popUpInfo, "left_price_tag"),
		RightPriceTag:          getArrDcmpContentByPath(popUpInfo, "right_price_tag"),
		ButtonText:             dcmp.GetStringDcmpContentByPath(popUpInfo, "button_text"),
		FeeMargin:              feeMargin,
		FeeDescContentTemplate: descTemplate,
		FeeMsgTemplate:         dcmp.GetStringDcmpContentByPath(popUpInfo, "fee_msg_template"),
	}
}

func buildMarginNode(amount string, notice string) *proto.BargainFeeMarginNode {
	return &proto.BargainFeeMarginNode{
		Amount: &amount,
		Notice: &notice,
	}
}

func getJumpType(ctx context.Context) *int32 {
	res, _ := strconv.Atoi(dcmp.GetJSONContentWithPath(ctx, bargainInfoDcmpKey, nil, "jump_type"))
	jumpType := int32(res)
	return &jumpType
}

func (b *BargainBuilder) Access() bool {
	return b.pcID > 0 && b.product != nil
}

func (b *BargainBuilder) GetName() string {
	return "BargainLayout"
}

func (b *BargainBuilder) PreBuild(ctx context.Context) {
	return
}

func (b *BargainBuilder) BuildFormShowType(ctx context.Context) int32 {
	b.formShowType = topArea
	return b.formShowType
}

func (b *BargainBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	if b.reqData.CommonBizInfo.TopRec == nil || b.reqData.CommonBizInfo.TopRec.ExtraInfo == nil {
		return nil
	}

	bargainSceneRec, ok := b.reqData.CommonBizInfo.TopRec.ExtraInfo["bargain_scene_recommend"]
	if ok && bargainSceneRec == "1" {
		return b.themeData
	}

	return nil
}

func (b *BargainBuilder) BuildWeight(ctx context.Context) int64 {
	b.buildCommonWeight(ctx)
	return b.weight
}

func getRecommendLimitData(ctx context.Context, product *biz_runtime.ProductInfoFull) map[string]string {
	limitData := make(map[string]string, 0)

	redPacket := product.GetHolidayFee()

	lowLimitInt := math.Max(redPacket, cast.ToFloat64(lowLimit))
	lowLowLimitInt := math.Max(redPacket, cast.ToFloat64(lowLowLimit))
	limitData["ceil"] = upLimit
	limitData["floor1"] = cast.ToString(lowLimitInt)
	limitData["floor2"] = cast.ToString(lowLowLimitInt)

	if product == nil {
		return limitData
	}

	if product.GetBargainRecommendInfo() != nil {
		recommendPriceInfo := product.GetBargainRecommendInfo()

		limitData["ceil"] = util.FormatPrice(util.Float64Add(recommendPriceInfo.UpperLimit, product.GetHolidayFee()), 1)
		limitData["floor1"] = util.FormatPrice(util.Float64Add(recommendPriceInfo.LowerLimit, product.GetHolidayFee()), 1)
		limitData["floor2"] = util.FormatPrice(util.Float64Add(recommendPriceInfo.LowerLowerLimit, product.GetHolidayFee()), 1)
	}
	price.CheckMulti(ctx, "getRecommendLimitData", "RecommendLimitData", limitData)

	return limitData
}

func getArrDcmpContentByPath(infoMap map[string]gjson.Result, path string) []int32 {
	var res []int32

	if infoMap == nil || len(infoMap) <= 0 {
		return res
	}

	for _, arrItem := range infoMap[path].Array() {
		res = append(res, int32(arrItem.Int()))
	}

	return res
}

// isShowPlusMinus 自选车冒泡页是否展示+&-号
func isShowPlusMinus(ctx context.Context, product *biz_runtime.ProductInfoFull) bool {
	if product == nil || product.BaseReqData == nil {
		return false
	}
	passengerInfo := product.BaseReqData.PassengerInfo
	areaInfo := product.BaseReqData.AreaInfo
	commonInfo := product.BaseReqData.CommonInfo
	userKey := strconv.FormatInt(passengerInfo.PID, 10)
	apolloParams := map[string]string{
		"pid":           strconv.FormatInt(passengerInfo.PID, 10),
		"city":          strconv.FormatInt(int64(areaInfo.City), 10),
		"phone":         passengerInfo.Phone,
		"access_key_id": strconv.FormatInt(int64(commonInfo.AccessKeyID), 10),
		"county_id":     strconv.FormatInt(int64(areaInfo.FromCounty), 10),
		"lang":          commonInfo.Lang,
		"app_version":   commonInfo.AppVersion,
	}
	ok, toggle := apollo.FeatureExp(ctx, "bargain_perception", userKey, apolloParams)
	if !ok || toggle == nil || toggle.GetGroupName() == "control_group" {
		return false
	}
	return true
}
