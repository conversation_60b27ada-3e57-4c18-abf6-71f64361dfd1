package sort_strategy

import (
	"context"
	"encoding/json"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	CommonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
)

type SortStrategyCal interface {
	BuildLayoutWeight(productSort *AthenaApiv3.ProductSortInfo, layout *proto.NewFormLayout, WeightByLayout int64) int64
}
type AbstractSort struct {
}

var bottomPcIds = getBottomPcIds()

func (s *AbstractSort) BuildLayoutWeight(productSort *AthenaApiv3.ProductSortInfo, layout *proto.NewFormLayout, WeightByLayout int64) int64 {
	var (
		weight int64
	)
	switch layout.FormShowType {
	case consts.TopArea:
		weight += consts.HighWeight
		break
	case consts.RecommendArea:
		weight += consts.MidWeight
		break
	case consts.OtherArea:
		weight += consts.LowWeight
		break
	default:
		weight += consts.MidWeight
	}

	// 包框置顶
	if layout.ThemeData != nil && layout.ThemeData.ThemeType != nil {
		if *layout.ThemeData.ThemeType == CommonConsts.ThemeTypeNormative {
			weight += 20000
		}
	}

	for _, group := range layout.Groups {
		if group.Type == consts.GuideGroupType2 {
			weight += consts.HighThousandWeight
		} else if group.Type == consts.GuideGroupType {
			weight += consts.MidThousandWeight
		}

		if productSort != nil && productSort.DefaultSelectUp != nil && *productSort.DefaultSelectUp == CommonConsts.DefaultSelectUp && group.IsSelected == consts.CHECK {
			weight += consts.LowThousandWeight
		}

		if 1 == len(group.Products) && util.InArrayStr(group.Products[0], bottomPcIds) {
			weight += consts.NegativeThousandWeight // 分区内置底
		}

		weight += WeightByLayout
	}

	return weight
}

func getBottomPcIds() []string {
	config := apollo.GetConfig(context.TODO(), "products_sort_conf", "product_category_custom_sort")
	if len(config) == 0 || config["bottom_pc_ids"] == "" {
		return nil
	}

	var pcIds []string
	err := json.Unmarshal([]byte(config["bottom_pc_ids"]), &pcIds)
	if err != nil {
		return nil
	}
	return pcIds
}
