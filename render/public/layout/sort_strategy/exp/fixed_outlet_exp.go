package exp

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	WaitRecPosStyle = "wait_rec_pos_style"
)

func IsFixedOutletExp(ctx context.Context, pid, phone, city, accessKeyID, appVersion string) bool {

	param := map[string]string{
		"pid":           pid,
		"phone":         phone,
		"city":          city,
		"access_key_id": accessKeyID,
		"app_version":   appVersion,
		"caller":        "mamba",
	}
	toggle := apollo.GetHitToggleByNamespace(ctx, param["pid"], WaitRecPosStyle, param)

	// 如果实验没过
	if toggle == nil || !toggle.IsAllow() || toggle.GetAssignment() == nil || toggle.GetAssignment().GetParameter("is_hit", "0") != "1" {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "user not hit exp||namespace = %v ||user=%v", WaitRecPosStyle, param)
		return false
	}
	log.Trace.Infof(ctx, trace.DLTagUndefined, "user hit exp||namespace = %v ||user=%v", WaitRecPosStyle, param)
	return true
}
