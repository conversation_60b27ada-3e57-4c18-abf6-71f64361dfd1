package sort_strategy

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"testing"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	CommonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestBuildLayoutWeight(t *testing.T) {
	// 该测试函数由AI自动生成
	tests := []struct {
		name           string
		productSort    *AthenaApiv3.ProductSortInfo
		layout         *proto.NewFormLayout
		WeightByLayout int64
		expectedWeight int64
	}{
		{
			name: "TopArea with ThemeTypeNormative",
			layout: &proto.NewFormLayout{
				FormShowType: consts.TopArea,
				ThemeData: &proto.NewFormThemeData{
					ThemeType: func() *int32 { var i int32 = CommonConsts.ThemeTypeNormative; return &i }(),
				},
				Groups: []*proto.NewFormGroup{},
			},
			WeightByLayout: 0,
			expectedWeight: consts.HighWeight + 20000,
		},
		{
			name: "RecommendArea without ThemeData",
			layout: &proto.NewFormLayout{
				FormShowType: consts.RecommendArea,
				Groups:       []*proto.NewFormGroup{},
			},
			WeightByLayout: 0,
			expectedWeight: consts.MidWeight,
		},
		{
			name: "OtherArea with GuideGroupType2",
			layout: &proto.NewFormLayout{
				FormShowType: consts.OtherArea,
				Groups:       []*proto.NewFormGroup{{Type: consts.GuideGroupType2}},
			},
			WeightByLayout: 0,
			expectedWeight: consts.LowWeight + consts.HighThousandWeight,
		},
		{
			name: "Default case with GuideGroupType and DefaultSelectUp",
			productSort: &AthenaApiv3.ProductSortInfo{
				DefaultSelectUp: func() *int32 { var i int32 = CommonConsts.DefaultSelectUp; return &i }(),
			},
			layout: &proto.NewFormLayout{
				Groups: []*proto.NewFormGroup{{Type: consts.GuideGroupType, IsSelected: consts.CHECK}},
			},
			WeightByLayout: 0,
			expectedWeight: consts.MidWeight + consts.MidThousandWeight + consts.LowThousandWeight,
		},
		{
			name: "Single product in group and in bottomPcIds",
			layout: &proto.NewFormLayout{
				Groups: []*proto.NewFormGroup{{Products: []string{"12200"}}},
			},
			WeightByLayout: 0,
			expectedWeight: consts.MidWeight + consts.NegativeThousandWeight,
		},
		{
			name: "Single product in group but not in bottomPcIds",
			layout: &proto.NewFormLayout{
				Groups: []*proto.NewFormGroup{{Products: []string{"12"}}},
			},
			WeightByLayout: 0,
			expectedWeight: consts.MidWeight,
		},
	}

	patch := gomonkey.ApplyFunc(getBottomPcIds, func() []string {
		return []string{"12200"}
	})
	bottomPcIds = getBottomPcIds()
	defer patch.Reset()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &AbstractSort{}
			weight := s.BuildLayoutWeight(tt.productSort, tt.layout, tt.WeightByLayout)
			assert.Equal(t, tt.expectedWeight, weight)
		})
	}
}

func TestGetBottomPcIds(t *testing.T) {
	// 该测试函数由AI自动生成
	tests := []struct {
		name          string
		config        map[string]string
		expectedPcIds []string
	}{
		{
			name: "Valid bottom_pc_ids",
			config: map[string]string{
				"bottom_pc_ids": `["12200", "12300"]`,
			},
			expectedPcIds: []string{"12200", "12300"},
		},
		{
			name: "Empty bottom_pc_ids",
			config: map[string]string{
				"bottom_pc_ids": `[]`,
			},
			expectedPcIds: []string{},
		},
		{
			name: "No bottom_pc_ids key",
			config: map[string]string{
				"some_other_key": `["12200", "12300"]`,
			},
			expectedPcIds: nil,
		},
		{
			name: "Invalid JSON in bottom_pc_ids",
			config: map[string]string{
				"bottom_pc_ids": `invalid_json`,
			},
			expectedPcIds: nil,
		},
		{
			name:          "Empty config",
			config:        map[string]string{},
			expectedPcIds: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patch := gomonkey.ApplyFunc(apollo.GetConfig, func(ctx context.Context, namespace, key string) map[string]string {
				return tt.config
			})
			defer patch.Reset()

			pcIds := getBottomPcIds()
			assert.Equal(t, tt.expectedPcIds, pcIds)
		})
	}
}
