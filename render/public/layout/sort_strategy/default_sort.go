package sort_strategy

import (
	"context"
	"encoding/json"
	"sort"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/builder"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
)

type DefaultSort struct {
	weightMap   map[string]int64 // map[pcid]weight
	productSort *AthenaApiv3.ProductSortInfo
	SortStrategyCal
	topBoxLayoutIndex int
}

func NewDefaultSort(ctx context.Context, BaseReqData *models.BaseReqData) *DefaultSort {

	weightMap := getWeightConfig(ctx)
	return &DefaultSort{
		weightMap:         weightMap,
		productSort:       BaseReqData.CommonBizInfo.ProductSortInfo,
		SortStrategyCal:   new(AbstractSort),
		topBoxLayoutIndex: -1,
	}
}

// 获取车型排序配置
func getWeightConfig(ctx context.Context) map[string]int64 {
	var (
		tag     = "getWeightConfig"
		carSort []*consts.CarSortConf
		weights map[string]int64
	)

	weights = make(map[string]int64)
	configs, err := apollo.GetConfigsByNamespace(ctx, consts.CarSortNamespace)
	if err != nil {
		log.Trace.Warnf(ctx, tag, "get config fail with err %v", err)
		return weights
	}

	err = json.Unmarshal(configs, &carSort)
	if err != nil {
		log.Trace.Warnf(ctx, tag, "unmarshal sorted list conf fail with err %v", err)
		return weights
	}

	if carSort == nil || len(carSort) == 0 || len(carSort[0].ProductsSort) == 0 {
		log.Trace.Warnf(ctx, tag, "car sort is nil")
		return weights
	}

	num := len(carSort[0].ProductsSort)
	for index, p := range carSort[0].ProductsSort {
		weights[strconv.Itoa(p.ProductCategory)] = int64(consts.LowHundredWeight + (num - index))
	}

	return weights
}

func (s *DefaultSort) BuildLayoutSort(ctx context.Context, builderList []builder.IBuilder) []*proto.NewFormLayout {
	var (
		layouts []*proto.NewFormLayout
	)

	// builder构建
	for _, iBuilder := range builderList {
		if !iBuilder.Access() {
			continue
		}
		iBuilder.PreBuild(ctx)
		layoutItem := &proto.NewFormLayout{
			ThemeData:    iBuilder.BuildTheme(ctx),
			Groups:       iBuilder.BuildGroups(ctx),
			FormShowType: iBuilder.BuildFormShowType(ctx),
		}
		layoutItem.Weight = s.BuildLayoutWeight(s.productSort, layoutItem, s.getLayoutWeightByPcId(layoutItem.Groups))
		layouts = s.appendLayout(layouts, layoutItem)
	}

	return s.LayoutBubbleSort(layouts)
}

func (s *DefaultSort) appendLayout(layouts []*proto.NewFormLayout, singleLayout *proto.NewFormLayout) []*proto.NewFormLayout {
	// 置顶 但是不是 导流位 => 合并
	if singleLayout.FormShowType == consts.TopArea && singleLayout.Groups[0].Type != consts.GuideGroupType {
		if s.topBoxLayoutIndex != -1 {
			for _, group := range singleLayout.Groups {
				layouts[s.topBoxLayoutIndex].Groups = append(layouts[s.topBoxLayoutIndex].Groups, group)
			}
		} else {
			layouts = append(layouts, singleLayout)
			s.topBoxLayoutIndex = len(layouts) - 1
		}
	} else {
		layouts = append(layouts, singleLayout)
	}

	return layouts
}

func (s *DefaultSort) LayoutBubbleSort(layouts []*proto.NewFormLayout) []*proto.NewFormLayout {
	// 排序
	sort.SliceStable(layouts, func(i, j int) bool {
		return layouts[i].Weight > layouts[j].Weight
	})

	return layouts
}

func (s *DefaultSort) getLayoutWeightByPcId(Groups []*proto.NewFormGroup) int64 {
	if len(Groups) != 0 && Groups[0] != nil && len(Groups[0].Products) > 0 {
		if w, ok := s.weightMap[Groups[0].Products[0]]; ok {
			return w
		}
	}
	return 0
}
