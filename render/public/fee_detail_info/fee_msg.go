package fee_detail_info

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	"github.com/spf13/cast"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
)

type aPlusFeeMsgV3Adapter struct {
	fee_info_render.PriceDescProvider
	enableAPlusRange bool
	minAPlusFee      float64
}

func (A *aPlusFeeMsgV3Adapter) EnableFeeRange() bool {
	return A.enableAPlusRange
}

func (A *aPlusFeeMsgV3Adapter) GetMinFeeRange() float64 {
	return A.minAPlusFee
}

func GetFeeInfo(ctx context.Context, prov fee_info_render.PriceDescProvider, productInfo *biz_runtime.ProductInfoFull) fee_info_render.FeeInfoResp {

	if estimate_pc_id.EstimatePcIdBargain == prov.GetProductCategory() && productInfo.GetPrivateBizInfo().BargainData.FixPrice != nil { // 改价
		amount := util.RoundAbs(cast.ToFloat64(productInfo.GetPrivateBizInfo().BargainData.FixPrice.EstimateFee), 2)
		productInfo.SetBargainEstimateFee(amount)
		var text string
		var replace map[string]string
		if productInfo.GetPrivateBizInfo().BargainData.SenseConfig != nil {
			text = productInfo.GetPrivateBizInfo().BargainData.SenseConfig.PriceLeftText
			replace = map[string]string{
				"num": cast.ToString(amount),
			}
		} else {
			text = dcmp.GetDcmpPlainContent(ctx, "common-price_desc")
			currencyUnit, _ := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
			replace = map[string]string{
				"total_fee":     cast.ToString(amount),
				"currency_unit": currencyUnit,
			}
		}

		var resp = fee_info_render.FeeInfoResp{
			FeeMsg:    util.ReplaceTag(ctx, text, replace),
			FeeAmount: productInfo.GetPrivateBizInfo().BargainData.FixPrice.EstimateFee,
			FeeType:   consts.FeeTypeDefault,
		}
		return resp
	}
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdLowPriceCarpool {
		if conf := dcmp.GetJSONContentWithPath(ctx, "cap_price-low_carpool_price_desc", nil, "fee_msg"); len(conf) > 0 {
			key, params := prov.GetApolloParams(func(full *biz_runtime.ProductInfoFull) string {
				if full.BaseReqData == nil {
					return ""
				}
				return strconv.FormatInt(full.BaseReqData.PassengerInfo.PID, 10)
			})
			estimateFee := util2.PriceFormat(ctx, params, key, prov.GetEstimateFee(), consts.FeeTypeTwoPrice)
			feeMsg := dcmp.TranslateTemplate(conf, map[string]string{
				"price": estimateFee,
			})

			return fee_info_render.FeeInfoResp{
				FeeMsg:    feeMsg,
				FeeAmount: estimateFee,
			}
		}
	}

	if prov.GetProductCategory() != estimate_pc_id.EstimatePcIdAplus {
		var resp = fee_info_render.FeeInfoResp{}
		resp.FeeMsg, resp.FeeAmount, resp.FeeType = fee_info_render.GetFeeInfo(ctx, prov)
		if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdHuiXuanCar {
			resp.FeeRangeTemplate = dcmp.GetDcmpContent(ctx, "bargain_range-fee_range_template", nil)
		}
		return resp
	}

	enableAPlusRange, minAplusFee := false, 0.0
	if sceneType := productInfo.GetSceneDataBySceneMarkNoPay(map[string]string{
		"is_a_plus_range": "1",
	}); sceneType != nil {
		enableAPlusRange = true
		minAplusFee = sceneType.GetFee()
	}

	adapter := &aPlusFeeMsgV3Adapter{
		PriceDescProvider: prov,
		enableAPlusRange:  enableAPlusRange,
		minAPlusFee:       minAplusFee,
	}

	ret := fee_info_render.GetAPlusFeeInfo(ctx, adapter)
	if !IsRangeTemplateEnabled(ctx, prov) {
		ret.MinFeeAmount = ""
		ret.FeeRangeTemplate = ""
	}
	return ret
}

func IsRangeTemplateEnabled(ctx context.Context, prov fee_info_render.PriceDescProvider) bool {
	key, params := prov.GetApolloParams(func(full *biz_runtime.ProductInfoFull) string {
		if full.BaseReqData == nil {
			return ""
		}
		return strconv.FormatInt(full.BaseReqData.PassengerInfo.PID, 10)
	})
	return apollo.FeatureToggle(ctx, "enable_render_aplus_range", key, params)
}
