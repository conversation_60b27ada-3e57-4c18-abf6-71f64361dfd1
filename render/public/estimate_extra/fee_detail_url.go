package estimate_extra

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/private/simple_estimate/utils"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

const FeeDetailH5New = "fee_detail_h5_new"

type feeDetailUrl struct {
}

func NewFeeDetailUrl() *feeDetailUrl {
	return &feeDetailUrl{}
}

func (f *feeDetailUrl) GetFeeDetailUrl(ctx context.Context) string {
	config, err := utils.GetConfigH5URL(0, FeeDetailH5New)
	if err != nil || !config.Exists() {
		log.Trace.Warnf(ctx, "GetFeeDetailUrl", "get config h5 url fail, err:%v", err)
		return ""
	}

	return config.String()
}
