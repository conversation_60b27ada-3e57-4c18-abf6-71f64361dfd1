package charter_open_products

import (
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/passenger-common/common"
	"github.com/spf13/cast"
	"sort"
	"strings"
)

func (h *CharterOpenProductHandler) GetOpenPcIds(city int, sourceId int) ([]int, error) {
	var res []int

	if h.productInfo == nil {
		return res, fmt.Errorf("empty productInfo")
	}

	for _, prod := range h.productInfo {
		if prod == nil {
			continue
		}

		if prod.IsOpenStatusFailed {
			continue
		}

		if prod.SourceId == cast.ToString(sourceId) && prod.City == cast.ToString(city) {
			res = append(res, util.ToInt(prod.EstimatePcId))
		}
	}

	return res, nil
}

func (h *CharterOpenProductHandler) GetCityList(sourceIds []int) []*proto.CityCategory {
	var (
		res []*proto.CityCategory

		allCityIds        = map[int]struct{}{}
		failedCityIds     []int
		letterCategoryMap = map[string]*proto.CityCategory{}
	)

	if h.productInfo == nil || h.cityConf == nil {
		return res
	}

	// 获取城市全集
	for _, prod := range h.productInfo {
		if prod == nil {
			continue
		}

		if prod.IsOpenStatusFailed {
			continue
		}

		if common.InArrayInt(util.ToInt(prod.SourceId), sourceIds) {
			allCityIds[util.ToInt(prod.City)] = struct{}{}
		}
	}

	// 构造城市列表
	for cityId, _ := range allCityIds {
		if conf, ok := h.cityConf[cityId]; ok && conf != nil {
			category, ok := letterCategoryMap[conf.Letter]
			if !ok || category == nil {
				letterCategoryMap[conf.Letter] = &proto.CityCategory{
					Name:   conf.Letter,
					Cities: []*proto.CityInfo{},
				}
			}

			category, _ = letterCategoryMap[conf.Letter]
			category.Cities = append(category.Cities, &proto.CityInfo{
				Name:   conf.Name,
				Tags:   conf.Tags,
				Cityid: int32(conf.Cityid),
				Lat:    conf.Lat,
				Lng:    conf.Lng,
			})
		} else {
			failedCityIds = append(failedCityIds, cityId)
		}
	}

	if len(failedCityIds) > 0 {
		log.Trace.Warnf(h.ctx, LogTag, "[charter_car_city] cityIds without conf: %+v", failedCityIds)
	}

	for _, category := range letterCategoryMap {
		res = append(res, category)
	}

	// 按照字母正序重排
	sort.Slice(res, func(i, j int) bool {
		return strings.Compare(res[i].Name, res[j].Name) < 0
	})

	return res
}

func (h *CharterOpenProductHandler) GetAllProductInfo() []*ProductCheckInfo {
	return h.productInfo
}

// GetOpenProductIds 获取已开城的业务线
func (h *CharterOpenProductHandler) GetOpenProductIds(sourceIds []int, cityId int) ([]int, []int) {
	var openProductIds, openProductIdsByCity []int
	for sId, item := range h.productCityMap {
		if common.InArrayInt(sId, sourceIds) {
			for pId, cityList := range item {
				openProductIds = append(openProductIds, pId)
				if common.InArrayInt(cityId, cityList) {
					openProductIdsByCity = append(openProductIdsByCity, pId)
				}
			}
		}
	}

	return openProductIds, openProductIdsByCity
}

// GetProductCityListData 获取业务线的城市列表信息  map[int]map[int][]*proto.CityCategory
func (h *CharterOpenProductHandler) GetProductCityListData(sourceIds []int) map[int]map[int][]*proto.CityCategory {
	res := make(map[int]map[int][]*proto.CityCategory, len(sourceIds))
	for _, sId := range sourceIds {
		res[sId] = make(map[int][]*proto.CityCategory, len(h.productCityMap[sId]))
		for pId, cityList := range h.productCityMap[sId] {
			letterCategoryMap := map[string]*proto.CityCategory{}
			for _, cId := range cityList {
				if conf, ok := h.cityConf[cId]; ok && conf != nil {
					category, ok := letterCategoryMap[conf.Letter]
					if !ok || category == nil {
						letterCategoryMap[conf.Letter] = &proto.CityCategory{
							Name:   conf.Letter,
							Cities: []*proto.CityInfo{},
						}
					}
					category, _ = letterCategoryMap[conf.Letter]
					category.Cities = append(category.Cities, &proto.CityInfo{
						Name:   conf.Name,
						Tags:   conf.Tags,
						Cityid: int32(conf.Cityid),
						Lat:    conf.Lat,
						Lng:    conf.Lng,
					})
				}
			}

			for _, category := range letterCategoryMap {
				res[sId][pId] = append(res[sId][pId], category)
			}
			// 按照字母正序重排
			sort.Slice(res[sId][pId], func(i, j int) bool {
				return strings.Compare(res[sId][pId][i].Name, res[sId][pId][j].Name) < 0
			})
		}
	}

	return res
}
