package charter_open_products

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product_center"
	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/utils"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/passenger-common/common"
	"github.com/spf13/cast"
)

type CharterOpenProductHandler struct {
	ctx             context.Context
	openProductConf []*openProductConfig  // 全量开城配置
	cityConf        map[int]*cityConfItem // 城市信息

	productInfo []*ProductCheckInfo // 开城品类

	// 开城状态校验
	needOpenStatusCheck bool

	// 业务线城市构建map
	productCityMap        map[int]map[int][]int
	needBuildPCityListMap bool // 是否需要构建业务线城市map
}

func NewCharterOpenProductHandler(ctx context.Context) *CharterOpenProductHandler {
	return &CharterOpenProductHandler{ctx: ctx}
}

func (h *CharterOpenProductHandler) WithOpenStatusCheck() *CharterOpenProductHandler {
	h.needOpenStatusCheck = true
	return h
}

func (h *CharterOpenProductHandler) ExecCheck() {
	if h.productInfo == nil {
		return
	}

	for _, prod := range h.productInfo {
		conf := prod.rawConf
		if conf == nil {
			continue
		}

		if h.needOpenStatusCheck {
			if conf.Status != ProductOpenStatusOn {
				prod.IsOpenStatusFailed = true
				prod.RemoveFlag = true
			}
		}
	}
}

func (h *CharterOpenProductHandler) Load(city string, sourceID []string, pcID []string, token string) error {
	if err := h.loadOpenProductConf(); err != nil {
		return err
	}

	h.buildProductInfoWithBasicFilter(city, sourceID, pcID, token)
	// 构建业务线的映射map
	if h.needBuildPCityListMap {
		h.BuildProductCityListMap()
	}

	if err := h.loadCityConf(); err != nil {
		return err
	}

	return nil
}

func (h *CharterOpenProductHandler) SetNeedBuildPCityListMap() *CharterOpenProductHandler {
	h.needBuildPCityListMap = true
	return h
}

func (h *CharterOpenProductHandler) buildProductInfoWithBasicFilter(city string, sourceIDs []string, pcIDs []string, token string) {
	if h.openProductConf == nil {
		return
	}

	var ret []*ProductCheckInfo
	for _, conf := range h.openProductConf {
		if conf == nil {
			continue
		}

		// 入参为有效城市（传""表示不做城市过滤）且 配置中城市不为全国配置，则校验
		if city != "" && conf.City != "" && conf.City != "0" {
			if city != conf.City {
				continue
			}
		}

		if sourceIDs != nil && len(sourceIDs) > 0 && conf.SourceId != "" {
			if !util.InArrayStr(conf.SourceId, sourceIDs) {
				continue
			}
		}

		if pcIDs != nil && len(pcIDs) > 0 {
			if !utils.InArray(conf.EstimatePcId, pcIDs) {
				continue
			}
		}

		if len(conf.ApolloToggle) > 0 {
			userInfo, err := getUserInfo(h.ctx, token)
			if err != nil || userInfo == nil {
				continue
			}

			key := cast.ToString(userInfo.UID)
			apolloParam := make(map[string]string)
			apolloParam["uid"] = key
			apolloParam["pid"] = cast.ToString(userInfo.PID)
			apolloParam["phone"] = cast.ToString(userInfo.Phone)

			if !apollo.FeatureToggle(h.ctx, conf.ApolloToggle, key, apolloParam) {
				continue
			}
		}

		ret = append(ret, &ProductCheckInfo{
			City:         conf.City,
			SourceId:     conf.SourceId,
			EstimatePcId: conf.EstimatePcId,
			rawConf:      conf,
		})
	}

	h.productInfo = ret
}

func (h *CharterOpenProductHandler) loadOpenProductConf() error {
	bytes, err := apollo.GetConfigsByNamespace(h.ctx, CharterOpenProductsConfNS)
	if err != nil {
		return fmt.Errorf("getConfigs failed. err=%s", err.Error())
	}

	if len(bytes) == 0 {
		return fmt.Errorf("nil configs")
	}

	var res []*openProductConfig
	err = json.Unmarshal(bytes, &res)
	if err != nil {
		return fmt.Errorf("unmarshal failed. err=%s", err.Error())
	}

	h.openProductConf = res
	return err
}

// 新增城市需要在这里配置
func (h *CharterOpenProductHandler) loadCityConf() error {
	raw := dcmp.GetDcmpContent(h.ctx, CharterCityConfDcmpKey, nil)
	if raw == "" {
		return fmt.Errorf("cityConf get dcmp failed with empty content")
	}

	conf := map[int]*cityConfItem{}
	if err := json.Unmarshal([]byte(raw), &conf); err != nil {
		return fmt.Errorf("cityConf dcmp unmarshal failed. raw=%s, err=%s", raw, err.Error())
	}

	h.cityConf = conf
	return nil
}

// BuildProductCityListMap 构建业务线城市列表map  map[source_id]map[product_id][]int
func (h *CharterOpenProductHandler) BuildProductCityListMap() {
	productCityMap := make(map[int]map[int][]int, 0)
	// 过滤未开城的配置
	for _, conf := range h.productInfo {
		if conf == nil || conf.rawConf == nil || (h.needOpenStatusCheck && conf.rawConf.Status != ProductOpenStatusOn) {
			continue
		}

		nTuple := product_center.GetNTupleByPCId(h.ctx, cast.ToInt64(conf.EstimatePcId))
		if nTuple != nil {
			if _, ok := productCityMap[cast.ToInt(conf.SourceId)]; !ok {
				productCityMap[cast.ToInt(conf.SourceId)] = make(map[int][]int, 0)
			}

			if !common.InArrayInt(cast.ToInt(conf.City), productCityMap[cast.ToInt(conf.SourceId)][cast.ToInt(nTuple.ProductId)]) {
				productCityMap[cast.ToInt(conf.SourceId)][cast.ToInt(nTuple.ProductId)] = append(productCityMap[cast.ToInt(conf.SourceId)][int(nTuple.ProductId)], cast.ToInt(conf.City))
			}
		}
	}

	h.productCityMap = productCityMap
}

func getUserInfo(ctx context.Context, token string) (*passport.UserInfo, error) {
	userInfo, err := passport.GetUserInfo(ctx, token, "")
	if err != nil || userInfo == nil {
		return nil, err
	}

	return userInfo, nil
}
