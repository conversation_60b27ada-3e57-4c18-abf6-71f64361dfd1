package charter_open_products

const (
	CharterOpenProductsConfNS = "charter_open_city_v2"
	CharterCityConfDcmpKey    = "charter-city_list_info"

	ProductOpenStatusOn = 1

	LogTag = "charter_open_products"
)

/* 开城配置 */
type openProductConfig struct {
	City         string `json:"city"`
	SourceId     string `json:"source_id"`
	EstimatePcId string `json:"estimate_pc_id"`
	ApolloToggle string `json:"apollo_toggle"`
	Status       int    `json:"status"`
}

type ProductCheckInfo struct {
	City         string `json:"city"`
	SourceId     string `json:"source_id"`
	EstimatePcId string `json:"estimate_pc_id"`

	// 品类过滤的总标识
	RemoveFlag bool `json:"remove_flag"`

	// 细分过滤条件的标识
	IsOpenStatusFailed bool `json:"is_open_status_failed"`

	rawConf *openProductConfig
}

/* 城市信息 */
type cityConfItem struct {
	Name   string  `json:"name"` // "北京市"
	Tags   string  `json:"tags"` // "BEIJING,北京市",
	Cityid int     `json:"cityid"`
	Lat    float64 `json:"lat"`
	Lng    float64 `json:"lng"`

	Letter string `json:"letter"` // "B" 首字母，后续用于索引
}
