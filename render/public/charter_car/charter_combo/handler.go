package charter_combo

import (
	"context"
)

// CharterComboHandler 处理包车的套餐逻辑
type CharterComboHandler struct {
	ctx context.Context

	openComboIDs map[int]map[int]struct{}

	packageInfo   map[int]map[int]*ComboPackage // map[product_id][combo_id]套餐信息
	timeSliceInfo map[int]*TimeSliceInfo        // map[product_id][combo_id]时间片信息

	needTimeSlice bool // 是否需要获取套餐时间片信息
}

func NewCharterComboHandler(ctx context.Context) *CharterComboHandler {
	return &CharterComboHandler{ctx: ctx}
}

func (h *CharterComboHandler) SetNeedTimeSlice() *CharterComboHandler {
	h.needTimeSlice = true
	return h
}

func (h *CharterComboHandler) Load(productIds []int, area int, district string, timestamp int64, accessKeyId int, appVersion string) error {
	var err error

	h.openComboIDs = loadOpenComboIDs(h.ctx)

	h.packageInfo, err = getComboStrategy(h.ctx, productIds, district, timestamp)
	h.filterPackageInfo()

	if h.needTimeSlice {
		h.timeSliceInfo = loadTimeSliceConf(h.ctx, productIds, area, accessKeyId, appVersion)
		h.filterTimeSliceConf()
	}

	return err
}

func (h *CharterComboHandler) filterPackageInfo() {
	if h.packageInfo == nil || h.openComboIDs == nil {
		return
	}

	for productId, packInfo := range h.packageInfo {
		comboIdSet, ok := h.openComboIDs[productId]
		if !ok || comboIdSet == nil {
			continue
		}

		for comboId, _ := range packInfo {
			_, ok := comboIdSet[comboId]
			if !ok {
				delete(packInfo, comboId)
			}
		}
	}
}

func (h *CharterComboHandler) filterTimeSliceConf() {
	if h.timeSliceInfo == nil || h.openComboIDs == nil {
		return
	}

	for productId, tsConf := range h.timeSliceInfo {
		comboIdSet, ok := h.openComboIDs[productId]
		if !ok || comboIdSet == nil {
			continue
		}

		for comboId, _ := range tsConf.TimeSliceConf {
			_, ok := comboIdSet[comboId]
			if !ok {
				delete(tsConf.TimeSliceConf, comboId)
			}
		}
	}
}
