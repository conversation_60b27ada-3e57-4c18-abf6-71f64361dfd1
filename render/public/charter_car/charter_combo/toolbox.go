package charter_combo

import "git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"

func (h *CharterComboHandler) GetPackageInfo(productId int) map[int]*ComboPackage {
	if h.packageInfo == nil {
		return nil
	}

	pack, ok := h.packageInfo[productId]
	if !ok {
		log.Trace.Warnf(h.ctx, LogTag, "GetPackageInfo empty of product_id=%d", productId)
		return nil
	}

	return pack
}

func (h *CharterComboHandler) GetComboTimeSlice(productId int) map[int]*ComboTimeSlice {
	if h.timeSliceInfo == nil {
		return nil
	}

	conf, ok := h.timeSliceInfo[productId]
	if !ok || conf.TimeSliceConf == nil {
		log.Trace.Warnf(h.ctx, LogTag, "GetComboTimeSlice empty of product_id=%d", productId)
		return nil
	}

	return conf.TimeSliceConf
}

func (h *CharterComboHandler) GetTimePageTitles(productId int) (string, string) {
	if h.timeSliceInfo == nil {
		return "", ""
	}

	conf, ok := h.timeSliceInfo[productId]
	if !ok || conf.TimeSliceConf == nil {
		log.Trace.Warnf(h.ctx, LogTag, "GetComboTimeSlice empty of product_id=%d", productId)
		return "", ""
	}

	return conf.TimePageTitle, conf.TimePageSubTitle
}
