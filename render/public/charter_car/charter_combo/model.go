package charter_combo

import (
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/combo_type"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"
)

const (
	LogTag = "charter_combo"

	CharterComboTimeSliceNS       = "charter_combo_ns"
	CharterComboTimeSliceConfName = "combo_time_slice_conf"

	CharterComboListDcmpKey = "charter-combo_list"
)

var (
	productId2ComboType = map[int]int{
		product_id.ProductIdDefault:       combo_type.ComboTypeRentNew,
		product_id.ProductIdFirstClassCar: combo_type.ComboTypeRented,
	}
)

type ComboPackage struct {
	ComboId  int     `json:"combo_id"`
	Amount   float64 `json:"amount"`           // 金额（元）
	Distance float64 `json:"package_distance"` // 里程（公里）
	Hour     int     `json:"package_hour"`     // 时长（小时）
	Time     float64 `json:"package_time"`     // 时长（分钟) 账单原始时间
}

type ComboTimeSlice struct {
	ComboId         int `json:"combo_id"`
	StartTime       int `json:"start_time"` // 每天起始营业时间点（小时）
	EndTime         int `json:"end_time"`   // 每天最后营业时间点（小时）
	EarliestMinutes int `json:"earliest_minutes"`
	DuringDays      int `json:"during_days"` // 总时间跨度（天数）
	TimeGap         int `json:"time_gap"`    // 时间片切割间隔（分钟）
}

type comboConditionParam struct {
	City        int    `json:"city"`
	ProductId   int    `json:"product_id"`
	AccessKeyId int    `json:"access_key_id"`
	AppVersion  string `json:"app_version"` // App版本校验
}

type comboTimeSliceMaterial struct {
	TimeSliceConf    string `json:"time_slice_conf"`
	TimePageTitle    string `json:"time_page_title"`
	TimePageSubTitle string `json:"time_page_sub_title"`
}

type TimeSliceInfo struct {
	TimeSliceConf    map[int]*ComboTimeSlice
	TimePageTitle    string `json:"time_page_title"`
	TimePageSubTitle string `json:"time_page_sub_title"`
}
