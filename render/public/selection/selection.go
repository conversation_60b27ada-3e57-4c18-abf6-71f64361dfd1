package selection

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
)

type Provider interface {
	GetProductCategory() int64
	GetMultiRequireProduct() []models.RequireProduct
	GetProductCheckStatus() int
}

func GetSelection(ctx context.Context, prov Provider) int32 {
	// athena推荐
	if prov.GetProductCheckStatus() == consts.Checked {
		return consts.Checked
	}

	// 用户勾选
	multiRequireProduct := prov.GetMultiRequireProduct()
	if len(prov.GetMultiRequireProduct()) > 0 {
		for _, product := range multiRequireProduct {
			if prov.GetProductCategory() == product.ProductCategory {
				return consts.Checked
			}
		}
	}

	return consts.UnChecked
}
