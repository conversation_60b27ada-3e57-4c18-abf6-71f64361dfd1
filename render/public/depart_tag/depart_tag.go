package depart_tag

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"math"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	CommonUtil "git.xiaojukeji.com/gulfstream/passenger-common/util"
)

const productCategoryAutoDriving = 99
const productCategoryLowPriceCarpool = 46
const productCategoryInterCityCarpool = 5
const sceneTypeInterCityCarpool = 32
const comboTypeInterCityCarpool = 302
const selfDriveDcmpKey = "config_text-self_drive_depart_tag"
const waitSendCarDcmpKey = "config_text-carpool_wait_send_empty_car_depart_tag"
const waitRewardDcmpKey = "config_text-carpool_wait_reward_depart_tag"
const departTagDcmpKey = "config_text-depart_tag"
const carpoolNewDepartToggle = "ab_carpool_test_new_depart_tag"
const pinCheCheDepartToggle = "gs_pincheche_depart_tag"
const TabIdClassify = "classify"

type DepartTagProvider interface {
	GetProductCategory() int64
	GetEstimateId() string
	GetComboType() int64
	GetCarpoolType() int64
	GetSceneType() int64
	// GetPinchecheEtsInfo() *CarpoolApi.TimeWindow
	GetDiscountSet() *PriceApi.EstimateNewFormDiscountSet
	GetTabId() string
	GetPrivateBizInfo() *models.PrivateBizInfo
	GetClientInfo() *models.ClientInfo
	render.ApolloProvider
}

func GetDepartTag(ctx context.Context, prov DepartTagProvider) *string {
	var departTag string
	// 自动驾驶
	// if estimate_pc_id.EstimatePcIdAutoDriving == prov.GetProductCategory() {
	// 	departTag = dcmp.GetDcmpContent(ctx, selfDriveDcmpKey, nil)
	// }

	// 拼成乐
	if estimate_pc_id.EstimatePcIdLowPriceCarpool == prov.GetProductCategory() {
		departTag = dcmp.GetJSONContentWithPath(ctx, "config_text-depart_tag", nil, "low_price_carpool")
		return &departTag
	}

	// 城际拼车
	if consts.ComboTypeCarpoolInterCity == prov.GetComboType() &&
		consts.CarpoolTypeIntercity == consts.CarpoolType(prov.GetCarpoolType()) {
		departTag = dcmp.GetJSONContentWithPath(ctx, "config_text-depart_tag", nil, "inter_city")
		return &departTag
	}

	// 顺风车
	if estimate_pc_id.EstimatePcIdCarpoolSFCar == prov.GetProductCategory() ||
		estimate_pc_id.EstimatePcIdCarpoolCrossSFCar == prov.GetProductCategory() {
		departTag = dcmp.GetJSONContentWithPath(ctx, "config_text-depart_tag", nil, "sfc")
		return &departTag
	}

	// 智能小巴
	if consts.CarpoolTypeSmartBus == consts.CarpoolType(prov.GetCarpoolType()) {
		departTag = dcmp.GetJSONContentWithPath(ctx, "config_text-depart_tag", nil, "smart_bus")
	}

	return &departTag
}

// IsNewVersionAnyCarFlow (端 >= 6.9.18; 小程序 >= 6.9.80)
func isNewVersionAnyCarFlow(accessKeyId int32, appVersion string) bool {
	if accessKeyId == bizCommonConsts.AccessKeyIDDiDiIos || accessKeyId == bizCommonConsts.AccessKeyIDDiDiAndroid {
		return CommonUtil.VersionCompare(appVersion, "6.9.18", ">=")
	} else if accessKeyId == bizCommonConsts.AccessKeyIDDiDiWechatMini || accessKeyId == bizCommonConsts.AccessKeyIDDiDiAlipayMini {
		return CommonUtil.VersionCompare(appVersion, "6.9.80", ">=")
	}
	return false
}

func getLowPriceCarpooDepartTag(ctx context.Context, prov DepartTagProvider) string {
	var departTag string
	if TabIdClassify == prov.GetTabId() {
		return ""
	}

	departTag = bulidNewEts(prov)

	if "" == departTag {
		departTag = buildWaitSendCar(ctx, prov)
	}

	if "" == departTag {
		departTag = buildNewWaitReward(ctx, prov)
	}

	return ""
}

func bulidNewEts(prov DepartTagProvider) string {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	allow, expParams := apollo.GetParameters(carpoolNewDepartToggle, pidKey, params)
	if allow && expParams != nil && expParams["pincheche_depart_tag_test"] != "" {
		return expParams["pincheche_depart_tag_test"]
	}

	return ""
}

func buildWaitSendCar(ctx context.Context, prov DepartTagProvider) string {
	domain := "passenger"
	key := "low_price_carpool.wait_send_empty_car_time"

	val, err := ufs.GetFeatureV2(ctx, domain, key, map[string]string{"estimate_id": prov.GetEstimateId()})
	if err != nil || "" == val {
		return ""
	}

	log.Trace.Infof(ctx, "getUfs", "domain=%s||keys=%v||eid=%v||resp_data=%v", domain, key, prov.GetEstimateId(), val)

	dcmpInfo := dcmp.GetDcmpContent(ctx, waitSendCarDcmpKey, nil)

	timeSec, _ := strconv.Atoi(val)

	replace := map[string]string{
		"wait_time": strconv.Itoa(int(math.Ceil(float64(timeSec) / 60))),
	}

	return util.ReplaceTag(ctx, dcmpInfo, replace)

}

func buildNewWaitReward(ctx context.Context, prov DepartTagProvider) string {
	discount := prov.GetDiscountSet()

	if discount == nil || discount.SpsLikeWaitReward == nil {
		return ""
	}

	reward := discount.SpsLikeWaitReward

	amount := util.FormatPrice(float64(reward.Amount)/100, 2)

	timeWait := math.Ceil(float64(reward.WaitTime) / 60)

	dcmpInfo := dcmp.GetDcmpContent(ctx, waitRewardDcmpKey, nil)

	replace := map[string]string{
		"amount":    amount,
		"wait_time": strconv.Itoa(int(timeWait)),
	}

	return util.ReplaceTag(ctx, dcmpInfo, replace)
}

//func buildOldEtsInfo(ctx context.Context, prov DepartTagProvider) string {
//	var departTag string
//	etsInfo := prov.GetPinchecheEtsInfo()
//	ets := etsInfo.GetEndTime()
//
//	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
//
//	allow, expParams := apollo.GetParameters(pinCheCheDepartToggle, pidKey, params)
//
//	if allow && expParams != nil {
//		if ets > 0 {
//			replace := map[string]string{
//				"ets": strconv.Itoa(int(ets)),
//			}
//			departTag = util.ReplaceTag(ctx, expParams["depart_tag_ets"], replace)
//		} else {
//			departTag = expParams["depart_tag_default"]
//		}
//	} else {
//		// dcmpInfo := dcmp.GetDcmpContent(ctx, departTagDcmpKey, nil)
//	}
//	return departTag
//}
