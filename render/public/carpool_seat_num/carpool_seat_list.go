package carpool_seat_num

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"

	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type SeatListProvider interface {
	GetUserSelectSeatNum() int32
	GetProductCategory() int64
	GetCarpoolType() int64
}

func GetCarpoolSeatNumV3(ctx context.Context, prov SeatListProvider) []*proto.NewFormCarpoolSeatOption {
	if !util.InArrayInt64(prov.GetProductCategory(), []int64{
		estimate_pc_id.EstimatePcIdCarpoolStation,
		estimate_pc_id.EstimatePcIdTaxiCarpool}) &&
		!carpool.IsMiniBus(int(prov.GetCarpoolType())) {
		return nil
	}

	var carpoolSeatNum = prov.GetUserSelectSeatNum()
	if carpoolSeatNum <= 0 {
		carpoolSeatNum = 1
	}

	originRsp, err := buildCarpoolSeats(ctx, carpoolSeatNum)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "err:%v", err)
		return nil
	}

	if originRsp != nil && len(originRsp) > 0 {
		var res = make([]*proto.NewFormCarpoolSeatOption, 0)
		for _, seatInfo := range originRsp {
			res = append(res, &proto.NewFormCarpoolSeatOption{
				Label:    seatInfo.GetLabel(),
				Value:    seatInfo.GetValue(),
				Selected: seatInfo.GetSelected(),
			})
		}
		return res
	}

	return nil
}
