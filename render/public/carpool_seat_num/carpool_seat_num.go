package carpool_seat_num

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/biz-lib-go/utils/tagreplace"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/bitly/go-simplejson"
)

type SeatNumProvider interface {
	render.BaseProvider
	render.ProductProvider
	render.RequestProvider
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
}

//func GetCarpoolSeatNum(ctx context.Context, prov SeatNumProvider) []*proto.SimpleFormCarpoolSeatOption {
//	if !carpool.IsCarpool(prov.GetCarpoolType()) {
//		return nil
//	}
//
//	var carpoolSeatNum int32
//	if prov.GetCarpoolSeatNum() != 0 {
//		carpoolSeatNum = prov.GetCarpoolSeatNum()
//	}
//	if prov.GetPassengerCount() != nil && carpoolSeatNum == 0 &&
//		carpool.IsLowPriceCarpoolByInfos(prov.GetComboType(), prov.GetProductId(), prov.GetCarpoolType(), prov.GetRequireLevel()) {
//		carpoolSeatNum = *prov.GetPassengerCount()
//	}
//	carpoolSeats, err := buildCarpoolSeats(ctx, carpoolSeatNum)
//	if err != nil {
//		log.Trace.Warnf(ctx, trace.DLTagUndefined, "err:%v", err)
//	}
//	return carpoolSeats
//}

func buildCarpoolSeats(ctx context.Context, carpoolSeatNum int32) ([]*proto.CarpoolSeatOption, error) {
	config := dcmp.GetDcmpContent(ctx, "config_text-pget_order_match_info", nil)
	jsonConfig, err := simplejson.NewJson([]byte(config))
	if err != nil {
		return nil, err
	}
	textModel, err := jsonConfig.Get("guide_carpool_confirm_seat_num").String()
	if err != nil {
		return nil, err
	}
	tr := tagreplace.NewDefaultTagReplacer()
	oneSeatText := tr.ReplaceTag(textModel, map[string]string{"num": "1"})
	var oneIsSelected bool
	var twoIsSelected bool
	// 兜底第一次预估没有座位数问题
	if 0 == carpoolSeatNum || 1 == carpoolSeatNum {
		oneIsSelected = true
	}
	twoSeatText := tr.ReplaceTag(textModel, map[string]string{"num": "2"})
	if 2 == carpoolSeatNum {
		twoIsSelected = true
	}

	res := []*proto.CarpoolSeatOption{{
		Label:    oneSeatText,
		Value:    1,
		Selected: oneIsSelected,
	}, {
		Label:    twoSeatText,
		Value:    2,
		Selected: twoIsSelected,
	}}
	return res, nil
}
