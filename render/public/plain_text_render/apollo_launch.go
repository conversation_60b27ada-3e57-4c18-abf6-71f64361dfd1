package plain_text_render

import (
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type ApolloParams struct {
	CityId      string `json:"city_id"`
	Pid         string `json:"pid"`
	Phone       string `json:"phone"`
	AccessKeyID string `json:"access_key_id"`
	AppVersion  string `json:"app_version"`
}

const emptyStr = ""

func ApolloLaunch(toggleName string, paramKey string, abUser render.ApolloProvider) string {
	pidKey, apolloParams := abUser.GetApolloParams(biz_runtime.WithPIDKey)
	if apolloParams == nil {
		return emptyStr
	}

	user := model.NewUser(pidKey)
	for k, v := range apolloParams {
		user.With(k, v)
	}

	toggle, err := apollo.FeatureToggle(toggleName, user)
	if err == nil && toggle.IsAllow() {
		assign := toggle.GetAssignment()
		return assign.GetParameter(paramKey, emptyStr)
	} else {
		return emptyStr
	}
}
