package plain_text_render

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

const defaultProduct = "0" //兜底的品类文案配置key

type ProductInfo interface {
	GetProductCategory() int64
}

/*
*
在dcmp中配置如下格式的配置，优先去存在的品类配置，无则走0 兜底文案，不配置0 返回""

	{
		0: "滴滴",
		1: "快车",
		3: "拼车"
	}
*/
func DCMPSimpleStringByProduct(ctx context.Context, dcmpKey string, info ProductInfo) string {
	var (
		confMap = make(map[string]string)
		pc      = strconv.FormatInt(info.GetProductCategory(), 10)
	)
	confStr := dcmp.GetDcmpContent(ctx, dcmpKey, nil)
	err := json.Unmarshal([]byte(confStr), &confMap)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrJsonUnMarshal, "dcmp key %s with err %v", dcmpKey, err)
		return ""
	}
	if val, ok := confMap[pc]; ok {
		return val
	}

	if val, ok := confMap[defaultProduct]; ok {
		return val
	}

	return ""
}
