package force_notice_toast

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type forceNoticeToast struct {
	product *biz_runtime.ProductInfoFull
}

func NewForceNoticeToast(product *biz_runtime.ProductInfoFull) *forceNoticeToast {
	return &forceNoticeToast{
		product: product,
	}
}

func (f *forceNoticeToast) GetForceNoticeToast(ctx context.Context) *string {
	if f.product == nil || f.product.BaseReqData == nil {
		return nil
	}

	if f.product.BaseReqData.CommonBizInfo.IsRestSeatInfo {
		toast := dcmp.GetDcmpPlainContent(ctx, "intercity_sku-seat_reset")
		return &toast
	}

	return nil
}
