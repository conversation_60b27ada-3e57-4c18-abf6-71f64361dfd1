package sub_title_list

import (
	"context"
	"encoding/json"
	"fmt"
	"math"

	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	midl "git.xiaojukeji.com/dirpc/dirpc-go-http-Member"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	CommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	anyCarSubTitleList            = "anycar_v3-sub_title_list"
	anyCarDefaultSubTitleList     = "anycar_v3-default_sub_title_list"
	anyCarTaxiDefaultSubTitleList = "anycar_v3-taxi_default_sub_title_list"

	MemberRapidResponse    = "rapid_response"
	MemberPriorityResponse = "priority_response"

	taxi_peak_fee         = "taxi_peak_fee"
	taxi_holiday_peak_fee = "taxi_peak_fee_holiday"
)

type SubTitleListProvider interface {
	render.ProductProvider
	render.ApolloProvider
	GetMultiRequireProduct() []models.RequireProduct
	GetPrivateBizInfo() *models.PrivateBizInfo
	GetMemberProfile() *midl.V1QueryInfo
	GetMemberLevel() int32
	GetOrderType() int16
	GetIsShowFemaleDriver() int64
	GetEtp() int64
	GetBillTaxiPeakFee() float64
	GetFontScaleType() int32

	IsTripcloud(context.Context) bool
}

func GetSubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	// 大字版不返回
	if prov.GetFontScaleType() != consts.NormalFontSize {
		return nil
	}

	// 与平台无车赔标签互斥
	if prov.GetPrivateBizInfo() != nil && len(prov.GetPrivateBizInfo().CompensationInfo) != 0 {
		if decision, ok := prov.GetPrivateBizInfo().CompensationInfo[compensation.NormalNoAnswerCompensation]; ok && decision != nil && decision.Decision == 1 {
			return nil
		}
	}

	subtitleList := make([]*proto.SubTitle, 0)

	//自营subtitle
	switch prov.GetProductCategory() {
	case estimate_pc_id.EstimatePcIdAplus:
		subtitleList = getAPlusSubTitleList(ctx, prov)
	case estimate_pc_id.EstimatePcIdPremiumComfort, estimate_pc_id.EstimatePcIdPremiumBusiness:
		subtitleList = getPremiumSubTitleList(ctx, prov)
	case estimate_pc_id.EstimatePcIdUnione,
		estimate_pc_id.EstimatePcIdUnioneSpecialPrice,
		estimate_pc_id.EstimatePcIdYouxuanTaxi,
		estimate_pc_id.EstimatePcIdTaxiMarketisationPutong,

		estimate_pc_id.EstimatePcIdFastTaxi,
		estimate_pc_id.EstimatePcIdTaxiMarketisationYouxuan:
		subtitleList = getTaxiSubTitleList(ctx, prov)
	case estimate_pc_id.EstimatePcIdMinibusOne,
		estimate_pc_id.EstimatePcIdMinibusTwo,
		estimate_pc_id.EstimatePcIdMinibusThree:
		subtitleList = getMinibusSubTitleList(ctx, prov)
	case estimate_pc_id.EstimatePcIdBargain:
		subtitleList = getBargainSubTitleList(ctx, prov)
	case estimate_pc_id.EstimatePcIdLowPriceCarpool:
		subtitleList = getLowCarpoolSubTitleList(ctx, prov)
	default:
		subtitleList = getDefaultSubTitleList(ctx, prov)
	}

	//三方subtitle
	if prov.IsTripcloud(ctx) {
		subtitleList = getThirtyPartySubTitleList(ctx, prov)
	}

	// 香港三方subtitle
	if product.IsHongKongThird(CommonConsts.ProductID(prov.GetProductId())) {
		subtitleList = getHKThirtySubTitleList(ctx, prov)
	}

	if len(subtitleList) <= 0 {
		return nil
	}

	return subtitleList
}

func getBargainSubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	subTitles := make([]*proto.SubTitle, 0)

	if prov.GetPrivateBizInfo() != nil && prov.GetPrivateBizInfo().SenseConfig != nil {
		subTitle := &proto.SubTitle{
			Content:     prov.GetPrivateBizInfo().SenseConfig.SubTitle,
			FontColor:   prov.GetPrivateBizInfo().SenseConfig.FontColor,
			BorderColor: prov.GetPrivateBizInfo().SenseConfig.BorderColor,
		}
		subTitles = append(subTitles, subTitle)
		return subTitles
	}

	// 兜底
	subTitle := getDedaultSubTitle(ctx, prov.GetProductCategory())
	if subTitle != nil {
		subTitles = append(subTitles, subTitle)
		return subTitles
	}
	return nil

}

func getDefaultSubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	if prov.GetPrivateBizInfo() == nil {
		return nil
	}

	subTitles := make([]*proto.SubTitle, 0)

	//女司机优先
	femaleDriverFirst := getFemaleDriverFirstSubTitle(ctx, prov)
	if femaleDriverFirst != nil {
		subTitles = append(subTitles, femaleDriverFirst)
		return subTitles
	}

	subTitle := buildSubTitleByTag(ctx, prov, compensation.NoAnswerCompensation)
	if subTitle != nil {
		subTitles = append(subTitles, subTitle)
		return subTitles
	}

	fastCarFastWay := getFastCarMemberPrivilegeSubTitle(ctx, prov)
	if fastCarFastWay != nil {
		subTitles = append(subTitles, fastCarFastWay)
		return subTitles
	}

	// 兜底
	subTitle = getDedaultSubTitle(ctx, prov.GetProductCategory())
	if subTitle != nil {
		subTitles = append(subTitles, subTitle)
		return subTitles
	}

	return subTitles
}

func getThirtyPartySubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	subTitles := make([]*proto.SubTitle, 0)
	subTitle := buildThirtyPartyTag(ctx, prov)
	if subTitle != nil {
		subTitles = append(subTitles, subTitle)
		return subTitles
	}

	return []*proto.SubTitle{{
		FontColor:              "",
		BackgroundColor:        "",
		BorderColor:            "",
		BackgroundTransparency: "",
		Content:                dcmp.GetDcmpContent(ctx, "guide_anycar-business_label_text_with_trip_cloud", nil),
	}}
}

func getHKThirtySubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	subTitles := make([]*proto.SubTitle, 0)
	dcmpConf := dcmp.GetJSONContentWithPath(ctx, "hk_product-estimate_sub_title", nil, fmt.Sprintf("%d.%d.infos", prov.GetProductId(), prov.GetComboType()))
	err := json.Unmarshal([]byte(dcmpConf), &subTitles)
	if err != nil {
		return []*proto.SubTitle{}
	}
	return subTitles
}

func buildSubTitleByTag(ctx context.Context, prov SubTitleListProvider, tag string) *proto.SubTitle {
	if prov.GetPrivateBizInfo() == nil || len(prov.GetPrivateBizInfo().CompensationInfo) == 0 {
		return nil
	}

	text := dcmp.GetJSONMap(ctx, anyCarSubTitleList, tag)
	config := ""

	if decision, ok := prov.GetPrivateBizInfo().CompensationInfo[tag]; ok && decision != nil && decision.Decision == 1 {
		bizName, bizNameOk := util.MapInterface2String(decision.Extra, "biz_name")
		privilegeSource, privilegeSourceOk := util.MapInterface2String(decision.Extra, "privilege_source")
		if privilegeSourceOk && len(privilegeSource) > 0 {
			config = text[privilegeSource].String()
		} else {
			config = text["default"].String()
		}

		subTitle := &proto.SubTitle{}
		err := json.Unmarshal([]byte(config), subTitle)
		if err != nil {
			return nil
		}

		if bizNameOk && len(bizName) > 0 {
			subTitle.Content = bizName
		}

		subTitle.Key = util.StringPtr(compensation.NoAnswerCompensation)

		return subTitle
	}

	return nil
}

func getAPlusSubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	subTitles := make([]*proto.SubTitle, 0)

	//女司机优先
	femaleDriverFirst := getFemaleDriverFirstSubTitle(ctx, prov)
	if femaleDriverFirst != nil {
		subTitles = append(subTitles, femaleDriverFirst)
	}

	noAnswerSubTitle := buildSubTitleByTag(ctx, prov, compensation.NoAnswerCompensation)
	if noAnswerSubTitle != nil {
		subTitles = append(subTitles, noAnswerSubTitle)
	}

	noCarAPlus := buildCompensationTag(ctx, prov, compensation.BusinessAPlusInsurance)
	if noCarAPlus != nil {
		subTitles = append(subTitles, noCarAPlus)
	}

	return subTitles
}

func getPremiumSubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	subTitles := make([]*proto.SubTitle, 0)

	//女司机优先
	femaleDriverFirst := getFemaleDriverFirstSubTitle(ctx, prov)
	if femaleDriverFirst != nil {
		subTitles = append(subTitles, femaleDriverFirst)
		return subTitles
	}

	// 快速通道
	fastWay := getMemberPrivilegeSubTitle(ctx, prov)
	if fastWay != nil {
		subTitles = append(subTitles, fastWay)
		return subTitles
	}

	// 误机险
	airportInsurance := buildCompensationTag(ctx, prov, compensation.BusinessAirportInsurance)
	if airportInsurance != nil {
		subTitles = append(subTitles, airportInsurance)
		return subTitles
	}

	return subTitles
}

func getMinibusSubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	if conf := dcmp.GetJSONMap(ctx, anyCarSubTitleList, "minibus"); len(conf) > 0 {
		subTitles := make([]*proto.SubTitle, 0)
		if prov.GetEtp() >= 0 {
			subTitles = append(subTitles, &proto.SubTitle{
				Content: util.ReplaceTag(ctx, conf["content"].String(), map[string]string{
					"amount": util.FormatPriceCeil(float64(prov.GetEtp())/60, 0)}),
			})
		}

		return subTitles
	}
	return nil
}

func getTaxiSubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	subTitles := make([]*proto.SubTitle, 0)

	//女司机优先
	femaleDriverFirst := getFemaleDriverFirstSubTitle(ctx, prov)
	if femaleDriverFirst != nil {
		subTitles = append(subTitles, femaleDriverFirst)
		return subTitles
	}

	subTitle := buildSubTitleByTag(ctx, prov, compensation.NoAnswerCompensation)
	if subTitle != nil {
		subTitles = append(subTitles, subTitle)
		return subTitles
	}

	// 峰期加价标签
	taxiPeak := getTaxiPeakFeeSubTitleList(ctx, prov)
	if taxiPeak != nil {
		return []*proto.SubTitle{taxiPeak}
	}

	defaultSubTitle := buildTaxiDefaultTag(ctx, prov)
	if defaultSubTitle != nil {
		subTitles = append(subTitles, defaultSubTitle)
		return subTitles
	}

	return subTitles
}

func getMemberPrivilegeSubTitle(ctx context.Context, prov SubTitleListProvider) *proto.SubTitle {
	if prov.GetOrderType() != consts.RealTimeOrder {
		return nil
	}

	memberInfo := prov.GetMemberProfile()
	if memberInfo == nil || memberInfo.Privileges == nil {
		return nil
	}

	var fastWayInfo *GoMember.PrivInfo
	privileges := memberInfo.Privileges
	if privilegeInfo, ok := privileges.(map[string]*GoMember.PrivInfo); ok {
		if fastWay, ok1 := privilegeInfo["fast_way"]; ok1 {
			fastWayInfo = fastWay
		}
	}

	if fastWayInfo == nil || fastWayInfo.Type == "" {
		return nil
	}

	if fastWayInfo.Type == MemberRapidResponse || fastWayInfo.Type == MemberPriorityResponse {
		config := dcmp.GetJSONContentWithPath(ctx, anyCarSubTitleList, nil, fastWayInfo.Type)
		subTitle := &proto.SubTitle{}
		err := json.Unmarshal([]byte(config), subTitle)
		if err != nil {
			return nil
		}

		return subTitle
	}

	return nil
}

func getFemaleDriverFirstSubTitle(ctx context.Context, prov SubTitleListProvider) *proto.SubTitle {
	var (
		isShow bool
	)
	if prov.GetPrivateBizInfo() == nil || len(prov.GetPrivateBizInfo().CustomFeatureList) == 0 {
		return nil
	}

	if prov.GetIsShowFemaleDriver() == consts.UnChecked {
		return nil
	}

	customFeatureList := prov.GetPrivateBizInfo().CustomFeatureList
	for _, customFeature := range customFeatureList {
		if customFeature.ServiceId == consts.ServiceIdFemaleDriver {
			isShow = true
			break
		}
	}

	if !isShow {
		return nil
	}

	text := dcmp.GetJSONMap(ctx, anyCarDefaultSubTitleList, "female_driver_first")
	if len(text) <= 0 {
		return nil
	}

	subTitle := &proto.SubTitle{
		Content:     text["content"].String(),
		FontColor:   text["font_color"].String(),
		BorderColor: text["border_color"].String(),
	}

	return subTitle
}

func getFastCarMemberPrivilegeSubTitle(ctx context.Context, prov SubTitleListProvider) *proto.SubTitle {
	if prov.GetOrderType() != consts.RealTimeOrder {
		return nil
	}

	if prov.GetProductCategory() != estimate_pc_id.EstimatePcIdFastCar {
		return nil
	}

	// 功能开关
	apolloKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)

	// 开城开关
	isAllow := apollo.FeatureToggle(ctx, "toggle_fastwayplus_switch", apolloKey, params)
	if !isAllow {
		return nil
	}

	levelID := prov.GetMemberLevel()
	text := dcmp.GetJSONMap(ctx, anyCarSubTitleList, "fast_car_fastwayplus")
	config := ""
	config = text[util.ToString(levelID)].String()
	subTitle := &proto.SubTitle{}
	err := json.Unmarshal([]byte(config), subTitle)
	if err != nil {
		return nil
	}

	return subTitle
}

func getDedaultSubTitle(ctx context.Context, productCategory int64) *proto.SubTitle {
	text := dcmp.GetJSONMap(ctx, anyCarDefaultSubTitleList, util.ToString(productCategory))
	if len(text) <= 0 {
		return nil
	}

	subTitle := &proto.SubTitle{
		Content:     text["content"].String(),
		FontColor:   text["font_color"].String(),
		BorderColor: text["border_color"].String(),
	}

	return subTitle
}

func buildCompensationTag(ctx context.Context, prov SubTitleListProvider, tag string) *proto.SubTitle {
	if prov.GetPrivateBizInfo() == nil || len(prov.GetPrivateBizInfo().CompensationInfo) == 0 {
		return nil
	}

	text := dcmp.GetJSONMap(ctx, anyCarSubTitleList, tag)
	config := ""

	if decision, ok := prov.GetPrivateBizInfo().CompensationInfo[tag]; ok && decision != nil && decision.Decision == 1 {
		bizName, bizNameOk := util.MapInterface2String(decision.Extra, "biz_name")
		config = text["default"].String()
		subTitle := &proto.SubTitle{}
		err := json.Unmarshal([]byte(config), subTitle)
		if err != nil {
			return nil
		}

		if bizNameOk && len(bizName) > 0 {
			subTitle.Content = bizName
		}

		subTitle.Key = util.StringPtr(tag)

		return subTitle
	}

	return nil
}

func buildThirtyPartyTag(ctx context.Context, prov SubTitleListProvider) *proto.SubTitle {
	type ItemConfig struct {
		BackgroundColor string `json:"background_color"`
		BorderColor     string `json:"border_color"`
		Content         string `json:"content"`
		FontColor       string `json:"font_color"`
	}

	type SubTitleConf struct {
		ItemConfig *ItemConfig `json:"item_config"`
	}

	// 按照pcid、lang获取物料配置
	condition := map[string]string{"product_category": util.ToString(prov.GetProductCategory()), "lang": reqctx.GetLang(ctx)}
	configs, err := apollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, "estimate_formdata_materiel", condition, reqctx.GetLang(ctx))

	if configs == nil || err != nil || len(configs) < 1 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo estimate_formdata_materiel read err with %v and conf %v", err, configs)
		return nil
	}

	var subTitleConf = make([]*SubTitleConf, 0)
	err = configs[0].GetJsonValue("sub_title_text", &subTitleConf)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo estimate_formdata_materiel read err with %v and conf %v", err, configs)
		return nil
	}

	subTitle := &proto.SubTitle{}
	for _, item := range subTitleConf {
		if item == nil || item.ItemConfig == nil {
			return nil
		}

		subTitle = &proto.SubTitle{
			BackgroundColor: item.ItemConfig.BackgroundColor,
			BorderColor:     item.ItemConfig.BorderColor,
			Content:         item.ItemConfig.Content,
			FontColor:       item.ItemConfig.FontColor,
		}
	}

	return subTitle
}

func buildTaxiDefaultTag(ctx context.Context, prov SubTitleListProvider) *proto.SubTitle {
	conf := dcmp.GetJSONContentWithPath(ctx, anyCarTaxiDefaultSubTitleList, nil, util.ToString(prov.GetProductCategory()))
	if conf == "" {
		return nil
	}

	subTitle := &proto.SubTitle{}
	err := json.Unmarshal([]byte(conf), subTitle)
	if err != nil {
		return nil
	}

	if subTitle.Content == "" {
		return nil
	}

	// 188在盒子内展示标签，194盒子内 非盒子都展示标签
	if (prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiMarketisationPutong &&
		prov.GetSubGroupId() == consts.SubGroupIdTaxiPricingBox) || prov.GetProductCategory() == estimate_pc_id.EstimatePcIdFastTaxi {
		return subTitle
	}

	//非盒子不展示
	if prov.GetSubGroupId() != consts.SubGroupIdTaxiPricingBox && prov.GetSubGroupId() != consts.SubGroupIdUnitaxi {
		return nil
	}

	apolloKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	isOpen := apollo.FeatureToggle(ctx, "taxi_v3_sub_title_list_toggle", apolloKey, params)
	if !isOpen {
		return nil
	}

	return subTitle
}

func getLowCarpoolSubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	if prov.GetPrivateBizInfo() == nil {
		return nil
	}

	subTitles := make([]*proto.SubTitle, 0)
	subTitle := new(proto.SubTitle)
	config := dcmp.GetJSONMap(ctx, anyCarSubTitleList, "low_price_carpool")
	subTitle.Content = config["content"].String()

	// 有派空车权益，则感知【N分钟未拼成也找车】
	// 畅拼卡
	if len(config["content_carpool_go_right_card"].String()) > 0 &&
		prov.GetPrivateBizInfo().VcardData != nil &&
		prov.GetPrivateBizInfo().VcardData.UsableCard != nil &&
		prov.GetPrivateBizInfo().VcardData.UsableCard.SendEmptyCarTime > 0 &&
		prov.GetPrivateBizInfo().VcardData.UsableCard.SendEmptyCarSecond > 0 {
		iWaitMinute := int(math.Ceil(float64(prov.GetPrivateBizInfo().VcardData.UsableCard.SendEmptyCarSecond) / 60.0))
		subTitle.Content = fmt.Sprintf(config["content_carpool_go_right_card"].String(), iWaitMinute)
	}

	// 等必走 low_price_carpool.wait_send_empty_car_time ufs中有值说明命中等必走 存储的是倒计时
	if len(config["content_wait_minute"].String()) > 0 &&
		prov.GetPrivateBizInfo().WaitMinuteSendEmptyCarTime != 0 &&
		prov.GetPrivateBizInfo().WaitMinuteSendEmptyCarTime > 0 {
		iWaitMinute := int(math.Ceil(float64(prov.GetPrivateBizInfo().WaitMinuteSendEmptyCarTime) / 60.0))
		subTitle.Content = fmt.Sprintf(config["content_wait_minute"].String(), iWaitMinute)
	}

	subTitles = append(subTitles, subTitle)
	return subTitles
}

// getTaxiPeakFeeSubTitleList 获取峰期加价标签
func getTaxiPeakFeeSubTitleList(ctx context.Context, prov SubTitleListProvider) *proto.SubTitle {
	bizInfo := prov.GetPrivateBizInfo()
	if bizInfo == nil || bizInfo.TaxiSps == nil {
		return nil
	}
	// 没有信息费或者有信息费有出口
	if bizInfo.TaxiSps.HasFee != 1 || bizInfo.TaxiSps.CanSelect == 1 {
		return nil
	}
	price := prov.GetBillTaxiPeakFee()
	if int64(price*100) <= 0 {
		return nil
	}
	// 根据是否是节假日更改名称
	typeName := taxi_peak_fee
	if bizInfo.TaxiSps.IsHoliday == 1 {
		typeName = taxi_holiday_peak_fee
	}
	text := dcmp.GetJSONMap(ctx, anyCarDefaultSubTitleList, typeName)
	if len(text) == 0 {
		return nil
	}
	subTitle := &proto.SubTitle{
		Content:     text["content"].String(),
		FontColor:   text["font_color"].String(),
		BorderColor: text["border_color"].String(),
	}
	return subTitle
}
