package render

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/omega"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	apolloConfig "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"strconv"
)

func Render(ctx context.Context, estimateInfo *proto.NewFormEstimateResponse, req *proto.AnyCarEstimateV4Req) *proto.AnyCarEstimateAppendCarForm {
	var (
		isSelect int32
	)

	// 获取物料配置
	config, err := GetRenderMaterial(ctx)
	if err != nil {
		return nil
	}

	//埋点数据整理
	omegaInfo := map[string]interface{}{}

	for pcID, data := range estimateInfo.RawEstimateData {
		estimateData, ok := estimateInfo.EstimateData[pcID]
		if ok && estimateData != nil {
			isSelect = estimateData.IsSelected
		}

		omegaInfo[strconv.Itoa(int(pcID))] = map[string]string{
			"estimate_id":       data.EstimateId,
			"estimate_trace_id": estimateInfo.EstimateTraceId,
			"is_selected":       util.ToString(isSelect),
		}
	}

	omegaInfoData, err1 := json.Marshal(omegaInfo)
	if err1 != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "omegaInfo jsonMarshal fail")
	}

	resp := &proto.AnyCarEstimateAppendCarForm{
		Title:        config.Title,
		EstimateInfo: estimateInfo,
		OmegaInfo: &proto.OmegaInfo{
			Key: omega.GetOmegaSourceReplace(ctx, consts.OmegaSwKey, req.AccessKeyId),
			Params: map[string]interface{}{
				"form_source_type":  req.FormSourceType,
				"estimate_trace_id": estimateInfo.EstimateTraceId,
				"estimate_info":     string(omegaInfoData),
				"guide_pos":         9,
				"is_ck":             1,
			},
		},
		Button: &proto.AppendCarButton{
			ActionType: cast.ToInt32(config.Button.ActionType),
			Text:       config.Button.Text,
			ActionOmega: &proto.OmegaInfo{
				Key: omega.GetOmegaSourceReplace(ctx, consts.OmegaCkKey, req.AccessKeyId),
				Params: map[string]interface{}{
					"form_source_type": req.FormSourceType,
					"is_ck":            1,
					"guide_pos":        9,
				},
			},
			Style: &proto.AppendCarButtonStyle{
				BorderColor:     config.Button.Style.BorderColor,
				FontColor:       config.Button.Style.FontColor,
				BgGradientColor: config.Button.Style.BgGradientColor,
				BoxShadowColor:  config.Button.Style.BoxShadowColor,
			},
		},
		TopButton: &proto.TopButton{
			ActionType: cast.ToInt32(config.TopButton.ActionType),
			Text:       config.TopButton.Text,
		},
		ClickTitle: config.ClickTitle,
	}

	for _, data := range estimateInfo.EstimateData {
		val, ok := data.PNewOrderParams["is_default_auth"]
		if ok && val == "1" {
			resp.AuthButton = &proto.AuthButton{
				AuthButtonText: config.AuthButton.AuthButtonText,
			}
		}
	}

	// 对于高版本替换标题文本
	if (util.IsNA(req.AccessKeyId) && util.VersionCompare(req.AppVersion, "6.9.9") >= 0) ||
		(util.IsMini(req.AccessKeyId) && util.VersionCompare(req.AppVersion, "6.9.35") >= 0) {
		if len(config.TitleV2) > 0 {
			resp.Title = config.TitleV2
		}
	}

	return resp
}

func GetRenderMaterial(ctx context.Context) (*models.AnyCarEstimateAppendCarFormMaterial, error) {
	config, err := apolloConfig.GetConfig("append_car_config", "append_car_material_config")
	if err != nil {
		return nil, err
	}
	marshal, marshalErr := json.Marshal(config.GetJsonConfigs())
	if marshalErr != nil {
		return nil, marshalErr
	}
	configMaterial := &models.AnyCarEstimateAppendCarFormMaterial{}
	unmarshalErr := json.Unmarshal(marshal, configMaterial)
	if unmarshalErr != nil {
		return nil, errors.New(fmt.Sprintf("append_car_material_config GetMaterial err:%v", unmarshalErr))
	}
	return configMaterial, nil
}
