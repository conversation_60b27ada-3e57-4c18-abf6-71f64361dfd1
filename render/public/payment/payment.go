package payment

import (
	"context"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"sort"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type PaymentOption struct {
	PaymentType      int32
	BusinessConstSet int32
	ChannelName      string
}

type UserPayInfoProvider interface {
	GetAllPaymentOptions() []PaymentOption
	GetCurrentPaymentType() int
}

// UserPayInfo 支付方式
// 渲染支付方式列表, 以及当前值
func UserPayInfo(ctx context.Context, prov UserPayInfoProvider) *proto.PaymentOptionModule {
	opts := prov.GetAllPaymentOptions()
	pl := make([]*proto.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		tmp := &proto.PaymentOption{
			Msg:              opt.ChannelName,
			Tag:              opt.PaymentType,
			Selected:         false,
			BusinessConstSet: strconv.Itoa(int(opt.BusinessConstSet)),
		}
		if opt.PaymentType == int32(prov.GetCurrentPaymentType()) {
			tmp.Selected = true
			tmp.IsSelected = 1
		}
		pl = append(pl, tmp)
	}
	return &proto.PaymentOptionModule{
		Title:       dcmp.GetJSONContentWithPath(ctx, "payment-user_pay_info", nil, "title"),
		SubTitle:    dcmp.GetJSONContentWithPath(ctx, "payment-user_pay_info", nil, "sub_title"),
		PaymentList: pl,
	}
}

func GetPayInfo(ctx context.Context, prov UserPayInfoProvider) *proto.NewFormUserPayInfo {
	res := &proto.NewFormUserPayInfo{}
	opts := prov.GetAllPaymentOptions()
	opt := prov.GetCurrentPaymentType()

	for _, item := range opts {
		if item.PaymentType == int32(opt) {
			res.BusinessConstSet = strconv.Itoa(int(item.BusinessConstSet))
			break
		}
	}

	res.PaymentId = strconv.Itoa(opt)

	return res
}

func GetUserPayInfoList(ctx context.Context, provs []UserPayInfoProvider) *proto.PaymentOptionModule {
	if len(provs) == 0 {
		return nil
	}

	allPaymentMap := map[int32]bool{}
	pl := make([]*proto.PaymentOption, 0)
	var defaultPayNum, defaultPayTag int32

	// 支付方式合集
	for _, prov := range provs {
		for _, opt := range prov.GetAllPaymentOptions() {
			if _, ok := allPaymentMap[opt.PaymentType]; !ok {
				tmp := &proto.PaymentOption{
					Msg:              opt.ChannelName,
					Tag:              opt.PaymentType,
					Selected:         false,
					BusinessConstSet: strconv.Itoa(int(opt.BusinessConstSet)),
				}
				pl = append(pl, tmp)
				allPaymentMap[opt.PaymentType] = opt.PaymentType == int32(prov.GetCurrentPaymentType())
			}
		}
	}

	// 默认支付方式
	for paytag, selected := range allPaymentMap {
		if selected {
			defaultPayNum += 1
			defaultPayTag = paytag
		}
	}

	// 只有一种支付方式 且为个人付 则不下发组件
	if len(allPaymentMap) == 0 || len(allPaymentMap) == 1 && defaultPayTag == consts2.PayTypeByPerson {
		return nil
	}

	if defaultPayNum == 1 {
		for _, opt := range pl {
			if opt.GetTag() == defaultPayTag {
				opt.Selected = true
				opt.IsSelected = 1
			}
		}
	}

	sort.Slice(pl, func(i, j int) bool {
		return pl[i].Tag > pl[j].Tag
	})

	return &proto.PaymentOptionModule{
		Title:       dcmp.GetJSONContentWithPath(ctx, "payment-user_pay_info", nil, "title"),
		SubTitle:    dcmp.GetJSONContentWithPath(ctx, "payment-user_pay_info", nil, "sub_title"),
		PaymentList: pl,
	}
}
