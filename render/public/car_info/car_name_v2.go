package car_info

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/carpool_type"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	TaxiUtil "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	mambaApollo "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const ClassTab = "classify"
const TabConfig = "tab_category_config"
const PageCarTitleConf = "scene_category_config_v2"
const expSpaciousCar = "ab_spacious_car_check_separately"
const ChengjiNameAB = "chengjinametest_ab"
const NSMaterial = "estimate_formdata_materiel"
const IntercityStationCarName = "站点巴士"
const ProductCategorySpaciousCar = 190

type CarNameProviderV2 interface {
	GetProductId() int64
	GetProductCategory() int64
	GetSubGroupId() int32
	GetUserPID() int64
	GetCityID() int
	GetCountyID() int
	GetCountyName() string
	GetTabId() string
	GetAccessKeyId() int32
	GetPageType() int32
	GetUserPhone() string
	GetAppVersion() string
	GetLang() string
	GetPricingBoxData() *TaxiUtil.PricingBoxData
	GetCarpoolType() int64
	GetComboType() int64
	GetBusinessID() int64
	GetRequireLevel() string
	GetCarpoolPriceType() int32
	GetRouteType() int64

	render.ApolloProvider
}

type CarInfoItem struct {
	Name string `json:"car_tittle"`
	Icon string `json:"car_icon"`
}

type PageTypeTitleConf struct {
	ProductCategory string `json:"product_category"`
	IntroMsg        string `json:"intro_msg"`
}

type ClassifyTabConf struct {
	ProductCategory int64  `json:"product_category"`
	Name            string `json:"intro_msg"`
	Icon            string `json:"car_icon"`
}

func GetCarNameV2(ctx context.Context, prov CarNameProviderV2) string {
	var carName string

	carName = getIntercityCarNameByAB(ctx, prov)
	if "" != carName {
		return carName
	}
	carName = getCarNameFromBronzeDoor(ctx, prov)
	if "" != carName {
		return carName
	}

	// 出租车
	if prov.GetProductId() == product_id.ProductIdUniOne {
		carName = GetUnionCarName(ctx, prov)
		if carName != "" {
			return carName
		}
	}

	// 站点巴士
	if prov.GetCarpoolType() == carpool_type.CarpoolTypeInterCityStation {
		return IntercityStationCarName
	}

	// 三方表单配置
	if ClassTab == prov.GetTabId() {
		carInfo := GetCarInfoForClassifyTab(ctx, prov)
		carName = carInfo.Name
		if "" != carName {
			return carName
		}
	}

	// page type 配置
	carName = getCarNameByPageType(ctx, prov)
	if "" != carName {
		return carName
	}
	// 默认逻辑 区县 > 城市 > 默认
	return GetNormalCarName(ctx, prov)
}

func getIntercityCarNameByAB(ctx context.Context, prov CarNameProviderV2) string {
	if estimate_pc_id.EstimatePcIdCarpoolInter != prov.GetProductCategory() {
		return ""
	}
	PIDkey, apolloParams := prov.GetApolloParams(biz_runtime.WithPIDKey)
	apolloParams["tab_id"] = prov.GetTabId()
	apolloParams["product_id"] = strconv.Itoa(int(prov.GetProductId()))
	apolloParams["carpool_type"] = strconv.Itoa(int(prov.GetCarpoolType()))
	apolloParams["combo_type"] = strconv.Itoa(int(prov.GetComboType()))
	apolloParams["business_id"] = strconv.Itoa(int(prov.GetBusinessID()))
	apolloParams["require_level"] = prov.GetRequireLevel()
	apolloParams["carpool_price_type"] = strconv.Itoa(int(prov.GetCarpoolPriceType()))
	apolloParams["route_type"] = strconv.Itoa(int(prov.GetRouteType()))
	apolloParams["caller_fun"] = "pre_sale_intro"
	allow, params := mambaApollo.GetParameters(ChengjiNameAB, PIDkey, apolloParams)
	if allow && params != nil {
		carTitle := params["car_title"]
		if carTitle != "" {
			return carTitle
		}
	}
	return ""
}

func getDefaultCarName(ctx context.Context, prov CarNameProviderV2) string {
	// 出租车
	if prov.GetProductId() == product_id.ProductIdUniOne {
		carTitle := GetUnionCarName(ctx, prov)
		if carTitle != "" {
			return carTitle
		}
	}

	// 车大联盟实验
	if prov.GetProductCategory() == ProductCategorySpaciousCar {
		PIDkey, apolloParams := prov.GetApolloParams(biz_runtime.WithPIDKey)
		allow, params := mambaApollo.GetParameters(expSpaciousCar, PIDkey, apolloParams)
		if allow && params != nil {
			carTitle := params["car_title"]
			if carTitle != "" {
				return carTitle
			}
		}
	}

	// 兜底
	return getNormalCarName(ctx, prov)
}

func getNormalCarName(ctx context.Context, prov CarNameProviderV2) string {
	var carName string

	// 名称分区县配置
	carName = getCarNameByCounty(ctx, prov)
	if carName != "" {
		return carName
	}

	// 名称分城市配置
	carName = getCarNameByCity(ctx, prov)
	if carName != "" {
		return carName
	}

	m := map[string]string{"product_category": strconv.Itoa(int(prov.GetProductCategory())), "lang": reqctx.GetLang(ctx)}
	configs, err := mambaApollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, NSMaterial, m, reqctx.GetLang(ctx))
	if configs == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo estimate_formdata_materiel read err with %v and conf %v", err, configs)
		return carName
	}

	if len(configs) > 0 {
		if value, err := configs[0].GetStringValue("intro_msg"); err == nil {
			carName = value
		}
	}

	return carName
}

func getCarNameByPageType(ctx context.Context, prov CarNameProviderV2) string {
	var carName string
	// SceneCarTitle pageType特殊配置
	condition := map[string]string{"page_type": string(prov.GetPageType()), "lang": reqctx.GetLang(ctx)}
	configs, err := mambaApollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, PageCarTitleConf, condition, reqctx.GetLang(ctx))

	if configs == nil || err != nil || len(configs) < 1 {
		// log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo scene_category_config_v2 read err with %v", err)
		return carName
	}

	PageTypeConf := make([]*PageTypeTitleConf, 0)
	err = configs[0].GetJsonValue("intro_msg", &PageTypeConf)

	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "apollo scene_category_config_v2 read err with %v", err)
		return carName
	}

	for _, config := range PageTypeConf {
		if strconv.Itoa(int(prov.GetProductCategory())) == config.ProductCategory {
			return config.IntroMsg
		}
	}
	return carName
}

func GetCarInfoForClassifyTab(ctx context.Context, prov CarNameProviderV2) *CarInfoItem {
	var carInfo = CarInfoItem{}

	// TabId、lang获取物料配置
	condition := map[string]string{"tab_id": prov.GetTabId(), "lang": reqctx.GetLang(ctx)}
	configs, err := mambaApollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, TabConfig, condition, reqctx.GetLang(ctx))

	if configs == nil || err != nil || len(configs) < 1 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo tab_category_config read err with %v", err)
		return &carInfo
	}

	ClassifyTabConf := make([]*ClassifyTabConf, 0)
	err = configs[0].GetJsonValue("intro_msg", &ClassifyTabConf)

	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo tab_category_config read err with %v", err)
		return &carInfo
	}

	// pcid匹配
	for _, config := range ClassifyTabConf {
		if prov.GetProductCategory() == config.ProductCategory {
			carInfo.Name = config.Name
			carInfo.Icon = config.Icon
		}
	}

	return &carInfo
}
