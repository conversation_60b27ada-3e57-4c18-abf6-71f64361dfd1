package car_info

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product_center"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	taxi_biz "git.xiaojukeji.com/gulfstream/passenger-common/biz/taxi"
	"git.xiaojukeji.com/gulfstream/passenger-common/constant"
	"git.xiaojukeji.com/s3e/x-engine/activity"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	taxi_util "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
	"git.xiaojukeji.com/nuwa/trace"
	s3e_model "git.xiaojukeji.com/s3e/x-engine/activity/model"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/model"
)

const (
	nsUNIONEMaterial               = "taxi_product_name_conf"
	ComponentName                  = "wait_answer_tab_component"
	Page                           = "wait_answer_tab_page"
	MainFormComponentName          = "estimate_bubble_tab_component"
	MainFormPage                   = "estimate_tab_page"
	ActivityPrefix_ProductCategory = "product_category_"
	CarInfoCongTag                 = 16
)

type CarNameProvider interface {
	GetCityID() int
	GetCountyID() int
	GetCountyName() string
	GetUserPID() int64
	GetUserPhone() string
	GetAppVersion() string
	GetAccessKeyId() int32
	GetProductId() int64
	GetProductCategory() int64
	GetSubGroupId() int32
	GetLang() string
	GetPricingBoxData() *taxi_util.PricingBoxData
	GetPageType() int32
}

func GetCarName(ctx context.Context, prov CarNameProvider) string {
	var carName string

	// 从青铜门获取
	carName = getCarNameFromBronzeDoor(ctx, prov)
	if carName != "" {
		return carName
	}

	// 出租车
	if prov.GetProductId() == product_id.ProductIdUniOne {
		carName = GetUnionCarName(ctx, prov)
		if carName != "" {
			return carName
		}
	}

	// 普通
	return GetNormalCarName(ctx, prov)
}

func getCarNameFromBronzeDoor(ctx context.Context, prov CarNameProvider) string {

	activityName := ActivityPrefix_ProductCategory + strconv.FormatInt(prov.GetProductCategory(), 10)

	// 灰度
	param := ApolloModel.NewUser("").
		With("pid", strconv.FormatInt(prov.GetUserPID(), 10)).
		With("activity_name", activityName)
	toggle, err := ApolloSDK.FeatureToggle("gs_get_car_info_by_bronze_switch", param)

	if err != nil || !toggle.IsAllow() {
		return ""
	}

	commonRes := &s3e_model.CommonRequest{
		CityId:          prov.GetCityID(),
		AppVersion:      prov.GetAppVersion(),
		Pid:             strconv.FormatInt(prov.GetUserPID(), 10),
		AccessKeyId:     int(prov.GetAccessKeyId()),
		Lang:            prov.GetLang(),
		ProductCategory: int(prov.GetProductCategory()),
		TagCaller:       "mamba",
	}
	var management *activity.MaterialManagement
	switch prov.GetPageType() {
	case page_type.PageTypeUndefined:
		management = activity.NewMaterialManagement(activityName, Page, ComponentName, commonRes)
	case page_type.PageTypeGuideAnyCar:
		management = activity.NewMaterialManagement(activityName, MainFormPage, MainFormComponentName, commonRes)
	}

	if management == nil {
		return ""
	}

	// 添加额外参数
	extraErr := management.SetExtraParamV2(getNameExtraParams(prov))
	if extraErr != nil {
		return ""
	}

	material, err := management.Check(ctx)
	if err != nil || material == nil || material.Hit == false {
		return ""
	}

	bs, err := json.Marshal(material.Material)
	if err != nil {
		return ""
	}

	carData := &models.CarInfo{}
	err = json.Unmarshal(bs, carData)
	if err != nil || carData == nil || carData.CarName == nil {
		return ""
	}

	return carData.CarName[prov.GetLang()]
}

func getNameExtraParams(prov CarNameProvider) map[string]interface{} {
	extraParams := make(map[string]interface{})
	extraParams["product_id"] = prov.GetProductId()
	extraParams["tag_caller"] = "mamba"
	return extraParams
}

type CarNormalNameProvider interface {
	GetCityID() int
	GetCountyID() int
	GetCountyName() string
	GetUserPID() int64
	GetUserPhone() string
	GetAppVersion() string
	GetAccessKeyId() int32
	GetProductId() int64
	GetProductCategory() int64
	GetSubGroupId() int32
	GetLang() string
}

func GetNormalCarName(ctx context.Context, prov CarNormalNameProvider) string {
	// 名称分区县配置
	carName := getCarNameByCounty(ctx, prov)
	if carName != "" {
		return carName
	}

	return product_center.GetProductMaterialByPcID(ctx, reqctx.GetLang(ctx), prov.GetProductCategory(), prov.GetCityID(), product_center.NormalStage)
}

func GetUnionCarName(ctx context.Context, provider CarNameProvider) string {

	// 计价盒子
	if provider.GetSubGroupId() == consts.SubGroupIdTaxiPricingBox && provider.GetPricingBoxData() != nil {
		return provider.GetPricingBoxData().GetCarTitleByPcId(provider.GetProductCategory())
	}

	return ""
}

func GetCarNameByCondition(ctx context.Context, pcID int64, cityID int, lang string) string {
	m := map[string]string{"product_category": strconv.Itoa(int(pcID)), "city_id": strconv.Itoa(cityID)}
	configs, err := apollo.GetConfigsByNamespaceAndConditions(nsUNIONEMaterial, model.NewCondition(m))
	if configs == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo estimate_formdata_materiel read err with %v and conf %v", err, configs)
		return ""
	}

	allConfig := configs.GetAllConfigs()
	for _, conf := range allConfig {
		switch lang {
		case constant.LanguageEnUS:
			if value, err := conf.GetStringValue("product_name_en"); err == nil {
				return value
			}
		case constant.LanguageZhCN:
			if value, err := conf.GetStringValue("product_name"); err == nil {
				return value
			}
		}
	}
	return ""
}

func getCarNameByCity(ctx context.Context, prov CarNormalNameProvider) string {
	return GetCarNameByCondition(ctx, prov.GetProductCategory(), prov.GetCityID(), prov.GetLang())
}

func getCarNameByCounty(ctx context.Context, prov CarNormalNameProvider) string {
	condition := &taxi_biz.CountyCondition{
		EstimatePcID: int(prov.GetProductCategory()),
		CountyID:     prov.GetCountyID(),
		CountyName:   prov.GetCountyName(),
		Lang:         prov.GetLang(),
	}

	return taxi_biz.NewProductNameByCountyController(condition).InitConfs(ctx).GetProductNameByCounty()
}
