package category_unione

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/level_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"github.com/spf13/cast"
)

type PriceDescProvider interface {
	render.BaseProvider
	render.BillInfoProvider
	render.ProductProvider
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
	render.ApolloProvider
}

func GetTaxiFeeInfo(ctx context.Context, prov PriceDescProvider, digitExp bool) (feeMsg string, feeAmount string, feeType int) {
	var (
		priceDesc string
	)
	key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
	price.CheckSingle(ctx, "getTaxiFeeInfo", "TaxiFeeInfo", "estimateFee", prov.GetEstimateFee())
	if carpool.IsTaxiCarpool(prov.GetProductCategory()) {
		template := dcmp.GetJSONResultWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "taxi_carpool").String()
		fee := util2.PriceFormat(ctx, param, cast.ToString(key), prov.GetEstimateFee(), feeType)
		feeMsg = dcmp.TranslateTemplate(template, map[string]string{
			"num": fee,
		})
		return feeMsg, fee, consts.FeeTypeCapPrice
	}

	priceDesc = dcmp.GetDcmpContent(ctx, "unione-price_desc_without_price", nil)

	if digitExp {
		//小数点位数实验
		estimateFee := prov.GetEstimateFee()
		feeType = consts.FeeTypeDefault
		feeAmount = util2.PriceFormat(ctx, param, cast.ToString(key), estimateFee, feeType)
	} else {
		//不需要过小数点位数实验
		feeAmount = util.Float64ToString(prov.GetEstimateFee())
	}

	// 计价盒子
	if prov.GetSubGroupId() == consts.SubGroupIdTaxiPricingBox {
		feeMsg, feeType = GetPricingBoxFeeMsg(ctx, prov, priceDesc)

		if len(feeMsg) > 0 {
			priceDesc = feeMsg
			return
		}
	}

	if CheckTaxiShowEstimateFee(ctx, prov) {
		feeType = consts.FeeTypeDefault
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
		tag := map[string]string{
			"currency_symbol": symbol,
			"total_fee":       feeAmount,
			"currency_unit":   unit,
		}
		priceDesc = dcmp.GetDcmpContent(ctx, "common-price_desc", tag)

		// 预估价前缀逻辑
		if prefix := GetPrefixText(ctx, prov); prefix != "" {
			priceDesc = prefix + priceDesc
		}
	} else {
		feeType = consts.FeeTypeTable
	}

	if isSpecialPrice(ctx, prov) {
		var textKey string
		tag := map[string]string{
			"total_fee": feeAmount,
		}
		textKey, feeType = getSpecialPriceTextKey(ctx, prov)
		priceDesc = dcmp.GetJSONContentWithPath(ctx, "unione-special_price_taxi_price_desc", tag, textKey)
	}

	return priceDesc, feeAmount, feeType
}

func GetPricingBoxFeeMsg(ctx context.Context, prov PriceDescProvider, priceDesc string) (string, int) {
	uidKey, params := prov.GetApolloParams(biz_runtime.WithUIDKey)
	useNew := taxi.UsingNewBox(ctx, cast.ToString(uidKey), params)
	price.CheckSingle(ctx, "getPricingBoxFeeMsg", "PricingBoxFeeMsg", "estimateFee", prov.GetEstimateFee(), price.WithCheckBelowZero())
	if (prov.GetLevelType() != level_type.LevelTypeTaxiMarketisation && prov.GetLevelType() != level_type.LevelTypeTaxiBuTian) && !useNew {
		// 不走实验 保持原逻辑 显示打表计价
		return priceDesc, consts.FeeTypeTable
	} else {
		// 在线计价 或者 走实验的打表计价 显示预估价格
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
		key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
		feeAmount := util2.PriceFormat(ctx, param, cast.ToString(key), prov.GetEstimateFee(), consts.FeeTypeDefault)

		tag := map[string]string{
			"currency_symbol": symbol,
			"total_fee":       feeAmount,
			"currency_unit":   unit,
		}
		priceDesc = dcmp.GetDcmpContent(ctx, "common-price_desc", tag)
		if prefix := GetPrefixText(ctx, prov); prefix != "" && useNew {
			priceDesc = prefix + priceDesc
		}
		return priceDesc, consts.FeeTypeDefault
	}
}

// GetPrefixText 获取前缀文案
func GetPrefixText(ctx context.Context, prov PriceDescProvider) string {
	// 是否需要前缀文案
	key, param := prov.ApolloParamsGen(apollo_model.WithPhoneKey, apollo_model.WithMenuID,
		apollo_model.WithProductCategory, apollo_model.WithIsCrossCity, apollo_model.WithFromCounty)
	withoutPrefix := apollo.FeatureToggle(ctx, "taxi_estimate_fee_without_prefix", key, param)
	if withoutPrefix {
		return ""
	}
	// 一车两价出租车，在预估价前加上"在线计价·" 文案展示
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiMarketisationPutong ||
		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiMarketisationYouxuan {
		return dcmp.GetDcmpContent(ctx, "config-taxi_price_online_text", nil)
	}

	// 打表出租车，在预估价前加上"打表·" 文案展示
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdUnione ||
		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdYouxuanTaxi ||
		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCountyTaxi {
		return dcmp.GetDcmpContent(ctx, "config-taxi_price_unione_text", nil)
	}
	return ""
}

type checkerDependency interface {
	GetProductCategory() int64
	GetBillDriverMetre() int64
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
}

// CheckTaxiShowEstimateFee 出租车预估价展示逻辑 -- 需要同步峰期加价（超长单除外）
func CheckTaxiShowEstimateFee(ctx context.Context, prov checkerDependency) bool {
	userPhone, params := prov.ApolloParamsGen(apollo_model.WithPhoneKey, apollo_model.WithMenuID,
		apollo_model.WithProductCategory, apollo_model.WithIsCrossCity, apollo_model.WithFromCounty)
	uidKey, _ := prov.ApolloParamsGen(apollo_model.WithUIDKey)

	return IsTaxiShowEstimateFee(ctx, prov.GetProductCategory(), uidKey, userPhone, params, prov.GetBillDriverMetre())
}

func IsTaxiShowEstimateFee(ctx context.Context, pcID int64, uidKey string, userPhone string, params map[string]string, BillDriverMetre int64) bool {
	// 超值、一车两价需要展示 "预估xx元"，而非 "打表计价"
	if pcID == estimate_pc_id.EstimatePcIdUnioneSpecialPrice ||
		pcID == estimate_pc_id.EstimatePcIdTaxiMarketisationPutong ||
		pcID == estimate_pc_id.EstimatePcIdTaxiMarketisationYouxuan {
		return true
	}

	var isShow = false

	if pcID == estimate_pc_id.EstimatePcIdUnione {
		isShow = checkForCityAB(ctx, uidKey, params) || isShow
	}

	if checkForCounty(ctx, userPhone, params) || checkForCity(ctx, userPhone, params) {
		isShow = true
	}

	if pcID == estimate_pc_id.EstimatePcIdCountyTaxi {
		isShow = true
	}

	// 超长单维度
	if BillDriverMetre <= 0 {
		return isShow
	}

	params["estimate_metre"] = cast.ToString(BillDriverMetre)
	isLongOrder := apollo.FeatureToggle(ctx, "taxi_estimate_price_show_threshold", userPhone, params)

	// 超长单实验
	if isShow && isLongOrder {
		isShow = false
		ok, assign := apollo.FeatureExp(ctx, "unione_show_estimate_fee_long_metre_ab", uidKey, params)
		if ok && assign.GetGroupName() == "control_group" {
			isShow = true
		}
	}
	return isShow
}

func checkForCounty(ctx context.Context, userPhoneKey string, params map[string]string) bool {
	return apollo.FeatureToggle(ctx, "estimate_form_unione_show_estimate_fee_county", userPhoneKey, params)
}

func checkForCity(ctx context.Context, userPhoneKey string, params map[string]string) bool {
	return apollo.FeatureToggle(ctx, "estimate_form_unione_show_estimate_fee", userPhoneKey, params)
}

// func checkForCityAB(ctx context.Context, userPhoneKey string, params map[string]string) bool {
// 	ok, assign := apollo.FeatureExp(ctx, "unione_show_estimate_fee_ab", userPhoneKey, params)
// 	return ok && "treatment_group" == assign.GetGroupName()
// }

// checkForCityAB 打表预估价漏出实验
// 实验链接为：https://x.intra.xiaojukeji.com/project/exps?globalBusinessId=100&globalProjectId=412600099864577&expListParams=%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A10%2C%22projectId%22%3A%22412600099864577%22%2C%22businessId%22%3A%22100%22%2C%22filterType%22%3A%22involved%22%2C%22templateTypeList%22%3A%5B%5D%2C%22targetStatusList%22%3A%5B%5D%2C%22query%22%3A%22%22%7D
func checkForCityAB(ctx context.Context, uidKey string, params map[string]string) bool {
	ok, toggle := apollo.FeatureExp(ctx, "estimate_price_normal_taxi", uidKey, params)
	if !ok || toggle == nil {
		return false
	}
	return toggle.GetParameter("show_estimate_price", "0") == "1"
}

// 是否为超值一口价
func isSpecialPrice(ctx context.Context, prov PriceDescProvider) bool {
	var isShowMetredFare int64
	if result, err := product_id.GetConfigByProductIDAndPath(prov.GetProductId(), "show_metered_fare"); err != nil || !result.Exists() {
		return false
	} else {
		isShowMetredFare = result.Int()
	}

	return isShowMetredFare == 1 &&
		prov.IsSpecialPrice() &&
		util.InArrayInt64(prov.GetProductId(), []int64{consts.ProductIDUNITAXI, consts.ProductIDBusinessTaixCar})
}

func getSpecialPriceTextKey(ctx context.Context, prov PriceDescProvider) (string, int) {
	uidKey, params := prov.GetApolloParams(biz_runtime.WithUIDKey, biz_runtime.WithIsSpecialPrice, biz_runtime.WithIsCrossCity, biz_runtime.WithFromCounty, biz_runtime.WithMenuID)
	params["car_level"] = prov.GetRequireLevel()
	params["require_level"] = prov.GetRequireLevel()
	params["combo_type"] = strconv.FormatInt(prov.GetComboType(), 10)

	if apollo.FeatureToggle(ctx, "taxi_special_not_show_price_text", uidKey, params) {
		return "taxi_special_not_show_price_text", consts.FeeTypeDefault
	} else {
		return "taxi_chaozhi_fee_estimate", consts.FeeTypeCapPrice
	}
}
