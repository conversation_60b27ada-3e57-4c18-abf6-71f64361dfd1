package category_unione

import (
	"context"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	model2 "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

// mockPriceInfoDescListProvider 实现 PriceInfoDescListProvider 接口
type mockPriceInfoDescListProvider struct {
	baseReqData     *models.BaseReqData
	accessKeyId     int32
	appVersion      string
	lang            string
	productCategory string
}

func (m *mockPriceInfoDescListProvider) IsHitDynamicIconAb(ctx context.Context) bool {
	//TODO implement me
	return false
}

func (m *mockPriceInfoDescListProvider) GetAccessKeyId() int32 {
	//TODO implement me
	return m.accessKeyId
}

func (m *mockPriceInfoDescListProvider) GetProductId() int64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetProductCategory() int64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetBusinessID() int64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetCarpoolType() int64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetComboType() int64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetRequireLevel() string {
	//TODO implement me
	return ""
}

func (m *mockPriceInfoDescListProvider) GetCarpoolPriceType() int32 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetSubGroupId() int32 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) IsDualCarpoolPrice() bool {
	//TODO implement me
	return false
}

func (m *mockPriceInfoDescListProvider) IsSpecialPrice() bool {
	//TODO implement me
	return false
}

func (m *mockPriceInfoDescListProvider) GetOrderType() int16 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetCountPriceType() int32 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetLevelType() int32 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetCityID() int {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetUserPID() int64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetBillFeeDetailInfo() map[string]float64 {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetBillDriverMetre() int64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetBillInfoCurrency() string {
	//TODO implement me
	return ""
}

func (m *mockPriceInfoDescListProvider) GetEstimateFee() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetCarpoolFailEstimateFee() (float64, bool) {
	//TODO implement me
	return 0, false
}

func (m *mockPriceInfoDescListProvider) GetCapPrice() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetMemberDynamicProtectFee() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetDisplayLines() map[string]*PriceApi.DisplayLine {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetPersonalEstimateFee() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetDynamicTotalFee() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetTotalFeeWithoutDiscount() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetBonusAmount() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetHkBonus() *PriceApi.EstimateNewFormBonusInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetVCard() *PriceApi.EstimateNewFormVCardInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetRevolvingAccountDiscount() *PriceApi.RevolvingAccountDiscountInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetRevolvingAccountRebate() *PriceApi.RevolvingAccountRebateInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetSpecZiYouBao() *PriceApi.EstimateNewFormSpecZiYoubao {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetRight() *PriceApi.EstimateNewFormRightInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetCard() *PriceApi.EstimateNewFormCardInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetBusCard() *PriceApi.NewFormBusCard {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetMemberDiscountCard() *PriceApi.DiscountCard {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetAlipayCoupon() *PriceApi.EstimateNewFormCouponInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetTCDiscountFee() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetRenderDiscount() []*PriceApi.RenderDiscount {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetCarpoolFailedCoupon() *PriceApi.EstimateNewFormCouponInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetExtendList() []*PriceApi.EstimateNewFormExtend {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetCashBack() *PriceApi.CashbackXinZhuInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) IsBusinessPay() bool {
	//TODO implement me
	return false
}

func (m *mockPriceInfoDescListProvider) GetMemberDpaInfo() *GoMember.PrivInfo {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetLevelID() int32 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetTaxiSps() *hestia_charge.TaxiSpsData {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetPricingByMeterPrice() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetApolloParams(f func(full *biz_runtime.ProductInfoFull) string, f2 ...func(full *biz_runtime.ProductInfoFull) (string, string)) (string, map[string]string) {
	//TODO implement me
	return "", nil
}

func (m *mockPriceInfoDescListProvider) IsCarpoolV3Merge(ctx context.Context) bool {
	//TODO implement me
	return false
}

func (m *mockPriceInfoDescListProvider) IsHaveCarpoolVCard(ctx context.Context) bool {
	//TODO implement me
	return false
}

func (m *mockPriceInfoDescListProvider) GetSceneEstimatePrice() []biz_runtime.SceneEstimateFeeViewer {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetExactSeatNum() int32 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetComboID() int64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool {
	//TODO implement me
	return false
}

func (m *mockPriceInfoDescListProvider) GetSceneDataBySceneMark(reqSceneMark map[string]string) *biz_runtime.SceneEstimateFee {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetBonus() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetFastEstimatePrice() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetDiscountCard() *PriceApi.DiscountCard {
	//TODO implement me
	return nil
}

func (m *mockPriceInfoDescListProvider) GetExactEstimateFee() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetCarpoolFailExactEstimateFee() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetMixedDeductPrice() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetFastCarPrice() float64 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetOpenCitySourceId() int32 {
	//TODO implement me
	return 0
}

func (m *mockPriceInfoDescListProvider) GetBaseReqData() *models.BaseReqData {
	return m.baseReqData
}

func (m *mockPriceInfoDescListProvider) GetAppVersion() string {
	return m.appVersion
}

func (m *mockPriceInfoDescListProvider) GetLang() string {
	return m.lang
}

func (m *mockPriceInfoDescListProvider) ApolloParamsGen(
	keyFunc func(param *apollo_model.ParamsConnector) string,
	paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string),
) (key string, params map[string]string) {

	return
}

func TestGetPriceInfoDescList(t *testing.T) {
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock NewFeeEngine
	engin := &fee_desc_engine.Engine{}
	patches.ApplyFunc(fee_desc_engine.NewFeeEngine, func(input *model2.FeeInput, env *model2.Env) *fee_desc_engine.Engine {
		return engin
	})

	// Mock Do method
	patches.ApplyMethod(engin, "Do", func(e *fee_desc_engine.Engine, ctx context.Context) []*model2.FeeOutput {
		return []*model2.FeeOutput{
			{
				BorderColor: "red",
				Icon:        "test_icon",
				Content:     "test_content",
				Type:        1,
				TextColor:   "black",
				Fee: &model2.FeeDetail{
					Amount: 10.0,
				},
			},
		}
	})

	tests := []struct {
		name     string
		provider PriceInfoDescListProvider
		want     []*proto.NewFormFeeDesc
	}{
		{
			name: "新竹场景-正常返回",
			provider: &mockPriceInfoDescListProvider{
				baseReqData: &models.BaseReqData{
					CommonBizInfo: models.CommonBizInfo{
						SourceId: 22,
					},
				},
				accessKeyId:     1,
				appVersion:      "7.0.50",
				lang:            "zh-CN",
				productCategory: "test_category",
			},
			want: []*proto.NewFormFeeDesc{
				{
					BorderColor: "red",
					Icon:        "test_icon",
					Content:     "test_content",
					Type:        1,
					TextColor:   util.StringPtr("black"),
					Amount:      10.0,
				},
			},
		},
		{
			name: "非新竹场景-正常返回",
			provider: &mockPriceInfoDescListProvider{
				baseReqData: &models.BaseReqData{
					CommonBizInfo: models.CommonBizInfo{
						SourceId: 22,
					},
				},
				accessKeyId:     1,
				appVersion:      "7.0.10",
				lang:            "zh-CN",
				productCategory: "test_category",
			},
			want: []*proto.NewFormFeeDesc{
				{
					BorderColor: "red",
					Icon:        "test_icon",
					Content:     "test_content",
					Type:        1,
					TextColor:   util.StringPtr("black"),
					Amount:      10.0,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetPriceInfoDescList(context.Background(), tt.provider)
			if len(got) != len(tt.want) {
				t.Errorf("GetPriceInfoDescList() = %v, want %v", got, tt.want)
			}
			// Add more detailed comparison if needed
			for i := range got {
				if got[i].BorderColor != tt.want[i].BorderColor ||
					got[i].Icon != tt.want[i].Icon ||
					got[i].Content != tt.want[i].Content ||
					got[i].Type != tt.want[i].Type ||
					*got[i].TextColor != *tt.want[i].TextColor ||
					got[i].Amount != tt.want[i].Amount {
					t.Errorf("GetPriceInfoDescList() = %v, want %v", got[i], tt.want[i])
				}
			}
		})
	}
}

func TestGetPriceInfoDescListHarbour(t *testing.T) {
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	engin := &fee_desc_engine.Engine{}
	patches.ApplyFunc(fee_desc_engine.NewFeeEngine, func(input *model2.FeeInput, env *model2.Env) *fee_desc_engine.Engine {
		return engin
	})
	patches.ApplyMethod(engin, "Do", func(e *fee_desc_engine.Engine, ctx context.Context) []*model2.FeeOutput {
		return []*model2.FeeOutput{
			{
				BorderColor: "red",
				Icon:        "test_icon",
				Content:     "test_content",
				Type:        1,
				TextColor:   "black",
				Fee: &model2.FeeDetail{
					Amount: 10.0,
				},
			},
		}
	})

	provider := &mockPriceInfoDescListProvider{
		baseReqData: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				SourceId: 22,
			},
		},
		accessKeyId:     1,
		appVersion:      "7.0.50",
		lang:            "zh-CN",
		productCategory: "test_category",
	}

	got := GetPriceInfoDescListHarbour(context.Background(), provider)
	assert.Len(t, got, 1)
	assert.Equal(t, "red", got[0].BorderColor)
	assert.Equal(t, "test_icon", got[0].Icon)
	assert.Equal(t, "test_content", got[0].Content)
	assert.Equal(t, int32(1), got[0].Type)
	assert.NotNil(t, got[0].TextColor)
	assert.Equal(t, "black", *got[0].TextColor)
	assert.Equal(t, 10.0, got[0].Amount)
}

func TestGetPriceInfoDescListHarbourV4(t *testing.T) {
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	engin := &fee_desc_engine.Engine{}
	patches.ApplyFunc(fee_desc_engine.NewFeeEngine, func(input *model2.FeeInput, env *model2.Env) *fee_desc_engine.Engine {
		return engin
	})
	patches.ApplyMethod(engin, "Do", func(e *fee_desc_engine.Engine, ctx context.Context) []*model2.FeeOutput {
		return []*model2.FeeOutput{
			{
				BorderColor: "blue",
				Icon:        "icon_v4",
				Content:     "content_v4",
				Type:        2,
				TextColor:   "gray",
				Fee: &model2.FeeDetail{
					Amount: 20.0,
				},
			},
		}
	})

	provider := &mockPriceInfoDescListProvider{
		baseReqData: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				SourceId: 22,
			},
		},
		accessKeyId:     1,
		appVersion:      "8.0.0",
		lang:            "en-US",
		productCategory: "test_category",
	}

	got := GetPriceInfoDescListHarbourV4(context.Background(), provider)
	assert.Len(t, got, 1)
	assert.Equal(t, "blue", got[0].BorderColor)
	assert.Equal(t, "icon_v4", got[0].Icon)
	assert.Equal(t, "content_v4", got[0].Content)
	assert.Equal(t, int32(2), got[0].Type)
	assert.NotNil(t, got[0].TextColor)
	assert.Equal(t, "gray", *got[0].TextColor)
	assert.Equal(t, 20.0, got[0].Amount)
}
