package category_unione

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	model2 "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

func GetPriceInfoDescList(ctx context.Context, prov PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	descList := make([]*proto.NewFormFeeDesc, 0)

	var (
		env      *model2.Env
		feeInput *model2.FeeInput
	)
	if prov.GetBaseReqData() != nil &&
		util.IsFromXinZhuWithBusinessTravelOrderType(prov.GetBaseReqData().CommonBizInfo.BusinessTravelOrderType) {
		env = fee_desc_engine.NewEnv(consts.XinZhuAppendCarForm).SetApolloParams(prov).SetDcmpKey(consts.XinZhuAppendCarFormFeeDesc)
		feeInput = input.BuildTaxiFeeInput(ctx, prov, consts.XinZhuAppendCarForm)
	} else {
		env = fee_desc_engine.NewEnv(consts.AnyCarV3Form).SetApolloParams(prov).SetDcmpKey(consts.AnyCarV3FeeDesc)
		feeInput = input.BuildTaxiFeeInput(ctx, prov, consts.AnyCarV3Form)
	}

	resp := fee_desc_engine.NewFeeEngine(feeInput, env).SetProductCategory(prov.GetProductCategory()).Do(ctx)

	if len(resp) == 1 && resp[0].Key == consts.FeeTaxiPricingByOnline {
		return descList
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			BorderColor: output.BorderColor,
			Icon:        output.Icon,
			Content:     output.Content,
			Type:        output.Type,
			TextColor:   util.StringPtr(output.TextColor),
		}

		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}

		descList = append(descList, desc)
	}

	return descList
}

func GetTaxiPriceInfoDescList(ctx context.Context, prov PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	descList := make([]*proto.NewFormFeeDesc, 0)

	env := fee_desc_engine.NewEnv(consts.DefaultForm).SetApolloParams(prov).SetDcmpKey(consts.EstimateV4FeeDesc)
	resp := fee_desc_engine.NewFeeEngine(input.BuildTaxiFeeInput(ctx, prov, consts.DefaultForm), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)

	if len(resp) == 1 && resp[0].Key == consts.FeeTaxiPricingByOnline {
		return descList
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			BorderColor: output.BorderColor,
			Icon:        output.Icon,
			Content:     output.Content,
			Type:        output.Type,
			TextColor:   util.StringPtr(output.TextColor),
		}

		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}

		descList = append(descList, desc)
	}

	return descList
}
