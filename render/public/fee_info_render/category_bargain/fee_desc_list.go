package category_bargain

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
)

func GetBargainFeeDescList(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	var (
		ret = make([]*proto.NewFormFeeDesc, 0)
	)

	_, params := prov.ApolloParamsGen(apollo_model.WithPhoneKey)
	if !apollo.FeatureToggle(ctx, "bargain_recommend_v2_switch", "", params) {
		//命中端上推荐二期新版本，展示费用描述信息
		return nil
	}

	env := fee_desc_engine.NewEnv(consts.DefaultForm).SetApolloParams(prov).SetDcmpKey(consts.EstimateV4FeeDesc)
	resp := fee_desc_engine.NewFeeEngine(input.BuildBargainFeeInput(ctx, prov), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			BorderColor: output.BorderColor,
			Icon:        output.Icon,
			Content:     output.Content,
			Type:        output.Type,
			TextColor:   util.StringPtr(output.TextColor),
		}

		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}

		ret = append(ret, desc)
	}

	return ret
}
