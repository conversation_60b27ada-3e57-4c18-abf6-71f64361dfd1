package smart_bus

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
)

const (
	SmartBusFeeDetailListMaxNum = 1
)

type PriceInfoDescListProvider interface {
	render.ProductProvider
	render.BaseProvider
	render.BillInfoProvider
	render.ActivityInfoProvider
	render.PaymentInfoProvider
	render.MemberInfoProvider
	render.TaxiInfoProvider
	render.ApolloProvider
	render.DynamicIconABProvider
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
	GetFastCarPrice() float64
}

func GetPriceInfoDescList(ctx context.Context, prov PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	ret := make([]*proto.NewFormFeeDesc, 0)
	env := fee_desc_engine.NewEnv(consts.SmartBusForm).SetDcmpKey(consts.SmartBusFeeDetailDesc).SetApolloParams(prov).SetCap(SmartBusFeeDetailListMaxNum)
	resp := fee_desc_engine.NewFeeEngine(input.BuildNormalFeeInput(ctx, prov, consts.SmartBusForm), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			Icon:        output.Icon,
			Content:     output.Content,
			BorderColor: output.BorderColor,
		}
		ret = append(ret, desc)
	}
	return ret
}
