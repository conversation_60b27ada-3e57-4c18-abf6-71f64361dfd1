package fee_info_render

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"regexp"
	"sort"
	"strconv"
	"strings"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product_center"
	CommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_unione"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/spacious_car/spacious_common"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
)

type FeeInfoProvider interface {
	render.BaseProvider
	render.BillInfoProvider
	render.ProductProvider
	IsCarpoolV3Merge(context.Context) bool
	GetMixedDeductPrice() float64
	GetVCard() *PriceApi.EstimateNewFormVCardInfo
	IsBusinessPay() bool

	render.ApolloProvider
}

type PriceDescProvider interface {
	render.BaseProvider
	render.BillInfoProvider
	render.ProductProvider
	IsCarpoolV3Merge(context.Context) bool
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
	GetMixedDeductPrice() float64
	GetVCard() *PriceApi.EstimateNewFormVCardInfo
	IsBusinessPay() bool

	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
	render.ApolloProvider
}

type PriceInfoProvider interface {
	PriceDescProvider
	APlusFeeDescProvider
	render.BargainInfoProvider
	render.CarpoolInfoProvider
}

type APlusFeeDescProvider interface {
	PriceDescProvider
	EnableFeeRange() bool
	GetMinFeeRange() float64
}

type FeeInfoResp struct {
	FeeMsg           string
	FeeAmount        string
	FeeType          int
	FeeTemplate      string
	MinFeeAmount     string
	FeeRangeTemplate string
}

// RenderFeeMsg 基础渲染
func RenderFeeMsg(ctx context.Context, key string, path string, fee string) string {
	if conf := dcmp.GetJSONContentWithPath(ctx, key, nil, path); len(conf) > 0 {
		feeMsg := util.ReplaceTag(ctx, conf, map[string]string{
			"num": fee,
		})

		return feeMsg
	}

	return fmt.Sprintf("预估{%s}元", fee)
}

func GetFeeInfo(ctx context.Context, prov PriceDescProvider) (feeMsg string, feeAmount string, feeType int) {
	if prov.GetProductId() == product_id.ProductIdUniOne {
		// 出租车业务线
		return category_unione.GetTaxiFeeInfo(ctx, prov, true)
	} else if product.IsHongKongProduct(CommonConsts.ProductID(prov.GetProductId())) {
		// 香港业务线
		return getHKFeeInfo(ctx, prov)
	}

	feeInfoResp := GetNormalFeeInfo(ctx, prov)
	return feeInfoResp.FeeMsg, feeInfoResp.FeeAmount, feeInfoResp.FeeType
}

// GetEstimateFeeInfo 拉齐预估v3的fee_msg
func GetEstimateFeeInfo(ctx context.Context, prov PriceInfoProvider) FeeInfoResp {
	feeInfoResp := FeeInfoResp{}
	if carpool.IsCarpool(prov.GetCarpoolType()) {
		// 拼车
		feeInfoResp.FeeMsg, feeInfoResp.FeeAmount = getCarpoolEstimateFeeInfo(ctx, prov)
		return feeInfoResp
	}

	if order.IsSpecialRateV2(prov, 0) {
		// 特惠快车
		feeInfoResp.FeeMsg, feeInfoResp.FeeAmount = getSpecialRateFeeInfo(ctx, prov)
		return feeInfoResp
	}

	if prov.GetProductId() == product_id.ProductIdUniOne {
		// 出租车业务线
		feeInfoResp.FeeMsg, feeInfoResp.FeeAmount, _ = category_unione.GetTaxiFeeInfo(ctx, prov, true)
		return feeInfoResp
	}

	if product.IsHongKongProduct(CommonConsts.ProductID(prov.GetProductId())) {
		// 香港业务线
		feeInfoResp.FeeMsg, feeInfoResp.FeeAmount, _ = getHKFeeInfo(ctx, prov)
		return feeInfoResp
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdSpaciousCar {
		// 车大联盟
		feeInfoResp.FeeMsg, feeInfoResp.FeeAmount = getSpaciousCarFeeInfo(ctx, prov)
		return feeInfoResp
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain {
		// 司乘议价
		feeInfoResp.FeeMsg, feeInfoResp.FeeAmount = getBargainFeeInfo(ctx, prov)
		return feeInfoResp
	}

	feeInfoResp = GetNormalEstimateFeeInfo(ctx, prov)
	return feeInfoResp
}

func normalizeFeeRange(lowerBoundFee float64, upperBoundFee float64) (float64, float64) {
	upperBoundFee = math.Max(upperBoundFee, 0)
	lowerBoundFee = math.Min(upperBoundFee, lowerBoundFee)
	lowerBoundFee = math.Max(0, lowerBoundFee)
	return lowerBoundFee, upperBoundFee
}

func GetAPlusFeeInfo(ctx context.Context, prov APlusFeeDescProvider) FeeInfoResp {
	feeInfoResp := GetNormalFeeInfo(ctx, prov)
	return buildAPlusRange(ctx, prov, feeInfoResp, true)
}

func buildAPlusRange(ctx context.Context, prov APlusFeeDescProvider, feeInfoResp FeeInfoResp, digitExp bool) FeeInfoResp {
	if !prov.EnableFeeRange() {
		return feeInfoResp
	}

	upperBoundFee := prov.GetEstimateFee()
	lowerBoundFee := prov.GetMinFeeRange()
	if deductFee := prov.GetMixedDeductPrice(); deductFee > 0 {
		upperBoundFee -= deductFee
		lowerBoundFee -= deductFee
	}

	lowerBoundFee, upperBoundFee = normalizeFeeRange(lowerBoundFee, upperBoundFee)
	lowerBoundStr := util.Float64ToString(lowerBoundFee)
	upperBoundStr := util.Float64ToString(upperBoundFee)

	if digitExp {
		//小数点位数实验
		key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
		lowerBoundStr = util2.PriceFormat(ctx, param, cast.ToString(key), lowerBoundFee, consts.FeeTypeRange)
		upperBoundStr = util2.PriceFormat(ctx, param, cast.ToString(key), upperBoundFee, consts.FeeTypeRange)
	}

	if upperBoundStr == lowerBoundStr {
		return feeInfoResp
	}

	if feeInfoResp.FeeTemplate == "" {
		return feeInfoResp
	}

	rangeTemplate := feeInfoResp.FeeTemplate

	feeAmount := fmt.Sprintf("%s-%s", lowerBoundStr, upperBoundStr)
	ret := FeeInfoResp{
		FeeMsg:    RenderByCommonTemplate(ctx, prov, feeAmount, rangeTemplate),
		FeeAmount: upperBoundStr,
		FeeType:   feeInfoResp.FeeType,
	}

	ret.MinFeeAmount = lowerBoundStr
	// 很玄学的逻辑 现在的无奈之举 {{{num}}} -> {{num}} -> %s
	rangeTemplate = RenderByCommonTemplate(ctx, prov, "{num}", rangeTemplate)
	ret.FeeRangeTemplate = RenderByCommonTemplate(ctx, prov, " %s ", rangeTemplate)

	return ret
}

func GetNormalFeeInfo(ctx context.Context, prov PriceDescProvider) FeeInfoResp {
	var (
		feeMsg    string
		feeAmount string
		feeType   int
	)

	generalTmp := util2.GetGeneralFeeMsgTemplate(ctx, prov.GetCityID(), int(prov.GetProductCategory()), prov.GetLang())
	currencyUnit, currencySymbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
	price.CheckSingle(ctx, "GetNormalFeeInfo", "NormalFeeInfo", "GetEstimateFee", prov.GetEstimateFee(), price.WithCheckBelowZero())
	key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)

	// 企业抵扣
	if deductFee := prov.GetMixedDeductPrice(); deductFee > 0 {
		estimateFeeAmount := prov.GetEstimateFee() - deductFee
		price.CheckSingle(ctx, "GetNormalFeeInfo", "NormalFeeInfo", "estimateFeeAmount", estimateFeeAmount, price.WithCheckBelowZero())
		//小数点位数实验
		feeType = consts.FeeTypeBusinessPay
		feeAmount = util2.PriceFormat(ctx, param, cast.ToString(key), estimateFeeAmount, feeType)

		if conf := dcmp.GetJSONContentWithPath(ctx, "simple_estimate-estimate_price_msg", nil, "mix_personal"); len(conf) > 0 {
			feeMsg = dcmp.TranslateTemplate(conf, map[string]string{
				"num": feeAmount,
			})

			feeType = consts.FeeTypeBusinessPay
			return FeeInfoResp{
				FeeMsg:      feeMsg,
				FeeAmount:   feeAmount,
				FeeType:     feeType,
				FeeTemplate: conf,
			}
		}
	}

	estimateFeeAmount := prov.GetEstimateFee()
	feeAmount = util.Float64ToString(prov.GetEstimateFee())
	price.CheckSingle(ctx, "GetNormalFeeInfo", "NormalFeeInfo", "EstimateFee", prov.GetEstimateFee(), price.WithCheckBelowZero())
	tag := map[string]string{
		"num":             feeAmount,
		"fee_amount":      feeAmount,
		"total_fee":       feeAmount,
		"currency_unit":   currencyUnit,
		"currency_symbol": currencySymbol,
	}

	// 如果存在省钱卡-赠卡 或者 省钱卡-买卡 则展示”稳享拼成价xx元“
	// 获取Vcard服务
	vcardResult := prov.GetVCard()
	if vcardResult != nil {
		if carpool.SendNew == vcardResult.Source || carpool.SendLoss == vcardResult.Source || carpool.Usable == vcardResult.Source {
			feeAmount = carpool3.FormatPrice(estimateFeeAmount, carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), -1).StringVal
			tag["total_fee"] = feeAmount
			tag["num"] = feeAmount
			tag["fee_amount"] = feeAmount
			feeMsg = dcmp.GetDcmpContent(ctx, "anycar_estimate-new_free_card_top", tag)
			return FeeInfoResp{FeeMsg: feeMsg, FeeAmount: feeAmount, FeeType: feeType} // 稳享拼成价不受小数点位数影响
		}
	}

	if util.InArrayInt32(prov.GetCarpoolPriceType(), []int32{11, 105, 108}) ||
		order.IsCapFast(prov) ||
		(prov.GetComboType() == 309 && prov.GetCountPriceType() == 11) || // 多因素一口价
		prov.GetCapPrice() > 0 {
		// 一口价
		estimateFeeAmount := prov.GetEstimateFee()
		feeType = consts.FeeTypeCapPrice
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
		key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
		feeAmount = util2.PriceFormat(ctx, param, cast.ToString(key), estimateFeeAmount, feeType)

		tag := map[string]string{
			"num":             feeAmount,
			"fee_amount":      feeAmount,
			"total_fee":       feeAmount,
			"currency_unit":   unit,
			"currency_symbol": symbol,
		}

		feeType = consts.FeeTypeCapPrice

		feeTemplate := dcmp.GetDcmpPlainContent(ctx, "cap_price-price_desc")
		// 通用文案：优先级最高
		if generalTmp != "" {
			feeTemplate = generalTmp
		}

		return FeeInfoResp{
			FeeMsg:      dcmp.TranslateTemplate(feeTemplate, tag),
			FeeAmount:   feeAmount,
			FeeType:     feeType,
			FeeTemplate: feeTemplate,
		}
	}

	// 既没有省钱卡-赠卡，也没省钱卡-买卡，则展示”预估xx元“
	//小数点位数实验
	feeType = consts.FeeTypeDefault
	feeAmount = util2.PriceFormat(ctx, param, cast.ToString(key), estimateFeeAmount, feeType)
	tag["total_fee"] = feeAmount
	tag["num"] = feeAmount
	tag["fee_amount"] = feeAmount

	apolloKey, _ := prov.GetApolloParams(biz_runtime.WithPIDKey)
	passGeneral := int64(consts.ProductIDDefault) == prov.GetProductId() &&
		apollo.FeatureToggle(ctx, "pre_sale_fee_msg_old_template_toggle", apolloKey, map[string]string{
			"product_id":       strconv.FormatInt(prov.GetProductId(), 10),
			"product_category": strconv.FormatInt(prov.GetProductCategory(), 10),
			"count_price_type": strconv.FormatInt(int64(prov.GetCountPriceType()), 10),
			"city":             strconv.FormatInt(int64(prov.GetCityID()), 10),
		})

	feeTemplate := dcmp.GetDcmpPlainContent(ctx, "common-price_desc")
	// 通用文案：优先级最高
	if generalTmp != "" && !passGeneral {
		feeTemplate = generalTmp
	}

	feeMsg = dcmp.TranslateTemplate(feeTemplate, tag)

	if order.IsCapAPlus(ctx, prov) {
		//小数点位数实验
		feeType = consts.FeeTypeCapPrice
		feeAmount = util2.PriceFormat(ctx, param, cast.ToString(key), estimateFeeAmount, feeType)
		tag["total_fee"] = feeAmount
		tag["num"] = feeAmount

		feeTemplate = dcmp.GetDcmpPlainContent(ctx, "cap_price-price_desc")
		// 通用文案：优先级最高
		if generalTmp != "" {
			feeTemplate = generalTmp
		}

		feeMsg = dcmp.TranslateTemplate(feeTemplate, tag)
		if _, feeDescCapAPlus := GetAPlusCapDesc(ctx, prov); feeDescCapAPlus != "" {
			feeTemplate = feeDescCapAPlus
			if generalTmp != "" {
				feeTemplate = generalTmp
			}
			feeMsg = dcmp.TranslateTemplate(feeTemplate, tag)
		}
	}

	return FeeInfoResp{
		FeeMsg:      feeMsg,
		FeeAmount:   feeAmount,
		FeeType:     feeType,
		FeeTemplate: feeTemplate,
	}
}

func GetNormalEstimateFeeInfo(ctx context.Context, prov PriceInfoProvider) FeeInfoResp {
	feeInfoResp := FeeInfoResp{}
	if prov.GetProductId() == product_id.ProductIdAutoDriving {
		key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
		params["func_type"] = "1"
		if !apollo.FeatureToggle(ctx, "auto_driving_fee_msg_open_city", key, params) {
			return feeInfoResp
		}
	} else if prov.GetBillDetail() == nil {
		feeInfoResp.FeeMsg = RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "fail", "")
		return feeInfoResp
	}

	personalEstimateFee := prov.GetPersonalEstimateFee()
	price.CheckSingle(ctx, "GetNormalEstimateFeeInfo", "NormalEstimateFeeInfo", "personalEstimateFee", personalEstimateFee, price.WithCheckBelowZero())
	feeInfoResp.FeeAmount = util.FormatPriceWithoutZero(personalEstimateFee, 2)
	deductFee := prov.GetMixedDeductPrice()
	if deductFee > 0 {
		template := dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "mix_personal")
		feeInfoResp.FeeTemplate = template
		feeInfoResp.FeeMsg = dcmp.TranslateTemplate(template, map[string]string{
			"num": feeInfoResp.FeeAmount,
		})
	} else {
		template := GetNormalFeeMsgTemplate(ctx, prov)
		feeInfoResp.FeeTemplate = template
		feeInfoResp.FeeMsg = dcmp.TranslateTemplate(template, map[string]string{
			"num": feeInfoResp.FeeAmount,
		})
	}

	// 特快range
	feeInfoResp = buildAPlusRange(ctx, prov, feeInfoResp, false)
	apolloKey, apolloParams := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if !apollo.FeatureToggle(ctx, "enable_render_aplus_range", apolloKey, apolloParams) {
		feeInfoResp.MinFeeAmount = ""
		feeInfoResp.FeeRangeTemplate = ""
	}

	return feeInfoResp
}

func GetNormalFeeMsgTemplate(ctx context.Context, prov PriceInfoProvider) (template string) {
	if int64(consts.ProductIDDefault) == prov.GetProductId() {
		key := fmt.Sprintf("%d_%d_%d", prov.GetProductCategory(), prov.GetCountPriceType(), prov.GetCityID())
		template = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg_special", nil, key)
		if template != "" {
			return template
		}
	}

	if order.IsAPlus(ctx, prov) {
		if prov.GetCapPrice() > 0 {
			template = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "upfront_fare")
			if feeDescCapAPlus, _ := GetAPlusCapDesc(ctx, prov); feeDescCapAPlus != "" {
				template = feeDescCapAPlus
			}
			return template
		}
	} else if order.IsFlatRate(prov.GetComboType(), 0) || order.IsAirportFlatRate(prov.GetProductId(), prov.GetComboType(), prov.GetCapPrice()) {
		template = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "upfront_fare")
		return template
	} else if order.IsMultiFactorFlatRateV2(prov) || order.IsDiOne(ctx, prov) || order.IsDioneCountPriceType(prov) ||
		(tripcloud.IsTripcloudProductID(CommonConsts.ProductID(prov.GetProductId())) && prov.GetCapPrice() > 0) ||
		order.IsCapFast(prov) {
		template = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "flat_rate")
		return template
	}

	if prov.GetCapPrice() > 0 {
		template = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "upfront_fare")
		//展示固定价
		//短期方案，长期方案：与pre-sale拉齐
		apolloKey, apolloParams := prov.GetApolloParams(biz_runtime.WithPIDKey)
		if apollo.FeatureToggle(ctx, "is_show_fix_price_controller", apolloKey, apolloParams) {
			template = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "fixed_price")
		}
		if apollo.FeatureToggle(ctx, "one_call_car_pincheche_fee_msg", apolloKey, apolloParams) {
			template = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "flat_rate_least")
		}
		return
	}
	template = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "normal_with_prefix")
	return template
}

func RenderByCommonTemplate(ctx context.Context, prov PriceDescProvider, feeAmount string, template string) string {
	currencyUnit, currencySymbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
	tag := map[string]string{
		"total_fee":       feeAmount,
		"num":             feeAmount,
		"currency_unit":   currencyUnit,
		"currency_symbol": currencySymbol,
	}

	if generalTmp := util2.GetGeneralFeeMsgTemplate(ctx, prov.GetCityID(), int(prov.GetProductCategory()), prov.GetLang()); generalTmp != "" {
		template = generalTmp
	}

	return dcmp.TranslateTemplate(template, tag)
}

type aPlusApolloConfig struct {
	Cities                []string `json:"cities"`
	FeeDescTemplate       string   `json:"fee_desc_template"`        // 预估页价格描述模版
	FeeDescTemplateAnycar string   `json:"fee_desc_template_anycar"` // 等待应答价格描述模版
}

func GetAPlusCapDesc(ctx context.Context, prov PriceDescProvider) (string, string) {
	configs, err := apollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, "a_plus_cap_fee_desc_by_city", map[string]string{}, reqctx.GetLang(ctx))
	if configs == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo a_plus_cap_fee_desc_by_city read err with %v and conf %v", err, configs)
		return "", ""
	}

	for _, config := range configs {
		rawConfig := config.GetJsonConfigs()
		var rawConfigBytes []byte
		if rawConfigBytes, err = json.Marshal(rawConfig); err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo a_plus_cap_fee_desc_by_city marshal err with %v and conf %v", err, rawConfig)
			return "", ""
		}

		apolloConfig := &aPlusApolloConfig{}
		if err = json.Unmarshal(rawConfigBytes, apolloConfig); err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo a_plus_cap_fee_desc_by_city unmarshal err with %v and conf %s", err, string(rawConfigBytes))
			return "", ""
		}

		for _, city := range apolloConfig.Cities {
			if city == strconv.Itoa(prov.GetCityID()) {
				return apolloConfig.FeeDescTemplate, apolloConfig.FeeDescTemplateAnycar
			}
		}
	}

	return "", ""
}

func getHKFeeInfo(ctx context.Context, prov PriceDescProvider) (feeMsg string, feeAmount string, feeType int) {

	price.CheckSingle(ctx, "getTaxiFeeInfo", "TaxiFeeInfo", "estimateFee", prov.GetEstimateFee(), price.WithCheckBelowZero())
	key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
	feeAmount = util2.PriceFormat(ctx, param, cast.ToString(key), prov.GetEstimateFee(), consts.FeeTypeDefault)
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdHKTaxi {
		feeMsg = dcmp.GetDcmpContent(ctx, "hk_taxi-price_desc_without_price", nil)
		return
	}

	tag := map[string]string{
		"num": feeAmount,
	}
	feeMsg = dcmp.GetDcmpContent(ctx, "hk_taxi-price_desc", tag)
	feeType = consts.FeeTypeDefault

	return
}

func getCarpoolEstimateFeeInfo(ctx context.Context, prov PriceInfoProvider) (feeMsg string, feeAmount string) {
	amount := GetCarpoolFeeAmount(ctx, prov)
	feeAmount = util.FormatPriceWithoutZero(amount, 2)
	userSelectNum := prov.GetExactSeatNum()
	if userSelectNum <= 0 {
		userSelectNum = 1
	}

	dcmpPath := "normal_with_prefix"
	if carpool.IsPinCheCheV2(prov.GetProductCategory(), prov.GetCarpoolPriceType()) {
		dcmpPath = "flat_rate_least"
	} else if carpool.IsCarpoolFlatRate(prov.GetCarpoolType()) {
		dcmpPath = "flat_rate"
	} else if carpool.IsInterCityCarpool(prov.GetCarpoolType(), prov.GetComboType()) {
		dcmpPath = "flat_rate"
		apolloKey, apolloParams := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithRouteGroup, biz_runtime.WithComboID, biz_runtime.WithProductID)
		hit, _ := PreferentialStatus(ctx, apolloKey, apolloParams)
		if hit {
			dcmpPath = "flat_rate_least"
		}
	} else if carpool.IsMiniBus(util.ToInt(prov.GetCarpoolType())) {
		dcmpPath = "mini_bus"
	} else if carpool.IsSmartBus(util.ToInt(prov.GetCarpoolType())) {
		dcmpPath = "smart_bus"
	} else if carpool.IsCarpoolUnSuccessFlatPrice(prov) && prov.IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx) {
		dcmpPath = "flat_rate"
		if prov.IsHaveCarpoolVCard(ctx) {
			dcmpPath = "carpool_two_price.confirm_succ"
		}
	}

	deductFee := prov.GetMixedDeductPrice()
	if deductFee > 0 {
		dcmpPath = "mix_personal"
	}

	tag := map[string]string{
		"num":      feeAmount,
		"seat_num": util.ToString(userSelectNum),
	}
	feeMsg = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-estimate_price_msg", tag, dcmpPath)
	return feeMsg, feeAmount
}

func GetCarpoolFeeAmount(ctx context.Context, prov PriceInfoProvider) float64 {
	estimateFee := prov.GetEstimateFee()
	price.CheckSingle(ctx, "GetCarpoolFeeAmount", "CarpoolFeeAmount", "estimateFee", estimateFee, price.WithCheckBelowZero())
	if carpool.IsPinCheCheV2(prov.GetProductCategory(), prov.GetCarpoolPriceType()) {
		if prov.IsBusinessPay() {
			return 0.0
		}
		priceList := make([]float64, 0)
		sceneList := prov.GetSceneEstimatePrice()
		for _, scene := range sceneList {
			priceList = append(priceList, scene.GetFee())
		}
		if len(priceList) > 0 {
			sort.Float64s(priceList)
			estimateFee = priceList[0]
		}
		estimateFee = carpool3.FormatPrice(estimateFee, carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), util.RoundAbs(estimateFee, 1)).FloatVal
	} else if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
		if prov.IsBusinessPay() {
			return 0.0
		}
		estimateFee = carpool3.FormatPrice(estimateFee, carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), util.RoundAbs(math.Ceil(estimateFee*10)/10, 1)).FloatVal
	}

	deductFee := prov.GetMixedDeductPrice()
	if deductFee > 0 {
		estimateFee = math.Max(estimateFee-deductFee, 0.0)
	}

	return estimateFee
}

func PreferentialStatus(ctx context.Context, key string, params map[string]string) (bool, int) {
	if !apollo.FeatureToggle(ctx, "intercity_carpool_subscribe_preferential_toggle", key, params) {
		return false, 0
	}

	isAllow, assignment := apollo.FeatureExp(ctx, "yuantu_reservation_discount", key, params)
	if isAllow && assignment.GetGroupName() == "treatment_group" {
		timeInterval, ok := assignment.GetParameters()["time_interval"]
		if !ok {
			timeInterval = "0"
		}

		return true, util.ToInt(timeInterval) * 60
	}

	return false, 0
}

func getSpecialRateFeeInfo(ctx context.Context, prov PriceDescProvider) (feeMsg string, feeAmount string) {
	feeAmount = util.FormatPriceWithoutZero(prov.GetPersonalEstimateFee(), 2)
	price.CheckSingle(ctx, "getSpecialRateFeeInfo", "SpecialRateFeeInfo", "feeAmount", prov.GetPersonalEstimateFee(), price.WithCheckBelowZero())
	// 企业支付
	if prov.IsBusinessPay() {
		feeMsg = RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "mix_personal", feeAmount)
		return feeMsg, feeAmount
	}

	// 强制预估价
	toggleKey, toggleParams := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if apollo.FeatureToggle(ctx, "gs_fee_msg_by_city", toggleKey, toggleParams) {
		feeMsg = RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "normal_with_prefix", feeAmount)
		return feeMsg, feeAmount
	}

	// 一口价
	if prov.GetCapPrice() > 0 {
		feeMsg = RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "upfront_fare", feeAmount)
		return feeMsg, feeAmount
	}

	feeMsg = RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "normal_with_prefix", feeAmount)
	return feeMsg, feeAmount
}

func getSpaciousCarFeeInfo(ctx context.Context, prov PriceDescProvider) (feeMsg string, feeAmount string) {
	feeAmount = util.FormatPriceWithoutZero(prov.GetPersonalEstimateFee(), 2)
	price.CheckSingle(ctx, "getSpaciousCarFeeInfo", "SpaciousCarFeeInfo", "feeAmount", prov.GetPersonalEstimateFee(), price.WithCheckBelowZero())
	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if spacious_common.HitSingle(ctx, key, params) {
		if prov.GetCapPrice() > 0 {
			feeMsg = RenderFeeMsg(ctx, "config_text-link_product_spacious_car_alliance", "cap_price", feeAmount)
			return feeMsg, feeAmount
		}

		feeMsg = RenderFeeMsg(ctx, "config_text-link_product_spacious_car_alliance", "fee_msg_single", feeAmount)
		return feeMsg, feeAmount
	}

	feeDetailInfo := prov.GetBillFeeDetailInfo()
	isHasSelectFee, isHasDiscount, selectionFee := spacious_common.GetSpaciousCarInfo(feeDetailInfo)
	if isHasSelectFee && isHasDiscount {
		feeMsg = RenderFeeMsg(ctx, "config_text-link_product_spacious_car_alliance", "fee_msg_first", util.FormatPriceWithoutZero(selectionFee, 2))
		return feeMsg, feeAmount
	} else if isHasSelectFee {
		feeMsg = RenderFeeMsg(ctx, "config_text-link_product_spacious_car_alliance", "fee_msg", util.FormatPriceWithoutZero(selectionFee, 2))
		return feeMsg, feeAmount
	} else {
		feeMsg = RenderFeeMsg(ctx, "config_text-link_product_spacious_car_alliance", "fee_msg", "0")
		return feeMsg, feeAmount
	}
}

func getBargainFeeInfo(ctx context.Context, prov PriceInfoProvider) (feeMsg string, feeAmount string) {
	feeAmount = util.FormatPriceWithoutZero(getBargainFeeAmount(ctx, prov), 2)
	price.CheckSingle(ctx, "getBargainFeeInfo", "BargainFeeInfo", "feeAmount", getBargainFeeAmount(ctx, prov))
	feeMsg = RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "bargain_with_prefix", feeAmount)
	return feeMsg, feeAmount
}

func getBargainFeeAmount(ctx context.Context, prov PriceInfoProvider) float64 {
	_, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if apollo.FeatureToggle(ctx, "bargain_recommend_v2_switch", "", params) {
		//命中端上推荐二期新版本，展示券后价
		return util.RoundAbs(prov.GetEstimateFee(), 1)
	} else {
		// 司乘议价表单输入框展示
		price := getBargainRecommendInfo(ctx, prov)
		if price > 0 {
			util.RoundAbs(price, 1)
		}
		//推荐价为空，兜底使用券前价
		return util.RoundAbs(prov.GetDynamicTotalFee(), 1)
	}
}

func getBargainRecommendInfo(ctx context.Context, prov PriceInfoProvider) float64 {
	bubbleInfo := prov.GetBargainRecommendInfo()
	if bubbleInfo != nil && bubbleInfo.RecommendPrice != nil && bubbleInfo.RecommendPrice[0] != nil {
		pri := bubbleInfo.RecommendPrice[0].Price
		price.CheckSingle(ctx, "getBargainRecommendInfo", "BargainRecommendInfo", "RecommendPrice", pri)
		return pri
	}

	return 0.0
}

func GetFeeMsgPrefix(feeMsg string) string {
	feeMsg = strings.ReplaceAll(feeMsg, "{", "")
	feeMsg = strings.ReplaceAll(feeMsg, "}", "")
	feeMsg = strings.ReplaceAll(feeMsg, " ", "")
	re := regexp.MustCompile(`^(.*?)(\d+)`)
	matches := re.FindStringSubmatch(feeMsg)

	if len(matches) > 1 {
		return matches[1]
	} else if len(matches) == 0 {
		return feeMsg
	}

	return ""
}

func BuildFeeInfoByOrder(ctx context.Context, orderInfo *order_info.OrderInfo, product *biz_runtime.ProductInfoFull) (string, string, int) {
	feeMsg, feeAmount, feeType := GetFeeInfo(ctx, product)

	feeMsg = GetFeeMsgPrefix(feeMsg)
	if orderInfo != nil && len(orderInfo.GetSceneList()) > 0 {
		for _, item := range orderInfo.GetSceneList() {
			if item == nil {
				continue
			}

			if util.InArrayInt(item.Id, []int{consts.PrivilegeFastUpgradePremier, consts.PrivilegePremierUpgradeLuxury, consts.WaitUpgrade}) {
				feeMsg = dcmp.GetJSONContentWithPath(ctx, "common-upgrade_price_desc", nil, strconv.Itoa(item.Id))
				break
			}
		}
	}

	if len(feeMsg) <= 0 {
		feeMsg = dcmp.GetJSONContentWithPath(ctx, "common-price_description", nil, "order_estimate_prefix_default")
	}

	return feeMsg, feeAmount, feeType
}

func GetPetFeeMsg(ctx context.Context, prov PriceDescProvider) (res FeeInfoResp) {

	desc := product_center.GetProductPriceDesc(int(prov.GetProductCategory()), strconv.Itoa(prov.GetCityID()), prov.GetLang())
	if desc != nil && desc.FeeMsg != "" {
		return FeeInfoResp{
			FeeMsg: desc.FeeMsg,
		}
	}

	feeMsg, _ := getSpecialRateFeeInfo(ctx, prov)
	return FeeInfoResp{
		FeeMsg: feeMsg,
	}
}
