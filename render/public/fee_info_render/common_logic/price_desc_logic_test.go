package common_logic

import (
	"context"
	"fmt"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/tidwall/gjson"
	"testing"
)

type AnyCarV4AdapterTest struct {
	*biz_runtime.ProductInfoFull
	// apolloParams *plain_text_render.ApolloParams
}

func (a *AnyCarV4AdapterTest) IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool {
	return false
}

func (a *AnyCarV4AdapterTest) GetLevelID() int32 {
	return 3
}
func (a *AnyCarV4AdapterTest) GetCarpoolFailCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	return &PriceApi.EstimateNewFormCouponInfo{
		CustomTag: "member_v3",
		Amount:    "100000",
	}
}
func (a *AnyCarV4AdapterTest) GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	return &PriceApi.EstimateNewFormCouponInfo{
		CustomTag: "member_v3",
		Amount:    "100000",
	}
}

func (a *AnyCarV4AdapterTest) GetTabId() string {
	return a.BaseReqData.CommonInfo.TabId
}

func (a *AnyCarV4AdapterTest) GetEstimateStyleType() int32 {
	return a.BaseReqData.CommonInfo.EstimateStyleType
}

func (a *AnyCarV4AdapterTest) GetTimeSpan() []*EstimateDecision.TimeSpanV2 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil || len(bizInfo.TimeSpan) == 0 {
		return nil
	}
	return bizInfo.TimeSpan
}

func (a *AnyCarV4AdapterTest) GetEstimateFeeAmount() float64 {
	return 5.69
}

func (a *AnyCarV4AdapterTest) GetBonus() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (a *AnyCarV4AdapterTest) GetCoupon() *fee_info_render.FeeItem {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return nil
	}
	coupon := fee.GetFeeDetail().GetCoupon()
	if coupon == nil {
		return nil
	}
	return &fee_info_render.FeeItem{
		Tag:    coupon.Tag,
		Amount: coupon.Amount,
	}
}

func (a *AnyCarV4AdapterTest) GetHolidayFee() (amount float64) {

	if a.GetBillDisplayLines() == nil {
		return
	}

	for _, item := range a.GetBillDisplayLines() {
		if item.Name == "red_packet" && item.Value > 0.0 {
			amount = item.Value
		}
	}

	return
}

func (a *AnyCarV4AdapterTest) GetMaxSeatNum() int32 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.MaxCarpoolSeatNum
}
func (a *AnyCarV4AdapterTest) GetUserSelectSeatNum() int32 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.CarpoolSeatNum
}

func (a *AnyCarV4AdapterTest) GetAllPaymentOptions() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := a.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (a *AnyCarV4AdapterTest) GetCurrentPaymentType() int {
	opts := a.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (a *AnyCarV4AdapterTest) GetMultiRequireProduct() []models.RequireProduct {
	if a.BaseReqData == nil {
		return nil
	}

	return a.BaseReqData.CommonBizInfo.MultiRequireProduct
}

func (a *AnyCarV4AdapterTest) GetMenuId() string {
	return a.BaseReqData.CommonInfo.MenuID
}

func (a *AnyCarV4AdapterTest) GetUserPhone() string {
	if a.BaseReqData == nil {
		return ""
	}
	return a.BaseReqData.PassengerInfo.Phone
}

func (a *AnyCarV4AdapterTest) GetUserChoosePayment() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonInfo.PaymentsType
}

func (a *AnyCarV4AdapterTest) GetCarpoolSeatNum() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonBizInfo.CarpoolSeatNum
}

func (a *AnyCarV4AdapterTest) GetProductCheckStatus() int {
	return a.GetBizInfo().CheckStatus
}

func (a *AnyCarV4AdapterTest) GetPassengerCount() *int32 {
	if a.BaseReqData == nil {
		return nil
	}
	return a.BaseReqData.CommonBizInfo.PassengerCount
}

func (a *AnyCarV4AdapterTest) GetIsShowFemaleDriver() int64 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonBizInfo.IsShowFemaleDriverFirst
}

func (a *AnyCarV4AdapterTest) GetBillRouteIdList() []string {
	if a.GetBillInfo() == nil {
		return nil
	}
	return a.GetBillInfo().RouteIdList
}

func (a *AnyCarV4AdapterTest) GetBillDriverMetre() int64 {
	if a.GetBillInfo() == nil {
		return 0
	}
	return a.GetBillInfo().DriverMetre
}

func (a *AnyCarV4AdapterTest) GetBillFeeDetailInfoFee(key string) (bool, float64) {
	if len(a.GetBillFeeDetailInfo()) < 1 {
		return false, 0
	}
	return true, a.GetBillFeeDetailInfo()[key]
}

func (a *AnyCarV4AdapterTest) GetFullProduct() *biz_runtime.ProductInfoFull {
	return a.ProductInfoFull
}

func (a *AnyCarV4AdapterTest) GetFastEstimatePrice() float64 {
	return a.BaseReqData.CommonBizInfo.FastCarPrice
}

// GetMixedDeductPrice 返回企业付 抵扣金额
func (a *AnyCarV4AdapterTest) GetMixedDeductPrice() float64 {
	var deductInfo float64

	if !a.IsBusinessPay() {
		return 0.0
	}

	deductInfo = a.GetEstimateFee()

	if payInfo := a.GetPaymentInfo(); payInfo != nil && payInfo.MixedPayDeductInfo != nil && payInfo.MixedPayDeductInfo.DeductFee > 0 {
		deductInfo = payInfo.MixedPayDeductInfo.DeductFee
	}

	return deductInfo
}

func (a *AnyCarV4AdapterTest) GetVcard() *PriceApi.EstimateNewFormVCardInfo {
	return a.GetVCard()
}

func (a *AnyCarV4AdapterTest) GetCountPriceType() int32 {
	if a.GetBillInfo() == nil {
		return 0
	}

	return a.GetBillInfo().CountPriceType
}

func (a *AnyCarV4AdapterTest) GetMemberLevel() int32 {
	if a.GetPrivateBizInfo() == nil {
		return 0
	}

	return a.GetPrivateBizInfo().UserMemberProfile.LevelID
}

func (a *AnyCarV4AdapterTest) GetCarpoolFailExactEstimateFee() float64 {
	if len(a.GetExtendList()) == 1 {
		return a.GetExtendList()[0].ExactEstimateFee
	}

	return 0
}

func (a *AnyCarV4AdapterTest) GetNoAnswerCompensationData() []*Compensation.GetNoAnswerCompensationTackData {
	if a.BaseReqData == nil {
		return nil
	}

	return a.GetCommonBizInfo().NoAnswerCompensationData
}

func (a *AnyCarV4AdapterTest) GetRecPos(category int64) *int32 {
	if a.BaseReqData == nil || a.BaseReqData.CommonBizInfo.RecPosMap == nil {
		return nil
	}
	res, ok := a.BaseReqData.CommonBizInfo.RecPosMap[int32(category)]
	if ok {
		return &res
	}

	return nil
}

func (a *AnyCarV4AdapterTest) GetFontScaleType() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonInfo.FontScaleType
}

func (a *AnyCarV4AdapterTest) GetAppVersion() string {
	if a.BaseReqData == nil {
		return ""
	}
	return a.BaseReqData.CommonInfo.AppVersion
}

func (a *AnyCarV4AdapterTest) IsBusinessPay() bool {
	return false
}

func (a *AnyCarV4AdapterTest) ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string) {
	return
}

func (a *AnyCarV4AdapterTest) GetDirectEstimatePrice() biz_runtime.SceneEstimateFeeViewer {
	return biz_runtime.NewSceneEstimateFeeViewer(
		nil,
		&price_api.BillInfoV3{
			DynamicTotalFee: 11.11,
		},
		&price_api.DiscountSetV3{
			Coupon: &PriceApi.EstimateNewFormCouponInfo{
				BatchId:      "",
				CouponType:   "",
				CouponSource: "",
				Amount:       "1",
				Discount:     0,
				CustomTag:    "CustomTagSuc",
				CouponTag:    "",
				CouponId:     "",
				ExpireTime:   nil,
				CouponAmount: nil,
				ExtraInfo:    nil,
			},
		}, nil)
}

func (a *AnyCarV4AdapterTest) GetSceneEstimatePrice() []biz_runtime.SceneEstimateFeeViewer {
	return []biz_runtime.SceneEstimateFeeViewer{
		biz_runtime.NewSceneEstimateFeeViewer(
			nil,
			&price_api.BillInfoV3{},
			&price_api.DiscountSetV3{
				Coupon: &PriceApi.EstimateNewFormCouponInfo{
					BatchId:      "",
					CouponType:   "",
					CouponSource: "",
					Amount:       "1",
					Discount:     0,
					CustomTag:    "CustomTagFail",
					CouponTag:    "",
					CouponId:     "",
					ExpireTime:   nil,
					CouponAmount: nil,
					ExtraInfo:    nil,
				},
			},
			map[string]string{
				"is_carpool_success": "0",
				"seat_num":           "0",
			}),
	}
}

func TestGetDiscountInfo(t *testing.T) {

	pat := gomonkey.NewPatches()

	pat.ApplyFunc(DualPriceOrderedOnAscInner, func(prov apollo_model.ApolloParamsProvider) bool {
		fmt.Println("Mock DualPriceOrderedOnAscInner Suc...")
		return true
	})

	pat.ApplyFunc(dcmp.GetJSONMap, func(ctx context.Context, key string, path string) map[string]gjson.Result {
		fmt.Println("Mock dcmp.GetJSONMap Suc...")
		return map[string]gjson.Result{
			"content":      gjson.Parse("{拼成惠-{{suc_num}}元}"),
			"icon":         gjson.Parse(""),
			"border_color": gjson.Parse("#FFC2BF"),
			"title":        gjson.Parse("汇总优惠"),
		}
	})

	defer pat.Reset()

	prov := &AnyCarV4AdapterTest{
		ProductInfoFull: &biz_runtime.ProductInfoFull{
			BaseReqData: nil,
			Product: &models.Product{
				ProductCategory: 3,
				BizInfo: &models.PrivateBizInfo{
					CarpoolSeatNum: 0,
				},
			},
			ExtraInfo: nil,
		},
	}

	info := GetDiscountInfo(context.Background(), prov, 20)
	fmt.Println(info.GetCustomTag())
}
