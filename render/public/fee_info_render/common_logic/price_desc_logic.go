package common_logic

import (
	"context"
	"encoding/json"
	"math"
	"strconv"
	"strings"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	cardRenderCore "git.xiaojukeji.com/gulfstream/carpool-communication-go/cardController"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/passenger-common/constant/communicate"
	util2 "git.xiaojukeji.com/gulfstream/passenger-common/util"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

const (
	RedPacketFee        = "red_packet"
	TaxiPeakFeeKey      = "taxi_peak_price"    // 出租车峰期加价
	TaxiPeakDiscountKey = "taxi_peak_discount" // 出租车峰期折扣
)

const (
	SceneMarkIsCarpoolFail = "0"
	SceneMarkIsCarpoolSuc  = "1"
)

const (
	SuffixCarpoolSuc  = "-carpool_suc"  //拼成感知配置 suffix
	SuffixCarpoolDual = "-carpool_dual" //拼成、未拼成感知配置 suffix
	SuffixCarpoolFail = "-carpool_fail" //未拼成感知配置 suffix
)

type PriceDescCommonProvider interface {
	GetProductId() int64
	GetProductCategory() int64
	GetCarpoolType() int64
	GetComboType() int64
	GetRequireLevel() string
	GetCarpoolPriceType() int32
	IsDualCarpoolPrice() bool
	IsSpecialPrice() bool
	GetOrderType() int16
	GetFastEstimatePrice() float64
	GetEstimateFee() float64
	GetLevelID() int32
	GetCurrentSeatNum() int32

	//Render.ApolloProvider
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)

	GetBillDetail() *PriceApi.EstimateNewFormBillInfo

	GetBillFeeDetailInfo() map[string]float64
	GetBillDriverMetre() int64
	GetBillInfoCurrency() string
	GetCarpoolFailEstimateFee() (float64, bool)
	IsCarpoolV3Merge(context.Context) bool
	IsBusinessPay() bool
	GetExactEstimateFee() float64
	GetCarpoolFailExactEstimateFee() float64
	GetTotalFeeWithoutDiscount() float64

	GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo
	GetBonus() float64
	GetHkBonus() *PriceApi.EstimateNewFormBonusInfo
	GetCarpoolFailedCoupon() *PriceApi.EstimateNewFormCouponInfo
	GetVCard() *PriceApi.EstimateNewFormVCardInfo
	GetDiscountCard() *PriceApi.DiscountCard

	GetMixedDeductPrice() float64
	GetRevolvingAccountDiscount() *PriceApi.RevolvingAccountDiscountInfo
	GetRevolvingAccountRebate() *PriceApi.RevolvingAccountRebateInfo
	GetOpenCitySourceId() int32
	GetSceneEstimatePrice() []biz_runtime.SceneEstimateFeeViewer
	GetDirectEstimatePrice() biz_runtime.SceneEstimateFeeViewer
}

type FeeDescHandlerFunc func(ctx context.Context, prov PriceDescCommonProvider, pageType int32) *proto.NewFormFeeDesc

type CustomTagMap struct {
	Content         string `json:"content"`
	Icon            string `json:"icon"`
	ProductCategory []int  `json:"product_category"`
	MaxDeduction    int    `json:"max_deduction"`
	BorderColor     string `json:"border_color"` //边框RGB色值, 如#000000, 空串标识无边框
}

type DcmpVcard struct {
	VcardText map[string]DcmpItem
}

type DcmpItem struct {
	LeftIcon    string `json:"left_icon"`
	Text        string `json:"text"`
	TextColor   string `json:"text_color"`
	BorderColor string `json:"border_color"`
	BgColor     string `json:"bg_color"`
}

// GetIncrementItems 构建加价项
func GetIncrementItems(ctx context.Context, prov PriceDescCommonProvider, num int, functionList []FeeDescHandlerFunc, pageType int32) []*proto.NewFormFeeDesc {
	var (
		increItems []*proto.NewFormFeeDesc
	)

	for _, incrementFunc := range functionList {
		if len(increItems) >= num {
			break
		}

		if desc := incrementFunc(ctx, prov, pageType); desc != nil && len(desc.Content) > 0 {
			increItems = append(increItems, desc)
		}
	}

	return increItems
}

func BuildDecrementItems(ctx context.Context, prov PriceDescCommonProvider, functionList []FeeDescHandlerFunc, pageType int32) []*proto.NewFormFeeDesc {
	var decItems []*proto.NewFormFeeDesc
	cap := 1
	cnt := 0

	for _, function := range functionList {
		if cnt >= cap {
			break
		}
		// 遍历需要执行的减价项
		if priceDesc := function(ctx, prov, pageType); priceDesc != nil && priceDesc.Content != "" {
			decItems = append(decItems, priceDesc)
			cnt += 1
		}
	}
	return decItems
}

// GetRedPacketFee 加价项:春节服务费
func GetRedPacketFee(ctx context.Context, prov PriceDescCommonProvider, pageType int32) *proto.NewFormFeeDesc {

	uidKey, params := prov.ApolloParamsGen(apollo_model.WithUIDKey)

	if !apollo.FeatureToggle(ctx, "gs_holiday_fee_fee_desc", uidKey, params) {
		return nil
	}
	if page_type.PageTypeUndefined == pageType {
		return GetHolidayFee4MainForm(ctx, prov.GetBillFeeDetailInfo())
	}
	return GetHolidayFee(ctx, prov.GetBillFeeDetailInfo())
}

func GetFastPrice(ctx context.Context, prov PriceDescCommonProvider, pageType int32) *proto.NewFormFeeDesc {
	fastFee := prov.GetFastEstimatePrice()
	if fastFee == 0.0 {
		return &proto.NewFormFeeDesc{}
	}

	conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-fee_desc", "carpool_diff_fast")
	if len(conf) <= 0 {
		return &proto.NewFormFeeDesc{}
	}

	//这里的处理逻辑是和主预估V3的拼成价格保持一致的
	succCarpoolPrice, err := util.FormatPriceCeilFloat64(prov.GetEstimateFee(), 1)
	if err != nil {
		return &proto.NewFormFeeDesc{}
	}
	diffFee := fastFee - succCarpoolPrice
	if diffFee <= 0 {
		return &proto.NewFormFeeDesc{}
	}

	diffFeeDesc := &proto.NewFormFeeDesc{}
	textColor := conf["text_color"].String()
	if prov.IsCarpoolV3Merge(ctx) {
		diffFeeDesc = &proto.NewFormFeeDesc{
			BorderColor: conf["border_color"].String(),
			Content: util.ReplaceTag(ctx, conf["content"].String(), map[string]string{
				"num": util.Float64ToString(diffFee)}),
			Icon:      conf["icon"].String(),
			TextColor: &textColor,
			Amount:    0.0,
			Type:      0,
		}
	} else {
		var (
			baseContent   string
			suffixContent string
			content       string
		)
		failPrice, ok := prov.GetCarpoolFailEstimateFee()
		if !ok {
			return &proto.NewFormFeeDesc{}
		}

		failCarpoolPrice, err1 := util.FormatPriceCeilFloat64(failPrice, 1)
		if err1 != nil {
			return &proto.NewFormFeeDesc{}
		}

		diffFailFee := fastFee - failCarpoolPrice
		if diffFailFee > 0 {
			baseContent = util.ReplaceTag(ctx, conf["content"].String(), map[string]string{"num": util.Float64ToString(diffFailFee)})
			suffixContent = util.ReplaceTag(ctx, conf["suffix"].String(), map[string]string{"num": util.Float64ToString(diffFee)})
			content = baseContent + suffixContent
			if sortContent := GetDualPricetContentWithSort(ctx, diffFee, diffFailFee, "estimate_form_v3-dual_price_fee_desc", "cheap_than_fast", DualPriceOrderedOnAsc(prov, pageType)); sortContent != "" {
				content = sortContent
			}
		} else {
			content = util.ReplaceTag(ctx, conf["content"].String(), map[string]string{"num": util.Float64ToString(diffFee)})
		}

		diffFeeDesc = &proto.NewFormFeeDesc{
			BorderColor: conf["border_color"].String(),
			Content:     content,
			Icon:        conf["icon"].String(),
			TextColor:   &textColor,
			Amount:      0.0,
			Type:        0,
		}
	}

	return diffFeeDesc
}

// xyq 极速拼车追加车型+默认拼
func GetMemberDoubleDiscount(ctx context.Context, prov PriceDescCommonProvider, pageType int32) *proto.NewFormFeeDesc {
	if pageType == page_type.PageTypeRecCarpoolEstimate {
		return &proto.NewFormFeeDesc{}
	}

	card := prov.GetDiscountCard()
	if card == nil || card.Amount <= 0 {
		return &proto.NewFormFeeDesc{}
	}

	conf := dcmp.GetJSONMap(ctx, "anycar_v3-fee_detail_desc", "carpool_member_discount_card")
	if conf == nil || len(conf) == 0 {
		return &proto.NewFormFeeDesc{}
	}

	var (
		icon      string
		textColor string
		text      string

		totalFeeWithoutDiscount float64
		succDiff                float64
		failDiff                float64
	)

	// 标签文案
	totalFeeWithoutDiscount = prov.GetTotalFeeWithoutDiscount()
	succDiff = totalFeeWithoutDiscount - prov.GetExactEstimateFee()
	if failFee := prov.GetCarpoolFailExactEstimateFee(); failFee > 0 && prov.GetExactEstimateFee() != failFee {
		failDiff = totalFeeWithoutDiscount - failFee
	}

	if succDiff > 0 && failDiff > 0 { // 两口价
		text = util.ReplaceTag(ctx, conf["content_dual"].String(), map[string]string{
			"num1": util.Float64ToString(failDiff), "num2": util.Float64ToString(succDiff)})
		if sortContent := GetDualPricetContentWithSort(ctx, succDiff, failDiff, "estimate_form_v3-dual_price_fee_desc", "member_discount_card", DualPriceOrderedOnAsc(prov, pageType)); sortContent != "" {
			text = sortContent
		}
	} else if succDiff > 0 { // 一口价
		text = util.ReplaceTag(ctx, conf["content_single"].String(), map[string]string{
			"num": util.Float64ToString(succDiff)})
	} else {
		return &proto.NewFormFeeDesc{}
	}

	// 会员标签
	if memberConf := dcmp.GetJSONMap(ctx, "estimate_form_v3-fee_desc_member_discount", strconv.Itoa(int(prov.GetLevelID()))); len(memberConf) > 0 {
		icon = memberConf["icon"].String()
		textColor = memberConf["text_color"].String()
	}

	output := &proto.NewFormFeeDesc{
		BorderColor:    conf["border_color"].String(),
		BgColor:        util.StringPtr(conf["bg_fill_color"].String()),
		HighlightColor: util.StringPtr(conf["highlight_color"].String()),
		Content:        text,
		Icon:           icon,
		TextColor:      util.StringPtr(textColor),
		Type:           3,
	}

	return output
}

func GetVcardDesc(ctx context.Context, prov PriceDescCommonProvider, pageType int32) *proto.NewFormFeeDesc {

	// 沟通体系: 高优走下面
	renderObj := cardRenderCore.NewCardRenderObj(log.Trace, communicate.BubbleAnycarV3Page, communicate.FeeDescComponent)
	if desc := renderObj.BuildFeeDesc(ctx, prov.GetVCard(), nil); desc != nil {
		return &proto.NewFormFeeDesc{
			BorderColor:    desc.BorderColor,
			Content:        desc.Content,
			Icon:           desc.Icon,
			TextColor:      desc.TextColor,
			BgColor:        desc.BgColor,
			HighlightColor: desc.HighlightColor,
		}
	}

	var (
		firstIndex  = "0"
		nextIndex   = "1"
		thirdIndex  = "2"
		fourthIndex = "3"
		fifthIndex  = "4"
	)

	dcmpVcard := &DcmpVcard{}
	vcardText := &dcmpVcard.VcardText
	// 读取极速拼车-新增赠卡相关文案
	config := dcmp.GetDcmpContent(ctx, "anycar_v3-new_free_card", nil)
	if err := json.Unmarshal([]byte(config), vcardText); err != nil {
		return &proto.NewFormFeeDesc{}
	}

	//1、获取Vcard服务
	vcardResult := prov.GetVCard()
	if vcardResult == nil {
		return &proto.NewFormFeeDesc{}
	}

	var failDesc *proto.NewFormFeeDesc
	if carpool.SendNew == vcardResult.Source {
		textColor := (*vcardText)[firstIndex].TextColor
		bgColor := (*vcardText)[firstIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[firstIndex].BorderColor,
			Content:     (*vcardText)[firstIndex].Text,
			Icon:        (*vcardText)[firstIndex].LeftIcon,
			Amount:      0.0,
			Type:        0,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	} else if carpool.SendLoss == vcardResult.Source {
		textColor := (*vcardText)[nextIndex].TextColor
		bgColor := (*vcardText)[nextIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[nextIndex].BorderColor,
			Content:     (*vcardText)[nextIndex].Text,
			Icon:        (*vcardText)[nextIndex].LeftIcon,
			Amount:      0.0,
			Type:        0,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	} else if carpool.Usable == vcardResult.Source && carpool.PayStatus == vcardResult.PayStatus {
		textColor := (*vcardText)[thirdIndex].TextColor
		bgColor := (*vcardText)[thirdIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[thirdIndex].BorderColor,
			Content:     (*vcardText)[thirdIndex].Text,
			Icon:        (*vcardText)[thirdIndex].LeftIcon,
			Amount:      0.0,
			Type:        0,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	} else if carpool.Usable == vcardResult.Source && carpool.PayStatus != vcardResult.PayStatus {
		textColor := (*vcardText)[fourthIndex].TextColor
		bgColor := (*vcardText)[fourthIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[fourthIndex].BorderColor,
			Content:     (*vcardText)[fourthIndex].Text,
			Icon:        (*vcardText)[fourthIndex].LeftIcon,
			Amount:      0.0,
			Type:        0,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	} else if carpool.UsableGive == vcardResult.Source {
		textColor := (*vcardText)[fifthIndex].TextColor
		bgColor := (*vcardText)[fifthIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[fifthIndex].BorderColor,
			Content:     (*vcardText)[fifthIndex].Text,
			Icon:        (*vcardText)[fifthIndex].LeftIcon,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	}
	return failDesc
}

func GetHolidayFee(ctx context.Context, feeDetailInfo map[string]float64) *proto.NewFormFeeDesc {
	if len(feeDetailInfo) < 1 {
		return nil
	}
	if redPacketFee := feeDetailInfo[RedPacketFee]; redPacketFee > 0 {
		if conf := dcmp.GetJSONMap(ctx, "anycar_v3-fee_detail_desc", "red_packet"); len(conf) > 0 {
			tag := map[string]string{
				"num": util.FormatPrice(redPacketFee, -1),
			}
			return &proto.NewFormFeeDesc{
				BorderColor: conf["border_color"].String(),
				Content:     util.ReplaceTag(ctx, conf["content"].String(), tag),
				Icon:        conf["icon"].String(),
				Amount:      redPacketFee,
			}
		}
	}
	return nil
}

func GetHolidayFee4MainForm(ctx context.Context, feeDetailInfo map[string]float64) *proto.NewFormFeeDesc {
	if len(feeDetailInfo) < 1 {
		return nil
	}
	dcmpKey := consts.EstimateV3FeeDesc
	if redPacketFee := feeDetailInfo[RedPacketFee]; redPacketFee > 0 {
		if conf := dcmp.GetJSONMap(ctx, dcmpKey, "red_packet"); len(conf) > 0 {
			tag := map[string]string{
				"num": util.FormatPrice(redPacketFee, -1),
			}
			return &proto.NewFormFeeDesc{
				BorderColor:    conf["border_color"].String(),
				TextColor:      util.String2PtrString(conf["text_color"].String()),
				HighlightColor: util.String2PtrString(conf["highlight_color"].String()),
				Content:        util.ReplaceTag(ctx, conf["content"].String(), tag),
				Icon:           conf["icon"].String(),
				Amount:         redPacketFee,
				Type:           consts.TypeIncrement,
			}
		}
	}
	return nil
}

func GetDualPriceBusinessPayFeeDesc(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, ascOrder bool, dcmpKey string) (priceDesc *proto.NewFormFeeDesc) {
	succFee := prov.GetEstimateFee()
	failFee, failFeeExist := prov.GetCarpoolFailEstimateFee()
	conf := dcmp.GetJSONMap(ctx, dcmpKey, "business_pay_deduction")
	if len(conf) == 0 {
		return nil
	}

	priceDesc = &proto.NewFormFeeDesc{
		BorderColor: conf["border_color"].String(),
		Icon:        conf["icon"].String(),
	}

	if prov.IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx) && failFeeExist {
		priceDesc.Content = dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": util.ToString(failFee)})
		priceDesc.Amount = failFee
		return
	}

	if sortContent := GetDualPricetContentWithSort(ctx, succFee, failFee, dcmpKey, "business_pay_deduction_multi", ascOrder); sortContent != "" {
		priceDesc.Content = sortContent
		priceDesc.Amount = succFee
		return
	}
	return nil
}

func IsSetDiscountSet(product *biz_runtime.ProductInfoFull) bool {
	if product.GetCouponInfo() != nil && len(product.GetCouponAmount()) > 0 {
		return true
	}

	if product.GetBonus() != nil && len(product.GetBonusAmountStr()) > 0 {
		return true
	}

	return false
}

type DualPriceOrderedProvider interface {
	GetOpenCitySourceId() int32
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
}

func DualPriceOrderedOnAsc(prov DualPriceOrderedProvider, pageType int32) bool {
	if !(pageType == 0 && prov.GetOpenCitySourceId() == source_id.SourceIDCompositeTravel85 || pageType == page_type.PageTypeGuideAnyCar) {
		return false
	}

	return DualPriceOrderedOnAscInner(prov)
}

func DualPriceOrderedOnAscV3(prov DualPriceOrderedProvider) bool {
	return DualPriceOrderedOnAscInner(prov)
}

func DualPriceOrderedOnAscInner(prov apollo_model.ApolloParamsProvider) bool {
	// dual_price_sort_toggle
	// v7list_carpool_price_exchange
	key, params := prov.ApolloParamsGen(apollo_model.WithPIDKey, apollo_model.WithProductCategory)
	if !apollo.FeatureToggle(nil, "dual_price_sort_toogle", key, params) {
		return false
	}
	// 实验分组
	expAllow, assignParams := apollo.GetParameters("v7list_carpool_price_exchange", key, params)

	if expAllow && assignParams != nil && len(assignParams) > 0 {
		if sort, ok := assignParams["success_fail"]; ok {
			return sort == "1"
		}
	}

	return false
}

func GetDualPricetContentWithSort(ctx context.Context, succDiff float64, failDiff float64, dcmpKey string, dcmpPath string, ascOrder bool) string {
	if succDiff == 0 && failDiff == 0 {
		return ""
	}

	var preContent, suffContent string
	conf := dcmp.GetJSONMap(ctx, dcmpKey, dcmpPath)
	if len(conf) == 0 {
		return ""
	}

	if ascOrder {
		preContent = dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": util.FormatPrice(succDiff, -1)})
		suffContent = dcmp.TranslateTemplate(conf["suffix"].String(), map[string]string{"num": util.FormatPrice(failDiff, -1)})
	} else {
		preContent = dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": util.FormatPrice(failDiff, -1)})
		suffContent = dcmp.TranslateTemplate(conf["suffix"].String(), map[string]string{"num": util.FormatPrice(succDiff, -1)})
	}

	return preContent + conf["connect"].String() + suffContent

}

// GetDiscountInfo for两口价。等应答追加出口fee_desc_list 根据拼成价格优惠决定 icon「惠-、券-」
func GetDiscountInfo(ctx context.Context, prov PriceDescCommonProvider, pageType int32) *proto.NewFormFeeDesc {
	//非等应答不渲染
	if page_type.PageTypeGuideAnyCar != pageType {
		return nil
	}

	if product_category.ProductCategoryCarpoolStation != prov.GetProductCategory() {
		return nil
	}

	var (
		failPriceViewer biz_runtime.SceneEstimateFeeViewer
		sucPriceViewer  biz_runtime.SceneEstimateFeeViewer

		couponSuc  *biz_runtime.Coupon
		couponFail *biz_runtime.Coupon

		spsSuccDiscout *float64
		spsFailDiscout *float64

		couponSuccDiscout *float64
		couponFailDiscout *float64

		couponSuccCustomTag string
		couponFailCustomTag string

		allFailDiscount *float64
		allSuccDiscount *float64

		communicateDiscount bool //惠沟通
	)

	/**
	 * 1、数据计算
	 */
	sucPriceViewer = prov.GetDirectEstimatePrice()

	priceInfos := prov.GetSceneEstimatePrice()
	for _, priceItem := range priceInfos {
		poolTag := priceItem.GetFeeAttributes().GetString("is_carpool_success")
		seatTag := priceItem.GetFeeAttributes().GetString("seat_num")
		if nil != poolTag && SceneMarkIsCarpoolFail == *poolTag && nil != seatTag && cast.ToString(prov.GetCurrentSeatNum()) == *seatTag {
			failPriceViewer = priceItem
			break
		}
	}

	if nil == failPriceViewer || nil == sucPriceViewer {
		return nil
	}

	sucFeeDetail := sucPriceViewer.GetFeeDetail()
	failFeeDetail := failPriceViewer.GetFeeDetail()

	//sps
	spsValue := func(data map[string]float64) (float64, bool, float64, bool) {
		v1, b1 := data["sps_carpool_discount_surprised"]
		v2, b2 := data["sps_carpool_discount_surprised_two"]

		return v1, b1, v2, b2
	}
	sucFeeDetailInfo := sucFeeDetail.GetFeeDetailInfo()
	if v1, ok1, v2, ok2 := spsValue(sucFeeDetailInfo); ok1 || ok2 {
		spsSuccDiscout = util.Float64Ptr(math.Abs(v1) + math.Abs(v2))
	}

	failFeeDetailInfo := failFeeDetail.GetFeeDetailInfo()
	if v1, ok1, v2, ok2 := spsValue(failFeeDetailInfo); ok1 || ok2 {
		spsFailDiscout = util.Float64Ptr(math.Abs(v1) + math.Abs(v2))
	}

	//coupon
	if couponSuc = sucFeeDetail.GetCoupon(); nil != couponSuc {
		couponSuccDiscout = &couponSuc.Amount
		couponSuccCustomTag = couponSuc.Tag
	}
	if couponFail = failFeeDetail.GetCoupon(); nil != couponFail {
		couponFailDiscout = &couponFail.Amount
		couponFailCustomTag = couponFail.Tag
	}

	succDiffPrice := sucFeeDetail.GetDynamicTotalFee() - sucPriceViewer.GetExactFee()   //dynamic_total_fee - exact_estimate_fee
	failDiffPrice := failFeeDetail.GetDynamicTotalFee() - failPriceViewer.GetExactFee() //dynamic_total_fee - exact_estimate_fee

	if 0 != failDiffPrice || nil != spsFailDiscout {
		allFailDiscount = util.Float64Ptr(failDiffPrice + util2.TernaryDelayExec(nil != spsFailDiscout, func() interface{} { return *spsFailDiscout }, func() interface{} { return float64(0) }).(float64)) //NOLINT
	}

	if 0 != succDiffPrice || nil != spsSuccDiscout {
		allSuccDiscount = util.Float64Ptr(succDiffPrice + util2.TernaryDelayExec(nil != spsSuccDiscout, func() interface{} { return *spsSuccDiscout }, func() interface{} { return float64(0) }).(float64)) //NOLINT
	}

	/**
	 * 2、渲染
	 */
	couponRenderCfg := func(customTag string, couponFailAmount, couponSucAmount *float64) map[string]gjson.Result {
		priceMerge := prov.IsCarpoolV3Merge(ctx)

		if nil == couponFailAmount && nil == couponSucAmount {
			return map[string]gjson.Result{}
		}

		customCouponKeyPath := func() (string, string) {
			dcmpKey := "anycar_estimate-custom_tag_coupon"
			_path := util2.Ternary(priceMerge, "merge-", "").(string) //NOLINT
			switch true {
			case nil == couponFailAmount && nil != couponSucAmount:
				_path += customTag + SuffixCarpoolSuc
			case nil != couponFailAmount && nil == couponSucAmount:
				_path += customTag + SuffixCarpoolFail
			default:
				_path += customTag + SuffixCarpoolDual
			}
			return dcmpKey, _path
		}
		key, path := customCouponKeyPath()
		if cfg_ := dcmp.GetJSONMap(ctx, key, path); len(cfg_) > 0 {
			return cfg_
		}

		commonCouponKeyPath := func() (string, string) {
			dcmpKey := "anycar_v3-fee_detail_desc"
			_path := util2.Ternary(priceMerge, "merge-", "").(string) //NOLINT
			switch true {
			case nil == couponFailAmount && nil != couponSucAmount:
				_path += "coupon" + SuffixCarpoolSuc
			case nil != couponFailAmount && nil == couponSucAmount:
				_path += "coupon" + SuffixCarpoolFail
			default:
				_path += "coupon" + SuffixCarpoolDual
			}
			return dcmpKey, _path
		}
		key, path = commonCouponKeyPath()
		if cfg_ := dcmp.GetJSONMap(ctx, key, path); len(cfg_) > 0 {
			return cfg_
		}

		return map[string]gjson.Result{}
	}

	DiscountRenderCfg := func(allFailDiscount, allSucDiscount *float64) map[string]gjson.Result {
		priceMerge := prov.IsCarpoolV3Merge(ctx)

		if nil == allFailDiscount && nil == allSucDiscount {
			return map[string]gjson.Result{}
		}

		//惠沟通
		dcmpKey := "anycar_v3-fee_detail_desc"
		path := util2.Ternary(priceMerge, "merge-", "").(string) //NOLINT
		switch true {
		case nil == allFailDiscount && nil != allSucDiscount:
			path += "discount" + SuffixCarpoolSuc
		case nil != allFailDiscount && nil == allSucDiscount:
			path += "discount" + SuffixCarpoolFail
		default:
			path += "discount" + SuffixCarpoolDual
		}
		return dcmp.GetJSONMap(ctx, dcmpKey, path)
	}

	renderData := func(communicateDiscount bool, allSuccDiscount, allFailDiscount *float64, couponSuccDiscout, couponFailDiscout *float64) (string, string, string, string) {
		sucBeforeFail := DualPriceOrderedOnAscInner(prov)
		if communicateDiscount {
			suc := util2.TernaryDelayExec(nil == allSuccDiscount, func() interface{} { return float64(0) }, func() interface{} { return *allSuccDiscount }).(float64)  //NOLINT
			fail := util2.TernaryDelayExec(nil == allFailDiscount, func() interface{} { return float64(0) }, func() interface{} { return *allFailDiscount }).(float64) //NOLINT
			price1 := util2.Ternary(sucBeforeFail, suc, fail).(float64)                                                                                                //NOLINT
			price2 := util2.Ternary(sucBeforeFail, fail, suc).(float64)                                                                                                //NOLINT

			return util.Float64ToString(suc), util.Float64ToString(fail), util.Float64ToString(price1), util.Float64ToString(price2)
		}

		suc := util2.TernaryDelayExec(nil == couponSuccDiscout, func() interface{} { return float64(0) }, func() interface{} { return *couponSuccDiscout }).(float64)  //NOLINT
		fail := util2.TernaryDelayExec(nil == couponFailDiscout, func() interface{} { return float64(0) }, func() interface{} { return *couponFailDiscout }).(float64) //NOLINT
		price1 := util2.Ternary(sucBeforeFail, suc, fail).(float64)                                                                                                    //NOLINT
		price2 := util2.Ternary(sucBeforeFail, fail, suc).(float64)                                                                                                    //NOLINT
		return util.Float64ToString(suc), util.Float64ToString(fail), util.Float64ToString(price1), util.Float64ToString(price2)
	}

	renderDispatch := func(allSuccDiscount *float64, couponFail *biz_runtime.Coupon) func(couponSuc, couponFail *biz_runtime.Coupon, allSuccDiscount, allFailDiscount, couponSuccDiscout, couponFailDiscout *float64) *proto.NewFormFeeDesc {
		//2.1拼成优惠。根据拼成决定渲染逻辑
		if nil != allSuccDiscount {
			return func(couponSuc, couponFail *biz_runtime.Coupon, allSuccDiscount, allFailDiscount, couponSuccDiscout, couponFailDiscout *float64) *proto.NewFormFeeDesc {
				//根据拼成价格优惠信息决定 icon是惠-还是券-
				if nil == couponSuccDiscout || 0 != util.CompareFloat64ByPrec(*allSuccDiscount, *couponSuccDiscout, 2) {
					communicateDiscount = true
				}
				cfg := map[string]gjson.Result{}

				sucPrice, failPrice, price1, price2 := renderData(communicateDiscount, allSuccDiscount, allFailDiscount, couponSuccDiscout, couponFailDiscout)

				if communicateDiscount {
					cfg = DiscountRenderCfg(allFailDiscount, allSuccDiscount)
				}

				if !communicateDiscount {
					cfg = couponRenderCfg(couponSuc.Tag, couponFailDiscout, couponSuccDiscout)
				}

				if len(cfg) <= 0 {
					return nil
				}

				return &proto.NewFormFeeDesc{
					BorderColor: cfg["border_color"].String(),
					Content: util.ReplaceTag(ctx, cfg["content"].String(), map[string]string{
						"suc_num":  sucPrice,
						"fail_num": failPrice,
						"num_1":    price1,
						"num_2":    price2,
					}),
					Icon:      cfg["icon"].String(),
					Amount:    0.0,
					Type:      3,
					TextColor: util2.StrPtr(cfg["text_color"].String()),
					CustomTag: util2.StrPtr(strings.Trim(strings.Join([]string{couponSuccCustomTag, couponFailCustomTag}, ","), ",")),
				}
			}
		}

		//2.2未拼成有追加券。沟通追加券
		if nil != couponFail && "add_select_bind_coupon" == couponFail.Tag {
			return func(couponSuc, couponFail *biz_runtime.Coupon, allSuccDiscount, allFailDiscount, couponSuccDiscout, couponFailDiscout *float64) *proto.NewFormFeeDesc {
				cfg := couponRenderCfg(couponFail.Tag, couponFailDiscout, couponSuccDiscout)
				if len(cfg) <= 0 {
					return nil
				}

				sucPrice, failPrice, price1, price2 := renderData(false, allSuccDiscount, allFailDiscount, couponSuccDiscout, couponFailDiscout)

				return &proto.NewFormFeeDesc{
					BorderColor: cfg["border_color"].String(),
					Content: util.ReplaceTag(ctx, cfg["content"].String(), map[string]string{
						"suc_num":  sucPrice,
						"fail_num": failPrice,
						"num_1":    price1,
						"num_2":    price2,
					}),
					Icon:      cfg["icon"].String(),
					Amount:    0.0,
					Type:      3,
					TextColor: util2.StrPtr(cfg["text_color"].String()),
					CustomTag: util2.StrPtr(strings.Trim(strings.Join([]string{couponSuccCustomTag, couponFailCustomTag}, ","), ",")),
				}
			}
		}

		//2.3不渲染
		return func(couponSuc, couponFail *biz_runtime.Coupon, allSuccDiscount, allFailDiscount, couponSuccDiscout, couponFailDiscout *float64) *proto.NewFormFeeDesc {
			return nil
		}
	}

	return renderDispatch(allSuccDiscount, couponFail)(couponSuc, couponFail, allSuccDiscount, allFailDiscount, couponSuccDiscout, couponFailDiscout)
}
