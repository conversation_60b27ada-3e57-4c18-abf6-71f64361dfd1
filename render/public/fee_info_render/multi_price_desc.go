package fee_info_render

import (
	"context"
	"encoding/json"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

type MultiPriceDescProvider interface {
	render.ProductProvider
	render.BaseProvider
	GetEstimateFee() float64
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
	GetBillInfoCurrency() string
	GetBillFeeDetailInfo() map[string]float64
	IsCarpoolV3Merge(context.Context) bool
	GetCarpoolFailEstimateFee() (float64, bool)
	GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo
	GetMultiPriceCouponInfoByScene(index map[string]string) *PriceApi.EstimateNewFormCouponInfo
	GetVCard() *PriceApi.EstimateNewFormVCardInfo
	IsBusinessPay() bool
	GetMixedDeductPrice() float64
	GetCarpoolScenePrice() []*PriceApi.CarpoolScenePrice
	GetSceneDataBySceneMark(reqSceneMark map[string]string) *biz_runtime.SceneEstimateFee
	GetFontScaleType() int32
}

type MultiPriceProvider interface {
	render.BaseProvider
	GetProductCategory() int64
	GetSceneDataBySceneMark(reqSceneMark map[string]string) *biz_runtime.SceneEstimateFee
}

type MultiDcmpConf struct {
	SuccCarpoolDay string `json:"succ_carpool_day"`
	Succ           string `json:"succ"`
	SuccPoll1      string `json:"succ_pool_1"`
	SuccPoll2      string `json:"succ_pool_2"`
	CapPriceMerge  string `json:"cap_price_merge"`

	FailV2 string `json:"fail_v_2"`
	FailV3 string `json:"fail_v_3"`
	FailV4 string `json:"fail_v_4"`

	FeeDesc map[string]*FeeDescStruct `json:"fee_desc"`
}
type FeeDescStruct struct {
	Content     string `json:"content"`
	Icon        string `json:"icon"`
	BorderColor string `json:"border_color"`
}

type DcmpVcard struct {
	VcardText map[string]DcmpItem
}

type DcmpItem struct {
	LeftIcon    string `json:"left_icon"`
	Text        string `json:"text"`
	TextColor   string `json:"text_color"`
	BorderColor string `json:"border_color"`
	BgColor     string `json:"bg_color"`
}

func GetMultiPriceDesc(ctx context.Context, prov MultiPriceDescProvider, seatNum int32) (descList []*proto.NewFormMultiPrice) {
	if prov.GetProductCategory() == ProductCategory.ProductCategoryLowPriceCarpool {
		return GetLowPriceSucPriceDescNew(ctx, prov, seatNum)
	} else if prov.GetProductCategory() == ProductCategory.ProductCategoryCarpoolStation {
		return GetMultiPriceDescSation(ctx, prov)
	}
	return
}

// func FixMultiPriceDescSation(ctx context.Context, prov MultiPriceDescProvider, dList []*proto.NewFormMultiPrice) []*proto.NewFormMultiPrice {
// 	// 大字版
// 	if len(dList) == 2 && prov.GetFontScaleType() != cast.ToInt32(consts.NormalFontSize) &&
// 		util.CompareAppVersion(prov.GetAppVersion(), "7.0.10") >= 0 {
// 		dList[0], dList[1] = dList[1], dList[0]
// 	}
// 	return dList
// }

// GetMultiPriceDesc 多口价价格描述渲染
func GetMultiPriceDescSation(ctx context.Context, prov MultiPriceDescProvider) (descList []*proto.NewFormMultiPrice) {
	if prov.GetProductCategory() != ProductCategory.ProductCategoryCarpoolStation {
		return
	}

	conf := getCarpool3DcmpConfig(ctx)
	if conf == nil {
		return
	}

	successFeeContent := conf.Succ

	dcmpVcard := &DcmpVcard{}
	vcardText := &dcmpVcard.VcardText
	// 读取极速拼车-新增赠卡相关文案
	config := dcmp.GetDcmpContent(ctx, "anycar_v3-new_free_card", nil)
	if err := json.Unmarshal([]byte(config), vcardText); err != nil {
		return
	}

	vcardResult := prov.GetVCard()
	if prov.IsBusinessPay() {
		// 未拼成价格
		failDesc := &proto.NewFormMultiPrice{
			FeeMsg: util.ReplaceTag(ctx, conf.FailV3, map[string]string{
				"carpool_fail_price": "0",
			}),
			FeeAmount:   0,
			IsLargeFont: consts2.CarpoolSmallFont,
		}
		descList = append(descList, failDesc)

		// 拼成价格
		successDesc := &proto.NewFormMultiPrice{
			FeeMsg: util.ReplaceTag(ctx, successFeeContent, map[string]string{
				"carpool_success_price": "0",
			}),
			FeeAmount:   0,
			IsLargeFont: consts2.CarpoolLargeFont,
		}
		descList = append(descList, successDesc)
		return
	}

	if vcardResult != nil {
		// 当存在省钱卡-赠卡/买卡时，展示“稳享拼成价xx元”
		feeAmount, err := util.FormatPriceCeilFloat64(prov.GetEstimateFee(), 1)
		if err != nil {
			return
		}
		feeAmount = carpool3.FormatPrice(prov.GetEstimateFee(), carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), feeAmount).FloatVal
		successDesc := &proto.NewFormMultiPrice{
			FeeMsg: dcmp.GetDcmpContent(ctx, "anycar_v3-new_free_card_top", map[string]string{
				"carpool_success_price": cast.ToString(feeAmount)}),
			FeeAmount:   feeAmount,
			IsLargeFont: consts2.CarpoolLargeFont,
		}

		descList = append(descList, successDesc)
		return
	} else if prov.IsCarpoolV3Merge(ctx) {
		// 替换为稳享拼成价
		successFeeContent = conf.CapPriceMerge
	} else {
		// 未拼成价格
		var carpoolFailMsg string
		var carpoolFailFee float64
		if carpool.IsCarpoolDualPriceV2(prov.GetCarpoolType(), prov.IsDualCarpoolPrice(), prov.GetCarpoolPriceType()) {
			carpoolFailMsg = conf.FailV2
		} else if fee, ok := prov.GetCarpoolFailEstimateFee(); ok {
			carpoolFailFee, _ = util.FormatPriceCeilFloat64(fee, 1)
			carpoolFailFee = carpool3.FormatPrice(fee, carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), carpoolFailFee).FloatVal
			carpoolFailMsg = util.ReplaceTag(ctx, conf.FailV3, map[string]string{
				"carpool_fail_price": cast.ToString(carpoolFailFee),
			})
		}

		if len(carpoolFailMsg) > 0 {
			failDesc := &proto.NewFormMultiPrice{
				FeeMsg:      carpoolFailMsg,
				FeeAmount:   carpoolFailFee,
				IsLargeFont: consts2.CarpoolSmallFont,
			}

			descList = append(descList, failDesc)
		}
	}

	// 拼成价格
	successFee, _ := util.FormatPriceCeilFloat64(prov.GetEstimateFee(), 1)
	successFee = carpool3.FormatPrice(prov.GetEstimateFee(), carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), successFee).FloatVal
	successDesc := &proto.NewFormMultiPrice{
		FeeMsg: util.ReplaceTag(ctx, successFeeContent, map[string]string{
			"carpool_success_price": cast.ToString(successFee),
		}),
		FeeAmount:   successFee,
		IsLargeFont: consts2.CarpoolLargeFont,
	}

	descList = append(descList, successDesc)

	return
}

// GetLowPriceMultiPriceDesc 特价拼车多口价价格描述渲染
func GetLowPriceMultiPriceDesc(ctx context.Context, prov MultiPriceDescProvider) (descList []*proto.NewFormMultiPrice) {
	conf := getCarpool3DcmpConfig(ctx)
	if conf == nil {
		return
	}

	scenePrice := prov.GetCarpoolScenePrice()
	for _, price := range scenePrice {
		feeConf := ""
		feeKey := ""
		// 拼1人价格
		if price.Option.PoolNum == 1 && price.Option.IsCarpoolSuccess {
			feeConf = conf.SuccPoll1
			feeKey = "carpool_success_pool_1_price"
		}
		// 拼2人价格
		if price.Option.PoolNum == 2 && price.Option.IsCarpoolSuccess {
			feeConf = conf.SuccPoll2
			feeKey = "carpool_success_pool_2_price"
		}
		// 未拼成
		if price.Option.PoolNum == 0 && !price.Option.IsCarpoolSuccess {
			feeConf = conf.FailV3
			feeKey = "carpool_fail_price"
		}

		fee, err := util.FormatPriceCeilFloat64(price.EstimateFee, 1)
		if err != nil {
			return
		}
		feeStr := cast.ToString(fee)
		if prov.IsBusinessPay() {
			feeStr = "0"
			fee = 0
		}
		successDesc := &proto.NewFormMultiPrice{
			FeeMsg:    util.ReplaceTag(ctx, feeConf, map[string]string{feeKey: feeStr}),
			FeeAmount: fee,
		}
		descList = append(descList, successDesc)
	}
	return
}

func getCarpool3DcmpConfig(ctx context.Context) *MultiDcmpConf {
	conf := new(MultiDcmpConf)
	dcmpStr := dcmp.GetDcmpContent(ctx, "carpool_config-dual_price", nil)
	err := json.Unmarshal([]byte(dcmpStr), conf)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshal dcmp string fail with %v", err)
		return nil
	}
	return conf
}

func getLowCarpoolDcmpConfig(ctx context.Context) map[string]interface{} {
	conf := make(map[string]interface{})
	dcmpStr := dcmp.GetDcmpContent(ctx, "carpool_config-low_price", nil)
	err := json.Unmarshal([]byte(dcmpStr), &conf)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshal dcmp string fail with %v", err)
		return nil
	}

	return conf
}

func GetLowCarpoolSortDcmpConfig(ctx context.Context) map[string]interface{} {
	conf := make(map[string]interface{})
	dcmpStr := dcmp.GetDcmpContent(ctx, "carpool_config-sort_46", nil)
	err := json.Unmarshal([]byte(dcmpStr), &conf)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshal dcmp string fail with %v", err)
		return nil
	}

	return conf
}

type PriceSelect struct {
	PoolNum          string `json:"pool_num"`
	SeatNum          string `json:"seat_num"`
	IsCarpoolSuccess string `json:"is_carpool_success"`
}

// GetLowPriceSucPriceDesc 特价拼车拼成多口价价格描述渲染
func GetLowPriceSucPriceDescNew(ctx context.Context, prov MultiPriceDescProvider, seatNum int32) (descList []*proto.NewFormMultiPrice) {
	feeMap, equalStatus := BuildMultiPrice(ctx, prov, seatNum, "carpool_config-price_select")
	conf := getLowCarpoolDcmpConfig(ctx)
	precision, _ := conf["precision"].(float64) // nolint
	if precision == 0 {
		precision = 1
	}
	fee := float64(0)
	if equalStatus == true {
		if prov.IsBusinessPay() {
			fee = 0
		} else {
			for _, v := range feeMap {
				fee = carpool3.FormatPrice(v.GetFee(), carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), v.GetFee()).FloatVal
				break
			}
		}
		successDesc := &proto.NewFormMultiPrice{
			FeeMsg:      util.ReplaceTag(ctx, conf["suc_same_price"].(string), map[string]string{"price": util.FormatPriceWithoutZero(fee, int(precision))}),
			FeeAmount:   cast.ToFloat64(util.FormatPriceWithoutZero(fee, int(precision))),
			IsLargeFont: consts2.CarpoolLargeFont,
		}

		descList = append(descList, successDesc)
		return
	} else {
		// 多价格场景
		sort := []string{}
		largeFont := consts2.CarpoolSmallFont
		sortRes, _ := json.Marshal(GetLowCarpoolSortDcmpConfig(ctx)["sort"]) // nolint
		_ = json.Unmarshal(sortRes, &sort)                                   // nolint
		if len(sort) == 0 {
			return
		}
		for idx, v := range sort {
			feeAll, b := feeMap[v]
			fee = carpool3.FormatPrice(feeAll.GetFee(), carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), feeAll.GetFee()).FloatVal
			if !b {
				return
			}
			if prov.IsBusinessPay() {
				fee = 0
			}
			if idx == len(sort)-1 {
				largeFont = consts2.CarpoolLargeFont
			}
			descList = append(descList, &proto.NewFormMultiPrice{
				FeeMsg:      util.ReplaceTag(ctx, conf[v].(string), map[string]string{"price": util.FormatPriceWithoutZero(fee, int(precision))}),
				FeeAmount:   cast.ToFloat64(util.FormatPriceWithoutZero(fee, int(precision))),
				IsLargeFont: int32(largeFont),
			})
		}
	}
	return
}

func BuildMultiPrice(ctx context.Context, prov MultiPriceProvider, seatNum int32, dcmpKey string) (feeMap map[string]*biz_runtime.SceneEstimateFee, equalStatus bool) {
	if seatNum == 0 {
		seatNum = 1
	}
	// 获取想要进行展示的价格多口价结构
	selectPriceListStr := gjson.Get(dcmp.GetDcmpPlainContent(ctx, dcmpKey), strconv.Itoa(int(prov.GetProductCategory()))+"."+strconv.Itoa(int(seatNum))).String()
	if selectPriceListStr == "" {
		return
	}
	selectPriceList := make([]PriceSelect, 0)
	err := json.Unmarshal([]byte(selectPriceListStr), &selectPriceList)
	if err != nil || selectPriceList == nil || len(selectPriceList) == 0 {
		return
	}

	fee := float64(0)
	feeMap = make(map[string]*biz_runtime.SceneEstimateFee)
	for _, v := range selectPriceList {
		res := prov.GetSceneDataBySceneMark(map[string]string{
			"pool_num":           v.PoolNum,
			"seat_num":           v.SeatNum,
			"is_carpool_success": v.IsCarpoolSuccess,
		})
		if res == nil {
			return
		}
		// 1-2-1 35.89
		feeMap[v.SeatNum+"-"+v.PoolNum+"-"+v.IsCarpoolSuccess] = res
		fee = carpool3.FormatPrice(res.GetFee(), carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), res.GetFee()).FloatVal
	}
	equalStatus = true
	for _, v := range feeMap {
		if fee != carpool3.FormatPrice(v.GetFee(), carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), v.GetFee()).FloatVal {
			equalStatus = false
		}
	}
	return feeMap, equalStatus
}
