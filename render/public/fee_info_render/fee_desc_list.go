package fee_info_render

import (
	"context"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	model2 "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
)

type PriceInfoDescListProvider interface {
	render.ProductProvider
	render.BaseProvider
	render.BillInfoProvider
	render.ActivityInfoProvider
	render.PaymentInfoProvider
	render.MemberInfoProvider
	render.TaxiInfoProvider
	render.ApolloProvider
	render.CarpoolInfoProvider
	render.DynamicIconABProvider

	// render.ApolloProvider
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)

	IsCarpoolV3Merge(context.Context) bool
	GetBonus() float64
	GetFastEstimatePrice() float64
	GetDiscountCard() *PriceApi.DiscountCard
	GetExactEstimateFee() float64
	GetCarpoolFailExactEstimateFee() float64

	GetMixedDeductPrice() float64
	GetSceneEstimatePrice() []biz_runtime.SceneEstimateFeeViewer
	GetFastCarPrice() float64
	GetOpenCitySourceId() int32
	GetCurrentSeatNum() int32
	GetDirectEstimatePrice() biz_runtime.SceneEstimateFeeViewer
	GetCarpoolFailRawBill() *price_api.BillInfoV3
	GetCarpoolFailCouponInfo() *PriceApi.EstimateNewFormCouponInfo
	GetBaseReqData() *models.BaseReqData
}

func GetPriceInfoDescList(ctx context.Context, prov PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	ret := make([]*proto.NewFormFeeDesc, 0)

	var (
		env      *model2.Env
		feeInput *model2.FeeInput
	)
	if prov.GetBaseReqData() != nil && util.IsFromXinZhuWithBusinessTravelOrderType(prov.GetBaseReqData().CommonBizInfo.BusinessTravelOrderType) {
		env = fee_desc_engine.NewEnv(consts.XinZhuAppendCarForm).SetApolloParams(prov).SetDcmpKey(consts.XinZhuAppendCarFormFeeDesc)
		feeInput = input.BuildNormalFeeInput(ctx, prov, consts.XinZhuAppendCarForm)
	} else {
		env = fee_desc_engine.NewEnv(consts.AnyCarV3Form).SetApolloParams(prov).SetDcmpKey(consts.AnyCarV3FeeDesc)
		feeInput = input.BuildNormalFeeInput(ctx, prov, consts.AnyCarV3Form)
	}

	resp := fee_desc_engine.NewFeeEngine(feeInput, env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			BorderColor: output.BorderColor,
			Icon:        output.Icon,
			Content:     output.Content,
			Type:        output.Type,
			TextColor:   util.StringPtr(output.TextColor),
			CustomTag:   util.StringPtr(output.CustomTag),
		}

		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}

		ret = append(ret, desc)
	}

	return ret
}

func GetPriceInfoDescListV4(ctx context.Context, prov PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	ret := make([]*proto.NewFormFeeDesc, 0)
	env := fee_desc_engine.NewEnv(consts.DefaultForm).SetApolloParams(prov).SetDcmpKey(consts.EstimateV4FeeDesc)
	resp := fee_desc_engine.NewFeeEngine(input.BuildNormalFeeInput(ctx, prov, consts.DefaultForm), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			BorderColor: output.BorderColor,
			Icon:        output.Icon,
			Content:     output.Content,
			Type:        output.Type,
			TextColor:   util.StringPtr(output.TextColor),
		}

		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}

		ret = append(ret, desc)
	}

	return ret
}

func GetPriceInfoDescListV3(ctx context.Context, prov PriceInfoDescListProvider, input *model2.FeeInput) []*proto.NewFormFeeDesc {
	ret := make([]*proto.NewFormFeeDesc, 0)
	env := fee_desc_engine.NewEnv(consts.DefaultForm).SetApolloParams(prov).SetDcmpKey(consts.EstimateV3FeeDesc)
	resp := fee_desc_engine.NewFeeEngine(input, env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			BorderColor:    "",
			Icon:           output.Icon,
			Content:        output.Content,
			Type:           output.Type,
			TextColor:      util.StringPtr(output.TextColor),
			HighlightColor: util.StringPtr(output.HighLightColor),
		}

		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}
		// hack 折上折+支付宝券的互斥
		if output.Type == consts.TypeDecrement && input.DiscountInfo[consts.FeeAlipayCoupon] > 0 && input.DiscountInfo[consts.FeeMemberDiscountCard] > 0 {
			conf := dcmp.GetJSONMap(ctx, consts.EstimateV3FeeDesc, "normal_discount")
			desc.Icon = ""
			desc.Content = util.ReplaceTag(ctx, conf["content"].String(), map[string]string{"num": cast.ToString(desc.Amount)})
		}

		ret = append(ret, desc)
	}

	return ret
}
