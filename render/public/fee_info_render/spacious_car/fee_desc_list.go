package spacious_car

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/spacious_car/spacious_common"
)

func GetSpaciousCarFeeDescList(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	apolloKey, apolloParam := prov.ApolloParamsGen(apollo_model.WithPIDKey)

	if !spacious_common.HitSingle(ctx, apolloKey, apolloParam) {
		return nil
	}

	ret := make([]*proto.NewFormFeeDesc, 0)

	env := fee_desc_engine.NewEnv(consts.DefaultForm).SetApolloParams(prov).SetDcmpKey(consts.EstimateV4FeeDesc)
	resp := fee_desc_engine.NewFeeEngine(input.BuildSpaciousFeeInput(ctx, prov, consts.DefaultForm), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			BorderColor: output.BorderColor,
			Icon:        output.Icon,
			Content:     output.Content,
			Type:        output.Type,
			TextColor:   util.StringPtr(output.TextColor),
		}

		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}

		ret = append(ret, desc)
	}

	return ret

}
