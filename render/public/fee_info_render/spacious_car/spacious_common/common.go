package spacious_common

import (
	"context"
	"math"
)

func HitSingle(ctx context.Context, key string, params map[string]string) bool {
	//isAllow, assignment := apollo.FeatureExp(ctx, "ab_spacious_car_check_separately", key, params)
	//if isAllow {
	//	hitSingle, ok := assignment.GetParameters()["hit_single"]
	//	if ok && util.ToInt(hitSingle) == 1 {
	//		return true
	//	}
	//}
	//
	//return false

	// 高版本的车大单勾全量，按全量单勾处理
	return true
}

// GetSpaciousCarInfo 获取车大费用信息  选车费 = 选车费+(-账单折扣)+(-sps折扣)
func GetSpaciousCarInfo(feeDetailInfo map[string]float64) (bool, bool, float64) {
	var (
		isHasDiscount  bool
		isHasSelectFee bool
		selectionFee   float64
	)

	if feeDetailInfo == nil || len(feeDetailInfo) == 0 {
		return isHasSelectFee, isHasDiscount, selectionFee
	}

	talosSelectionFee, ok1 := feeDetailInfo["talos_spacious_car_selection_fee"]
	if ok1 {
		isHasSelectFee = true
	}

	spsFeeDiscount, ok2 := feeDetailInfo["sps_choose_car_fee_discount"]
	spsMemberDiscount, ok3 := feeDetailInfo["sps_spacious_car_member_discount"]
	if ok1 && (ok2 || ok3) {
		if talosSelectionFee != 0 && (spsFeeDiscount != 0 || spsMemberDiscount != 0) {
			isHasDiscount = true
			selectionFee = talosSelectionFee + spsFeeDiscount + spsMemberDiscount
		}
	} else {
		selectionFee = talosSelectionFee
	}

	return isHasSelectFee, isHasDiscount, math.Max(selectionFee, 0.0)
}
