package util

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	trace "git.xiaojukeji.com/lego/context-go"
)

const (
	PriceFormatABExp = "cap_price_point_1" //小数点保留位数实验v3, 已替换为灰度

	//小数点保留位数
	FloatRoundNull = -1 //不对位数进行修改
	FloatRound0    = 0  //取整
	FloatRound1    = 1  //保留1位小数
	FloatRound2    = 2  //保留2位小数

	//价格format函数
	Round = "0" //四舍五入
	Ceil  = "1" //向上取整
	Floor = "2" //向下取整
)

type PriceUnitABTest struct {
	params *PriceUnitABTestParams
}

type PriceUnitABTestParams struct {
	hitPriceUnitABTest    bool
	capPriceDigit         int
	estimatePriceDigit    int
	capPriceFunction      string
	estimatePriceFunction string
}

func NewPriceUnitABTest(ctx context.Context) (p *PriceUnitABTest) {
	// 初始化配置
	p = &PriceUnitABTest{}
	return
}

func (p *PriceUnitABTest) getTestParams(ctx context.Context, params map[string]string, key string) *PriceUnitABTestParams {
	if p.params == nil {
		p.getPriceUnitABTestOnce(ctx, params, key)
	}
	return p.params
}

func (p *PriceUnitABTest) getPriceUnitABTestOnce(ctx context.Context, params map[string]string, key string) {
	allow, toggle := apollo.FeatureExp(ctx, PriceFormatABExp, key, params)
	if allow {
		estimatePriceDigit, err := strconv.Atoi(toggle.GetParameter("estimate_price_digit", "-1"))
		capPriceFunction := toggle.GetParameter("cap_price_function", Ceil)
		estimatePriceFunction := toggle.GetParameter("estimate_price_function", Round)
		if err == nil {
			p.params = &PriceUnitABTestParams{
				hitPriceUnitABTest:    true,
				capPriceDigit:         FloatRound1,
				estimatePriceDigit:    estimatePriceDigit,
				capPriceFunction:      capPriceFunction,
				estimatePriceFunction: estimatePriceFunction,
			}
			return
		} else {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "GetPriceUnitABTest Atoi ERROR, err= %v", err)
		}
	}

	p.params = &PriceUnitABTestParams{
		hitPriceUnitABTest:    false,
		capPriceDigit:         FloatRoundNull,
		estimatePriceDigit:    FloatRoundNull,
		capPriceFunction:      Round,
		estimatePriceFunction: Round,
	}
}
