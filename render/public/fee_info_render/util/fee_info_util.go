package util

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	mambaApollo "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

func PriceFormat(ctx context.Context, params map[string]string, key string, estimateFee float64, feeType int) string {
	var priceUnitABTest = NewPriceUnitABTest(ctx)
	testParams := priceUnitABTest.getTestParams(ctx, params, key)
	if testParams.hitPriceUnitABTest {
		return GetABTestPriceFormat(ctx, estimateFee, feeType, testParams)
	}

	return util.Float64ToString(estimateFee)
}

func GetABTestPriceFormat(ctx context.Context, estimateFee float64, feeType int, testParams *PriceUnitABTestParams) string {
	//一口价
	if feeType == consts.FeeTypeCapPrice {
		if testParams.capPriceFunction == Round {
			return SetFloatFormat(estimateFee, testParams.capPriceDigit)
		}
		if testParams.capPriceFunction == Ceil {
			return SetFloatFormatCeil(estimateFee, testParams.capPriceDigit)
		}
	}
	//预估价（包含两口价）
	if testParams.estimatePriceFunction == Round {
		return SetFloatFormat(estimateFee, testParams.estimatePriceDigit)
	}
	if testParams.estimatePriceFunction == Ceil {
		return SetFloatFormatCeil(estimateFee, testParams.estimatePriceDigit)
	}

	return util.Float64ToString(estimateFee)
}

func SetFloatFormat(estimateFee float64, digit int) string {
	if FloatRoundNull == digit {
		return util.Float64ToString(estimateFee)
	}

	numstr := fmt.Sprint(estimateFee)
	tmp := strings.Split(numstr, ".")
	floatLength := 0 //浮点数有几位小数
	if len(tmp) <= 1 {
		floatLength = 0
	} else {
		floatLength = len(tmp[1])
	}

	if floatLength <= digit {
		return util.Float64ToString(estimateFee)
	}
	return util.FormatPriceForRound(estimateFee, digit)
}

func SetFloatFormatCeil(estimateFee float64, digit int) string {
	if FloatRoundNull == digit {
		return util.Float64ToString(estimateFee)
	}

	numstr := fmt.Sprint(estimateFee)
	tmp := strings.Split(numstr, ".")
	floatLength := 0 //浮点数有几位小数
	if len(tmp) <= 1 {
		floatLength = 0
	} else {
		floatLength = len(tmp[1])
	}

	if floatLength <= digit {
		return util.Float64ToString(estimateFee)
	}
	return util.FormatPriceCeil(estimateFee, digit)
}

func GetGeneralFeeMsgTemplate(ctx context.Context, city int, pcID int, lang string) string {
	m := map[string]string{"product_category": strconv.Itoa(pcID), "city": strconv.Itoa(city)}
	configs, err := mambaApollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, "fee_msg_conf", m, lang)
	if configs == nil || err != nil {
		m := map[string]string{"product_category": strconv.Itoa(pcID), "city": "0"}
		configs, err = mambaApollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, "fee_msg_conf", m, lang)
		if configs == nil || err != nil {
			return ""
		}
	}

	if len(configs) == 0 {
		return ""
	}

	if value, err := configs[0].GetStringValue("fee_msg"); err != nil {
		return ""
	} else {
		return value
	}
}

func GetGeneralFeeMsgTemplateNew(ctx context.Context, city int, pcID int, lang string) string {
	m := map[string]string{"product_category": strconv.Itoa(pcID), "city": strconv.Itoa(city)}
	configs, err := mambaApollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, "fee_msg_conf", m, lang)
	if configs == nil || err != nil {
		m := map[string]string{"product_category": strconv.Itoa(pcID), "city": "0"}
		configs, err = mambaApollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, "fee_msg_conf", m, lang)
		if configs == nil || err != nil {
			return ""
		}
	}

	if len(configs) == 0 {
		return ""
	}

	if value, err := configs[0].GetStringValue("fee_msg_template"); err != nil {
		return ""
	} else {
		return value
	}
}
