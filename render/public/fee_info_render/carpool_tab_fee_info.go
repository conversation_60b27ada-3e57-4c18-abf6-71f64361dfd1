package fee_info_render

import (
	"context"

	"strconv"

	constsCommon "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/ticket_detail_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"github.com/shopspring/decimal"
	"github.com/tidwall/gjson"
)

type ProductInfo interface {
	GetProductCategory() int64
	GetScene() string
	GetPageType() int32
}

type FeeInfo interface {
	ProductInfo
	GetEstimateFeeAmount() float64
	GetCurrentPaymentType() int
	GetRebookServiceCharge() float64
	GetSelectedBusServiceShiftId() string
	GetVersion() string
	GetAccessKeyID() int
}

type FeeItem struct {
	Tag    string
	Amount float64
}

type FeeItemList interface {
	ProductInfo
	GetCoupon() *FeeItem
	GetBusinessPayAmount() float64
	GetBonus() float64
	GetSelectedBusServiceShiftId() string
}

type SpecialFeeProvider interface {
	ProductInfo
	GetHolidayFee() (amount float64)
	render.ApolloProvider
}

func CheckVersionNew(accessKeyId int, version string) bool {
	if accessKeyId == constsCommon.AccessKeyIDDiDiAndroid || accessKeyId == constsCommon.AccessKeyIDDiDiIos {
		if util.VersionCompare(version, "6.6.18") >= 0 {
			return true
		}
	}

	if accessKeyId == constsCommon.AccessKeyIDDiDiWechatMini || accessKeyId == constsCommon.AccessKeyIDDiDiAlipayMini {
		if util.CompareAppVersion(version, "6.6.100") >= 0 {
			return true
		}
	}

	return false
}

func FeeMsg(ctx context.Context, info FeeInfo) string {
	paymentType := info.GetCurrentPaymentType()
	fee := info.GetEstimateFeeAmount()
	if paymentType == consts.BusinessPaymentType {
		fee = 0.0
	}
	var amount string
	pageType := info.GetPageType()
	if pageType == page_type.PageTypeIntercityStationEstimate {
		amount = util.Float64ToString(fee)
	} else {
		amount = util.FormatPrice(fee, 1)
	}
	tag := map[string]string{
		"amount": amount,
	}
	ret := dcmp.GetDcmpContent(ctx, "inter_city_carpool-fee_msg_template", tag)

	if info.GetScene() == ticket_detail_consts.SceneRebook {
		if info.GetSelectedBusServiceShiftId() == "" {
			ret = dcmp.GetDcmpPlainContent(ctx, "inter_city_carpool-fee_msg_template_rebook_charge_first_bubble")
		} else {
			if !CheckVersionNew(info.GetAccessKeyID(), info.GetVersion()) { // old version
				rebookCharge := info.GetRebookServiceCharge()
				if rebookCharge > 0 {
					tag = map[string]string{
						"charge": util.FormatPrice(rebookCharge, 2),
					}

					ret += dcmp.GetDcmpContent(ctx, "inter_city_carpool-fee_msg_template_rebook_charge", tag)
				}
			}
		}
	}

	return ret
}

func BusDetailFeeMsg(ctx context.Context, info *biz_runtime.ProductInfoFull) string {
	tag := map[string]string{
		"amount": decimal.NewFromFloat(info.GetCombineEstimateFee()).Round(2).String(),
	}
	return dcmp.GetDcmpContent(ctx, "inter_city_carpool-fee_msg_template", tag)
}

// FeeMsgV2 这个去0了
func FeeMsgV2(ctx context.Context, info *biz_runtime.ProductInfoFull) string {
	paymentType := info.GetCurrentPaymentType()

	fee := info.GetEstimateFeeAmount()
	if paymentType == consts.BusinessPaymentType {
		fee = 0.0
	}
	tag := map[string]string{
		"amount": decimal.NewFromFloat(fee).Round(2).String(),
	}

	return dcmp.GetDcmpContent(ctx, "inter_city_carpool-fee_msg_template", tag)
}

func RebookFee(ctx context.Context, info FeeInfo) string {
	if !CheckVersionNew(info.GetAccessKeyID(), info.GetVersion()) { // old version
		return ""
	}

	if info.GetScene() != ticket_detail_consts.SceneRebook || info.GetRebookServiceCharge() == 0 {
		return ""
	}

	tag := map[string]string{
		"rebook_fee": util.FormatPrice(info.GetRebookServiceCharge(), 2),
	}
	return dcmp.GetDcmpContent(ctx, "inter_city_carpool-rebook_fee", tag)
}

func InterCityFeeDesc(ctx context.Context, product *biz_runtime.ProductInfoFull) []*proto.IntercityFeeInfoItem {
	env := fee_desc_engine.NewEnv(consts2.InterCityForm).SetCap(2).SetApolloParams(product).SetDcmpKey("inter_city_carpool-price_desc_list")
	engin := fee_desc_engine.NewFeeEngine(input.BuildNormalFeeInput(ctx, product, consts2.InterCityForm), env)
	feeOutput := engin.SetProductCategory(product.GetProductCategory()).Do(ctx)

	var result = make([]*proto.IntercityFeeInfoItem, 0)
	for _, fee := range feeOutput {
		if fee == nil {
			continue
		}

		result = append(result, &proto.IntercityFeeInfoItem{
			Content:       fee.Content,
			LeftIcon:      fee.Icon,
			FontColor:     fee.TextColor,
			BorderColor:   fee.BorderColor,
			FontTextColor: fee.TextExtraColor,
			BgFillColor:   fee.BgFillColor,
		})
	}

	return result
}

func ExtraPriceDesc(ctx context.Context, provider SpecialFeeProvider) string {

	var amount float64
	//apolloParams := provider.GetApolloParam()
	uidKey, apolloParams := provider.GetApolloParams(biz_runtime.WithUIDKey)
	if apollo.FeatureToggle(ctx, "gs_holiday_fee_fee_desc", uidKey, apolloParams) {
		amount = provider.GetHolidayFee()
	}

	if amount > 0 {
		template := dcmp.GetDcmpContent(ctx,
			"carpool_tab_pincheche-special_price_desc",
			map[string]string{
				"fee": strconv.Itoa(int(amount)),
			},
		)
		return gjson.Get(template, "spring_festival").String()
	}
	return ""
}
