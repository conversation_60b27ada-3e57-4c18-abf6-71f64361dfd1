package category_carpool

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
	"github.com/agiledragon/gomonkey/v2"
)

type AnyCarV4AdapterTest struct {
	*biz_runtime.ProductInfoFull
	// apolloParams *plain_text_render.ApolloParams
}

func (a *AnyCarV4AdapterTest) GetBaseReqData() *models.BaseReqData {
	return a.BaseReqData
}

func (a *AnyCarV4AdapterTest) IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool {
	return false
}

func (a *AnyCarV4AdapterTest) GetLevelID() int32 {
	return 3
}
func (a *AnyCarV4AdapterTest) GetCarpoolFailCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	return &PriceApi.EstimateNewFormCouponInfo{
		CustomTag: "member_v3",
		Amount:    "100000",
	}
}
func (a *AnyCarV4AdapterTest) GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	return &PriceApi.EstimateNewFormCouponInfo{
		CustomTag: "member_v3",
		Amount:    "100000",
	}
}

func (a *AnyCarV4AdapterTest) GetTabId() string {
	return a.BaseReqData.CommonInfo.TabId
}

func (a *AnyCarV4AdapterTest) GetEstimateStyleType() int32 {
	return a.BaseReqData.CommonInfo.EstimateStyleType
}

func (a *AnyCarV4AdapterTest) GetTimeSpan() []*EstimateDecision.TimeSpanV2 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil || len(bizInfo.TimeSpan) == 0 {
		return nil
	}
	return bizInfo.TimeSpan
}

func (a *AnyCarV4AdapterTest) GetEstimateFeeAmount() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	return fee.GetFee()
}

func (a *AnyCarV4AdapterTest) GetBonus() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (a *AnyCarV4AdapterTest) GetCoupon() *fee_info_render.FeeItem {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return nil
	}
	coupon := fee.GetFeeDetail().GetCoupon()
	if coupon == nil {
		return nil
	}
	return &fee_info_render.FeeItem{
		Tag:    coupon.Tag,
		Amount: coupon.Amount,
	}
}

func (a *AnyCarV4AdapterTest) GetHolidayFee() (amount float64) {

	if a.GetBillDisplayLines() == nil {
		return
	}

	for _, item := range a.GetBillDisplayLines() {
		if item.Name == "red_packet" && item.Value > 0.0 {
			amount = item.Value
		}
	}

	return
}

func (a *AnyCarV4AdapterTest) GetMaxSeatNum() int32 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.MaxCarpoolSeatNum
}
func (a *AnyCarV4AdapterTest) GetUserSelectSeatNum() int32 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.CarpoolSeatNum
}

func (a *AnyCarV4AdapterTest) GetAllPaymentOptions() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := a.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (a *AnyCarV4AdapterTest) GetCurrentPaymentType() int {
	opts := a.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (a *AnyCarV4AdapterTest) GetMultiRequireProduct() []models.RequireProduct {
	if a.BaseReqData == nil {
		return nil
	}

	return a.BaseReqData.CommonBizInfo.MultiRequireProduct
}

func (a *AnyCarV4AdapterTest) GetMenuId() string {
	return a.BaseReqData.CommonInfo.MenuID
}

func (a *AnyCarV4AdapterTest) GetUserPhone() string {
	if a.BaseReqData == nil {
		return ""
	}
	return a.BaseReqData.PassengerInfo.Phone
}

func (a *AnyCarV4AdapterTest) GetUserChoosePayment() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonInfo.PaymentsType
}

func (a *AnyCarV4AdapterTest) GetCarpoolSeatNum() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonBizInfo.CarpoolSeatNum
}

func (a *AnyCarV4AdapterTest) GetProductCheckStatus() int {
	return a.GetBizInfo().CheckStatus
}

func (a *AnyCarV4AdapterTest) GetPassengerCount() *int32 {
	if a.BaseReqData == nil {
		return nil
	}
	return a.BaseReqData.CommonBizInfo.PassengerCount
}

func (a *AnyCarV4AdapterTest) GetIsShowFemaleDriver() int64 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonBizInfo.IsShowFemaleDriverFirst
}

func (a *AnyCarV4AdapterTest) GetBillRouteIdList() []string {
	if a.GetBillInfo() == nil {
		return nil
	}
	return a.GetBillInfo().RouteIdList
}

func (a *AnyCarV4AdapterTest) GetBillDriverMetre() int64 {
	if a.GetBillInfo() == nil {
		return 0
	}
	return a.GetBillInfo().DriverMetre
}

func (a *AnyCarV4AdapterTest) GetBillFeeDetailInfoFee(key string) (bool, float64) {
	if len(a.GetBillFeeDetailInfo()) < 1 {
		return false, 0
	}
	return true, a.GetBillFeeDetailInfo()[key]
}

func (a *AnyCarV4AdapterTest) GetFullProduct() *biz_runtime.ProductInfoFull {
	return a.ProductInfoFull
}

func (a *AnyCarV4AdapterTest) GetFastEstimatePrice() float64 {
	return a.BaseReqData.CommonBizInfo.FastCarPrice
}

// GetMixedDeductPrice 返回企业付 抵扣金额
func (a *AnyCarV4AdapterTest) GetMixedDeductPrice() float64 {
	var deductInfo float64

	if !a.IsBusinessPay() {
		return 0.0
	}

	deductInfo = a.GetEstimateFee()

	if payInfo := a.GetPaymentInfo(); payInfo != nil && payInfo.MixedPayDeductInfo != nil && payInfo.MixedPayDeductInfo.DeductFee > 0 {
		deductInfo = payInfo.MixedPayDeductInfo.DeductFee
	}

	return deductInfo
}

func (a *AnyCarV4AdapterTest) GetVcard() *PriceApi.EstimateNewFormVCardInfo {
	return a.GetVCard()
}

func (a *AnyCarV4AdapterTest) GetCountPriceType() int32 {
	if a.GetBillInfo() == nil {
		return 0
	}

	return a.GetBillInfo().CountPriceType
}

func (a *AnyCarV4AdapterTest) GetMemberLevel() int32 {
	if a.GetPrivateBizInfo() == nil {
		return 0
	}

	return a.GetPrivateBizInfo().UserMemberProfile.LevelID
}

func (a *AnyCarV4AdapterTest) GetCarpoolFailExactEstimateFee() float64 {
	if len(a.GetExtendList()) == 1 {
		return a.GetExtendList()[0].ExactEstimateFee
	}

	return 0
}

func (a *AnyCarV4AdapterTest) GetNoAnswerCompensationData() []*Compensation.GetNoAnswerCompensationTackData {
	if a.BaseReqData == nil {
		return nil
	}

	return a.GetCommonBizInfo().NoAnswerCompensationData
}

func (a *AnyCarV4AdapterTest) GetRecPos(category int64) *int32 {
	if a.BaseReqData == nil || a.BaseReqData.CommonBizInfo.RecPosMap == nil {
		return nil
	}
	res, ok := a.BaseReqData.CommonBizInfo.RecPosMap[int32(category)]
	if ok {
		return &res
	}

	return nil
}

func (a *AnyCarV4AdapterTest) GetFontScaleType() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonInfo.FontScaleType
}

func (a *AnyCarV4AdapterTest) GetAppVersion() string {
	if a.BaseReqData == nil {
		return ""
	}
	return a.BaseReqData.CommonInfo.AppVersion
}

func (a *AnyCarV4AdapterTest) IsBusinessPay() bool {
	return false
}

func TestGetDualCarpoolFeeDescListV3(t *testing.T) {
	type args struct {
		ctx           context.Context
		prov          fee_info_render.PriceInfoDescListProvider
		dcmpKey       string
		isBigFontSize bool
	}

	pppp := &AnyCarV4AdapterTest{
		ProductInfoFull: &biz_runtime.ProductInfoFull{
			BaseReqData: &models.BaseReqData{
				CommonInfo: models.CommonInfo{
					FontScaleType: 1,
				},
			},
		},
	}
	tests := []struct {
		name string
		args args
		want []*proto.NewFormFeeDesc
	}{
		// TODO: Add test cases.
		{
			name: "a",
			args: args{
				ctx:           context.Background(),
				prov:          pppp,
				dcmpKey:       "xxxx",
				isBigFontSize: true,
			},
			want: nil,
		},
	}

	pathes := gomonkey.NewPatches()
	pathes.Reset()

	pathes.ApplyMethodReturn(pppp, "IsBusinessPay", true)
	pathes.ApplyFuncReturn(common_logic.DualPriceOrderedOnAscV3, false)
	pathes.ApplyFuncReturn(common_logic.DualPriceOrderedOnAscInner, false)
	pathes.ApplyFuncReturn(common_logic.GetDualPriceBusinessPayFeeDesc, &proto.NewFormFeeDesc{})

	// pathes.ApplyFuncReturn(getMemberDoubleDiscount, nil)
	// pathes.ApplyFuncReturn(getDualDiscount4Classify, nil)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetDualCarpoolFeeDescListV3(tt.args.ctx, tt.args.prov, tt.args.dcmpKey, tt.args.isBigFontSize); !reflect.DeepEqual(got, tt.want) {
				fmt.Println(got)
			}
		})
	}
}
