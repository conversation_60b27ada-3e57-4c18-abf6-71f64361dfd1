package category_carpool

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	Apollo2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	Render "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	util3 "git.xiaojukeji.com/gulfstream/passenger-common/util"
	util2 "git.xiaojukeji.com/intercity/biz-api/intercity-common-go/util"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"math"
)

type MultiPriceProvider interface {
	Render.PriceInfoProvider
	IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool
	GetOpenCitySourceId() int32
}

// GetMultiPriceList 多价格展示 主要服务于拼车两口价同时展示 拼成/未拼成 两个价格信息 价格描述部分全在 feeDescList 中展示;
func GetMultiPriceList(ctx context.Context, prov MultiPriceProvider) []*proto.NewFormMultiPrice {
	var (
		multiPriceList = make([]*proto.NewFormMultiPrice, 0)
		succItem       = &proto.NewFormMultiPrice{}
		failItem       = &proto.NewFormMultiPrice{}
		succPrice      float64
		failPrice      float64
	)

	if !carpool.IsCarpoolDualPriceFull(prov) {
		return multiPriceList
	}

	if prov.IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx) {
		// 当按照一口价样式显示时, 不再渲染multi_price_desc字段
		return multiPriceList
	}

	failEstimatePrice, ok := prov.GetCarpoolFailEstimateFee()
	if !ok {
		return multiPriceList
	}

	succPrice = util.RoundAbs(math.Ceil(prov.GetEstimateFee()*10)/10, 1)
	failPrice = util.RoundAbs(math.Ceil(failEstimatePrice*10)/10, 1)
	price.CheckSingle(ctx, "GetMultiPriceList", "carpool_multi_price_list_success", "succPrice", succPrice)
	price.CheckSingle(ctx, "GetMultiPriceList", "carpool_multi_price_list_fail", "failPrice", failPrice)

	// 两个价格, 不能根据两个价格抵扣算
	if prov.IsBusinessPay() {
		succPrice = 0.0
		failPrice = 0.0
	}

	succFeeMsg := Render.RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "carpool_two_price.succ", util.FormatPriceWithoutZero(succPrice, 1))
	failFeeMsg := Render.RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "carpool_two_price.fail", util.FormatPriceWithoutZero(failPrice, 1))

	sucFontSize, failFontSize := DualPriceFontSize(ctx, prov)

	failItem = &proto.NewFormMultiPrice{
		FeeMsg:      failFeeMsg,
		FeeAmount:   failPrice,
		IsLargeFont: consts2.CarpoolSmallFont,
		FontSize:    failFontSize,
	}
	multiPriceList = append(multiPriceList, failItem)

	succItem = &proto.NewFormMultiPrice{
		FeeMsg:      succFeeMsg,
		FeeAmount:   succPrice,
		IsLargeFont: consts2.CarpoolLargeFont,
		FontSize:    sucFontSize,
	}
	multiPriceList = append(multiPriceList, succItem)

	// 极速拼车【拼成/未拼成】顺序实验
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation && len(multiPriceList) == 2 && common_logic.DualPriceOrderedOnAsc(prov, 0) {
		multiPriceList[0], multiPriceList[1] = multiPriceList[1], multiPriceList[0]
	}

	return multiPriceList
}

func DualPriceFontSize(ctx context.Context, prov apollo_model.ApolloParamsProvider) (int32, int32) {
	//cfg
	fontSizeCfg := dcmp.GetJSONMap(ctx, "estimate_form_v3-estimate_price_font_size", "dual_price_carpool")

	//exp
	_, sucFontSize_, failFontSize_ := DualPriceFontSizeExp(ctx, prov)

	sucFontSize := util3.TernaryDelayExec(nil != sucFontSize_, func() interface{} { return *sucFontSize_ }, func() interface{} { return cast.ToInt32(fontSizeCfg["suc_font_size"].Int()) }).(int32)     //NOLINT
	failFontSize := util3.TernaryDelayExec(nil != failFontSize_, func() interface{} { return *failFontSize_ }, func() interface{} { return cast.ToInt32(fontSizeCfg["fail_font_size"].Int()) }).(int32) //NOLINT

	return sucFontSize, failFontSize
}

func DualPriceFontSizeExp(ctx context.Context, prov apollo_model.ApolloParamsProvider) (isHit bool, sucFontSize, failFontSize *int32) {
	key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)

	isHit, assignParams := Apollo2.GetParameters("carpool_pricestyle_cpo", key, param)

	if !isHit {
		return
	}

	if "" != assignParams["fail_font_size"] && "0" != assignParams["fail_font_size"] {
		failFontSize = util2.Int32Ptr(cast.ToInt32(assignParams["fail_font_size"]))
	}

	if "" != assignParams["suc_font_size"] && "0" != assignParams["suc_font_size"] {
		sucFontSize = util2.Int32Ptr(cast.ToInt32(assignParams["suc_font_size"]))
	}
	return
}
