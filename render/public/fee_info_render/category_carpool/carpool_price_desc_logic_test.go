package category_carpool

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

func Test_getCarpoolMemberCouponDesc(t *testing.T) {
	type args struct {
		ctx           context.Context
		prov          fee_info_render.PriceInfoDescListProvider
		dcmpKey       string
		isBigFontSize bool
	}
	ppp := &AnyCarV4AdapterTest{
		ProductInfoFull: &biz_runtime.ProductInfoFull{
			BaseReqData: &models.BaseReqData{
				CommonInfo: models.CommonInfo{},
			},
		},
	}
	tests := []struct {
		name string
		args args
		want *proto.NewFormFeeDesc
	}{
		// TODO: Add test cases.
		{
			name: "a",
			args: args{
				ctx:  context.Background(),
				prov: ppp,
			},
		},
	}
	pathes := gomonkey.NewPatches()
	pathes.Reset()

	pathes.ApplyMethodReturn(ppp, "IsBusinessPay", true)
	//pathes.ApplyMethodReturn(ppp, "IsCarpoolUnSuccessFlatPriceShowAsCapPrice", false)

	pathes.ApplyFuncReturn(cast.ToString, "")
	pathes.ApplyFuncReturn(dcmp.GetJSONMap, map[string]gjson.Result{
		"a": gjson.Result{},
		"b": gjson.Result{},
	})
	pathes.ApplyFuncReturn(common_logic.DualPriceOrderedOnAscInner, false)
	pathes.ApplyFuncReturn(common_logic.GetDualPriceBusinessPayFeeDesc, &proto.NewFormFeeDesc{})

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getCarpoolMemberCouponDesc(tt.args.ctx, tt.args.prov, tt.args.dcmpKey, tt.args.isBigFontSize); !reflect.DeepEqual(got, tt.want) {
				fmt.Errorf("getCarpoolMemberCouponDesc() = %v, want %v", got, tt.want)
			}
		})
	}
}
