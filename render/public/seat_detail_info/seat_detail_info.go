package seat_detail_info

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type SeatDetailInfo struct {
	product *biz_runtime.ProductInfoFull
}

func NewSeatDetailInfo(product *biz_runtime.ProductInfoFull) *SeatDetailInfo {
	return &SeatDetailInfo{
		product: product,
	}
}

func (s *SeatDetailInfo) GetSeatDetailInfo(ctx context.Context) []*proto.SeatDetailInfo {
	seatList := make([]*proto.SeatDetailInfo, 0)
	if s.product.BaseReqData.CommonBizInfo.SeatDetailInfo != nil {
		for _, seatItem := range s.product.BaseReqData.CommonBizInfo.SeatDetailInfo {
			seatList = append(seatList, &proto.SeatDetailInfo{
				Type:           seatItem.PassengerType,
				PassengerCount: seatItem.PassengerCount,
				IsOccupySeat:   seatItem.IsOccupySeat,
			})
		}

		return seatList
	}

	return nil
}
