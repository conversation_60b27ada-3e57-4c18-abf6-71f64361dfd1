package model

import (
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
)

type Env struct {
	ApolloParams apollo_model.ApolloParamsProvider

	Form       int32  // 表单标识
	IsShareCap bool   // 是否共享容量
	ShareCap   int    // (只有是共享容量才会使用)共享容量
	DcmpKey    string // dcmp
}

func (e *Env) SetApolloParams(apolloProv apollo_model.ApolloParamsProvider) *Env {
	e.ApolloParams = apolloProv

	return e
}

func (e *Env) SetIsShareCap(isShareCap bool) *Env {
	e.IsShareCap = isShareCap

	return e
}

func (e *Env) SetCap(cap int) *Env {
	e.ShareCap = cap

	return e
}

func (e *Env) SetDcmpKey(dcmpKey string) *Env {
	e.DcmpKey = dcmpKey

	return e
}

func (e *Env) SetDcmpKeyByFormType(formType int32) *Env {
	switch formType {
	case consts.SimpleForm:
		e.DcmpKey = consts.FeeDetailDesc
		break
	case consts.AnyCarForm:
		e.DcmpKey = consts.AnyCarV3FeeDesc
		break
	case consts.RecCarpoolOthersForm:
		e.DcmpKey = consts.RecCarpoolFeeDesc
		break
	default:
		e.DcmpKey = consts.AnyCarV3FeeDesc
		break
	}

	return e
}
