package model

import (
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"github.com/spf13/cast"
)

type (
	Coupon struct {
		CustomTag string
		Amount    float64
	}

	Ziyoubao struct {
		Amount float64
		Mile   float64
	}

	BusCard struct {
		Amount float64
	}

	CheaperThanFastCar struct {
		Amount float64
	}

	FeeDetail struct {
		Amount   float64
		Type     int32
		ExtraMap map[string]interface{}
	}

	FeeGroup struct {
		Name     string
		Text     string // 组内合并后文案
		IsMerge  bool   // 是否合并过
		Type     int32  // 费用类型
		Cap      int    // 组内容量
		CanMerge bool   // 是否可以合并
		Items    []*FeeItem
	}

	FeeOutput struct {
		Key            string
		Fee            *FeeDetail
		BorderColor    string
		TextColor      string
		TextExtraColor string
		BgFillColor    string
		HighLightColor string
		Content        string
		Icon           string
		Type           int32
		CustomTag      string
	}

	DiscountSet struct {
		CouponInfo    *Coupon
		FeeDetailInfo map[string]*FeeDetail
	}

	FeeItem struct {
		Key      string // 费项标识
		Fee      *FeeDetail
		Priority int // 在group内的优先级
	}

	ConfInstance struct {
		Form   string       `json:"form"`
		Groups []*ConfGroup `json:"groups"`
	}

	ConfGroup struct {
		Name      string     `json:"group_name"`
		MergeText string     `json:"merge_text"`
		Cap       int        `json:"cap"`
		Type      int32      `json:"fee_type"`
		CanMerge  bool       `json:"can_merge"`
		FeeList   []*ConfFee `json:"fee_list"`
	}

	ConfFee struct {
		Key           string `json:"key"`
		Toggle        string `json:"toggle"`
		MergePriority int    `json:"merge_priority"`
	}
)

func (f *FeeOutput) Less(target *FeeOutput) bool {
	if f == nil || f.Fee == nil {
		return true
	}
	if target == nil || target.Fee == nil {
		return false
	}
	switch f.Type {
	case consts.TypeDynamic:
		originDiff, ok := f.Fee.ExtraMap["dynamic_times"].(float64)
		if !ok {
			return false
		}
		targetDiff, ok := target.Fee.ExtraMap["dynamic_times"].(float64)
		if !ok {
			return false
		}
		return originDiff < targetDiff
	default:
		return f.Fee.Amount < target.Fee.Amount
	}
}

type RenderCallback map[string]func(key string, path string, fee float64, feeType int32) *FeeOutput
type FilterCallback map[string]func() bool

type FeeDescEngineInputProvider interface {
	render.BaseProvider
	render.ProductProvider
	render.BillInfoProvider
	render.ActivityInfoProvider
	render.PaymentInfoProvider
	render.MemberInfoProvider
	render.TaxiInfoProvider
	render.DynamicIconABProvider
	// render.ApolloProvider
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
	GetFastCarPrice() float64
}

type FeeDescByQuotationEngineInputProvider interface {
	render.QuotationInfoProvider
	render.DynamicIconABProvider

	GetProductCategory() int64
	GetCityID() int
	GetProductId() int64
}

type FeeList []*FeeItem

func (l FeeList) Len() int           { return len(l) }
func (l FeeList) Swap(i, j int)      { l[i], l[j] = l[j], l[i] }
func (l FeeList) Less(i, j int) bool { return l[i].Priority < l[j].Priority }

type RenderAdapter struct {
	FeeDescEngineInputProvider
}

func (ra *RenderAdapter) IsHoliday() int32 {
	displayLines := ra.GetDisplayLines()

	if displayLines == nil {
		return 0
	}

	if displayLine, ok1 := displayLines[consts.FeeTaxiPeak]; ok1 && displayLine.ExtraInfo != nil {
		if v, ok2 := displayLine.ExtraInfo["is_holiday"]; ok2 {
			return cast.ToInt32(v)
		}
	}

	return 0
}
