package fee_desc_engine

import (
	"context"
	"fmt"
	model2 "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/tidwall/gjson"
)

func newInput(ctx context.Context) *model2.FeeInput {
	res := &model2.FeeInput{
		// BusinessPayDeduction: util.Float64Ptr(10.01),
		// DynamicProtect: util.Float64Ptr(11.01),
		// TaxiSpecial:    util.Float64Ptr(12.01),

		//CouponInfo: &model.Coupon{
		//	CustomTag: "aaa",
		//	Amount:    13.01,
		//},
		//Ziyoubao: &model.Ziyoubao{
		//	Amount: 14.01,
		//	Mile:   15.01,
		//},
		DiscountInfo: map[string]float64{
			// "bonus":     16.01,
			"hk_bonus":  17.01,
			"hk_coupon": 18.01,
		},
		//FeeDetailInfo: map[string]float64{
		//	"red_packet":                18.01,
		//	"dynamic_diff_price":        19.01,
		//	"energy_consume_fee":        20.01,
		//	"designated_driver_fee":     21.01,
		//	"cross_city_fee":            22.01,
		//	"taxi_peak_price":           23.01,
		//	"taxi_holiday_price":        24.01,
		//	"sps_barrier_free_car_fee":  25.01,
		//	"sps_pet_fee":               26.01,
		//	"sps_pet_platform_fee":      27.01,
		//	"sps_pick_up_guide_fee":     28.01,
		//	consts.FeeRevolvingDiscount: 29.01,
		//	consts.FeeRevolvingRebate:   30.01,
		//},
		RenderCallback: map[string]func(key string, groupName string, fee float64, feeType int32) *model2.FeeOutput{
			"default": RenderCallbackDefault(ctx),
		},
	}
	return res
}

//func TestEngine(t *testing.T) {
//	env := NewEnv(2).SetDcmpKey("anycar_estimate-fee_detail_desc")
//	output := NewFeeEngine(newInput(context.Background()), env).Do(context.Background())
//
//	fmt.Println(util.JustJsonEncode(output))
//}

// RenderCallbackDefault 默认渲染回调
func RenderCallbackDefault(ctx context.Context) func(key string, path string, fee float64, feeType int32) *model2.FeeOutput {
	return func(key string, path string, fee float64, feeType int32) *model2.FeeOutput {
		fmt.Println(path)
		if conf := gjson.Get(mockDcmp, path).Map(); len(conf) > 0 {
			tag := map[string]string{
				"num": util.FormatPrice(fee, -1),
			}

			output := &model2.FeeOutput{
				BorderColor: conf["border_color"].String(),
				Content:     util.ReplaceTag(ctx, conf["content"].String(), tag),
				Icon:        conf["icon"].String(),
				Type:        feeType,
				Fee: &model2.FeeDetail{
					Amount: fee,
				},
			}

			if conf["text_color"].Exists() {
				output.TextColor = conf["text_color"].String()
			} else {
				output.TextColor = conf["border_color"].String()
			}

			return output
		}

		return nil
	}
}

var mockDcmp = `{
  "taxi_peak_fee": {
    "content": "{含信息费{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "出租车信息费(此字段用作备注)"
  },
  "red_packet": {
    "content": "含节假日司机服务费{{{num}}}元",
    "icon": "",
    "border_color": "",
    "title": "节假日服务费"
  },
  "taxi_holiday_price": {
    "content": "含节假日附加费{{{num}}}元",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "出租车节假日"
  },
  "bonus": {
    "content": "福利金已抵扣{{{num}}}{{currency_unit}}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "打车金"
  },
  "coupon": {
    "content": "券已抵扣{{{num}}}元",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "券"
  },
  "revolving_account_rebate": {
    "content": "{{{num}}}元车费",
    "icon": "https://s.didi.cn/rtQBCP?suffix=.png",
    "border_color": "#FFC2BF",
    "title": "泛快内循环返利"
  },
  "revolving_account_discount": {
    "content": "-{{{num}}}元",
    "icon": "https://s.didi.cn/rtQBCP?suffix=.png",
    "border_color": "#FFC2BF",
    "title": "泛快内循环抵扣"
  },
  "business_pay_deduction": {
    "content": "{-{{num}}元}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/TJcl9iFeku1638954388682.png",
    "border_color": "#AFBCD8",
    "title": "企业支付"
  },
  "normal_discount": {
    "content": "{-{{num}}元}",
    "icon": "https://img-hxy021.didistatic.com/static/starimg/img/TknwOw0HY41669881453792.png",
    "border_color": "#F47749",
    "title": "汇总优惠"
  },
  "normal_increase": {
    "content": "{+{{num}}元}",
    "icon": "https://img-hxy021.didistatic.com/static/starimg/img/Pg7lNELB5c1670492523230.png",
    "border_color": "#FFC2BF",
    "title": "加价汇总"
  }
}`

var mockHKDCmp = `{
  "hk_bonus": {
    "content": "{-{{num}}HKD}",
    "icon": "https://dpubstatic.udache.com/static/dpubimg/KqqrHwyL27MDlHrhyyiC7.png",
    "border_color": "#FFC2BF",
    "title": "香港打车金"
  },
  "hk_coupon": {
    "content": "{-{{amount}}HKD}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/ydBUiytPMj1638954388837.png",
    "border_color": "#FFC2BF",
    "title": "香港券"
  }
}`
