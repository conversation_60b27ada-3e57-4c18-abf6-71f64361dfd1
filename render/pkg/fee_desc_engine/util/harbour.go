package util

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

func BattleHarbourFeeDesc(ctx context.Context, pcIdToDescs map[int64][]*proto.NewFormFeeDesc, maxCount int) []*proto.NewFormFeeDesc {
	var typeToDesc = make(map[int32][]*proto.NewFormFeeDesc)
	var tunnelFeeDescs = make([]*proto.NewFormFeeDesc, 0, 8)
	for _, descs := range pcIdToDescs {
		if len(descs) <= 0 {
			continue
		}
		for _, feeDesc := range descs {
			if feeDesc == nil {
				continue
			}
			if feeDesc.Key != nil && util.InArrayStr(*feeDesc.Key, []string{"hk_tunnel_fee", "hk_tunnel_fee_contained"}) {
				tunnelFeeDescs = append(tunnelFeeDescs, feeDesc)
			} else {
				typeToDesc[feeDesc.Type] = append(typeToDesc[feeDesc.Type], feeDesc)
			}
		}
	}

	var res = make([]*proto.NewFormFeeDesc, 0, 4)
	tunnelFeeDesc := mergeTunnelFeeDesc(ctx, tunnelFeeDescs, len(pcIdToDescs))
	if tunnelFeeDesc != nil {
		res = append(res, tunnelFeeDesc)
	}

	for feeType, feeDescs := range typeToDesc {
		switch feeType {
		case consts.TypeDecrement:
			decrFee := mergeDecrFeeDesc(ctx, feeDescs)
			if decrFee != nil {
				res = append(res, decrFee)
			}
		case consts.TypeDynamic:
			if len(feeDescs) > 0 {
				res = append(res, feeDescs[0]) // 动调只取第一个
			}
		case consts.TypeNeutral:
			for _, desc := range feeDescs {
				if len(res) >= maxCount {
					break
				}
				if desc != nil {
					res = append(res, desc)
				}
			}
		}
	}

	return res[:min(len(res), maxCount)]
}

func mergeTunnelFeeDesc(ctx context.Context, feeDescs []*proto.NewFormFeeDesc, productCnt int) *proto.NewFormFeeDesc {
	if len(feeDescs) <= 0 {
		return nil
	}

	// 隧道费冲突，则展示「详见内页」
	isTunnelFeeConflict := false
	if len(feeDescs) != productCnt {
		isTunnelFeeConflict = true
	}
	tunnelFee := 0.
	for i, desc := range feeDescs {
		if i == 0 {
			tunnelFee = desc.Amount
			continue
		}
		if desc.Amount != tunnelFee {
			isTunnelFeeConflict = true
			break
		}
	}
	if isTunnelFeeConflict {
		return GetFeeDescFromDcmp(ctx, consts.DcmpKeyHarbourFeeDescAggregation, "hk_tunnel_fee_conflict", map[string]string{})
	}

	return feeDescs[0]
}

func mergeDecrFeeDesc(ctx context.Context, decrFeeDescs []*proto.NewFormFeeDesc) *proto.NewFormFeeDesc {
	if len(decrFeeDescs) <= 0 {
		return nil
	}

	if len(decrFeeDescs) == 1 {
		return decrFeeDescs[0]
	}

	maxAmount := 0.
	for _, desc := range decrFeeDescs {
		maxAmount = max(maxAmount, desc.Amount)
	}
	if maxAmount <= 0 {
		return nil
	}

	desc := GetFeeDescFromDcmp(ctx, consts.DcmpKeyHarbourFeeDescAggregation, "decr_max", map[string]string{"num": strconv.FormatFloat(maxAmount, 'f', -1, 64)}) // 获取最大减价项
	if desc == nil {
		return nil
	}
	desc.Amount = maxAmount
	return desc
}

func GetFeeDescFromDcmp(ctx context.Context, key, path string, tag map[string]string) *proto.NewFormFeeDesc {
	if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
		return &proto.NewFormFeeDesc{
			Icon:           conf["icon"].String(),
			Content:        util.ReplaceTag(ctx, conf["content"].String(), tag),
			BorderColor:    conf["border_color"].String(),
			TextColor:      util.StringPtr(conf["border_color"].String()),
			BgColor:        util.StringPtr(conf["bg_color"].String()),
			HighlightColor: util.StringPtr(conf["highlight_color"].String()),
		}
	}
	return nil
}

func ConvertFeeOutputToDesc(ctx context.Context, outputs []*model.FeeOutput) []*proto.NewFormFeeDesc {
	ret := make([]*proto.NewFormFeeDesc, 0, 4)

	for _, output := range outputs {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			BorderColor:    output.BorderColor,
			Icon:           output.Icon,
			Content:        output.Content,
			Type:           output.Type,
			TextColor:      util.StringPtr(output.TextColor),
			HighlightColor: util.StringPtr(output.HighLightColor),
			BgColor:        util.StringPtr(output.BgFillColor),
			Key:            util.StringPtr(output.Key),
		}

		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}

		ret = append(ret, desc)
	}

	return ret
}
