package util

import (
	"context"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
)

func TestBattleHarbourFeeDesc_TunnelFeeConflict(t *testing.T) {
	ctx := context.Background()
	typeToDesc := map[int64][]*proto.NewFormFeeDesc{
		12300: {{Amount: 10, Type: consts.TypeIncrement, Key: util.StringPtr("hk_tunnel_fee")}},
		12310: {{Amount: 20, Type: consts.TypeIncrement, Key: util.StringPtr("hk_tunnel_fee_contained")}},
	}

	// mock GetFeeDescFromDcmp
	p := mockey.Mock(GetFeeDescFromDcmp).Return(&proto.NewFormFeeDesc{Amount: 5, Content: "tunnel conflict"}).Build()
	defer p.UnPatch()

	res := BattleHarbourFeeDesc(ctx, typeToDesc, 3)
	if len(res) == 0 || res[0].Content != "tunnel conflict" {
		t.Errorf("expect tunnel conflict desc, got %+v", res)
	}
}

func TestBattleHarbourFeeDesc_TunnelFeeNoConflict(t *testing.T) {
	ctx := context.Background()
	typeToDesc := map[int64][]*proto.NewFormFeeDesc{
		12300: {{Amount: 10, Type: consts.TypeIncrement, Key: util.StringPtr("hk_tunnel_fee")}},
	}
	res := BattleHarbourFeeDesc(ctx, typeToDesc, 3)
	if len(res) == 0 || res[0].Amount != 10 {
		t.Errorf("expect tunnel fee desc, got %+v", res)
	}
}

func TestBattleHarbourFeeDesc_Decrement(t *testing.T) {
	// mock GetFeeDescFromDcmp
	p := mockey.Mock(GetFeeDescFromDcmp).Return(&proto.NewFormFeeDesc{Amount: 5}).Build()
	defer p.UnPatch()

	ctx := context.Background()
	typeToDesc := map[int64][]*proto.NewFormFeeDesc{
		12300: {{Amount: 5, Type: consts.TypeDecrement}},
		12310: {{Amount: 5, Type: consts.TypeDecrement}},
	}
	res := BattleHarbourFeeDesc(ctx, typeToDesc, 3)
	if len(res) < 1 || res[0].Amount != 5 {
		t.Errorf("expect decrement desc, got %+v", res)
	}
}

func TestBattleHarbourFeeDesc_DecrementMulti(t *testing.T) {
	// mock GetFeeDescFromDcmp
	p := mockey.Mock(GetFeeDescFromDcmp).Return(&proto.NewFormFeeDesc{Amount: 8}).Build()
	defer p.UnPatch()

	ctx := context.Background()
	typeToDesc := map[int64][]*proto.NewFormFeeDesc{
		12300: {{Amount: 5, Type: consts.TypeDecrement}, {Amount: 8, Type: consts.TypeDecrement}},
	}

	res := BattleHarbourFeeDesc(ctx, typeToDesc, 3)
	if len(res) < 1 || res[0].Amount != 8 {
		t.Errorf("expect max decrement desc, got %+v", res)
	}
}

func TestBattleHarbourFeeDesc_Neutral(t *testing.T) {
	ctx := context.Background()
	typeToDesc := map[int64][]*proto.NewFormFeeDesc{
		12300: {{Amount: 1, Type: consts.TypeNeutral}, {Amount: 2, Type: consts.TypeNeutral}},
	}
	res := BattleHarbourFeeDesc(ctx, typeToDesc, 2)
	if len(res) != 2 {
		t.Errorf("expect 2 neutral desc, got %+v", res)
	}
}

func TestConvertFeeOutputToDesc(t *testing.T) {
	ctx := context.Background()

	t.Run("normal case", func(t *testing.T) {
		outputs := []*model.FeeOutput{
			{
				BorderColor:    "red",
				Icon:           "icon1",
				Content:        "desc1",
				Type:           1,
				TextColor:      "black",
				HighLightColor: "yellow",
				Fee:            &model.FeeDetail{Amount: 12.34},
			},
		}
		descs := ConvertFeeOutputToDesc(ctx, outputs)
		assert.Len(t, descs, 1)
		assert.Equal(t, "red", descs[0].BorderColor)
		assert.Equal(t, "icon1", descs[0].Icon)
		assert.Equal(t, "desc1", descs[0].Content)
		assert.Equal(t, int32(1), descs[0].Type)
		assert.NotNil(t, descs[0].TextColor)
		assert.Equal(t, "black", *descs[0].TextColor)
		assert.NotNil(t, descs[0].HighlightColor)
		assert.Equal(t, "yellow", *descs[0].HighlightColor)
		assert.Equal(t, 12.34, descs[0].Amount)
	})

	t.Run("nil FeeOutput", func(t *testing.T) {
		outputs := []*model.FeeOutput{nil}
		descs := ConvertFeeOutputToDesc(ctx, outputs)
		assert.Len(t, descs, 0)
	})

	t.Run("nil Fee", func(t *testing.T) {
		outputs := []*model.FeeOutput{
			{
				BorderColor:    "blue",
				Icon:           "icon2",
				Content:        "desc2",
				Type:           2,
				TextColor:      "white",
				HighLightColor: "green",
				Fee:            nil,
			},
		}
		descs := ConvertFeeOutputToDesc(ctx, outputs)
		assert.Len(t, descs, 1)
		assert.Equal(t, 0.0, descs[0].Amount)
	})

	t.Run("empty input", func(t *testing.T) {
		descs := ConvertFeeOutputToDesc(ctx, []*model.FeeOutput{})
		assert.Len(t, descs, 0)
	})

	t.Run("multiple outputs", func(t *testing.T) {
		outputs := []*model.FeeOutput{
			{
				BorderColor:    "red",
				Icon:           "icon1",
				Content:        "desc1",
				Type:           1,
				TextColor:      "black",
				HighLightColor: "yellow",
				Fee:            &model.FeeDetail{Amount: 1.1},
			},
			{
				BorderColor:    "blue",
				Icon:           "icon2",
				Content:        "desc2",
				Type:           2,
				TextColor:      "white",
				HighLightColor: "green",
				Fee:            &model.FeeDetail{Amount: 2.2},
			},
		}
		descs := ConvertFeeOutputToDesc(ctx, outputs)
		assert.Len(t, descs, 2)
		assert.Equal(t, 1.1, descs[0].Amount)
		assert.Equal(t, 2.2, descs[1].Amount)
	})
}

func TestGetFeeDescFromDcmp(t *testing.T) {
	ctx := context.Background()

	t.Run("dcmp返回完整配置", func(t *testing.T) {
		patch := mockey.Mock(dcmp.GetJSONMap).Return(map[string]gjson.Result{
			"icon":            gjson.Parse(`"icon-url"`),
			"content":         gjson.Parse(`"desc-content"`),
			"border_color":    gjson.Parse(`"red"`),
			"bg_color":        gjson.Parse(`"white"`),
			"highlight_color": gjson.Parse(`"yellow"`),
		}).Build()
		defer patch.UnPatch()

		desc := GetFeeDescFromDcmp(ctx, "key", "path", map[string]string{"tag": "val"})
		assert.NotNil(t, desc)
		assert.Equal(t, "icon-url", desc.Icon)
		assert.Equal(t, "desc-content", desc.Content)
		assert.Equal(t, "red", desc.BorderColor)
		assert.NotNil(t, desc.TextColor)
		assert.Equal(t, "red", *desc.TextColor)
		assert.NotNil(t, desc.BgColor)
		assert.Equal(t, "white", *desc.BgColor)
		assert.NotNil(t, desc.HighlightColor)
		assert.Equal(t, "yellow", *desc.HighlightColor)
	})

	t.Run("dcmp返回空map", func(t *testing.T) {
		patch := mockey.Mock(dcmp.GetJSONMap).Return(map[string]gjson.Result{}).Build()
		defer patch.UnPatch()

		desc := GetFeeDescFromDcmp(ctx, "key", "path", nil)
		assert.Nil(t, desc)
	})
}
