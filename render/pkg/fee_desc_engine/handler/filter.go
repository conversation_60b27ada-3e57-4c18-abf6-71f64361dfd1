package handler

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

func (h *handler) registerFilter(ctx context.Context) *handler {
	if h.input == nil || len(h.input.FilterCallback) <= 0 {
		return h
	}

	h.defaultFilter = model.FilterCallback{}

	return h
}

func (h *handler) recoverFilter(ctx context.Context) *handler {
	if h.input == nil || len(h.input.FilterCallback) <= 0 {
		return h
	}

	if h.defaultFilter == nil {
		h.defaultFilter = model.FilterCallback{}
	}

	for key, filter := range h.input.FilterCallback {
		h.defaultFilter[key] = filter
	}

	return h
}

func (h *handler) filter(ctx context.Context) *handler {
	var (
		keys []string
	)

	if len(h.formatInput) <= 0 || len(h.defaultFilter) <= 0 {
		return h
	}

	for key := range h.formatInput {
		if function, ok := h.defaultFilter[key]; ok && function != nil && function() {
			keys = append(keys, key)
			delete(h.formatInput, key)
		}
	}

	if len(keys) > 0 {
		log.Trace.Infof(ctx, "engine.filter", "pc-id:$v, key:%+v", h.productCategory, keys)
	}

	return h
}
