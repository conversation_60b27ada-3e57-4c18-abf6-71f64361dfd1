package handler

import (
	"context"
	"encoding/json"
	"fmt"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	apolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

func (h *handler) load(ctx context.Context) *handler {
	return h.getConf(ctx).
		mount(ctx).
		registerRender(ctx).
		coverRender(ctx).
		registerFilter(ctx).
		recoverFilter(ctx)
}

// 从apollo获取表单的费项配置
func (h *handler) getConf(ctx context.Context) *handler {
	var instance = make([]*model.ConfInstance, 0)

	coditions := apolloModel.NewCondition(map[string]string{
		"form": fmt.Sprintf("%d", h.env.Form),
	})

	configs, err := apollo.GetConfigsByNamespaceAndConditions(ctx, consts.FeeDescription, coditions)
	if err != nil {
		log.Trace.Warnf(ctx, "getConf", "get config fail, namespce: %+v, err: %v", consts.FeeDescription, err)
		return h
	}

	err = json.Unmarshal(configs, &instance)
	if err != nil || len(instance) <= 0 {
		log.Trace.Warnf(ctx, "getConf", "unmarshal fail, err: %v", err)
		return h
	}

	h.confInstance = instance[0]

	return h
}

// mount 挂载是否可以通过适用方注册进来? 每次加逻辑不利于后续迭代
// 将各种费用挂载到handle上
func (h *handler) mount(ctx context.Context) *handler {
	input := h.input

	h.formatInput = make(map[string]*model.FeeDetail)

	// 处理账单fee_detail_info
	if input.FeeDetailInfo != nil {
		for key, amount := range input.FeeDetailInfo {
			h.formatInput[key] = &model.FeeDetail{
				Amount:   amount,
				ExtraMap: nil,
			}
		}
	}

	// 账单补充信息
	if input.FeeExtraInfo != nil {
		for key, amount := range input.FeeExtraInfo {
			h.formatInput[key] = &model.FeeDetail{
				Amount:   amount,
				ExtraMap: nil,
			}
		}
	}

	// 处理discount
	if input.DiscountInfo != nil {
		for key, amount := range input.DiscountInfo {
			h.formatInput[key] = &model.FeeDetail{
				Amount:   amount,
				ExtraMap: nil,
			}
		}
	}

	// 处理coupon
	if input.CouponInfo != nil {
		h.formatInput[consts.FeeCoupon] = &model.FeeDetail{
			Amount: input.CouponInfo.Amount,
			ExtraMap: map[string]interface{}{
				"custom_tag": input.CouponInfo.CustomTag,
			},
		}
	}

	// 处理自由宝
	if input.Ziyoubao != nil {
		h.formatInput[consts.FeeZiyoubao] = &model.FeeDetail{
			Amount: input.Ziyoubao.Amount,
			ExtraMap: map[string]interface{}{
				"mile": input.Ziyoubao.Mile,
			},
		}
	}

	// 处理企业付
	if input.BusinessPayDeduction != nil {
		h.formatInput[consts.BusinessPay] = &model.FeeDetail{
			Amount: *input.BusinessPayDeduction,
		}
	}

	// 处理溢价保护
	if input.DynamicProtect != nil {
		h.formatInput[consts.FeeMemberProtect] = &model.FeeDetail{
			Amount: *input.DynamicProtect,
		}
	}

	// 出租车特惠
	if input.TaxiSpecial != nil {
		h.formatInput[consts.FeeSpecialPrice] = &model.FeeDetail{
			Amount: *input.TaxiSpecial,
		}
	}

	// 网开台优惠
	if input.TCDiscountFee != nil {
		h.formatInput[consts.TCDiscountFee] = &model.FeeDetail{
			Amount: *input.TCDiscountFee,
		}
	}

	// 比快车省
	if input.CheaperThanFastCar != nil {
		h.formatInput[consts.CheaperThanFastCar] = &model.FeeDetail{
			Amount: input.CheaperThanFastCar.Amount,
		}
	}

	// 处理中性项
	h.loadNeutralInfo()

	return h
}

// loadNeutralInfo 处理中性项
func (h *handler) loadNeutralInfo() *handler {
	if h.input == nil {
		return h
	}

	if h.input.NeutralInfo != nil {
		for key, amount := range h.input.NeutralInfo {
			h.formatInput[key] = &model.FeeDetail{
				Amount: amount,
			}
		}
	}

	return h
}
