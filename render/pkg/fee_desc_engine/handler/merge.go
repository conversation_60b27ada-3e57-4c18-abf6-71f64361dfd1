package handler

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

func (h *handler) merge(ctx context.Context) *handler {
	if len(h.groups) <= 0 {
		return h
	}

	if h.env.IsShareCap {
		h.sharedCapMergeSameGroup()
	} else {
		h.noSharedCapMerge()
	}
	return h
}

func (h *handler) noSharedCapMerge() {
	for _, group := range h.groups {
		if len(group.Items) > group.Cap {
			h.mergeGroup(group)
		}
	}
}

func (h *handler) sharedCapMerge() {
	for index, group := range h.groups {
		if h.residueCap <= 0 {
			// 没有可用容量时 不会再继续构建组 在这里截断
			h.groups = h.groups[:index]
			break
		} else if len(group.Items) <= h.residueCap {
			h.residueCap -= len(group.Items)
			continue
		} else if len(group.Items) > h.residueCap && group.CanMerge {
			h.mergeGroup(group)
			h.residueCap--
		} else {
			h.groups = append(h.groups[:index], h.groups[index+1:]...)
		}
	}
}

// sharedCapMergeSameGroup 同组合并模式
func (h *handler) sharedCapMergeSameGroup() {
	for index, group := range h.groups {
		if h.residueCap <= 0 {
			// 没有可用容量时 不会再继续构建组 在这里截断
			h.groups = h.groups[:index]
			break
		} else if len(group.Items) > 1 {
			h.mergeGroup(group)
			h.residueCap--
		} else if len(group.Items) == 1 {
			h.residueCap--
		} else {
			h.groups = append(h.groups[:index], h.groups[index+1:]...)
		}
	}
}

func (h *handler) mergeGroup(group *model.FeeGroup) {
	if group == nil {
		return
	}

	var (
		totalAmount float64
	)

	for _, item := range group.Items {
		amount := item.Fee.Amount

		amount = amount * 100
		totalAmount += amount
	}

	totalAmount = totalAmount / 100

	if totalAmount < 0 {
		totalAmount = -totalAmount
	}
	group.Items = []*model.FeeItem{
		{
			Fee: &model.FeeDetail{
				Amount: totalAmount,
			},
		},
	}

	group.IsMerge = true

	return
}
