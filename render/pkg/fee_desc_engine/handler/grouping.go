package handler

import (
	"context"
	"sort"

	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/passenger-common/sdk/apollo"
)

func (h *handler) grouping(ctx context.Context) *handler {
	if h.confInstance == nil {
		return h
	}

	confGroups := h.confInstance.Groups
	groups := make([]*model.FeeGroup, 0)

	for _, confGroup := range confGroups {
		feeItems := make([]*model.FeeItem, 0)
		if confGroup == nil || len(confGroup.FeeList) <= 0 {
			continue
		}

		for _, item := range confGroup.FeeList {
			if confGroup.Type == consts.TypeNeutral {
				if feeItem := h.neutralGroup(ctx, item); feeItem != nil {
					feeItems = append(feeItems, feeItem)
				}

				continue
			}

			if feeDetail, ok := h.formatInput[item.Key]; ok && feeDetail.Amount != 0 {
				if feeDetail.Amount < 0 {
					feeDetail.Amount = -feeDetail.Amount
				}

				if len(item.Toggle) > 0 {
					if !h.checkToggle(ctx, item.Toggle) {
						continue
					}
				}

				if item.MergePriority == 0 {
					item.MergePriority = consts.MergePriorityDefault
				}

				feeItems = append(feeItems, &model.FeeItem{
					Key:      item.Key,
					Fee:      feeDetail,
					Priority: item.MergePriority,
				})
			}
		}

		if len(feeItems) <= 0 {
			continue
		}

		var sortedItems model.FeeList
		sortedItems = feeItems
		sort.Sort(sortedItems)

		mergeText := confGroup.MergeText
		if firstItem := sortedItems[0]; firstItem.Priority < consts.MergePriorityDefault {
			mergeText = firstItem.Key
		}

		// 取优先级最高的item，如果无优先级区分，取组的默认text

		groups = append(groups, &model.FeeGroup{
			Name:     confGroup.Name,
			Items:    feeItems,
			Text:     mergeText,
			Type:     confGroup.Type,
			Cap:      confGroup.Cap,
			CanMerge: confGroup.CanMerge,
		})
	}

	h.groups = groups

	return h
}

func (h *handler) checkToggle(ctx context.Context, key string) bool {
	provider := h.env.ApolloParams
	if provider == nil {
		return false
	}
	apolloKey, params := provider.ApolloParamsGen(apollo_model.WithUIDKey, apollo_model.WithMenuID, apollo_model.WithProductCategory)
	//apolloKey, params := provider.GetApolloParams(biz_runtime.WithUIDKey, biz_runtime.WithMenuID)
	//params := map[string]string{
	//	"city":          fmt.Sprintf("%d", provider.GetCityID()),
	//	"lang":          provider.GetLang(),
	//	"phone":         provider.GetUserPhone(),
	//	"pid":           fmt.Sprintf("%d", provider.GetPID()),
	//	"uid":           fmt.Sprintf("%d", provider.GetUID()),
	//	"access_key_id": fmt.Sprintf("%d", provider.GetAccessKeyId()),
	//	"app_version":   provider.GetAppVersion(),
	//	"menu_id":       provider.GetMenuId(),
	//}
	res := apollo.FeatureToggle(key, apolloKey, params)
	if res.IsAllow() {
		return true
	}

	return false
}

func (h *handler) neutralGroup(ctx context.Context, item *model.ConfFee) *model.FeeItem {
	if feeDetail, ok := h.formatInput[item.Key]; ok {
		if len(item.Toggle) > 0 {
			if !h.checkToggle(ctx, item.Toggle) {
				return nil
			}
		}

		return &model.FeeItem{
			Key: item.Key,
			Fee: feeDetail,
		}
	}

	return nil
}
