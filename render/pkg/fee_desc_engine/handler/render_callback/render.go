package render_callback

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

// RenderCallBackDefault 默认渲染
func RenderCallBackDefault(ctx context.Context) func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
	return func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
		if len(key) < 0 {
			key = consts.FeeDetailDesc
		}

		if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
			tag := map[string]string{
				"num": util.FormatPrice(fee, -1),
			}

			output := &engine_model.FeeOutput{
				Key:            path,
				BorderColor:    conf["border_color"].String(),
				TextExtraColor: conf["text_extra_color"].String(),
				BgFillColor:    conf["bg_fill_color"].String(),
				TextColor:      conf["text_color"].String(),
				HighLightColor: conf["highlight_color"].String(),
				Content:        util.ReplaceTag(ctx, conf["content"].String(), tag),
				Icon:           conf["icon"].String(),
				Type:           feeType,
				Fee: &engine_model.FeeDetail{
					Amount: fee,
				},
			}

			return output
		}

		return nil
	}
}
