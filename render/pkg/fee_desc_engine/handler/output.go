package handler

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

func (h *handler) output(ctx context.Context) []*model.FeeOutput {
	result := make([]*model.FeeOutput, 0)
	if len(h.groups) <= 0 {
		return result
	}

	for _, group := range h.groups {
		if group.IsMerge && len(group.Items) > 0 {
			output := h.formatMergeOutput(ctx, group)
			if output != nil {
				result = append(result, output)
			}
		} else if len(group.Items) > 0 {
			for _, item := range group.Items {
				output := h.formatOutput(ctx, item.Key, item.Fee.Amount, group.Type)
				if output != nil {
					result = append(result, output)
				}
			}
		}
	}

	return result
}

func (h *handler) formatOutput(ctx context.Context, key string, fee float64, feeType int32) *model.FeeOutput {
	if function, ok := h.defaultRender[key]; ok {
		result := function(h.env.DcmpKey, key, fee, feeType)

		return result
	} else if function, ok := h.defaultRender["default"]; ok {
		result := function(h.env.DcmpKey, key, fee, feeType)

		return result
	}

	return nil
}

func (h *handler) formatMergeOutput(ctx context.Context, group *model.FeeGroup) *model.FeeOutput {
	key, fee, feeType := group.Text, group.Items[0].Fee, group.Type

	dcmpKey := h.env.DcmpKey
	if len(dcmpKey) < 0 {
		dcmpKey = consts.FeeDetailDesc
	}

	conf := dcmp.GetJSONMap(ctx, h.env.DcmpKey, key)

	if len(conf) > 0 {
		// 没有优先级区别
		if group.Items[0].Priority == consts.MergePriorityDefault {
			return &model.FeeOutput{
				Fee:            fee,
				BorderColor:    conf["border_color"].String(),
				TextExtraColor: conf["text_extra_color"].String(),
				BgFillColor:    conf["bg_fill_color"].String(),
				TextColor:      conf["text_color"].String(),
				HighLightColor: conf["highlight_color"].String(),
				Content: util.ReplaceTag(ctx, conf["content"].String(), map[string]string{
					"num": util.FormatPrice(fee.Amount, 2),
				}),
				Icon: conf["icon"].String(),
				Type: feeType,
			}
		}

		// 按照最高优先级的样式渲染
		return h.formatOutput(ctx, key, group.Items[0].Fee.Amount, group.Type)
	}

	return nil
}
