package handler

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/handler/render_callback"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

// 全局默认渲染方法
func (h *handler) registerRender(ctx context.Context) *handler {
	h.defaultRender = model.RenderCallback{
		"default": render_callback.RenderCallBackDefault(ctx),
	}

	return h
}

// 为每个费项注册渲染方法
func (h *handler) coverRender(ctx context.Context) *handler {
	if h.input == nil || len(h.input.RenderCallback) <= 0 {
		return h
	}

	if h.defaultRender == nil {
		h.defaultRender = model.RenderCallback{}
	}

	for key, function := range h.input.RenderCallback {
		h.defaultRender[key] = function
	}

	return h
}
