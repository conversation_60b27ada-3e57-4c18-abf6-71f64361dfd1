package input

import (
	"context"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"git.xiaojukeji.com/gulfstream/mamba/logic/sfc_simple_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func TestBuildBargainFeeInput(t *testing.T) {
	ad := sfc_simple_estimate.SFCarAdapter{
		ProductInfoFull: &biz_runtime.ProductInfoFull{},
	}
	_ = BuildBargainFeeInput(context.TODO(), ad)
}

// 该测试函数由AI自动生成
func TestGetDiscountInfo4EngageCar(t *testing.T) {
	// 设置测试上下文
	ctx := context.Background()

	t.Run("有折扣数据", func(t *testing.T) {
		// Mock供应商接口
		mockProv := &MockFeeDescEngineInputProvider{}

		// Mock GetRenderDiscount返回有折扣数据
		patchGetRenderDiscount := mockey.Mock((*MockFeeDescEngineInputProvider).GetRenderDiscount).Return([]*PriceApi.RenderDiscount{
			{Amount: 1000},
		}).Build()
		defer patchGetRenderDiscount.UnPatch()

		// Mock CheckSingle以避免实际调用
		patchCheckSingle := mockey.Mock(price.CheckSingle).Return().Build()
		defer patchCheckSingle.UnPatch()

		// 执行测试函数
		result := getDiscountInfo(ctx, mockProv)

		// 断言结果
		assert.NotNil(t, result, "返回结果不应为nil")
		assert.Equal(t, 10.0, result[consts.FeeNormalDiscount], "折扣金额应为10.0（1000/100）")
	})

	t.Run("无折扣数据", func(t *testing.T) {
		// Mock供应商接口
		mockProv := &MockFeeDescEngineInputProvider{}

		// Mock GetRenderDiscount返回空数据
		patchGetRenderDiscount := mockey.Mock((*MockFeeDescEngineInputProvider).GetRenderDiscount).Return([]*PriceApi.RenderDiscount{}).Build()
		defer patchGetRenderDiscount.UnPatch()

		// 执行测试函数
		result := getDiscountInfo(ctx, mockProv)

		// 断言结果
		assert.NotNil(t, result, "返回结果不应为nil")
		assert.Empty(t, result, "折扣信息应为空")
	})

	t.Run("折扣金额为0", func(t *testing.T) {
		// Mock供应商接口
		mockProv := &MockFeeDescEngineInputProvider{}

		// Mock GetRenderDiscount返回金额为0的折扣数据
		patchGetRenderDiscount := mockey.Mock((*MockFeeDescEngineInputProvider).GetRenderDiscount).Return([]*PriceApi.RenderDiscount{
			{Amount: 0},
		}).Build()
		defer patchGetRenderDiscount.UnPatch()

		// 执行测试函数
		result := getDiscountInfo(ctx, mockProv)

		// 断言结果
		assert.NotNil(t, result, "返回结果不应为nil")
		assert.Empty(t, result, "折扣金额为0时不应添加到结果中")
	})
}

// MockFeeDescEngineInputProvider 实现model.FeeDescEngineInputProvider接口用于测试
type MockFeeDescEngineInputProvider struct {
	model.FeeDescEngineInputProvider
}

func (m *MockFeeDescEngineInputProvider) GetRenderDiscount() []*PriceApi.RenderDiscount {
	return []*PriceApi.RenderDiscount{
		{Amount: 1000},
	}
}

func (m *MockFeeDescEngineInputProvider) IsBusinessPay() bool {
	return false
} // 判断是否是企业付

func (m *MockFeeDescEngineInputProvider) GetMemberDpaInfo() *GoMember.PrivInfo {
	return nil
}

func (m *MockFeeDescEngineInputProvider) GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	return nil
} // Discount 券
func (m *MockFeeDescEngineInputProvider) GetBonusAmount() float64 {
	return 0
} // Discount 打车金
func (m *MockFeeDescEngineInputProvider) GetHkBonus() *PriceApi.EstimateNewFormBonusInfo {
	return nil
} // Discount 香港打车金
func (m *MockFeeDescEngineInputProvider) GetVCard() *PriceApi.EstimateNewFormVCardInfo {
	return nil
} // Discount 省钱卡
func (m *MockFeeDescEngineInputProvider) GetRevolvingAccountDiscount() *PriceApi.RevolvingAccountDiscountInfo {
	return nil
} // Discount 泛快内循环折扣
func (m *MockFeeDescEngineInputProvider) GetRevolvingAccountRebate() *PriceApi.RevolvingAccountRebateInfo {
	return nil
} // Discount 泛快内循环返利
func (m *MockFeeDescEngineInputProvider) GetSpecZiYouBao() *PriceApi.EstimateNewFormSpecZiYoubao {
	return nil
} // Discount 自由宝
func (m *MockFeeDescEngineInputProvider) GetRight() *PriceApi.EstimateNewFormRightInfo {
	return nil
} // Discount 权益
func (m *MockFeeDescEngineInputProvider) GetCard() *PriceApi.EstimateNewFormCardInfo {
	return nil
} // Discount 畅行卡
func (m *MockFeeDescEngineInputProvider) GetBusCard() *PriceApi.NewFormBusCard {
	return nil
} // Discount 大车出行卡
func (m *MockFeeDescEngineInputProvider) GetMemberDiscountCard() *PriceApi.DiscountCard {
	return nil
} // Discount 折上折
func (m *MockFeeDescEngineInputProvider) GetAlipayCoupon() *PriceApi.EstimateNewFormCouponInfo {
	return nil
} // Discount 支付宝支付券
func (m *MockFeeDescEngineInputProvider) GetTCDiscountFee() float64 {
	return 0
}
func (m *MockFeeDescEngineInputProvider) GetCarpoolFailedCoupon() *PriceApi.EstimateNewFormCouponInfo {
	return nil
} // 未拼成券
func (m *MockFeeDescEngineInputProvider) GetExtendList() []*PriceApi.EstimateNewFormExtend {
	return nil
}

func (m *MockFeeDescEngineInputProvider) GetBillFeeDetailInfo() map[string]float64 {
	return nil
}

func (m *MockFeeDescEngineInputProvider) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return nil
}

func (m *MockFeeDescEngineInputProvider) GetCashBack() *PriceApi.CashbackXinZhuInfo {
	return nil
}

// mockFeeDescEngineInputProvider 实现 FeeDescEngineInputProvider 接口
type mockFeeDescEngineInputProvider struct {
	memberDiscountCard *PriceApi.DiscountCard
	bonusAmount        float64
	right              *PriceApi.EstimateNewFormRightInfo
	card               *PriceApi.EstimateNewFormCardInfo
	alipayCoupon       *PriceApi.EstimateNewFormCouponInfo
	revolvingRebate    *PriceApi.RevolvingAccountRebateInfo
	revolvingDiscount  *PriceApi.RevolvingAccountDiscountInfo
	busCard            *PriceApi.NewFormBusCard
	cashBack           *PriceApi.CashbackXinZhuInfo
	bill               *PriceApi.EstimateNewFormBillInfo
}

func (m *mockFeeDescEngineInputProvider) IsHitDynamicIconAb(ctx context.Context) bool {
	//TODO implement me
	return false
}

func (m *mockFeeDescEngineInputProvider) GetMemberDiscountCard() *PriceApi.DiscountCard {
	return m.memberDiscountCard
}

func (m *mockFeeDescEngineInputProvider) GetBonusAmount() float64 {
	return m.bonusAmount
}

func (m *mockFeeDescEngineInputProvider) GetRight() *PriceApi.EstimateNewFormRightInfo {
	return m.right
}

func (m *mockFeeDescEngineInputProvider) GetCard() *PriceApi.EstimateNewFormCardInfo {
	return m.card
}

func (m *mockFeeDescEngineInputProvider) GetAlipayCoupon() *PriceApi.EstimateNewFormCouponInfo {
	return m.alipayCoupon
}

func (m *mockFeeDescEngineInputProvider) GetRevolvingAccountRebate() *PriceApi.RevolvingAccountRebateInfo {
	return m.revolvingRebate
}

func (m *mockFeeDescEngineInputProvider) GetRevolvingAccountDiscount() *PriceApi.RevolvingAccountDiscountInfo {
	return m.revolvingDiscount
}

func (m *mockFeeDescEngineInputProvider) GetBusCard() *PriceApi.NewFormBusCard {
	return m.busCard
}

func (m *mockFeeDescEngineInputProvider) GetCashBack() *PriceApi.CashbackXinZhuInfo {
	return m.cashBack
}

// 实现其他必要的接口方法
func (m *mockFeeDescEngineInputProvider) GetProductId() int64                      { return 0 }
func (m *mockFeeDescEngineInputProvider) GetBusinessID() int64                     { return 0 }
func (m *mockFeeDescEngineInputProvider) GetCarpoolType() int64                    { return 0 }
func (m *mockFeeDescEngineInputProvider) GetComboType() int64                      { return 0 }
func (m *mockFeeDescEngineInputProvider) GetRequireLevel() string                  { return "" }
func (m *mockFeeDescEngineInputProvider) GetCarpoolPriceType() int32               { return 0 }
func (m *mockFeeDescEngineInputProvider) GetSubGroupId() int32                     { return 0 }
func (m *mockFeeDescEngineInputProvider) IsDualCarpoolPrice() bool                 { return false }
func (m *mockFeeDescEngineInputProvider) IsSpecialPrice() bool                     { return false }
func (m *mockFeeDescEngineInputProvider) GetOrderType() int16                      { return 0 }
func (m *mockFeeDescEngineInputProvider) GetCountPriceType() int32                 { return 0 }
func (m *mockFeeDescEngineInputProvider) GetLevelType() int32                      { return 0 }
func (m *mockFeeDescEngineInputProvider) GetCityID() int                           { return 0 }
func (m *mockFeeDescEngineInputProvider) GetUserPID() int64                        { return 0 }
func (m *mockFeeDescEngineInputProvider) GetBillFeeDetailInfo() map[string]float64 { return nil }
func (m *mockFeeDescEngineInputProvider) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return m.bill
}
func (m *mockFeeDescEngineInputProvider) GetBillDriverMetre() int64                  { return 0 }
func (m *mockFeeDescEngineInputProvider) GetBillInfoCurrency() string                { return "" }
func (m *mockFeeDescEngineInputProvider) GetEstimateFee() float64                    { return 0 }
func (m *mockFeeDescEngineInputProvider) GetCarpoolFailEstimateFee() (float64, bool) { return 0, false }
func (m *mockFeeDescEngineInputProvider) GetCapPrice() float64                       { return 0 }
func (m *mockFeeDescEngineInputProvider) GetMemberDynamicProtectFee() float64        { return 0 }
func (m *mockFeeDescEngineInputProvider) GetDisplayLines() map[string]*PriceApi.DisplayLine {
	return nil
}
func (m *mockFeeDescEngineInputProvider) GetPersonalEstimateFee() float64     { return 0 }
func (m *mockFeeDescEngineInputProvider) GetDynamicTotalFee() float64         { return 0 }
func (m *mockFeeDescEngineInputProvider) GetTotalFeeWithoutDiscount() float64 { return 0 }
func (m *mockFeeDescEngineInputProvider) GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	return nil
}
func (m *mockFeeDescEngineInputProvider) GetHkBonus() *PriceApi.EstimateNewFormBonusInfo { return nil }
func (m *mockFeeDescEngineInputProvider) GetVCard() *PriceApi.EstimateNewFormVCardInfo   { return nil }
func (m *mockFeeDescEngineInputProvider) GetSpecZiYouBao() *PriceApi.EstimateNewFormSpecZiYoubao {
	return nil
}
func (m *mockFeeDescEngineInputProvider) GetTCDiscountFee() float64                     { return 0 }
func (m *mockFeeDescEngineInputProvider) GetRenderDiscount() []*PriceApi.RenderDiscount { return nil }
func (m *mockFeeDescEngineInputProvider) GetCarpoolFailedCoupon() *PriceApi.EstimateNewFormCouponInfo {
	return nil
}
func (m *mockFeeDescEngineInputProvider) GetExtendList() []*PriceApi.EstimateNewFormExtend {
	return nil
}
func (m *mockFeeDescEngineInputProvider) GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo {
	return nil
}
func (m *mockFeeDescEngineInputProvider) IsBusinessPay() bool                    { return false }
func (m *mockFeeDescEngineInputProvider) GetMemberDpaInfo() *GoMember.PrivInfo   { return nil }
func (m *mockFeeDescEngineInputProvider) GetLevelID() int32                      { return 0 }
func (m *mockFeeDescEngineInputProvider) GetTaxiSps() *hestia_charge.TaxiSpsData { return nil }
func (m *mockFeeDescEngineInputProvider) GetPricingByMeterPrice() float64        { return 0 }
func (m *mockFeeDescEngineInputProvider) GetApolloParams(f func(full *biz_runtime.ProductInfoFull) string, f2 ...func(full *biz_runtime.ProductInfoFull) (string, string)) (string, map[string]string) {
	return "", nil
}
func (m *mockFeeDescEngineInputProvider) IsCarpoolV3Merge(ctx context.Context) bool   { return false }
func (m *mockFeeDescEngineInputProvider) IsHaveCarpoolVCard(ctx context.Context) bool { return false }
func (m *mockFeeDescEngineInputProvider) GetSceneEstimatePrice() []biz_runtime.SceneEstimateFeeViewer {
	return nil
}
func (m *mockFeeDescEngineInputProvider) GetExactSeatNum() int32 { return 0 }
func (m *mockFeeDescEngineInputProvider) GetComboID() int64      { return 0 }
func (m *mockFeeDescEngineInputProvider) IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool {
	return false
}
func (m *mockFeeDescEngineInputProvider) GetSceneDataBySceneMark(reqSceneMark map[string]string) *biz_runtime.SceneEstimateFee {
	return nil
}
func (m *mockFeeDescEngineInputProvider) GetBonus() float64                       { return 0 }
func (m *mockFeeDescEngineInputProvider) GetFastEstimatePrice() float64           { return 0 }
func (m *mockFeeDescEngineInputProvider) GetDiscountCard() *PriceApi.DiscountCard { return nil }
func (m *mockFeeDescEngineInputProvider) GetExactEstimateFee() float64            { return 0 }
func (m *mockFeeDescEngineInputProvider) GetCarpoolFailExactEstimateFee() float64 { return 0 }
func (m *mockFeeDescEngineInputProvider) GetMixedDeductPrice() float64            { return 0 }
func (m *mockFeeDescEngineInputProvider) GetFastCarPrice() float64                { return 0 }
func (m *mockFeeDescEngineInputProvider) GetOpenCitySourceId() int32              { return 0 }
func (m *mockFeeDescEngineInputProvider) GetCurrentSeatNum() int32                { return 0 }
func (m *mockFeeDescEngineInputProvider) GetDirectEstimatePrice() biz_runtime.SceneEstimateFeeViewer {
	return nil
}
func (m *mockFeeDescEngineInputProvider) GetCarpoolFailRawBill() *price_api.BillInfoV3 { return nil }
func (m *mockFeeDescEngineInputProvider) GetCarpoolFailCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	return nil
}
func (m *mockFeeDescEngineInputProvider) GetBaseReqData() *models.BaseReqData { return nil }
func (m *mockFeeDescEngineInputProvider) GetAppVersion() string               { return "" }
func (m *mockFeeDescEngineInputProvider) GetLang() string                     { return "" }
func (m *mockFeeDescEngineInputProvider) GetProductCategory() int64           { return 0 }
func (m *mockFeeDescEngineInputProvider) ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (string, map[string]string) {
	return "", nil
}
func (m *mockFeeDescEngineInputProvider) GetAccessKeyId() int32 { return 0 }

func TestGetDiscountInfo(t *testing.T) {
	tests := []struct {
		name     string
		provider *mockFeeDescEngineInputProvider
		want     map[string]float64
	}{
		{
			name: "会员折扣卡",
			provider: &mockFeeDescEngineInputProvider{
				memberDiscountCard: &PriceApi.DiscountCard{
					Amount: 1000, // 10元
				},
			},
			want: map[string]float64{
				"member_discount_card": 10.0,
			},
		},
		{
			name: "奖金",
			provider: &mockFeeDescEngineInputProvider{
				bonusAmount: 5.0,
			},
			want: map[string]float64{
				"bonus": 5.0,
			},
		},
		{
			name: "权益",
			provider: &mockFeeDescEngineInputProvider{
				right: &PriceApi.EstimateNewFormRightInfo{
					Amount: "2000", // 20元
				},
			},
			want: map[string]float64{
				"right": 20.0,
			},
		},
		{
			name: "卡券",
			provider: &mockFeeDescEngineInputProvider{
				card: &PriceApi.EstimateNewFormCardInfo{
					Amount: "3000", // 30元
				},
			},
			want: map[string]float64{
				"card": 30.0,
			},
		},
		{
			name: "支付宝券",
			provider: &mockFeeDescEngineInputProvider{
				alipayCoupon: &PriceApi.EstimateNewFormCouponInfo{
					Amount: "4000", // 40元
				},
			},
			want: map[string]float64{
				"alipay_coupon": 40.0,
			},
		},
		{
			name: "循环账户返利",
			provider: &mockFeeDescEngineInputProvider{
				revolvingRebate: &PriceApi.RevolvingAccountRebateInfo{
					Amount: "5000", // 50元
				},
			},
			want: map[string]float64{
				"revolving_account_rebate": 50.0,
			},
		},
		{
			name: "循环账户折扣",
			provider: &mockFeeDescEngineInputProvider{
				revolvingDiscount: &PriceApi.RevolvingAccountDiscountInfo{
					Amount: "6000", // 60元
				},
			},
			want: map[string]float64{
				"revolving_account_discount": 60.0,
			},
		},
		{
			name: "公交卡",
			provider: &mockFeeDescEngineInputProvider{
				busCard: &PriceApi.NewFormBusCard{
					Amount: 7000, // 70元
				},
			},
			want: map[string]float64{
				"bus_card": 70.0,
			},
		},
		{
			name: "新竹返现",
			provider: &mockFeeDescEngineInputProvider{
				cashBack: &PriceApi.CashbackXinZhuInfo{
					CashbackAmount: 80.0, // 80元
				},
			},
			want: map[string]float64{
				"cashback_xin_zhu": 80.0,
			},
		},
		{
			name: "所有折扣项",
			provider: &mockFeeDescEngineInputProvider{
				memberDiscountCard: &PriceApi.DiscountCard{
					Amount: 1000, // 10元
				},
				bonusAmount: 5.0,
				right: &PriceApi.EstimateNewFormRightInfo{
					Amount: "2000", // 20元
				},
				card: &PriceApi.EstimateNewFormCardInfo{
					Amount: "3000", // 30元
				},
				alipayCoupon: &PriceApi.EstimateNewFormCouponInfo{
					Amount: "4000", // 40元
				},
				revolvingRebate: &PriceApi.RevolvingAccountRebateInfo{
					Amount: "5000", // 50元
				},
				revolvingDiscount: &PriceApi.RevolvingAccountDiscountInfo{
					Amount: "6000", // 60元
				},
				busCard: &PriceApi.NewFormBusCard{
					Amount: 7000, // 70元
				},
				cashBack: &PriceApi.CashbackXinZhuInfo{
					CashbackAmount: 80.0, // 80元
				},
			},
			want: map[string]float64{
				"member_discount_card":       10.0,
				"bonus":                      5.0,
				"right":                      20.0,
				"card":                       30.0,
				"alipay_coupon":              40.0,
				"revolving_account_rebate":   50.0,
				"revolving_account_discount": 60.0,
				"bus_card":                   70.0,
				"cashback_xin_zhu":           80.0,
			},
		},
		{
			name:     "无折扣项",
			provider: &mockFeeDescEngineInputProvider{},
			want:     map[string]float64{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getDiscountInfo(context.Background(), tt.provider)
			if len(got) != len(tt.want) {
				t.Errorf("getDiscountInfo() = %v, want %v", got, tt.want)
				return
			}

			for k, v := range tt.want {
				if got[k] != v {
					t.Errorf("getDiscountInfo()[%s] = %v, want %v", k, got[k], v)
				}
			}
		})
	}
}

func Test_getHkFeeExtraInfo(t *testing.T) {
	// case1: billDetail为nil
	prov1 := &mockFeeDescEngineInputProvider{}
	ret1 := getHkFeeExtraInfo(prov1)
	assert.Empty(t, ret1)

	// case2: billDetail.HistoryExtraMap为nil
	prov2 := &mockFeeDescEngineInputProvider{bill: &PriceApi.EstimateNewFormBillInfo{HistoryExtraMap: nil}}
	ret2 := getHkFeeExtraInfo(prov2)
	assert.Empty(t, ret2)

	// case3: billDetail.HistoryExtraMap不是map[string]interface{}
	prov3 := &mockFeeDescEngineInputProvider{bill: &PriceApi.EstimateNewFormBillInfo{HistoryExtraMap: map[string]float64{}}}
	ret3 := getHkFeeExtraInfo(prov3)
	assert.Empty(t, ret3)

	// case4: extraMap[consts.FeeKeyTunnelFeeDetail]为nil
	prov4 := &mockFeeDescEngineInputProvider{bill: &PriceApi.EstimateNewFormBillInfo{HistoryExtraMap: map[string]interface{}{}}}
	ret4 := getHkFeeExtraInfo(prov4)
	assert.Empty(t, ret4)

	// case5: tunnelFees不是map[string]interface{}
	prov5 := &mockFeeDescEngineInputProvider{bill: &PriceApi.EstimateNewFormBillInfo{HistoryExtraMap: map[string]interface{}{consts.FeeKeyTunnelFeeDetail: 123}}}
	ret5 := getHkFeeExtraInfo(prov5)
	assert.Empty(t, ret5)

	// case6: tunnelFees为空
	prov6 := &mockFeeDescEngineInputProvider{bill: &PriceApi.EstimateNewFormBillInfo{
		HistoryExtraMap: map[string]interface{}{
			consts.FeeKeyTunnelFeeDetail: map[string]interface{}{},
		},
	}}
	ret6 := getHkFeeExtraInfo(prov6)
	assert.Empty(t, ret6)

	// case7: tunnelFeeTotal为0
	prov7 := &mockFeeDescEngineInputProvider{bill: &PriceApi.EstimateNewFormBillInfo{
		HistoryExtraMap: map[string]interface{}{
			consts.FeeKeyTunnelFeeDetail: map[string]interface{}{
				"a": 0.0,
				"b": 0.0,
			},
		},
	}}
	ret7 := getHkFeeExtraInfo(prov7)
	assert.Empty(t, ret7)

	// case8: tunnelFeeTotal>0
	prov8 := &mockFeeDescEngineInputProvider{bill: &PriceApi.EstimateNewFormBillInfo{
		HistoryExtraMap: map[string]interface{}{
			consts.FeeKeyTunnelFeeDetail: map[string]interface{}{
				"a": 1.5,
				"b": 2.5,
			},
		},
	}}
	ret8 := getHkFeeExtraInfo(prov8)
	assert.Equal(t, 4.0, ret8[consts.FeeHkTunnelFee])
}

type MockProvider struct {
	mockFeeDescEngineInputProvider
}

func (m *MockProvider) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return &PriceApi.EstimateNewFormBillInfo{
		HistoryExtraMap: map[string]any{
			"surprise_deal_privilege_discount_amount": "100",
		},
	}
}

type MockProvider1 struct {
	mockFeeDescEngineInputProvider
}

func (m *MockProvider1) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return &PriceApi.EstimateNewFormBillInfo{
		HistoryExtraMap: "1",
	}
}

type MockProvider2 struct {
	mockFeeDescEngineInputProvider
}

func (m *MockProvider2) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return &PriceApi.EstimateNewFormBillInfo{
		HistoryExtraMap: map[string]any{
			"surprise_deal_privilege_discount_amount": 1,
		},
	}
}

type MockProvider3 struct {
	mockFeeDescEngineInputProvider
}

func (m *MockProvider3) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return &PriceApi.EstimateNewFormBillInfo{
		HistoryExtraMap: map[string]any{
			"surprise_deal_privilege_discount_amount": "0",
		},
	}
}

type MockProvider4 struct {
	mockFeeDescEngineInputProvider
}

func (m *MockProvider4) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return &PriceApi.EstimateNewFormBillInfo{
		HistoryExtraMap: map[string]any{
			"surprise_deal_privilege": "0",
		},
	}
}

func Test_BuildSpecialRate(t *testing.T) {
	mockey.PatchConvey("Test1", t, func() {
		prov := &MockFeeDescEngineInputProvider{}
		mockey.Mock(BuildNormalFeeInput).Return(&model.FeeInput{
			DiscountInfo: make(map[string]float64),
		}).Build()
		BuildSpecialRate(context.Background(), prov, 1)
	})
	mockey.PatchConvey("Test2", t, func() {
		prov := &MockProvider{}
		mockey.Mock(BuildNormalFeeInput).Return(&model.FeeInput{
			DiscountInfo: make(map[string]float64),
		}).Build()
		BuildSpecialRate(context.Background(), prov, 1)
	})
	mockey.PatchConvey("Test3", t, func() {
		prov := &MockProvider1{}
		mockey.Mock(BuildNormalFeeInput).Return(&model.FeeInput{
			DiscountInfo: make(map[string]float64),
		}).Build()
		BuildSpecialRate(context.Background(), prov, 1)
	})
	mockey.PatchConvey("Test4", t, func() {
		prov := &MockProvider2{}
		mockey.Mock(BuildNormalFeeInput).Return(&model.FeeInput{
			DiscountInfo: make(map[string]float64),
		}).Build()
		BuildSpecialRate(context.Background(), prov, 1)
	})
	mockey.PatchConvey("Test5", t, func() {
		prov := &MockProvider3{}
		mockey.Mock(BuildNormalFeeInput).Return(&model.FeeInput{
			DiscountInfo: make(map[string]float64),
		}).Build()
		BuildSpecialRate(context.Background(), prov, 1)
	})
	mockey.PatchConvey("Test5", t, func() {
		prov := &MockProvider4{}
		mockey.Mock(BuildNormalFeeInput).Return(&model.FeeInput{
			DiscountInfo: make(map[string]float64),
		}).Build()
		BuildSpecialRate(context.Background(), prov, 1)
	})
}
