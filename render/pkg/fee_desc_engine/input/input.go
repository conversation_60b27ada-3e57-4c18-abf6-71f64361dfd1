package input

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/require_level"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input/filter_callback"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input/render_callback"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input/taxi_input"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/spacious_car/spacious_common"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

func BuildNormalFeeInput(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	feeInput := &model.FeeInput{
		BusinessPayDeduction: getBusinessPay(prov),
		DynamicProtect:       getMemberDynamicProtectFee(prov),
		CouponInfo:           getCouponInfo(ctx, prov),
		Ziyoubao:             getZiYouBao(ctx, prov),
		DiscountInfo:         getDiscountInfo(ctx, prov),
		FeeDetailInfo:        prov.GetBillFeeDetailInfo(),
		TCDiscountFee:        getTripCloudDiscount(prov),
		CheaperThanFastCar:   getCheaperThanFastCar(ctx, prov),

		RenderCallback: getRenderCallback(ctx, prov, formType),
	}
	return feeInput
}

func BuildNormalFeeInput4MainForm(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	feeInput := BuildNormalFeeInput(ctx, prov, formType)
	buildInputExtra4MainForm(ctx, feeInput, prov, formType)
	return feeInput
}

func BuildTaxiFeeInput(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	ret := &model.FeeInput{
		CouponInfo:    getCouponInfo(ctx, prov),
		DiscountInfo:  getDiscountInfo(ctx, prov),
		FeeDetailInfo: prov.GetBillFeeDetailInfo(),
		NeutralInfo:   getNeutralInfo(ctx, prov),

		FilterCallback: getFilterCallback(ctx, prov, formType),
		RenderCallback: getRenderCallback(ctx, prov, formType),
	}

	// if _, ok := ret.FeeDetailInfo["dynamic_price"]; ok {
	// 	delete(ret.FeeDetailInfo, "dynamic_price")
	// }

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdUnioneSpecialPrice {
		ret.TaxiSpecial = getTaxiSpecial(ctx, prov)
	}

	return ret
}

func BuildTaxiFeeInput4MainForm(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	ret := BuildTaxiFeeInput(ctx, prov, formType)
	buildTaxiExtra4MainForm(ctx, prov, ret, formType)
	return ret
}

func BuildSpaciousCarFeeInput4MainForm(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	apolloKey, apolloParam := prov.ApolloParamsGen(apollo_model.WithPIDKey)
	if !spacious_common.HitSingle(ctx, apolloKey, apolloParam) {
		return nil
	}
	return &model.FeeInput{
		DynamicProtect: getMemberDynamicProtectFee(prov),
		CouponInfo:     getCouponInfo(ctx, prov),
		DiscountInfo:   getDiscountInfo(ctx, prov),

		RenderCallback: getRenderCallback4MainForm(ctx, prov, formType),
	}
}

func BuildSFCFeeInput(ctx context.Context, prov model.FeeDescEngineInputProvider) *model.FeeInput {
	return &model.FeeInput{
		DiscountInfo: getDiscountInfo4SFC(ctx, prov),
	}
}

// BuildSpecialRate 惊喜特价
func BuildSpecialRate(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	feeInput := BuildNormalFeeInput(ctx, prov, formType)
	buildInputExtra4MainForm(ctx, feeInput, prov, formType)
	if prov.GetBillDetail() == nil || prov.GetBillDetail().HistoryExtraMap == nil {
		return feeInput
	}
	extraMap := prov.GetBillDetail().HistoryExtraMap
	extraMapReal, ok := extraMap.(map[string]any)
	if !ok {
		return feeInput
	}
	discount, ok := extraMapReal["surprise_deal_privilege_discount_amount"]
	if !ok {
		return feeInput
	}
	discountAmountStr, ok := discount.(string)
	if !ok {
		return feeInput
	}
	discountAmount, err := strconv.ParseInt(discountAmountStr, 10, 64)
	if err != nil {
		return feeInput
	}
	amount := float64(discountAmount) / 100.0
	if amount <= 0.0 {
		return feeInput
	}
	amount = decimal.NewFromFloat(amount).RoundFloor(1).InexactFloat64() // 保留一位小数
	feeInput.DiscountInfo["surprise_privilege_discount"] = amount
	return feeInput
}

func buildInputExtra4MainForm(ctx context.Context, input *model.FeeInput, prov model.FeeDescEngineInputProvider, formType int) {
	if consts.DefaultForm != formType {
		return
	}
	input.CheaperThanFastCar = nil
	input.RenderCallback = getRenderCallback4MainForm(ctx, prov, formType)
	if prov.GetBillDetail() != nil {
		if prov.GetBillDetail().CrossCityFee > 0 {
			input.FeeDetailInfo[consts.FeeCrossCity] = prov.GetBillDetail().CrossCityFee
		}
		if prov.GetBillDetail().InfoFee > 0 {
			input.FeeDetailInfo[consts.FeeInfoFee] = prov.GetBillDetail().InfoFee
		}
	}
}

func buildTaxiExtra4MainForm(ctx context.Context, prov model.FeeDescEngineInputProvider, input *model.FeeInput, formType int) {
	if consts.DefaultForm != formType {
		return
	}
	// 中性项比打表省并入减价项，删除在线计价标签
	if input.NeutralInfo != nil {
		if input.NeutralInfo[consts.FeeComparePricingByMeter] != 0 {
			input.DiscountInfo[consts.FeeComparePricingByMeter] = input.NeutralInfo[consts.FeeComparePricingByMeter]
		}
		input.NeutralInfo = nil
	}
	ra := &model.RenderAdapter{FeeDescEngineInputProvider: prov}
	isHoliday := ra.IsHoliday() == render_callback.GeneralHoliday
	buildTaxiInc4MainForm(input, isHoliday)
	buildTaxiLimitTime(ctx, prov, input, formType)
	input.RenderCallback = getRenderCallback4MainForm(ctx, prov, formType)
}

func buildTaxiInc4MainForm(input *model.FeeInput, isHoliday bool) {
	holiday := input.FeeDetailInfo["taxi_holiday_price"]
	peak := input.FeeDetailInfo["taxi_peak_price"]
	cross := input.FeeDetailInfo["cross_city_fee"]
	red := input.FeeDetailInfo["red_packet"]
	feeDetailInfo := make(map[string]float64, 4)
	if holiday > 0 {
		feeDetailInfo["taxi_holiday_price"] = holiday
	}
	if peak > 0 {
		if isHoliday {
			feeDetailInfo["taxi_peak_price_holiday"] = peak
		} else {
			feeDetailInfo["taxi_peak_price"] = peak
		}
	}
	if cross > 0 {
		feeDetailInfo["taxi_cross_city_fee"] = cross
	}
	if red > 0 {
		feeDetailInfo["taxi_red_packet"] = red
	}
	input.FeeDetailInfo = feeDetailInfo
}

// 出租车愿等, 满足下面条件的目前只有39 超值出租车
func buildTaxiLimitTime(ctx context.Context, prov model.FeeDescEngineInputProvider, input *model.FeeInput, formType int) {
	if consts.DefaultForm == formType && product_id.ProductIdUnitaxi == prov.GetProductId() &&
		cast.ToString(require_level.RequireLevelUniTaxi) == prov.GetRequireLevel() && 0 == prov.GetComboType() && prov.IsSpecialPrice() {
		limitTimeAmount := prov.GetBillDetail().TaxiSpDiscountFee
		if limitTimeAmount > 0 {
			input.DiscountInfo[consts.FeeTimeLimit] = limitTimeAmount
		}
	}
}

func getDiscountInfo4SFC(ctx context.Context, prov model.FeeDescEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)
	extendList := prov.GetExtendList()
	if extendList != nil && len(extendList) > 0 {
		discount := extendList[0].DiscountSet
		if discount != nil && discount.Coupon != nil {
			if amount := util.String2float64(ctx, discount.Coupon.Amount); amount > 0 {
				ret[consts.FeeNormalDiscount] = amount / 100
			}
		}
	}
	return ret
}

/* 本次不会用到，出租车 && 出租车拼车在page_type=45 会有此减价项
func getLanKeBaoAmount(ctx context.Context, prov model.FeeDescEngineInputProvider, pageType int32) float64 {
	if page_type.PageTypeLankeBao != pageType {
		return 0
	}
	if feeDetailInfo := prov.GetBillFeeDetailInfo(); feeDetailInfo != nil {
		lanKeBaoAmount := math.Abs(feeDetailInfo["sps_single_discount"] * 100)
		if lanKeBaoAmount > 0 {
			return lanKeBaoAmount
		}
	}
	return 0
}
*/

func getNeutralInfo(ctx context.Context, prov model.FeeDescEngineInputProvider) map[string]float64 {
	neutralInfo := make(map[string]float64)

	pricingByOnlineAmount, ok := taxi_input.BuildTaxiPricingByOnline(ctx, prov)
	if ok {
		neutralInfo[consts.FeeTaxiPricingByOnline] = decimal.NewFromFloat(pricingByOnlineAmount).Round(2).InexactFloat64()
	}

	amount, ok := taxi_input.BuildComparePricingByMeter(ctx, prov)
	if ok && amount > 0 {
		neutralInfo[consts.FeeComparePricingByMeter] = decimal.NewFromFloat(amount).Round(2).InexactFloat64()
	}

	return neutralInfo
}

func BuildInterCityInput(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	ret := &model.FeeInput{
		BusinessPayDeduction: getBusinessPay(prov),
		CouponInfo:           getCouponInfo(ctx, prov),
		DiscountInfo:         getDiscountInfo(ctx, prov),
		FeeDetailInfo:        prov.GetBillFeeDetailInfo(),

		RenderCallback: getRenderCallback(ctx, prov, formType),
	}

	return ret
}

func BuildInterCityCommonEstimateInput(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	ret := &model.FeeInput{
		CouponInfo:    getCouponInfo(ctx, prov),
		DiscountInfo:  getDiscountInfo(ctx, prov),
		FeeDetailInfo: prov.GetBillFeeDetailInfo(),

		RenderCallback: getRenderCallback4MainForm(ctx, prov, formType),
	}

	if _, ok := ret.FeeDetailInfo["dynamic_price"]; ok {
		delete(ret.FeeDetailInfo, "dynamic_price")
	}

	return ret
}

func BuildHkProductFeeInput(ctx context.Context, prov model.FeeDescEngineInputProvider) *model.FeeInput {
	return &model.FeeInput{
		DiscountInfo:  getHkDiscountInfo(ctx, prov),
		FeeDetailInfo: getHkFeeDetailInfo(prov),
		FeeExtraInfo:  getHkFeeExtraInfo(prov),
	}
}

func BuildBargainFeeInput(ctx context.Context, prov model.FeeDescEngineInputProvider) *model.FeeInput {
	return &model.FeeInput{
		DiscountInfo:  getBargainDiscountInfo(ctx, prov),
		FeeDetailInfo: prov.GetBillFeeDetailInfo(),
	}
}

func BuildSpaciousFeeInput(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	return &model.FeeInput{
		CouponInfo:     getCouponInfo(ctx, prov),
		DiscountInfo:   getSpaciousDiscountInfo(ctx, prov),
		DynamicProtect: getMemberDynamicProtectFee(prov),
		FeeDetailInfo:  prov.GetBillFeeDetailInfo(),

		RenderCallback: getRenderCallback(ctx, prov, formType),
	}
}
func BuildPetsTravelFeeInput(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) *model.FeeInput {
	return &model.FeeInput{
		CouponInfo:    getCouponInfo(ctx, prov),
		DiscountInfo:  getDiscountInfo(ctx, prov),
		FeeDetailInfo: prov.GetBillFeeDetailInfo(),
	}
}

func getBusinessPay(prov model.FeeDescEngineInputProvider) (ret *float64) {
	if prov.IsBusinessPay() {
		if payInfo := prov.GetPaymentInfo(); payInfo != nil && payInfo.MixedPayDeductInfo != nil && payInfo.MixedPayDeductInfo.DeductFee > 0 {
			return util.Float64Ptr(payInfo.MixedPayDeductInfo.DeductFee)
		} else if estimateFee := prov.GetEstimateFee(); estimateFee > 0 {
			return util.Float64Ptr(estimateFee)
		}
	}

	return
}

func getMemberDynamicProtectFee(prov model.FeeDescEngineInputProvider) (ret *float64) {
	dpa := prov.GetMemberDpaInfo()
	if dpa == nil {
		return nil
	}

	return util.Float64Ptr(prov.GetMemberDynamicProtectFee())
}

func getCouponInfo(ctx context.Context, prov model.FeeDescEngineInputProvider) *model.Coupon {
	if prov.GetCouponInfo() == nil {
		return nil
	}

	couponAmount, err := strconv.ParseFloat(prov.GetCouponInfo().Amount, 64)
	if err != nil || couponAmount <= 0 {
		return nil
	}

	coupon := &model.Coupon{
		CustomTag: prov.GetCouponInfo().CustomTag,
		Amount:    couponAmount / 100,
	}
	price.CheckSingle(ctx, "getCouponInfo", "CouponInfo", "Amount", coupon.Amount)

	return coupon
}

func getZiYouBao(ctx context.Context, prov model.FeeDescEngineInputProvider) *model.Ziyoubao {
	var (
		tmpAmount float64
		tmpMile   float64

		err error
	)

	billInfo := prov.GetSpecZiYouBao()
	if billInfo == nil {
		return nil
	}

	if tmpAmount, err = strconv.ParseFloat(billInfo.Amount, 64); err != nil || tmpAmount <= 0 {
		return nil
	}

	if tmpMile, err = strconv.ParseFloat(billInfo.Mile, 64); err != nil || tmpMile <= 0 {
		return nil
	}

	ziyoubao := &model.Ziyoubao{
		Mile:   tmpMile / 1000.0,
		Amount: tmpAmount / 100.0,
	}
	price.CheckSingle(ctx, "getZiYouBao", "ZiYouBao", "Amount", ziyoubao.Amount)
	return ziyoubao
}

func getCheaperThanFastCar(ctx context.Context, prov model.FeeDescEngineInputProvider) *model.CheaperThanFastCar {
	key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
	if !apollo.FeatureToggle(ctx, "cheaper_than_fast_car_controller", key, param) {
		return nil
	}

	fastCarPrice := prov.GetFastCarPrice()
	if fastCarPrice == 0 {
		return nil
	}

	diffPrice := fastCarPrice - prov.GetEstimateFee()

	if diffPrice < 3 {
		return nil
	}

	result := util.RoundAbs(diffPrice, 1)
	price.CheckSingle(ctx, "getCheaperThanFastCar", "CheaperThanFastCar", "diffPrice", result)

	return &model.CheaperThanFastCar{
		Amount: result,
	}
}

func getTripCloudDiscount(prov model.FeeDescEngineInputProvider) (ret *float64) {
	tcDiscount := prov.GetTCDiscountFee()
	return &tcDiscount
}

// 香港账单补充信息
func getHkFeeExtraInfo(prov model.FeeDescEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)
	billDetail := prov.GetBillDetail()
	if billDetail == nil || billDetail.HistoryExtraMap == nil {
		return ret
	}

	extraMap, ok := billDetail.HistoryExtraMap.(map[string]interface{})
	if !ok || len(extraMap) == 0 || extraMap[consts.FeeKeyTunnelFeeDetail] == nil {
		return ret
	}

	tunnelFees, ok := extraMap[consts.FeeKeyTunnelFeeDetail].(map[string]interface{})
	if !ok || len(tunnelFees) == 0 {
		return ret
	}

	var tunnelFeeTotal float64
	for _, fee := range tunnelFees {
		tunnelFeeTotal += cast.ToFloat64(fee)
	}
	if tunnelFeeTotal > 0 {
		ret[consts.FeeHkTunnelFee] = tunnelFeeTotal
	}

	return ret
}

func getHkDiscountInfo(ctx context.Context, prov model.FeeDescEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)

	// 香港负费项金额仅保留整数（向下取整）
	if HkBonus := prov.GetHkBonus(); HkBonus != nil {
		if amount := util.String2Int(ctx, HkBonus.Amount); amount >= 100 {
			ret[consts.FeeHkBonus] = float64(amount / 100)
		}
	}

	if HkCoupon := prov.GetCouponInfo(); HkCoupon != nil {
		if amount := util.String2Int(ctx, HkCoupon.Amount); amount >= 100 {
			ret[consts.FeeHkCoupon] = float64(amount / 100)
		}
	}

	return ret
}

// 香港获取feeDetail
func getHkFeeDetailInfo(prov model.FeeDescEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)
	billDetail := prov.GetBillDetail()
	if billDetail == nil {
		return ret
	}
	if billDetail.DynamicTimes > 0 {
		ret[consts.HkDynamicPrice] = billDetail.DynamicTimes
	}
	return ret
}

func getBargainDiscountInfo(ctx context.Context, prov model.FeeDescEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)

	if coupon := prov.GetCouponInfo(); coupon != nil {
		if amount := util.String2float64(ctx, coupon.Amount); amount > 0 {
			ret[consts.FeeCoupon] = amount / 100
		}
	}

	return ret
}

func getDiscountInfo(ctx context.Context, prov model.FeeDescEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)
	if memberDiscountCard := prov.GetMemberDiscountCard(); memberDiscountCard != nil && memberDiscountCard.Amount > 0 {
		ret[consts.FeeMemberDiscountCard] = float64(memberDiscountCard.Amount) / 100
	}

	if bonus := prov.GetBonusAmount(); bonus > 0 {
		ret[consts.FeeBonus] = bonus
	}

	if right := prov.GetRight(); right != nil && cast.ToFloat64(right.Amount) > 0 {
		if amount := util.String2float64(ctx, right.Amount); amount > 0 {
			ret[consts.FeeRight] = amount / 100
		}
	}

	if card := prov.GetCard(); card != nil {
		if amount := util.String2float64(ctx, card.Amount); amount > 0 {
			ret[consts.FeeCard] = amount / 100
		}
	}

	if alipayCoupon := prov.GetAlipayCoupon(); alipayCoupon != nil {
		if amount := util.String2float64(ctx, alipayCoupon.Amount); amount > 0 {
			ret[consts.FeeAlipayCoupon] = amount / 100
		}
	}

	if revolvingRebate := prov.GetRevolvingAccountRebate(); revolvingRebate != nil {
		if amount := util.String2float64(ctx, revolvingRebate.Amount); amount > 0 {
			ret[consts.FeeRevolvingRebate] = amount / 100
		}
	}

	if revolvingDiscount := prov.GetRevolvingAccountDiscount(); revolvingDiscount != nil {
		if amount := util.String2float64(ctx, revolvingDiscount.Amount); amount > 0 {
			ret[consts.FeeRevolvingDiscount] = amount / 100
		}
	}

	if busCard := prov.GetBusCard(); busCard != nil && busCard.Amount > 0 {
		ret[consts.FeeBusCard] = float64(busCard.Amount) / 100
	}

	if cashBack := prov.GetCashBack(); cashBack != nil && cashBack.CashbackAmount > 0 {
		ret[consts.FeeCashBackXinZhu] = cashBack.CashbackAmount
	}

	// 非网约车品类减项
	var sum float64
	for _, item := range prov.GetRenderDiscount() {
		sum += cast.ToFloat64(item.Amount)
	}
	if sum > 0 {
		ret[consts.FeeNormalDiscount] = sum / 100
	}

	return ret
}

func getSpaciousDiscountInfo(ctx context.Context, prov model.FeeDescEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)

	if bonus := prov.GetBonusAmount(); bonus > 0 {
		ret[consts.FeeBonus] = bonus
	}

	if busCard := prov.GetBusCard(); busCard != nil && busCard.Amount > 0 {
		ret[consts.FeeBusCard] = float64(busCard.Amount) / 100
	}

	if memberDiscountCard := prov.GetMemberDiscountCard(); memberDiscountCard != nil && memberDiscountCard.Amount > 0 {
		ret[consts.FeeMemberDiscountCard] = float64(memberDiscountCard.Amount) / 100
	}

	if alipayCoupon := prov.GetAlipayCoupon(); alipayCoupon != nil {
		alipayCouponAmount, err := strconv.ParseFloat(alipayCoupon.Amount, 64)
		if err == nil && alipayCouponAmount > 0 {
			ret[consts.FeeAlipayCoupon] = alipayCouponAmount / 100
		}
	}

	return ret
}

func getTaxiSpecial(ctx context.Context, prov model.FeeDescEngineInputProvider) (ret *float64) {
	billInfo := prov.GetBillDetail()
	if billInfo == nil {
		return nil
	}

	return util.ValidFloat64(cast.ToFloat64(util.FormatPrice(billInfo.PreTotalFee-billInfo.CapPrice, 2)))
}

func getRenderCallback(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) model.RenderCallback {

	ra := &model.RenderAdapter{FeeDescEngineInputProvider: prov}

	var (
		deductionMile float64
		customTag     string
		dynamicTimes  float64
	)
	if ziyoubao := prov.GetSpecZiYouBao(); ziyoubao != nil {
		deductionMile = util.String2float64(ctx, ziyoubao.Mile) / 1000.0
		price.CheckSingle(ctx, "getRenderCallback", "ziyoubao", "deductionMile", deductionMile)
	}

	if coupon := prov.GetCouponInfo(); coupon != nil {
		customTag = coupon.CustomTag
	}

	if bill := prov.GetBillDetail(); bill != nil {
		dynamicTimes = bill.DynamicTimes
	}

	ret := model.RenderCallback{}
	ret["default"] = render_callback.RenderCallbackDefault(ctx)
	if ra.GetProductId() == product_id.ProductIdUnitaxi {
		ret["dynamic_price"] = render_callback.RenderTaxiCallbackDynamicPrice(ctx, dynamicTimes, prov.IsHitDynamicIconAb(ctx))
	} else {
		ret["dynamic_price"] = render_callback.RenderCallbackDynamicPrice(ctx, dynamicTimes, prov.IsHitDynamicIconAb(ctx))
	}
	ret[consts.CheaperThanFastCar] = render_callback.RenderCallbackCheaperThanFast(ctx)
	ret[consts.FeeTaxiPeak] = render_callback.RenderCallbackTaxiPeakPrice(ctx, ra)
	ret["ziyoubao"] = render_callback.RenderCallbackZiyoubao(ctx, deductionMile)
	ret["coupon"] = render_callback.RenderCallbackCoupon(ctx, formType, customTag, int64(prov.GetLevelID()), nil)

	if revolvingRebate := prov.GetRevolvingAccountRebate(); revolvingRebate != nil {
		ret[consts.FeeRevolvingRebate] = render_callback.RenderCallbackRevolvingRebate(ctx, revolvingRebate.ActivityType, revolvingRebate.Multiple)
	}

	if memberDiscountCard := prov.GetMemberDiscountCard(); memberDiscountCard != nil && memberDiscountCard.Amount > 0 {
		ret[consts.FeeMemberDiscountCard] = render_callback.RenderCallbackMemberDiscountCard(ctx, int64(prov.GetLevelID()), memberDiscountCard.CustomTag, nil)
	}

	return ret
}

func getRenderCallback4MainForm(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) model.RenderCallback {
	var (
		customTag    string
		dynamicTimes float64
	)

	if coupon := prov.GetCouponInfo(); coupon != nil {
		customTag = coupon.CustomTag
	}
	if bill := prov.GetBillDetail(); bill != nil {
		dynamicTimes = bill.DynamicTimes
	}

	config := render_callback.NewStyleExpRenderFeeDescDCMPConfig()
	ret := model.RenderCallback{}
	ret["default"] = render_callback.RenderCallbackDefault(ctx)
	ret["dynamic_price"] = render_callback.RenderCallbackDynamicPrice(ctx, dynamicTimes, prov.IsHitDynamicIconAb(ctx))
	ret["coupon"] = render_callback.RenderCallbackCoupon(ctx, formType, customTag, int64(prov.GetLevelID()), config)

	if memberDiscountCard := prov.GetMemberDiscountCard(); memberDiscountCard != nil && memberDiscountCard.Amount > 0 {
		ret[consts.FeeMemberDiscountCard] = render_callback.RenderCallbackMemberDiscountCard(ctx, int64(prov.GetLevelID()), memberDiscountCard.CustomTag, config)
	}

	return ret
}

func getFilterCallback(ctx context.Context, prov model.FeeDescEngineInputProvider, formType int) model.FilterCallback {
	ret := model.FilterCallback{}
	ret["taxi_peak_price"] = filter_callback.FilterCallbackTaxiPeakPrice(ctx, prov, formType)
	ret["taxi_peak_price_holiday"] = filter_callback.FilterCallbackTaxiPeakPrice(ctx, prov, formType)

	return ret
}
