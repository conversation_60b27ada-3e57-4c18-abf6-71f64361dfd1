package input

import (
	"context"
	"math"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input/filter_callback"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input/quotation_input"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input/render_callback"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"github.com/spf13/cast"
)

func BuildNormalInputByQuotation(ctx context.Context, prov engine_model.FeeDescByQuotationEngineInputProvider, formType int) *engine_model.FeeInput {
	input := engine_model.NewFeeInput().
		SetBusinessPayByQuotation(ctx, prov).
		SetCouponInfoByQuotation(ctx, prov).
		SetDiscountInfoByQuotation(ctx, prov).
		SetZiYouBaoByQuotation(ctx, prov).
		SetFeeDetailInfoByQuotation(ctx, prov).
		SetTCDiscountFeeByQuotation(ctx, prov).
		SetTaxiSpecialByQuotation(ctx, prov)

	coupon := prov.GetDiscountDescByTypes(ctx, []string{engine_model.CouponDefault, engine_model.CouponStudent, engine_model.CouponMonth, engine_model.CouponPaidMember, engine_model.CouponCityCard})
	if coupon != nil {
		input.RegisterRenderCallBack(consts.FeeCoupon, render_callback.RenderCallbackCoupon(ctx, formType, coupon.CustomTag, prov.GetLevelID(), nil))
	}

	dynamicTimes := prov.GetDynamicTimes()
	if dynamicTimes > 0 {
		input.RegisterRenderCallBack(consts.DynamicDiffPrice, render_callback.RenderCallbackDynamicPrice(ctx, dynamicTimes, prov.IsHitDynamicIconAb(ctx)))
	}

	tcDiscount := prov.GetTCDiscountFee()
	if tcDiscount != 0 {
		input.RegisterRenderCallBack(consts.TCDiscountFee, render_callback.RenderCallbackDefault(ctx))
	}

	ziyoubao := prov.GetDiscountDescByTypes(ctx, []string{engine_model.ZiyoubaoType})
	if ziyoubao != nil {
		if mile := util.String2float64(ctx, ziyoubao.DeductionMile); mile > 0 {
			input.RegisterRenderCallBack(consts.FeeZiyoubao, render_callback.RenderCallbackZiyoubao(ctx, mile))
		}
	}

	revolvingRebate := prov.GetDiscountDescByTypes(ctx, []string{engine_model.RevolvingAccountRebate})
	if revolvingRebate != nil {
		activityType, typeOk := util.MapInterface2Int32(revolvingRebate.ExtraInfo, "type")
		multiple, multipleOk := util.MapInterface2Float64(revolvingRebate.ExtraInfo, "multiple")
		if typeOk && multipleOk {
			input.RegisterRenderCallBack(consts.FeeRevolvingRebate, render_callback.RenderCallbackRevolvingRebate(ctx, activityType, multiple))
		}
	}

	quotation_input.BuildMemberDiscountCard(ctx, input, prov, nil)

	return input
}

func BuildNormalInputByQuotation4NewStyleExp(ctx context.Context, prov engine_model.FeeDescByQuotationEngineInputProvider, formType int) *engine_model.FeeInput {
	input := engine_model.NewFeeInput().
		SetBusinessPayByQuotation(ctx, prov).
		SetCouponInfoByQuotation(ctx, prov).
		SetDiscountInfoByQuotation(ctx, prov).
		SetZiYouBaoByQuotation(ctx, prov).
		SetFeeDetailInfoByQuotation(ctx, prov).
		SetTCDiscountFeeByQuotation(ctx, prov).
		SetTaxiSpecialByQuotation(ctx, prov)

	config := render_callback.NewStyleExpRenderFeeDescDCMPConfig()
	coupon := prov.GetDiscountDescByTypes(ctx, []string{engine_model.CouponDefault, engine_model.CouponStudent, engine_model.CouponMonth, engine_model.CouponPaidMember, engine_model.CouponCityCard})
	if coupon != nil {
		input.RegisterRenderCallBack(consts.FeeCoupon, render_callback.RenderCallbackCoupon(ctx, formType, coupon.CustomTag, prov.GetLevelID(), config))
	}

	dynamicTimes := prov.GetDynamicTimes()
	if dynamicTimes > 0 {
		input.RegisterRenderCallBack(consts.DynamicDiffPrice, render_callback.RenderCallbackDynamicPrice(ctx, dynamicTimes, prov.IsHitDynamicIconAb(ctx)))
	}

	tcDiscount := prov.GetTCDiscountFee()
	if tcDiscount != 0 {
		input.RegisterRenderCallBack(consts.TCDiscountFee, render_callback.RenderCallbackDefault(ctx))
	}

	quotation_input.BuildMemberDiscountCard(ctx, input, prov, config)

	return input
}

func BuildTaxiInputByQuotation(ctx context.Context, prov engine_model.FeeDescByQuotationEngineInputProvider, formType int, config *render_callback.RenderFeeDescDCMPConfig) *engine_model.FeeInput {
	input := engine_model.NewFeeInput().
		SetCouponInfoByQuotation(ctx, prov).
		SetDiscountInfoByQuotation(ctx, prov).
		SetFeeDetailInfoByQuotation(ctx, prov).
		SetTaxiSpecialByQuotation(ctx, prov).
		SetFilterCallBack(engine_model.FilterCallback{
			consts.FeeTaxiPeak: filter_callback.FilterCallbackTaxiPeakPriceByQuotation(ctx, prov),
		})

	dynamicTimes := prov.GetDynamicTimes()
	if dynamicTimes > 0 {
		input.RegisterRenderCallBack(consts.DynamicDiffPrice, render_callback.RenderTaxiCallbackDynamicPrice(ctx, dynamicTimes, prov.IsHitDynamicIconAb(ctx)))
	}
	coupon := prov.GetDiscountDescByTypes(ctx, []string{engine_model.CouponDefault, engine_model.CouponStudent, engine_model.CouponMonth, engine_model.CouponPaidMember, engine_model.CouponCityCard})
	if coupon != nil {
		input.RegisterRenderCallBack(consts.FeeCoupon, render_callback.RenderCallbackCoupon(ctx, formType, coupon.CustomTag, prov.GetLevelID(), config))
	}

	return input
}

func BuildHkProductFeeInputByQuotation(ctx context.Context, prov engine_model.FeeDescByQuotationEngineInputProvider, formType int) *engine_model.FeeInput {
	return &engine_model.FeeInput{
		DiscountInfo:  getHkDiscountInfoByQuotation(ctx, prov),
		FeeDetailInfo: getHkFeeDetailInfoByQuotation(ctx, prov),
		FeeExtraInfo:  getHkFeeExtraInfoByQuotation(ctx, prov),
	}
}

// 香港账单补充信息
func getHkFeeExtraInfoByQuotation(ctx context.Context, prov engine_model.FeeDescByQuotationEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)

	tunnelFees := prov.GetTunnelFeeDetail()
	if len(tunnelFees) == 0 {
		return ret
	}

	var tunnelFeeTotal float64
	for _, fee := range tunnelFees {
		tunnelFeeTotal += cast.ToFloat64(fee)
	}
	if tunnelFeeTotal > 0 {
		ret[consts.FeeHkTunnelFee] = tunnelFeeTotal
	}

	return ret
}

func getHkDiscountInfoByQuotation(ctx context.Context, prov engine_model.FeeDescByQuotationEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)

	// 香港负费项金额仅保留整数（向下取整）
	coupon := prov.GetDiscountDescByTypes(ctx, []string{engine_model.CouponDefault, engine_model.CouponStudent, engine_model.CouponMonth, engine_model.CouponPaidMember, engine_model.CouponCityCard})
	if coupon != nil && coupon.Amount >= 0 {
		ret[consts.FeeHkCoupon] = math.Floor(coupon.Amount*100) / 100
	}

	return ret
}

// 香港获取feeDetail
func getHkFeeDetailInfoByQuotation(ctx context.Context, prov engine_model.FeeDescByQuotationEngineInputProvider) map[string]float64 {
	ret := make(map[string]float64)
	if prov.GetDynamicTimes() > 0 {
		ret[consts.HkDynamicPrice] = prov.GetDynamicTimes()
	}
	return ret
}
