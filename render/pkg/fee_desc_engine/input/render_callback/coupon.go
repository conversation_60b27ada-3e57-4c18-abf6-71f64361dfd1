package render_callback

import (
	"context"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	feeConsts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/simple_estimate/consts"
	"github.com/tidwall/gjson"
)

// RenderCallbackCoupon 券渲染回调
func RenderCallbackCoupon(ctx context.Context, formType int, customTag string, memberLevelId int64, config *RenderFeeDescDCMPConfig) func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
	return func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
		var (
			conf = make(map[string]gjson.Result)

			memberKey    string
			customTagKey string
		)

		// 优先使用参数传入的，否则用formType判断
		if config != nil {
			customTagKey = config.CustomTagKey
			memberKey = config.MemberKey
		} else if formType == feeConsts.SimpleForm {
			memberKey = feeConsts.SimpleEstimateMemberCoupon
			customTagKey = feeConsts.SimpleEstimateCustomTagCoupon
		} else if formType == feeConsts.AnyCarForm {
			customTagKey = feeConsts.AnyCarCustomTagCoupon
		} else if formType == feeConsts.AnyCarV3Form {
			customTagKey = feeConsts.AnyCarCustomTagCoupon
		} else if formType == feeConsts.DefaultForm {
			customTagKey = feeConsts.EstimateV4FeeDesc
			memberKey = feeConsts.EstimateV4FeeDesc
		} else if formType == feeConsts.RouteEstimateForm {
			customTagKey = feeConsts.RouteEstimateFeeDesc
			memberKey = feeConsts.RouteEstimateMemberFeeDesc
		}

		if len(customTag) > 0 {
			if strings.Contains(customTag, consts.CustomTagMemberV3) && len(memberKey) > 0 {
				if cf := dcmp.GetJSONMap(ctx, memberKey, customTag); len(cf) > 0 {
					conf = cf
				}
			} else if cf := getMemberCustomTagConf(ctx, memberLevelId, customTag, customTagKey); len(cf) > 0 { //会员券文案
				conf = cf
			} else if len(customTagKey) > 0 {
				if cf := dcmp.GetJSONMap(ctx, customTagKey, customTag); len(cf) > 0 {
					conf = cf
				}
			}
		}

		// 兜底
		if cf := dcmp.GetJSONMap(ctx, key, feeConsts.FeeCoupon); len(conf) <= 0 {
			conf = cf
		}

		if len(conf) == 0 {
			return nil
		}

		tag := map[string]string{
			"num": util.FormatPrice(fee, -1),
		}

		output := &engine_model.FeeOutput{
			Key:            path,
			BorderColor:    conf["border_color"].String(),
			TextExtraColor: conf["text_extra_color"].String(),
			BgFillColor:    conf["bg_fill_color"].String(),
			TextColor:      conf["text_color"].String(),
			HighLightColor: conf["highlight_color"].String(),
			Content:        util.ReplaceTag(ctx, conf["content"].String(), tag),
			Icon:           conf["icon"].String(),
			Type:           feeType,
			Fee: &engine_model.FeeDetail{
				Amount: fee,
			},
			CustomTag: customTag,
		}

		return output
	}
}

/*
*
获取会员优惠券对应的文案
*/
func getMemberCustomTagConf(ctx context.Context, memberLevelId int64, customTag string, customTagKey string) map[string]gjson.Result {
	if memberLevelId == 0 {
		return nil
	}
	conf := dcmp.GetJSONMap(ctx, customTagKey, customTag+"_"+strconv.FormatInt(memberLevelId, 10))
	return conf
}
