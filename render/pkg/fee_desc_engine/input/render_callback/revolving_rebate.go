package render_callback

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

func RenderCallbackRevolvingRebate(ctx context.Context, activityType int32, multiple float64) func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
	return func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
		tag := map[string]string{
			"num": util.FormatPrice(fee, -1),
		}

		if activityType == consts2.PopeActivityNFold {
			path = consts2.FeeRevolvingRebateNFold
			tag["multiple"] = util.FormatPrice(multiple, 1)
		}

		if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
			content := conf["content"].String()

			output := &engine_model.FeeOutput{
				Key:            path,
				BorderColor:    conf["border_color"].String(),
				TextExtraColor: conf["text_extra_color"].String(),
				BgFillColor:    conf["bg_fill_color"].String(),
				TextColor:      conf["text_color"].String(),
				HighLightColor: conf["highlight_color"].String(),
				Content:        util.ReplaceTag(ctx, content, tag),
				Icon:           conf["icon"].String(),
				Type:           feeType,
				Fee: &engine_model.FeeDetail{
					Amount: fee,
				},
			}

			return output
		}

		return nil
	}
}
