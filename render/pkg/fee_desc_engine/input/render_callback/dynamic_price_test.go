package render_callback

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

// TestRenderCallbackDynamicPrice 测试动调渲染函数
func TestRenderCallbackDynamicPrice(t *testing.T) {
	ctx := context.Background()

	// 模拟 dcmp.GetJSONMap 返回配置
	mockGetJSONMap := mockey.Mock(dcmp.GetJSONMap).Return(map[string]gjson.Result{
		"border_color":     gjson.Parse(`"#123456"`),
		"text_extra_color": gjson.Parse(`"#234567"`),
		"bg_fill_color":    gjson.Parse(`"#345678"`),
		"text_color":       gjson.Parse(`"#456789"`),
		"highlight_color":  gjson.Parse(`"#567890"`),
		"content":          gjson.Parse(`"价格上涨了{{num}}倍"`),
		"icon":             gjson.Parse(`"icon_url"`),
	}).Build()
	defer mockGetJSONMap.UnPatch()

	// 模拟 util.ReplaceTag 函数
	mockReplaceTag := mockey.Mock(util.ReplaceTag).To(func(ctx context.Context, text string, tag map[string]string) string {
		return "价格上涨了" + tag["num"] + "倍"
	}).Build()
	defer mockReplaceTag.UnPatch()

	// 模拟 util.FormatPrice 函数
	mockFormatPrice := mockey.Mock(util.FormatPrice).To(func(price float64, precision int) string {
		if price == 1.5 {
			return "1.5"
		} else if price == 2.5 {
			return "2.5"
		}
		return "2.0" // 默认返回
	}).Build()
	defer mockFormatPrice.UnPatch()

	t.Run("当 dynamicTimes <= 0 时应该返回 nil", func(t *testing.T) {
		callback := RenderCallbackDynamicPrice(ctx, 0, false)
		result := callback("key", "path", 100, 1)
		assert.Nil(t, result, "当 dynamicTimes <= 0 时应该返回 nil")
	})

	t.Run("当 bIsDynamicIcon 为 true 时应该返回 nil", func(t *testing.T) {
		callback := RenderCallbackDynamicPrice(ctx, 1.5, true)
		result := callback("key", "path", 100, 1)
		assert.Nil(t, result, "当 bIsDynamicIcon 为 true 时应该返回 nil")
	})

	t.Run("当 dynamicTimes > 0 且 bIsDynamicIcon 为 false 时应该返回正确结果", func(t *testing.T) {
		callback := RenderCallbackDynamicPrice(ctx, 1.5, false)
		result := callback("key", "path", 100, 1)

		assert.NotNil(t, result, "结果不应该为 nil")
		assert.Equal(t, "path", result.Key, "Key 应该正确设置")
		assert.Equal(t, "价格上涨了2.5倍", result.Content, "Content 应该正确设置")
		assert.Equal(t, float64(100), result.Fee.Amount, "Fee.Amount 应该正确设置")
		assert.Equal(t, float64(1.5), result.Fee.ExtraMap["dynamic_times"], "动态倍数应该正确设置")
	})
}

// TestRenderTaxiCallbackDynamicPrice 测试出租车动调渲染函数
func TestRenderTaxiCallbackDynamicPrice(t *testing.T) {
	ctx := context.Background()

	// 模拟 dcmp.GetJSONMap 返回配置
	mockGetJSONMap := mockey.Mock(dcmp.GetJSONMap).Return(map[string]gjson.Result{
		"border_color":     gjson.Parse(`"#123456"`),
		"text_extra_color": gjson.Parse(`"#234567"`),
		"bg_fill_color":    gjson.Parse(`"#345678"`),
		"text_color":       gjson.Parse(`"#456789"`),
		"highlight_color":  gjson.Parse(`"#567890"`),
		"content":          gjson.Parse(`"价格上涨了{{num}}倍"`),
		"icon":             gjson.Parse(`"icon_url"`),
	}).Build()
	defer mockGetJSONMap.UnPatch()

	// 模拟 util.ReplaceTag 函数
	mockReplaceTag := mockey.Mock(util.ReplaceTag).To(func(ctx context.Context, text string, tag map[string]string) string {
		return "价格上涨了" + tag["num"] + "倍"
	}).Build()
	defer mockReplaceTag.UnPatch()

	// 模拟 util.FormatPrice 函数
	mockFormatPrice := mockey.Mock(util.FormatPrice).To(func(price float64, precision int) string {
		if price == 1.5 {
			return "1.5"
		} else if price == 2.5 {
			return "2.5"
		}
		return "2.0" // 默认返回
	}).Build()
	defer mockFormatPrice.UnPatch()

	t.Run("当 dynamicTimes <= 0 时应该返回 nil", func(t *testing.T) {
		callback := RenderTaxiCallbackDynamicPrice(ctx, 0, false)
		result := callback("key", "path", 100, 1)
		assert.Nil(t, result, "当 dynamicTimes <= 0 时应该返回 nil")
	})

	t.Run("当 hitDynamicAb 为 true 时应该返回 nil", func(t *testing.T) {
		callback := RenderTaxiCallbackDynamicPrice(ctx, 1.5, true)
		result := callback("key", "path", 100, 1)
		assert.Nil(t, result, "当 hitDynamicAb 为 true 时应该返回 nil")
	})

	t.Run("当 dynamicTimes > 0 且 hitDynamicAb 为 false 时应该返回正确结果", func(t *testing.T) {
		callback := RenderTaxiCallbackDynamicPrice(ctx, 1.5, false)
		result := callback("key", "path", 100, 1)

		assert.NotNil(t, result, "结果不应该为 nil")
		assert.Equal(t, "path", result.Key, "Key 应该正确设置")
		assert.Equal(t, "价格上涨了1.5倍", result.Content, "Content 应该正确设置")
		assert.Equal(t, float64(100), result.Fee.Amount, "Fee.Amount 应该正确设置")
		assert.Equal(t, float64(1.5), result.Fee.ExtraMap["dynamic_times"], "动态倍数应该正确设置")
	})
}
