package render_callback

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

// RenderCallbackDynamicPrice 动调渲染
func RenderCallbackDynamicPrice(ctx context.Context, dynamicTimes float64, bIsDynamicIcon bool) func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
	return func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
		if dynamicTimes <= 0 {
			return nil
		}
		if bIsDynamicIcon {
			return nil
		}
		if len(key) < 0 {
			key = consts.FeeDetailDesc
		}

		if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
			tag := map[string]string{
				"num": util.FormatPrice(dynamicTimes+1, -1),
			}

			output := &engine_model.FeeOutput{
				Key:            path,
				BorderColor:    conf["border_color"].String(),
				TextExtraColor: conf["text_extra_color"].String(),
				BgFillColor:    conf["bg_fill_color"].String(),
				TextColor:      conf["text_color"].String(),
				HighLightColor: conf["highlight_color"].String(),
				Content:        util.ReplaceTag(ctx, conf["content"].String(), tag),
				Icon:           conf["icon"].String(),
				Type:           feeType,
				Fee: &engine_model.FeeDetail{
					Amount: fee,
					ExtraMap: map[string]interface{}{
						"dynamic_times": dynamicTimes,
					},
				},
			}

			return output
		}

		return nil
	}
}

// RenderTaxiCallbackDynamicPrice 出租车动调渲染
func RenderTaxiCallbackDynamicPrice(ctx context.Context, dynamicTimes float64, hitDynamicAb bool) func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
	return func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
		if dynamicTimes <= 0 {
			return nil
		}

		if hitDynamicAb {
			return nil
		}

		if len(key) < 0 {
			key = consts.FeeDetailDesc
		}

		if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
			tag := map[string]string{
				"num": util.FormatPrice(dynamicTimes, -1),
			}

			output := &engine_model.FeeOutput{
				Key:            path,
				BorderColor:    conf["border_color"].String(),
				TextExtraColor: conf["text_extra_color"].String(),
				BgFillColor:    conf["bg_fill_color"].String(),
				TextColor:      conf["text_color"].String(),
				HighLightColor: conf["highlight_color"].String(),
				Content:        util.ReplaceTag(ctx, conf["content"].String(), tag),
				Icon:           conf["icon"].String(),
				Type:           feeType,
				Fee: &engine_model.FeeDetail{
					Amount: fee,
					ExtraMap: map[string]interface{}{
						"dynamic_times": dynamicTimes,
					},
				},
			}

			return output
		}

		return nil
	}
}
