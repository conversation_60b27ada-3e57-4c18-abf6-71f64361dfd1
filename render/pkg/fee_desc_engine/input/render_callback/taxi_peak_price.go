package render_callback

import (
	"context"

	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

const (
	NotHoliday = iota
	GeneralHoliday
)

type TaxiPeakPriceRenderProvider interface {
	IsHoliday() int32
}

// RenderCallbackTaxiPeakPrice 信息费渲染回调
func RenderCallbackTaxiPeakPrice(ctx context.Context, prov TaxiPeakPriceRenderProvider) func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
	return func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {

		if prov.IsHoliday() == GeneralHoliday {
			key = "fee_engine_callback-fee_desc"
			path += "_holiday"
		}

		if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
			tag := map[string]string{
				"num": util.FormatPrice(fee, -1),
			}

			output := &engine_model.FeeOutput{
				Key:            path,
				BorderColor:    conf["border_color"].String(),
				TextExtraColor: conf["text_extra_color"].String(),
				BgFillColor:    conf["bg_fill_color"].String(),
				TextColor:      conf["text_color"].String(),
				HighLightColor: conf["highlight_color"].String(),
				Content:        util.ReplaceTag(ctx, conf["content"].String(), tag),
				Icon:           conf["icon"].String(),
				Type:           feeType,
				Fee: &engine_model.FeeDetail{
					Amount: fee,
				},
			}

			return output
		}

		return nil
	}
}
