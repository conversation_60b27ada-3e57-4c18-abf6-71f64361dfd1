package render_callback

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	feeConsts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

func RenderCallbackMemberDiscountCard(ctx context.Context, levelID int64, customTag string, config *RenderFeeDescDCMPConfig) func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
	return func(key string, path string, fee float64, feeType int32) *engine_model.FeeOutput {
		var (
			icon             string
			textColor        string
			newLossDcmpKey   = feeConsts.EstimateV4FeeDesc
			newlossPath      = "new_loss_discount"
			memberDcmpKey    = "estimate_form_v3-fee_desc_member_discount"
			memberPathPrefix = ""
		)
		if config != nil {
			newLossDcmpKey = config.NewLossKey
			memberDcmpKey = config.MemberKey
			memberPathPrefix = config.MemberPathPrefix
		}
		tag := map[string]string{
			"num": util.FormatPrice(fee, -1),
		}

		if customTag == "newloss_discount" {
			var newlossContent string
			if memberConf := dcmp.GetJSONMap(ctx, newLossDcmpKey, newlossPath); len(memberConf) > 0 {
				icon = memberConf["icon"].String()
				newlossContent = memberConf["content"].String()
			}

			if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
				output := &engine_model.FeeOutput{
					Key:            path,
					BorderColor:    conf["border_color"].String(),
					TextExtraColor: conf["text_extra_color"].String(),
					BgFillColor:    conf["bg_fill_color"].String(),
					HighLightColor: conf["highlight_color"].String(),
					Content:        util.ReplaceTag(ctx, newlossContent, tag),
					Icon:           icon,
					Type:           feeType,
					Fee: &engine_model.FeeDetail{
						Amount: fee,
					},
				}

				return output
			}
		} else {
			if memberConf := dcmp.GetJSONMap(ctx, memberDcmpKey, memberPathPrefix+strconv.Itoa(int(levelID))); len(memberConf) > 0 {
				icon = memberConf["icon"].String()
				textColor = memberConf["text_color"].String()
			}

			if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
				content := conf["content"].String()

				output := &engine_model.FeeOutput{
					Key:            path,
					BorderColor:    conf["border_color"].String(),
					TextExtraColor: conf["text_extra_color"].String(),
					BgFillColor:    conf["bg_fill_color"].String(),
					HighLightColor: conf["highlight_color"].String(),
					Content:        util.ReplaceTag(ctx, content, tag),
					Icon:           icon,
					TextColor:      textColor,
					Type:           feeType,
					Fee: &engine_model.FeeDetail{
						Amount: fee,
					},
				}

				return output
			}
		}

		return nil
	}
}
