package filter_callback

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"

	feeConsts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/simple_estimate/consts"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	apolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

func FilterCallbackTaxiPeakPrice(ctx context.Context, prov engine_model.FeeDescEngineInputProvider, formType int) func() bool {
	return func() bool {
		if prov.GetProductId() != product_id.ProductIdUnitaxi {
			return true
		}

		// 非预约单
		if prov.GetOrderType() != consts.TypeOrderNow {
			return true
		}

		// 主表单只有无出口才展示
		if formType == feeConsts.DefaultForm {
			// 无出口
			taxiSps := prov.GetTaxiSps()
			if taxiSps == nil || taxiSps.CanSelect != consts.FormNoInterActive {
				return true
			}
		}

		//
		// // 开城
		// condition := apolloModel.NewCondition(map[string]string{
		// 	"product_category": fmt.Sprintf("%d", prov.GetProductCategory()),
		// 	"city_id":          fmt.Sprintf("%d", prov.GetCityID()),
		// })
		//
		// config, err := apollo.GetConfigsByNamespaceAndConditions(ctx, consts.TaxiPeekFee, condition)
		// if err != nil || len(config) <= 0 {
		// 	return true
		// }
		//
		// apolloInstance := []*consts.ConfigTaxiPeek{}
		// err = json.Unmarshal(config, &apolloInstance)
		// if err != nil || len(apolloInstance) <= 0 {
		// 	log.Trace.Warnf(ctx, "FilterCallbackTaxiPeakPrice", "unmarshal fail, err:%v", err)
		// 	return true
		// }
		//
		// if apolloInstance[0].OpenStatus != "1" {
		// 	return true
		// }

		return false
	}
}

func FilterCallbackTaxiPeakPriceByQuotation(ctx context.Context, prov engine_model.FeeDescByQuotationEngineInputProvider) func() bool {
	return func() bool {
		if prov.GetProductId() != product_id.ProductIdUnitaxi {
			return true
		}

		// 非预约单
		if prov.GetOrderType() != consts.TypeOrderNow {
			return true
		}

		// 开城
		condition := apolloModel.NewCondition(map[string]string{
			"product_category": fmt.Sprintf("%d", prov.GetProductCategory()),
			"city_id":          "0",
		})

		config, err := apollo.GetConfigsByNamespaceAndConditions(ctx, consts.TaxiPeekFee, condition)
		if err != nil || len(config) <= 0 {
			return true
		}

		apolloInstance := []*consts.ConfigTaxiPeek{}
		err = json.Unmarshal(config, &apolloInstance)
		if err != nil || len(apolloInstance) <= 0 {
			log.Trace.Warnf(ctx, "FilterCallbackTaxiPeakPrice", "unmarshal fail, err:%v", err)
			return true
		}

		if apolloInstance[0].OpenStatus != "1" {
			return true
		}

		return false
	}
}
