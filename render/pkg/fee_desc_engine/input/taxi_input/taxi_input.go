package taxi_input

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

type taxiGapInfo struct {
	GapAmount   float64 `json:"gap_amount"`
	GapDistance int64   `json:"gap_distance"`
}

const (
	parameterHaveCompare            = "have_compare"
	parameterIsHitBox               = "is_hit_box"
	parameterHavePricingByOnlineTag = "have_pricing_by_online_tag"
)

// BuildTaxiPricingByOnline 在线计价标签
func BuildTaxiPricingByOnline(ctx context.Context, prov model.FeeDescEngineInputProvider) (float64, bool) {
	gapInfo := dcmp.GetDcmpContent(ctx, consts.AnyCarV3TaxiGapInfo, nil)
	gapInfoStruct := &taxiGapInfo{}
	err := json.Unmarshal([]byte(gapInfo), gapInfoStruct)
	if err != nil || gapInfoStruct == nil {
		return 0, false
	}

	// 在线计价
	if prov.GetSubGroupId() == consts2.SubGroupIdTaxiPricingBox && prov.GetLevelType() == 5 {
		if !checkAb(ctx, prov, parameterHavePricingByOnlineTag) {
			return 0, false
		}

		if checkDistance(ctx, prov, gapInfoStruct) && checkAmount(ctx, prov, gapInfoStruct) {
			return 0, false
		}

		return 0, true
	}

	return 0, false
}

// BuildComparePricingByMeter 比打表省
func BuildComparePricingByMeter(ctx context.Context, prov model.FeeDescEngineInputProvider) (float64, bool) {
	gapInfo := dcmp.GetDcmpContent(ctx, consts.AnyCarV3TaxiGapInfo, nil)
	gapInfoStruct := &taxiGapInfo{}
	err := json.Unmarshal([]byte(gapInfo), gapInfoStruct)
	if err != nil || gapInfoStruct == nil {
		return 0, false
	}

	// 在线计价
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiMarketisationPutong {
		if checkAb(ctx, prov, parameterIsHitBox) {
			return 0, false
		}

		if !checkDistance(ctx, prov, gapInfoStruct) {
			return 0, false
		}

		if !checkAmount(ctx, prov, gapInfoStruct) {
			return 0, false
		}

		if prov.GetPricingByMeterPrice() > 0 && prov.GetEstimateFee() > 0 && prov.GetPricingByMeterPrice()-prov.GetEstimateFee() > 0 {
			return prov.GetPricingByMeterPrice() - prov.GetEstimateFee(), true
		}

		return 0, false
	}

	return 0, false
}

func checkAb(ctx context.Context, prov model.FeeDescEngineInputProvider, parameter string) bool {
	apolloKey, params := prov.ApolloParamsGen(apollo_model.WithUIDKey, apollo_model.WithFromCountyId)
	toggle := apollo.GetHitToggleByNamespace(ctx, apolloKey, "taxi_form", params)
	return toggle != nil && toggle.GetAssignment().GetParameter(parameter, "0") == "1"
}

func checkDistance(ctx context.Context, prov model.FeeDescEngineInputProvider, gapInfo *taxiGapInfo) bool {
	if gapInfo == nil {
		return false
	}

	if prov.GetBillDriverMetre() < gapInfo.GapDistance {
		return true
	}

	return false
}

func checkAmount(ctx context.Context, prov model.FeeDescEngineInputProvider, gapInfo *taxiGapInfo) bool {
	if gapInfo == nil {
		return false
	}

	if prov.GetEstimateFee() < prov.GetPricingByMeterPrice()-gapInfo.GapAmount {
		return true
	}

	return false
}
