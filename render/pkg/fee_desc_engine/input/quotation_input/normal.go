package quotation_input

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input/render_callback"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

func BuildMemberDiscountCard(ctx context.Context, input *engine_model.FeeInput, prov engine_model.FeeDescByQuotationEngineInputProvider, config *render_callback.RenderFeeDescDCMPConfig) {
	if input == nil {
		return
	}

	var customTag string
	if memberDiscountCard := prov.GetDiscountDescByTypes(ctx, []string{engine_model.MemberDiscountCard}); memberDiscountCard != nil {
		customTag = memberDiscountCard.CustomTag
	}

	if _, ok := input.DiscountInfo[consts.FeeMemberDiscountCard]; ok {
		input.RegisterRenderCallBack(consts.FeeMemberDiscountCard, render_callback.RenderCallbackMemberDiscountCard(ctx, prov.GetLevelID(), customTag, config))
	}
}
