package fee_desc_carpool_render

// CarpoolFeeDesc 持有费项的类
type CarpoolFeeDesc struct {
	feeMap   map[string]map[string]float64
	Sequence []string
	mergeMap map[string]bool
}

func InitCarpoolFeeDesc(s []string) *CarpoolFeeDesc {
	return &CarpoolFeeDesc{
		Sequence: s,
		feeMap:   map[string]map[string]float64{},
	}

}

func (c *CarpoolFeeDesc) RegisterFee(key string, value map[string]float64) *CarpoolFeeDesc {
	if c.feeMap == nil {
		c.feeMap = map[string]map[string]float64{}
	}
	c.feeMap[key] = value
	return c
}

func (c *CarpoolFeeDesc) IsHitFee(key string) bool {
	if c.feeMap == nil {
		return false
	}
	if c.feeMap[key] == nil {
		return false
	}
	return true
}

func (c *CarpoolFeeDesc) IsHitMerge(key string) bool {
	if c.mergeMap == nil {
		return false
	}
	return c.mergeMap[key]
}

func (c *CarpoolFeeDesc) SetMerge(key string, status bool) {
	if c.mergeMap == nil {
		c.mergeMap = map[string]bool{}
	}
	c.mergeMap[key] = status
}

func (c *CarpoolFeeDesc) GetFee(key string) map[string]float64 {
	if c.feeMap == nil {
		return nil
	}
	if c.feeMap[key] == nil {
		return nil
	}
	return c.feeMap[key]
}
