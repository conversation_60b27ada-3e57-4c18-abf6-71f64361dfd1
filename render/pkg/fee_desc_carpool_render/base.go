package fee_desc_carpool_render

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

type Material struct {
	Content     string  `json:"content"`      // 主文案
	Connect     string  `json:"connect"`      // 多价格拼接缀：/
	Suffix      string  `json:"suffix"`       // append 文案
	Icon        string  `json:"icon"`         // icon
	BorderColor string  `json:"border_color"` // 字体颜色
	Title       string  `json:"title"`        // 备注
	Amount      float64 `json:"amount"`       // 数值
	TextColor   string  `json:"text_color"`
}

// CarpoolFeeDescBase 持有价格和文案等基础数据的类
type CarpoolFeeDescBase struct {

	// 排序 1_2_1
	sort []string
	// 多口价和费用数据 key : 1_2_1 一人拼2人拼成
	feeMap map[string]*biz_runtime.SceneEstimateFee
	// feeDetaillInfo
	feeDetaillInfo map[string]map[string]float64
	// 是否合并
	equalStatus bool
	// 渲染文案
	dcmp map[string]*Material

	carpoolFeeDesc *CarpoolFeeDesc

	prov Provider
}
type PriceSelect struct {
	PoolNum          string `json:"pool_num"`
	SeatNum          string `json:"seat_num"`
	IsCarpoolSuccess string `json:"is_carpool_success"`
}

type Provider interface {
	//Render.ApolloProvider
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
	GetExactSeatNum() int32 // 获取用户选择的座位数
	GetProductCategory() int64
	GetSceneDataBySceneMark(reqSceneMark map[string]string) *biz_runtime.SceneEstimateFee
	IsBusinessPay() bool
	GetLevelID() int32
}

func NewCarpoolFeeDescBase(ctx context.Context, prov Provider, dcmpKey string, sortDcmpKey string, priceSelectDcmpKey string) *CarpoolFeeDescBase {
	config := dcmp.GetDcmpContent(ctx, dcmpKey, nil)
	dcmpMaterial := make(map[string]*Material)
	_ = json.Unmarshal([]byte(config), &dcmpMaterial) //nolint
	if len(dcmpMaterial) == 0 {
		return nil
	}

	config = dcmp.GetDcmpContent(ctx, sortDcmpKey, nil)

	sort := []string{}
	_ = json.Unmarshal([]byte(gjson.Get(config, strconv.Itoa(int(prov.GetProductCategory()))+"."+strconv.Itoa(int(prov.GetExactSeatNum()))+"."+"sort").String()), &sort) //nolint
	if len(sort) == 0 {
		return nil
	}
	config = dcmp.GetDcmpContent(ctx, priceSelectDcmpKey, nil)
	selectPriceList := make([]PriceSelect, 0)
	_ = json.Unmarshal([]byte(gjson.Get(config, strconv.Itoa(int(prov.GetProductCategory()))+"."+strconv.Itoa(int(prov.GetExactSeatNum()))).String()), &selectPriceList) //nolint
	if len(selectPriceList) == 0 {
		return nil
	}

	feeMap, equalStatus := buildMultiPrice(ctx, prov, selectPriceList)
	if feeMap == nil {
		return nil
	}
	res := CarpoolFeeDescBase{
		prov:           prov,
		sort:           sort,
		dcmp:           dcmpMaterial,
		feeMap:         feeMap,
		feeDetaillInfo: make(map[string]map[string]float64),
		equalStatus:    equalStatus,
		carpoolFeeDesc: InitCarpoolFeeDesc([]string{FeeRedPacket, FeeBusinessPayDeduction, FeeMemberDiscountCard, FeeCoupon, FeeNormalDiscount}),
	}
	for k, v := range feeMap {
		if v == nil {
			continue
		}
		if v.GetFeeDetail() == nil {
			continue
		}

		res.feeDetaillInfo[k] = make(map[string]float64)
		if v.GetFeeDetail().GetFeeDetailInfo() == nil {
			continue
		}
		for i, j := range v.GetFeeDetail().GetFeeDetailInfo() {
			res.feeDetaillInfo[k][i] = j
		}

	}
	return &res
}

func buildMultiPrice(ctx context.Context, prov Provider, priceSelect []PriceSelect) (feeMap map[string]*biz_runtime.SceneEstimateFee, equalStatus bool) {
	seatNum := prov.GetExactSeatNum()
	if seatNum == 0 {
		seatNum = 1
	}
	feeMap = make(map[string]*biz_runtime.SceneEstimateFee, 0)

	fee := float64(0)
	for _, v := range priceSelect {
		res := prov.GetSceneDataBySceneMark(map[string]string{
			"pool_num":           v.PoolNum,
			"seat_num":           v.SeatNum,
			"is_carpool_success": v.IsCarpoolSuccess,
		})
		if res == nil {
			return
		}
		// 1-2-1 35.89
		feeMap[v.SeatNum+"-"+v.PoolNum+"-"+v.IsCarpoolSuccess] = res
		fee = res.GetFee()
	}
	equalStatus = true
	for _, v := range feeMap {
		if fee != v.GetFee() {
			equalStatus = false
		}
	}
	return feeMap, equalStatus
}

func (c *CarpoolFeeDescBase) RegisterFee(ctx context.Context, key string) *CarpoolFeeDescBase {
	res, status := make(map[string]float64), false
	switch key {
	case FeeRedPacket:
		res, status = c.GetRedPacketFee(ctx)
	case FeeBusinessPayDeduction:
		res, status = c.GetBusinessPayDeduction(ctx)
	case FeeMemberDiscountCard:
		res, status = c.GetMemberDiscountCard(ctx)
	case FeeNormalDiscount:
		res, status = c.GetNormalDiscount(ctx)
	case FeeCoupon:
		res, status = c.GetCoupon(ctx)
	}
	if res != nil && len(res) != 0 {
		c.carpoolFeeDesc.RegisterFee(key, res)
		c.carpoolFeeDesc.SetMerge(key, status)
	}
	return c
}

func (c *CarpoolFeeDescBase) BuildResp(ctx context.Context) (m *Material) {
	for _, v := range c.carpoolFeeDesc.Sequence {
		if c.carpoolFeeDesc.IsHitFee(v) {

			resp := c.build(ctx, v, c.carpoolFeeDesc.GetFee(v))
			if resp != nil && resp.Content != "" {
				if v == FeeMemberDiscountCard || v == FeeCoupon {
					memberLevel := c.prov.GetLevelID()
					if memberLevel > 0 {
						memberConf := dcmp.GetJSONMap(ctx, "estimate_form_v3-fee_desc_member_discount", util.ToString(memberLevel))
						resp.Icon = memberConf["icon"].String()
						resp.TextColor = memberConf["text_color"].String()
					}
				}
				return resp
			}

		}
	}
	return nil
}

func (c *CarpoolFeeDescBase) build(ctx context.Context, key string, fee map[string]float64) (m *Material) {
	if c.dcmp[key] == nil || c.dcmp[key].Content == "" || fee == nil {
		return nil
	}
	material := c.dcmp[key]
	if c.equalStatus || c.carpoolFeeDesc.IsHitMerge(key) {
		max := float64(0)
		for _, v := range fee {
			if max < v {
				max = v
			}
		}
		if max == 0 {
			return nil
		}
		return &Material{
			BorderColor: material.BorderColor,
			Icon:        material.Icon,
			Amount:      util.RoundAbs(max, 2),
			Content: util.ReplaceTag(ctx, material.Content, map[string]string{
				"num": util.FormatPriceWithoutZero(max, 2),
			}),
		}
	} else {
		m := &Material{
			BorderColor: material.BorderColor,
			Icon:        material.Icon,
		}
		max := float64(0)
		for _, v := range fee {
			if max < v {
				max = v
			}
		}
		if max == 0 {
			return nil
		}
		for k, s := range c.sort {

			if k == 0 {
				m.Content += util.ReplaceTag(ctx, material.Content, map[string]string{
					"num": util.FormatPriceWithoutZero(fee[s], 2),
				})
				m.Amount = util.RoundAbs(fee[s], 2)
			}
			if k != 0 {
				m.Content += material.Connect
				m.Content += util.ReplaceTag(ctx, material.Suffix, map[string]string{
					"num": util.FormatPriceWithoutZero(fee[s], 2),
				})
			}

		}
		return m
	}
}
