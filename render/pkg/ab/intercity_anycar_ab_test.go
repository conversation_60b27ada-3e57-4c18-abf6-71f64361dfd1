package ab

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestIsHitIntercityAnycarAb(t *testing.T) {
	convey.Convey("命中缓存场景", t, func() {
		ctx := context.Background()
		ctx = knife.New(ctx)
		knife.Set(ctx, IntercityAnycarAb, true)

		// 测试参数
		pid := "test_pid"
		param := map[string]string{
			"combo_id":    "123",
			"route_group": "456",
			"product_id":  "789",
		}

		// 调用被测函数
		result := IsHitIntercityAnycarAb(ctx, pid, param)

		// 验证结果
		convey.So(result, convey.ShouldBeTrue)
	})
	convey.Convey("当特性开关开启时", t, func() {
		defer mockey.UnPatchAll()
		ctx := context.Background()
		// 模拟 apollo.FeatureToggle 返回 true
		mockey.Mock(apollo.FeatureToggle).Return(true).Build()

		// 测试参数
		pid := "test_pid"
		param := map[string]string{
			"combo_id":    "123",
			"route_group": "456",
			"product_id":  "789",
		}

		// 调用被测函数
		result := IsHitIntercityAnycarAb(ctx, pid, param)

		// 验证结果
		convey.So(result, convey.ShouldBeTrue)
	})
	convey.Convey("当特性开关关闭时", t, func() {
		defer mockey.UnPatchAll()
		ctx := context.Background()
		// 模拟 apollo.FeatureToggle 返回 true
		mockey.Mock(apollo.FeatureToggle).Return(false).Build()

		// 测试参数
		pid := "test_pid"
		param := map[string]string{
			"combo_id":    "123",
			"route_group": "456",
			"product_id":  "789",
		}

		// 调用被测函数
		result := IsHitIntercityAnycarAb(ctx, pid, param)

		// 验证结果
		convey.So(result, convey.ShouldBeFalse)
	})
}
