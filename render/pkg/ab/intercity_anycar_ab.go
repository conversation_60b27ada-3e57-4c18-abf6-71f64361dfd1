package ab

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/nuwa/golibs/knife"
)

const IntercityAnycarAb = "intercity_anycar_estimate_switch"

// IsHitIntercityAnycarAb 城际小车多呼1期版控制
func IsHitIntercityAnycarAb(ctx context.Context, pid string, param map[string]string) bool {
	param["source"] = "mamba"
	v := knife.Get(ctx, IntercityAnycarAb)
	if v != nil {
		return v.(bool)
	}
	if apollo.FeatureToggle(ctx, IntercityAnycarAb, pid, param) {
		knife.Set(ctx, IntercityAnycarAb, true)
		return true
	}
	knife.Set(ctx, IntercityAnycarAb, false)
	return false
}
