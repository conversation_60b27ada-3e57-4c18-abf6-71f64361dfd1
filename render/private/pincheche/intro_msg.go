package pincheche

import (
	"context"
	"math"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Dirpc_SDK_Mamba "git.xiaojukeji.com/dirpc/dirpc-go-http-Mamba"
	cardRenderCore "git.xiaojukeji.com/gulfstream/carpool-communication-go/cardController"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/passenger-common/constant/communicate"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type CommuteCard struct {
	ID string
}
type CommuteCardGetter interface {
	GetCommuteCard() *CommuteCard
}
type IntroMsgProvider interface {
	CommuteCardGetter
	IsHitHotRoute() bool
}

func IntroMsg(ctx context.Context, prov IntroMsgProvider) string {
	if prov.IsHitHotRoute() {
		return dcmp.GetJSONContentWithPath(ctx, "carpool_tab_pincheche-intro_msg", nil, "hot_route")
	} else if card := prov.GetCommuteCard(); card != nil {
		return dcmp.GetJSONContentWithPath(ctx, "carpool_tab_pincheche-intro_msg", nil, "commute_card")
	}
	return dcmp.GetJSONContentWithPath(ctx, "carpool_tab_pincheche-intro_msg", nil, "normal")
}

type ApolloParamProvider interface {
	ApolloParam() *ApolloModel.User
}

type ExtraIntroTagProvider interface {
	CommuteCardGetter
	ApolloParamProvider
	GetDepartureTime() time.Time
	IsBookingOrder() bool
	GetEstimateID() string
	GetVcardData() *models.VcardResult
	GetSpsLikeWaitReward() *PriceApi.NewFormSpsLikeWaitReward
	GetVCard() *PriceApi.EstimateNewFormVCardInfo
	GetWaitSendEmptyCarTime(string) int64
	IsPinchecheNewForm() bool
}

func ExtraIntroTag(ctx context.Context, prov ExtraIntroTagProvider, isMergeShow bool) *proto.HyberTextButton {
	if prov.IsPinchecheNewForm() && isMergeShow && prov.IsBookingOrder() {
		return nil
	}

	// 畅拼卡 > 派空车 > 立减金 > 默认
	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheV2, "extra_intro_tag")

	if prov.IsPinchecheNewForm() && prov.IsBookingOrder() {
		return &proto.HyberTextButton{
			Content:     *dcmp.GetStringDcmpContentByPath(config, "extra_default_text_booking"),
			Icon:        "",
			BorderColor: "",
			LinkUrl:     "",
		}
	}

	vcardTagConf := communicate.CommunicationTagComponent
	waitRewardPath := DcmpPathWaitReward
	waitSendEmptyCarPath := DcmpPathWaitSendEmptyCar

	if prov.IsPinchecheNewForm() && isMergeShow {
		vcardTagConf = ApoConfVcardTagSimple
		waitRewardPath = DcmpPathWaitRewardSimple
		waitSendEmptyCarPath = DcmpPathWaitSendEmptyCarSimple
	} else if prov.IsPinchecheNewForm() {
		vcardTagConf = ApoConfVcardTag
		waitRewardPath = DcmpPathWaitRewardV2
		waitSendEmptyCarPath = DcmpPathWaitSendEmptyCarV2
	}

	if desc := GetInroMsg(ctx, prov, communicate.BubbleCarpoolTabPage, vcardTagConf); desc != nil {
		return &proto.HyberTextButton{
			Icon:        desc.Icon,
			Content:     desc.Content,
			BorderColor: desc.BorderColor,
			LinkUrl:     desc.LinkUrl,
		}
	} else if waitTime := prov.GetWaitSendEmptyCarTime(prov.GetEstimateID()); waitTime > 0 {
		tmp := config[waitSendEmptyCarPath].Map()
		ceilTime := int(math.Ceil(float64(waitTime) / 60))
		replaceTag := map[string]string{
			"wait_time": strconv.Itoa(ceilTime),
		}
		return &proto.HyberTextButton{
			Content:     dcmp.TranslateTemplate(*dcmp.GetStringDcmpContentByPath(tmp, "content"), replaceTag),
			Icon:        *dcmp.GetStringDcmpContentByPath(tmp, "icon"),
			LinkUrl:     *dcmp.GetStringDcmpContentByPath(tmp, "link_url"),
			BorderColor: *dcmp.GetStringDcmpContentByPath(tmp, "border_color"),
		}
	} else if reward := prov.GetSpsLikeWaitReward(); reward != nil {
		tmp := config[waitRewardPath].Map()
		ceilTime := int(math.Ceil(float64(reward.WaitTime) / 60))
		replaceTag := map[string]string{
			"amount":    util.RemoveSuffixZero(util.FormatFenToYuan(reward.Amount, 2)),
			"wait_time": strconv.Itoa(ceilTime),
		}
		return &proto.HyberTextButton{
			Content:     dcmp.TranslateTemplate(*dcmp.GetStringDcmpContentByPath(tmp, "content"), replaceTag),
			Icon:        *dcmp.GetStringDcmpContentByPath(tmp, "icon"),
			LinkUrl:     *dcmp.GetStringDcmpContentByPath(tmp, "link_url"),
			BorderColor: *dcmp.GetStringDcmpContentByPath(tmp, "border_color"),
		}
	} else {
		return &proto.HyberTextButton{
			Content:     *dcmp.GetStringDcmpContentByPath(config, "extra_default_text"),
			Icon:        "",
			BorderColor: "",
			LinkUrl:     "",
		}
	}

}

func GetInroMsg(ctx context.Context, prov ExtraIntroTagProvider, pageName, componentName string) *Dirpc_SDK_Mamba.HyberTextButton {
	//communicate.BubbleCarpoolTabPage, communicate.CommunicationTagComponent
	renderObj := cardRenderCore.NewCardRenderObj(log.Trace, pageName, componentName)
	if renderObj == nil {
		return nil
	}
	if prov.GetVCard() == nil {
		return nil
	}
	desc := renderObj.BuildCarpoolTabIntroMsg(ctx, prov.GetVCard(), nil)
	return desc
}
