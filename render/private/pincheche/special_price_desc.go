package pincheche

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"github.com/tidwall/gjson"
)

type ExtraPriceType int

const (
	ExtraPriceType_None ExtraPriceType = iota
	ExtraPriceType_SpringFestival
	ExtraPriceType_MultiSpringFestival
)

type SpecialPriceProvider interface {
	SpecialPriceType() ExtraPriceType
	SpecialPriceAmount() float64
}

func ExtraPriceDesc(ctx context.Context, prov SpecialPriceProvider) string {
	template := dcmp.GetDcmpContent(ctx,
		"carpool_tab_pincheche-special_price_desc",
		map[string]string{
			"fee": strconv.Itoa(int(prov.SpecialPriceAmount())),
		},
	)

	switch prov.SpecialPriceType() {
	case ExtraPriceType_SpringFestival:
		return gjson.Get(template, "spring_festival").String()
	case ExtraPriceType_MultiSpringFestival:
		return gjson.Get(template, "spring_festival_multi").String()
	default:
		return ""
	}
}
