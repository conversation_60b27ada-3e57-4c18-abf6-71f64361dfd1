package pincheche

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool"
	"sort"
	"strconv"
	"strings"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
)

type PriceDescProvider interface {
	GetEstimateFeeAmount() float64
	GetDiscountFeeAmount() float64
	GetExpectPoolNum() int
	GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo
	GetPaymentType() int
	GetRedPacketFee() float64
}

func MultiPriceDesc(ctx context.Context, prod render.BaseProvider, prov ...PriceDescProvider) (result []*proto.PriceWithSingleDesc) {
	if len(prov) == 0 {
		return nil
	}

	// 拼到人数多的在前面
	sort.Slice(prov, func(i, j int) bool {
		return prov[i].GetExpectPoolNum() > prov[j].GetExpectPoolNum()
	})

	businessPay := false
	feeAllEqual := true
	fee := carpool3.FormatPrice(prov[0].GetEstimateFeeAmount(), carpool3.PageDefault, util.ToString(prod.GetCityID()),
		util.Int642String(prod.GetUserPID()), -1).FloatVal
	for _, p := range prov {
		if p.GetExpectPoolNum() > 0 { // 只处理 拼到人的
			if carpool3.FormatPrice(p.GetEstimateFeeAmount(), carpool3.PageDefault, util.ToString(prod.GetCityID()),
				util.Int642String(prod.GetUserPID()), -1).FloatVal != fee {
				feeAllEqual = false
			}
		}
		if p.GetPaymentType() == consts.BusinessPaymentType {
			businessPay = true
		}
	}

	result = make([]*proto.PriceWithSingleDesc, 0, len(prov))

	estimatePriceTemplate := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-estimate_price_msg")
	discountTemplate := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-price_desc")

	if businessPay { //企业付
		theExpectOnePool := func(ps []PriceDescProvider) PriceDescProvider {
			for _, p := range ps {
				if p.GetExpectPoolNum() == 1 {
					return p
				}
			}
			return ps[0]
		}(prov)
		feeMsg := dcmp.TranslateTemplate(
			gjson.Get(estimatePriceTemplate, "business_pay").String(),
			map[string]string{"fee": formatPrice(0.0)}, // 企业支付预估价展示为0.0元
		)
		content := dcmp.TranslateTemplate(
			gjson.Get(discountTemplate, "business_pay.content").String(),
			map[string]string{"fee": formatPrice(theExpectOnePool.GetEstimateFeeAmount())}, // 抵扣金额用拼到1人价格
		)
		desc := &proto.PriceWithSingleDesc{
			FeeMsg: feeMsg,
			FeeDesc: &proto.TagWithIconAndBorder{
				Icon:        gjson.Get(discountTemplate, "business_pay.icon").String(),
				Content:     content,
				BorderColor: gjson.Get(discountTemplate, "business_pay.border_color").String(),
			},
		}
		result = append(result, desc)
	} else if feeAllEqual {

		feeMsg := dcmp.TranslateTemplate(
			gjson.Get(estimatePriceTemplate, "without_pool_number").String(),
			map[string]string{
				"fee": carpool3.FormatPrice(prov[0].GetEstimateFeeAmount(), carpool3.PageDefault,
					util.ToString(prod.GetCityID()), util.Int642String(prod.GetUserPID()), -1).StringVal},
		)
		desc := &proto.PriceWithSingleDesc{
			FeeMsg: feeMsg,
		}

		content := ""
		sIcon := ""
		oSuccessCouponInfo := prov[0].GetCouponInfo()
		if oSuccessCouponInfo != nil && strings.Count(oSuccessCouponInfo.CustomTag, "member_v3") > 0 { //存在优惠券且优惠券为会员券
			couponAmount, err := strconv.ParseFloat(oSuccessCouponInfo.Amount, 64)
			if err != nil {
				return nil
			}
			if couponAmount > 0 {
				couponAmount = couponAmount / 100
				memberIcons := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-paid_member_icons")
				content = dcmp.TranslateTemplate(gjson.Get(discountTemplate, "paid_member_desc.content").String(),
					map[string]string{"fee": formatPrice(couponAmount)},
				)
				sIcon = gjson.Get(memberIcons, oSuccessCouponInfo.CustomTag).String()
			}
		} else {
			if isDiscountAmountShouldDisplay(prov[0].GetDiscountFeeAmount()) {
				content = dcmp.TranslateTemplate(
					gjson.Get(discountTemplate, "diff_price_desc.content").String(),
					map[string]string{"fee": formatPrice(prov[0].GetDiscountFeeAmount())},
				)
			}
			sIcon = gjson.Get(discountTemplate, "diff_price_desc.icon").String()
		}
		if content != "" {
			desc.FeeDesc = &proto.TagWithIconAndBorder{
				Icon:        sIcon,
				Content:     content,
				BorderColor: gjson.Get(discountTemplate, "diff_price_desc.border_color").String(),
			}
		}
		result = append(result, desc)
	} else {
		for _, p := range prov {
			feeMsg := dcmp.TranslateTemplate(
				gjson.Get(estimatePriceTemplate, "with_pool_number").String(),
				map[string]string{
					"pool": strconv.Itoa(p.GetExpectPoolNum()),
					"fee": carpool3.FormatPrice(p.GetEstimateFeeAmount(), carpool3.PageDefault,
						util.ToString(prod.GetCityID()), util.Int642String(prod.GetUserPID()), -1).StringVal},
			)
			content := ""
			sIcon := ""
			oSuccessCouponInfo := p.GetCouponInfo()
			if oSuccessCouponInfo != nil && strings.Count(oSuccessCouponInfo.CustomTag, "member_v3") > 0 { //存在优惠券且优惠券为会员券
				couponAmount, err := strconv.ParseFloat(oSuccessCouponInfo.Amount, 64)
				if err != nil {
					return nil
				}
				if couponAmount > 0 {
					couponAmount = couponAmount / 100
					memberIcons := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-paid_member_icons")
					content = dcmp.TranslateTemplate(gjson.Get(discountTemplate, "paid_member_desc.content").String(),
						map[string]string{"fee": formatPrice(couponAmount)},
					)
					sIcon = gjson.Get(memberIcons, oSuccessCouponInfo.CustomTag).String()
				}
			} else {
				if isDiscountAmountShouldDisplay(p.GetDiscountFeeAmount()) {
					content = dcmp.TranslateTemplate(
						gjson.Get(discountTemplate, "diff_price_desc.content").String(),
						map[string]string{"fee": formatPrice(p.GetDiscountFeeAmount())},
					)
				}
				sIcon = gjson.Get(discountTemplate, "diff_price_desc.icon").String()
			}

			desc := &proto.PriceWithSingleDesc{
				FeeMsg: feeMsg,
			}
			if content != "" {
				desc.FeeDesc = &proto.TagWithIconAndBorder{
					Icon:        sIcon,
					Content:     content,
					BorderColor: gjson.Get(discountTemplate, "diff_price_desc.border_color").String(),
				}
			}

			result = append(result, desc)
		}
	}
	return result
}

// MultiPriceDescV2
//
// 实时单：                                           预约单：
//
//	  merge:                                             merge：
//		企业付： 拼一人价沟通0.0                                 企业付： 拼一人价沟通0.0
//		费用相同: 拼成一口价                                     费用相同: 拼成一口价
//		费用不同：拼一人价沟通  拼成最高XX                         费用不同：取拼一人价沟通 拼成最高XX
//	  normal:                                             normal：
//		企业付： 拼一人价沟通0.0                                 企业付： 拼一人价沟通0.0
//		费用相同: 拼成一口价                                     费用相同: 拼成一口价
//		费用不同：分别渲染                                       费用不同：取拼一人价沟通 拼成最高XX
func MultiPriceDescV2(ctx context.Context, prod render.BaseProvider, isBookingOrder bool, isMerge bool, prov ...PriceDescProvider) (result []*proto.PriceWithSingleDesc) {
	if len(prov) == 0 {
		return nil
	}

	// 拼到人数多的在前面
	sort.Slice(prov, func(i, j int) bool {
		return prov[i].GetExpectPoolNum() > prov[j].GetExpectPoolNum()
	})

	businessPay := false
	feeAllEqual := true
	onePoolProv := prov[0]

	fee := prov[0].GetEstimateFeeAmount()
	for _, p := range prov {
		if p.GetExpectPoolNum() > 0 { // 只处理 拼到人的
			if p.GetEstimateFeeAmount() != fee {
				feeAllEqual = false
			}
			if p.GetExpectPoolNum() == 1 {
				onePoolProv = p
			}
		}
		if p.GetPaymentType() == consts.BusinessPaymentType {
			businessPay = true
		}
	}

	result = make([]*proto.PriceWithSingleDesc, 0, len(prov))

	dcmpPath := "fee_config"
	if isBookingOrder {
		dcmpPath += "_booking"
	} else {
		dcmpPath += "_now"
	}

	if isMerge {
		dcmpPath += "_merge"
	}

	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheV2, dcmpPath)

	if businessPay {
		result = append(result, businessPayRender(ctx, onePoolProv, config["business_pay"].Map()))
		return result
	}

	if feeAllEqual {
		result = append(result, defaultRender(ctx, prod, onePoolProv, config["fee_equal"].Map()))
		return result
	}

	if isMerge || isBookingOrder {
		result = append(result, defaultRender(ctx, prod, onePoolProv, config["default"].Map()))
		return result
	}

	for _, p := range prov {
		result = append(result, defaultRender(ctx, prod, p, config["default"].Map()))
	}

	return result
}

func businessPayRender(ctx context.Context, prov PriceDescProvider, estimatePriceTemplate map[string]gjson.Result) *proto.PriceWithSingleDesc {
	feeMsg := dcmp.TranslateTemplate(
		*dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "fee_msg"),
		map[string]string{"fee": formatPrice(0.0)}, // 企业支付预估价展示为0.0元
	)

	content := ""
	sIcon := ""
	borderColor := ""
	textColor := ""

	if redPacketFee := prov.GetRedPacketFee(); redPacketFee > 0 {
		content = dcmp.TranslateTemplate(
			*dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "red_packet_content"),
			map[string]string{"fee": formatPrice(redPacketFee)},
		)
		sIcon = *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "red_packet_icon")
		borderColor = *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "red_packet_bc")
		textColor = *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "red_packet_tc")
	} else {
		content = dcmp.TranslateTemplate(
			*dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "business_desc"),
			map[string]string{"fee": formatPrice(prov.GetEstimateFeeAmount())},
		)
		sIcon = *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "icon")
		borderColor = *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "border_color")
		textColor = *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "text_color")
	}

	desc := &proto.PriceWithSingleDesc{
		FeeMsg: feeMsg,
		FeeDesc: &proto.TagWithIconAndBorder{
			Icon:        sIcon,
			Content:     content,
			BorderColor: borderColor,
			TextColor:   textColor,
		},
	}

	return desc
}

func defaultRender(ctx context.Context, prod render.BaseProvider, prov PriceDescProvider, estimatePriceTemplate map[string]gjson.Result) *proto.PriceWithSingleDesc {
	feeMsg := dcmp.TranslateTemplate(
		*dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "fee_msg"),
		map[string]string{
			"pool": strconv.Itoa(prov.GetExpectPoolNum()),
			"fee": carpool3.FormatPrice(prov.GetEstimateFeeAmount(), carpool3.PageDefault,
				util.ToString(prod.GetCityID()), util.Int642String(prod.GetUserPID()), -1).StringVal,
		},
	)

	desc := &proto.PriceWithSingleDesc{
		FeeMsg: feeMsg,
	}

	content := ""
	sIcon := ""

	if redPacketFee := prov.GetRedPacketFee(); redPacketFee > 0 {
		content = dcmp.TranslateTemplate(
			*dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "red_packet_content"),
			map[string]string{"fee": formatPrice(redPacketFee)},
		)
		sIcon = *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "red_packet_icon")
	} else if oSuccessCouponInfo := prov.GetCouponInfo(); oSuccessCouponInfo != nil && strings.Count(oSuccessCouponInfo.CustomTag, "member_v3") > 0 {
		//存在优惠券且优惠券为会员券
		couponAmount, err := strconv.ParseFloat(oSuccessCouponInfo.Amount, 64)
		if err != nil {
			return nil
		}
		if couponAmount > 0 {
			couponAmount = couponAmount / 100
			memberIcons := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-paid_member_icons")
			content = dcmp.TranslateTemplate(
				*dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "paid_member_content"),
				map[string]string{"fee": formatPrice(couponAmount)},
			)
			sIcon = gjson.Get(memberIcons, oSuccessCouponInfo.CustomTag).String()
		}
	} else {
		if isDiscountAmountShouldDisplay(prov.GetDiscountFeeAmount()) {
			content = dcmp.TranslateTemplate(
				*dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "diff_price_content"),
				map[string]string{"fee": formatPrice(prov.GetDiscountFeeAmount())},
			)
		}
		sIcon = *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "diff_price_icon")
	}

	if content != "" {
		desc.FeeDesc = &proto.TagWithIconAndBorder{
			Icon:        sIcon,
			Content:     content,
			BorderColor: *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "border_color"),
			TextColor:   *dcmp.GetStringDcmpContentByPath(estimatePriceTemplate, "text_color"),
		}
	}

	return desc
}

func isDiscountAmountShouldDisplay(amout float64) bool {
	const threshold = 0.05
	return amout >= threshold
}
