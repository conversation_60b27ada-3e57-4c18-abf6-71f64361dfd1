package pincheche

import (
	"context"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_fission/after_dds_fission"
	"math"
	"time"

	booking2 "git.xiaojukeji.com/gulfstream/mamba/render/private/pincheche/internal/booking"
	"git.xiaojukeji.com/lego/diconf-sdk-golang-v2/common/deepcopy"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
)

type CarpoolBookingProvider interface {
	GetDepartureTime() time.Time
	GetCityID() int
	GetCurSeatNum() int32
	GetToCityId() int32
	GetDriverMetre() int64
	GetTimeSpan() []*EstimateDecision.TimeSpanV2
	IsFission() bool
	ApolloParamProvider
}
type ConfirmButtonProvider interface {
	CarpoolBookingProvider
	IsBookingOrder() bool
}

// CarpoolBooking 时间片选择
func CarpoolBooking(ctx context.Context, prov CarpoolBookingProvider) *proto.CarpoolBookingModule {
	conf, err := getBookingConfigByCityIDWithDefault(ctx, prov.GetCityID(), 0)
	if err != nil {
		return nil
	}

	shadowConf := deepcopy.Copy(conf).(*booking2.TimeConfig)
	fixConf(ctx, prov, shadowConf)

	txtTemplate := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-booking_module")
	base := &proto.CarpoolBookingModule{
		Title:            gjson.Get(txtTemplate, "title").String(),
		Subtitle:         getSubTitle(txtTemplate, prov),
		AppointMaxDays:   int32(shadowConf.AppointMaxDays),
		BetweenMinutes:   int32(shadowConf.MinAdvanceMin),
		DepartMinMinutes: int32(shadowConf.DepartMinMinutes),
		BeginTimeRound:   int32(shadowConf.BeginTimeRound),
		TimeSpan: &proto.CarpoolBookingModuleTimeSpan{
			FromHour: int32(shadowConf.HourRange.From),
			ToHour:   int32(shadowConf.HourRange.To),
		},
		NeedConfirmTime: true,
	}

	// NeedConfirmTime
	departureTime := prov.GetDepartureTime()

	confRangeFrom := time.Date(departureTime.Year(), departureTime.Month(), departureTime.Day(), shadowConf.HourRange.From, 0, 0, 0, departureTime.Location())
	confRangeTo := time.Date(departureTime.Year(), departureTime.Month(), departureTime.Day(), shadowConf.HourRange.To, 0, 0, 0, departureTime.Location())

	if !departureTime.Before(confRangeFrom) && !departureTime.After(confRangeTo) {
		base.NeedConfirmTime = false
	}

	return base
}

func ConfirmButtonContent(ctx context.Context, prov ConfirmButtonProvider) string {
	txtTemplate := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-booking_module")

	if prov.IsBookingOrder() {
		return gjson.Get(txtTemplate, "confirm_button_text.booking").String()
	}

	conf, err := getBookingConfigByCityIDWithDefault(ctx, prov.GetCityID(), 0)
	if err != nil {
		return gjson.Get(txtTemplate, "confirm_button_text.normal").String()
	}

	shadowConf := deepcopy.Copy(conf).(*booking2.TimeConfig)
	fixConf(ctx, prov, shadowConf)

	departureTime := prov.GetDepartureTime()
	if departureTime.Hour() >= shadowConf.HourRange.From && departureTime.Hour() < shadowConf.HourRange.To {
		return gjson.Get(txtTemplate, "confirm_button_text.normal").String()
	}
	return gjson.Get(txtTemplate, "confirm_button_text.booking").String()
}

func getBookingConfigByCityIDWithDefault(ctx context.Context, cityID, defaultID int) (*booking2.TimeConfig, error) {
	var (
		conf *booking2.TimeConfig
		err  error
	)

	if conf, err = booking2.GetBookingConfigByCityID(ctx, cityID); err != nil {
		conf, err = booking2.GetBookingConfigByCityID(ctx, defaultID)
	}
	if err != nil {
		return nil, err
	}
	return conf, nil

}

func getSubTitle(txtTemplate string, prov CarpoolBookingProvider) string {
	status := GetNightCarpoolStateApollo(prov.ApolloParam())
	if status == "1" {
		seatNum := prov.GetCurSeatNum()
		if seatNum == 1 && disable1SeatDay(prov) {
			return gjson.Get(txtTemplate, "sub_title_disable_1_seat_day").String()
		} else if seatNum == 1 && disable1SeatNight(prov) {
			return gjson.Get(txtTemplate, "sub_title_disable_1_seat_night").String()
		} else if seatNum == 2 {
			return gjson.Get(txtTemplate, "sub_title_disable_2_seat").String()
		}
	}

	return gjson.Get(txtTemplate, "sub_title").String()
}

func disable1SeatDay(prov CarpoolBookingProvider) bool {
	// 白天冒泡
	if IsNight(time.Now()) {
		return false
	}
	// 距离跨城
	if prov.GetDriverMetre() > 30000 || prov.GetDriverMetre() == 0 || (int(prov.GetToCityId()) != prov.GetCityID()) {
		return true
	}

	return false
}

func disable1SeatNight(prov CarpoolBookingProvider) bool {
	// 夜间冒泡
	if !IsNight(time.Now()) {
		return false
	}
	// 距离跨城
	if prov.GetDriverMetre() == 0 || prov.GetDriverMetre() > 30000 || int(prov.GetToCityId()) != prov.GetCityID() {
		return true
	}

	return false
}

func fixConf(ctx context.Context, prov CarpoolBookingProvider, conf *booking2.TimeConfig) {
	state := GetNightCarpoolStateApollo(prov.ApolloParam())
	if state == "2" {
		conf.HourRange.From = 0
		conf.HourRange.To = 24
	} else if state == "1" {

		// 开城or超距
		if prov.GetDriverMetre() == 0 || prov.GetDriverMetre() > 30000 || (int(prov.GetToCityId()) != prov.GetCityID()) {
			return
		}

		if prov.GetCurSeatNum() > 1 {
			return
		}

		conf.HourRange.From = 0
		conf.HourRange.To = 24
	}

}

func CarpoolBookingOuter(ctx context.Context, prov CarpoolBookingProvider) []*proto.CarpoolLowPriceTimeRange {
	timeSpans := prov.GetTimeSpan()
	if len(timeSpans) == 0 {
		return nil
	}
	// 获取配置的外显时间片个数
	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheV2, DcmpPathBookingOuter)
	sliceNum := int(math.Min(3, dcmp.GetNumContentByPath(config, "time_slice_num")))
	if sliceNum == 0 {
		sliceNum = 1
	}
	// 是否是外显时间片中的时间
	isOuterSlice := false
	outerIdx := -1
	ourterTimes, _ := after_dds_fission.ConvertTimeSpan(timeSpans, sliceNum+1, 0)
	for _, timeSpan := range ourterTimes[1:] {
		if timeSpan == nil {
			continue
		}
		timeRange := util.StringTIntArray(timeSpan.Value)
		if len(timeRange) != 2 {
			continue
		}
		outerIdx++
		if prov.GetDepartureTime().After(time.Unix(timeRange[0], 0)) && prov.GetDepartureTime().Before(time.Unix(timeRange[1], 0)) ||
			prov.GetDepartureTime().Equal(time.Unix(timeRange[1], 0)) {
			isOuterSlice = true
			break
		}
	}

	var res []*proto.CarpoolLowPriceTimeRange
	// 选择其他出发时间 只展示一个
	if !prov.IsFission() && !isOuterSlice {
		res = buildOuterTimes(ctx, prov, 1, false)
	}

	// 按照配置的时间片个数给外显
	if len(res) == 0 {
		res = buildOuterTimes(ctx, prov, sliceNum, true)
	}

	// 处理时间片勾选状态
	// 裂变出来的 ｜ 非外显的时间片 ｜ index不合法 都默勾第一个时间片
	if (prov.IsFission() || !isOuterSlice || outerIdx < 0 || len(res) <= outerIdx) && len(res) > 0 && res[0] != nil {
		res[0].Selected = true
	} else if res[outerIdx] != nil {
		res[outerIdx].Selected = true
	}

	return res
}

func NewFormCarpoolBooking(ctx context.Context, prov CarpoolBookingProvider, isBooking bool) *proto.CarpoolLowPriceBookingModule {
	timeSpans := prov.GetTimeSpan()
	if len(timeSpans) == 0 {
		return nil
	}

	var departureTime int64
	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheV2, DcmpPathBookingModule)

	res := &proto.CarpoolLowPriceBookingModule{
		Title:      *dcmp.GetStringDcmpContentByPath(config, "title"),
		Subtitle:   *dcmp.GetStringDcmpContentByPath(config, "sub_title"),
		ButtonText: *dcmp.GetStringDcmpContentByPath(config, "button_text"),
	}

	if isBooking {
		departureTime = prov.GetDepartureTime().Unix()
	}

	_, timeSpanNew := after_dds_fission.ConvertTimeSpan(timeSpans, 0, departureTime)

	if !isBooking && len(timeSpanNew) > 0 && len(timeSpanNew[0].Time) > 0 {
		timeSpanNew[0].Time = timeSpanNew[0].Time[0:1]
		res.TimeSpan = timeSpanNew[:1]
	} else {
		res.TimeSpan = timeSpanNew
	}

	return res
}

func buildOuterTimes(ctx context.Context, prov CarpoolBookingProvider, sliceNum int, isFission bool) []*proto.CarpoolLowPriceTimeRange {
	timeSpans := prov.GetTimeSpan()
	res := make([]*proto.CarpoolLowPriceTimeRange, 0)
	for _, dayTime := range timeSpans {
		if dayTime == nil || len(dayTime.Time) == 0 {
			continue
		}
		for _, hourTime := range dayTime.Time {
			if hourTime == nil || len(hourTime.MinuteList) == 0 {
				continue
			}
			for _, minuTime := range hourTime.MinuteList {
				timeRange := util.StringTIntArray(minuTime.Value)
				if len(timeRange) != 2 {
					continue
				}

				if isFission {
					res = append(res, bulidOuterTimeSlice(ctx, dayTime, minuTime, timeRange))
				} else {
					minuTime.DefaultSelected = false
					if prov.GetDepartureTime().After(time.Unix(timeRange[0], 0)) && prov.GetDepartureTime().Before(time.Unix(timeRange[1], 0)) ||
						prov.GetDepartureTime().Equal(time.Unix(timeRange[1], 0)) {
						minuTime.DefaultSelected = true
						res = append(res, bulidOuterTimeSlice(ctx, dayTime, minuTime, timeRange))
						return res
					}
				}

				if isFission && len(res) == sliceNum+1 {
					return res[1:]
				}
			}
		}
	}
	if isFission && len(res) > 1 {
		return res[1:]
	}

	return res
}

func bulidOuterTimeSlice(ctx context.Context, dayTime *EstimateDecision.TimeSpanV2, minuTime *EstimateDecision.MinuteValue, timeRange []int64) *proto.CarpoolLowPriceTimeRange {
	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheV2, DcmpPathBookingOuter)

	timeRangeFormat := *dcmp.GetStringDcmpContentByPath(config, "time_range")
	skuMsgFormat := *dcmp.GetStringDcmpContentByPath(config, "sku_msg")
	buttonTextFormat := *dcmp.GetStringDcmpContentByPath(config, "button_text")

	replace := map[string]string{
		"date":       dayTime.Title,
		"left_time":  time.Unix(timeRange[0], 0).Format("15:04"),
		"right_time": time.Unix(timeRange[1], 0).Format("15:04"),
	}

	res := &proto.CarpoolLowPriceTimeRange{
		Value:      minuTime.Value,
		Msg:        util.ReplaceTag(ctx, skuMsgFormat, replace),
		OuterMsg:   util.ReplaceTag(ctx, timeRangeFormat, replace),
		OrderType:  minuTime.OrderType,
		ButtonText: util.ReplaceTag(ctx, buttonTextFormat, replace),
		Selected:   false,
	}

	return res
}
