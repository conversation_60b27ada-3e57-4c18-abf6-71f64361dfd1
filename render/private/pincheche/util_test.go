package pincheche

import "testing"

func Test_urlJoinParam(t *testing.T) {
	type args struct {
		url   string
		param map[string]string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "normal",
			args: args{
				url: "http://www.111.com",
				param: map[string]string{
					"name": "1",
					"last": "2",
				},
			},
			want: "http://www.111.com?last=2&name=1",
		},
		{
			name: "replace",
			args: args{
				url: "https://page.udache.com/passenger/apps/carpool-card-sell/index.html?carpool_card_pagetype=history&card_id=32603916423894589198",
				param: map[string]string{
					"name": "1",
					"last": "2",
				},
			},
			want: "https://page.udache.com/passenger/apps/carpool-card-sell/index.html?last=2&name=1",
		},
		{
			name: "invalid",
			args: args{
				url:   "qwer",
				param: nil,
			},
			want: "",
		},
		{
			name: "no param - nil",
			args: args{
				url:   "http://www.111.com",
				param: nil,
			},
			want: "http://www.111.com",
		},
		{
			name: "no param - empty",
			args: args{
				url:   "http://www.111.com",
				param: map[string]string{},
			},
			want: "http://www.111.com",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := urlJoinParam(tt.args.url, tt.args.param); got != tt.want {
				t.Errorf("urlJoinParam() = %v, want %v", got, tt.want)
			}
		})
	}
}
