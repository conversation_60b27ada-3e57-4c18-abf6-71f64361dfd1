package pincheche

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"github.com/tidwall/gjson"
)

type ExtraPriceTagProvider interface {
	GetEstimateFeeAmount() float64
	GetExpectPoolNum() int
	GetDcExtraInfo() string
	GetDisplayTag() string
	GetCustomTag() string
}

type DcExtraInfo struct {
	PricingTag string `json:"pricing_tag"`
}

func ExtraPriceTag(ctx context.Context, prov ...ExtraPriceTagProvider) string {
	if len(prov) == 0 {
		return ""
	}

	// 判断是否价格合并
	feeAllEqual := true
	fee := prov[0].GetEstimateFeeAmount()
	for _, p := range prov {
		if p.GetExpectPoolNum() > 0 { // 只处理 拼到人的
			if p.GetEstimateFeeAmount() != fee {
				feeAllEqual = false
			}
		}
	}

	if !feeAllEqual {
		return ""
	}

	discountTemplate := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-extra_price_tag")

	// 1. 判断是否有定价优惠
	if info := prov[0].GetDcExtraInfo(); info != "" {
		var dcExtraInfo DcExtraInfo

		if err := json.Unmarshal([]byte(info), &dcExtraInfo); err != nil {
			return ""
		}

		if dcExtraInfo.PricingTag != "" {
			if b := gjson.Get(discountTemplate, dcExtraInfo.PricingTag); b.Exists() {
				content := dcmp.TranslateTemplate(b.String(), nil)
				return content
			}
		}
	}

	// 2. 判断是否有sps活动
	if tag := prov[0].GetDisplayTag(); tag != "" {
		return prov[0].GetDisplayTag()
	}

	// 3. 判断是否有活动券
	//if tag := prov[0].GetCustomTag(); tag != "" {
	//	if b := gjson.Get(discountTemplate, tag); b.Exists() {
	//		content := dcmp.TranslateTemplate(b.String(), nil)
	//		return content
	//	}
	//}

	return ""
}
