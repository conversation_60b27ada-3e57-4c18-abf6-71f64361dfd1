package booking

import (
	"encoding/json"
	"testing"
)

func TestRange_UnmarshalJSON(t *testing.T) {
	type args struct {
		data []byte
	}
	tests := []struct {
		name     string
		expected Range
		args     args
		wantErr  bool
	}{
		{
			name: "1",
			expected: Range{
				From: 1,
				To:   2,
			},
			args: args{
				data: []byte("[1,2]"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rng := &Range{}
			if err := rng.UnmarshalJSON(tt.args.data); (err != nil) != tt.wantErr {
				t.<PERSON>("Range.UnmarshalJSON() error = %v, wantErr %v", err, tt.wantErr)
			}
			if *rng != tt.expected {
				t.<PERSON>rf("Range.UnmarshalJSON() expect %v, got %v", tt.expected, rng)
			}
		})
	}
}

func TestNested(t *testing.T) {
	type args struct {
		data []byte
	}
	type sss struct {
		HourRange Range `json:"range"`
	}
	tests := []struct {
		name     string
		expected sss
		args     args
		wantErr  bool
	}{
		{
			name: "1",
			expected: sss{
				HourRange: Range{
					From: 1,
					To:   2,
				},
			},
			args: args{
				data: []byte("{\"range\":[1,2]}"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := sss{}
			err := json.Unmarshal(tt.args.data, &s)
			if (err != nil) != tt.wantErr {
				t.Errorf("Range.UnmarshalJSON() error = %v, wantErr %v", err, tt.wantErr)
			}
			if s != tt.expected {
				t.Errorf("Range.UnmarshalJSON() expect %v, got %v", tt.expected, s)
			}
		})
	}
}
