package booking

import (
	"context"
	"sync"

	"git.xiaojukeji.com/nuwa/golibs/goutils"
	cmap "github.com/orcaman/concurrent-map"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
)

type tConfigContainer struct {
	store cmap.ConcurrentMap
}

func (con *tConfigContainer) storeOne(key string, val *TimeConfig) {
	con.store.Set(key, val)
}

func (con *tConfigContainer) getOne(key string) *TimeConfig {
	tmp, _ := con.store.Get(key)
	if tmp == nil {
		return nil
	}
	return tmp.(*TimeConfig)
}

func (con *tConfigContainer) clear() {
	con.store.Clear()
}

var vCache *tConfigContainer
var _cacheInitOnce sync.Once

func initCache(ctx context.Context) {
	vCache = &tConfigContainer{
		store: cmap.New(),
	}
	listener, _ := ApolloSDK.GetConfigNamespaceListener(NameSpace)
	if listener != nil {

		goutils.Go(ctx, func(ctx context.Context, args ...interface{}) {
			for {
				select {
				case <-listener:
					vCache.clear()
				}
			}
		})
	}
}

func getCache(ctx context.Context) *tConfigContainer {
	_cacheInitOnce.Do(func() {
		initCache(ctx)
	})
	return vCache
}
