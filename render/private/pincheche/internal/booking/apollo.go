package booking

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

const (
	NameSpace = "carpool_tab_pincheche_time_slice"
)

var (
	ErrNoThisCityConf  = errors.New("no conf")
	ErrConfParseFailed = errors.New("parse failed")
)

func getBookingConfigByCityIDDirect(ctx context.Context, cityID string) (*TimeConfig, error) {
	cond := ApolloModel.NewDefaultCondition().With("city_id", cityID)
	configs, err := ApolloSDK.GetConfigsByNamespaceAndConditions(NameSpace, cond)

	if err != nil {
		return nil, fmt.Errorf("%w %s", ErrNoThisCityConf, err.<PERSON><PERSON>r())
	}
	if configs.Size() == 0 {
		return nil, ErrNoThisCityConf
	}
	rawConf := configs.GetAllConfigs()[0].GetJsonConfigs()

	conf, err := parse(rawConf)
	if err != nil {
		return nil, fmt.Errorf("%w %s", ErrConfParseFailed, err.Error())
	}

	return conf.TimeConfig, nil
}

func GetBookingConfigByCityID(ctx context.Context, cityID int) (*TimeConfig, error) {
	city := strconv.Itoa(cityID)
	if cache := getCache(ctx); cache != nil {
		conf := cache.getOne(city)
		if conf != nil {
			return conf, nil
		}
		conf, err := getBookingConfigByCityIDDirect(ctx, city)
		if err != nil {
			return nil, err
		}
		cache.storeOne(city, conf)
		return conf, nil
	}
	return getBookingConfigByCityIDDirect(ctx, city)
}

func parse(raw map[string]interface{}) (*CityBookingConfig, error) {
	rawByte, _ := json.Marshal(raw)
	var result CityBookingConfig
	if err := json.Unmarshal(rawByte, &result); err != nil {
		return nil, err
	}
	return &result, nil
}
