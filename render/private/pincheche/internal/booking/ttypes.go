package booking

import (
	"encoding/json"
	"fmt"
)

type CityBookingConfig struct {
	CityID     int         `json:"city_id"`     // 所在城市
	TimeConfig *TimeConfig `json:"time_config"` // 时间配置
}

type TimeConfig struct {
	AppointMaxDays   int   `json:"appoint_max_days"`   // 最多预约天数
	HourRange        Range `json:"hour_range"`         // 每天开放时间段(小时)
	DepartMinMinutes int   `json:"depart_min_minutes"` // 时间缓冲
	BeginTimeRound   int   `json:"begin_time_round"`   // 开始时间的对齐标准
	MinAdvanceMin    int   `json:"min_advance_min"`    // 时间段长度
}

var _ json.Unmarshaler = &Range{}

type Range struct {
	From int
	To   int
}

func (rng *Range) UnmarshalJSON(data []byte) error {
	var tmp []int
	if err := json.Unmarshal(data, &tmp); err != nil {
		return err
	}
	if len(tmp) < 2 {
		return fmt.Errorf("range no enough bound")
	}
	rng.From = tmp[0]
	rng.To = tmp[1]
	return nil
}
