package get_dynamic_fee_desc_list

import (
	"context"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/dynamic_fee_desc_list/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/ab"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/util"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

// 首先，让我们模拟一个足够简单的Quotation实现，专注于测试IsHitDynamicIconAb方法
type mockProvider struct {
	pid         int64
	uid         int64
	cityID      int
	toCityID    int
	menuID      string
	lang        string
	accessKeyID int32
	appVersion  string
	phone       string
	county      int32
	prodCat     int64
	fontScale   int32
}

func (m *mockProvider) GetPID() int64             { return m.pid }
func (m *mockProvider) GetUID() int64             { return m.uid }
func (m *mockProvider) GetCityID() int            { return m.cityID }
func (m *mockProvider) GetToCityID() int          { return m.toCityID }
func (m *mockProvider) GetFromCounty() int32      { return m.county }
func (m *mockProvider) GetMenuId() string         { return m.menuID }
func (m *mockProvider) GetLang() string           { return m.lang }
func (m *mockProvider) GetAccessKeyId() int32     { return m.accessKeyID }
func (m *mockProvider) GetAppVersion() string     { return m.appVersion }
func (m *mockProvider) GetUserPhone() string      { return m.phone }
func (m *mockProvider) GetProductCategory() int64 { return m.prodCat }
func (m *mockProvider) GetFontStyleType() int32   { return m.fontScale }

// TestProductFullIsHitDynamicIconAb 测试IsHitDynamicIconAb方法
func TestProductFullIsHitDynamicIconAb(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()

	// 模拟 Quotation
	quotation := &biz_runtime.Quotation{}

	// 模拟Quotation的方法
	mockGetProductIdPatch := mockey.Mock((*biz_runtime.Quotation).GetProductId).Return(int64(11)).Build()
	defer mockGetProductIdPatch.UnPatch()

	mockGetProductCategoryPatch := mockey.Mock((*biz_runtime.Quotation).GetProductCategory).Return(int64(100)).Build()
	defer mockGetProductCategoryPatch.UnPatch()

	mockGetCityIdPatch := mockey.Mock((*biz_runtime.Quotation).GetCityID).Return(1).Build()
	defer mockGetCityIdPatch.UnPatch()

	mockGetToCityIdPatch := mockey.Mock((*biz_runtime.Quotation).GetToCityID).Return(2).Build()
	defer mockGetToCityIdPatch.UnPatch()

	// 模拟UserInfo
	mockUserInfo := &data.UserInfo{
		UID:   12345,
		PID:   6789,
		Phone: "13800138000",
	}

	// 设置可选字段的值
	fontScaleType := int32(2)
	formStyleExp := int32(1)

	mockRequest := &proto.DynamicFeeDescListRequest{
		AccessKeyId:   1,
		Lang:          "zh-CN",
		AppVersion:    "7.0.16",
		FormType:      5,
		ProductList:   "",
		FontScaleType: &fontScaleType,
		FormStyleExp:  &formStyleExp,
	}

	// 创建一个能转为*biz_runtime.Quotation的Quotation
	quotationMap := map[string]*biz_runtime.Quotation{
		"test": quotation,
	}

	mockAdapter := data.NewDynamicFeeDescListAdapter(quotationMap, mockUserInfo, mockRequest)

	// 创建productFull
	pf := NewProductFull(quotation, mockUserInfo, mockAdapter)

	t.Run("当ab.IsHitDynamicIconAb返回true时", func(t *testing.T) {
		// 模拟ab.IsHitDynamicIconAb返回true
		mockIsHitDynamicIconAb := mockey.Mock(ab.IsHitDynamicIconAb).Return(true).Build()
		defer mockIsHitDynamicIconAb.UnPatch()

		// 调用测试方法
		result := pf.IsHitDynamicIconAb(ctx)

		// 断言结果
		assert.True(t, result, "当ab.IsHitDynamicIconAb返回true时，IsHitDynamicIconAb方法应返回true")

		// 验证ab.IsHitDynamicIconAb被调用的次数
		assert.True(t, mockIsHitDynamicIconAb.Times() > 0, "应该调用ab.IsHitDynamicIconAb至少一次")

		// 验证调用次数
		assert.Equal(t, 1, mockIsHitDynamicIconAb.Times(), "应该调用一次ab.IsHitDynamicIconAb")
	})

	t.Run("当ab.IsHitDynamicIconAb返回false时", func(t *testing.T) {
		// 模拟ab.IsHitDynamicIconAb返回false
		mockIsHitDynamicIconAb := mockey.Mock(ab.IsHitDynamicIconAb).Return(false).Build()
		defer mockIsHitDynamicIconAb.UnPatch()

		// 调用测试方法
		result := pf.IsHitDynamicIconAb(ctx)

		// 断言结果
		assert.False(t, result, "当ab.IsHitDynamicIconAb返回false时，IsHitDynamicIconAb方法应返回false")
	})
}

// TestProductFullApolloParamsGen 测试productFull结构体获取Apollo参数的方法
func TestProductFullApolloParamsGen(t *testing.T) {
	// 准备测试数据
	// 模拟 Quotation
	quotation := &biz_runtime.Quotation{}

	// 模拟Quotation的方法
	mockGetProductIdPatch := mockey.Mock((*biz_runtime.Quotation).GetProductId).Return(int64(11)).Build()
	defer mockGetProductIdPatch.UnPatch()

	mockGetProductCategoryPatch := mockey.Mock((*biz_runtime.Quotation).GetProductCategory).Return(int64(100)).Build()
	defer mockGetProductCategoryPatch.UnPatch()

	mockGetCityIdPatch := mockey.Mock((*biz_runtime.Quotation).GetCityID).Return(1).Build()
	defer mockGetCityIdPatch.UnPatch()

	mockGetToCityIdPatch := mockey.Mock((*biz_runtime.Quotation).GetToCityID).Return(2).Build()
	defer mockGetToCityIdPatch.UnPatch()

	// 模拟UserInfo
	mockUserInfo := &data.UserInfo{
		UID:   12345,
		PID:   6789,
		Phone: "13800138000",
	}

	// 设置可选字段的值
	fontScaleType := int32(2)
	formStyleExp := int32(1)

	mockRequest := &proto.DynamicFeeDescListRequest{
		AccessKeyId:   1,
		Lang:          "zh-CN",
		AppVersion:    "7.0.16",
		FormType:      5,
		ProductList:   "",
		FontScaleType: &fontScaleType,
		FormStyleExp:  &formStyleExp,
	}

	// 创建一个能转为*biz_runtime.Quotation的Quotation
	quotationMap := map[string]*biz_runtime.Quotation{
		"test": quotation,
	}

	mockAdapter := data.NewDynamicFeeDescListAdapter(quotationMap, mockUserInfo, mockRequest)

	// 创建productFull
	pf := NewProductFull(quotation, mockUserInfo, mockAdapter)

	// 模拟keyFunc
	keyFunc := func(param *apollo_model.ParamsConnector) string {
		return "test_key"
	}

	// 模拟paramsFunc
	paramsFunc := func(param *apollo_model.ParamsConnector) (key, value string) {
		return "param_key", "param_value"
	}

	// 模拟apollo_model.ParamsConnector.ApolloParamsGen
	mockApolloParamsGen := mockey.Mock((*apollo_model.ParamsConnector).ApolloParamsGen).Return("test_key", map[string]string{
		"param_key": "param_value",
	}).Build()
	defer mockApolloParamsGen.UnPatch()

	// 调用测试方法
	key, params := pf.ApolloParamsGen(keyFunc, paramsFunc)

	// 断言结果
	assert.Equal(t, "test_key", key, "应返回正确的key")
	assert.Equal(t, "param_value", params["param_key"], "应返回正确的params")

	// 验证ApolloParamsGen方法被调用
	assert.True(t, mockApolloParamsGen.Times() > 0, "应该调用ParamsConnector.ApolloParamsGen方法至少一次")
}

func TestDynamicFeeDescList_BattleHarbour(t *testing.T) {
	ctx := context.Background()

	// 构造FeeOutput，Type分别为隧道费、减价项、中性项
	outputs := map[int64][]*engine_model.FeeOutput{
		1: {
			{
				Type:        consts.TypeIncrement,
				Content:     "隧道费A",
				Fee:         &engine_model.FeeDetail{Amount: 10},
				BorderColor: "red",
			},
			{
				Type:        consts.TypeDecrement,
				Content:     "减价项A",
				Fee:         &engine_model.FeeDetail{Amount: 5},
				BorderColor: "blue",
			},
			{
				Type:        consts.TypeNeutral,
				Content:     "中性项A",
				Fee:         &engine_model.FeeDetail{Amount: 2},
				BorderColor: "green",
			},
		},
		2: {
			{
				Type:        consts.TypeNeutral,
				Content:     "隧道费B",
				Fee:         &engine_model.FeeDetail{Amount: 5},
				BorderColor: "red",
			},
			{
				Type:        consts.TypeDecrement,
				Content:     "减价项B",
				Fee:         &engine_model.FeeDetail{Amount: 8},
				BorderColor: "blue",
			},
			{
				Type:        consts.TypeNeutral,
				Content:     "中性项B",
				Fee:         &engine_model.FeeDetail{Amount: 3},
				BorderColor: "green",
			},
		},
	}

	adapter := &data.DynamicFeeDescListAdapter{}
	d := NewDynamicFeeDescList(adapter, false)

	// mock feedescutil.GetFeeDescFromDcmp 返回固定desc
	patch := mockey.Mock(util.GetFeeDescFromDcmp).Return(&proto.NewFormFeeDesc{Content: "详见内页", Amount: 1}).Build()
	defer patch.UnPatch()

	result := d.BattleHarbour(ctx, outputs)
	assert.NotNil(t, result)
	assert.NotNil(t, result.FeeDescList)
	assert.True(t, len(result.FeeDescList) <= 2)
	// 检查第一个desc内容为mock的"详见内页"
	assert.Equal(t, "详见内页", result.FeeDescList[0].Content)
}

//func TestDynamicFeeDescList_GetFeeDescList_HKBranch(t *testing.T) {
//	ctx := context.Background()
//
//	p := mockey.Mock(log.Trace.Warnf).Return().Build()
//	defer p.UnPatch()
//
//	// 构造Quotation，ProductId为香港品类
//	quotation := &biz_runtime.Quotation{}
//	mockGetProductIdPatch := mockey.Mock((*biz_runtime.Quotation).GetProductId).Return(int64(99999)).Build()
//	defer mockGetProductIdPatch.UnPatch()
//	mockGetProductCategoryPatch := mockey.Mock((*biz_runtime.Quotation).GetProductCategory).Return(int64(99999)).Build()
//	defer mockGetProductCategoryPatch.UnPatch()
//
//	// mock IsHongKongProduct 返回true
//	patch := mockey.Mock(productutil.IsHongKongProduct).Return(true).Build()
//	defer patch.UnPatch()
//
//	userInfo := &data.UserInfo{UID: 1, PID: 2, Phone: "13800000000"}
//	formType := int32(consts.HarbourForm)
//	req := &proto.DynamicFeeDescListRequest{
//		FormType: formType,
//	}
//	quotationMap := map[string]*biz_runtime.Quotation{"hk": quotation}
//	adapter := data.NewDynamicFeeDescListAdapter(quotationMap, userInfo, req)
//	d := NewDynamicFeeDescList(adapter, false)
//
//	// mock input.BuildHkProductFeeInputByQuotation 返回FeeInput
//	mockey.Mock(input.BuildHkProductFeeInputByQuotation).Return(&engine_model.FeeInput{
//		FeeDetailInfo: map[string]float64{"hk_dynamic_price": 1.1},
//		DiscountInfo:  map[string]float64{"hk_coupon": 2.2},
//		FeeExtraInfo:  map[string]float64{"hk_tunnel_fee": 3.3},
//	}).Build()
//
//	// mock feedescutil.GetFeeDescFromDcmp 返回固定desc
//	mockey.Mock(util.GetFeeDescFromDcmp).Return(&proto.NewFormFeeDesc{Content: "详见内页", Amount: 1}).Build()
//
//	result := d.GetFeeDescList(ctx)
//	assert.NotNil(t, result)
//	assert.NotNil(t, result.FeeDescList)
//	assert.True(t, len(result.FeeDescList) > 0)
//	assert.Equal(t, "详见内页", result.FeeDescList[0].Content)
//}
