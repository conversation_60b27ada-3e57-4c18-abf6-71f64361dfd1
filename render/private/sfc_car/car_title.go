// Package sfc_car
package sfc_car

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
)

// CarTitleProvider 获取车辆标题
type CarTitleProvider interface {
	GetProductCategory() int64
}

// NSMaterial NSMaterial
const NSMaterial = "estimate_formdata_materiel"

// GetCarTitleIcon 获取车类型跟图片
func GetCarTitleIcon(ctx context.Context, prov CarTitleProvider) (string, string) {
	var (
		carName string
		carIcon string
	)

	m := map[string]string{"product_category": strconv.Itoa(int(prov.GetProductCategory())), "lang": reqctx.GetLang(ctx)}
	configs, err := apollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, NSMaterial, m, reqctx.GetLang(ctx))
	if configs == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo estimate_formdata_materiel read err with %v and conf %v", err, configs)
		return carName, carIcon
	}

	if len(configs) > 0 {
		if carName, err = configs[0].GetStringValue("intro_msg"); err != nil {
			carName = ""
		}
		if carIcon, err = configs[0].GetStringValue("car_icon"); err != nil {
			carIcon = ""
		}
	}

	return carName, carIcon
}
