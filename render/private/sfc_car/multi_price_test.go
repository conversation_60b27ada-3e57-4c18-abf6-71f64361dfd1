package sfc_car

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"testing"
)

func TestMultiPrice(t *testing.T) {
	ctx := context.Background()
	var dcmpData proto.SFCEstimateDcmp
	dcmpStr := getDcmp()
	_ = json.Unmarshal([]byte(dcmpStr), &dcmpData)
	tests := []struct {
		name                string
		productCategory     int64
		integerPriceAware   bool
		extraParams         map[string]string
		estimateFeeList     []float64
		originalPriceList   []float64
		prov                MultiPriceProvider
		dcmpData            proto.SFCEstimateDcmp
		eid                 string
		newPriceSwitch      bool
		doubleSwitch        bool
		passengerCount      int32
		newFenceStyleSwitch bool
	}{
		{
			name:              "单边围栏一口价",
			productCategory:   4001,
			integerPriceAware: true,
			extraParams: map[string]string{
				"fencePriceId":       "140737488393044",
				"successEstimateFee": "103",
				"failEstimateFee":    "110",
				"estimateNotEqual":   "1",
			},
			estimateFeeList:     []float64{103, 110},
			originalPriceList:   []float64{130, 150},
			prov:                nil,
			dcmpData:            dcmpData,
			eid:                 "d3accdaf122d84f2323f227ea23236ad",
			newPriceSwitch:      true,
			doubleSwitch:        true,
			passengerCount:      1,
			newFenceStyleSwitch: true,
		},
		{
			name:              "单边围栏一口价价格兜底",
			productCategory:   4001,
			integerPriceAware: true,
			extraParams: map[string]string{
				"fencePriceId":       "140737488393044",
				"successEstimateFee": "103",
				"failEstimateFee":    "110",
				"estimateNotEqual":   "1",
			},
			estimateFeeList:     []float64{103, 110},
			originalPriceList:   []float64{0, 0},
			prov:                nil,
			dcmpData:            dcmpData,
			eid:                 "d3accdaf122d84f2323f227ea23236ad",
			newPriceSwitch:      true,
			doubleSwitch:        true,
			passengerCount:      1,
			newFenceStyleSwitch: true,
		},
		{
			name:                "不命中单边围栏一口价",
			newFenceStyleSwitch: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			BuildNewFenceStyle(ctx, tt.productCategory, tt.integerPriceAware, tt.extraParams, tt.estimateFeeList, tt.originalPriceList, tt.dcmpData, tt.eid, tt.passengerCount, tt.newFenceStyleSwitch)
		})
	}
}

func TestBuildSpsDiscount(t *testing.T) {
	tests := []struct {
		name              string
		extraParams       map[string]string
		originalPrice     *proto.SFCPriceRollInfo
		failOriginalPrice *proto.SFCPriceRollInfo
		configDcmp        *DCMPForCouponStruct
		couponDiscount    float64
	}{
		{
			name: "气泡展示",
			extraParams: map[string]string{
				"newFenceStyle": NewFenceStyle,
			},
			originalPrice: &proto.SFCPriceRollInfo{
				PriceFrom: "110",
				PriceTo:   "90",
			},
			failOriginalPrice: &proto.SFCPriceRollInfo{
				PriceFrom: "150",
				PriceTo:   "100",
			},
			configDcmp: &DCMPForCouponStruct{
				SpsDiscount: DCMPForButtonCouponStruct{
					NewTitle: "{color=#ffffff font=3 size=10 text=超值路线{{spsAmount}}折}",
					Title:    "{color=#ffffff font=3 size=10 text=超值路线-{{spsAmount}}元}",
				},
			},
			couponDiscount: 5,
		},
		{
			name: "气泡展示,价格兜底",
			extraParams: map[string]string{
				"newFenceStyle": NewFenceStyle,
			},
			originalPrice: &proto.SFCPriceRollInfo{
				PriceFrom: "110",
				PriceTo:   "110",
			},
			failOriginalPrice: &proto.SFCPriceRollInfo{
				PriceFrom: "150",
				PriceTo:   "150",
			},
			configDcmp: &DCMPForCouponStruct{
				SpsDiscount: DCMPForButtonCouponStruct{
					NewTitle: "{color=#ffffff font=3 size=10 text=超值路线{{spsAmount}}折}",
					Title:    "{color=#ffffff font=3 size=10 text=超值路线-{{spsAmount}}元}",
				},
			},
			couponDiscount: 5,
		},
		{
			name: "气泡展示旧样式",
			extraParams: map[string]string{
				"newFenceStyle": OldFenceStyle,
			},
			originalPrice: &proto.SFCPriceRollInfo{
				PriceFrom: "110",
				PriceTo:   "90",
			},
			failOriginalPrice: &proto.SFCPriceRollInfo{
				PriceFrom: "150",
				PriceTo:   "100",
			},
			configDcmp: &DCMPForCouponStruct{
				SpsDiscount: DCMPForButtonCouponStruct{
					NewTitle: "{color=#ffffff font=3 size=10 text=超值路线{{spsAmount}}折}",
					Title:    "{color=#ffffff font=3 size=10 text=超值路线-{{spsAmount}}元}",
				},
			},
			couponDiscount: 5,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			BuildSpsDiscount(tt.extraParams, tt.originalPrice, tt.failOriginalPrice, tt.configDcmp, tt.couponDiscount)
		})
	}
}

func TestApollo(t *testing.T) {
	tests := []struct {
		name    string
		request proto.SFCEstimateRequest
		pid     uint64
	}{
		{
			name: "apollo",
			pid:  17592188021983,
			request: proto.SFCEstimateRequest{
				FromArea:    1,
				AccessKeyId: 1,
				AppVersion:  "7.0.0",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			GetNewFenceStyleSwitch(tt.request, tt.pid)
		})
	}
}

func getDcmp() string {
	dcmpStr := `
{
  "price_info": {
    "4000_new_fence_style": {
      "sub_title": "不拼人，舒适省时",
      "price_title": "{color=#F46B23 font=2 size=12 text=超值}{color=#F46B23 font=6 size=30 text={{amount}}}{color=#F46B23 font=2 size=12 text=元}",
      "price_sub_title": ""
    },
    "4001_new_fence_style": {
      "sub_title": "超低价，低碳环保",
      "price_title": "{color=#F46B23 font=2 size=12 text=拼成超值}{color=#F46B23 font=6 size=30 text={{amount}}}{color=#F46B23 font=2 size=12 text=元}",
      "price_sub_title": "拼不成超值{color=#242424 font=5 size=18 text={{amount}}}元"
    },
    "4002_new_fence_style": {
      "sub_title": "超低价，低碳环保",
      "price_title": "{color=#F46B23 font=2 size=12 text=拼成超值}{color=#F46B23 font=6 size=30 text={{amount}}}{color=#F46B23 font=2 size=12 text=元}",
      "price_sub_title": "拼不成超值{color=#242424 font=5 size=18 text={{amount}}}元"
    },
    "4000_new_fence_style_price_guarantee": {
      "sub_title": "不拼人，舒适省时",
      "price_title": "一口价{color=#F46B23 font=6 size=30 text={{amount}}}{color=#F46B23 font=2 size=12 text=元}",
      "price_sub_title": ""
    },
    "4001_new_fence_style_price_guarantee": {
      "sub_title": "超低价，低碳环保",
      "price_title": "拼成一口价{color=#F46B23 font=6 size=30 text={{amount}}}{color=#F46B23 font=2 size=12 text=元}",
      "price_sub_title": "拼不成{color=#242424 font=6 size=18 text={{amount}}}元"
    },
    "4002_new_fence_style_price_guarantee": {
      "sub_title": "超低价，低碳环保",
      "price_title": "拼成一口价{color=#F46B23 font=6 size=30 text={{amount}}}{color=#F46B23 font=2 size=12 text=元}",
      "price_sub_title": "拼不成{color=#242424 font=6 size=18 text={{amount}}}元"
    }
  },
  "roll_info": {
    "4000_new_fence_style": {
      "text_color": "#F46B23",
      "title_pre": "超值",
      "title_end": "元"
    },
    "4001_new_fence_style": {
      "text_color": "#F46B23",
      "title_pre": "拼成超值",
      "title_end": "元",
      "sub_text_color": "#242424",
      "sub_title_pre": "拼成超值",
      "sub_title_end": "元"
    },
    "4002_new_fence_style": {
      "text_color": "#F46B23",
      "title_pre": "拼成超值",
      "title_end": "元",
      "sub_text_color": "#242424",
      "sub_title_pre": "拼成超值",
      "sub_title_end": "元"
    },
    "4000_new_fence_style_price_guarantee": {
      "text_color": "#F46B23",
      "title_pre": "一口价",
      "title_end": "元"
    },
    "4001_new_fence_style_price_guarantee": {
      "text_color": "#F46B23",
      "title_pre": "拼成一口价",
      "title_end": "元",
      "sub_text_color": "#242424",
      "sub_title_pre": "拼成一口价",
      "sub_title_end": "元"
    },
    "4002_new_fence_style_price_guarantee": {
      "text_color": "#F46B23",
      "title_pre": "拼成一口价",
      "title_end": "元",
      "sub_text_color": "#242424",
      "sub_title_pre": "拼成一口价",
      "sub_title_end": "元"
    }
  },
  "sub_roll_info": {
    "4000_new_fence_style": {
      "text_color": "#242424",
      "title_pre": "超值",
      "title_end": "元"
    },
    "4001_new_fence_style": {
      "text_color": "#242424",
      "title_pre": "拼不成超值",
      "title_end": "元",
      "sub_text_color": "#242424",
      "sub_title_pre": "拼不成超值",
      "sub_title_end": "元"
    },
    "4002_new_fence_style": {
      "text_color": "#242424",
      "title_pre": "拼不成超值",
      "title_end": "元",
      "sub_text_color": "#242424",
      "sub_title_pre": "拼不成超值",
      "sub_title_end": "元"
    },
    "4000_new_fence_style_price_guarantee": {
      "text_color": "#242424",
      "title_pre": "一口价",
      "title_end": "元"
    },
    "4001_new_fence_style_price_guarantee": {
      "text_color": "#242424",
      "title_pre": "拼不成",
      "title_end": "元",
      "sub_text_color": "#242424",
      "sub_title_pre": "拼不成",
      "sub_title_end": "元"
    },
    "4002_new_fence_style_price_guarantee": {
      "text_color": "#242424",
      "title_pre": "拼不成",
      "title_end": "元",
      "sub_text_color": "#242424",
      "sub_title_pre": "拼不成",
      "sub_title_end": "元"
    }
  }
}`
	return dcmpStr
}
