// Package sfc_car
package sfc_car

import (
	"context"
)

// SelectionProvider 获取用户选择品类
type SelectionProvider interface {
	GetProductCategory() int64
	GetIsSelected() int32
	HasMultiRequireProduct() int32
}

// GetSelection 获取上次选择
func GetSelection(ctx context.Context, prov SelectionProvider) int32 {
	// 优先使用用户上次勾选态
	if prov.HasMultiRequireProduct() == 1 {
		return prov.GetIsSelected()
	}

	return 0
}
