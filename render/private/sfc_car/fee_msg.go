// Package sfc_car
package sfc_car

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

// FeeMsgProvider FeeMsgProvider
type FeeMsgProvider interface {
	GetEstimateFee() float64
}

// GetFeeMsg 获取价格文案
func GetFeeMsg(ctx context.Context, prov FeeMsgProvider) string {
	estimateFee := prov.GetEstimateFee()
	tag := map[string]string{
		"amount": util.ToString(estimateFee),
	}

	return dcmp.GetDcmpContent(ctx, "sfc-fee_msg", tag)
}
