// Package sfc_car
package sfc_car

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/beatles-common/constant"
	apollo2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/nuwa/trace"
	ApolloSDKV2 "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"math"
	"strings"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/coupon"
	BeatlesUtil "git.xiaojukeji.com/gulfstream/beatles-common/util"

	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	BeatlesPrice "git.xiaojukeji.com/gulfstream/beatles-common/model/price"

	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	richText       = "_rich"
	crossCityText  = "_cross_city"
	intAware       = "_with_int"
	rollPrice      = "_roll"
	LOG_TAG        = "sfc"
	doublePrice    = "_double"
	newFenceStyle  = "_new_fence_style"
	priceGuarantee = "_price_guarantee"

	OldFenceStyle = "0" // 旧围栏样式
	NewFenceStyle = "1" // 新围栏样式
)

// MultiPriceProvider 获取品类价格
type MultiPriceProvider interface {
	GetMultiPrice() []float64
	GetProductCategory() int64
	ShowAnimation() bool
	GetExtraInfo() []map[string]interface{}
	GetOriginalPriceInfo() []float64
	GetPriceRollSwitch() int32
	render.ApolloProvider
}

type DCMPForButtonCouponStruct struct {
	Title           string   `json:"title"`
	NewTitle        string   `json:"new_title"`
	Icon            string   `json:"icon"`
	Type            string   `json:"type"`
	BackgroundColor []string `json:"background_colors"`
	BorderColor     string   `json:"border_color"`
	TitleColor      string   `json:"title_color"`
}
type DCMPForCouponStruct struct {
	Title           string                    `json:"title"`
	Icon            string                    `json:"icon"`
	Type            string                    `json:"type"`
	BackgroundColor []string                  `json:"background_colors"`
	TitleOld        string                    `json:"title_oid"`
	CustomTag       DCMPForButtonCouponStruct `json:"custom_tag"`      //整合营销
	SelfCouponTag   DCMPForButtonCouponStruct `json:"self_coupon_tag"` //券套餐
	SpsDiscount     DCMPForButtonCouponStruct `json:"sps_discount"`    //围栏减免
	DoubleStyle     DCMPForButtonCouponStruct `json:"double_style"`    //多框样式
}

// GetEstimatePriceInfo 是否跨城 拼座、独享 四种组合
func GetEstimatePriceInfo(ctx context.Context, prov MultiPriceProvider, dcmpData proto.SFCEstimateDcmp, isCrossCity int8, eid string, estimateAbGroup string, newPriceSwitch, doubleSwitch bool, passengerCount int32, extraParams map[string]string, newFenceStyleSwitch bool) *proto.PriceInfo {
	var (
		estimateFeeList    []float64
		icon               string
		title              string
		subTitle           string
		successEstimateFee string
		key                string
		titleRollInfo      *proto.SFCPriceRollInfo
		subtitleRollInfo   *proto.SFCPriceRollInfo
		rollEnable         int32
		doubleEnable       int32
	)
	extraInfo := map[string]string{}
	// 第一个是拼成预估价，第二个是拼不成预估价
	estimateFeeList = prov.GetMultiPrice()
	icon = dcmpData.PriceIcon
	productCategory := prov.GetProductCategory()
	productCategoryStr := strconv.FormatInt(productCategory, 10)
	originalPriceList := prov.GetOriginalPriceInfo()
	priceRollSwitch := prov.GetPriceRollSwitch()
	integerPriceAware := IntegerPriceAware(ctx, prov, estimateFeeList)
	// 展示单边围栏样式
	newPriceInfo := BuildNewFenceStyle(ctx, productCategory, integerPriceAware, extraParams, estimateFeeList, originalPriceList, dcmpData, eid, passengerCount, newFenceStyleSwitch)
	if newPriceInfo != nil {
		return newPriceInfo
	}
	if isCrossCity == 1 {
		key = productCategoryStr + crossCityText
	} else {
		key = productCategoryStr
	}

	allow, _ := RegionAnimationSupport(prov)

	if prov.ShowAnimation() && allow {
		key += richText
	}

	if integerPriceAware {
		key += intAware
	}
	//已全量
	if newPriceSwitch {
		key += rollPrice
	}
	if newPriceSwitch && doubleSwitch {
		key += doublePrice
		doubleEnable = 1
	}
	if !newPriceSwitch || estimateAbGroup != constant.TreatmentGroup {
		priceRollSwitch = 0
	}

	successEstimateFee = fmt.Sprintf("%.2f", estimateFeeList[0])
	if successFee, ok := getIntegerPrice(estimateFeeList[0]); integerPriceAware && ok {
		successEstimateFee = successFee
	}
	title = dcmp.TranslateTemplate(dcmpData.PriceInfo[key].PriceTitle, map[string]string{"amount": successEstimateFee})
	if newPriceSwitch && doubleSwitch {
		integerNum, decimalNum := getPriceIntegerAndDecimal(estimateFeeList[0])
		title = dcmp.TranslateTemplate(dcmpData.PriceInfo[key].PriceTitle, map[string]string{"amount": integerNum, "decimal_price": decimalNum})
		if decimalNum != "" {
			title += dcmp.TranslateTemplate(*dcmpData.DecimalText, map[string]string{"decimal_price": decimalNum})
		}
		title += *dcmpData.EndText
	}
	// 单边围栏一口价也要滚动展示
	if extraParams["fencePriceId"] != "" && newFenceStyleSwitch {
		priceRollSwitch = 1
	}
	if priceRollSwitch == 1 && originalPriceList != nil && len(originalPriceList) > 0 && dcmpData.RollInfo != nil && originalPriceList[0] > 0 && originalPriceList[0] > cast.ToFloat64(successEstimateFee) {
		if rollInfo, ok := dcmpData.RollInfo[productCategoryStr]; ok {
			priceFrom := cast.ToString(originalPriceList[0])
			priceTo := successEstimateFee
			if newPriceSwitch && doubleSwitch {
				priceFrom = getPriceInteger(originalPriceList[0])
				priceTo = getPriceInteger(cast.ToFloat64(successEstimateFee))
			}
			rollEnable = 1
			titleRollInfo = &proto.SFCPriceRollInfo{
				TextColor: rollInfo.TextColor,
				TitlePre:  rollInfo.TitlePre,
				TitleEnd:  rollInfo.TitleEnd,
				PriceFrom: priceFrom,
				PriceTo:   priceTo,
			}
			extraInfo["original_price"] = cast.ToString(originalPriceList[0])
		}
	}

	if len(estimateFeeList) == 2 { //有拼座价格
		failEstimateFee := fmt.Sprintf("%.2f", estimateFeeList[1])
		if failFee, ok := getIntegerPrice(estimateFeeList[1]); integerPriceAware && ok {
			failEstimateFee = failFee
		}
		if successEstimateFee != failEstimateFee {
			if newPriceSwitch && doubleSwitch {
				failEstimateFee = getPriceInteger(estimateFeeList[1])
			}
			subTitle = dcmp.TranslateTemplate(dcmpData.PriceInfo[key].PriceSubTitle, map[string]string{"amount": failEstimateFee})
		}
	}

	billExtraInfo := map[string]string{
		"passenger_count": cast.ToString(passengerCount),
	}
	billExtraInfoJson, err := json.Marshal(billExtraInfo)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json marsh bill extraInfo fail:%v", err)
	}

	return &proto.PriceInfo{
		Title:            title,
		Subtitle:         subTitle,
		FareAmount:       successEstimateFee,
		AwardAmount:      "0",
		Icon:             icon,
		JumpUrl:          BeatlesPrice.GetPassengerEstimateBillInfo(nil, &eid, proto.StrPtr(string(billExtraInfoJson))),
		TitleRollInfo:    titleRollInfo,
		SubtitleRollInfo: subtitleRollInfo,
		IsRoll:           &rollEnable,
		IsDecimalSmall:   &doubleEnable,
	}
}

func GetCouponBubble(ctx context.Context, product *biz_runtime.ProductInfoFull, dcmpData proto.SFCEstimateDcmp) (*string, string, string, string, string, string) {
	discount := ""
	customTag := ""
	selfCoupon := ""
	expiretime := ""
	batchId := ""
	//拼车取拼失败优惠券
	if product.GetProductCategory() != estimate_pc_id.EstimatePcIdAloneSFCar {
		carpoolFailDiscountInfo := product.GetCarpoolFailedCouponFromRawData()
		if carpoolFailDiscountInfo != nil {
			discountAmount := cast.ToFloat64(carpoolFailDiscountInfo.Amount) / 100
			carpoolSuccPrice := product.GetCapPrice()
			_, discountNum := coupon.GetCarpoolSuccInfoWithCoupon(carpoolSuccPrice, discountAmount)
			discount = cast.ToString(BeatlesUtil.RoundHalfUp(discountNum, 2))
			customTag = carpoolFailDiscountInfo.CustomTag
			if _, ok := carpoolFailDiscountInfo.ExtraInfo["fe_self_support_coupon_tag"]; ok {
				selfCoupon = cast.ToString(carpoolFailDiscountInfo.ExtraInfo["fe_self_support_coupon_tag"])
			}
			if carpoolFailDiscountInfo.ExpireTime != nil {
				expiretime = *carpoolFailDiscountInfo.ExpireTime
			}
			batchId = carpoolFailDiscountInfo.BatchId
		} else {
			discount = product.GetCouponAmount()
			if product.GetCouponInfo() != nil {
				customTag = product.GetCouponInfo().CustomTag
				if _, ok := product.GetCouponInfo().ExtraInfo["fe_self_support_coupon_tag"]; ok {
					selfCoupon = cast.ToString(product.GetCouponInfo().ExtraInfo["fe_self_support_coupon_tag"])
				}
				if product.GetCouponInfo().ExpireTime != nil {
					expiretime = *product.GetCouponInfo().ExpireTime
				}
				batchId = product.GetCouponInfo().BatchId
			}
		}
	} else {
		discount = product.GetCouponAmount()
		if product.GetCouponInfo() != nil {
			customTag = product.GetCouponInfo().CustomTag
			if _, ok := product.GetCouponInfo().ExtraInfo["fe_self_support_coupon_tag"]; ok {
				selfCoupon = cast.ToString(product.GetCouponInfo().ExtraInfo["fe_self_support_coupon_tag"])
			}
			if product.GetCouponInfo().ExpireTime != nil {
				expiretime = *product.GetCouponInfo().ExpireTime
			}
			batchId = product.GetCouponInfo().BatchId
		}
	}

	if discount == "" {
		return &discount, "", "", "", "", ""
	}
	discountText := dcmp.TranslateTemplate(dcmpData.DiscountText, map[string]string{"discount": discount})
	return &discountText, discount, customTag, selfCoupon, expiretime, batchId
}

// RegionAnimationSupport 区域一口价 定价动画感知是否展示
func RegionAnimationSupport(product render.ApolloProvider) (isHit bool, assignParams map[string]string) {
	pidKey, params := product.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo2.GetParameters("sfc_region_animation", pidKey, params)
}

// IntegerPriceAware 顺风车整数价格感知
func IntegerPriceAware(ctx context.Context, product render.ApolloProvider, estimateFeeList []float64) bool {
	var integerAware, isIntegerFee bool

	pidKey, params := product.GetApolloParams(biz_runtime.WithPIDKey)
	integerAware = apollo2.FeatureToggle(ctx, "sfc_int_price_aware", pidKey, params)

	_, isIntegerFee = getIntegerPrice(estimateFeeList[0])
	if len(estimateFeeList) == 2 {
		_, isFailEstimateFeeInteger := getIntegerPrice(estimateFeeList[1])
		isIntegerFee = isIntegerFee || isFailEstimateFeeInteger
	}

	return integerAware && isIntegerFee
}

func getIntegerPrice(fee float64) (string, bool) {
	feeStr := fmt.Sprintf("%.2f", fee)
	tmp := strings.TrimRight(feeStr, "0")
	tmp = strings.TrimRight(tmp, ".")
	if integerFee, err := strconv.Atoi(tmp); err == nil {
		return strconv.Itoa(integerFee), true
	}
	return "", false
}

func GetCouponList(ctx context.Context, discount string, isNewUser, isNewVersion bool, isSelfCoupon, isCustomTag string, isCouponMarketingSwitch bool, originalPrice *proto.SFCPriceRollInfo, failOriginalPrice *proto.SFCPriceRollInfo, extraParams map[string]string) ([]*proto.CouponEstimateInfo, string) {
	configDcmp := &DCMPForCouponStruct{}
	customTagName := ""
	if isCustomTag != "" {
		customTagName = coupon.GetDCMPCustomTagText(ctx, isCustomTag)
	}
	params := map[string]string{
		"couponAmount": discount,
		"couponName":   customTagName,
	}
	if isSelfCoupon != "" {
		params["couponName"] = isSelfCoupon
	}
	config := dcmp.GetDcmpContent(ctx, "sfc-estimate_coupon_text", params)
	errUnmarshal := json.Unmarshal([]byte(config), &configDcmp)
	if errUnmarshal != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "sfc-estimate_coupon_text Unmarshal=%+v", errUnmarshal)
		return nil, ""
	}

	var oneCouponEstimateInfo *proto.CouponEstimateInfo
	var couponType string
	if discount != "" {
		oneCouponEstimateInfo = &proto.CouponEstimateInfo{}
		couponType = "normal"
		if isNewUser && isNewVersion {
			oneCouponEstimateInfo.Title = configDcmp.Title
			oneCouponEstimateInfo.Icon = configDcmp.Icon
			oneCouponEstimateInfo.BackgroundColors = configDcmp.BackgroundColor
			oneCouponEstimateInfo.Type = configDcmp.Type
			couponType = "new"
		} else if isSelfCoupon != "" && isCouponMarketingSwitch {
			oneCouponEstimateInfo.Title = configDcmp.SelfCouponTag.Title
			oneCouponEstimateInfo.Icon = configDcmp.SelfCouponTag.Icon
			oneCouponEstimateInfo.BackgroundColors = configDcmp.SelfCouponTag.BackgroundColor
			oneCouponEstimateInfo.Type = configDcmp.SelfCouponTag.Type
			couponType = "package"
		} else if customTagName != "" && isCouponMarketingSwitch {
			oneCouponEstimateInfo.Title = configDcmp.CustomTag.Title
			oneCouponEstimateInfo.Icon = configDcmp.CustomTag.Icon
			oneCouponEstimateInfo.BackgroundColors = configDcmp.CustomTag.BackgroundColor
			oneCouponEstimateInfo.Type = configDcmp.CustomTag.Type
			couponType = "mkt"
		} else {
			oneCouponEstimateInfo.Title = configDcmp.TitleOld
			oneCouponEstimateInfo.Type = "normal"
			oneCouponEstimateInfo.BackgroundColors = configDcmp.BackgroundColor
		}
	}
	var spsDiscount *proto.CouponEstimateInfo
	if originalPrice != nil && originalPrice.PriceFrom != "" && originalPrice.PriceTo != "" {
		couponDiscount := 0.0
		if discount != "" {
			couponDiscount = cast.ToFloat64(discount)
		}
		spsDiscount = BuildSpsDiscount(extraParams, originalPrice, failOriginalPrice, configDcmp, couponDiscount)
	}
	var CouponEstimateInfoList []*proto.CouponEstimateInfo
	if spsDiscount != nil {
		CouponEstimateInfoList = append(CouponEstimateInfoList, spsDiscount)
	}
	if oneCouponEstimateInfo != nil {
		CouponEstimateInfoList = append(CouponEstimateInfoList, oneCouponEstimateInfo)
	}

	if len(CouponEstimateInfoList) >= 2 {
		for key, item := range CouponEstimateInfoList {
			titleIndex := strings.Index(item.Title, "text=")
			titleEnd := len(item.Title) - 1
			CouponEstimateInfoList[key].Title = dcmp.TranslateTemplate(configDcmp.DoubleStyle.Title, map[string]string{
				"doubleName": item.Title[titleIndex+5 : titleEnd],
			})
			CouponEstimateInfoList[key].Icon = configDcmp.DoubleStyle.Icon
			CouponEstimateInfoList[key].BackgroundColors = configDcmp.DoubleStyle.BackgroundColor
			CouponEstimateInfoList[key].Type = configDcmp.DoubleStyle.Type
			CouponEstimateInfoList[key].BorderColor = &configDcmp.DoubleStyle.BorderColor
			CouponEstimateInfoList[key].TitleColor = &configDcmp.DoubleStyle.TitleColor
		}
	}
	return CouponEstimateInfoList, couponType
}

func getPriceIntegerAndDecimal(price float64) (integerValue string, decimalValue string) {
	integerPrice := int32(math.Floor(price * 100))
	if integerPrice%10 > 0 {
		newPrice := integerPrice/10 + 1
		if newPrice%10 == 0 {
			return cast.ToString(newPrice / 10), ""
		}
		return cast.ToString(newPrice / 10), "." + cast.ToString(newPrice%10)
	}
	if (integerPrice/10)%10 == 0 {
		return cast.ToString(integerPrice / 100), ""
	}
	return cast.ToString(integerPrice / 100), "." + cast.ToString((integerPrice/10)%10)
}

func getPriceInteger(price float64) string {
	integerPrice := int32(math.Floor(price * 100))
	if integerPrice%10 > 0 {
		newPrice := integerPrice/10 + 1
		return cast.ToString(cast.ToFloat64(newPrice) / 10)
	}
	return cast.ToString(cast.ToFloat64(integerPrice) / 100)
}

func BuildNewFenceStyle(ctx context.Context, productCategory int64, integerPriceAware bool, extraParams map[string]string, estimateFeeList []float64, originalPriceList []float64, dcmpData proto.SFCEstimateDcmp, eid string, passengerCount int32, newFenceStyleSwitch bool) *proto.PriceInfo {
	if !newFenceStyleSwitch {
		return nil
	}
	// 命中围栏，拼成不等于拼失败
	if extraParams["fencePriceId"] != "" && extraParams["estimateNotEqual"] == "1" {
		// 根据拼座围栏的拼成和拼失败价来判断是否展示新样式
		extraParams["newFenceStyle"] = NewFenceStyle
		var (
			rollEnable       int32
			title            string
			subTitle         string
			doubleEnable     int32
			subtitleRollInfo *proto.SFCPriceRollInfo
			titleRollInfo    *proto.SFCPriceRollInfo
		)
		doubleEnable = 1
		rollEnable = 1
		productCategoryStr := strconv.FormatInt(productCategory, 10)
		key := productCategoryStr + newFenceStyle

		successEstimateFee := fmt.Sprintf("%.2f", estimateFeeList[0])
		if successFee, ok := getIntegerPrice(estimateFeeList[0]); integerPriceAware && ok {
			successEstimateFee = successFee
		}
		titleRollInfo, title = BuildRollInfo(key, originalPriceList, estimateFeeList, 0, integerPriceAware, dcmpData)
		if len(estimateFeeList) == 2 && len(originalPriceList) == 2 {
			subtitleRollInfo, subTitle = BuildRollInfo(key, originalPriceList, estimateFeeList, 1, integerPriceAware, dcmpData)
		}
		billExtraInfo := map[string]string{
			"passenger_count": cast.ToString(passengerCount),
		}
		billExtraInfoJson, err := json.Marshal(billExtraInfo)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "json marsh bill extraInfo fail:%v", err)
		}
		return &proto.PriceInfo{
			Title:            title,
			Subtitle:         subTitle,
			FareAmount:       successEstimateFee,
			AwardAmount:      "0",
			Icon:             dcmpData.PriceIcon,
			JumpUrl:          BeatlesPrice.GetPassengerEstimateBillInfo(nil, &eid, proto.StrPtr(string(billExtraInfoJson))),
			TitleRollInfo:    titleRollInfo,
			SubtitleRollInfo: subtitleRollInfo,
			IsRoll:           &rollEnable,
			IsDecimalSmall:   &doubleEnable,
		}
	}

	return nil
}

func GetNewFenceStyleSwitch(request proto.SFCEstimateRequest, pid uint64) bool {
	pidStr := cast.ToString(pid)
	apolloParams := ApolloModel.NewUser(cast.ToString(pid)).
		With("key", pidStr).
		With("pid", pidStr).
		With("city", cast.ToString(request.GetFromArea())).
		With("access_key_id", cast.ToString(request.GetAccessKeyId())).
		With("app_version", request.AppVersion)
	toggle, err := ApolloSDKV2.FeatureToggle("sfc_estimate_new_fence_style", apolloParams)
	if err != nil || toggle == nil || !toggle.IsAllow() || toggle.GetAssignment() == nil {
		return false
	}
	return true
}

func BuildSpsDiscount(extraParams map[string]string, originalPrice *proto.SFCPriceRollInfo, failOriginalPrice *proto.SFCPriceRollInfo, configDcmp *DCMPForCouponStruct, couponDiscount float64) *proto.CouponEstimateInfo {
	var title string
	if extraParams["newFenceStyle"] == NewFenceStyle {
		// 单边围栏样式
		priceDiscount := BeatlesUtil.RoundHalfUp(((cast.ToFloat64(originalPrice.PriceTo)+couponDiscount)/cast.ToFloat64(originalPrice.PriceFrom))*10, 1)
		if failOriginalPrice != nil && failOriginalPrice.PriceFrom != "" && failOriginalPrice.PriceTo != "" {
			tempPriceDiscount := BeatlesUtil.RoundHalfUp(((cast.ToFloat64(failOriginalPrice.PriceTo)+couponDiscount)/cast.ToFloat64(failOriginalPrice.PriceFrom))*10, 1)
			if tempPriceDiscount < priceDiscount {
				priceDiscount = tempPriceDiscount
			}
		}
		if priceDiscount < 10 {
			title = dcmp.TranslateTemplate(configDcmp.SpsDiscount.NewTitle, map[string]string{
				"spsAmount": cast.ToString(priceDiscount),
			})
		} else {
			// 命中兜底会导致折扣大于10
			return nil
		}
	} else {
		if diffPrice := BeatlesUtil.RoundHalfUp(cast.ToFloat64(originalPrice.PriceFrom)-cast.ToFloat64(originalPrice.PriceTo)-couponDiscount, 2); diffPrice > 0 {
			title = dcmp.TranslateTemplate(configDcmp.SpsDiscount.Title, map[string]string{
				"spsAmount": cast.ToString(diffPrice),
			})
		} else {
			return nil
		}
	}
	return &proto.CouponEstimateInfo{
		Title:            title,
		Icon:             configDcmp.SpsDiscount.Icon,
		BackgroundColors: configDcmp.SpsDiscount.BackgroundColor,
		Type:             configDcmp.SpsDiscount.Type,
	}
}

func BuildRollInfo(key string, originalPriceList []float64, estimateFeeList []float64, index int, integerPriceAware bool, dcmpData proto.SFCEstimateDcmp) (*proto.SFCPriceRollInfo, string) {
	var (
		priceFrom     string
		title         string
		titleRollInfo *proto.SFCPriceRollInfo
		dcmpRollInfo  *proto.SFCEstimateRollInfo
	)
	notPriceGuarantee := originalPriceList != nil && len(originalPriceList) > 0 && originalPriceList[index] > 0
	if !notPriceGuarantee {
		// 兜底
		key = key + priceGuarantee
	}
	estimateFee := fmt.Sprintf("%.2f", estimateFeeList[index])
	if fee, ok := getIntegerPrice(estimateFeeList[index]); integerPriceAware && ok {
		estimateFee = fee
	}
	priceTo := estimateFee
	if notPriceGuarantee && originalPriceList[index] > cast.ToFloat64(priceTo) {
		// 未兜底价格同时防止倒挂
		priceFrom = cast.ToString(originalPriceList[index])
	} else {
		priceFrom = priceTo
	}
	priceFrom = getPriceInteger(cast.ToFloat64(priceFrom))
	priceTo = getPriceInteger(cast.ToFloat64(priceTo))
	if index == 0 {
		dcmpRollInfo = dcmpData.RollInfo[key]
		title = dcmp.TranslateTemplate(dcmpData.PriceInfo[key].PriceTitle, map[string]string{"amount": estimateFee})
	} else {
		dcmpRollInfo = dcmpData.SubRollInfo[key]
		title = dcmp.TranslateTemplate(dcmpData.PriceInfo[key].PriceSubTitle, map[string]string{"amount": estimateFee})
	}
	titleRollInfo = &proto.SFCPriceRollInfo{
		TextColor: dcmpRollInfo.TextColor,
		TitlePre:  dcmpRollInfo.TitlePre,
		TitleEnd:  dcmpRollInfo.TitleEnd,
		PriceFrom: priceFrom,
		PriceTo:   priceTo,
	}
	return titleRollInfo, title
}
