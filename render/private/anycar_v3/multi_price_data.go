package anycar_v3

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"strconv"

	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type MultiPriceDataProvider interface {
	render.ProductProvider
	GetEstimateFee() float64
	GetCarpoolFailEstimateFee() (float64, bool)
	IsBusinessPay() bool
	GetMixedDeductPrice() float64
	GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo
	GetCapPrice() float64
	GetDynamicTotalFee() float64
	GetCarpoolSeatNum() int32
	GetCarpoolFailRawBill() *price_api.BillInfoV3
	GetCarpoolFailCouponInfo() *PriceApi.EstimateNewFormCouponInfo
	GetExtendList() []*PriceApi.EstimateNewFormExtend
}

func GetMultiPriceData(ctx context.Context, prov MultiPriceDataProvider) []*proto.MultiPriceData {
	if prov.GetProductCategory() == ProductCategory.ProductCategoryCarpoolStation {
		return getCarpoolStationPrice(ctx, prov)
	}
	if prov.GetProductCategory() == ProductCategory.ProductCategoryLowPriceCarpool {
		return getPinchenglePrice(ctx, prov)
	}
	return nil
}
func getPinchenglePrice(ctx context.Context, prov MultiPriceDataProvider) []*proto.MultiPriceData {
	var (
		multiPriceData = make([]*proto.MultiPriceData, 0)
	)
	list := prov.GetExtendList()
	if list != nil && len(list) > 0 {
		for _, item := range list {
			data := &proto.MultiPriceData{
				EstimateFee: item.EstimateFee,
				Option:      &proto.CarpoolOption{},
				DiscountSet: &proto.DiscountSet{},
			}

			if item.BillInfo != nil {
				data.DynamicTotalFee = item.BillInfo.DynamicTotalFee
				data.CapPrice = item.BillInfo.CapPrice
			}

			if item.SceneMark != nil && len(item.SceneMark) > 0 {
				for k, v := range item.SceneMark {
					switch k {
					case "is_carpool_success":
						if v == "1" {
							data.Option.IsCarpoolSuccess = true
						} else {
							data.Option.IsCarpoolSuccess = false
						}
						break
					case "seat_num":
						num, err := strconv.Atoi(v)
						if err != nil {
							return nil
						}
						data.Option.SeatNum = int32(num)
						break
					case "pool_num":
						num, err := strconv.Atoi(v)
						if err != nil {
							return nil
						}
						data.Option.PoolNum = int32(num)
						break
					}
				}
			}

			if item.DiscountSet != nil {
				if item.DiscountSet.Coupon != nil {
					coupon := &proto.EstimateCouponInfo{
						BatchId:    item.DiscountSet.Coupon.BatchId,
						CouponId:   item.DiscountSet.Coupon.CouponId,
						CouponType: item.DiscountSet.Coupon.CouponType,
						CustomTag:  item.DiscountSet.Coupon.CustomTag,
					}
					amount, err := strconv.ParseInt(item.DiscountSet.Coupon.Amount, 10, 32)
					if err != nil {
						return nil
					}
					coupon.Amount = int32(amount)
					data.DiscountSet.Coupon = coupon
				}
				if item.DiscountSet.SpsLikeWaitReward != nil {
					sps := &proto.EstimateSpsLikeWaitReward{
						WaitTime: item.DiscountSet.SpsLikeWaitReward.WaitTime,
						Amount:   item.DiscountSet.SpsLikeWaitReward.Amount,
					}
					data.DiscountSet.SpsLikeWaiteReward = sps
				}
			}

			multiPriceData = append(multiPriceData, data)
		}
	}
	return multiPriceData
}

func getCarpoolStationPrice(ctx context.Context, prov MultiPriceDataProvider) []*proto.MultiPriceData {
	var (
		multiPriceData = make([]*proto.MultiPriceData, 0)
	)

	succItem := &proto.MultiPriceData{}
	failItem := &proto.MultiPriceData{}

	if prov.IsBusinessPay() {
		// 未拼成价格
		failItem.EstimateFee = 0
		// 拼成价格
		succItem.EstimateFee = 0
	} else {
		if carpoolFailFee, ok := prov.GetCarpoolFailEstimateFee(); ok {
			failItem.EstimateFee = carpoolFailFee
		}
		succItem.EstimateFee = prov.GetEstimateFee()
	}

	// 拼成
	succItem.CapPrice = prov.GetCapPrice()
	succItem.DynamicTotalFee = prov.GetDynamicTotalFee()
	succItem.DiscountSet = &proto.DiscountSet{}
	couponInfo := prov.GetCouponInfo()
	if couponInfo != nil {
		succItem.DiscountSet.Coupon = &proto.EstimateCouponInfo{
			BatchId:    couponInfo.BatchId,
			CouponId:   couponInfo.CouponId,
			Amount:     util.ToInt32(couponInfo.Amount),
			CouponType: couponInfo.CouponType,
			CustomTag:  couponInfo.CustomTag,
		}
	}

	succItem.Option = &proto.CarpoolOption{
		IsCarpoolSuccess: true,
		SeatNum:          prov.GetCarpoolSeatNum(),
	}
	multiPriceData = append(multiPriceData, succItem)

	// 未拼成
	failBill := prov.GetCarpoolFailRawBill()
	if failBill != nil {
		failItem.DynamicTotalFee = failBill.DynamicTotalFee
		failItem.CapPrice = failBill.CapPrice
	}

	failItem.DiscountSet = &proto.DiscountSet{}
	failCouponInfo := prov.GetCarpoolFailCouponInfo()
	if failCouponInfo != nil {
		failItem.DiscountSet.Coupon = &proto.EstimateCouponInfo{
			BatchId:    failCouponInfo.BatchId,
			CouponId:   failCouponInfo.CouponId,
			Amount:     util.ToInt32(failCouponInfo.Amount),
			CouponType: failCouponInfo.CouponType,
			CustomTag:  failCouponInfo.CustomTag,
		}
	}

	failItem.Option = &proto.CarpoolOption{
		IsCarpoolSuccess: false,
		SeatNum:          prov.GetCarpoolSeatNum(),
	}
	multiPriceData = append(multiPriceData, failItem)

	return multiPriceData
}
