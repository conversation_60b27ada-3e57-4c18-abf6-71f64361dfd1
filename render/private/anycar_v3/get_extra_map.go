package anycar_v3

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const (
	IsAuthExtra = int32(0)

	IsNotAuthExtra = int32(1)
)

type GetExtraMapAuthProvider interface {
	render.ProductProvider
	GetPrivateBizInfo() *models.PrivateBizInfo
}

func GetExtraMapAuth(ctx context.Context, prov NewOrderParamProvider) (extraMap *proto.NewFormExtraMap) {
	extraMap = &proto.NewFormExtraMap{}
	extraMap.SetBusinessId(prov.GetBusinessID())
	extraMap.SetComboType(prov.GetComboType())
	extraMap.SetLevelType(prov.GetLevelType())
	extraMap.SetRequireLevel(prov.GetRequireLevel())
	extraMap.SetSubGroupId(prov.GetSubGroupId())
	extraMap.SetCarpoolType(int32(prov.GetCarpoolType()))

	extraMap.SetIsDefaultAuth(IsAuthExtra)
	businessID := prov.GetBusinessID()
	_, IsExist := prov.GetPrivateBizInfo().UfsTripCloudAuthBusinessID[businessID]
	if IsExist {
		extraMap.SetIsDefaultAuth(IsNotAuthExtra)
	}

	// 补天出租车需要授权的话
	if prov.GetPrivateBizInfo().IsButianNeedAuth && prov.GetProductCategory() == product_category.ProductCategoryFastTaxi {
		extraMap.SetIsDefaultAuth(IsNotAuthExtra)
	}

	return extraMap
}
