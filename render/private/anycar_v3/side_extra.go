package anycar_v3

import (
	"context"
	render2 "git.xiaojukeji.com/gulfstream/mamba/render/public/sub_title_list"

	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	SupplyDemandNotMatch = 1
	SupplyCrunch         = 2
	LowDemand            = 3
)

type SideEstimateProvider interface {
	render.ProductProvider
	GetPrivateBizInfo() *models.PrivateBizInfo
	IsHitNormalNoAnswerCompensation(ctx context.Context) bool
	render2.SubTitleListProvider
}

func GetSideEstimateData(ctx context.Context, p *biz_runtime.ProductInfoFull, prov SideEstimateProvider) (extra *proto.SideExtra) {
	extra = &proto.SideExtra{
		RecommendBubble: buildRecommendBubble(p),
		BottomRule:      buildBottomRule(ctx, p, prov),
		RightSubTitle:   buildRightSubTitle(ctx, p, prov, ""),
	}

	return extra
}

func GetSideEstimateDataV4(ctx context.Context, p *biz_runtime.ProductInfoFull, prov SideEstimateProvider) (extra *proto.SideExtra) {
	extra = &proto.SideExtra{
		RecommendBubble: buildRecommendBubble(p),
		BottomRule:      buildBottomRule(ctx, p, prov),
		RightSubTitle:   buildRightSubTitle(ctx, p, prov, "pAnycarEstimateV4"),
		SubTitleList:    render2.GetSubTitleList(ctx, prov),
	}

	return extra
}

func buildRecommendBubble(p *biz_runtime.ProductInfoFull) string {
	if len(p.BaseReqData.CommonBizInfo.RecommendInfoMap) < 1 {
		return ""
	}
	info := p.BaseReqData.CommonBizInfo.RecommendInfoMap[int32(p.GetProductCategory())]
	if info != nil && info.ShowType == 2 {
		// 气泡文案
		return info.GetText()
	}

	return ""
}

func buildBottomRule(ctx context.Context, p *biz_runtime.ProductInfoFull, prov SideEstimateProvider) *proto.BottomRule {
	return DoRender(ctx, p, prov)
}

func buildRightSubTitle(ctx context.Context, p *biz_runtime.ProductInfoFull, prov SideEstimateProvider, from string) *proto.GroupSubTitle {
	// 大字版，追加车型4.0列表，屏蔽无车赔以及供需标签
	if from == "pAnycarEstimateV4" && p != nil && p.BaseReqData != nil && p.BaseReqData.CommonInfo.FontScaleType != 0 {
		return nil
	}

	compensationRightSubTitle := buildCompensationRightSubTitle(ctx, p, prov, from)
	if compensationRightSubTitle != nil {
		return compensationRightSubTitle
	}

	supplySceneRightSubTitle := buildSupplySceneRightSubTitle(ctx, p, prov, from)
	if supplySceneRightSubTitle != nil {
		return supplySceneRightSubTitle
	}

	return nil
}

func buildCompensationRightSubTitle(ctx context.Context, p *biz_runtime.ProductInfoFull, prov SideEstimateProvider, from string) *proto.GroupSubTitle {
	subTitle := &proto.GroupSubTitle{}

	if prov.GetPrivateBizInfo() == nil || len(prov.GetPrivateBizInfo().CompensationInfo) == 0 {
		return nil
	}

	if decision, ok := prov.GetPrivateBizInfo().CompensationInfo[compensation.NormalNoAnswerCompensation]; ok && decision != nil && decision.Decision == 1 {
		var content string
		if text, ok := decision.Extra["biz_name"]; ok && text != nil && util.ToString(text) != "" {
			content = util.ToString(text)
		}

		dcmpConf := dcmp.GetJSONContentWithPath(ctx, "config_text-normal_compensation", nil, "right_sub_title")
		if dcmpConf != "" {
			err := json.Unmarshal([]byte(dcmpConf), subTitle)
			if err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal fail with config_text-normal_compensation with err %v", err)
			} else {
				if content != "" {
					subTitle.Content = content
				}
				return subTitle
			}
		}
	}

	return nil
}

func buildSupplySceneRightSubTitle(ctx context.Context, p *biz_runtime.ProductInfoFull, prov SideEstimateProvider, from string) *proto.GroupSubTitle {
	// 有命中赔付
	if p.IsHitNormalNoAnswerCompensation(ctx) {
		return nil
	}

	subTitle := &proto.GroupSubTitle{}
	supplySceneInfo := p.GetCommonBizInfo().SupplySceneInfo
	// 不存在或者不需要展示
	if supplySceneInfo == nil || !supplySceneInfo.GetIsDisplaySupplyLabel() {
		return nil
	}

	// 返回为空或者不是需要展示标签的车型
	if supplySceneInfo.SupplyLabelProductList == nil || !util.InArrayInt32(int32(prov.GetProductCategory()), supplySceneInfo.SupplyLabelProductList) {
		return nil
	}

	supplySceneType := supplySceneInfo.GetSupplySceneType()
	switch supplySceneType {
	case SupplyDemandNotMatch, SupplyCrunch:
		subTitle.IconUrl = dcmp.GetJSONContentWithPath(ctx, "config_text-supply_scene", nil, "icon_url_"+util.ToString(supplySceneType))
	case LowDemand:
		if p.GetPrivateBizInfo().OrderReceiveCnt == 0 {
			return nil
		}

		subTitle.Content = dcmp.GetJSONContentWithPath(ctx, "config_text-supply_scene", map[string]string{"answer_cnt": util.ToString(p.GetPrivateBizInfo().OrderReceiveCnt)}, "content")
		subTitle.BgUrl = util.StringPtr(dcmp.GetJSONContentWithPath(ctx, "config_text-supply_scene", nil, "bg_url"))
	}

	subTitle.Disabled = util.BoolPtr(true)
	return subTitle
}
