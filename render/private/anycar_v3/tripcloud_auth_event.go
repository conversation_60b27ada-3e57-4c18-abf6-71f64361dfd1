package anycar_v3

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
)

const (
	TripCloudAuthdcmpKey     = "anycar_v3-tripcloud_auth_bottom_event"
	TripCloudParamRecordKey  = "record_id_list"
	TripCloudParamServiceKey = "service_auth_id_list"
	CityIdHk                 = 357
	HkTripCloudAuthdcmpKey   = "anycar_v3-hk_tripcloud_auth_bottom_event"
)

type TripCloudAuthEvent struct {
	eventKey  string
	product   *biz_runtime.ProductInfoFull
	provider  SideEstimateProvider
	factor    int
	subFactor int
	content   *proto.BottomContent
}

type Extra struct {
	Lang string `json:"lang"`
	Area string `json:"area"`
}

func (tae *TripCloudAuthEvent) setInput(p *biz_runtime.ProductInfoFull, prov SideEstimateProvider, Position int, key string) {
	tae.eventKey = key
	tae.product = p
	tae.provider = prov
	tae.factor = Position
	tae.subFactor = 0 //给端传相同weight
}

func (tae *TripCloudAuthEvent) isOpen() bool {
	businessID := tae.product.GetBusinessID()
	//判断BusinessID是否在未授权的品类map里
	_, IsExist := tae.provider.GetPrivateBizInfo().UfsTripCloudAuthBusinessID[businessID]
	if IsExist {
		return true
	}
	// 补天出租车三方
	if tae.product.GetPrivateBizInfo().IsButianNeedAuth && tae.product.GetProductCategory() == product_category.ProductCategoryFastTaxi {
		return true
	}

	return false
}

func (tae *TripCloudAuthEvent) preBuild(ctx context.Context) {
	// 香港三方授权的特殊逻辑
	if tae.product.IsHongKong() {
		hkPreBuild(ctx, tae)
		return
	}

	var (
		linkParam map[string]string
	)
	linkParam = make(map[string]string, 2)
	text := dcmp.GetDcmpContent(ctx, TripCloudAuthdcmpKey, nil)
	if text == "" {
		return
	}

	content := &proto.BottomContent{}
	err := json.Unmarshal([]byte(text), content)
	if err != nil {
		return
	}

	linkParam[TripCloudParamRecordKey] = cast.ToString(tae.product.GetBusinessID())
	linkParam[TripCloudParamServiceKey] = cast.ToString(tae.product.GetBusinessID())
	content.LinkQuerys = linkParam
	tae.content = content
}

// 处理香港三方授权的特殊逻辑
func hkPreBuild(ctx context.Context, tae *TripCloudAuthEvent) {
	var (
		linkParam = make(map[string]string, 2)
	)
	text := dcmp.GetDcmpContent(ctx, HkTripCloudAuthdcmpKey, nil)
	if text == "" {
		return
	}

	content := &proto.BottomContent{}
	err := json.Unmarshal([]byte(text), content)
	if err != nil {
		return
	}

	linkParam[TripCloudParamServiceKey] = cast.ToString(tae.product.GetBusinessID())

	extra := Extra{
		Lang: tae.product.GetLang(),
		Area: strconv.Itoa(CityIdHk),
	}
	replaceItem, ok := json.Marshal(extra)
	if ok != nil {
		content = nil
		return
	}
	replaceMap := map[string]string{
		"extra": string(replaceItem),
	}

	content.LinkUrl = util.ReplaceTag(ctx, content.LinkUrl, replaceMap)
	content.LinkQuerys = linkParam
	tae.content = content
	return
}

func (tae *TripCloudAuthEvent) setEventAttr() []int32 {
	return []int32{1}
}

func (tae *TripCloudAuthEvent) getFactor() int {
	return tae.factor
}

func (tae *TripCloudAuthEvent) getSubFactor() int {
	return tae.subFactor
}

func (tae *TripCloudAuthEvent) getEventKey() string {
	return tae.eventKey
}

func (tae *TripCloudAuthEvent) getEventContent() *proto.BottomContent {
	return tae.content
}
