package anycar_v3

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const (
	TripCloudParamKey = "is_default_auth"

	IsAuth = "0"

	IsNotAuth = "1"
)

type NewOrderParamProvider interface {
	GetHolidayFee() float64
	render.ProductProvider
	GetPrivateBizInfo() *models.PrivateBizInfo
}

func PNewOrderParamsData(ctx context.Context, prov NewOrderParamProvider) map[string]string {
	paramMap := make(map[string]string, 1)
	paramMap[TripCloudParamKey] = IsAuth
	businessID := prov.GetBusinessID()
	_, IsExist := prov.GetPrivateBizInfo().UfsTripCloudAuthBusinessID[businessID]
	if IsExist {
		paramMap[TripCloudParamKey] = IsNotAuth
	}

	// 补天出租车需要授权的话
	if prov.GetPrivateBizInfo().IsButianNeedAuth && prov.GetProductCategory() == product_category.ProductCategoryFastTaxi {
		paramMap[TripCloudParamKey] = IsNotAuth
	}

	return paramMap
}
