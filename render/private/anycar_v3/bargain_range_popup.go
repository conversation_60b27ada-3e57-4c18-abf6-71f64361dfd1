package anycar_v3

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type FastRangePopupProvider interface {
	render.ProductProvider
}

func GetFastRangePopup(ctx context.Context, prov NewOrderParamProvider) (popupInfo *proto.BargainRangePopup) {
	if prov.GetProductCategory() != estimate_pc_id.EstimatePcIdHuiXuanCar {
		return nil
	}
	popupInfo = &proto.BargainRangePopup{}

	config := dcmp.GetDcmpContent(ctx, "bargain_range-popup_config", nil)
	err := json.Unmarshal([]byte(config), popupInfo)
	if err != nil {
		return nil
	}
	if prov.GetHolidayFee() != 0 {
		popupInfo.SubTitle += popupInfo.RedPacketTitle
	}

	popupInfo.PriceLimitLower = util.Float64Add(float64(prov.GetPrivateBizInfo().PriceLimitLower), prov.GetHolidayFee())
	popupInfo.PriceLimitUpper = util.Float64Add(float64(prov.GetPrivateBizInfo().PriceLimitUpper), prov.GetHolidayFee())
	popupInfo.RecommendPriceLower = util.Float64Add(float64(prov.GetPrivateBizInfo().RecommendPriceLower), prov.GetHolidayFee())
	popupInfo.RecommendPriceUpper = util.Float64Add(float64(prov.GetPrivateBizInfo().RecommendPriceUpper), prov.GetHolidayFee())
	popupInfo.FastCarEstimateFee = prov.GetPrivateBizInfo().FastCarEstimateFee
	popupInfo.SpFastCarEstimateFee = prov.GetPrivateBizInfo().SpFastCarEstimateFee

	return popupInfo
}
