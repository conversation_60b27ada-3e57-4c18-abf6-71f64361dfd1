package anycar_v3

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
)

// 创建模拟的SideEstimateProvider
type MockSideEstimateProvider struct {
	privateBizInfo *models.PrivateBizInfo
}

type AnyCarAdapter struct {
	*biz_runtime.ProductInfoFull
}

func (a AnyCarAdapter) GetMemberLevel() int32 {
	return 0
}

func (a AnyCarAdapter) GetIsShowFemaleDriver() int64 {
	return 0
}

func (a AnyCarAdapter) GetPrivateBizInfo() *models.PrivateBizInfo {
	return &models.PrivateBizInfo{}
}

// 设置所需的依赖项模拟
func setupMockDependencies(lang string) func() {
	var patchList []*mockey.Mocker
	// Mock GetDcmpContent 函数
	res := mockey.Mock(dcmp.GetDcmpContent).To(func(_ context.Context, _ string, _ map[string]string) string {
		var content *proto.BottomContent
		if lang == "zh-CN" {
			// 返回模拟的dcmp内容
			content = &proto.BottomContent{
				Text:      "使用第三方服务请阅读并同意{个人信息共享授权书及相关协议}",
				TextColor: "#C63F00",
				LinkUrl:   "https://page.udache.com/passenger/apps/third-protocol/index.html?source=v3&extra={{extra}}",
			}
		}
		if len(lang) == 0 {
			content = nil
			return ""
		}

		if lang == "zh-HK" {
			// 返回模拟的dcmp内容
			content = &proto.BottomContent{
				Text:      "使用第三方服務前，請閱讀並同意{個人資料共享授權書及相關協議}",
				LinkUrl:   "https://page.udache.com/passenger/apps/third-protocol/index.html?source=v3&extra={{extra}}",
				TextColor: "#C63F00",
			}
		}
		contentBytes, _ := json.Marshal(content)
		return string(contentBytes)
	}).Build()

	patchList = append(patchList, res)

	return func() {
		for _, patch := range patchList {
			patch.UnPatch()
		}
	}
}

func createTripCloudAuthEvent(lang string, area, toArea int32, businessId int64) *TripCloudAuthEvent {
	return &TripCloudAuthEvent{
		eventKey: "tripcloud_is_auth_event",
		product: &biz_runtime.ProductInfoFull{
			BaseReqData: &models.BaseReqData{
				CommonInfo: models.CommonInfo{
					Lang: lang,
				},
				AreaInfo: models.AreaInfo{
					Area:   area,
					ToArea: toArea,
				},
			},
			Product: &models.Product{
				BusinessID: businessId,
			},
		},
		provider: AnyCarAdapter{
			ProductInfoFull: &biz_runtime.ProductInfoFull{
				BaseReqData: &models.BaseReqData{
					CommonInfo: models.CommonInfo{
						Lang: lang,
					},
					AreaInfo: models.AreaInfo{
						Area:   area,
						ToArea: toArea,
					},
				},
				Product: &models.Product{
					BusinessID: businessId,
				},
			},
		},
		factor:    0,
		subFactor: 0,
		content:   &proto.BottomContent{},
	}
}

func TestTripCloudAuthEvent_preBuild(t *testing.T) {
	tests := []struct {
		name string
		ctx  context.Context
		auth *TripCloudAuthEvent
		want *TripCloudAuthEvent
		fun  func()
		flag string
	}{
		{
			name: "zh-CN normal test case",
			ctx:  context.Background(),
			auth: createTripCloudAuthEvent("zh-CN", 357, 357, 7501),
			want: &TripCloudAuthEvent{
				eventKey: "tripcloud_is_auth_event",
				product: &biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							Lang: "zh-CN",
						},
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
					},
					Product: &models.Product{
						BusinessID: 7501,
					},
				},
				provider: AnyCarAdapter{
					ProductInfoFull: &biz_runtime.ProductInfoFull{
						BaseReqData: &models.BaseReqData{
							CommonInfo: models.CommonInfo{
								Lang: "zh-CN",
							},
							AreaInfo: models.AreaInfo{
								Area:   357,
								ToArea: 357,
							},
						},
						Product: &models.Product{
							BusinessID: 7501,
						},
					},
				},
				factor:    0,
				subFactor: 0,
				content: &proto.BottomContent{
					Text:      "使用第三方服务请阅读并同意{个人信息共享授权书及相关协议}",
					TextColor: "#C63F00",
					LinkUrl:   "https://page.udache.com/passenger/apps/third-protocol/index.html?source=v3&extra={\"lang\":\"zh-CN\",\"area\":\"357\"}",
					LinkQuerys: map[string]string{
						"service_auth_id_list": "7501",
					},
				},
			},
			flag: "zh-CN",
		},
		{
			name: "zh-HK normal test case",
			ctx:  context.Background(),
			auth: createTripCloudAuthEvent("zh-HK", 357, 357, 7501),
			want: &TripCloudAuthEvent{
				eventKey: "tripcloud_is_auth_event",
				product: &biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							Lang: "zh-HK",
						},
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
					},
					Product: &models.Product{
						BusinessID: 7501,
					},
				},
				provider: AnyCarAdapter{
					ProductInfoFull: &biz_runtime.ProductInfoFull{
						BaseReqData: &models.BaseReqData{
							CommonInfo: models.CommonInfo{
								Lang: "zh-HK",
							},
							AreaInfo: models.AreaInfo{
								Area:   357,
								ToArea: 357,
							},
						},
						Product: &models.Product{
							BusinessID: 7501,
						},
					},
				},
				factor:    0,
				subFactor: 0,
				content: &proto.BottomContent{
					Text:      "使用第三方服務前，請閱讀並同意{個人資料共享授權書及相關協議}",
					TextColor: "#C63F00",
					LinkUrl:   "https://page.udache.com/passenger/apps/third-protocol/index.html?source=v3&extra={\"lang\":\"zh-HK\",\"area\":\"357\"}",
					LinkQuerys: map[string]string{
						"service_auth_id_list": "7501",
					},
				},
			},
			flag: "zh-HK",
		},
		{
			name: "not language test case",
			ctx:  context.Background(),
			want: &TripCloudAuthEvent{
				eventKey: "tripcloud_is_auth_event",
				product: &biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							//Lang: lang,
						},
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
					},
					Product: &models.Product{
						BusinessID: 7501,
					},
				},
				provider: AnyCarAdapter{
					ProductInfoFull: &biz_runtime.ProductInfoFull{
						BaseReqData: &models.BaseReqData{
							CommonInfo: models.CommonInfo{
								//Lang: "",
							},
							AreaInfo: models.AreaInfo{
								Area:   357,
								ToArea: 357,
							},
						},
						Product: &models.Product{
							BusinessID: 7501,
						},
					},
				},
				factor:    0,
				subFactor: 0,
				content:   &proto.BottomContent{},
			},
			auth: createTripCloudAuthEvent("", 357, 357, 7501),
			flag: "",
		},
		{
			name: "area is not hk test case",
			ctx:  context.Background(),
			want: &TripCloudAuthEvent{
				eventKey: "tripcloud_is_auth_event",
				product: &biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							Lang: "zh-HK",
						},
						AreaInfo: models.AreaInfo{
							//Area:   357,
							ToArea: 357,
						},
					},
					Product: &models.Product{
						BusinessID: 7501,
					},
				},
				provider: AnyCarAdapter{
					ProductInfoFull: &biz_runtime.ProductInfoFull{
						BaseReqData: &models.BaseReqData{
							CommonInfo: models.CommonInfo{
								Lang: "zh-HK",
							},
							AreaInfo: models.AreaInfo{
								//Area:   357,
								ToArea: 357,
							},
						},
						Product: &models.Product{
							BusinessID: 7501,
						},
					},
				},
				factor:    0,
				subFactor: 0,
				content:   &proto.BottomContent{},
			},
			auth: createTripCloudAuthEvent("", 0, 357, 7501),
			flag: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// mock函数
			cleanup := setupMockDependencies(tt.flag)
			defer cleanup()

			// 执行测试
			tt.auth.preBuild(tt.ctx)
			// 验证结果
			assert.Equal(t, tt.want.content, tt.auth.content)
		})
	}
}
