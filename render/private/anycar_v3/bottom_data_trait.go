package anycar_v3

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const (
	TripcloudEvent = "tripcloud_is_auth_event"
)

type BottomEvent interface {
	setInput(*biz_runtime.ProductInfoFull, SideEstimateProvider, int, string)
	isOpen() bool
	preBuild(context.Context)
	setEventAttr() []int32
	getFactor() int
	getSubFactor() int
	getEventContent() *proto.BottomContent
	getEventKey() string
}

func DoRender(ctx context.Context, p *biz_runtime.ProductInfoFull, prov SideEstimateProvider) *proto.BottomRule {
	factory := make(map[string]BottomEvent, 1)
	//等待应答页沟通组件，只有底部由我们下发
	eventList := getRankedEvents()
	iLen := len(eventList) + 1
	for iPos, events := range eventList {
		event := createEvent(events, factory)
		if event == nil {
			continue
		}

		event.setInput(p, prov, iLen-iPos, events)
		if event.isOpen() {
			event.preBuild(ctx)
			return renderFunc(event)
		}
	}

	return nil
}

func getRankedEvents() []string {
	//产品口径：不会有其他需求使用该位置，因此只考虑简单实现
	//todo 如果增加event，可以迁移到Apollo配置同步
	return []string{TripcloudEvent}
}

func createEvent(eventKey string, factory map[string]BottomEvent) BottomEvent {
	eventList := getAllEvent()
	_, eventExist := eventList[eventKey]
	if eventExist {
		_, structExist := factory[eventKey]
		if structExist {
			return factory[eventKey]
		}

		factory[eventKey] = eventList[eventKey]
		return factory[eventKey]
	}

	return nil
}

func getAllEvent() map[string]BottomEvent {
	eventMap := make(map[string]BottomEvent, 1)
	//当前只有一个事件
	eventMap[TripcloudEvent] = &TripCloudAuthEvent{}
	return eventMap
}

func renderFunc(event BottomEvent) *proto.BottomRule {
	bottomRule := &proto.BottomRule{}
	bottomRule.Event = event.getEventKey()
	bottomRule.EventSubList = event.setEventAttr()
	bottomRule.Weight = calcWeight(event.getFactor(), event.getSubFactor())
	bottomRule.Content = event.getEventContent()
	return bottomRule
}

func calcWeight(Factor int, SubFactor int) int32 {
	//权重逻辑copy pSideEstimate
	weight := Factor*100000 + SubFactor*100
	return int32(weight)
}
