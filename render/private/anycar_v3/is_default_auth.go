package anycar_v3

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type IsDefaultAuthProvider interface {
	render.ProductProvider
	GetPrivateBizInfo() *models.PrivateBizInfo
}

func GetIsDefaultAuth(ctx context.Context, prov IsDefaultAuthProvider) int32 {
	businessID := prov.GetBusinessID()
	_, IsExist := prov.GetPrivateBizInfo().UfsTripCloudAuthBusinessID[businessID]
	if IsExist {
		return IsNotAuthExtra
	}

	return IsAuthExtra
}
