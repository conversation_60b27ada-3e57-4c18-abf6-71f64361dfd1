package anycar_v3

import (
	"context"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type DiscountInfoProvider interface {
	render.ProductProvider
	GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo
}

func GetDiscountSet(ctx context.Context, prov DiscountInfoProvider) *proto.DiscountSet {
	discountSet := &proto.DiscountSet{}
	couponInfo := GetEstimateCouponInfo(ctx, prov)
	if couponInfo != nil {
		discountSet.Coupon = couponInfo
	}

	return discountSet
}

func GetEstimateCouponInfo(ctx context.Context, prov DiscountInfoProvider) *proto.EstimateCouponInfo {
	couponInfo := prov.GetCouponInfo()
	if couponInfo == nil {
		return nil
	}

	return &proto.EstimateCouponInfo{
		BatchId:    couponInfo.BatchId,
		CouponId:   couponInfo.CouponId,
		Amount:     util.ToInt32(couponInfo.Amount),
		CouponType: couponInfo.CouponType,
		CustomTag:  couponInfo.CustomTag,
	}
}
