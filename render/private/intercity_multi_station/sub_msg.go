package intercity_multi_station

import (
	"context"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/apolloconf/bus_shift"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/carpoolstation"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
)

const (
	GongJiaoModel = "公交"
)

type SubMsgProv interface {
	GetSelectInfo() *models.StationInventorySelectInfo
	GetBusMode() string
	GetRobinMap() map[string]*carpoolstation.BusServerShift
}

func GetInventoryTag(ctx context.Context, prov SubMsgProv) *proto.SubMsg {
	if len(prov.GetRobinMap()) == 0 ||
		prov.GetSelectInfo() == nil ||
		len(prov.GetSelectInfo().ShiftID) == 0 {
		return nil
	}
	robinMap := prov.GetRobinMap()
	shiftId := prov.GetSelectInfo().ShiftID
	if robinMap[shiftId] == nil {
		return nil
	}

	busShiftRobinData := robinMap[prov.GetSelectInfo().ShiftID]
	if len(busShiftRobinData.ShiftTagId) == 0 {
		return nil
	}
	tagName := bus_shift.GetTagNameByTagId(ctx, busShiftRobinData.ShiftTagId)
	if len(tagName) == 0 {
		return nil
	}
	// 通过shiftTagId转换为match的tagName
	subMsgTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_station-suffix")

	return &proto.SubMsg{
		Color:   gjson.Get(subMsgTemplate, "color").String(),
		Content: tagName,
	}
}

func GetInventorySubMsg(ctx context.Context, prov SubMsgProv) *proto.SubMsg {
	subMsgTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_station-submsg")
	prefix := "more"
	if prov.GetSelectInfo().RemainSeats < 10 {
		prefix = "less"
	}
	if prov.GetSelectInfo().RemainSeats <= 0 {
		prefix = "no"
	}

	return &proto.SubMsg{
		Color: gjson.Get(subMsgTemplate, prefix+".color").String(),
		Content: dcmp.TranslateTemplate(gjson.Get(subMsgTemplate, prefix+".content").String(), map[string]string{
			"num": strconv.Itoa(int(prov.GetSelectInfo().RemainSeats)),
		}),
	}
}

func GetSuffix(ctx context.Context, prov SubMsgProv) *proto.SubMsg {
	subMsgTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_station-suffix")
	if suffix := prov.GetBusMode(); suffix != "" {
		return &proto.SubMsg{
			Color:   gjson.Get(subMsgTemplate, "color").String(),
			Content: GongJiaoModel,
		}
	}
	return nil
}

func SubMsg(ctx context.Context, prov SubMsgProv) *proto.SubMsg {
	subMsgTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_station-submsg")
	prefix := "more"
	if prov.GetSelectInfo().RemainSeats < 10 {
		prefix = "less"
	}
	if prov.GetSelectInfo().RemainSeats <= 0 {
		prefix = "no"
	}

	return &proto.SubMsg{
		Color: gjson.Get(subMsgTemplate, prefix+".color").String(),
		Content: dcmp.TranslateTemplate(gjson.Get(subMsgTemplate, prefix+".content").String(), map[string]string{
			"num": strconv.Itoa(int(prov.GetSelectInfo().RemainSeats)),
		}),
	}
}

func SubBackground(ctx context.Context, prov SubMsgProv) *string {
	robinMap := prov.GetRobinMap()
	shiftId := prov.GetSelectInfo().ShiftID
	if robinMap[shiftId] == nil {
		return nil
	}

	busShiftRobinData := robinMap[prov.GetSelectInfo().ShiftID]
	if len(busShiftRobinData.ShiftTagId) == 0 {
		return nil
	}

	config := dcmp.GetDcmpPlainContent(ctx, "intercity_station-shift_background")
	if background := gjson.Get(config, busShiftRobinData.ShiftTagId); background.Exists() {
		backgroundStr := background.String()
		return &backgroundStr
	}

	return nil
}

func SubTagListForOldVersion(ctx context.Context, prov SubMsgProv, isAllow bool) []*proto.SubMsg {
	var (
		tmp *proto.SubMsg
		res = make([]*proto.SubMsg, 0)
	)
	if isAllow {
		tmp = GetSuffix(ctx, prov)
		if tmp != nil {
			res = append(res, tmp)
		}
	}
	tmp = GetInventorySubMsg(ctx, prov)
	if tmp != nil {
		res = append(res, tmp)
	}
	if isAllow {
		tmp = GetInventoryTag(ctx, prov)
		if tmp != nil {
			res = append(res, tmp)
		}
	}
	return res
}

func SubTagList(ctx context.Context, prov SubMsgProv) []*proto.SubMsg {
	var (
		tmp *proto.SubMsg
		res = make([]*proto.SubMsg, 0)
	)

	tmp = GetSuffix(ctx, prov)
	if tmp != nil {
		res = append(res, tmp)
	}
	tmp = GetInventorySubMsg(ctx, prov)
	if tmp != nil {
		res = append(res, tmp)
	}
	tmp = GetInventoryTag(ctx, prov)
	if tmp != nil {
		res = append(res, tmp)
	}
	return res
}
