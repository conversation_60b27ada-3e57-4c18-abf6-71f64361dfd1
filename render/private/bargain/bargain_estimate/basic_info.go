package bargain_estimate

import (
	"context"
	Bargain "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/bargain"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type BasicInfoProvider interface {
	GetNTuple() *biz_runtime.NTuple
	GetDynamicTotalFee() float64
	GetEstimateId() string
	GetCouponAmount(ctx context.Context) float64
	GetBillExtraMap() map[string]interface{}
	render.ApolloProvider
}

func GetFeeAmount(product BasicInfoProvider) string {
	recommendInfo := Bargain.GetRecommendInfo(product.GetBillExtraMap())
	if recommendInfo == nil || recommendInfo.FastCarFee == nil || *recommendInfo.FastCarFee <= 0 {
		return util.FormatPriceForRound(product.GetDynamicTotalFee(), 1)
	}
	return util.FormatPriceForRound(*recommendInfo.FastCarFee, 1)
}

func GetEstimateId(product BasicInfoProvider) string {
	return product.GetEstimateId()
}
func GetProductCategory(product BasicInfoProvider) int32 {
	return int32(product.GetNTuple().ProductCategory)
}
func GetProductId(product BasicInfoProvider) int32 {
	return int32(product.GetNTuple().ProductID)
}
func GetRequireLevel(ctx context.Context, product BasicInfoProvider) int32 {
	return util.String2int32(ctx, product.GetNTuple().RequireLevel)
}
func GetComboType(product BasicInfoProvider) int32 {
	return int32(product.GetNTuple().ComboType)
}
func GetBusinessId(product BasicInfoProvider) int32 {
	return int32(product.GetNTuple().BusinessID)
}

// 获取优惠总金额
func GetCouponAmount(ctx context.Context, product BasicInfoProvider) float64 {
	//司乘议价只有券类型的优惠,故直接获取券抵扣金额即可。后续有其他优惠后可考虑使用应付-实付（目前实付价为四舍五入保留一位所得,相减后存在误差;改造成本和风险较大）来计算
	return product.GetCouponAmount(ctx)
}
