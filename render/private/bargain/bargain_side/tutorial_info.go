package bargain_side

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/tidwall/gjson"
)

type TutorialInfoProvider interface {
	GetPid() string
	render.ApolloProvider
}

func GetTutorialInfo(ctx context.Context, product TutorialInfoProvider) (data *proto.TutorialInfo) {
	apolloKey, apolloParams := product.GetApolloParams(biz_runtime.WithPIDKey)
	// ufs get
	ufsParam := map[string]string{"passenger_id": product.GetPid()}
	feature, err2 := ufs.GetFeatureV2(ctx, ufs.DomainPassenger, ufs.KeyBargainFirst, ufsParam)
	if err2 != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get ufs passenger.bargain.frist err = %v", err2)
		return nil
	}
	featureNum := util.ToInt64(feature)
	// 如果结果不为0，不出蒙层
	if featureNum != 0 {
		return nil
	}
	kv := map[string]string{ufs.KeyBargainMask: ""}
	if _, err := ufs.SetFeature(ctx, ufs.DomainPassenger, ufsParam, kv); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "setFeature err = %v", err)
	}

	if ok, assign := apollo.FeatureExp(ctx, "passenger_bargain_newuser", apolloKey, apolloParams); ok && assign.GetGroupName() == "treatment_group" {
		dcmpTutorialInfo := dcmp.GetDcmpPlainContent(ctx, "bargain-tutorial_info")
		if dcmpTutorialInfo == "" {
			return nil
		}
		return &proto.TutorialInfo{
			ImgUrl: gjson.Get(dcmpTutorialInfo, "img_url").String(),
			Text:   gjson.Get(dcmpTutorialInfo, "text").String(),
		}
	}
	return nil
}
