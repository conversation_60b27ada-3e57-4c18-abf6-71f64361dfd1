package bargain_side

import (
	"context"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/tidwall/gjson"
)

type RetainFrameDataProvider interface {
	GetPid() string
	render.ApolloProvider
}

// 离开挽留弹框文案
func GetRetainFrameData(ctx context.Context, product RetainFrameDataProvider) (retainFrameData *proto.RetainFrameData) {
	const (
		PreRedisKey            = "BargainRetainFrame_"         //redis key前缀
		EtxInfoExpireTime      = 60 * 60 * 24 * time.Second    //有效时间
		ABTest                 = "passenger_bargain_frame_ecr" //apollo实验名称
		BargainRetainFrameText = "bargain-retain_frame_text"   //dcmp 文案信息key
		Coupon                 = "coupon"                      //dcmp 文案信息前缀-券相关
		Normal                 = "normal"                      //dcmp 文案信息前缀-普通
		Title                  = "title"                       //dcmp 文案信息-标题
		SubTitle               = "sub_title"                   //dcmp 文案信息-子标题
		BgImgURL               = "bg_img_url"                  //dcmp 文案信息-背景图片地址
		LeftButtonText         = "left_button_text"            //dcmp 文案信息-左按钮文案
		RightButtonText        = "right_button_text"           //dcmp 文案信息-右按钮文案
	)
	//1.接入AB实验
	key, params := product.GetApolloParams(biz_runtime.WithPIDKey)
	isAllow := apollo.FeatureToggle(ctx, ABTest, key, params)
	if !isAllow {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getRetainFrameData AB filter,params=%s", util.JustJsonEncode(params))
		return nil
	}
	//2. redis控制频次，每个用户24小时下发一次
	redisKey := PreRedisKey + product.GetPid() //Redis key生成规则：BargainRetainFrame_+Pid
	if _, err := redis.GetClient().SetNEx(ctx, redisKey, EtxInfoExpireTime, 1); err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getRetainFrameData redis  already exit,redisKey=%s", redisKey)
		return nil
	}
	//3.dcmp获取对应的文案
	retainFrameDataStr := dcmp.GetDcmpPlainContent(ctx, BargainRetainFrameText)
	if len(retainFrameDataStr) == 0 || len(gjson.Get(retainFrameDataStr, Coupon).String()) == 0 ||
		len(gjson.Get(retainFrameDataStr, Normal).String()) == 0 {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getRetainFrameData dcmp error filter")
		return nil
	}
	couponContentStr := gjson.Get(retainFrameDataStr, Coupon).String()
	couponData := &proto.RetainFrameContent{
		Title:           gjson.Get(couponContentStr, Title).String(),
		SubTitle:        gjson.Get(couponContentStr, SubTitle).String(),
		BgImgUrl:        gjson.Get(couponContentStr, BgImgURL).String(),
		LeftButtonText:  gjson.Get(couponContentStr, LeftButtonText).String(),
		RightButtonText: gjson.Get(couponContentStr, RightButtonText).String(),
	}
	normalContentStr := gjson.Get(retainFrameDataStr, Normal).String()
	normalData := &proto.RetainFrameContent{
		Title:           gjson.Get(normalContentStr, Title).String(),
		SubTitle:        gjson.Get(normalContentStr, SubTitle).String(),
		BgImgUrl:        gjson.Get(normalContentStr, BgImgURL).String(),
		LeftButtonText:  gjson.Get(normalContentStr, LeftButtonText).String(),
		RightButtonText: gjson.Get(normalContentStr, RightButtonText).String(),
	}
	retainFrameData = new(proto.RetainFrameData)
	retainFrameData.SetCoupon(couponData)
	retainFrameData.SetNormal(normalData)
	return retainFrameData
}
