package bargain_side

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

func GetCommentTag(ctx context.Context) (ret []*proto.BargainCommentTag) {
	var (
		err error
		str string
	)
	if str = dcmp.GetDcmpPlainContent(ctx, "bargain-comment_tag"); str == "" {
		return nil
	}
	if err = json.Unmarshal([]byte(str), &ret); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshalCommentTag: %v", err)
		return nil
	}
	return
}
