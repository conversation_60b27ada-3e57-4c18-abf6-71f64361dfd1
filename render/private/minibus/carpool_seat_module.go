package minibus

import (
	"context"

	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type CarpoolSeatModuleProvider interface {
	render.ProductProvider
	GetCommonBizInfo() models.CommonBizInfo
}

func GetCarpoolSeatModule(ctx context.Context, p *biz_runtime.ProductInfoFull, prov CarpoolSeatModuleProvider) *proto.NewCarpoolSeatModule {
	bizInfo := prov.GetCommonBizInfo()
	if bizInfo.SeatNumInfo == nil || !carpool.IsMiniBus(int(p.GetCarpoolType())) {
		return nil
	}

	res := &proto.NewCarpoolSeatModule{}

	dcmpConf := dcmp.GetDcmpContent(ctx, "minibus-carpool_seat_module", nil)
	err := json.Unmarshal([]byte(dcmpConf), &res)
	if err != nil {
		return nil
	}
	res.SelectCount = 1
	if bizInfo.CarpoolSeatNum > 0 {
		res.SelectCount = bizInfo.CarpoolSeatNum
	}
	seatOption := bizInfo.SeatNumInfo.PassengerCountOption
	if len(seatOption) > 0 {
		res.MaxCount = bizInfo.SeatNumInfo.PassengerCountOption[len(seatOption)-1]
	}

	return res
}
