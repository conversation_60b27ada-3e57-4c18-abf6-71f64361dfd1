package anycar

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar/common_logic"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	trace "git.xiaojukeji.com/lego/context-go"
)

type MultiPriceDescProvider interface {
	ProductProvider
	GetEstimateFee() float64
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
	GetBillInfoCurrency() string
	GetBillFeeDetailInfo() map[string]float64
	IsCarpoolV3Merge(context.Context) bool
	GetCarpoolFailEstimateFee() (float64, bool)
	GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo
	GetMultiPriceCouponInfoByScene(index map[string]string) *PriceApi.EstimateNewFormCouponInfo
}

type DcmpConf struct {
	SuccCarpoolDay string `json:"succ_carpool_day"`
	Succ           string `json:"succ"`
	CapPriceMerge  string `json:"cap_price_merge"`

	FailV2 string `json:"fail_v_2"`
	FailV3 string `json:"fail_v_3"`
}

// GetMultiPriceDesc 多口价价格描述渲染
func GetMultiPriceDesc(ctx context.Context, prov MultiPriceDescProvider) (descList []*proto.MultiPriceDesc) {

	if prov.GetProductCategory() == ProductCategory.ProductCategoryCarpoolStation {
		conf := getCarpool3DcmpConfig(ctx)
		if conf == nil {
			return
		}

		successCouponInfo := prov.GetCouponInfo()

		if prov.IsCarpoolV3Merge(ctx) {
			// 合并价格
			successDesc := &proto.MultiPriceDesc{
				PriceInfo: util.ReplaceTag(ctx, conf.CapPriceMerge, map[string]string{
					"carpool_success_price": util.Float64ToString(prov.GetEstimateFee())}),
			}

			if redPacketFeePriceDesc := common_logic.GetFeeByFeeDetailInfo(ctx, prov.GetBillFeeDetailInfo()); redPacketFeePriceDesc != nil && len(redPacketFeePriceDesc.Content) > 0 {
				successDesc.Content = redPacketFeePriceDesc.Content
				successDesc.LeftIcon = redPacketFeePriceDesc.LeftIcon
			} else {
				setCouponInfo(ctx, successCouponInfo, successDesc)
			}

			descList = append(descList, successDesc)

		} else {
			// 未拼成价格
			var carpoolFailMsg string
			if carpool.IsCarpoolDualPriceV2(prov.GetCarpoolType(), prov.IsDualCarpoolPrice(), prov.GetCarpoolPriceType()) {
				carpoolFailMsg = conf.FailV2
			} else if fee, ok := prov.GetCarpoolFailEstimateFee(); ok {
				tag := map[string]string{
					"carpool_fail_price": util.Float64ToString(fee),
				}
				carpoolFailMsg = util.ReplaceTag(ctx, conf.FailV3, tag)
			}
			if len(carpoolFailMsg) > 0 {
				failDesc := &proto.MultiPriceDesc{
					PriceInfo: carpoolFailMsg,
				}

				if failedCouponInfo := prov.GetMultiPriceCouponInfoByScene(map[string]string{
					"pool_num":           "0",
					"seat_num":           "0",
					"is_carpool_success": "0",
				}); failedCouponInfo != nil {

					if redPacketFeePriceDesc := common_logic.GetFeeByFeeDetailInfo(ctx, prov.GetBillFeeDetailInfo()); redPacketFeePriceDesc != nil && len(redPacketFeePriceDesc.Content) > 0 {
						failDesc.Content = redPacketFeePriceDesc.Content
						failDesc.LeftIcon = redPacketFeePriceDesc.LeftIcon
					} else {
						setCouponInfo(ctx, failedCouponInfo, failDesc)
					}
				}
				descList = append(descList, failDesc)
			}

			// 拼成价格
			successDesc := &proto.MultiPriceDesc{
				PriceInfo: util.ReplaceTag(ctx, conf.Succ, map[string]string{
					"carpool_success_price": util.Float64ToString(prov.GetEstimateFee())}),
			}

			if FeeRedPacketPriceDesc := common_logic.GetFeeByFeeDetailInfo(ctx, prov.GetBillFeeDetailInfo()); FeeRedPacketPriceDesc != nil && len(FeeRedPacketPriceDesc.Content) > 0 {
				successDesc.Content = FeeRedPacketPriceDesc.Content
				successDesc.LeftIcon = FeeRedPacketPriceDesc.LeftIcon
			} else {
				setCouponInfo(ctx, successCouponInfo, successDesc)
			}
			descList = append(descList, successDesc)
		}

		return
	}
	return
}

func setCouponInfo(ctx context.Context, info *PriceApi.EstimateNewFormCouponInfo, successDesc *proto.MultiPriceDesc) {
	if info != nil {
		couponAmount, err := strconv.ParseFloat(info.Amount, 64)
		if err != nil || couponAmount == 0 {
			return
		}
		couponAmount = couponAmount / 100

		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "coupon_desc"); content != "" {
			var tag = map[string]string{
				"amount": strconv.FormatFloat(couponAmount, 'f', -1, 64),
			}
			successDesc.Content, successDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), ""
		}
	}
}

func getCarpool3DcmpConfig(ctx context.Context) *DcmpConf {
	conf := new(DcmpConf)
	dcmpStr := dcmp.GetDcmpContent(ctx, "carpool_config-dual_price", nil)
	err := json.Unmarshal([]byte(dcmpStr), conf)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshal dcmp string fail with %v", err)
		return nil
	}
	return conf
}
