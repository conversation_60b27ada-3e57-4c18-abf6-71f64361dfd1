package anycar

import (
	"context"

	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
)

func GetBusinessLabelText(ctx context.Context, prov ProductProvider) string {

	if tripcloud.IsTripcloudProductID(bizCommonConsts.ProductID(prov.GetProductId())) {
		return dcmp.GetDcmpContent(ctx, "guide_anycar-business_label_text_with_trip_cloud", nil)
	}

	return ""
}
