package anycar

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
)

type RightSubTitleProvider interface {
	ProductProvider
	GetMultiRequireProduct() []models.RequireProduct
	GetPrivateBizInfo() *models.PrivateBizInfo
}

func GetRightSubTitle(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {
	subTitle := &proto.SubTitle{}

	if prov.GetPrivateBizInfo() == nil && len(prov.GetPrivateBizInfo().CompensationInfo) == 0 {
		return nil
	}

	if decision, ok := prov.GetPrivateBizInfo().CompensationInfo[compensation.NormalNoAnswerCompensation]; ok && decision != nil && decision.Decision == 1 {
		var content string
		if text, ok := decision.Extra["biz_name"]; ok && text != nil && util.ToString(text) != "" {
			content = util.ToString(text)
		}

		dcmpConf := dcmp.GetDcmpContent(ctx, "config_text-normal_compensation", nil)
		if dcmpConf != "" {
			subTitle = &proto.SubTitle{}
			err := json.Unmarshal([]byte(dcmpConf), subTitle)
			if err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal fail with config_text-normal_compensation with err %v", err)
			} else {
				if content != "" {
					subTitle.Content = content
				}
				return []*proto.SubTitle{subTitle}
			}
		}
	}

	return nil
}
