package category_unione

import (
	"context"

	mambaconsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
)

// GetPriceInfoDescListHarbour AnyCarEstimateController 调用
func GetPriceInfoDescListHarbour(ctx context.Context, full *biz_runtime.ProductInfoFull) []*proto.AnyCarPriceDescInfo {
	ret := make([]*proto.AnyCarPriceDescInfo, 0)
	cnt := 2 // 最大展示标签
	if full.GetLang() == mambaconsts.LangEnUS {
		cnt = 1
	}
	env := fee_desc_engine.NewEnv(consts.HarbourForm).SetApolloParams(full).SetDcmpKey(consts.DcmpKeyHarbourFeeDesc).SetCap(cnt)
	resp := fee_desc_engine.NewFeeEngine(input.BuildHkProductFeeInput(ctx, full), env).SetProductCategory(full.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.AnyCarPriceDescInfo{
			LeftIcon: output.Icon,
			Content:  output.Content,
		}

		ret = append(ret, desc)
	}

	return ret
}
