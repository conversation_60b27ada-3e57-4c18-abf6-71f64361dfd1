package anycar

import (
	"context"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/biz-lib-go/utils/tagreplace"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/bitly/go-simplejson"
)

type SeatNumProvider interface {
	BaseProvider
	ProductProvider
	RequestProvider
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
}

func GetCarpoolSeatNum(ctx context.Context, prov SeatNumProvider) []*proto.SeatNum {
	if !carpool.IsCarpool(prov.GetCarpoolType()) {
		return nil
	}

	var carpoolSeatNum int32
	if prov.GetCarpoolSeatNum() != 0 {
		carpoolSeatNum = prov.GetCarpoolSeatNum()
	}
	if prov.GetPassengerCount() != nil && carpoolSeatNum == 0 &&
		carpool.IsLowPriceCarpoolByInfos(prov.GetComboType(), prov.GetProductId(), prov.GetCarpoolType(), prov.GetRequireLevel()) {
		carpoolSeatNum = *prov.GetPassengerCount()
	}
	carpoolSeats, err := buildCarpoolSeats(ctx, carpoolSeatNum)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "err:%v", err)
	}
	return carpoolSeats
}

func buildCarpoolSeats(ctx context.Context, carpoolSeatNum int32) ([]*proto.SeatNum, error) {
	config := dcmp.GetDcmpContent(ctx, "config_text-pget_order_match_info", nil)
	jsonConfig, err := simplejson.NewJson([]byte(config))
	if err != nil {
		return nil, err
	}
	textModel, err := jsonConfig.Get("guide_carpool_confirm_seat_num").String()
	if err != nil {
		return nil, err
	}
	tr := tagreplace.NewDefaultTagReplacer()
	oneSeatText := tr.ReplaceTag(textModel, map[string]string{"num": "1"})
	var oneIsSelected int64
	var twoIsSelected int64
	if 1 == carpoolSeatNum {
		oneIsSelected = 1
	}
	twoSeatText := tr.ReplaceTag(textModel, map[string]string{"num": "2"})
	if 2 == carpoolSeatNum {
		twoIsSelected = 1
	}

	res := []*proto.SeatNum{{
		Text:       oneSeatText,
		Num:        1,
		IsSelected: oneIsSelected,
	}, {
		Text:       twoSeatText,
		Num:        2,
		IsSelected: twoIsSelected,
	}}
	return res, nil
}
