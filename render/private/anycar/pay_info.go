package anycar

import (
	"context"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/biz-common-go/i18n"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/bitly/go-simplejson"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type PayInfoProvider interface {
	BaseProvider
	RequestProvider
	ProductProvider
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
}

type BuildTagParam struct {
	Payment      []*PriceApi.PaymentElem
	MenuID       string
	OrderPayType int32
	DeductFee    float64
}

func GetPayInfo(ctx context.Context, prov PayInfoProvider) *PayInfo {

	var (
		deductFee float64
		payment   []*PriceApi.PaymentElem
	)
	if prov.GetPaymentInfo() != nil && prov.GetPaymentInfo().MixedPayDeductInfo != nil {
		deductFee = prov.GetPaymentInfo().MixedPayDeductInfo.DeductFee
	}

	if prov.GetPaymentInfo() != nil {
		payment = prov.GetPaymentInfo().Payment
	}
	var paymentsType = prov.GetUserChoosePayment()

	buildTagParam := BuildTagParam{
		Payment:      payment,
		MenuID:       prov.GetMenuId(),
		OrderPayType: paymentsType,
		DeductFee:    deductFee,
	}
	userPayTag := buildPayTag(ctx, buildTagParam)
	pUserPayInfo := &proto.UserPayInfo{
		PaymentId:        int64(userPayTag.PayFlag),
		BusinessConstSet: int64(userPayTag.BusinessConstSet),
		PaymentInfo:      userPayTag.UserPayInfo,
	}

	return &PayInfo{
		PayTag:      userPayTag.PayTag,
		PayFlag:     userPayTag.PayFlag,
		UserPayInfo: pUserPayInfo,
	}
}

type PayInfo struct {
	PayTag      string
	PayFlag     int32
	UserPayInfo *proto.UserPayInfo
}

type PayTag struct {
	PayTag           string
	PayFlag          int32
	BusinessConstSet int32
	UserPayInfo      *proto.PaymentInfo
}

func buildPayTag(ctx context.Context, param BuildTagParam) PayTag {
	payments := param.Payment
	menuID := param.MenuID
	orderPayType := param.OrderPayType
	if menuID == "dache_anycar" {
		if payments == nil || len(payments) == 0 {
			return PayTag{}
		}
		for _, payment := range payments {
			if payment.IsSelected != nil && 1 == *payment.IsSelected {
				userInfo := getUserPayInfo(ctx, payment, param.DeductFee)
				return *userInfo
			}
		}
	}
	if orderPayType != 21 {
		return PayTag{}
	}
	config := dcmp.GetDcmpContent(ctx, "config_text-panycar_estimate", nil)
	jsonConfig, err := simplejson.NewJson([]byte(config))
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "new json fail: %v", err)
		return PayTag{}
	}
	personPayTag, err := jsonConfig.Get("person_pay").String()
	if err != nil {
		return PayTag{}
	}
	businessPayTag, err := jsonConfig.Get("business_pay").String()
	if err != nil {
		return PayTag{}
	}
	if payments == nil || len(payments) == 0 {
		return PayTag{
			PayTag:      personPayTag,
			PayFlag:     1,
			UserPayInfo: nil,
		}
	}
	for _, payment := range payments {
		if payment.PayType == 21 {
			return PayTag{
				PayTag:      businessPayTag,
				PayFlag:     2,
				UserPayInfo: nil,
			}
		}
	}
	return PayTag{
		PayTag:      personPayTag,
		PayFlag:     1,
		UserPayInfo: nil,
	}
}

func getUserPayInfo(ctx context.Context, elem *PriceApi.PaymentElem, deductFee float64) *PayTag {
	user := getUserPayInfoConfig(ctx, elem, deductFee)
	if user == nil {
		user = getDefaultPaymentInfo(ctx, elem)
	}
	return &PayTag{
		PayTag:           *elem.ChannelName,
		PayFlag:          elem.PayType,
		BusinessConstSet: elem.BusinessConstSet,
		UserPayInfo:      user,
	}

}

// TODO缓存
func getUserPayInfoConfig(ctx context.Context, elem *PriceApi.PaymentElem, deductFee float64) *proto.PaymentInfo {
	configIndex := strconv.Itoa(int(elem.PayType))
	if deductFee > 0 && *elem.NeedSeparate == 1 && elem.PayType == 21 {
		configIndex = "m_" + strconv.Itoa(int(elem.PayType)) + "_" + strconv.Itoa(int(elem.MixedPayType))
	}
	var localLang string
	condition := model.NewDefaultCondition().With("payment_type", configIndex)
	ret, err := apollo.GetConfigsByNamespaceAndConditions(ctx, "payment_type", condition)
	if err != nil {
		return nil
	}
	jsonConfig, err := simplejson.NewJson(ret)
	if err != nil {
		return nil
	}
	arrs, err := jsonConfig.Array()
	if err != nil {
		return nil
	}
	for i := 0; i < len(arrs); i++ {
		lang := reqctx.GetLang(ctx)
		localLang, err = jsonConfig.GetIndex(i).Get("__language").String()
		if err != nil {
			return nil
		}
		if lang == localLang {
			jsonConfig = jsonConfig.GetIndex(i)
			break
		}
	}
	//jsonConfig = jsonConfig.GetIndex(0)
	content, err := jsonConfig.Get("content").String()
	if err != nil {
		return nil
	}
	fontColor, err := jsonConfig.Get("font_color").String()
	if err != nil {
		return nil
	}
	highlightFontColor, _ := jsonConfig.Get("highlight_font_color").String()
	backgroundColor, _ := jsonConfig.Get("background_color").String()
	borderColor, _ := jsonConfig.Get("border_color").String()
	backgroundTransparency, _ := jsonConfig.Get("background_transparency").String()
	res := proto.PaymentInfo{
		Content:                content,
		FontColor:              fontColor,
		HighlightFontColor:     highlightFontColor,
		BackgroundColor:        backgroundColor,
		BorderColor:            borderColor,
		BackgroundTransparency: backgroundTransparency,
	}

	return &res
}

func getDefaultPaymentInfo(ctx context.Context, elem *PriceApi.PaymentElem) *proto.PaymentInfo {
	// 兜底的内层支付方式
	var ret = proto.PaymentInfo{
		Content:                "个人付",
		FontColor:              "#000000",
		HighlightFontColor:     "",
		BackgroundColor:        "#FFFFFF",
		BorderColor:            "#999999",
		BackgroundTransparency: "100%",
	}
	if elem.PayType == 23 {
		ret.Content = "个人支付"
	}
	lang := reqctx.GetLang(ctx)
	if lang == string(i18n.LanguageEnUS) {
		ret.Content = "Personal Pay"
	}
	return &ret
}
