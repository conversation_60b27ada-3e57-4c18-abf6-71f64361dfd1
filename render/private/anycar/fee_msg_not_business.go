package anycar

import (
	"context"
	"encoding/json"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_unione"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	trace "git.xiaojukeji.com/lego/context-go"
)

type PriceDescProvider interface {
	render.BaseProvider
	render.BillInfoProvider
	render.ProductProvider
	IsCarpoolV3Merge(context.Context) bool
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
	GetVCard() *PriceApi.EstimateNewFormVCardInfo

	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)

	render.ApolloProvider
}

func GetFeeMsg(ctx context.Context, prov PriceDescProvider) string {
	if util.InArrayInt64(prov.GetProductCategory(), []int64{
		ProductCategory.ProductCategoryCarpoolNormal, int64(ProductCategory.ProductCategoryCarpoolFlatRateBySeat)}) {

		// 站点拼车，拼成价格
		return GetCarpoolV3PriceDesc(ctx, prov)
	} else if prov.GetProductId() == product_id.ProductIdUniOne {
		// 出租车业务线
		return GetTaxiFeeMsg(ctx, prov)
	} else if product.IsHongKongProduct(consts.ProductID(prov.GetProductId())) {
		// 香港业务线
		return GetHKFeeMsg(ctx, prov)
	}

	return GetPriceDesc(ctx, prov)
}

func GetPriceDesc(ctx context.Context, prov PriceDescProvider) string {
	generalTmp := util2.GetGeneralFeeMsgTemplate(ctx, prov.GetCityID(), int(prov.GetProductCategory()), reqctx.GetLang(ctx))

	if util.InArrayInt32(prov.GetCarpoolPriceType(), []int32{11, 105, 108}) || order.IsCapFast(prov) {
		// 一口价
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
		tag := map[string]string{
			"num":             util.Float64ToString(prov.GetEstimateFee()),
			"total_fee":       util.Float64ToString(prov.GetEstimateFee()),
			"currency_unit":   unit,
			"currency_symbol": symbol,
		}

		if generalTmp != "" {
			return dcmp.TranslateTemplate(generalTmp, tag)
		}

		return dcmp.GetDcmpContent(ctx, "cap_price-price_desc", tag)
	}

	currencyUnit, currencySymbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
	tag := map[string]string{
		"num":             util.Float64ToString(prov.GetEstimateFee()),
		"total_fee":       util.Float64ToString(prov.GetEstimateFee()),
		"currency_unit":   currencyUnit,
		"currency_symbol": currencySymbol,
	}

	// 如果存在省钱卡-赠卡 或者 省钱卡-买卡 则展示”稳享拼成价xx元“
	// 获取Vcard服务
	vcardResult := prov.GetVCard()
	if vcardResult != nil {
		if carpool.SendNew == vcardResult.Source || carpool.SendLoss == vcardResult.Source || carpool.Usable == vcardResult.Source || carpool.UsableGive == vcardResult.Source {
			priceDesc := dcmp.GetDcmpContent(ctx, "anycar_estimate-new_free_card_top", tag)
			return priceDesc
		}
	}

	// 既没有省钱卡-赠卡，也没省钱卡-买卡，展示兜底
	if generalTmp != "" {
		return dcmp.TranslateTemplate(generalTmp, tag)
	}

	return dcmp.GetDcmpContent(ctx, "common-price_desc", tag)
}

func GetCarpoolV3PriceDesc(ctx context.Context, prov PriceDescProvider) string {

	var (
		conf   = new(DcmpConf)
		feeMsg string
	)

	dcmpStr := dcmp.GetDcmpContent(ctx, "carpool_config-dual_price", nil)
	err := json.Unmarshal([]byte(dcmpStr), conf)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshal dcmp string fail with %v", err)
		return ""
	}

	if prov.IsCarpoolV3Merge(ctx) {
		feeMsg = util.ReplaceTag(ctx, conf.CapPriceMerge, map[string]string{"carpool_success_price": util.Float64ToString(prov.GetEstimateFee())})
	} else {
		feeMsg = util.ReplaceTag(ctx, conf.Succ, map[string]string{"carpool_success_price": util.Float64ToString(prov.GetEstimateFee())})
	}

	return feeMsg
}

func GetTaxiFeeMsg(ctx context.Context, prov PriceDescProvider) string {
	var (
		priceDesc string
	)

	priceDesc = dcmp.GetDcmpContent(ctx, "unione-price_desc_without_price", nil)
	if category_unione.CheckTaxiShowEstimateFee(ctx, prov) {
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
		tag := map[string]string{
			"currency_symbol": symbol,
			"total_fee":       util.Float64ToString(prov.GetEstimateFee()),
			"currency_unit":   unit,
		}
		priceDesc = dcmp.GetDcmpContent(ctx, "common-price_desc", tag)
	}

	// 预估价前缀逻辑
	if prefix := GetPrefixText(ctx, prov); prefix != "" {
		priceDesc = prefix + priceDesc
	}

	return priceDesc
}

func GetHKFeeMsg(ctx context.Context, prov PriceDescProvider) string {
	var (
		priceDesc string
	)

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdHKTaxi {
		priceDesc = dcmp.GetDcmpContent(ctx, "hk_taxi-price_desc_without_price", nil)
		return priceDesc
	}

	tag := map[string]string{
		"num": util.Float64ToString(prov.GetEstimateFee()),
	}
	priceDesc = dcmp.GetDcmpContent(ctx, "hk_taxi-price_desc", tag)

	return priceDesc
}

// GetPrefixText 获取前缀文案
func GetPrefixText(ctx context.Context, prov PriceDescProvider) string {
	// 出租车一车两价品类，在预估价前加上"在线计价" 文案展示
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiMarketisationPutong ||
		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiMarketisationYouxuan {
		return dcmp.GetDcmpContent(ctx, "config-taxi_price_online_text", nil)
	}
	return ""
}
