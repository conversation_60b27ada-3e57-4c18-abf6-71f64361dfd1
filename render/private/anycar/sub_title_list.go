package anycar

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type SubTitleListProvider interface {
	ProductProvider
	GetMultiRequireProduct() []models.RequireProduct
	GetPrivateBizInfo() *models.PrivateBizInfo
}

func GetSubTitleList(ctx context.Context, prov SubTitleListProvider) []*proto.SubTitle {

	var subTitle *proto.SubTitle
	if prov.GetProductCategory() == ProductCategory.ProductCategoryAplus {
		if prov.GetPrivateBizInfo() == nil && len(prov.GetPrivateBizInfo().CompensationInfo) == 0 {
			return nil
		}

		if decision, ok := prov.GetPrivateBizInfo().CompensationInfo[compensation.BusinessAPlusInsurance]; ok && decision != nil && decision.Decision == 1 {
			dcmpConf := dcmp.GetDcmpContent(ctx, "config_text-intro_tags_nocar_aplus", nil)
			if dcmpConf != "" {
				subTitle = &proto.SubTitle{}
				err := json.Unmarshal([]byte(dcmpConf), subTitle)
				if err != nil {
					log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal fail with intro_tags_nocar_aplus with err %v", err)
				} else {
					return []*proto.SubTitle{subTitle}
				}
			}
		}
	}

	return nil
}
