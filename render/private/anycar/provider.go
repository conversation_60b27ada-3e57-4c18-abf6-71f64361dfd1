package anycar

type ProductProvider interface {
	GetProductId() int64
	GetProductCategory() int64
	GetCarpoolType() int64
	GetComboType() int64
	GetRequireLevel() string
	GetCarpoolPriceType() int32
	IsDualCarpoolPrice() bool
}

type BaseProvider interface {
	GetCityID() int
	GetPID() int64
	GetAccessKeyId() int32
	GetMenuId() string
}

type RequestProvider interface {
	GetUserChoosePayment() int32
	GetCarpoolSeatNum() int32
	GetPassengerCount() *int32
}
