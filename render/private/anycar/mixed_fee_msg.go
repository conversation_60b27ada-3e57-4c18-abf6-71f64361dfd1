package anycar

import (
	"context"
	"fmt"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type MixedFeeMsgProvider interface {
	ProductProvider
	GetEstimateFee() float64
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
	GetBillInfoCurrency() string
}

func GetMixedFeeMsg(ctx context.Context, prov MixedFeeMsgProvider) *proto.MixedFeeMsg {

	if prov.GetPaymentInfo() == nil || prov.GetPaymentInfo().MixedPayDeductInfo == nil {
		return nil
	}

	var (
		actualPayFee, needPayFee, deductFee float64
	)

	if prov.GetPaymentInfo() != nil && prov.GetPaymentInfo().MixedPayDeductInfo != nil {
		deductFee = prov.GetPaymentInfo().MixedPayDeductInfo.DeductFee
	}
	mixedPayDeductInfo := prov.GetPaymentInfo().MixedPayDeductInfo
	needPayFee = prov.GetEstimateFee()
	if needPayFee < deductFee {
		actualPayFee = 0
	} else {
		actualPayFee = needPayFee - deductFee
	}

	unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
	params := map[string]string{
		"fee":             util.FormatPrice(actualPayFee, -1),
		"sub_fee":         util.FormatPrice(deductFee, -1),
		"currency_unit":   unit,
		"currency_symbol": symbol,
	}
	sj1, err := dcmp.GetTextObject(ctx, "guide_anycar-payment_type_mixed_fee_msg", params)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "new json fail: %v", err)
		return nil
	}
	key := fmt.Sprintf("m_%d_%d", mixedPayDeductInfo.CarPayType, mixedPayDeductInfo.MixedPayType)
	if sj2, ok := sj1.CheckGet(key); !ok {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "checkGetMissed")
		return nil
	} else {
		sj3, err := sj2.Get("fee_msg").String()
		if err != nil {
			return nil
		}
		sj4, err := sj2.Get("sub_fee_msg").String()
		if err != nil {
			return nil
		}
		return &proto.MixedFeeMsg{
			FeeMsg:    sj3,
			SubFeeMsg: sj4,
		}
	}
}
