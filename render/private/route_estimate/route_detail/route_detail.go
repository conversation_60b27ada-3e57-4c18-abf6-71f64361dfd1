package route_detail

import (
	"context"
	"encoding/json"
	"fmt"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/security"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/route_estimate/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	apolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"strconv"
)

type GetDetailBillRespData struct {
	BubbleSubTitle *string `json:"bubble_sub_title,omitempty" form:"bubble_sub_title"`
	TotalFeeText   *string `json:"total_fee_text,omitempty" form:"total_fee_text"`
}

// BuildRouteDetail 构建路线价格信息
func BuildRouteDetail(ctx context.Context, serviceReq *model.ServiceReq, fulls []*biz_runtime.ProductInfoFull) []*proto.RouteDetail {
	var (
		estimateFee float64
		feeMsg      string
	)
	routeDetailList := make([]*proto.RouteDetail, 0)
	for _, productFull := range fulls {
		if productFull == nil {
			continue
		}

		// 多路线请求，当前路线的价格使用价格组件（真实价格），不使用重新预估的结果
		if serviceReq != nil && serviceReq.OriginRequest != nil && len(serviceReq.OriginRequest.RouteIdList) > 1 &&
			serviceReq.OriginRequest.RouteIdList[0] == productFull.Product.RouteID {
			routeDetail := buildCurrentRouteDetail(ctx, productFull)
			if routeDetail != nil {
				routeDetailList = append(routeDetailList, routeDetail)
				continue
			}
		}

		estimateFee, feeMsg = buildFeeInfo(ctx, serviceReq, productFull)
		routeDetailList = append(routeDetailList, &proto.RouteDetail{
			EstimateId:      productFull.Product.EstimateID,
			RouteId:         productFull.Product.RouteID,
			EstimateFee:     estimateFee,
			FeeMsg:          feeMsg,
			BubbleMsg:       buildBubbleMsg(ctx, serviceReq, productFull),
			OriginBills:     buildOriginBills(ctx, serviceReq, productFull),
			DynamicTotalFee: productFull.GetDynamicTotalFee(),
		})
	}

	return routeDetailList
}

func buildFeeInfo(ctx context.Context, req *model.ServiceReq, full *biz_runtime.ProductInfoFull) (float64, string) {
	estimateFee := buildEstimateFee(ctx, req, full.GetEstimateFee())
	feeMsg := buildFeeMsg(ctx, estimateFee, full)

	return estimateFee, feeMsg
}

func buildBubbleMsg(ctx context.Context, req *model.ServiceReq, full *biz_runtime.ProductInfoFull) string {
	env := fee_desc_engine.NewEnv(consts.RouteEstimateForm).SetApolloParams(full).SetDcmpKey(consts.RouteEstimateFeeDesc)
	feeList := fee_desc_engine.NewFeeEngine(input.BuildNormalFeeInput(ctx, full, consts.RouteEstimateForm), env).SetProductCategory(full.GetProductCategory()).Do(ctx)
	if len(feeList) <= 0 {
		return ""
	}

	// 返回第一个减价项
	for _, feeItem := range feeList {
		if feeItem != nil && feeItem.Type == consts.TypeDecrement {
			return feeItem.Content
		}
	}

	return ""
}

func buildFeeMsg(ctx context.Context, estimateFee float64, full *biz_runtime.ProductInfoFull) string {
	currencyUnit, currencySymbol := util.GetCurrencyUnitAndSymbol(ctx, full.GetProductId(), full.GetBillInfoCurrency())

	tag := map[string]string{
		"num":             util.Float64ToString(estimateFee),
		"total_fee":       util.Float64ToString(estimateFee),
		"fee_amount":      util.Float64ToString(estimateFee),
		"currency_unit":   currencyUnit,
		"currency_symbol": currencySymbol,
	}

	if full.GetCapPrice() > 0 {
		feeTemplate := dcmp.GetDcmpPlainContent(ctx, "cap_price-price_desc")
		generalTmp := util2.GetGeneralFeeMsgTemplate(ctx, full.GetCityID(), int(full.GetProductCategory()), full.GetLang())
		if len(generalTmp) >= 0 {
			feeTemplate = generalTmp
		}
		return dcmp.TranslateTemplate(feeTemplate, tag)
	} else {
		return dcmp.GetJSONContentWithPath(ctx, "route_estimate-fee_msg", tag, "default")
	}
}

// buildOriginBills ...
func buildOriginBills(ctx context.Context, serviceReq *model.ServiceReq, product *biz_runtime.ProductInfoFull) *proto.OriginBills {
	if serviceReq == nil || product == nil {
		return nil
	}

	if serviceReq.OriginBills[product.GetEstimateID()] != nil {
		return serviceReq.OriginBills[product.GetEstimateID()]
	}

	if product.GetBizInfo() != nil && product.GetBizInfo().OriginBills != nil {
		originBillsData := product.GetBizInfo().OriginBills
		return &proto.OriginBills{
			DriverMinute:                originBillsData.DriverMinute,
			DriverMetre:                 originBillsData.DriverMetre,
			LowSpeedTime:                originBillsData.LowSpeedTime,
			EstimateId:                  originBillsData.EstimateId,
			PassengerOrderResult:        buildOrderResult(ctx, originBillsData.PassengerOrderResult),
			DriverOrderResult:           buildOrderResult(ctx, originBillsData.DriverOrderResult),
			MultiRouteArgs:              originBillsData.MultiRouteArgs,
			RbArgs:                      originBillsData.RbArgs,
			ModifyDestEstimateFixedFees: originBillsData.ModifyDestEstimateFixedFees,
		}
	}

	return nil
}

func buildEstimateFee(ctx context.Context, serviceReq *model.ServiceReq, estimateFee float64) float64 {
	var (
		userID      string
		phone       string
		orderStatus string
		lang        string
		appVersion  string
		accessKeyID string
		city        string
		caller      string

		accuracy int32
		baseReq  *models.BaseReqData
	)

	if serviceReq != nil && serviceReq.UserInfo != nil {
		userID = fmt.Sprintf("%d", serviceReq.UserInfo.UID)
		phone = serviceReq.UserInfo.Phone
	}

	if serviceReq != nil && serviceReq.OrderInfo != nil {
		orderStatus = serviceReq.OrderInfo.OrderStatus
	}

	if serviceReq != nil && serviceReq.BaseReq != nil {
		baseReq = serviceReq.BaseReq
		lang = baseReq.CommonInfo.Lang
		appVersion = baseReq.CommonInfo.AppVersion
		accessKeyID = fmt.Sprintf("%d", baseReq.CommonInfo.AccessKeyID)
		city = fmt.Sprintf("%d", baseReq.AreaInfo.Area)
	}

	if serviceReq != nil && serviceReq.OriginRequest != nil && serviceReq.OriginRequest.Caller != nil {
		caller = *serviceReq.OriginRequest.Caller
	}

	user := apolloModel.NewUser(userID).
		With("order_status", orderStatus).
		With("phone", phone).
		With("lang", lang).
		With("city", city).
		With("app_version", appVersion).
		With("access_key_id", accessKeyID).
		With("caller", caller)

	toggle, err := apollo.FeatureToggle("gs_route_estimate_accuracy_control", user)
	if err != nil || !toggle.IsAllow() {
		return decimal.NewFromFloat(estimateFee).Ceil().InexactFloat64()
	}

	assignment := toggle.GetAssignment()
	if assignment != nil {
		accuracy = util.String2int32(ctx, assignment.GetParameter("accuracy", "0"))
	}

	if accuracy > 0 {
		return decimal.NewFromFloat(estimateFee).RoundCeil(accuracy).InexactFloat64()
	} else {
		return decimal.NewFromFloat(estimateFee).Ceil().InexactFloat64()
	}
}

func buildOrderResult(ctx context.Context, result *PriceApi.OrderResult) *proto.OrderResult {
	if result == nil {
		return nil
	}

	orderResult := &proto.OrderResult{
		TotalFee:      result.TotalFee,
		CapPrice:      result.CapPrice,
		PreTotalFee:   result.PreTotalFee,
		FenceId:       result.FenceId,
		FeeDetailInfo: result.FeeDetailInfo,
		ExtraInfo:     result.ExtraInfo,
	}

	billExtra := result.BillExtra
	if billExtra != nil {
		orderResult.BillExtra = &proto.BillExtra{
			CompanyCarpoolFlag:     billExtra.CompanyCarpoolFlag,
			PlanInterceptDiffFee:   billExtra.PlanInterceptDiffFee,
			RealtimePriceBack:      billExtra.RealtimePriceBack,
			BasicTotalFee:          billExtra.BasicTotalFee,
			PreTotalFee:            billExtra.PreTotalFee,
			DynamicTotalFee:        billExtra.DynamicTotalFee,
			TotalFeeNotIntercept:   billExtra.TotalFeeNotIntercept,
			FreeCommission:         billExtra.FreeCommission,
			FreeCommissionExtraFee: billExtra.FreeCommissionExtraFee,
		}
	}

	return orderResult
}

func BuildExtraInfo(ctx context.Context, serviceReq *model.ServiceReq, productFulls []*biz_runtime.ProductInfoFull) map[string]string {
	if serviceReq == nil || serviceReq.UserInfo == nil || serviceReq.OrderInfo == nil || serviceReq.OriginRequest == nil {
		return nil
	}

	if len(productFulls) <= 0 {
		return nil
	}
	feePrefix, _, feeType := fee_info_render.BuildFeeInfoByOrder(ctx, serviceReq.OrderInfo, productFulls[0])

	extraInfo := make(map[string]string)
	extraInfo["estimate_id"] = serviceReq.OrderInfo.EstimateId
	extraInfo["order_id"] = serviceReq.OriginRequest.Oid
	extraInfo["order_status"] = serviceReq.OrderInfo.OrderStatus
	extraInfo["access_key_id"] = util.Int2String(int(serviceReq.OriginRequest.AccessKeyId))
	extraInfo["fee_prefix"] = feePrefix
	extraInfo["fee_type"] = util.Int2String(feeType)

	userInfo, err := security.AesEncryptMsg(util.JustJsonEncode(model.UserInfo{
		Pid:   int64(serviceReq.UserInfo.PID),
		Uid:   int64(serviceReq.UserInfo.UID),
		Phone: serviceReq.UserInfo.Phone,
	}))
	if err == nil {
		extraInfo["user_info"] = userInfo
	}

	// 卫士
	// 读实验
	param := apolloModel.NewUser(strconv.FormatInt(int64(serviceReq.UserInfo.PID), 10)).
		With("key", strconv.FormatInt(int64(serviceReq.UserInfo.PID), 10)).
		With("pid", strconv.FormatInt(int64(serviceReq.UserInfo.PID), 10)).
		With("city", serviceReq.OrderInfo.Area).
		With("city_id", serviceReq.OrderInfo.Area).
		With("p_access_key_id", util.Int2String(int(serviceReq.OriginRequest.AccessKeyId))).
		With("app_version", serviceReq.OriginRequest.AppVersion).
		With("lang", serviceReq.OriginRequest.Lang).
		With("product_id", strconv.FormatInt(serviceReq.OrderInfo.GetProductID(), 10)).
		With("combo_type", strconv.FormatInt(serviceReq.OrderInfo.GetComboType(), 10)).
		With("carpool_type", strconv.FormatInt(serviceReq.OrderInfo.GetCarpoolType(), 10)).
		With("caller", "mamba")

	toggle, err := apollo.FeatureToggle("protector_vision", param)

	if err != nil {
		return extraInfo
	}

	if !toggle.IsAllow() || toggle.GetAssignment() == nil {
		return extraInfo
	}

	if toggle.GetAssignment().GetGroupName() != "treatment_group_1" && toggle.GetAssignment().GetGroupName() != "treatment_group_2" {
		return extraInfo
	}
	configStr := dcmp.GetDcmpPlainContent(ctx, "config_text-bodyguard_conf")
	if configStr == "" {
		return extraInfo
	}

	var conf map[string]string
	err = json.Unmarshal([]byte(configStr), &conf)
	if err != nil {
		return extraInfo
	}

	extraInfo["icon"] = conf["left_icon"]

	return extraInfo
}

func buildCurrentRouteDetail(ctx context.Context, productFull *biz_runtime.ProductInfoFull) *proto.RouteDetail {
	var (
		estimateFee float64
		feeMsg      string
		detailData  *GetDetailBillRespData
	)
	if detailBill, ok := productFull.ExtraInfo["detail_bill"]; ok && detailBill != "" {
		err := json.Unmarshal([]byte(detailBill), &detailData)
		if err == nil && detailData != nil && detailData.BubbleSubTitle != nil && detailData.TotalFeeText != nil {
			estimateFee = cast.ToFloat64(*(detailData.BubbleSubTitle))
			feeMsg = buildFeeMsg(ctx, estimateFee, productFull)
			routeDetail := &proto.RouteDetail{
				EstimateId:      productFull.Product.EstimateID,
				RouteId:         productFull.Product.RouteID,
				EstimateFee:     estimateFee,
				FeeMsg:          feeMsg,
				BubbleMsg:       "",
				OriginBills:     nil,
				DynamicTotalFee: productFull.GetDynamicTotalFee(),
			}
			return routeDetail
		}
	}
	return nil
}
