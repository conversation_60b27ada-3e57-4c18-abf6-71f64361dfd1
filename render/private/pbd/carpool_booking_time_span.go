package pbd

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_estimate/data"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_booking_time_module"
)

type carpoolBookingTimeSpan struct {
	adapter *data.PBDEstimateAdapter
}

func NewCarpoolBookingTimeSpan(adapter *data.PBDEstimateAdapter) *carpoolBookingTimeSpan {
	return &carpoolBookingTimeSpan{
		adapter: adapter,
	}
}

func (c *carpoolBookingTimeSpan) Render(ctx context.Context) []*proto.B2BTimeSpan {
	if c.adapter == nil {
		return nil
	}

	if len(c.adapter.GetTimeSpan()) <= 0 {
		return nil
	}

	// 城际
	if carpool.IsInterCityCarpool(c.adapter.GetCarpoolType(), c.adapter.GetComboType()) {
		return c.buildIntercityTimeSpan(ctx)
	} else {
		return c.buildPinCheCheTimeSpan(ctx)
	}
}

func (c *carpoolBookingTimeSpan) buildIntercityTimeSpan(ctx context.Context) []*proto.B2BTimeSpan {
	carpoolBookingTimeModule := carpool_booking_time_module.InterCityCarpoolBookingTime(ctx, c.adapter, 0)
	if carpoolBookingTimeModule == nil {
		return nil
	}

	resTimeSpanList := make([]*proto.B2BTimeSpan, 0)
	timeSpanList := carpoolBookingTimeModule.GetTimeSpan()
	for _, timeSpanItem := range timeSpanList {
		if timeSpanItem == nil {
			continue
		}

		resTimeSpanList = append(resTimeSpanList, &proto.B2BTimeSpan{
			Title:     timeSpanItem.Date,
			TimeRange: timeSpanItem.Range,
			Day:       &timeSpanItem.Day,
			Selected:  &timeSpanItem.Selected,
		})
	}

	return resTimeSpanList
}

func (c *carpoolBookingTimeSpan) buildPinCheCheTimeSpan(ctx context.Context) []*proto.B2BTimeSpan {
	resTimeSpanList := make([]*proto.B2BTimeSpan, 0)

	if bytes, err := json.Marshal(c.adapter.GetTimeSpan()); err == nil {
		if err = json.Unmarshal(bytes, &resTimeSpanList); err == nil {
			return resTimeSpanList
		}
	}

	return nil
}
