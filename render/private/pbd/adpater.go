package pbd

import (
	"context"
	"encoding/json"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/view/common_view_logic"
	LegoContext "git.xiaojukeji.com/lego/context-go"
)

type CarpoolInfo struct {
	coupon           *PriceApi.EstimateNewFormCouponInfo
	fee              float64
	SeatNum          string
	PoolNum          string
	IsCarpoolSuccess string
	PbdCallReturn    *PriceApi.NewFormPbdCallReturn
}

func Render(ctx context.Context, baseReqData *models.BaseReqData, pf []*biz_runtime.ProductInfoFull) (*proto.B2BEstimateData, error) {
	if len(pf) == 0 {
		return nil, nil
	}
	resp := &proto.B2BEstimateData{
		EstimateTraceId:         LegoContext.GetTrace(ctx).GetTraceId(),
		Products:                []*proto.B2BBizItem{},
		IsSupportMultiSelection: true,
	}
	if baseReqData.CommonInfo.OrderType != 0 {
		resp.IsSupportMultiSelection = false
	}
	for _, product := range pf {
		b2BProduct := BuildB2BProduct(product)
		if b2BProduct == nil {
			continue
		}
		b2BBill := BuildB2BBill(product)
		if b2BBill == nil {
			continue
		}
		b2BNormalFee := BuildNormalFee(product)
		b2BCarpoolFee := BuildCarpoolFee(product)
		b2BBiz := BuildB2BBiz(ctx, product)
		resp.Products = append(resp.Products, &proto.B2BBizItem{
			Product:    b2BProduct,
			Bill:       b2BBill,
			NormalFee:  b2BNormalFee,
			CarpoolFee: b2BCarpoolFee,
			EstimateId: product.Product.EstimateID,
			Biz:        b2BBiz,
		})
	}
	return resp, nil
}

func BuildB2BProduct(product *biz_runtime.ProductInfoFull) (b2BProduct *proto.B2BProduct) {
	if product == nil || product.GetBillDetail() == nil {
		return nil
	}

	var comboID *int64
	if product.GetBizInfo() != nil {
		comboID = &product.GetBizInfo().ComboID
	}
	return &proto.B2BProduct{
		ProductCategory: int32(product.Product.ProductCategory),
		OrderType:       int32(product.Product.OrderType),
		ProductId:       int32(product.Product.ProductID),
		BusinessId:      int32(product.Product.BusinessID),
		RequireLevel:    int32(product.Product.RequireLevelInt),
		LevelType:       product.Product.LevelType,
		ComboType:       int32(product.Product.ComboType),
		IsSpecialPrice:  product.Product.IsSpecialPrice,
		AirportType:     &product.Product.AirportType,
		RailwayType:     &product.Product.RailwayType,
		CarpoolType:     int32(product.Product.CarpoolType),
		ComboId:         comboID,
	}
}

func BuildB2BBill(product *biz_runtime.ProductInfoFull) (b2BBill *proto.B2BBill) {
	if product == nil || product.GetBillDetail() == nil {
		return nil
	}
	displayLines := []*proto.DisplayLine{}
	billDisplayLines := product.GetBillDisplayLines()
	if billDisplayLines != nil {
		for _, v := range billDisplayLines {
			if v == nil {
				continue
			}
			displayLines = append(displayLines, &proto.DisplayLine{
				Name:  v.Name,
				Value: v.Value,
			})
		}
	}

	return &proto.B2BBill{
		DriverMetre:          product.GetBillDriverMetre(),  //里程
		DriverMinute:         product.GetBillDriverMinute(), //时间
		DynamicTotalFee:      product.GetDynamicTotalFee(),  //券前价
		TotalFee:             product.GetBillTotalFee(),
		CapPrice:             product.GetCapPrice(),                //一口价
		HighwayFee:           product.GetHighWayFee(),              //高速路桥费
		CrossCityFee:         product.GetBillDetail().CrossCityFee, //跨城费
		FeeDetailInfo:        product.GetBillFeeDetailInfo(),       //费用详情
		CountPriceType:       int64(product.GetBillDetail().CountPriceType),
		RouteIdList:          product.GetBillRouteIdList(),
		DisplayLines:         displayLines,
		TripCloudDiscountFee: product.GetBillDetail().TripCloudDiscountFee,  //网开台折扣
		BasicTotalFee:        product.GetBasicTotalFee(),                    //可抵扣金额
		DiscountDesc:         util.JustJsonEncode(product.GetDiscountSet()), //折扣详情
		DynamicMemberReduce:  product.GetMemberDynamicProtectFee(),          // 会员溢价保护费
		DesignatedDriverFee:  product.GetBillDetail().DesignatedDriverFee,   // 豪华车司务员
		TaxiSpDiscountFee:    product.GetBillDetail().TaxiSpDiscountFee,     // 出租车愿等
		DynamicDiffPrice:     product.GetNewDynamicDiffPrice(),              //动调
	}
}

func BuildNormalFee(product *biz_runtime.ProductInfoFull) *proto.B2BFee {
	if product == nil {
		return nil
	}

	b2BFee := &proto.B2BFee{
		EstimateFee:         product.GetEstimateFee(),
		PersonalEstimateFee: product.GetPersonalEstimateFee(),
		ExactEstimateFee:    product.GetExactEstimateFee(),
	}

	if product.GetCouponInfo() != nil {
		amount, _ := strconv.Atoi(product.GetCouponInfo().Amount)
		b2BFee.CouponInfo = &proto.CouponInfo{
			BatchId:    product.GetCouponInfo().BatchId,
			CouponId:   product.GetCouponInfo().CouponId,
			Amount:     int32(amount),
			CouponType: int32(2),
		}
	}

	// pbd呼返补贴
	if product.GetPBDCallReturn() != nil {
		b2BFee.PbdCallReturn = float64(product.GetPBDCallReturn().Amount) / 100
	}

	return b2BFee
}

func BuildCarpoolFee(product *biz_runtime.ProductInfoFull) []*proto.B2BCarpoolFee {
	if product == nil || product.GetCarpoolType() <= 0 {
		return nil
	}
	b2BCarpoolFee := []*proto.B2BCarpoolFee{}

	// 占位项
	if carpoolFee := getOuterCarpoolFee(product); carpoolFee != nil {
		b2BCarpoolFee = append(b2BCarpoolFee, carpoolFee)
	}

	// 衍生项
	if extendCarpoolInfo := getExtendCarpoolInfo(product); extendCarpoolInfo != nil {
		b2BCarpoolFee = append(b2BCarpoolFee, extendCarpoolInfo...)
	}

	return b2BCarpoolFee
}

func getOuterCarpoolFee(product *biz_runtime.ProductInfoFull) (ret *proto.B2BCarpoolFee) {
	if product == nil {
		return nil
	}

	var poolNum int32

	if carpool.IsCarpoolDualPriceV3(int32(product.GetCarpoolType()), product.GetCarpoolPriceType(), product.GetIsDualCarpoolPrice()) {
		ret = &proto.B2BCarpoolFee{
			SeatNum: 1,
		}
		poolNum = 0
	} else if carpool.IsPinCheCheV2(product.GetProductCategory(), product.GetCarpoolPriceType()) {
		// 外层是1人拼2人
		ret = &proto.B2BCarpoolFee{
			SeatNum: 1,
		}
		poolNum = 2
	} else if carpool.IsInterCityCarpool(product.GetCarpoolType(), product.GetComboType()) {
		return buildIntercityCarpoolFee(product)
	} else {
		return
	}

	preTotalFee := product.GetPreTotalFee()
	ret.PriceInfo = &proto.B2BPriceInfo{
		IsCarpoolSuccess: true,
		EstimateFee:      product.GetEstimateFee(),
		DiscountFee:      util.RoundAbs(preTotalFee-product.GetEstimateFee(), 2),
		PoolNum:          poolNum,
	}

	// pbd呼返补贴
	if product.GetPBDCallReturn() != nil {
		ret.PriceInfo.PbdCallReturn = float64(product.GetPBDCallReturn().Amount) / 100
	}

	if product.GetCouponInfo() != nil {
		amount, _ := strconv.Atoi(product.GetCouponInfo().Amount)
		ret.PriceInfo.CouponInfo = &proto.CouponInfo{
			BatchId:    product.GetCouponInfo().BatchId,
			CouponId:   product.GetCouponInfo().CouponId,
			Amount:     int32(amount),
			CouponType: int32(2),
		}
	}

	return
}

// buildIntercityCarpoolFee ...
func buildIntercityCarpoolFee(product *biz_runtime.ProductInfoFull) *proto.B2BCarpoolFee {
	var carpoolSeatNum int32
	carpoolSeatNum = 1
	if product.GetUserOption() != nil && product.GetUserOption().CarpoolSeatNum >= 1 {
		carpoolSeatNum = product.GetUserOption().CarpoolSeatNum
	}
	ret := &proto.B2BCarpoolFee{
		SeatNum: carpoolSeatNum,
	}

	ret.PriceInfo = &proto.B2BPriceInfo{
		IsCarpoolSuccess: false,
		EstimateFee:      product.GetEstimateFee(),
		DiscountFee:      util.RoundAbs(product.GetPreTotalFee()-product.GetEstimateFee(), 2),
		PoolNum:          0,
	}

	// pbd呼返补贴
	if product.GetPBDCallReturn() != nil {
		ret.PriceInfo.PbdCallReturn = float64(product.GetPBDCallReturn().Amount) / 100
	}

	if product.GetCouponInfo() != nil {
		amount, _ := strconv.Atoi(product.GetCouponInfo().Amount)
		ret.PriceInfo.CouponInfo = &proto.CouponInfo{
			BatchId:    product.GetCouponInfo().BatchId,
			CouponId:   product.GetCouponInfo().CouponId,
			Amount:     int32(amount),
			CouponType: int32(2),
		}
	}

	return ret
}

func BuildB2BBiz(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.B2BBiz {
	if product == nil || product.Product == nil {
		return nil
	}

	ret := &proto.B2BBiz{}

	isShowTaxiFee := common_view_logic.CheckTaxiAppendShowEstimateFee(ctx, product)
	ret.IsShowTaxiFee = &isShowTaxiFee

	if waitReward := product.GetSpsLikeWaitReward(); waitReward != nil {
		ret.WaitReward = &proto.SpsLikeWaitReward{
			waitReward.Amount,
			waitReward.WaitTime,
		}
	}

	if product.GetPrivateBizInfo() != nil && product.GetPrivateBizInfo().PinchecheEtsInfo != nil {
		ets := product.GetPrivateBizInfo().PinchecheEtsInfo
		ret.Ets = util.Int32Ptr(int32(ets.EndTime))
	}

	if product.GetPrivateBizInfo() != nil && product.GetPrivateBizInfo().TimeSpan != nil {
		res := []*proto.B2BTimeSpan{}
		if bytes, err := json.Marshal(product.GetPrivateBizInfo().TimeSpan); err == nil {
			if err = json.Unmarshal(bytes, &res); err == nil {
				ret.CarpoolBookingTimeSpan = res
			}
		}
	}

	return ret
}

func getExtendCarpoolInfo(product *biz_runtime.ProductInfoFull) []*proto.B2BCarpoolFee {
	if product == nil {
		return nil
	}
	preTotalFee := product.GetPreTotalFee()
	sceneMarkList := getSceneMark(product)
	if sceneMarkList == nil {
		return nil
	}

	carpoolInfos := []*CarpoolInfo{}
	for _, v := range sceneMarkList {
		sceneData := product.GetSceneDataBySceneMarkNoPay(v)
		if sceneData == nil {
			continue
		}

		carpoolInfos = append(carpoolInfos, &CarpoolInfo{
			coupon:           sceneData.GetCouponInfo(),
			fee:              sceneData.GetFee(),
			SeatNum:          v["seat_num"],
			PoolNum:          v["pool_num"],
			IsCarpoolSuccess: v["is_carpool_success"],
			PbdCallReturn:    sceneData.GetPbdCallReturn(),
		})
	}
	fees := []*proto.B2BCarpoolFee{}
	for _, v := range carpoolInfos {
		if v.SeatNum == "" {
			continue
		}
		seatNum, _ := strconv.Atoi(v.SeatNum)
		if seatNum == 0 {
			continue
		}
		if v.PoolNum == "" {
			continue
		}
		poolNum, _ := strconv.Atoi(v.PoolNum)
		if v.IsCarpoolSuccess == "" {
			continue
		}
		isCarpoolSuccess, _ := strconv.Atoi(v.IsCarpoolSuccess)
		isCarpoolSuccessBool := false
		if isCarpoolSuccess == 1 {
			isCarpoolSuccessBool = true
		}
		discountFee := float64(0)
		if isCarpoolSuccessBool || carpool.IsInterCityCarpool(product.GetCarpoolType(), product.GetComboType()) {
			discountFee = util.RoundAbs(preTotalFee-v.fee, 2)
		}
		carpoolFee := &proto.B2BCarpoolFee{
			SeatNum: int32(seatNum),
			PriceInfo: &proto.B2BPriceInfo{
				IsCarpoolSuccess: isCarpoolSuccessBool,
				EstimateFee:      v.fee,
				DiscountFee:      discountFee,
				PoolNum:          int32(poolNum),
			},
		}

		if v.PbdCallReturn != nil {
			carpoolFee.PriceInfo.PbdCallReturn = float64(v.PbdCallReturn.Amount) / 100
		}

		if v.coupon != nil {
			amount, _ := strconv.Atoi(v.coupon.Amount)
			carpoolFee.PriceInfo.CouponInfo = &proto.CouponInfo{
				BatchId:    v.coupon.BatchId,
				CouponId:   v.coupon.CouponId,
				Amount:     int32(amount),
				CouponType: int32(2),
			}
		}
		fees = append(fees, carpoolFee)
	}
	return fees
}

// 两口价v3 取三个; 拼成乐v2 取5个  xyq 这里需要确认所有拼车类型！！！
func getSceneMark(product *biz_runtime.ProductInfoFull) []map[string]string {
	if carpool.IsCarpoolDualPriceV3(int32(product.GetCarpoolType()), product.GetCarpoolPriceType(), product.GetIsDualCarpoolPrice()) {
		return []map[string]string{
			// 一座未拼成
			map[string]string{
				"pool_num":           "0",
				"seat_num":           "1",
				"is_carpool_success": "0",
			},
			// 两座拼成
			map[string]string{
				"pool_num":           "0",
				"seat_num":           "2",
				"is_carpool_success": "1",
			},
			// 两座未拼成
			map[string]string{
				"pool_num":           "0",
				"seat_num":           "2",
				"is_carpool_success": "0",
			},
		}
	} else if carpool.IsPinCheCheV2(product.GetProductCategory(), product.GetCarpoolPriceType()) {
		// 外层是1人拼2人
		return []map[string]string{
			{
				"pool_num":           "1",
				"seat_num":           "1",
				"is_carpool_success": "1",
			},
			{
				"pool_num":           "0",
				"seat_num":           "1",
				"is_carpool_success": "0",
			},
			{
				"pool_num":           "1",
				"seat_num":           "2",
				"is_carpool_success": "1",
			},
			{
				"pool_num":           "0",
				"seat_num":           "2",
				"is_carpool_success": "0",
			},
		}
	} else if carpool.IsInterCityCarpool(product.GetCarpoolType(), product.GetComboType()) {
		return []map[string]string{
			{
				"pool_num":           "0",
				"seat_num":           "1",
				"is_carpool_success": "0",
			},
			{
				"pool_num":           "0",
				"seat_num":           "2",
				"is_carpool_success": "0",
			},
			{
				"pool_num":           "0",
				"seat_num":           "3",
				"is_carpool_success": "0",
			},
		}
	} else {
		return nil
	}
}
