package estimate_v3

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/bargain"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	carpool2 "git.xiaojukeji.com/s3e/pts/carpool"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

// 测试getCapPriceFeeMsgReplace函数
func Test_getCapPriceFeeMsgReplace(t *testing.T) {
	// 初始化测试上下文
	ctx := context.Background()
	prov := &MockProvider{}

	t.Run("未命中Apollo时返回原始文案", func(t *testing.T) {
		// Mock GetApolloParams
		mockGetApolloParams := mockey.Mock((*MockProvider).GetApolloParams).Return("test_key", map[string]string{}).Build()
		defer mockGetApolloParams.UnPatch()

		// Mock apollo.GetParameters，未命中配置
		mockGetParameters := mockey.Mock(apollo.GetParameters).Return(false, nil).Build()
		defer mockGetParameters.UnPatch()

		// 调用测试函数
		originalMsg := "原始价格文案"
		result := getCapPriceFeeMsgReplace(ctx, prov, originalMsg)

		// 断言结果
		assert.Equal(t, originalMsg, result, "未命中Apollo时应该返回原始文案")
	})

	t.Run("命中Apollo但不包含替换源时返回原始文案", func(t *testing.T) {
		// Mock GetApolloParams
		mockGetApolloParams := mockey.Mock((*MockProvider).GetApolloParams).Return("test_key", map[string]string{}).Build()
		defer mockGetApolloParams.UnPatch()

		// Mock apollo.GetParameters，命中配置但没有替换源
		mockParams := map[string]string{
			"other_key": "other_value",
		}
		mockGetParameters := mockey.Mock(apollo.GetParameters).Return(true, mockParams).Build()
		defer mockGetParameters.UnPatch()

		// 调用测试函数
		originalMsg := "原始价格文案"
		result := getCapPriceFeeMsgReplace(ctx, prov, originalMsg)

		// 断言结果
		assert.Equal(t, originalMsg, result, "配置中不包含替换源时应该返回原始文案")
	})

	t.Run("命中Apollo且原始文案包含替换源时返回替换后文案", func(t *testing.T) {
		// Mock GetApolloParams
		mockGetApolloParams := mockey.Mock((*MockProvider).GetApolloParams).Return("test_key", map[string]string{}).Build()
		defer mockGetApolloParams.UnPatch()

		// Mock apollo.GetParameters，命中配置且有替换源和目标
		mockParams := map[string]string{
			"replace_src": "预计",
			"replace_dst": "约",
		}
		mockGetParameters := mockey.Mock(apollo.GetParameters).Return(true, mockParams).Build()
		defer mockGetParameters.UnPatch()

		// 调用测试函数
		originalMsg := "预计需要支付¥XX"
		result := getCapPriceFeeMsgReplace(ctx, prov, originalMsg)

		// 断言结果
		assert.Equal(t, "约需要支付¥XX", result, "应该用replace_dst替换replace_src")
	})

	t.Run("命中Apollo但原始文案不包含替换源时返回原始文案", func(t *testing.T) {
		// Mock GetApolloParams
		mockGetApolloParams := mockey.Mock((*MockProvider).GetApolloParams).Return("test_key", map[string]string{}).Build()
		defer mockGetApolloParams.UnPatch()

		// Mock apollo.GetParameters，命中配置且有替换源和目标
		mockParams := map[string]string{
			"replace_src": "预计",
			"replace_dst": "约",
		}
		mockGetParameters := mockey.Mock(apollo.GetParameters).Return(true, mockParams).Build()
		defer mockGetParameters.UnPatch()

		// 调用测试函数
		originalMsg := "总价不超过¥XX"
		result := getCapPriceFeeMsgReplace(ctx, prov, originalMsg)

		// 断言结果
		assert.Equal(t, originalMsg, result, "原始文案不包含替换源时应该返回原始文案")
	})
}

// 测试GetEstimateFeeInfo函数
func Test_GetEstimateFeeInfo(t *testing.T) {
	// 初始化测试上下文
	ctx := context.Background()
	prov := &MockProvider{}

	t.Run("验证价格文案替换逻辑", func(t *testing.T) {
		// Mock产品ID
		mockProductId := mockey.Mock((*MockProvider).GetProductId).Return(int64(1)).Build()
		defer mockProductId.UnPatch()

		// Mock IsHongKongThird
		mockIsHongKongThird := mockey.Mock(product.IsHongKongThird).Return(false).Build()
		defer mockIsHongKongThird.UnPatch()

		// Mock getSpecialFeeMsgTemplate，返回测试模板
		mockSpecialTemplate := mockey.Mock(getSpecialFeeMsgTemplate).Return("预计需要支付¥XX").Build()
		defer mockSpecialTemplate.UnPatch()

		// Mock getFeeAmount，返回测试金额
		mockFeeAmount := mockey.Mock(getFeeAmount).Return("100").Build()
		defer mockFeeAmount.UnPatch()

		// Mock IsBusinessPay
		mockIsBusinessPay := mockey.Mock((*MockProvider).IsBusinessPay).Return(false).Build()
		defer mockIsBusinessPay.UnPatch()

		// Mock util.ToFloat64
		mockToFloat64 := mockey.Mock(util.ToFloat64).Return(100.0).Build()
		defer mockToFloat64.UnPatch()

		// Mock price.CheckSingle
		mockCheckSingle := mockey.Mock(price.CheckSingle).Return().Build()
		defer mockCheckSingle.UnPatch()

		// Mock util.RemoveSuffixZero
		mockRemoveSuffixZero := mockey.Mock(util.RemoveSuffixZero).Return("100").Build()
		defer mockRemoveSuffixZero.UnPatch()

		// Mock getCapPriceFeeMsgReplace，模拟文案替换
		mockCapPriceReplace := mockey.Mock(getCapPriceFeeMsgReplace).Return("约需要支付¥XX").Build()
		defer mockCapPriceReplace.UnPatch()

		// 调用测试函数
		result := GetEstimateFeeInfo(ctx, prov)

		// 断言结果
		assert.NotNil(t, result, "返回结果不应为nil")
		assert.Equal(t, "约需要支付¥XX", result.FeeMsgTemplate, "应该返回替换后的文案模板")
		assert.Equal(t, "100", result.FeeAmount, "应该返回正确的金额")
	})
}

// 测试getDefaultFeeMsgTemplate函数
func Test_getDefaultFeeMsgTemplate(t *testing.T) {
	ctx := context.Background()

	t.Run("MixedDeductPrice大于0的情况", func(t *testing.T) {
		// 准备带有MixedDeductPrice的Provider
		prov := &MockProvider{}
		mockMixedDeductPrice := mockey.Mock((*MockProvider).GetMixedDeductPrice).Return(10.0).Build()
		defer mockMixedDeductPrice.UnPatch()

		// Mock dcmp.GetJSONContentWithPath
		expectedTemplate := "混合支付模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getDefaultFeeMsgTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "MixedDeductPrice大于0时应返回对应模板")
	})

	t.Run("CapPrice大于0的情况", func(t *testing.T) {
		// 准备带有CapPrice的Provider
		prov := &MockProvider{}
		mockMixedDeductPrice := mockey.Mock((*MockProvider).GetMixedDeductPrice).Return(0.0).Build()
		mockCapPrice := mockey.Mock((*MockProvider).GetCapPrice).Return(20.0).Build()
		defer mockMixedDeductPrice.UnPatch()
		defer mockCapPrice.UnPatch()

		// Mock dcmp.GetJSONContentWithPath
		expectedTemplate := "封顶价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getDefaultFeeMsgTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "CapPrice大于0时应返回对应模板")
	})

	t.Run("默认情况", func(t *testing.T) {
		// 准备默认Provider
		prov := &MockProvider{}
		mockMixedDeductPrice := mockey.Mock((*MockProvider).GetMixedDeductPrice).Return(0.0).Build()
		mockCapPrice := mockey.Mock((*MockProvider).GetCapPrice).Return(0.0).Build()
		defer mockMixedDeductPrice.UnPatch()
		defer mockCapPrice.UnPatch()

		// Mock dcmp.GetJSONContentWithPath
		expectedTemplate := "常规模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getDefaultFeeMsgTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "默认情况应返回对应模板")
	})
}

// 测试多个场景下的GetEstimateFeeInfo函数
func Test_GetEstimateFeeInfoMultiScenarios(t *testing.T) {
	ctx := context.Background()

	t.Run("香港出租车场景", func(t *testing.T) {
		// 准备香港出租车Provider
		prov := &MockProvider{}
		mockProductId := mockey.Mock((*MockProvider).GetProductId).Return(product_id.ProductIdHLTaxiCar).Build()
		mockIsHongKongThird := mockey.Mock(product.IsHongKongThird).Return(false).Build()
		defer mockProductId.UnPatch()
		defer mockIsHongKongThird.UnPatch()

		// Mock getHKFeeInfo
		mockGetHKFeeInfo := mockey.Mock(getHKFeeInfo).Return("香港出租车模板", "100HKD").Build()
		defer mockGetHKFeeInfo.UnPatch()

		// Mock util.RemoveSuffixZero
		mockRemoveSuffixZero := mockey.Mock(util.RemoveSuffixZero).Return("100HKD").Build()
		defer mockRemoveSuffixZero.UnPatch()

		// Mock getCapPriceFeeMsgReplace
		mockCapPriceReplace := mockey.Mock(getCapPriceFeeMsgReplace).Return("香港出租车模板").Build()
		defer mockCapPriceReplace.UnPatch()

		// 调用测试函数
		result := GetEstimateFeeInfo(ctx, prov)

		// 断言结果
		assert.NotNil(t, result, "返回结果不应为nil")
		assert.Equal(t, "香港出租车模板", result.FeeMsgTemplate, "应该返回香港出租车模板")
		assert.Equal(t, "100HKD", result.FeeAmount, "应该返回正确的金额")
	})

	// 移除原先的测试用例，避免重复
	// 其他测试用例...
}

// 删除旧的测试函数
// func Test_FeeInfo(t *testing.T) {}

type MockProvider struct{}

func (MockProvider) GetCityID() int {
	return 0
}

func (MockProvider) GetAccessKeyId() int32 {
	return 0
}

func (MockProvider) GetLang() string {
	return ""
}

func (MockProvider) GetUserPID() int64 {
	return 0
}

func (MockProvider) GetAppVersion() string {
	return ""
}

func (MockProvider) GetBillFeeDetailInfo() map[string]float64 {
	return nil
}

func (MockProvider) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return nil
}

func (MockProvider) GetBillDriverMetre() int64 {
	return 0
}

func (MockProvider) GetBillInfoCurrency() string {
	return ""
}

func (MockProvider) GetEstimateFee() float64 {
	return 0
}

func (MockProvider) GetCarpoolFailEstimateFee() (float64, bool) {
	return 0, false
}

func (MockProvider) GetCapPrice() float64 {
	return 0
}

func (MockProvider) GetMemberDynamicProtectFee() float64 {
	return 0
}

func (MockProvider) GetDisplayLines() map[string]*PriceApi.DisplayLine {
	return nil
}

func (MockProvider) GetPersonalEstimateFee() float64 {
	return 0
}

func (MockProvider) GetDynamicTotalFee() float64 {
	return 0
}

func (MockProvider) GetTotalFeeWithoutDiscount() float64 {
	return 0
}

func (MockProvider) GetProductId() int64 {
	return 0
}

func (MockProvider) GetProductCategory() int64 {
	return 8200
}

func (MockProvider) GetBusinessID() int64 {
	return 0
}

func (MockProvider) GetCarpoolType() int64 {
	return 0
}

func (MockProvider) GetComboType() int64 {
	return 0
}

func (MockProvider) GetRequireLevel() string {
	return ""
}

func (MockProvider) GetCarpoolPriceType() int32 {
	return 0
}

func (MockProvider) GetSubGroupId() int32 {
	return 0
}

func (MockProvider) IsDualCarpoolPrice() bool {
	return false
}

func (MockProvider) IsSpecialPrice() bool {
	return false
}

func (MockProvider) GetOrderType() int16 {
	return 0
}

func (MockProvider) GetCountPriceType() int32 {
	return 0
}

func (MockProvider) GetLevelType() int32 {
	return 0
}

func (MockProvider) IsCarpoolV3Merge(ctx context.Context) bool {
	return false
}

func (MockProvider) GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo {
	return nil
}

func (MockProvider) GetMixedDeductPrice() float64 {
	return 0
}

func (MockProvider) GetVCard() *PriceApi.EstimateNewFormVCardInfo {
	return nil
}

func (MockProvider) IsBusinessPay() bool {
	return false
}

func (MockProvider) GetPaymentType() int32 {
	return 0
}

func (MockProvider) GetExtendList() []*PriceApi.EstimateNewFormExtend {
	return nil
}

func (MockProvider) GetCarpoolVCard(ctx context.Context) (string, int32) {
	return "", 0
}

func (MockProvider) GetIsSpecialPrice() bool {
	return false
}

func (MockProvider) ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key string, value string)) (key string, params map[string]string) {
	return "", nil
}

func (MockProvider) GetApolloParams(f func(full *biz_runtime.ProductInfoFull) string, f2 ...func(full *biz_runtime.ProductInfoFull) (string, string)) (string, map[string]string) {
	return "", nil
}

func (MockProvider) GetBargainRecommendInfo() *bargain.RecommendPriceInfo {
	return nil
}

func (MockProvider) IsHaveCarpoolVCard(ctx context.Context) bool {
	return false
}

func (MockProvider) GetSceneEstimatePrice() []biz_runtime.SceneEstimateFeeViewer {
	return nil
}

func (MockProvider) GetExactSeatNum() int32 {
	return 0
}

func (MockProvider) GetComboID() int64 {
	return 0
}

func (MockProvider) IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool {
	return false
}

func (MockProvider) GetSceneDataBySceneMark(reqSceneMark map[string]string) *biz_runtime.SceneEstimateFee {
	return nil
}

func (MockProvider) GetPageType() int32 {
	return 0
}

func (MockProvider) GetBaseReqData() *models.BaseReqData {
	return nil
}

// 测试getCarpoolFeeTemplate函数
func Test_getCarpoolFeeTemplate(t *testing.T) {
	ctx := context.Background()

	t.Run("低价拼车场景", func(t *testing.T) {
		// 准备拼车Provider
		prov := &MockProvider{}
		mockRequireLevel := mockey.Mock((*MockProvider).GetRequireLevel).Return("1").Build()
		mockComboType := mockey.Mock((*MockProvider).GetComboType).Return(int64(1)).Build()
		mockProductId := mockey.Mock((*MockProvider).GetProductId).Return(int64(1)).Build()
		mockCarpoolType := mockey.Mock((*MockProvider).GetCarpoolType).Return(int64(1)).Build()
		mockCarpoolPriceType := mockey.Mock((*MockProvider).GetCarpoolPriceType).Return(int32(1)).Build()
		defer mockRequireLevel.UnPatch()
		defer mockComboType.UnPatch()
		defer mockProductId.UnPatch()
		defer mockCarpoolType.UnPatch()
		defer mockCarpoolPriceType.UnPatch()

		// Mock IsLowPriceCarpoolV2
		mockIsLowPriceCarpool := mockey.Mock(carpool2.IsLowPriceCarpoolV2).Return(true).Build()
		defer mockIsLowPriceCarpool.UnPatch()

		// Mock GetJSONContentWithPath
		expectedTemplate := "拼车低价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getCarpoolFeeTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "低价拼车时应返回对应模板")
	})

	t.Run("城际拼车场景", func(t *testing.T) {
		// 准备城际拼车Provider
		prov := &MockProvider{}
		mockRequireLevel := mockey.Mock((*MockProvider).GetRequireLevel).Return("1").Build()
		mockComboType := mockey.Mock((*MockProvider).GetComboType).Return(int64(1)).Build()
		mockProductId := mockey.Mock((*MockProvider).GetProductId).Return(int64(1)).Build()
		mockCarpoolType := mockey.Mock((*MockProvider).GetCarpoolType).Return(int64(1)).Build()
		mockCarpoolPriceType := mockey.Mock((*MockProvider).GetCarpoolPriceType).Return(int32(1)).Build()
		defer mockRequireLevel.UnPatch()
		defer mockComboType.UnPatch()
		defer mockProductId.UnPatch()
		defer mockCarpoolType.UnPatch()
		defer mockCarpoolPriceType.UnPatch()

		// Mock IsLowPriceCarpoolV2
		mockIsLowPriceCarpool := mockey.Mock(carpool2.IsLowPriceCarpoolV2).Return(false).Build()
		defer mockIsLowPriceCarpool.UnPatch()

		// Mock IsInterCityCarpool
		mockIsInterCityCarpool := mockey.Mock(carpool.IsInterCityCarpool).Return(true).Build()
		defer mockIsInterCityCarpool.UnPatch()

		// Mock GetApolloParams
		mockGetApolloParams := mockey.Mock((*MockProvider).GetApolloParams).Return("test_key", map[string]string{}).Build()
		defer mockGetApolloParams.UnPatch()

		// Mock PreferentialStatus
		mockPreferentialStatus := mockey.Mock(PreferentialStatus).Return(false, 0).Build()
		defer mockPreferentialStatus.UnPatch()

		// Mock GetJSONContentWithPath
		expectedTemplate := "封顶价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getCarpoolFeeTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "城际拼车时应返回对应模板")
	})

	t.Run("小巴士拼车场景", func(t *testing.T) {
		// 准备小巴士拼车Provider
		prov := &MockProvider{}
		mockRequireLevel := mockey.Mock((*MockProvider).GetRequireLevel).Return("1").Build()
		mockComboType := mockey.Mock((*MockProvider).GetComboType).Return(int64(1)).Build()
		mockProductId := mockey.Mock((*MockProvider).GetProductId).Return(int64(1)).Build()
		mockCarpoolType := mockey.Mock((*MockProvider).GetCarpoolType).Return(int64(1)).Build()
		mockCarpoolPriceType := mockey.Mock((*MockProvider).GetCarpoolPriceType).Return(int32(1)).Build()
		defer mockRequireLevel.UnPatch()
		defer mockComboType.UnPatch()
		defer mockProductId.UnPatch()
		defer mockCarpoolType.UnPatch()
		defer mockCarpoolPriceType.UnPatch()

		// Mock IsLowPriceCarpoolV2
		mockIsLowPriceCarpool := mockey.Mock(carpool2.IsLowPriceCarpoolV2).Return(false).Build()
		defer mockIsLowPriceCarpool.UnPatch()

		// Mock IsInterCityCarpool
		mockIsInterCityCarpool := mockey.Mock(carpool.IsInterCityCarpool).Return(false).Build()
		defer mockIsInterCityCarpool.UnPatch()

		// Mock IsMiniBusCarpool
		mockIsMiniBusCarpool := mockey.Mock(carpool2.IsMiniBusCarpool).Return(true).Build()
		defer mockIsMiniBusCarpool.UnPatch()

		// Mock GetJSONContentWithPath
		expectedTemplate := "封顶价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getCarpoolFeeTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "小巴士拼车时应返回对应模板")
	})

	t.Run("拼车未成功场景", func(t *testing.T) {
		// 准备未成功拼车Provider
		prov := &MockProvider{}
		mockRequireLevel := mockey.Mock((*MockProvider).GetRequireLevel).Return("1").Build()
		mockComboType := mockey.Mock((*MockProvider).GetComboType).Return(int64(1)).Build()
		mockProductId := mockey.Mock((*MockProvider).GetProductId).Return(int64(1)).Build()
		mockCarpoolType := mockey.Mock((*MockProvider).GetCarpoolType).Return(int64(1)).Build()
		mockCarpoolPriceType := mockey.Mock((*MockProvider).GetCarpoolPriceType).Return(int32(1)).Build()
		defer mockRequireLevel.UnPatch()
		defer mockComboType.UnPatch()
		defer mockProductId.UnPatch()
		defer mockCarpoolType.UnPatch()
		defer mockCarpoolPriceType.UnPatch()

		// Mock IsLowPriceCarpoolV2
		mockIsLowPriceCarpool := mockey.Mock(carpool2.IsLowPriceCarpoolV2).Return(false).Build()
		defer mockIsLowPriceCarpool.UnPatch()

		// Mock IsInterCityCarpool
		mockIsInterCityCarpool := mockey.Mock(carpool.IsInterCityCarpool).Return(false).Build()
		defer mockIsInterCityCarpool.UnPatch()

		// Mock IsMiniBusCarpool
		mockIsMiniBusCarpool := mockey.Mock(carpool2.IsMiniBusCarpool).Return(false).Build()
		defer mockIsMiniBusCarpool.UnPatch()

		// Mock IsCarpoolUnSuccessFlatPrice
		mockIsCarpoolUnSuccessFlatPrice := mockey.Mock(carpool.IsCarpoolUnSuccessFlatPrice).Return(true).Build()
		defer mockIsCarpoolUnSuccessFlatPrice.UnPatch()

		// Mock IsCarpoolUnSuccessFlatPriceShowAsCapPrice
		mockIsCarpoolUnSuccessFlatPriceShowAsCapPrice := mockey.Mock((*MockProvider).IsCarpoolUnSuccessFlatPriceShowAsCapPrice).Return(true).Build()
		defer mockIsCarpoolUnSuccessFlatPriceShowAsCapPrice.UnPatch()

		// Mock IsHaveCarpoolVCard
		mockIsHaveCarpoolVCard := mockey.Mock((*MockProvider).IsHaveCarpoolVCard).Return(false).Build()
		defer mockIsHaveCarpoolVCard.UnPatch()

		// Mock GetJSONContentWithPath
		expectedTemplate := "封顶价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getCarpoolFeeTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "未成功拼车时应返回对应模板")
	})

	t.Run("智能巴士场景", func(t *testing.T) {
		// 准备智能巴士Provider
		prov := &MockProvider{}
		mockRequireLevel := mockey.Mock((*MockProvider).GetRequireLevel).Return("1").Build()
		mockComboType := mockey.Mock((*MockProvider).GetComboType).Return(int64(1)).Build()
		mockProductId := mockey.Mock((*MockProvider).GetProductId).Return(int64(1)).Build()
		mockCarpoolType := mockey.Mock((*MockProvider).GetCarpoolType).Return(int64(1)).Build()
		mockCarpoolPriceType := mockey.Mock((*MockProvider).GetCarpoolPriceType).Return(int32(1)).Build()
		defer mockRequireLevel.UnPatch()
		defer mockComboType.UnPatch()
		defer mockProductId.UnPatch()
		defer mockCarpoolType.UnPatch()
		defer mockCarpoolPriceType.UnPatch()

		// Mock IsLowPriceCarpoolV2
		mockIsLowPriceCarpool := mockey.Mock(carpool2.IsLowPriceCarpoolV2).Return(false).Build()
		defer mockIsLowPriceCarpool.UnPatch()

		// Mock IsInterCityCarpool
		mockIsInterCityCarpool := mockey.Mock(carpool.IsInterCityCarpool).Return(false).Build()
		defer mockIsInterCityCarpool.UnPatch()

		// Mock IsMiniBusCarpool
		mockIsMiniBusCarpool := mockey.Mock(carpool2.IsMiniBusCarpool).Return(false).Build()
		defer mockIsMiniBusCarpool.UnPatch()

		// Mock IsCarpoolUnSuccessFlatPrice
		mockIsCarpoolUnSuccessFlatPrice := mockey.Mock(carpool.IsCarpoolUnSuccessFlatPrice).Return(false).Build()
		defer mockIsCarpoolUnSuccessFlatPrice.UnPatch()

		// Mock IsSmartBus
		mockIsSmartBus := mockey.Mock(carpool.IsSmartBus).Return(true).Build()
		defer mockIsSmartBus.UnPatch()

		// Mock GetJSONContentWithPath
		expectedTemplate := "封顶价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getCarpoolFeeTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "智能巴士时应返回对应模板")
	})
}

// 测试getSpaciousFeeMsgTemplate函数
func Test_getSpaciousFeeMsgTemplate(t *testing.T) {
	ctx := context.Background()

	t.Run("车大封顶价场景", func(t *testing.T) {
		// 准备Provider
		prov := &MockProvider{}
		mockCapPrice := mockey.Mock((*MockProvider).GetCapPrice).Return(100.0).Build()
		defer mockCapPrice.UnPatch()

		// Mock GetJSONContentWithPath
		expectedTemplate := "封顶价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getSpaciousFeeMsgTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "车大封顶价场景应返回封顶价模板")
	})

	t.Run("车大非封顶价场景", func(t *testing.T) {
		// 准备Provider
		prov := &MockProvider{}
		mockCapPrice := mockey.Mock((*MockProvider).GetCapPrice).Return(0.0).Build()
		defer mockCapPrice.UnPatch()

		// Mock GetJSONContentWithPath
		expectedTemplate := "单价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getSpaciousFeeMsgTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "车大非封顶价场景应返回单价模板")
	})
}

// 测试getSpecialRateFeeMsgTemplate函数
func Test_getSpecialRateFeeMsgTemplate(t *testing.T) {
	ctx := context.Background()

	t.Run("特惠封顶价场景", func(t *testing.T) {
		// 准备Provider
		prov := &MockProvider{}
		mockCapPrice := mockey.Mock((*MockProvider).GetCapPrice).Return(100.0).Build()
		defer mockCapPrice.UnPatch()

		// Mock GetJSONContentWithPath
		expectedTemplate := "封顶价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getSpecialRateFeeMsgTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "特惠封顶价场景应返回封顶价模板")
	})
}

// 测试getNormalFeeMsgTemplate函数
func Test_getNormalFeeMsgTemplate(t *testing.T) {
	ctx := context.Background()

	t.Run("出租车拼车澜客宝场景", func(t *testing.T) {
		// 准备Provider
		prov := &MockProvider{}
		mockProductCategory := mockey.Mock((*MockProvider).GetProductCategory).Return(int64(estimate_pc_id.EstimatePcIdTaxiCarpool)).Build()
		mockPageType := mockey.Mock((*MockProvider).GetPageType).Return(int32(page_type.PageTypeLankeBao)).Build()
		defer mockProductCategory.UnPatch()
		defer mockPageType.UnPatch()

		// Mock GetJSONContentWithPath
		expectedTemplate := "封顶价模板"
		mockGetJSONContent := mockey.Mock(dcmp.GetJSONContentWithPath).Return(expectedTemplate).Build()
		defer mockGetJSONContent.UnPatch()

		// 调用测试函数
		result := getNormalFeeMsgTemplate(ctx, prov)

		// 断言结果
		assert.Equal(t, expectedTemplate, result, "出租车拼车澜客宝场景应返回封顶价模板")
	})
}

// 创建一个mock的PriceInfoProvider
type mockPriceInfoProvider struct {
	baseReqData *models.BaseReqData
	orderType   int16
	PriceDescProvider
	render.BargainInfoProvider
	render.CarpoolInfoProvider
}

func (m *mockPriceInfoProvider) GetPageType() int32 {
	return 0
}

func (m *mockPriceInfoProvider) GetBaseReqData() *models.BaseReqData {
	return m.baseReqData
}

func (m *mockPriceInfoProvider) GetOrderType() int16 {
	return m.orderType
}

func (m *mockPriceInfoProvider) IsCarpoolV3Merge(ctx context.Context) bool {
	return false
}

func (m *mockPriceInfoProvider) ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (string, map[string]string) {
	return "test_key", map[string]string{
		"county_id":  "123",
		"order_type": "1",
		"lang":       "zh-CN",
		"tab_id":     "test_tab",
	}
}

func TestUsingNewBox(t *testing.T) {
	// 测试前清理所有mock
	mockey.UnPatchAll()

	// 测试上下文
	ctx := context.Background()

	// 测试用例
	tests := []struct {
		name           string
		setupProvider  func() *mockPriceInfoProvider
		mockTaxi       func() []*mockey.Mocker
		expectedResult bool
	}{
		{
			name: "BaseReqData为nil时返回false",
			setupProvider: func() *mockPriceInfoProvider {
				return &mockPriceInfoProvider{
					baseReqData: nil,
					orderType:   order.OrderTypeNow,
				}
			},
			mockTaxi:       func() []*mockey.Mocker { return nil },
			expectedResult: false,
		},
		{
			name: "UID为0时返回false",
			setupProvider: func() *mockPriceInfoProvider {
				return &mockPriceInfoProvider{
					baseReqData: &models.BaseReqData{
						PassengerInfo: models.PassengerInfo{
							UID: 0,
						},
					},
					orderType: order.OrderTypeNow,
				}
			},
			mockTaxi:       func() []*mockey.Mocker { return nil },
			expectedResult: false,
		},
		{
			name: "OrderType不是OrderTypeNow时返回false",
			setupProvider: func() *mockPriceInfoProvider {
				return &mockPriceInfoProvider{
					baseReqData: &models.BaseReqData{
						PassengerInfo: models.PassengerInfo{
							UID: 12345,
						},
					},
					orderType: order.OrderTypeBooking,
				}
			},
			mockTaxi:       func() []*mockey.Mocker { return nil },
			expectedResult: false,
		},
		{
			name: "所有条件满足且taxi.UsingNewBox返回true时返回true",
			setupProvider: func() *mockPriceInfoProvider {
				return &mockPriceInfoProvider{
					baseReqData: &models.BaseReqData{
						PassengerInfo: models.PassengerInfo{
							UID: 12345,
						},
						AreaInfo: models.AreaInfo{
							FromCounty: 1001,
						},
						CommonInfo: models.CommonInfo{
							Lang:  "zh-CN",
							TabId: "test_tab",
						},
					},
					orderType: order.OrderTypeNow,
				}
			},
			mockTaxi: func() []*mockey.Mocker {
				mock := mockey.Mock(taxi.UsingNewBox).Return(true).Build()
				return []*mockey.Mocker{mock}
			},
			expectedResult: true,
		},
		{
			name: "所有条件满足但taxi.UsingNewBox返回false时返回false",
			setupProvider: func() *mockPriceInfoProvider {
				return &mockPriceInfoProvider{
					baseReqData: &models.BaseReqData{
						PassengerInfo: models.PassengerInfo{
							UID: 12345,
						},
						AreaInfo: models.AreaInfo{
							FromCounty: 1001,
						},
						CommonInfo: models.CommonInfo{
							Lang:  "zh-CN",
							TabId: "test_tab",
						},
					},
					orderType: order.OrderTypeNow,
				}
			},
			mockTaxi: func() []*mockey.Mocker {
				mock := mockey.Mock(taxi.UsingNewBox).Return(false).Build()
				return []*mockey.Mocker{mock}
			},
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备数据
			provider := tt.setupProvider()

			// 设置mock
			var taxiMockers []*mockey.Mocker
			if tt.mockTaxi != nil {
				taxiMockers = tt.mockTaxi()
				for _, m := range taxiMockers {
					defer m.UnPatch()
				}
			}

			// 执行测试
			result := usingNewBox(ctx, provider)

			// 验证结果
			assert.Equal(t, tt.expectedResult, result, "结果应该匹配预期")
		})
	}
}
