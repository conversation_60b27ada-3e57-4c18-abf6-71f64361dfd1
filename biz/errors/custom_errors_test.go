package errors

import (
	"errors"
	"github.com/stretchr/testify/require"
	"testing"
)

func TestNewStdBizErr(t *testing.T) {
	p := NewStdBizErr(errors.New("test error"), ErrnoSystemError)
	var c BizError
	require.Truef(t, errors.As(p, &c), "p is not a BizError")
	require.NotNilf(t, c, "c is nil")
	require.Equal(t, ErrnoSystemError, c<PERSON>())
	require.Equalf(t, "test error", c.<PERSON>(), "error message is not equal")
}
