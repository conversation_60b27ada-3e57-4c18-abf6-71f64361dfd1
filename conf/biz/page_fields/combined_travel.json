{"receiver_struct_name": "proto.CombinedTravelEstimateData", "index": {"page_type": 0}, "data": {"business_name": [{"render": "CarNameRender"}], "car_icon": [{"render": "CarIconRender"}], "price_desc": [{"comment": "一口价", "conditions": ["cap_price/neq/0"], "render": "CapPrice<PERSON>ee<PERSON>g<PERSON>ender"}, {"render": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "price_info_desc_list": [{"render": "CombinedTravelPriceDescRender"}], "pay_info": [{"render": "PayInfoRender"}], "fee_detail_icon": [{"render": "DcmpPlainText", "render_conf": {"dcmp_key": "combined_travel-fee_detail_icon"}}]}}