package common_view_logic

import (
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

// IsHitMerge 是否命中高热路线一口价（拼成==未拼成）
func IsHitMerge(full *biz_runtime.ProductInfoFull) bool {
	if !carpool.IsCarpoolDualPriceV3(int32(full.Product.CarpoolType), full.Product.CarpoolPriceType, full.Product.IsDualCarpoolPrice) {
		return false
	}

	if len(full.DiscountInfo) < 2 {
		return false
	}

	return full.DiscountInfo[0].EstimateFee == full.DiscountInfo[1].EstimateFee
}
