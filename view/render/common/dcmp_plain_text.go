package common

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type DcmpPlainText struct {
}

func (p *DcmpPlainText) GetName() string {
	return "DcmpPlainText"
}

func (p *DcmpPlainText) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) string {
	if key, ok := renderConf["dcmp_key"]; ok && key != "" {
		return dcmp.GetDcmpContent(ctx, key, nil)
	}

	return ""
}
