package fee

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type PriceRender struct {
}

func (p *PriceRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) float64 {
	if len(full.DiscountInfo) == 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "discountInfo invalid")
		return 0
	}
	return full.DiscountInfo[0].EstimateFee
}
