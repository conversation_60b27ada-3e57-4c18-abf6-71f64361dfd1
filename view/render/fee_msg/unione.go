package fee_msg

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/view/common_view_logic"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type UniOneFeeMsgRender struct {
}

func (r *UniOneFeeMsgRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) string {
	var (
		priceDesc string
	)
	priceDesc = dcmp.GetDcmpContent(ctx, "unione-price_desc_without_price", nil)
	if common_view_logic.CheckTaxiAppendShowEstimateFee(ctx, full) {
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.BillDetail.Currency)
		tag := map[string]string{
			"currency_symbol": symbol,
			"total_fee":       util.Float64ToString(full.DiscountInfo[0].EstimateFee),
			"currency_unit":   unit,
		}
		priceDesc = dcmp.GetDcmpContent(ctx, "common-price_desc", tag)
	}

	return priceDesc
}
