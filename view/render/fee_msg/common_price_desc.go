package fee_msg

import (
	"context"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type CommonFeeMsgRender struct {
}

func (a *CommonFeeMsgRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) string {
	currencyUnit, currencySymbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.BillDetail.Currency)
	tag := map[string]string{
		"num":             util.Float64ToString(full.DiscountInfo[0].EstimateFee),
		"total_fee":       util.Float64ToString(full.DiscountInfo[0].EstimateFee),
		"currency_unit":   currencyUnit,
		"currency_symbol": currencySymbol,
	}

	generalTmp := util2.GetGeneralFeeMsgTemplate(ctx, full.GetCityID(), int(full.GetProductCategory()), full.GetLang())

	if generalTmp != "" {
		return dcmp.TranslateTemplate(generalTmp, tag)
	}

	priceDesc := dcmp.GetDcmpContent(ctx, "common-price_desc", tag)
	return priceDesc
}
