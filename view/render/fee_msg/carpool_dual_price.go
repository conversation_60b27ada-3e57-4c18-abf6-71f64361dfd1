package fee_msg

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/view/common_view_logic"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type dcmpConf struct {
	SuccCarpoolDay string `json:"succ_carpool_day"`
	Succ           string `json:"succ"`
	CapPriceMerge  string `json:"cap_price_merge"`
}

type CarpoolDualPriceFeeMsgRender struct {
}

func (r *CarpoolDualPriceFeeMsgRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) string {

	var (
		dcmpConf *dcmpConf
		feeMsg   string
	)

	dcmpConf, err := loadDcmpConf(ctx)
	if err != nil || dcmpConf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshal dcmp string fail with %v", err)
		return ""
	}

	if common_view_logic.IsHitMerge(full) {
		feeMsg = util.ReplaceTag(ctx, dcmpConf.CapPriceMerge, map[string]string{"carpool_success_price": util.Float64ToString(full.DiscountInfo[0].EstimateFee)})
	} else {
		feeMsg = util.ReplaceTag(ctx, dcmpConf.Succ, map[string]string{"carpool_success_price": util.Float64ToString(full.DiscountInfo[0].EstimateFee)})
	}

	return feeMsg
}

func loadDcmpConf(ctx context.Context) (conf *dcmpConf, err error) {
	conf = new(dcmpConf)
	dcmpStr := dcmp.GetDcmpContent(ctx, "carpool_config-dual_price", nil)
	err = json.Unmarshal([]byte(dcmpStr), conf)
	return
}
