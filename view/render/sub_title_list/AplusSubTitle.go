package sub_title_list

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type APlusSubTitleList struct {
}

func (r *APlusSubTitleList) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) interface{} {
	var subTitle *proto.SubTitle

	if full.Product.BizInfo == nil && len(full.Product.BizInfo.CompensationInfo) == 0 {
		return nil
	}

	if decision, ok := full.Product.BizInfo.CompensationInfo[compensation.BusinessAPlusInsurance]; ok && decision != nil && decision.Decision == 1 {
		dcmpConf := dcmp.GetDcmpContent(ctx, "config_text-intro_tags_nocar_aplus", nil)
		if dcmpConf != "" {
			subTitle = &proto.SubTitle{}
			err := json.Unmarshal([]byte(dcmpConf), subTitle)
			if err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal fail with intro_tags_nocar_aplus with err %v", err)
			} else {
				return []*proto.SubTitle{subTitle}
			}
		}
	}

	return nil
}
