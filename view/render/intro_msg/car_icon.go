package intro_msg

import (
	"context"
	"encoding/json"
	"strconv"

	mambaConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type CarIconRender struct {
}

type ProductIconConfig struct {
	Icon string `json:"icon"`
}

func (r *CarIconRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) string {
	var carName string
	if full == nil || full.Product == nil {
		return carName
	}

	return getBasicCarIcon(ctx, full.Product.ProductCategory)
}

func getBasicCarIcon(ctx context.Context, productCategory int64) string {
	var carIcon = "https://pt-starimg.didistatic.com/static/starimg/img/XtE5lO2oQq1641537142732.png"
	allProductConfig := map[string]ProductIconConfig{}
	configStr := dcmp.GetDcmpPlainContent(ctx, "combined_travel-car_icon")

	if configStr != "" {
		err := json.Unmarshal([]byte(configStr), &allProductConfig)
		if err != nil {
			log.Trace.Warnf(ctx, mambaConsts.TagErrJsonUnMarshal, "json %s fail with err %v", configStr, err)
			return carIcon
		}

		if productConfig, ok := allProductConfig[strconv.FormatInt(productCategory, 10)]; ok && "" != productConfig.Icon {
			carIcon = productConfig.Icon
		}
	}

	return carIcon
}
