package selector

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	"git.xiaojukeji.com/gulfstream/mamba/view/render"
)

var AllPageFieldConfig = make(map[string]map[string]NodeConfList)

type Config struct {
	ReceiverStructName string `json:"receiver_struct_name"`
	Index              struct {
		PageType int `json:"page_type"`
	} `json:"index"`
	Data map[string][]struct {
		Conditions []string          `json:"conditions"`
		Render     string            `json:"render"`
		RenderConf map[string]string `json:"render_conf"`
	} `json:"data"`
}

type BizNodeConfig struct {
	Conditions []string          `json:"conditions"`
	Render     string            `json:"render"`
	RenderConf map[string]string `json:"render_conf"`
}

func InitPageFieldsRouter() error {
	var conditions Conditions

	configList, err := getAllConfigs()
	if err != nil {
		return err
	}

	for _, config := range configList {
		if config.ReceiverStructName == "" {
			return errors.New("error config with no name")
		}

		dataNodeMap := make(map[string]NodeConfList)
		for key, nodeConfList := range config.Data {
			nodeList := make(NodeConfList, 0)
			for _, conf := range nodeConfList {
				conditions, err = ParseConditions(conf.Conditions)
				if err != nil {
					return errors.New(fmt.Sprintf("invalid conditions with %v ", conditions))
				}

				renderUnion, exist := render.LocalRenderMap[conf.Render]
				if !exist {
					return errors.New(fmt.Sprintf("invalid render name with %s ", conf.Render))
				}
				node := &NodeConf{
					conditions: conditions,
					render:     renderUnion,
					renderConf: conf.RenderConf,
					nodeName:   GetNodeName(conf.Render, conf.RenderConf),
				}
				nodeList = append(nodeList, node)
			}
			dataNodeMap[key] = nodeList
		}

		AllPageFieldConfig[config.ReceiverStructName] = dataNodeMap
	}
	_, _ = fmt.Fprintf(os.Stdout, "AllFieldConfig: %s ;=> %s ; ", util.JustJsonEncode(AllPageFieldConfig), util.JustJsonEncode(configList))
	return err
}

func getAllConfigs() ([]Config, error) {
	var (
		fileContent []byte
		configList  []Config
	)

	//_, fn, _, _ := runtime.Caller(0)
	owd, err := os.Getwd()
	if err != nil {
		return nil, err
	}
	confDir := filepath.Join(owd, "conf/biz/page_fields")
	fileList, err := ioutil.ReadDir(confDir)
	fmt.Printf("biz_conf_file %v", fileList)
	for _, file := range fileList {
		fileContent, err = ioutil.ReadFile(filepath.Join(confDir, file.Name()))
		if err != nil {
			return configList, err
		}
		config := &Config{}
		err = json.Unmarshal(fileContent, config)
		if err != nil {
			return configList, err
		}
		configList = append(configList, *config)
	}

	return configList, err
}
