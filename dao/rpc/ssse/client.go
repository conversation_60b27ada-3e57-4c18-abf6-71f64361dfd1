package ssse

import (
	"context"

	ssse "git.xiaojukeji.com/dirpc/dirpc-go-http-Ssse"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ssseClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

const disfName = "disf!biz-gs-ssse"

var (
	client *ssseClient.Client
)

// InitClient 初始化赔付client
func InitClient() (err error) {
	client, err = ssseClient.NewSsseClient(disfName)
	if err != nil {
		return err
	}
	return nil
}

func GetService(ctx context.Context, req *ssse.GetMultiServiceReq) [][]*ssse.CustomService {
	// 接口文档: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=798371683
	res, err := client.GetService(ctx, req)

	if err != nil {
		// log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "GetService error=[%v]", err)
		return nil
	}

	if res == nil || res.Errno != 0 || len(res.Data) == 0 {
		log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "GetService res exception:[%s]", util.ToJSONString(res))
		return nil
	}

	return res.Data
}
