package carpool_api

import (
	"fmt"
	"strconv"

	"github.com/spf13/cast"

	CarpoolApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi"
)

// Product 产品信息
type Product struct {
	ProductID    int64
	ComboType    int64
	RequireLevel string

	NTuple NTuple
}

// NTuple N元组
type NTuple struct {
	EstimateID         string
	CarpoolPriceType   int
	CarpoolType        int
	RouteType          int
	OrderType          int
	MenuID             string
	InvitationType     int
	IsDualCarpoolPrice bool
	DepartureRange     []int64
	LevelType          int32
	RouteGroup         int64
}

func (rawProduct *Product) toProductType() *CarpoolApi.ProductType {
	prod := CarpoolApi.NewProductType()

	const NumberBase = 10
	prodID := strconv.FormatInt(rawProduct.ProductID, NumberBase)
	prod.ProductID = &prodID

	prod.ComboType = new(int16)
	*prod.ComboType = int16(rawProduct.ComboType)

	prod.RequireLevel = new(string)
	*prod.RequireLevel = rawProduct.RequireLevel

	const NTupleFieldCount = 11
	nTupleMap := make(map[string]string, NTupleFieldCount)
	nTupleMap["product_id"] = prodID
	nTupleMap["require_level"] = rawProduct.RequireLevel
	nTupleMap["combo_type"] = strconv.Itoa(int(rawProduct.ComboType))

	nTupleMap["carpool_price_type"] = strconv.Itoa(rawProduct.NTuple.CarpoolPriceType)
	nTupleMap["carpool_type"] = strconv.Itoa(rawProduct.NTuple.CarpoolType)
	nTupleMap["route_type"] = strconv.Itoa(rawProduct.NTuple.RouteType)
	nTupleMap["order_type"] = strconv.Itoa(rawProduct.NTuple.OrderType)
	nTupleMap["menu_id"] = rawProduct.NTuple.MenuID
	nTupleMap["invitation_type"] = strconv.Itoa(rawProduct.NTuple.InvitationType)
	nTupleMap["estimate_id"] = rawProduct.NTuple.EstimateID
	nTupleMap["level_type"] = cast.ToString(rawProduct.NTuple.LevelType)

	nTupleMap["is_dual_carpool_price"] = ""
	if rawProduct.NTuple.IsDualCarpoolPrice {
		nTupleMap["is_dual_carpool_price"] = "1"
	}

	const RangeAtLeastLength = 2
	if rng := rawProduct.NTuple.DepartureRange; len(rng) >= RangeAtLeastLength {
		nTupleMap["departure_range"] = fmt.Sprintf("[%d,%d]", rng[0], rng[1])
	}

	// 城际拼车品类需要给duse传路线id
	if rawProduct.NTuple.RouteGroup != 0 {
		nTupleMap["route_group"] = cast.ToString(rawProduct.NTuple.RouteGroup)
	}

	prod.OrderNTuple = nTupleMap

	return prod
}
