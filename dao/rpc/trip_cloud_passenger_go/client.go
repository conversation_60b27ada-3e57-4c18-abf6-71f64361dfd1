package trip_cloud_passenger_go

import (
	"context"
	"errors"
	TripCloudPassengerGo "git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassengerGo"
)

var client *TripCloudPassengerGo.Client

const (
	disfName = "disf!biz-gs-tripcloud_passenger_go"
)

func InitClient() (err error) {
	client, err = TripCloudPassengerGo.NewClient(disfName)
	if err != nil {
		return err
	}

	return nil
}

func GetPEstimateExtraInfo(ctx context.Context, req *TripCloudPassengerGo.PEstimateExtraInfoReq) (*TripCloudPassengerGo.PEstimateExtraInfoResp, error) {
	if client == nil {
		return nil, errors.New("trip cloud passenger go cli is nil")
	}

	resp, err := client.PEstimateExtraInfo(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Errno != 0 || resp.Data == nil {
		return nil, errors.New("PEstimateExtraInfo err")
	}

	return resp, err
}
