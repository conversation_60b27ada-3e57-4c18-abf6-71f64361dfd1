package route_broker

import (
	"context"
	"encoding/json"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	"git.xiaojukeji.com/dirpc/dirpc-go-thrift-itsThrift/its/service/base"
	"git.xiaojukeji.com/dirpc/dirpc-go-thrift-itsThrift/its/service/route_plan_base"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	legoTrace "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/dirpc/dirpc-go-thrift-itsThrift/its/service/newbroker"
	dirpcClient "git.xiaojukeji.com/dirpc/dirpc-go-thrift-itsThrift/its/service/route_broker_service"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

const (
	disfName = "disf!map-traffic-online-route_broker"
	logTag   = "route_broker_thrift"
	Ratio    = 100000
	CallerId = "pre-sale-core-smart-bus"
)

type Point struct {
	Lat        float64
	Lng        float64
	EstimateId string
}

var (
	client *dirpcClient.RouteBrokerServiceDirpcClient
)

func InitClient() (err error) {
	client, err = dirpcClient.NewRouteBrokerServiceDirpcClient(disfName)
	if err != nil {
		return nil
	}
	return nil
}

func WalkRoutePlan(ctx context.Context, fromLat float64, fromLng float64, toLat float64, toLng float64) (distance float64, duration int32, err error) {
	// 接口文档http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=740541534
	var (
		endGeos    []*base.PointT
		req        *newbroker.BatchRbPack
		rbPack     *newbroker.RbPack
		rbPackList []*newbroker.RbPack
		walkReq    *route_plan_base.Point2MultPointWalkRouteRequest
		resp       *newbroker.BatchRbPack
		walkResp   *route_plan_base.Point2MultPointWalkRouteResponse
	)

	startGeo := &base.PointT{
		X: base.CoordT(util.ToInt32(fromLng * Ratio)),
		Y: base.CoordT(util.ToInt32(fromLat * Ratio)),
	}
	endGeo := &base.PointT{
		X: base.CoordT(util.ToInt32(toLng * Ratio)),
		Y: base.CoordT(util.ToInt32(toLat * Ratio)),
	}
	endGeos = append(endGeos, endGeo)

	walkReq = &route_plan_base.Point2MultPointWalkRouteRequest{
		StartGeo: startGeo,
		EndGeos:  endGeos,
	}

	rbPack = &newbroker.RbPack{
		WalkRoutePoint_2MultPointReq: walkReq,
	}

	rbPackList = append(rbPackList, rbPack)

	req = &newbroker.BatchRbPack{
		RbPacks: rbPackList,
	}

	reqLog, _ := json.Marshal(req)
	log.Trace.Infof(ctx, logTag, "BatchRoutePlanFromRB req=%v", string(reqLog))

	resp, err = client.BatchRoutePlanFromRB(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, logTag, "WalkRoutePlan error=[%v]", err)
		return 0, 0, err
	}

	if resp == nil || resp.RbPacks == nil || len(resp.RbPacks) == 0 || resp.RbPacks[0] == nil || resp.RbPacks[0].WalkRoutePoint_2MultPointRes == nil {
		log.Trace.Warnf(ctx, logTag, "WalkRoutePlan fail")
		return 0, 0, errors.New("WalkRoutePlan fail")
	}

	walkResp = resp.RbPacks[0].WalkRoutePoint_2MultPointRes
	if walkResp.RetCode != 0 {
		log.Trace.Warnf(ctx, logTag, "WalkRoutePlan errmsg=%s", walkResp.RetMsg)
		return 0, 0, errors.New("WalkRoutePlan fail")
	}

	if len(walkResp.Dists) == 0 || len(walkResp.Durations) == 0 {
		log.Trace.Warnf(ctx, logTag, "WalkRoutePlan no available distance or duration")
		return 0, 0, errors.New("no available distance or duration")
	}

	respLog, _ := json.Marshal(resp)
	log.Trace.Infof(ctx, logTag, "BatchRoutePlanFromRB resp=%v", string(respLog))

	return walkResp.Dists[0], walkResp.Durations[0], nil
}

func buildTrace(ctx context.Context) *base.Trace {
	trace := base.NewTrace()
	if val, ok := legoTrace.GetCtxTrace(ctx); ok {
		trace.TraceId = &val.TraceId
	}
	trace.Caller = CallerId
	return trace
}

func BatchWalkRoutePlan(ctx context.Context, fromPoint *Point, toPoints []*Point) (dists map[string]float64, err error) {
	if len(toPoints) == 0 {
		return nil, nil
	}
	// 1. 构造起点
	startGeo := &base.PointT{
		X: base.CoordT(util.ToInt32(fromPoint.Lng * Ratio)),
		Y: base.CoordT(util.ToInt32(fromPoint.Lat * Ratio)),
	}
	// 2. 构造终点
	var endGeos []*base.PointT
	for _, toPoint := range toPoints {
		endGeo := &base.PointT{
			X: base.CoordT(util.ToInt32(toPoint.Lng * Ratio)),
			Y: base.CoordT(util.ToInt32(toPoint.Lat * Ratio)),
		}
		endGeos = append(endGeos, endGeo)
	}
	// 3. 构造入参
	walkReq := &route_plan_base.Point2MultPointWalkRouteRequest{
		StartGeo:     startGeo,
		EndGeos:      endGeos,
		CallerID:     proto.StrPtr(CallerId),
		DownStrategy: proto.Int16Ptr(0),
		Trace:        buildTrace(ctx),
	}

	rbReq := &newbroker.RouteBrokerApiRequest{
		CallerID: proto.StrPtr(CallerId),
		Trace:    buildTrace(ctx),
	}
	rbPack := &newbroker.RbPack{
		RbReq:                        rbReq,
		WalkRoutePoint_2MultPointReq: walkReq,
	}
	var rbPackList []*newbroker.RbPack
	rbPackList = append(rbPackList, rbPack)
	req := &newbroker.BatchRbPack{
		RbPacks:  rbPackList,
		CallerID: proto.StrPtr(CallerId),
		Trace:    buildTrace(ctx),
	}
	// 4. rpc调用
	resp, err := client.BatchRoutePlanFromRB(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, logTag, "BatchWalkRoutePlan error=[%v]", err)
		return nil, err
	}
	respLog, _ := json.Marshal(resp)
	log.Trace.Infof(ctx, logTag, "BatchRoutePlanFromRB resp=%v", string(respLog))
	// 5. 异常判断
	if resp == nil || resp.RbPacks == nil || len(resp.RbPacks) == 0 || resp.RbPacks[0] == nil || resp.RbPacks[0].WalkRoutePoint_2MultPointRes == nil {
		log.Trace.Warnf(ctx, logTag, "BatchWalkRoutePlan fail")
		return nil, errors.New("BatchWalkRoutePlan fail")
	}
	batchWalkResp := resp.RbPacks[0].WalkRoutePoint_2MultPointRes
	if batchWalkResp.RetCode != 0 {
		log.Trace.Warnf(ctx, logTag, "BatchWalkRoutePlan errmsg=%s", batchWalkResp.RetMsg)
		return nil, errors.New("BatchWalkRoutePlan fail")
	}
	if len(batchWalkResp.Dists) != len(toPoints) {
		log.Trace.Warnf(ctx, logTag, "BatchWalkRoutePlan no available distance or duration")
		return nil, errors.New("BatchWalkRoutePlan no available distance or duration")
	}
	// 6. 记录log返回结果
	respLog, _ = json.Marshal(resp)
	log.Trace.Infof(ctx, logTag, "BatchRoutePlanFromRB resp=%v", string(respLog))
	dists = make(map[string]float64)
	for i, p := range toPoints {
		dists[p.EstimateId] = batchWalkResp.Dists[i]
	}
	return dists, nil
}
