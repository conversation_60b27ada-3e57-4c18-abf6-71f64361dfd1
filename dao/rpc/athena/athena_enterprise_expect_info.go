package athena

import (
	"context"
	"errors"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

type enterpriseExpectInfoRequestWrap struct {
	req *AthenaApiv3.AthenaEnterpriseExpectInfoReq
}

type PassengerInfo struct {
	Pid   *string
	Phone *string
}

type GEOInfo struct {
	CityID   *int32
	FromLng  *float64
	ToLng    *float64
	FromLat  *float64
	ToLat    *float64
	FromName *string
	ToName   *string
}

type CommonInfo struct {
	AppVersion  *string
	AccessKeyID *string
	ClientType  *string
	Channel     *int32
	Lang        *string
}

func NewAthenaEnterpriseExpectInfoRequestWrap() *enterpriseExpectInfoRequestWrap {
	return &enterpriseExpectInfoRequestWrap{
		req: &AthenaApiv3.AthenaEnterpriseExpectInfoReq{},
	}
}

func (e *enterpriseExpectInfoRequestWrap) getRequest() *AthenaApiv3.AthenaEnterpriseExpectInfoReq {
	return e.req
}

func (e *enterpriseExpectInfoRequestWrap) WithReqType(reqType *int32) *enterpriseExpectInfoRequestWrap {
	e.req.ReqType = reqType

	return e
}

func (e *enterpriseExpectInfoRequestWrap) WithExpectScene(expectScene []string) *enterpriseExpectInfoRequestWrap {
	e.req.ExpectScene = expectScene

	return e
}

func (e *enterpriseExpectInfoRequestWrap) WithPassengerInfo(passengerInfo *PassengerInfo) *enterpriseExpectInfoRequestWrap {
	e.req.Pid = passengerInfo.Pid
	e.req.Phone = passengerInfo.Phone

	return e
}

func (e *enterpriseExpectInfoRequestWrap) WithGEOInfo(geoInfo *GEOInfo) *enterpriseExpectInfoRequestWrap {
	e.req.CityID = geoInfo.CityID
	e.req.FromLat = geoInfo.FromLat
	e.req.FromLng = geoInfo.FromLng
	e.req.ToLat = geoInfo.ToLat
	e.req.ToLng = geoInfo.ToLng
	e.req.FromName = geoInfo.FromName
	e.req.ToName = geoInfo.ToName

	return e
}

func (e *enterpriseExpectInfoRequestWrap) WithCommonInfo(commonInfo *CommonInfo) *enterpriseExpectInfoRequestWrap {
	e.req.AppVersion = commonInfo.AppVersion
	e.req.AccessKeyID = commonInfo.AccessKeyID
	e.req.ClientType = commonInfo.ClientType
	e.req.Channel = commonInfo.Channel
	e.req.Lang = commonInfo.Lang

	return e
}

func (e *enterpriseExpectInfoRequestWrap) WithCanAddProduct(canAddProduct []*AthenaApiv3.NTuple) *enterpriseExpectInfoRequestWrap {
	e.req.CanAddProduct = canAddProduct

	return e
}

func (e *enterpriseExpectInfoRequestWrap) WithAlreadySendProduct(alreadySendProduct []*AthenaApiv3.NTuple) *enterpriseExpectInfoRequestWrap {
	e.req.AlreadySendProduct = alreadySendProduct

	return e
}

func (e *enterpriseExpectInfoRequestWrap) WithBubbleTraceID(bubbleTraceID *string) *enterpriseExpectInfoRequestWrap {
	e.req.BubbleTraceID = bubbleTraceID

	return e
}

func (e *enterpriseExpectInfoRequestWrap) WithOrderID(orderID *int64) *enterpriseExpectInfoRequestWrap {
	e.req.OrderID = orderID

	return e
}

func (e *enterpriseExpectInfoRequestWrap) WithExtraInfo(extraInfo map[string]string) *enterpriseExpectInfoRequestWrap {
	e.req.ExtraInfo = extraInfo

	return e
}

// GetAthenaEnterpriseExpectInfo ...
func GetAthenaEnterpriseExpectInfo(ctx context.Context, req *enterpriseExpectInfoRequestWrap) (*AthenaApiv3.AthenaEnterpriseExpectInfoResp, error) {
	if req == nil {
		return nil, errors.New("req is nil")
	}

	tag := "GetAthenaEnterpriseExpectInfo"
	_trace := getTrace(ctx)

	resp, err := client.GetAthenaEnterpriseExpectInfo(ctx, req.getRequest(), &_trace)
	if err != nil {
		log.Trace.Warnf(ctx, tag, "GetAthenaEnterpriseBubbleRecommend fail with errno %v and err message %v", err)
		return nil, err
	}

	if resp == nil {
		log.Trace.Warnf(ctx, tag, "GetAthenaEnterpriseBubbleRecommend fail response is nil")
		return nil, err
	}

	if resp.ErrNo != 0 {
		log.Trace.Warnf(ctx, tag, "GetAthenaEnterpriseBubbleRecommend fail with errno %v and err message %v", resp.ErrNo, resp.ErrMsg)
		return nil, err
	}

	return resp, nil
}
