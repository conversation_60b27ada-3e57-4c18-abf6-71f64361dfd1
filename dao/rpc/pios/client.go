package pios

import (
	"context"
	"encoding/json"
	"errors"
	Pios "git.xiaojukeji.com/dirpc/dirpc-go-http-Pios"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
)

const disfName = "disf!biz-passenger-operation-pios"
const EncryptKey = "ba9A4eF31477dAB2"

var (
	client                   *Pios.Client
	ErrMultiProductQueryFail = errors.New("fail to get member info")
)

type Product struct {
	BusinessId   int64 `json:"business_id"`
	RequireLevel int64 `json:"require_level"`
	ComboType    int64 `json:"combo_type"`
	CarpoolType  int64 `json:"carpool_type"`
}

func InitClient() (err error) {
	client, err = Pios.NewClient(disfName)
	if err != nil {
		return err
	}

	return nil
}

func MultiProductQueryFromPios(ctx context.Context, uid int64, cityId int32, productList []Product, enableNewWaitReply int32) (productListRes *Pios.MultiQueryV2Resp, err error) {
	productListStr, _ := json.Marshal(productList)
	version := int32(3)
	req := &Pios.MultiQueryV2Req{
		Caller:      "mamba",
		CityId:      &cityId,
		Uid:         &uid,
		ProductList: string(productListStr),
		SwitchFlow:  &version,
	}

	if enableNewWaitReply != 0 {
		req.EnableNewWaitReply = &enableNewWaitReply
	}

	sign, err := getSign(req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to gen sign with err %v", err)
		return nil, ErrMultiProductQueryFail
	}

	req.Sign = sign
	productListRes, err = client.MultiProductsV2Query(ctx, req)
	return
}
