package routebroker

// http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=144447873

import (
	"context"
	"time"

	service "git.xiaojukeji.com/dirpc/dirpc-go-thrift-RouteBrokerService"
	RouteBrokerClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/routeBrokerClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
)

var client *RouteBrokerClient.Client

const ProductIDDefault = "3" //默认是快车业务线

// Init ...
func Init() (err error) {
	client, err = RouteBrokerClient.NewRouteBrokerServiceClient("disf!map-traffic-online-route_broker")
	if err != nil {
		return err
	}
	return nil
}

// GetETA http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=144689690
// duration 单位 s
func GetETA(ctx context.Context, src, dst *service.Base_GeoPoint) (ret *service.RouteBase_BaseRouteResponse, err error) {
	product := ProductIDDefault
	now := int32(time.Now().Unix())
	req := &service.RouteBrokerBase_PointToPointRequest{
		SrcPoint:      src,
		DstPoint:      dst,
		DepartureTime: &now,
		Strategy:      service.RouteBrokerBaseStrategyT_STATIC_MIN_DIST,
		CallerID:      "gulfstream_mamba",
		ProductID:     &product,
	}
	resp, err := client.PointToPointBaseRoute(ctx, req)

	if err != nil || resp == nil || resp.RetCode != 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "GetETA error error[%+v] resp[%+v]", err, resp)
		return nil, err
	}
	return resp, nil
}
