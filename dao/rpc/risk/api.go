package risk

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	DIRPCNewtonCommon "git.xiaojukeji.com/dirpc/dirpc-go-http-NewtonCommon"
)

var (
	ErrParamNotValid = errors.New("param invalid")
	ErrNetwork       = errors.New("newwork problem")
	ErrRiskHack      = errors.New("block by risk")

	SFCHackCodeNetWorkOK int32 = 200
	SFCHackCodeNormal    int32 = 100001
	SFCHackCodeNeedBlock int32 = 100002
	SFCHackCodeForPoison int32 = 100003
)

// HackNewPrice 预估6.0防刷
// http://newton.intra.xiaojukeji.com/thor/public-document/SERVICE_HACK/SERVICE_HACK_NEW_PRICE
func HackNewPrice(ctx context.Context, payload *HackNewPriceDataBody) error {
	const (
		NetworkOK int32 = 200

		Normal    int32 = 100001
		NeedBlock int32 = 100002
	)

	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	if err := encoder.Encode(payload); err != nil {
		return fmt.Errorf("%w %s", ErrParamNotValid, err.Error())
	}

	req := &DIRPCNewtonCommon.NewtonReq{
		Data: buf.String(),
	}
	resp, err := client.RiskServiceHackNewPrice(ctx, req)
	if err != nil {
		return fmt.Errorf("%w %s", ErrNetwork, err.Error())
	}
	if resp == nil || resp.ApiCode != NetworkOK || resp.Data == nil {
		return ErrNetwork
	}
	if resp.Data.Code == NeedBlock {
		return ErrRiskHack
	}
	return nil
}

// HackCompositeNewPrice 组合出行类接口防刷
// https://newton.intra.xiaojukeji.com/thor/public-document/SERVICE_HACK/SERVICE_HACK_WYC_COMPOSITE_ESTIMATE_PRICE
func HackCompositeNewPrice(ctx context.Context, payload *HackCompositeDataBody) error {
	const (
		NetworkOK int32 = 200

		Normal    int32 = 100001
		NeedBlock int32 = 100002
	)

	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	if err := encoder.Encode(payload); err != nil {
		return fmt.Errorf("%w %s", ErrParamNotValid, err.Error())
	}

	req := &DIRPCNewtonCommon.NewtonReq{
		Data: buf.String(),
	}

	resp, err := client.RiskServiceHackCompositeEstimatePrice(ctx, req)
	if err != nil {
		return fmt.Errorf("%w %s", ErrNetwork, err.Error())
	}

	if resp == nil || resp.ApiCode != NetworkOK || resp.Data == nil {
		return ErrNetwork
	}

	if resp.Data.Code == NeedBlock {
		return ErrRiskHack
	}

	return nil
}

// HackSfcEstimatePrice 顺风车预估类接口防刷
func HackSfcEstimatePrice(ctx context.Context, payload *HacSfcEstimatePriceDataBody) (int32, error) {

	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	if err := encoder.Encode(payload); err != nil {
		return 0, fmt.Errorf("%w %s", ErrParamNotValid, err.Error())
	}

	req := &DIRPCNewtonCommon.NewtonReq{
		Data: buf.String(),
	}
	// 换成实际的接口
	resp, err := client.RiskServiceHackSfcNewPrice(ctx, req)

	if err != nil {
		return 0, fmt.Errorf("%w %s", ErrNetwork, err.Error())
	}
	if resp == nil || resp.ApiCode != SFCHackCodeNetWorkOK || resp.Data == nil {
		return 0, ErrNetwork
	}
	if resp.Data.Code == SFCHackCodeNeedBlock {
		return 0, ErrRiskHack
	}
	return resp.Data.Code, nil

}
