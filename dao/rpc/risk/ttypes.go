package risk

// HackNewPricePayload 风控预估6.0防刷
// 下游不提供明确的IDL, 只能自己编码在业务代码中, 然后手动序列化之后传给下游字符串
// 对于空值/零值, 字段需要保留
type HackNewPricePayload struct {
	Data *HackNewPriceDataBody `json:"data"`
}

// HackNewPriceDataBody 预估6.0防刷数据部分
type HackNewPriceDataBody struct {
	Header *HackNewPriceDataHeader `json:"header"`
	Params *HackNewPriceDataParams `json:"params"`
}

// HackNewPriceDataHeader 预估接口的请求头
type HackNewPriceDataHeader struct {
	UserAgent     string `json:"User-Agent"`
	Referer       string `json:"Referer"`
	XRealIP       string `json:"X-Real-Ip"`
	DidiHeaderRID string `json:"Didi-Header-Rid"`
}

// HackNewPriceDataParams 预估接口的入参们
type HackNewPriceDataParams struct {
	ToPoiType                  string  `json:"to_poi_type"`
	AppVersion                 string  `json:"app_version"`
	Channel                    int64   `json:"channel"`
	PassportIsTestCell         bool    `json:"passport_is_test_cell"`
	ClientType                 int64   `json:"client_type"`
	LuxurySelectDriver         string  `json:"luxury_select_driver"`
	A3Token                    string  `json:"a3_token"`
	FlightDepCode              string  `json:"flight_dep_code"`
	UserType                   int64   `json:"user_type"`
	PlatformType               int64   `json:"platform_type"`
	ActivityID                 int64   `json:"activity_id"`
	DialogID                   string  `json:"dialog_id"`
	From                       string  `json:"from"`
	FromPoiType                string  `json:"from_poi_type"`
	FromAddress                string  `json:"from_address"`
	DepartureTime              string  `json:"departure_time"`
	OrderType                  int64   `json:"order_type"`
	TerminalID                 string  `json:"terminal_id"`
	Lat                        float64 `json:"lat"`
	ShakeFlag                  int64   `json:"shake_flag"`
	Lng                        float64 `json:"lng"`
	DestPoiCode                int64   `json:"dest_poi_code"`
	CallCarType                int64   `json:"call_car_type"`
	OriginPageType             int64   `json:"origin_page_type"`
	Suuid                      string  `json:"suuid"`
	Openid                     string  `json:"openid"`
	SpecialSceneParam          int64   `json:"special_scene_param"`
	PassportUID                int64   `json:"passport_uid"`
	ToAddress                  string  `json:"to_address"`
	PassportDuid               int64   `json:"passport_duid"`
	ShiftTime                  int64   `json:"shift_time"`
	Pixels                     string  `json:"pixels"`
	TrafficNumber              string  `json:"traffic_number"`
	MultiRequireProduct        string  `json:"multi_require_product"`
	TrafficDepTime             string  `json:"traffic_dep_time"`
	PassportCell               string  `json:"passport_cell"`
	AccessKeyID                int64   `json:"access_key_id"`
	AirportID                  int64   `json:"airport_id"`
	FromLng                    float64 `json:"from_lng"`
	PageType                   int64   `json:"page_type"`
	PreferredRouteID           string  `json:"preferred_route_id"`
	OriginID                   int64   `json:"origin_id"`
	PassportAppid              int64   `json:"passport_appid"`
	FromName                   string  `json:"from_name"`
	PaymentsType               int64   `json:"payments_type"`
	DestPoiTag                 string  `json:"dest_poi_tag"`
	PassportCreateTime         int64   `json:"passport_create_time"`
	LuxurySelectCarlevels      string  `json:"luxury_select_carlevels"`
	PassportCountryID          int64   `json:"passport_country_id"`
	FlightDepTerminal          string  `json:"flight_dep_terminal"`
	HasScroll                  int64   `json:"has_scroll"`
	TrafficArrTime             string  `json:"traffic_arr_time"`
	PreTraceID                 string  `json:"pre_trace_id"`
	DepartureRange             string  `json:"departure_range"`
	TooFarOrderLimit           int64   `json:"too_far_order_limit"`
	ToLng                      float64 `json:"to_lng"`
	PassportRole               string  `json:"passport_role"`
	Lang                       string  `json:"lang"`
	PassportOriginID           string  `json:"passport_origin_id"`
	FromPoiID                  string  `json:"from_poi_id"`
	MenuID                     string  `json:"menu_id"`
	FromLat                    float64 `json:"from_lat"`
	ToLat                      float64 `json:"to_lat"`
	AirportType                int64   `json:"airport_type"`
	PassportCountryCallingCode string  `json:"passport_country_calling_code"`
	ToName                     string  `json:"to_name"`
	FlightArrCode              string  `json:"flight_arr_code"`
	FlightArrTerminal          string  `json:"flight_arr_terminal"`
	BizTicket                  string  `json:"biz_ticket"`
	URL                        string  `json:"url"`
	Token                      string  `json:"token"`
	ToPoiID                    string  `json:"to_poi_id"`
	GuideType                  int64   `json:"guide_type"`
	Maptype                    string  `json:"maptype"`
	Imei                       string  `json:"imei"`
	CallCarPhone               string  `json:"call_car_phone"`
}

// HackCompositePayload 风控组合出行防刷
type HackCompositePayload struct {
	Data *HackNewPriceDataBody `json:"data"`
}

// HackCompositeDataBody 组合出行防刷数据部分
type HackCompositeDataBody struct {
	Header *HackCompositeDataHeader `json:"header"`
	Params *HackCompositeDataParams `json:"params"`
}

// HackCompositeDataHeader 组合出行防刷请求头
type HackCompositeDataHeader struct {
	UserAgent     string `json:"user_agent"`
	Referer       string `json:"referer"`
	XRealIP       string `json:"x_real_ip"`
	DidiHeaderRID string `json:"didi_header_rid"`
	Wsgdid        string `json:"wsgdid"`
	Wsgsig        string `json:"wsgsig"`
}

type HackCompositeDataParams struct {
	AccessKeyID  string  `json:"access_key_id"`
	WycFromLng   float64 `json:"wyc_from_lng"`
	AppVersion   string  `json:"app_version"`
	FromLng      float64 `json:"from_lng"`
	WycFromLat   float64 `json:"wyc_from_lat"`
	Channel      string  `json:"channel"`
	ClientType   string  `json:"client_type"`
	FromName     string  `json:"from_name"`
	UserType     string  `json:"user_type"`
	ToLng        float64 `json:"to_lng"`
	OrderType    string  `json:"order_type"`
	Lat          float64 `json:"lat"`
	FromLat      float64 `json:"from_lat"`
	WycFromName  string  `json:"wyc_from_name"`
	ToLat        float64 `json:"to_lat"`
	WycToLat     float64 `json:"wyc_to_lat"`
	Lng          float64 `json:"lng"`
	Ddfp         string  `json:"ddfp"`
	WycToLng     float64 `json:"wyc_to_lng"`
	ToName       string  `json:"to_name"`
	WycToName    string  `json:"wyc_to_name"`
	Token        string  `json:"token"`
	IsRecLanding string  `json:"is_rec_landing"`
}

// HackSfcEstimatePrice 顺风车预估类防刷
type HacSfcEstimatePricePayload struct {
	Data *HacSfcEstimatePriceDataBody `json:"data"`
}

// HackSfcEstimatePrice 顺风车预估类防刷数据部分
type HacSfcEstimatePriceDataBody struct {
	Header *HacSfcEstimatePriceDataHeader `json:"header"`
	Params *HacSfcEstimatePriceDataParams `json:"params"`
}

// HackSfcEstimatePrice 顺风车预估类防刷请求头
type HacSfcEstimatePriceDataHeader struct {
	UserAgent     string `json:"user_agent"`
	Referer       string `json:"referer"`
	XRealIP       string `json:"x_real_ip"`
	DidiHeaderRID string `json:"didi_header_rid"`
	Wsgdid        string `json:"wsgdid"`
}

// HackSfcEstimatePrice 顺风车预估类防刷的入参们
type HacSfcEstimatePriceDataParams struct {
	AccessKeyID         int64   `json:"access_key_id"`
	ToPoiType           string  `json:"to_poi_type"`
	AppVersion          string  `json:"app_version"`
	FromLng             float64 `json:"from_lng"`
	PriceTime           int64   `json:"price_time"`
	OID                 string  `json:"oid"`
	FromName            string  `json:"from_name"`
	PassengerNums       int64   `json:"passenger_nums"`
	MapType             string  `json:"map_type"`
	SelectNum           string  `json:"select_num"`
	ChooseFSearchID     string  `json:"choose_f_searchid"`
	Wsgenv              string  `json:"wsgenv"`
	DepartureRange      string  `json:"departure_range"`
	ToLng               float64 `json:"to_lng"`
	API                 string  `json:"api"`
	CarpoolType         string  `json:"carpool_type"`
	OrderType           int64   `json:"order_type"`
	FromPoiType         string  `json:"from_poi_type"`
	FromAddress         string  `json:"from_address"`
	DepartureTime       string  `json:"departure_time"`
	Lat                 float64 `json:"lat"`
	FromPoiID           string  `json:"from_poi_id"`
	FromLat             float64 `json:"from_lat"`
	ToLat               float64 `json:"to_lat"`
	FromCityID          string  `json:"from_city_id"`
	ChooseTSearchID     string  `json:"choose_t_searchid"`
	Lng                 float64 `json:"lng"`
	ToName              string  `json:"to_name"`
	EstimateID          string  `json:"estimate_id"`
	SelectTime          string  `json:"select_time"`
	ToAddress           string  `json:"to_address"`
	PassengerNum        int64   `json:"passenger_num"`
	Token               string  `json:"token"`
	FromPageID          string  `json:"from_page_id"`
	ToPoiID             string  `json:"to_poi_id"`
	MultiRequireProduct string  `json:"multi_require_product"`
	District            string  `json:"district"`
	ToCityID            string  `json:"to_city_id"`
	PriceType           string  `json:"price_type"`
}
