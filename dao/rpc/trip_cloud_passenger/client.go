package trip_cloud_passenger

import (
	"context"
	"errors"

	TripCloudPassenger "git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassenger"
	TripCloudPassengerClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/tripCloudPassengerClient"
)

var client *TripCloudPassengerClient.Client

const (
	disfName = "disf!biz-gs-tripcloud_passenger"
)

func InitClient() (err error) {
	client, err = TripCloudPassengerClient.NewTripCloudPassengerClient(disfName)
	if err != nil {
		return err
	}

	return nil
}

func GetChildTicketRule(ctx context.Context, req *TripCloudPassenger.GetCarpoolIntercityRuleRequest) (*TripCloudPassenger.GetCarpoolIntercityRuleResponse, error) {
	if client == nil {
		return nil, errors.New("trip cloud passenger cli is nil")
	}

	resp, err := client.PGetCarpoolIntercityRule(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Errno != 0 {
		return nil, errors.New("errno is not equal zero")
	}

	return resp, err
}

func GetStationBasicPrice(ctx context.Context, req *TripCloudPassenger.GetStationPriceRequest) (*TripCloudPassenger.GetStationPriceResponse, error) {
	if client == nil {
		return nil, errors.New("trip cloud passenger cli is nil")
	}

	resp, err := client.PGetStationPrice(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Errno != 0 {
		return nil, errors.New("errno is not equal zero")
	}

	return resp, err
}

func PGetMultiStationPrice(ctx context.Context, req *TripCloudPassenger.GetStationPriceRequest) (*TripCloudPassenger.GetStationPriceResponse, error) {
	if client == nil {
		return nil, errors.New("trip cloud passenger cli is nil")
	}
	resp, err := client.PGetStationPrice(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Errno != 0 {
		return nil, errors.New("errno is not equal zero")
	}

	return resp, err
}
