package dfs

import (
	"context"
	"encoding/json"
	dfs "git.xiaojukeji.com/dirpc/dirpc-go-thrift-dfs"
	dfsclient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/dfsClient"
	legoTrace "git.xiaojukeji.com/lego/context-go"
)

const disfName = "disf!biz-gs-dfs"

var (
	client *dfsclient.Client
)

// InitClient 初始化司机client
func InitClient() (err error) {
	client, err = dfsclient.NewdfsClient(disfName)
	if err != nil {
		return err
	}

	return nil
}

func GetLuxDriverTags(ctx context.Context, driverId int64) ([]int, error) {

	featureData := []string{"lux_driver_specific_feature"}

	req := &dfs.FeatureGetParams{
		DriverID:    driverId,
		FeatureKeys: featureData,
		Params:      nil,
	}

	var traceInfo *dfs.Trace
	if _trace, ok := legoTrace.GetCtxTrace(ctx); ok {
		traceInfo = &dfs.Trace{
			Caller:      _trace.GetCallerFunc(),
			SrcMethod:   _trace.SrcMethod,
			SpanID:      _trace.GetSpanId(),
			HintContent: _trace.GetHintContent(),
			HintCode:    _trace.GetHintCode(),
			TraceID:     _trace.GetTraceId(),
		}
	}

	feature, err := client.GetDriverFeature(ctx, req, traceInfo)

	if err != nil || len(feature.GetData()) <= 0 {
		return nil, err
	}

	if luxFeature, ok := feature.GetData()["lux_driver_specific_feature"]; ok {
		var tagsData = make(map[string]interface{}, 0)
		var tags = make([]int, 0)

		err = json.Unmarshal([]byte(luxFeature), &tagsData)
		if err == nil {
			if tagList, ok := tagsData["specific_grade_feature"]; ok {
				if results, ok := tagList.([]interface{}); ok {
					for _, tag := range results {
						if tagVal, ok := tag.(float64); ok {
							tags = append(tags, int(tagVal))
						}
					}
				}
				return tags, nil
			}
		}
	}

	return nil, nil

}
