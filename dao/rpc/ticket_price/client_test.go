package ticket_price

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

// 该测试函数由AI自动生成
func TestTicketPriceClient(t *testing.T) {
	ctx := context.Background()
	mockReq := &ticketPrice.GetCarpoolIntercityRuleRequest{
		RuleType:          3,
		BusServiceShiftId: "DZ701574104",
		BusinessId:        3023,
		ComboId:           202072,
	}

	tests := []struct {
		name    string
		setup   func() *gomonkey.Patches
		wantErr bool
	}{
		{
			name: "客户端为空",
			setup: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				client = nil
				return patches
			},
			wantErr: true,
		},
		{
			name: "RPC调用成功",
			setup: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				mockClient := &ticketPrice.Client{}
				client = mockClient

				patches.ApplyMethod(reflect.TypeOf(mockClient), "PGetCarpoolIntercityRule",
					func(_ *ticketPrice.Client, _ context.Context, _ *ticketPrice.GetCarpoolIntercityRuleRequest) (*ticketPrice.GetCarpoolIntercityRuleResponse, error) {
						return &ticketPrice.GetCarpoolIntercityRuleResponse{
							Errno: 0,
							Data:  &ticketPrice.RuleData{},
						}, nil
					})
				return patches
			},
			wantErr: false,
		},
		{
			name: "RPC调用失败",
			setup: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				mockClient := &ticketPrice.Client{}
				client = mockClient

				patches.ApplyMethod(reflect.TypeOf(mockClient), "PGetCarpoolIntercityRule",
					func(_ *ticketPrice.Client, _ context.Context, _ *ticketPrice.GetCarpoolIntercityRuleRequest) (*ticketPrice.GetCarpoolIntercityRuleResponse, error) {
						return nil, fmt.Errorf("error")
					})
				return patches
			},
			wantErr: true,
		},
		{
			name: "业务错误",
			setup: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				mockClient := &ticketPrice.Client{}
				client = mockClient

				patches.ApplyMethod(reflect.TypeOf(mockClient), "PGetCarpoolIntercityRule",
					func(_ *ticketPrice.Client, _ context.Context, _ *ticketPrice.GetCarpoolIntercityRuleRequest) (*ticketPrice.GetCarpoolIntercityRuleResponse, error) {
						return &ticketPrice.GetCarpoolIntercityRuleResponse{
							Errno: 1,
							Data:  nil,
						}, nil
					})
				return patches
			},
			wantErr: true,
		},
		{
			name: "业务错误2",
			setup: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				mockClient := &ticketPrice.Client{}
				client = mockClient

				patches.ApplyMethod(reflect.TypeOf(mockClient), "PGetCarpoolIntercityRule",
					func(_ *ticketPrice.Client, _ context.Context, _ *ticketPrice.GetCarpoolIntercityRuleRequest) (*ticketPrice.GetCarpoolIntercityRuleResponse, error) {
						return nil, nil
					})
				return patches
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := tt.setup()
			defer patches.Reset()

			InitClient()
			resp, err := PGetCarpoolIntercityRule(ctx, mockReq)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
			}
		})
	}
}
