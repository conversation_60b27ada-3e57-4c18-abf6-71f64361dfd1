package combined_travel_v2

import (
	"context"

	CombinedTravel_SDK "git.xiaojukeji.com/dirpc/dirpc-go-http-CombinedTravel"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/combinedtravelclient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

var (
	client *combinedtravelclient.Client
)

const (
	DISF   = "disf!biz-gs-combined_travel"
	LogTag = "CombinedTravelClient"
)

func InitClient() error {
	var err error
	client, err = combinedtravelclient.NewCombinedTravelClient(DISF)
	if err != nil {
		return err
	}

	return nil
}

func UTRecommend(ctx context.Context, req *CombinedTravel_SDK.UTRecommendReq) *CombinedTravel_SDK.UTRecommendResp {
	// http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=*********
	if req == nil {
		log.Trace.Warnf(ctx, LogTag, "_msg=CombinedTravel req=%+v", req)
		return nil
	}

	ret, err := client.UTRecommend(ctx, req)
	if err != nil || ret == nil || ret.Errno != 0 {
		log.Trace.Warnf(ctx, LogTag, "_msg= UTRecommend failed||req=%+v||err=%v||data=%v",
			req, err, util.ToJSONString(ret))
		return nil
	}
	if ret.Data == nil {
		log.Trace.Infof(ctx, LogTag, "_msg= UTRecommend failed||req=%+v||err=%v", req, ret.Errmsg)
		return nil
	}
	return ret
}
