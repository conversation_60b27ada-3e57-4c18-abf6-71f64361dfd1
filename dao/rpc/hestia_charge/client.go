package hestia_charge

import (
	"context"
	"encoding/json"
	"errors"
	Sps "git.xiaojukeji.com/dirpc/dirpc-go-http-Sps"

	HestiaCharge "git.xiaojukeji.com/dirpc/dirpc-go-thrift-HestiaCharge"
	trace "git.xiaojukeji.com/lego/context-go"

	"github.com/spf13/cast"

	HestiaChargeClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/hestiachargeclient"
)

// DISFName hestiaCharge DISFName
const (
	DISFName    = "disf!new-taxi-core-hestia_charge"
	SpsDISFName = "disf!biz-gs-sps"
)

var (
	client    *HestiaChargeClient.Client
	SpsClient *Sps.Client
)

// GetFeeReq 请求出租车sps接口入参
type GetFeeReq struct {
	CityID          int32  `json:"city_id"`
	ServiceID       int32  `json:"service_id"`
	EstimateID      string `json:"estimate_id"`
	ProductCategory int32  `json:"product_category"`
	FromCounty      int32  `json:"from_county"`
	OrderType       int32  `json:"order_type"`
	ExtraInfo       string `json:"extra_info"`
}

// TaxiSpsData 出租车sps数据
type TaxiSpsData struct {
	PassengerPrice    int    `json:"passenger_price"`
	PassengerDiscount int    `json:"passenger_discount"`
	DriverPrice       int    `json:"driver_price"`
	CanSelect         int    `json:"can_select"`
	FeeName           string `json:"fee_name"`
	FeeID             int    `json:"fee_id"`
	HasFee            int    `json:"has_fee"`
	IsHoliday         int    `json:"is_holiday"`
	IsFixPrice        int    `json:"is_fix_price"`
}

// Init hestia初始化方法
func Init() (err error) {

	if client, err = HestiaChargeClient.NewHestiaChargeClient(DISFName); err != nil {
		return err
	}

	if SpsClient, err = Sps.NewClient(SpsDISFName); err != nil {
		return err
	}

	return nil
}

// GetFee 请求出租车sps
func GetFee(ctx context.Context, req *GetFeeReq) (r *HestiaCharge.GetFeeResp, err error) {
	var getFeeReq = new(HestiaCharge.GetFeeReq)
	var hintCode int64
	traceCtx, ok := trace.GetCtxTrace(ctx)
	if !ok {
		return nil, errors.New("get hestia charge failed")
	}
	if traceCtx.HintCode == "" {
		hintCode = 0
	} else {
		hintCode = cast.ToInt64(traceCtx.HintCode)
	}

	if err != nil {
		return nil, errors.New("get hestia charge failed")
	}
	hestiaTrace := &HestiaCharge.Trace{
		TraceID:     traceCtx.TraceId,
		SpanID:      traceCtx.SpanId,
		HintCode:    hintCode,
		HintContent: traceCtx.HintContent,
	}
	reqJSON, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(reqJSON, getFeeReq)
	if err != nil {
		return nil, err
	}

	return client.GetFee(ctx, hestiaTrace, getFeeReq)
}
