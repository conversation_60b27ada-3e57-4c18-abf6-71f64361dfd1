package carpool_web

import (
	"context"
	"fmt"
	carpool "git.xiaojukeji.com/dirpc/dirpc-go-http-Carpool"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/carpoolClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	CarPoolDisfServName = "disf!biz-gs-carpoolweb"
)

type CarpoolClient struct {
	*carpoolClient.Client
}

var client *CarpoolClient

// Init 初始化单例
func Init() error {
	cli, err := carpoolClient.NewCarpoolClient(CarPoolDisfServName)
	if err != nil {
		return err
	}
	client = &CarpoolClient{Client: cli}
	return nil
}

// GetCarpoolClient get
func GetCarpoolClient() *CarpoolClient {
	return client
}

// CGetPoolInfo 获取行程共乘关系
func CGetPoolInfo(ctx context.Context, driverId, travelId int64) (result *carpool.CGetPoolInfoResult, err error) {
	poolInfoReq := &carpool.CGetPoolInfoReq{
		DriverId: driverId,
		TravelId: travelId,
	}
	resp, err := client.CGetPoolInfo(ctx, poolInfoReq)
	if err != nil || nil == resp {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "Carpool.CGetPoolInfo failed||resp=%V||error=%s", resp, err.Error())
		return nil, err
	}
	if "0" != resp.Errno {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "Carpool.CGetPoolInfo resp wrong||resp=%V", resp)
		return nil, fmt.Errorf("Carpool.CGetPoolInfo resp wrong, msg: %+v", resp.Errmsg)
	}
	return resp.Result, nil
}
