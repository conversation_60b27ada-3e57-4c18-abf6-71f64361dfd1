package passport

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"

	Passport "git.xiaojukeji.com/dirpc/dirpc-go-http-Passport"
	PassportClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/passportClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/kronos"
	"git.xiaojukeji.com/gulfstream/passenger-common/sdk/passport"
	legoTrace "git.xiaojukeji.com/lego/context-go"
)

// 准备模拟请求的数据
func mockPassportValidatePlusResp() *Passport.ValidatePlusResp {
	uid := int64(12345678)
	cell := "12345678901"
	countryCallingCode := "86"
	role := int32(1)
	duid := int64(87654321)
	channel := "channel_test"
	appid := int32(10000)
	originId := "origin_test"
	createTime := int64(1600000000)
	isTestCell := false

	return &Passport.ValidatePlusResp{
		Errno:              0,
		Uid:                &uid,
		Cell:               &cell,
		CountryCallingCode: &countryCallingCode,
		Role:               &role,
		Duid:               &duid,
		Channel:            &channel,
		Appid:              &appid,
		OriginId:           &originId,
		CreateTime:         &createTime,
		IsTestCell:         &isTestCell,
	}
}

// TestGetUserInfo 测试 GetUserInfo 函数
func TestGetUserInfo(t *testing.T) {
	ctx := context.Background()
	token := "test_token"
	funcCaller := ""

	// 直接模拟 trace 方法，而不是返回 mockTrace 对象
	tracePatch := mockey.Mock(legoTrace.GetCtxTrace).Return(nil, false).Build()
	defer tracePatch.UnPatch()

	// 模拟 trace 的 SetCustomKV 方法 - 由于实际不会调用到此方法，可以移除这个mock

	// 模拟 util.GetPidByUid
	pidPatch := mockey.Mock(util.GetPidByUid).Return(uint64(12345678)).Build()
	defer pidPatch.UnPatch()

	t.Run("使用缓存且成功获取数据", func(t *testing.T) {
		// 模拟 Apollo FeatureToggle 返回 true，启用缓存
		apolloPatch := mockey.Mock(apollo.FeatureToggle).Return(true).Build()
		defer apolloPatch.UnPatch()

		// 模拟 passport.GetPassportInfoByTokenWithFusion 返回成功结果
		mockResp := mockPassportValidatePlusResp()
		fusionPatch := mockey.Mock(passport.GetPassportInfoByTokenWithFusion).Return(mockResp, nil).Build()
		defer fusionPatch.UnPatch()

		// 调用被测试函数
		userInfo, err := GetUserInfo(ctx, token, funcCaller)

		// 断言
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, userInfo, "用户信息不应为空")
		assert.Equal(t, uint64(12345678), userInfo.UID, "UID应该匹配")
		assert.Equal(t, "8612345678901", userInfo.Phone, "手机号应该匹配")
		assert.Equal(t, int32(1), userInfo.Role, "角色应该匹配")
		assert.Equal(t, uint64(87654321), userInfo.DUid, "DUID应该匹配")
		assert.Equal(t, "channel_test", userInfo.Channel, "渠道应该匹配")
		assert.Equal(t, int32(10000), userInfo.AppID, "应用ID应该匹配")
		assert.Equal(t, "origin_test", userInfo.OriginId, "原始ID应该匹配")
		assert.Equal(t, int64(1600000000), userInfo.CreateTime, "创建时间应该匹配")
		assert.Equal(t, "12345678901", userInfo.Cell, "Cell应该匹配")
		assert.Equal(t, false, userInfo.IsTestCell, "IsTestCell应该匹配")
	})

	t.Run("使用缓存但返回离线错误", func(t *testing.T) {
		// 模拟 Apollo FeatureToggle 返回 true，启用缓存
		apolloPatch := mockey.Mock(apollo.FeatureToggle).Return(true).Build()
		defer apolloPatch.UnPatch()

		// 模拟 passport.GetPassportInfoByTokenWithFusion 返回离线错误
		fusionPatch := mockey.Mock(passport.GetPassportInfoByTokenWithFusion).Return(nil, passport.Passport201Err).Build()
		defer fusionPatch.UnPatch()

		// 调用被测试函数
		userInfo, err := GetUserInfo(ctx, token, funcCaller)

		// 断言
		assert.Error(t, err, "应该有错误")
		assert.Equal(t, ErrOffline, err, "应该返回离线错误")
		assert.Nil(t, userInfo, "用户信息应为空")
	})

	t.Run("不使用缓存且成功获取数据", func(t *testing.T) {
		// 模拟 Apollo FeatureToggle 返回 false，不启用缓存
		apolloPatch := mockey.Mock(apollo.FeatureToggle).Return(false).Build()
		defer apolloPatch.UnPatch()

		// 模拟 client.ValidatePlus 返回成功结果
		mockResp := mockPassportValidatePlusResp()
		validatePlusPatch := mockey.Mock((*Passport.Client).ValidatePlus).Return(mockResp, nil).Build()
		defer validatePlusPatch.UnPatch()

		// 模拟 client 不为空
		oldClient := client
		client = &PassportClient.Client{Client: &Passport.Client{}}
		defer func() { client = oldClient }()

		// 调用被测试函数
		userInfo, err := GetUserInfo(ctx, token, funcCaller)

		// 断言
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, userInfo, "用户信息不应为空")
		assert.Equal(t, uint64(12345678), userInfo.UID, "UID应该匹配")
	})

	t.Run("返回错误码不为0", func(t *testing.T) {
		// 模拟 Apollo FeatureToggle 返回 false，不启用缓存
		apolloPatch := mockey.Mock(apollo.FeatureToggle).Return(false).Build()
		defer apolloPatch.UnPatch()

		// 模拟 client.ValidatePlus 返回错误码不为0的结果
		mockResp := mockPassportValidatePlusResp()
		mockResp.Errno = 400 // 设置一个非0错误码
		validatePlusPatch := mockey.Mock((*Passport.Client).ValidatePlus).Return(mockResp, nil).Build()
		defer validatePlusPatch.UnPatch()

		// 模拟 client 不为空
		oldClient := client
		client = &PassportClient.Client{Client: &Passport.Client{}}
		defer func() { client = oldClient }()

		// 调用被测试函数
		userInfo, err := GetUserInfo(ctx, token, funcCaller)

		// 断言
		assert.Error(t, err, "应该有错误")
		assert.Equal(t, ErrNot200, err, "应该返回错误码不为200错误")
		assert.Nil(t, userInfo, "用户信息应为空")
	})

	t.Run("返回离线错误码", func(t *testing.T) {
		// 模拟 Apollo FeatureToggle 返回 false，不启用缓存
		apolloPatch := mockey.Mock(apollo.FeatureToggle).Return(false).Build()
		defer apolloPatch.UnPatch()

		// 模拟 client.ValidatePlus 返回离线错误码的结果
		mockResp := mockPassportValidatePlusResp()
		mockResp.Errno = ErrnoOfflinePassenger // 设置离线错误码
		validatePlusPatch := mockey.Mock((*Passport.Client).ValidatePlus).Return(mockResp, nil).Build()
		defer validatePlusPatch.UnPatch()

		// 模拟 client 不为空
		oldClient := client
		client = &PassportClient.Client{Client: &Passport.Client{}}
		defer func() { client = oldClient }()

		// 调用被测试函数
		userInfo, err := GetUserInfo(ctx, token, funcCaller)

		// 断言
		assert.Error(t, err, "应该有错误")
		assert.Equal(t, ErrOffline, err, "应该返回离线错误")
		assert.Nil(t, userInfo, "用户信息应为空")
	})

	t.Run("GetUserInfo with funcCaller=Kronos", func(t *testing.T) {
		// 设置 funcCaller 为 Kronos
		localFuncCaller := Callee

		// 模拟 Apollo FeatureToggle 返回 false，不启用缓存
		apolloPatch := mockey.Mock(apollo.FeatureToggle).Return(false).Build()
		defer apolloPatch.UnPatch()

		// 模拟 client.ValidatePlus 返回成功结果
		mockResp := mockPassportValidatePlusResp()
		validatePlusPatch := mockey.Mock((*Passport.Client).ValidatePlus).Return(mockResp, nil).Build()
		defer validatePlusPatch.UnPatch()

		// 模拟 kronos.GetGenderByUid
		kronosPatch := mockey.Mock(kronos.GetGenderByUid).Return(int32(2)).Build()
		defer kronosPatch.UnPatch()

		// 模拟 client 不为空
		oldClient := client
		client = &PassportClient.Client{Client: &Passport.Client{}}
		defer func() { client = oldClient }()

		// 调用被测试函数
		userInfo, err := GetUserInfo(ctx, token, localFuncCaller)

		// 断言
		assert.NoError(t, err, "应该没有错误")
		assert.NotNil(t, userInfo, "用户信息不应为空")
		assert.Equal(t, int32(2), userInfo.UserGender, "性别应该匹配")
	})
}
