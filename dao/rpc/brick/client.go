package brick

import (
	"context"
	"errors"

	Dirpc_SDK_Brick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
)

var client *Dirpc_SDK_Brick.Client

// Init 初始化
func Init() (err error) {
	if client, err = Dirpc_SDK_Brick.NewClient("disf!biz-gs-brick"); err != nil {
		return err
	}
	return nil
}

// GetBusTicketRuleInfo 获取大车推荐路线、时间片、库存
func GetBusTicketRuleInfo(ctx context.Context, req *Dirpc_SDK_Brick.GetBusTicketRuleInfoReq) (*Dirpc_SDK_Brick.GetBusTicketRuleInfoResp, error) {
	resp, err := client.GetBusTicketRuleInfo(ctx, req)
	return resp, err
}

// GetIdentityPageInfo 获取身份信息
func GetIdentityPageInfo(ctx context.Context, req *Dirpc_SDK_Brick.GetIdentityPageInfoReq) (*Dirpc_SDK_Brick.IdentityPageInfoData, error) {
	resp, err := client.GetIdentityPageInfo(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.Errno != 0 {
		return nil, errors.New("errno is not equal zero")
	}

	return resp.Data, nil
}
