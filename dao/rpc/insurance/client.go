package insurance

import (
	"context"
	"errors"
	"fmt"

	Insurance "git.xiaojukeji.com/dirpc/dirpc-go-http-Insurance"
	InsuranceClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/insuranceClient"
)

const (
	MOUDULE_NAME                = "disf!biz-gs-insurance"
	STAGE_ETD_LATE_PAY_Pre_Sale = "pre_sale"
	Carpool_Insurance           = "carpool_insurance"
	Carpool_Compensation        = "carpool_compensation"
)

var client *InsuranceClient.Client

func InitClient() (err error) {
	if client, err = InsuranceClient.NewInsuranceClient(MOUDULE_NAME); err != nil {
		return err
	}

	return nil
}

func EtdCalculateTakeEffectStatus(ctx context.Context, insuracneReq *Insurance.EtdCalculateTakeEffectStatusReq) (*Insurance.EtdCalculateTakeEffectStatusRsp, error) {
	resp, err := client.EtdCalculateTakeEffectStatus(ctx, insuracneReq)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("Trying to get insurance infomation goes wrong,err=%v", err))
	}
	if resp.Errno != 0 || resp.Data == nil {
		return nil, errors.New(fmt.Sprintf("The response from Insurance has some mistakes,errno=%d||errmsg=%s", resp.Errno, resp.Errmsg))
	}
	return resp, nil
}

func ExposureCompensationInfo(ctx context.Context, insuracneReq *Insurance.ExposureCarpoolCompensationReq) (*Insurance.ExposureCarpoolCompensationRsp, error) {
	resp, err := client.Exposure(ctx, insuracneReq)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("Trying to get insurance Exposure goes wrong,err=%v", err))
	}
	if resp.Errno != 0 || resp.Data == nil {
		return nil, errors.New(fmt.Sprintf("The response from Insurance has some mistakes,errno=%d||errmsg=%s", resp.Errno, resp.Errmsg))
	}
	return resp, nil
}
