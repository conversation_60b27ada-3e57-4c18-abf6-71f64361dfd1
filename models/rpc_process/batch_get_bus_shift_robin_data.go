package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	pool2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/pool"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/shift_data"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/carpoolstation"
	"github.com/spf13/cast"
	"strconv"
)

type BusShiftRobinData struct {
	Req                  *models.BaseReqData
	BusShiftInventoryRes map[string]*carpoolstation.BusServerShift
}

func (bs *BusShiftRobinData) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
}

func (bs *BusShiftRobinData) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if bs.BusShiftInventoryRes == nil || len(bs.BusShiftInventoryRes) == 0 {
		return
	}

	info.BusShiftInventoryRobinData = bs.BusShiftInventoryRes

}

func NewBusInventoryRobinRPC(baseReq *models.BaseReqData) *BusShiftRobinData {
	if baseReq == nil {
		return nil
	}

	key := cast.ToString(baseReq.PassengerInfo.PID)
	apolloParam := map[string]string{
		"pid":   strconv.FormatInt(baseReq.PassengerInfo.PID, 10),
		"phone": baseReq.PassengerInfo.Phone,
		"city":  string(baseReq.AreaInfo.City),
	}

	if apollo.FeatureToggle(nil, "gs_intercity_bubble_tag_downgrade_toggle", key, apolloParam) {
		return nil
	}

	return &BusShiftRobinData{
		Req: baseReq,
	}
}

func (bs *BusShiftRobinData) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	if len(productFulls) == 0 {
		return false
	}
	var shiftIdList []string
	for _, productItem := range productFulls {
		if productItem == nil ||
			productItem.Product == nil ||
			productItem.Product.BizInfo == nil ||
			productItem.Product.BizInfo.StationInventoryInfo == nil ||
			len(productItem.Product.BizInfo.StationInventoryInfo.SelectInfo.ShiftID) == 0 {
			continue
		}
		shiftIdList = append(shiftIdList, productItem.Product.BizInfo.StationInventoryInfo.SelectInfo.ShiftID)
	}

	if len(shiftIdList) == 0 {
		return false
	}

	bs.BusShiftInventoryRes = bs.MultiGetShiftData(ctx, shiftIdList)
	return true
}

// 并行批量获取班次robin数据
func (bs *BusShiftRobinData) MultiGetShiftData(ctx context.Context, products []string) map[string]*carpoolstation.BusServerShift {
	res := make(map[string]*carpoolstation.BusServerShift)
	pool := pool2.NewPool(ctx)
	for _, pFull := range products {
		pool.AddTask(&pool2.Task{
			Req:     pFull,
			RunTask: shift_data.GetShiftData,
		})
	}

	//并行渲染
	_, err := pool.MultiProc()
	if err != nil {
		log.Trace.Info(ctx, "get_shift_data", "multi_visit robin got err: %v", err)
	}
	for _, task := range pool.GetResult() {
		tmp, ok := task.Resp.(carpoolstation.BusServerShift)
		if ok && len(tmp.BusServiceShiftID) > 0 {
			res[tmp.BusServiceShiftID] = &tmp
		}
	}
	return res
}
