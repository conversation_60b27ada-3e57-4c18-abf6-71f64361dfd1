package rpc_process

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
)

// BatchRouterDetailRPC 站点巴士批量通过路线获取站点
type BatchRouterDetailRPC struct {
	baseReq *models.BaseReqData

	routeDetailList map[int32]*Prfs.RouteDetailData
}

// NewBatchRouteDetail 站点巴士批量通过路线获取站点
func NewBatchRouteDetail(baseReq *models.BaseReqData) *BatchRouterDetailRPC {
	return &BatchRouterDetailRPC{
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (m *BatchRouterDetailRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (rd *BatchRouterDetailRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	if rd.routeDetailList != nil {
		info.RouteDetail = rd.routeDetailList[int32(product.BizInfo.ComboID)]
		fromIndex := 0
		toIndex := len(info.RouteDetail.RouteBasicInfo.StationList) - 1
		for k, v := range info.RouteDetail.RouteBasicInfo.StationList {
			if int(v.StationId) == info.StationInventoryInfo.SelectInfo.FromStationId {
				info.StationInventoryInfo.SelectInfo.FromStationInfo = v
				fromIndex = k
			}
			if int(v.StationId) == info.StationInventoryInfo.SelectInfo.DestStationId {
				info.StationInventoryInfo.SelectInfo.DestStationInfo = v
				toIndex = k
			}
			if v.StationType == "3" {
				info.HasPickAndDropStation = true
			}
		}
		if fromIndex > toIndex {
			fromIndex, toIndex = toIndex, fromIndex
		}
		info.StationInventoryInfo.StationList = info.RouteDetail.RouteBasicInfo.StationList[fromIndex : toIndex+1]
		if info.RouteDetail.RouteExtendInfo != nil {
			if info.RouteDetail.RouteExtendInfo.SeatLimit != nil && info.RouteDetail.RouteExtendInfo.SeatLimit.MaxPassengerCount != 0 {
				maxPassengerCount := info.RouteDetail.RouteExtendInfo.SeatLimit.MaxPassengerCount
				if info.MaxCarpoolSeatNum > maxPassengerCount {
					info.MaxCarpoolSeatNum = maxPassengerCount
				}
			}
			// 公交模式复制
			if info.RouteDetail.RouteExtendInfo.RouteTag == 2 {
				info.BusMode = "2"
			}
		}

	}
}

// 初始化路线中的属性
func (rd *BatchRouterDetailRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if rd.routeDetailList != nil {
		// 路线列表，对应n个站点
		// 站点ID:属性ID_1->粤澳；属性ID_2->港澳；...
		stationPropertyMap := make(map[int32][]*Prfs.PropertyInfo)
		for _, stationInfo := range rd.routeDetailList {
			if nil == stationInfo || stationInfo.RouteBasicInfo == nil || len(stationInfo.RouteBasicInfo.StationList) == 0 {
				continue
			}

			for _, basicInfo := range stationInfo.RouteBasicInfo.StationList {
				if basicInfo == nil {
					continue
				}
				if len(basicInfo.StationProperty) != 0 {
					stationPropertyMap[basicInfo.StationId] = basicInfo.StationProperty
				}
			}
		}
		info.StationPropertyMap = stationPropertyMap
	}
}

// Fetch 获取/加载
func (rd *BatchRouterDetailRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	routeIdList := []string{}

	for _, product := range products {
		if product == nil {
			continue
		}
		if !carpool.IsIntercityStation(ctx, int(product.CarpoolType)) {
			continue
		}

		if product.BizInfo != nil && product.BizInfo.ComboID != 0 {
			id := strconv.Itoa(int(product.BizInfo.ComboID))
			if !util.InArrayStr(id, routeIdList) {
				routeIdList = append(routeIdList, id)
			}

		}
	}

	if len(routeIdList) == 0 {
		return true
	}
	routeIdListStr, _ := json.Marshal(routeIdList)

	req := &Prfs.BatchRouteDetailReq{
		RouteIdList: string(routeIdListStr),
		CallerScene: PRFS_CALLER_SCENE,
		StartCityId: rd.baseReq.AreaInfo.City,
		CarpoolType: consts.CarPoolTypeInterCityStation,
	}

	resp := prfs.GetBatchRouteDetail(ctx, req)
	if resp == nil || len(resp.Data) <= 0 {
		return false
	}
	rd.routeDetailList = resp.Data

	return true
}
