package rpc_process

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/route_broker"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
	"math"
)

type SmartBusWalkRoutePlan struct {
	FromLng float64
	FromLat float64
	resp    map[string]float64
}

func NewSmartBusWalkRoutePlanRPC(_ context.Context, req *models.BaseReqData) *SmartBusWalkRoutePlan {
	return &SmartBusWalkRoutePlan{
		FromLng: req.AreaInfo.CurLng,
		FromLat: req.AreaInfo.CurLat,
	}
}

func (s *SmartBusWalkRoutePlan) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {

	fromPoint := &route_broker.Point{
		Lng: s.FromLng,
		Lat: s.FromLat,
	}
	toPoints := make([]*route_broker.Point, 0, 3)
	for _, p := range productFulls {
		preMatchInfo := p.GetSmartBusPreMatch()
		if preMatchInfo == nil || preMatchInfo.ExtMap == nil || preMatchInfo.ExtMap["chosen_station_info"] == "" {
			continue
		}
		var stationInfo map[string]interface{}
		err := json.Unmarshal([]byte(preMatchInfo.ExtMap["chosen_station_info"]), &stationInfo)
		if err != nil {
			continue
		}
		toPoints = append(toPoints, &route_broker.Point{
			Lng:        cast.ToFloat64(stationInfo["lng"]),
			Lat:        cast.ToFloat64(stationInfo["lat"]),
			EstimateId: p.Product.EstimateID,
		})
	}
	var err error
	s.resp, err = route_broker.BatchWalkRoutePlan(ctx, fromPoint, toPoints)
	if s.resp == nil || err != nil {
		return false
	}
	return true
}

func (s *SmartBusWalkRoutePlan) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

func (s *SmartBusWalkRoutePlan) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	info.WalkDist = proto.Int32Ptr(int32(math.Round(s.resp[product.Product.EstimateID])))
}
