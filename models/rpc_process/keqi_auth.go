package rpc_process

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
)

type GetNeedVerifiedInfo struct {
	req  *Prfs.GetNeedVerifiedReq
	resp map[int64]bool
}

// 获取rpc错误信息
func (m *GetNeedVerifiedInfo) GetErrorInfo(ctx context.Context) error {
	return nil
}

func NewGetNeedVerifiedInfoRpc(commonInfo *models.CommonInfo, areaInfo *models.AreaInfo, passengerInfo *models.PassengerInfo) *GetNeedVerifiedInfo {
	req := &Prfs.GetNeedVerifiedReq{
		CityId: areaInfo.City,
	}

	return &GetNeedVerifiedInfo{req, map[int64]bool{}}
}

func (rp *GetNeedVerifiedInfo) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		comboID, pcID int64
	)
	if len(products) == 0 {
		return false
	}

	comboID = products[0].BizInfo.ComboID
	pcID = products[0].ProductCategory

	if pcID == 0 || comboID == 0 {
		return false
	}
	rp.req.ComboId = comboID

	resp := prfs.GetNeedVerified(ctx, rp.req)
	if resp == nil {
		return false
	}

	if resp.Errno != 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get prfs GetNeedVerified error with errno %d", resp.Errno)
	}
	if resp.Data != nil && resp.Data.NeedVerified != false {
		rp.resp[pcID] = true
	}
	return true
}

func (rp *GetNeedVerifiedInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

func (rp *GetNeedVerifiedInfo) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	if rp.resp != nil {
		if status, bo := rp.resp[product.ProductCategory]; status && bo {
			info.NeedVerified = true
		}
	}
}
