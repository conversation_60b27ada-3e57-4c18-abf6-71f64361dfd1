package rpc_process

import (
	"context"
	"strconv"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	Trace "git.xiaojukeji.com/nuwa/trace"
)

type GetMultiEtpEtdInfo struct {
	data *models.BaseReqData
	res  map[int32]*AthenaApiv3.EstimateEtpEtdInfo
}

func NewGetMultiEtpEtdInfo(data *models.BaseReqData) *GetMultiEtpEtdInfo {
	return &GetMultiEtpEtdInfo{
		data: data,
		res:  make(map[int32]*AthenaApiv3.EstimateEtpEtdInfo),
	}
}

func (gmi *GetMultiEtpEtdInfo) Fetch(ctx context.Context, products []*biz_runtime.ProductInfoFull) bool {
	req := buildGetMultiEstimateEtpEtdInfoReq(gmi.data, products)
	if req == nil {
		return false
	}

	resp := athena.GetMultiEstimateEtpEtaInfo(ctx, req)
	if resp == nil {
		return false
	}

	for i := 0; i < len(resp.EstimateEtpEtdInfo); i++ {
		if resp.EstimateEtpEtdInfo[i].ErrNo != 0 {
			log.Trace.Infof(ctx, Trace.DLTagUndefined, "GetMultiEstimateEtpEtaInfo fail with errno %v and errmsg %v", resp.EstimateEtpEtdInfo[i].ErrNo, resp.EstimateEtpEtdInfo[i].ErrMsg)
			continue
		}
		if resp.EstimateEtpEtdInfo[i].EstimateInfoProductItem != nil {
			gmi.res[resp.EstimateEtpEtdInfo[i].EstimateInfoProductItem.ProductCategory] = resp.EstimateEtpEtdInfo[i]
		}
	}
	return true
}

func (gmi *GetMultiEtpEtdInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

func (gmi *GetMultiEtpEtdInfo) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if tmp, ok := gmi.res[int32(product.Product.ProductCategory)]; ok {
		info.AthenaEstimateEtpEtdInfo = tmp
	}
	return
}

func buildGetMultiEstimateEtpEtdInfoReq(data *models.BaseReqData, products []*biz_runtime.ProductInfoFull) *AthenaApiv3.AthenaMultiEstimateEtpEtdInfoReq {
	if len(products) == 0 {
		return nil
	}

	res := AthenaApiv3.AthenaMultiEstimateEtpEtdInfoReq{
		Pid:        strconv.FormatInt(data.PassengerInfo.PID, 10),
		Phone:      data.PassengerInfo.Phone,
		CityId:     data.AreaInfo.City,
		AppVersion: data.CommonInfo.AppVersion,
		ClientType: strconv.FormatInt(int64(data.CommonInfo.ClientType), 10),
		Channel:    data.CommonInfo.Channel,
		Lang:       data.CommonInfo.Lang,
		FLat:       data.AreaInfo.FromLat,
		FLng:       data.AreaInfo.FromLng,
		TLat:       data.AreaInfo.ToLat,
		TLng:       data.AreaInfo.ToLng,
		FromName:   &data.AreaInfo.FromName,
		ToName:     &data.AreaInfo.ToName,
	}
	estimateProducts := make([]*AthenaApiv3.EstimateInfoProductItem, len(products))
	for i := 0; i < len(products); i++ {
		estimateProducts[i] = &AthenaApiv3.EstimateInfoProductItem{
			BubbleId:        products[i].Product.EstimateID,
			BusinessId:      int32(products[i].Product.BusinessID),
			RequireLevel:    int32(util.ToInt(products[i].Product.RequireLevel)),
			ComboType:       int32(products[i].Product.ComboType),
			ProductCategory: int32(products[i].Product.ProductCategory),
			LevelType:       products[i].Product.LevelType,
			CarpoolType:     int32(products[i].Product.CarpoolType),
			IsSpecialPrice:  products[i].Product.IsSpecialPrice,
		}
	}
	res.EstimateInfoProducts = estimateProducts
	return &res
}
