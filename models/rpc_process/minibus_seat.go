package rpc_process

import (
	"context"
	"runtime"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Carpool "git.xiaojukeji.com/dirpc/dirpc-go-http-Carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/seat"
)

var (
	defaultSeatNumConf = &Carpool.SeatResult{
		PassengerCountOption: []int32{1, 2},
		MaxSeatNum:           3,
		MaxOrderNum:          3,
	}
)

type MiniBusSeatInfo struct {
	seatInfo *Carpool.SeatResult
	req      *Carpool.PGetSeatNumReq
}

func NewMiniBusSeatRPC(baseReq *models.BaseReqData) *MiniBusSeatInfo {
	return &MiniBusSeatInfo{
		req: &Carpool.PGetSeatNumReq{
			Area:        &baseReq.AreaInfo.City,
			CountyId:    &baseReq.AreaInfo.FromCounty,
			CarpoolType: &baseReq.AreaInfo.City,
		},
	}
}

// 获取rpc错误信息
func (m *MiniBusSeatInfo) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (c *MiniBusSeatInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.SeatNumInfo = c.seatInfo
}

func (c *MiniBusSeatInfo) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}

func (c *MiniBusSeatInfo) Fetch(ctx context.Context, products []*models.Product) bool {
	defer func() {
		if res := recover(); res != nil {
			var buf [100000]byte
			n := runtime.Stack(buf[:], false)
			log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "minibus seat info fetch fail: %s", string(buf[:n]))
			return
		}
	}()

	productRes := make([]*models.Product, 0)

	for _, product := range products {
		if product == nil {
			continue
		}
		if carpool.IsMiniBus(int(product.CarpoolType)) {
			productRes = append(productRes, product)
		}
	}

	if len(productRes) == 0 {
		return true
	}

	status := c.fetchSeatInfo(ctx, products)

	if !status {
		c.seatInfo = defaultSeatNumConf
	}

	return true
}

func (c *MiniBusSeatInfo) initSeatReq(products []*models.Product) *Carpool.PGetSeatNumReq {
	carpoolType := int32(products[0].CarpoolType)
	c.req.CarpoolType = &carpoolType
	return c.req
}

func (c *MiniBusSeatInfo) fetchSeatInfo(ctx context.Context, products []*models.Product) bool {
	const (
		LogTag = "minibus_seat"
	)

	resp, err := seat.GetSeatInfo(ctx, c.initSeatReq(products))

	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "fetchSeatInfo failed %s", err)
		return false
	}

	if resp == nil || resp.Result == nil {
		log.Trace.Warnf(ctx, LogTag, "fetchSeatInfo got empty")
		return false
	}
	if resp.Errno != "0" {
		log.Trace.Warnf(ctx, LogTag, "fetchSeatInfo failed (%v) %s", resp.Errno, resp.Errmsg)
		return false
	}

	c.seatInfo = resp.Result

	return true
}
