package rpc_process

import (
	"context"
	"runtime"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const ErrPreMatchNoMiniBusRes = 59999
const ErrPreMatchRouteNotMatch = 60000    // 不顺路
const ErrPreMatchStartDestOverlap = 60001 // 起终点过近 站点集合有重合

// 小巴预匹配
type MiniBusPreMatchRPC struct {
	CarpoolEtx
}

// 获取rpc错误信息
func (m *MiniBusPreMatchRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

func NewMiniBusPreMatchRPC(baseReq *models.BaseReqData) *MiniBusPreMatchRPC {
	return &MiniBusPreMatchRPC{
		CarpoolEtx: *NewCarpoolEtx(baseReq, NotSSync),
	}
}

func (c *MiniBusPreMatchRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

	info.TokenInfo = c.tokenInfo
}

func (c *MiniBusPreMatchRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	info.MiniBusPreMatch = c.miniBusRes[product.LevelType]
}

func (c *MiniBusPreMatchRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	defer func() {
		if res := recover(); res != nil {
			var buf [100000]byte
			n := runtime.Stack(buf[:], false)
			log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "minibus prematch info fetch fail: %s", string(buf[:n]))
			return
		}
	}()

	productRes := make([]*models.Product, 0)

	for _, product := range products {
		if product == nil {
			continue
		}
		if carpool.IsMiniBus(int(product.CarpoolType)) {
			productRes = append(productRes, product)
		}
	}

	if len(productRes) == 0 {
		return true
	}

	status := c.CarpoolEtx.fetchEtxInfo(ctx, productRes, 0)

	if !status {
		return false
	}

	return true
}
