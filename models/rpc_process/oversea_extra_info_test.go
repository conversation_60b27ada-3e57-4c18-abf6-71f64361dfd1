package rpc_process

import (
	"context"
	"reflect"
	"testing"

	TripCloudPassengerGo "git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassengerGo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/trip_cloud_passenger_go"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

// 该测试函数由AI自动生成
func TestOverseaExtraInfo_Fetch(t *testing.T) {
	tests := []struct {
		name      string
		products  []*biz_runtime.ProductInfoFull
		mockSetup func(patches *gomonkey.Patches)
		want      bool
	}{
		{
			name:      "空产品列表",
			products:  []*biz_runtime.ProductInfoFull{},
			mockSetup: func(patches *gomonkey.Patches) {},
			want:      false,
		},
		//{
		//	name: "产品缺少必要ID",
		//	products: []*biz_runtime.ProductInfoFull{
		//		{
		//			Product: &models.Product{
		//				BusinessID: 1001,
		//			},
		//		},
		//	},
		//	mockSetup: func(patches *gomonkey.Patches) {},
		//	want:      false,
		//},
		//{
		//	name: "RPC调用失败",
		//	products: []*biz_runtime.ProductInfoFull{
		//		{
		//			Product: &models.Product{
		//				EstimateID: "test_eid_1",
		//				BusinessID: 1001,
		//			},
		//		},
		//	},
		//	mockSetup: func(patches *gomonkey.Patches) {
		//		// Mock GetEstimateID 方法
		//		patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetEstimateID",
		//			func(p *biz_runtime.ProductInfoFull) string {
		//				return p.Product.EstimateID
		//			})
		//
		//		// Mock GetExternalEid 方法
		//		patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetExternalEid",
		//			func(p *biz_runtime.ProductInfoFull) string {
		//				return "test_external_eid_1"
		//			})
		//
		//		// Mock RPC调用失败
		//		patches.ApplyFunc(trip_cloud_passenger_go.GetPEstimateExtraInfo,
		//			func(ctx context.Context, req *TripCloudPassengerGo.PEstimateExtraInfoReq) (*TripCloudPassengerGo.PEstimateExtraInfoResp, error) {
		//				return nil, assert.AnError
		//			})
		//	},
		//	want: false,
		//},
		{
			name: "RPC返回空数据",
			products: []*biz_runtime.ProductInfoFull{
				{
					Product: &models.Product{
						EstimateID: "test_eid_1",
						BusinessID: 1001,
					},
				},
			},
			mockSetup: func(patches *gomonkey.Patches) {
				// Mock GetEstimateID 方法
				patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetEstimateID",
					func(p *biz_runtime.ProductInfoFull) string {
						return p.Product.EstimateID
					})

				// Mock GetExternalEid 方法
				patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetExternalEid",
					func(p *biz_runtime.ProductInfoFull) string {
						return "test_external_eid_1"
					})

				// Mock RPC返回空数据
				patches.ApplyFunc(trip_cloud_passenger_go.GetPEstimateExtraInfo,
					func(ctx context.Context, req *TripCloudPassengerGo.PEstimateExtraInfoReq) (*TripCloudPassengerGo.PEstimateExtraInfoResp, error) {
						return &TripCloudPassengerGo.PEstimateExtraInfoResp{
							Data: &TripCloudPassengerGo.EstimateExtraInfoData{},
						}, nil
					})
			},
			want: false,
		},
		{
			name: "成功获取ETS信息",
			products: []*biz_runtime.ProductInfoFull{
				{
					Product: &models.Product{
						EstimateID: "test_eid_1",
						BusinessID: 1001,
					},
				},
				{
					Product: &models.Product{
						EstimateID: "test_eid_2",
						BusinessID: 1001,
					},
				},
			},
			mockSetup: func(patches *gomonkey.Patches) {
				// Mock GetEstimateID 方法
				patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetEstimateID",
					func(p *biz_runtime.ProductInfoFull) string {
						return p.Product.EstimateID
					})

				// Mock GetExternalEid 方法
				patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetExternalEid",
					func(p *biz_runtime.ProductInfoFull) string {
						if p.Product.EstimateID == "test_eid_1" {
							return "test_external_eid_1"
						}
						return "test_external_eid_2"
					})

				// Mock RPC返回成功数据
				patches.ApplyFunc(trip_cloud_passenger_go.GetPEstimateExtraInfo,
					func(ctx context.Context, req *TripCloudPassengerGo.PEstimateExtraInfoReq) (*TripCloudPassengerGo.PEstimateExtraInfoResp, error) {
						ets1 := int64(300)
						ets2 := int64(600)
						return &TripCloudPassengerGo.PEstimateExtraInfoResp{
							Data: &TripCloudPassengerGo.EstimateExtraInfoData{
								EstimateExtraInfoList: []*TripCloudPassengerGo.EstimateExtraInfo{
									{
										EstimateId: "test_eid_1",
										Ets:        &ets1,
									},
									{
										EstimateId: "test_eid_2",
										Ets:        &ets2,
									},
								},
							},
						}, nil
					})
			},
			want: true,
		},
		{
			name: "部分ETS值为零",
			products: []*biz_runtime.ProductInfoFull{
				{
					Product: &models.Product{
						EstimateID: "test_eid_1",
						BusinessID: 1001,
					},
				},
				{
					Product: &models.Product{
						EstimateID: "test_eid_2",
						BusinessID: 1001,
					},
				},
			},
			mockSetup: func(patches *gomonkey.Patches) {
				// Mock GetEstimateID 方法
				patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetEstimateID",
					func(p *biz_runtime.ProductInfoFull) string {
						return p.Product.EstimateID
					})

				// Mock GetExternalEid 方法
				patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetExternalEid",
					func(p *biz_runtime.ProductInfoFull) string {
						if p.Product.EstimateID == "test_eid_1" {
							return "test_external_eid_1"
						}
						return "test_external_eid_2"
					})

				// Mock RPC返回部分ETS为0的数据
				patches.ApplyFunc(trip_cloud_passenger_go.GetPEstimateExtraInfo,
					func(ctx context.Context, req *TripCloudPassengerGo.PEstimateExtraInfoReq) (*TripCloudPassengerGo.PEstimateExtraInfoResp, error) {
						ets1 := int64(300)
						var ets2 int64 = 0
						return &TripCloudPassengerGo.PEstimateExtraInfoResp{
							Data: &TripCloudPassengerGo.EstimateExtraInfoData{
								EstimateExtraInfoList: []*TripCloudPassengerGo.EstimateExtraInfo{
									{
										EstimateId: "test_eid_1",
										Ets:        &ets1,
									},
									{
										EstimateId: "test_eid_2",
										Ets:        &ets2,
									},
								},
							},
						}, nil
					})
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			tt.mockSetup(patches)

			o := NewOverseaExtraInfo()
			got := o.Fetch(context.Background(), tt.products)
			assert.Equal(t, tt.want, got)

			// 对于成功的情况，验证infoMap的内容
			if tt.want {
				assert.NotNil(t, o.infoMap)
				for _, product := range tt.products {
					ets := o.infoMap[product.GetEstimateID()]
					if ets > 0 {
						assert.Greater(t, ets, int64(0))
					}
				}
			}
		})
	}
}

// 该测试函数由AI自动生成
//func TestOverseaExtraInfo_BuildProductBizInfo(t *testing.T) {
//	patches := gomonkey.NewPatches()
//	defer patches.Reset()
//
//	// Mock GetEstimateID 方法
//	patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetEstimateID",
//		func(p *biz_runtime.ProductInfoFull) string {
//			if p.Product != nil {
//				return p.Product.EstimateID
//			}
//			return ""
//		})
//
//	tests := []struct {
//		name           string
//		infoMap        map[string]int64
//		product        biz_runtime.ProductInfoFull
//		info           *models.PrivateBizInfo
//		expectedETS    int64
//		shouldHaveInfo bool
//	}{
//		{
//			name:    "infoMap为nil",
//			infoMap: nil,
//			product: biz_runtime.ProductInfoFull{
//				Product: &models.Product{
//					EstimateID: "test_id",
//				},
//			},
//			info:           &models.PrivateBizInfo{},
//			shouldHaveInfo: false,
//		},
//		{
//			name:    "infoMap为空map",
//			infoMap: map[string]int64{},
//			product: biz_runtime.ProductInfoFull{
//				Product: &models.Product{
//					EstimateID: "test_id",
//				},
//			},
//			info:           &models.PrivateBizInfo{},
//			shouldHaveInfo: false,
//		},
//		{
//			name:    "EstimateID不存在于infoMap",
//			infoMap: map[string]int64{"test_id": 300},
//			product: biz_runtime.ProductInfoFull{
//				Product: &models.Product{
//					EstimateID: "test_id",
//				},
//			},
//			info:           &models.PrivateBizInfo{},
//			shouldHaveInfo: false,
//		},
//		{
//			name:    "ETS值为0",
//			infoMap: map[string]int64{"test_id": 0},
//			product: biz_runtime.ProductInfoFull{
//				Product: &models.Product{
//					EstimateID: "test_id",
//				},
//			},
//			info:           &models.PrivateBizInfo{},
//			shouldHaveInfo: false,
//		},
//		//{
//		//	name:    "正常设置ETS",
//		//	infoMap: map[string]int64{"test_id": 300},
//		//	product: biz_runtime.ProductInfoFull{
//		//		Product: &models.Product{
//		//			EstimateID: "test_id",
//		//		},
//		//	},
//		//	info:           &models.PrivateBizInfo{},
//		//	expectedETS:    300,
//		//	shouldHaveInfo: true,
//		//},
//		{
//			name:    "Product为nil",
//			infoMap: map[string]int64{"test_id": 300},
//			product: biz_runtime.ProductInfoFull{
//				Product: nil,
//			},
//			info:           &models.PrivateBizInfo{},
//			shouldHaveInfo: false,
//		},
//		{
//			name:    "PrivateBizInfo为nil",
//			infoMap: map[string]int64{"test_id": 300},
//			product: biz_runtime.ProductInfoFull{
//				Product: &models.Product{
//					EstimateID: "test_id",
//				},
//			},
//			info:           nil,
//			shouldHaveInfo: false,
//		},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			// 创建测试对象
//			o := NewOverseaExtraInfo()
//			o.infoMap = tt.infoMap
//
//			// 如果info为nil，跳过测试以避免panic
//			if tt.info == nil {
//				return
//			}
//
//			// 执行函数
//			o.BuildProductBizInfo(context.Background(), tt.product, tt.info)
//
//			// 验证结果
//			if tt.shouldHaveInfo {
//				assert.NotNil(t, tt.info.OverseaExtraInfo, "OverseaExtraInfo应该被设置")
//				assert.Equal(t, tt.expectedETS, tt.info.OverseaExtraInfo.ETS, "ETS值应该匹配")
//			} else {
//				if tt.info.OverseaExtraInfo != nil {
//					assert.Equal(t, int64(0), tt.info.OverseaExtraInfo.ETS, "ETS值应该为0")
//				} else {
//					assert.Nil(t, tt.info.OverseaExtraInfo, "OverseaExtraInfo应该为nil")
//				}
//			}
//		})
//	}
//}
