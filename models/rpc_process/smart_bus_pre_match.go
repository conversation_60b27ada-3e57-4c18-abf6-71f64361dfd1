package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"runtime"
)

// SmartBusPreMatchRPC 智能小巴预匹配
type SmartBusPreMatchRPC struct {
	CarpoolEtx
}

// GetErrorInfo 获取rpc错误信息
func (s *SmartBusPreMatchRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

func NewSmartBusPreMatchRPC(baseReq *models.BaseReqData) *SmartBusPreMatchRPC {
	return &SmartBusPreMatchRPC{
		CarpoolEtx: *NewCarpoolEtx(baseReq, NotSSync),
	}
}

func (s *SmartBusPreMatchRPC) BuildCommonBizInfo(_ context.Context, info *models.CommonBizInfo) {
	info.TokenInfo = s.tokenInfo
	info.PreMatchTPErrNo = s.preMatchTPErrNo
}

func (s *SmartBusPreMatchRPC) BuildProductBizInfo(_ context.Context, product models.Product, info *models.PrivateBizInfo) {
	info.SmartBusPreMatch = s.miniBusRes[product.LevelType]
}

func (s *SmartBusPreMatchRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	defer func() {
		if res := recover(); res != nil {
			var buf [100000]byte
			n := runtime.Stack(buf[:], false)
			log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "smart bus prematch info fetch fail: %s", string(buf[:n]))
			return
		}
	}()

	productRes := make([]*models.Product, 0)
	for _, product := range products {
		if product == nil {
			continue
		}
		if carpool.IsSmartBus(int(product.CarpoolType)) {
			productRes = append(productRes, product)
		}
	}
	if len(productRes) == 0 {
		return false
	}

	status := s.CarpoolEtx.fetchEtxInfo(ctx, productRes, 0)
	if !status {
		return false
	}
	return true
}
