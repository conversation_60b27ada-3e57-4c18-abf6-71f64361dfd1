package rpc_process

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
)

// BatchRouterDetailRPC 站点巴士批量通过路线获取站点
type BatchInsteadOrderRouterDetailRPC struct {
	baseReq *models.BaseReqData

	routeDetailList map[int32]*models.RouteDetail
}

// NewBatchRouteDetail 站点巴士批量通过路线获取站点
func NewInsteadOrderBatchRouteDetail(baseReq *models.BaseReqData) *BatchInsteadOrderRouterDetailRPC {
	return &BatchInsteadOrderRouterDetailRPC{
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (m *BatchInsteadOrderRouterDetailRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (rd *BatchInsteadOrderRouterDetailRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	//设置用户座位数和携童座位数

	if rd.routeDetailList != nil {
		info.RouteDetailV2 = rd.routeDetailList[int32(product.BizInfo.ComboID)]
		fromIndex := 0
		toIndex := len(info.RouteDetailV2.RouteBasicInfo.StationList) - 1
		for k, v := range info.RouteDetailV2.RouteBasicInfo.StationList {
			if int(v.StationId) == info.StationInventoryInfo.SelectInfo.FromStationId {
				info.StationInventoryInfo.SelectInfo.FromStationInfo = v
				fromIndex = k
			}
			if int(v.StationId) == info.StationInventoryInfo.SelectInfo.DestStationId {
				info.StationInventoryInfo.SelectInfo.DestStationInfo = v
				toIndex = k
			}
			if v.StationType == "3" {
				info.HasPickAndDropStation = true
			}
		}
		info.StationInventoryInfo.StationList = info.RouteDetailV2.RouteBasicInfo.StationList[fromIndex : toIndex+1]

	}
}

func (rd *BatchInsteadOrderRouterDetailRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

// Fetch 获取/加载
func (rd *BatchInsteadOrderRouterDetailRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	routeIdList := []string{}

	for _, product := range products {
		if product == nil {
			continue
		}
		if product.BizInfo != nil && product.BizInfo.ComboID != 0 {
			id := strconv.Itoa(int(product.BizInfo.ComboID))
			if !util.InArrayStr(id, routeIdList) {
				routeIdList = append(routeIdList, id)
			}

		}
	}

	if len(routeIdList) == 0 {
		return true
	}
	routeIdListStr, _ := json.Marshal(routeIdList)

	req := &Prfs.BatchRouteDetailReq{
		RouteIdList: string(routeIdListStr),
		CallerScene: PRFS_CALLER_SCENE,
		StartCityId: rd.baseReq.AreaInfo.City,
		CarpoolType: consts.CarPoolTypeInterCityStation,
	}

	resp := prfs.GetBatchRouteDetail(ctx, req)
	if resp == nil || len(resp.Data) <= 0 {
		return false
	}
	rd.routeDetailList = make(map[int32]*models.RouteDetail)
	for key, data := range resp.Data {
		routeDetailInfo := models.RouteDetail(*data)
		rd.routeDetailList[key] = &routeDetailInfo
	}
	return true
}
