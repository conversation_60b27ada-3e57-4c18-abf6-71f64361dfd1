package rpc_process

import (
	"context"
	"strconv"

	dirpcSdkBrick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/brick"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

type ChildTicketRuleRPC struct {
	baseReq *models.BaseReqData

	ruleData *dirpcSdkBrick.RuleData
}

func NewGetChildTicketRuleRPC(baseReq *models.BaseReqData) *ChildTicketRuleRPC {
	return &ChildTicketRuleRPC{
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (m *ChildTicketRuleRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (c *ChildTicketRuleRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		isCall     bool
		routeGroup int64
		businessID int64
		comboID    int64
		busShiftId string
	)

	if c.baseReq == nil {
		return false
	}

	if !apollo.FeatureToggle(ctx, "gs_child_ticket_switch", strconv.Itoa(int(c.baseReq.PassengerInfo.UID)), c.baseReq.GetApolloParam()) {
		return true
	}

	baseData := carpool.BaseData{
		City:        int(c.baseReq.AreaInfo.City),
		Phone:       c.baseReq.PassengerInfo.Phone,
		AccessKeyID: int(c.baseReq.CommonInfo.AccessKeyID),
	}

	for _, product := range products {
		if product == nil {
			continue
		}

		if product.BizInfo != nil && product.BizInfo.RouteInfo != nil && product.BizInfo.RouteInfo.RouteGroup != nil {
			routeGroup = *product.BizInfo.RouteInfo.RouteGroup
		}

		if carpool.IsIntercityStation(ctx, int(product.CarpoolType)) {
			isCall = true
			businessID = product.BusinessID
			if product.BizInfo != nil && product.BizInfo.StationInventoryInfo != nil {
				routeGroup = product.BizInfo.StationInventoryInfo.SelectInfo.RouteId
				busShiftId = product.BizInfo.StationInventoryInfo.SelectInfo.ShiftID
				comboID = routeGroup
			}

			break
		} else if routeGroup != 0 && carpool.IsIntercityThirdPID(ctx, int(product.ProductID)) && carpool.IsSkuModelNoSelf(baseData, int(routeGroup)) {
			isCall = true
			businessID = product.BusinessID
			if product.BizInfo != nil && product.BizInfo.RouteInfo != nil && product.BizInfo.RouteInfo.RouteId != nil {
				comboID = *product.BizInfo.RouteInfo.RouteId
			}

			break
		}
	}

	if isCall {

		//resp, err := trip_cloud_passenger.GetChildTicketRule(ctx, &TripCloudPassenger.GetCarpoolIntercityRuleRequest{
		//	RuleType:          ChildrenTicketRule,
		//	BusServiceShiftId: nil,
		//	BusinessId:        &businessID,
		//	ComboId:           &comboID,
		//})
		resp, err := brick.GetBusTicketRuleInfo(ctx, &dirpcSdkBrick.GetBusTicketRuleInfoReq{
			RuleType:          "3", // 获取票规则
			BusServiceShiftId: &busShiftId,
			BusinessId:        &businessID,
			ComboId:           &comboID,
			Phone:             &c.baseReq.PassengerInfo.Phone,
		})
		if err != nil {
			log.Trace.Warnf(ctx, "Fetch", "GetBusTicketRuleInfo fail, err:%v", err)
		}

		if resp != nil && resp.RuleInfo != nil {
			c.ruleData = resp.RuleInfo
		}
	}

	return true
}

func (c *ChildTicketRuleRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.RuleData = c.ruleData
}

func (c *ChildTicketRuleRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {

}
