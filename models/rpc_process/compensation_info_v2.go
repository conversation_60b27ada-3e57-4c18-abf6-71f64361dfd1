package rpc_process

import (
	"context"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/client_type"

	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type CompensationInfoV2 struct {
	compensationReq *Compensation.GetMultiCompensationAbilityV2Req
	resp            *Compensation.GetMultiCompensationAbilityData
}

func NewCompensationInfoRpcV2(commonInfo *models.CommonInfo, areaInfo *models.AreaInfo, passengerInfo *models.PassengerInfo,
	sendOrder *models.SendOrder, compensationBusiness string) *CompensationInfoV2 {
	req := &Compensation.GetMultiCompensationAbilityV2Req{
		Caller:                 getStringPtr("mamba"),
		From:                   "mamba",
		CompensationBusinesses: &compensationBusiness,
		OrderId:                util.StringPtr(util.ToString(sendOrder.OrderIdLow)),
		District:               &areaInfo.District,
		PassengerId:            getStringPtr(strconv.FormatInt(passengerInfo.PID, 10)),
		PassengerPhone:         &passengerInfo.Phone,
		CallCarType:            &commonInfo.CallCarType,
		FromWebapp:             getIntPtrFromBool(client_type.IsFromWebApp(commonInfo.ClientType)),
		AccessKeyId:            &commonInfo.AccessKeyID,
		ClientType:             &commonInfo.ClientType,
		AppVersion:             &commonInfo.AppVersion,
		Area:                   getStringPtr(cast.ToString(areaInfo.Area)),
		ToArea:                 getStringPtr(cast.ToString(areaInfo.ToArea)),
		FromLat:                getStringPtr(cast.ToString(areaInfo.FromLat)),
		FromLng:                getStringPtr(cast.ToString(areaInfo.FromLng)),
		ToLat:                  getStringPtr(cast.ToString(areaInfo.ToLat)),
		ToLng:                  getStringPtr(cast.ToString(areaInfo.ToLng)),
		CityId:                 util.Int32Ptr(areaInfo.City),
		Channel:                util.Int32Ptr(int32(commonInfo.Channel)),
		PrivilegeSource:        getStringPtr("not_member"),
	}

	return &CompensationInfoV2{req, nil}
}

func (rp *CompensationInfoV2) Fetch(ctx context.Context, productInfos []*biz_runtime.ProductInfoFull) bool {
	var (
		airportType        int32
		productCategoryIds = make([]string, 0)
		productEstimateIds = make([]string, 0)
		estimateItemList   = make([]*Compensation.EstimateItem, 0)
		notNeedFetch       = false
	)

	for _, p := range productInfos {

		// xinzhu场景过滤
		if p.BaseReqData != nil && util.IsFromXinZhuWithBusinessTravelOrderType(p.BaseReqData.CommonBizInfo.BusinessTravelOrderType) {
			notNeedFetch = true
			break
		}

		if airportType == 0 {
			airportType = p.Product.AirportType
		}
		productCategoryIds = append(productCategoryIds, strconv.FormatInt(p.Product.ProductCategory, 10))
		productEstimateIds = append(productEstimateIds, p.Product.EstimateID)

		requireLevel := int32(cast.ToInt(p.GetRequireLevel()))
		ComboType := int32(p.GetComboType())
		estimateItemList = append(estimateItemList, &Compensation.EstimateItem{
			ProductCategory: p.Product.ProductCategory,
			DynamicTotalFee: p.GetDynamicTotalFee(),
			EstimateFee:     p.GetEstimateFee(),
			RequireLevel:    &requireLevel,
			ComboType:       &ComboType,
			BusinessId:      &p.Product.BusinessID,
			LevelType:       &p.Product.LevelType,
			CarpoolType:     &p.Product.CarpoolType,
			IsSpecialPrice:  nil,
			ProductId:       &p.Product.ProductID,
			EstimateId:      p.GetEstimateID(),
		})
	}

	if notNeedFetch {
		return false
	}

	rp.compensationReq.AirportType = &airportType
	rp.compensationReq.ProductCategoryIds = getStringPtr(strings.Join(productCategoryIds, ","))
	rp.compensationReq.EstimateIds = getStringPtr(strings.Join(productEstimateIds, ","))

	rp.compensationReq.EstimateProductList = estimateItemList

	resp := compensation.GetMultiCompensationAbilityV2(ctx, rp.compensationReq)
	if resp == nil {
		return false
	}

	if resp.Errno != 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get compensation info error with errno %d", resp.Errno)
		return false
	}

	rp.resp = resp.Data
	return true
}

func (rp *CompensationInfoV2) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if rp.resp == nil {
		return
	}

	if info != nil {
		if rp.resp.CompensationAbilityCard != nil {
			info.NoAnswerCompensationData = rp.resp.CompensationAbilityCard.HitNoAnswerActivityList
		}
	}
}

func (rp *CompensationInfoV2) BuildProductBizInfo(ctx context.Context, productInfo biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if rp.resp != nil {
		pcId := strconv.FormatInt(productInfo.Product.ProductCategory, 10)
		if cInfo, exsit := rp.resp.CompensationAbilityLabel[pcId]; exsit && cInfo != nil {
			info.CompensationInfo = cInfo
		}
	}
}
