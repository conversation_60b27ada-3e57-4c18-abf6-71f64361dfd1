package rpc_process

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"strconv"

	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type UfsAuthFeature struct {
	Domain        string
	PassengerID   string
	AuthKey       string
	VideoKey      string
	ButianAuthKey string

	RemoveTripCloud []models.ProductCategory
	// FinalMapProductCategory map[int64]bool
	FinalMapBusinessID        map[int64]bool
	isButianTripCloud         bool // 是否有补天品类需要判断三方授权
	isButianTripCloudNeedAuth bool // 是否有补天品类需要真的去授权
}

func NewUserAuthRPC(ctx context.Context, domain string, pid string) *UfsAuthFeature {

	return &UfsAuthFeature{
		Domain:             domain,
		PassengerID:        pid,
		AuthKey:            ufs.TripcloudAuthKey,
		VideoKey:           ufs.VideoAuthKey,
		ButianAuthKey:      ufs.ButianAuthKey, // 新增ufs key
		RemoveTripCloud:    make([]models.ProductCategory, 0),
		FinalMapBusinessID: make(map[int64]bool, 0),
	}
}

func (uf *UfsAuthFeature) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	var (
		// 三方车型
		allTripCloud = make([]models.ProductCategory, 0) // 虽然名字叫tripcloud，但实际上有些场景加了个补天品类
		searchMap    = make(map[int64][]models.ProductCategory, 0)
		// UFSFeatures UFS特征
		UFSFeatures = make([]ufsClient.Feature, 0)
		// AuthBusiness VideoBusiness key：businessID，value：是否授权
		AuthBusiness   = make(map[int64]bool, 0)
		VideoBusiness  = make(map[int64]bool, 0)
		UfsVideoResult []int64
	)

	for _, p := range productFulls {
		if p.Product.IsTripcloudProduct(ctx) {
			allTripCloud = append(allTripCloud, models.ProductCategory(p.GetProductCategory()))
			searchMap[p.GetBusinessID()] = append(searchMap[p.GetBusinessID()], models.ProductCategory(p.GetProductCategory()))
		}

		// 获得补天是否授权
		if p.GetProductCategory() == product_category.ProductCategoryFastTaxi {
			// 没有在apollo里面配补天的标识，是因为在后续business_id链路共用apollo时是拿不到补天标识的
			apolloParams := map[string]string{
				"city": strconv.FormatInt(int64(p.BaseReqData.AreaInfo.City), 10),
				"pid":  strconv.Itoa(int(p.BaseReqData.PassengerInfo.PID)),
			}

			// 开量开关-正常先关掉。如果过了城市放量
			if apollo.FeatureToggle(ctx, "butian_tripcloud_toggle", strconv.Itoa(int(p.BaseReqData.PassengerInfo.PID)), apolloParams) {
				uf.isButianTripCloud = true
				allTripCloud = append(allTripCloud, models.ProductCategory(p.GetProductCategory())) // 实际上加了个补天三方，后面如果ufs获取失败啥的，要remove掉
			}
		}
	}

	// 构建用户未授权的三方feature列表
	for businessID := range searchMap {
		feature := ufsClient.Feature{
			Domain: uf.Domain,
			Keys:   []string{uf.AuthKey},
			Params: map[string]string{
				"passenger_id": uf.PassengerID,
				"business_id":  strconv.FormatInt(businessID, 10),
			},
		}
		UFSFeatures = append(UFSFeatures, feature)
	}

	// 获得用户录音录像授权 feature
	feature := ufsClient.Feature{
		Domain: uf.Domain,
		Keys:   []string{uf.VideoKey},
		Params: map[string]string{
			"passenger_id": uf.PassengerID,
		},
	}
	UFSFeatures = append(UFSFeatures, feature)

	// 如果有补天三方放量，那需要再去判断下是否授权过
	if uf.isButianTripCloud {
		feature := ufsClient.Feature{
			Domain: uf.Domain,
			Keys:   []string{uf.ButianAuthKey},
			Params: map[string]string{
				"passenger_id": uf.PassengerID,
			},
		}
		UFSFeatures = append(UFSFeatures, feature)
	}

	UFSResp, err := ufs.MultiGetFeatures(ctx, UFSFeatures, "")
	if err != nil {
		// 若获取三方授权和录音录像授权，若状态失败，则remove所有三方车型
		// log.Trace.Warnf(ctx, trace.DLTagUndefined, "get auth status from ufs fail with err %v", err)
		uf.RemoveTripCloud = allTripCloud
		return false
	}

	// 把UFS结果映射为map
	for strFeature, ufsResp := range UFSResp {
		_, key, params, featureErr := ufsClient.ParseFeature(strFeature)
		if featureErr != nil {
			uf.RemoveTripCloud = allTripCloud
			return false
		}

		if key == uf.AuthKey {
			// 三方品类授权状态
			businessID, authErr := strconv.ParseInt(params["business_id"], 10, 64)
			if authErr != nil || businessID == 0 {
				uf.RemoveTripCloud = allTripCloud
				return false
			}

			if ufsResp == nil || ufsResp.Errno != 0 || *ufsResp.Value != "1" {
				AuthBusiness[businessID] = true
			}
		} else if key == uf.VideoKey {
			// 录音录像授权状态
			if ufsResp == nil || ufsResp.Errno != 0 || *ufsResp.Value == "" {
				continue
			}

			videoErr := json.Unmarshal([]byte(*ufsResp.Value), &UfsVideoResult)
			if videoErr != nil {
				uf.RemoveTripCloud = allTripCloud
				return false
			}

			for _, businessID := range UfsVideoResult {
				VideoBusiness[businessID] = true
			}
		} else if key == uf.ButianAuthKey {
			// 如果结果没有，或者结果是用户授权过
			if ufsResp == nil || ufsResp.Errno != 0 || *ufsResp.Value == "1" {
				continue
			}

			// 如果ufs返回正常，且拿到的是默认的0
			uf.isButianTripCloudNeedAuth = true
		}

	}

	// 1表示授权过了
	// 保存未授权录音录像与三方协议的全部品类
	for _, p := range productFulls {
		if !p.Product.IsTripcloudProduct(ctx) {
			continue
		}

		_, isAuth := AuthBusiness[p.GetBusinessID()]
		if isAuth {
			uf.FinalMapBusinessID[p.GetBusinessID()] = true
			continue
		}

		_, isVideo := VideoBusiness[p.GetBusinessID()]
		if !isVideo { // 如果不是乘客允许授权过，就需要去授权
			uf.FinalMapBusinessID[p.GetBusinessID()] = true
		}
	}

	return true
}

func (uf *UfsAuthFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

func (uf *UfsAuthFeature) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	// 非三方品类和非补天不需要判定要不要授权
	if !product.Product.IsTripcloudProduct(ctx) && product.GetProductCategory() != product_category.ProductCategoryFastTaxi {
		return
	}

	if product.GetProductCategory() == product_category.ProductCategoryFastTaxi {
		// 新ufs就代表用户维度，补天是否授权过。默认是1，需要授权。只有mvp放量的城市才需要去读该ufs。
		if uf.isButianTripCloudNeedAuth {
			info.IsButianNeedAuth = true
		}
	} else { // 走原三方逻辑
		_, IsNeed := uf.FinalMapBusinessID[product.GetBusinessID()]
		// 已授权的三方品类不需要绑定
		if !IsNeed {
			return
		}

		info.UfsTripCloudAuthBusinessID = uf.FinalMapBusinessID
	}

	return
}

func (uf *UfsAuthFeature) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) []models.ProductCategory {
	// 补天ufs读失败，应该不出，给remove掉
	return uf.RemoveTripCloud
}
