package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car/charter_open_products"
	"git.xiaojukeji.com/nuwa/trace/v2"
)

type CharterOpenProduct struct {
	sourceID string
	token    string
	handler  *charter_open_products.CharterOpenProductHandler
}

func NewCharterOpenProduct(sourceID string, token string) *CharterOpenProduct {
	return &CharterOpenProduct{
		sourceID: sourceID,
		token:    token,
	}
}

func (c *CharterOpenProduct) Fetch(ctx context.Context) bool {
	handler := charter_open_products.NewCharterOpenProductHandler(ctx).WithOpenStatusCheck()
	err := handler.Load("", []string{c.sourceID}, nil, c.token)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "CharterOpenProduct handler load fail: err=%s", err.Error())
		return false
	}

	handler.ExecCheck()
	c.handler = handler

	return true
}

func (c *CharterOpenProduct) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.CharterOpenProduct = c.handler
}
