package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/ticket_detail_consts"
	"runtime"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_open_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
)

const (
	PRFS_CALLER_SCENE = "mamba-estimate"
)

// StationInventory 大车班车站点模式路线推荐与库存查询
type StationInventory struct {
	area                     *models.AreaInfo
	passenger                *models.PassengerInfo
	seatNum                  int32
	departureTime            int64
	_stationInventoryInfo    map[int64]models.StationInventoryInfo
	pageType                 int32
	routeId                  int64
	startStationId           int64
	endStationId             int64
	seatDetailInfo           []*models.SeatDetailInfo
	routeDetailData          *Prfs.RouteDetailData
	isScanCode               string
	scanCodeShiftId          string
	openAppointmentRange     int64
	scene                    string
	seletedBusServiceShiftId string
}

// NewStationInventory 大车班车站点模式路线推荐与库存查询
func NewStationInventory(commonInfo models.CommonInfo, commonBizInfo *models.CommonBizInfo, areaInfo *models.AreaInfo, passengerInfo *models.PassengerInfo) *StationInventory {
	var seatNum int32 = 1
	if commonBizInfo != nil {
		if commonBizInfo.CarpoolSeatNum != 0 {
			seatNum = commonBizInfo.CarpoolSeatNum
		} else if commonBizInfo.PassengerCount != nil && *commonBizInfo.PassengerCount > 0 {
			seatNum = *commonBizInfo.PassengerCount
		}
	}
	return &StationInventory{
		area:                     areaInfo,
		passenger:                passengerInfo,
		seatNum:                  seatNum,
		departureTime:            commonInfo.DepartureTime,
		pageType:                 commonInfo.PageType,
		routeId:                  commonBizInfo.RouteId,
		startStationId:           commonBizInfo.StartStationId,
		endStationId:             commonBizInfo.EndStationId,
		seatDetailInfo:           commonBizInfo.SeatDetailInfo,
		isScanCode:               commonInfo.IsScanCode,
		scanCodeShiftId:          commonInfo.ScanCodeShiftId,
		openAppointmentRange:     commonBizInfo.OpenAppointmentRange,
		scene:                    commonBizInfo.Scene,
		seletedBusServiceShiftId: commonBizInfo.SeletedBusServiceShiftId,
	}
}

// BuildProductBizInfo 设置单产品信息
func (si *StationInventory) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	if si.routeDetailData != nil && si.routeDetailData.ProductId == strconv.Itoa(int(product.ProductID)) {
		info.RouteDetail = si.routeDetailData
	}

	if si._stationInventoryInfo == nil {
		return
	}
	if res, status := si._stationInventoryInfo[product.ProductID]; (!status) || res.RecommendStation == nil ||
		!res.RecommendStation.HitStationRoute || len(res.StationInventorys) == 0 {
		return
	} else {
		defaultInfo := models.StationInventorySelectInfo{}
		userInfo := models.StationInventorySelectInfo{}
		for _, v := range res.StationInventorys {
			if (v.RemainSeats > 0 && v.Status != 0 && defaultInfo.DepartureTime == 0) ||
				(si.isScanCode == "station_scan" && si.scanCodeShiftId == v.BusServiceShiftID) {
				defaultInfo.DepartureTime = v.DepartureTime
				defaultInfo.RemainSeats = v.RemainSeats
				defaultInfo.RouteId = res.RecommendStation.RouteGroup
				defaultInfo.SeatNum = si.seatNum
				defaultInfo.FromStationId = int(res.RecommendStation.SrcStationID)
				defaultInfo.DestStationId = int(res.RecommendStation.DestStationID)
				defaultInfo.IsFirst = true
				defaultInfo.ShiftID = v.BusServiceShiftID
				defaultInfo.ExtraInfo = v.ExtraInfo
			}
			if (si.scene != ticket_detail_consts.SceneRebook && si.departureTime != 0 && si.departureTime == v.DepartureTime && v.RemainSeats > 0 && v.Status != 0) ||
				(si.scene == ticket_detail_consts.SceneRebook && si.seletedBusServiceShiftId == v.BusServiceShiftID) {
				userInfo.DepartureTime = v.DepartureTime
				userInfo.RemainSeats = v.RemainSeats
				userInfo.RouteId = res.RecommendStation.RouteGroup
				userInfo.SeatNum = si.seatNum
				userInfo.FromStationId = int(res.RecommendStation.SrcStationID)
				userInfo.DestStationId = int(res.RecommendStation.DestStationID)
				defaultInfo.IsFirst = false
				userInfo.ShiftID = v.BusServiceShiftID
				userInfo.ExtraInfo = v.ExtraInfo
			}
		}
		info.StationInventoryInfo = &res
		if userInfo.RouteId == 0 || userInfo.DepartureTime == 0 {
			info.StationInventoryInfo.SelectInfo = defaultInfo
		} else {
			info.StationInventoryInfo.SelectInfo = userInfo
		}

		info.CarryChildrenIsOccupySeat = si.getCarryChildrenIsOccupySeat(&info.StationInventoryInfo.SelectInfo, info)

		if len(res.StationList) == 0 {
			return
		}
		stationMap := make(map[int32]struct {
			s     *Prfs.StationInfo
			index int
		})
		for k, v := range res.StationList {
			if v == nil {
				continue
			}
			stationMap[v.StationId] = struct {
				s     *Prfs.StationInfo
				index int
			}{s: v, index: k}
		}
		if res.SeatLimit != nil {
			res.SelectInfo.UserMaxSeatNum = res.SeatLimit.MaxPassengerCount
		}
		fromStationId := 0
		destStationId := len(res.StationList) - 1
		if res, status := stationMap[int32(info.StationInventoryInfo.SelectInfo.FromStationId)]; status {
			info.StationInventoryInfo.SelectInfo.FromStationInfo = res.s
			fromStationId = res.index
		}
		if res, status := stationMap[int32(info.StationInventoryInfo.SelectInfo.DestStationId)]; status {
			info.StationInventoryInfo.SelectInfo.DestStationInfo = res.s
			destStationId = res.index
		}
		res.StationList = res.StationList[:destStationId+1]
		res.SelectInfo.FromStationIndex = fromStationId
	}
}

func (si *StationInventory) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if si.routeDetailData != nil && si.routeDetailData.RouteExtendInfo != nil && si.routeDetailData.RouteExtendInfo.ChildOccupySeat != nil {
		info.CarryChildrenIsOccupySeat = util.Bool2I32(*si.routeDetailData.RouteExtendInfo.ChildOccupySeat)
	}
}

// Do 获取/加载
func (si *StationInventory) Fetch(ctx context.Context, products []*models.Product) bool {
	defer func() {
		if rec := recover(); rec != nil {
			var buf [100000]byte
			n := runtime.Stack(buf[:], false)
			log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "carpool station_inventory info fetch fail: %s", string(buf[:n]))
			return
		}
	}()

	productRes := make([]*models.Product, 0)

	for _, product := range products {
		if product == nil {
			continue
		}
		if carpool.IsIntercityStation(ctx, int(product.CarpoolType)) {
			productRes = append(productRes, product)
		}
	}

	if len(productRes) == 0 {
		return true
	}
	status := si.fetchStationInventoryInfo(ctx, productRes)
	if !status {
		return false
	}
	if len(si._stationInventoryInfo) == 0 {
		return false
	}
	// 获取 路线细节信息
	// 因为大巴站点模式需要在请求price-api之前就获取路线信息 来改写预估起终点经纬度 和 duse推荐路线又有前后依赖关系
	// 所以只能写一起了。。。
	var productId int64
	var routeId int64
	for k, v := range si._stationInventoryInfo {
		if k != 0 && v.RecommendStation != nil && len(v.StationInventorys) != 0 {
			productId = k
			routeId = v.RecommendStation.RouteGroup
			break
		}

	}

	if productId != 0 && routeId != 0 {
		stationInventoryInfo, status := si.fetchRouteDetail(ctx, &Prfs.RouteDetailReq{
			ProductId:   strconv.Itoa(int(productId)),
			RouteId:     strconv.Itoa(int(routeId)),
			CallerScene: PRFS_CALLER_SCENE,
			StartCityId: si.area.City,
			DestCityId:  si.area.ToArea,
			CarpoolType: int32(productRes[0].CarpoolType),
		}, routeId, productId)
		si._stationInventoryInfo[productId] = stationInventoryInfo
		return status
	}

	return false
}

func (si *StationInventory) fetchStationInventoryInfo(ctx context.Context, products []*models.Product) bool {
	const (
		LogTag = "carpool_station_inventory"
	)
	stb := carpool_open_api.NewPrematchRecommendRequestBuilder(si.openAppointmentRange).SetGeoInfo(
		&carpool_open_api.GeoInfo{
			CityID:     si.area.City,
			DestCityID: si.area.ToArea,
			StartLng:   si.area.FromLng,
			StartLat:   si.area.FromLat,
			DestLng:    si.area.ToLng,
			DestLat:    si.area.ToLat,
		}).SetPassengerInfo(strconv.FormatInt(si.passenger.PID, 10), si.seatNum).SetPageType(si.pageType)

	if si.pageType == page_type.PageTypeIntercityStationEstimate {
		stb.SetRouteInfo(si.routeId, si.startStationId, si.endStationId)
	}

	stb.SetSeatDetailInfo(si.seatDetailInfo)

	extraInfo := make(map[string]string)
	if si.isScanCode == "station_scan" {
		extraInfo["is_scan_code"] = "1"
	}
	extraInfo["scan_code_shift_id"] = si.scanCodeShiftId
	stb.SetExtraInfo(extraInfo)

	productIds := []int32{}
	for _, v := range products {
		productIds = append(productIds, int32(v.ProductID))
	}

	traceInfo := stb.SetProductIds(productIds).BuildCtx(ctx)
	resp, err := carpool_open_api.GetQueryStationAndInventory(ctx, stb.Build(), traceInfo)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "fetch carpool_station_inventory info failed %s", err)
		return false
	}

	if resp == nil {
		log.Trace.Warnf(ctx, LogTag, "fetch carpool_station_inventory info got empty")
		return false
	}
	if resp.GetRetCode() != 0 {
		log.Trace.Warnf(ctx, LogTag, "fetch carpool_station_inventory info failed(%d) %s", resp.GetRetCode(), resp.GetRetMsg())
		return false
	}
	si._stationInventoryInfo = make(map[int64]models.StationInventoryInfo)
	if len(resp.RecommendStationList) > 0 && len(resp.StationInventoryList) > 0 {
		productId := resp.RecommendStationList[0].ProductID
		if len(resp.StationInventoryList[0]) > 0 && productId != 0 {
			si._stationInventoryInfo[int64(productId)] = models.StationInventoryInfo{
				RecommendStation:  resp.RecommendStationList[0],
				StationInventorys: resp.StationInventoryList[0],
			}
		}
	}
	return true
}

func (si *StationInventory) fetchRouteDetail(ctx context.Context, req *Prfs.RouteDetailReq, routeId int64, productId int64) (models.StationInventoryInfo, bool) {
	defaultInfo := models.StationInventoryInfo{}
	rep := prfs.GetRouteDetail(ctx, req)
	if rep == nil || rep.Errno != 0 || len(rep.Data) == 0 {
		return defaultInfo, false
	}
	if res, v := rep.Data[int32(routeId)]; !v {
		return defaultInfo, false
	} else {
		if res.RouteBasicInfo == nil || len(res.RouteBasicInfo.StationList) == 0 {
			return defaultInfo, false
		}
		si.routeDetailData = res
		stationInventoryInfo, status := si._stationInventoryInfo[productId]
		if !status {
			return defaultInfo, false
		}
		if res.RouteBasicInfo == nil {
			return stationInventoryInfo, false
		}
		stationInventoryInfo.StationList = res.RouteBasicInfo.StationList
		stationInventoryInfo.SeatLimit = res.RouteExtendInfo.SeatLimit
		stationInventoryInfo.ChildOccupySeat = res.RouteExtendInfo.ChildOccupySeat
		return stationInventoryInfo, true
	}
}

// getCarryChildrenIsOccupySeat
func (si *StationInventory) getCarryChildrenIsOccupySeat(inventory *models.StationInventorySelectInfo, info *models.PrivateBizInfo) int32 {
	// duse获取的班次维度携童是否占座
	if inventory != nil && len(inventory.ExtraInfo) > 0 {
		if val, ok := inventory.ExtraInfo["carry_child_need_seats"]; ok && val == "1" {
			return 1
		}
	}

	// prfs配置的路线维度携童是否占座
	if info != nil && info.StationInventoryInfo != nil && info.StationInventoryInfo.ChildOccupySeat != nil {
		return util.Bool2I32(*info.StationInventoryInfo.ChildOccupySeat)
	}

	return 0
}

// 获取rpc错误信息
func (m *StationInventory) GetErrorInfo(ctx context.Context) error {
	return nil
}
