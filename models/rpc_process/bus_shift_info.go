package rpc_process

import (
	"context"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_open_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
)

type BusShiftInfo struct {
	baseReq *models.BaseReqData
	data    *CarpoolOpenApi.ShiftDetail
}

func NewBusShiftInfo(ctx context.Context, baseReq *models.BaseReqData) *BusShiftInfo {
	// 删除过滤条件
	return &BusShiftInfo{
		baseReq: baseReq,
		data:    nil,
	}
}

func (b *BusShiftInfo) buildRequest(products []*models.Product) *CarpoolOpenApi.QueryShiftDetailRequest {
	if len(products) == 0 || b.baseReq.CommonBizInfo.BusServiceShiftId == "" || b.baseReq.AreaInfo.City == 0 {
		return nil
	}
	req := &CarpoolOpenApi.QueryShiftDetailRequest{
		ShiftID:       b.baseReq.CommonBizInfo.BusServiceShiftId,
		CityID:        b.baseReq.AreaInfo.City,
		RouteGroup:    &b.baseReq.CommonBizInfo.RouteId,
		DepartureTime: &b.baseReq.CommonInfo.DepartureTime,
	}
	return req
}

func (b *BusShiftInfo) Fetch(ctx context.Context, products []*models.Product) bool {
	const (
		LogTag = "bus_shift_info"
	)
	req := b.buildRequest(products)
	if req == nil {
		log.Trace.Infof(ctx, LogTag, "req is nil")
		return false
	}
	resp, err := carpool_open_api.GetQueryShiftDetail(ctx, req, buildTrace(ctx))
	if resp == nil || resp.RetCode != 0 || err != nil {
		log.Trace.Infof(ctx, LogTag, "fetch bus shift detail info failed %s", err)
		return false
	}
	if resp == nil || resp.ShiftDetail == nil {
		log.Trace.Warnf(ctx, LogTag, "fetch empty bus shift detail info")
		return false
	}

	b.data = resp.ShiftDetail
	return true
}

func (b *BusShiftInfo) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (b *BusShiftInfo) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
	if b.data == nil {
		return
	}
	commonBizInfo.ShiftInfo = b.data
}

// BuildProductBizInfo 设置单产品信息
func (b *BusShiftInfo) BuildProductBizInfo(ctx context.Context, product models.Product,
	privateBizInfo *models.PrivateBizInfo) {
}

func buildTrace(ctx context.Context) *CarpoolOpenApi.Trace {

	if tracer, ok := trace.GetCtxTrace(ctx); ok {
		hintCode := cast.ToInt64(tracer.HintCode)
		return &CarpoolOpenApi.Trace{
			LogId:       tracer.TraceId,
			Caller:      tracer.CallerFunc,
			SpanId:      &tracer.SpanId,
			HintContent: &tracer.HintContent,
			HintCode:    &hintCode,
		}
	}
	return nil
}
