package rpc_process

import (
	"context"
	"errors"
	TripCloudPassenger "git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassenger"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	pool2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/pool"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/trip_cloud_passenger"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"time"
)

// MultiStationPriceRPC 批量获取阶梯定价
type MultiStationPriceRPC struct {
	baseReq          *models.BaseReqData
	stationPriceData []*TripCloudPassenger.StationPriceDatas
}

// NewGetMultiStationPrice 批量获取阶梯定价
func NewGetMultiStationPrice(baseReq *models.BaseReqData) *MultiStationPriceRPC {
	return &MultiStationPriceRPC{
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (rd *MultiStationPriceRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (rd *MultiStationPriceRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	//根据班次匹配到对应的价格信息
	for _, value := range rd.stationPriceData {
		if product.ShiftID == value.BusShiftId {
			product.BizInfo.IntercityData.StationPrice = value
		}
	}
}

func (rd *MultiStationPriceRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

// Fetch 获取/加载
func (rd *MultiStationPriceRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	stationPriceReqList := make([]*TripCloudPassenger.GetStationPriceItem, 0)
	for _, product := range products {
		if product == nil {
			continue
		}
		stationPriceReq := &TripCloudPassenger.GetStationPriceItem{
			ProductId:  product.ProductID,
			ComboType:  product.ComboType,
			CarLevel:   product.RequireLevel,
			Role:       2,
			District:   rd.baseReq.AreaInfo.District,
			BusShiftId: product.ShiftID,
			ComboId:    product.BizInfo.ComboID,
		}
		stationPriceReqList = append(stationPriceReqList, stationPriceReq)
	}
	rd.processHandler(ctx, stationPriceReqList)
	return true
}

// 并发请求 每次请求20个 下游支持的单次最大请求数为20
func (rd *MultiStationPriceRPC) processHandler(ctx context.Context, params []*TripCloudPassenger.GetStationPriceItem) {
	paramGroups := rd.splitGroup(params)
	pool := pool2.NewPoolByTimeout(ctx, 1000*time.Millisecond)
	for _, param := range paramGroups {
		pool.AddTask(&pool2.Task{
			Req:     param,
			RunTask: sendMultiStationPrice,
		})
	}
	_, err := pool.MultiProc()
	if err != nil {
		log.Trace.Warnf(ctx, "MultiStationPrice MultiProc err||%v", err.Error())
		return
	}
	results := make([]*TripCloudPassenger.StationPriceDatas, 0)
	for _, task := range pool.GetResult() {
		if task.GetErr() != nil || task.Resp == nil {
			continue
		}
		if data, ok := task.Resp.([]*TripCloudPassenger.StationPriceDatas); ok {
			results = append(results, data...)
		}
	}
	rd.stationPriceData = results
}

// 分割参数
func (rd *MultiStationPriceRPC) splitGroup(params []*TripCloudPassenger.GetStationPriceItem) []*TripCloudPassenger.GetStationPriceRequest {
	pidStr := cast.ToString(rd.baseReq.PassengerInfo.PID)
	apolloParams := map[string]string{
		"city": cast.ToString(rd.baseReq.AreaInfo.Area),
	}
	_, hitParams := apollo.GetParameters("multi_station_price_batch_size", pidStr, apolloParams)
	batchSize := 20
	if hitParams["size"] != "" {
		batchSize = cast.ToInt(hitParams["size"])
	}
	var groups []*TripCloudPassenger.GetStationPriceRequest
	for i := 0; i < len(params); i += batchSize {
		end := i + batchSize
		if end > len(params) {
			end = len(params)
		}
		group := params[i:end]
		req := new(TripCloudPassenger.GetStationPriceRequest)
		req.Data = group
		groups = append(groups, req)
	}
	return groups
}

// 获取站点价格
func sendMultiStationPrice(ctx context.Context, input interface{}) (interface{}, error) {
	request, convertError := input.(*TripCloudPassenger.GetStationPriceRequest)
	if !convertError {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, " PGetMultiStationPrice req convertError")
		return nil, errors.New("PGetMultiStationPrice req convertError")
	}
	request.Type = 2
	res, err := trip_cloud_passenger.PGetMultiStationPrice(ctx, request)
	if err != nil || res == nil || res.Data == nil || res.Data.ListData == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, " PGetMultiStationPrice res is nil ")
		return nil, errors.New("PGetMultiStationPrice fail")
	}
	return res.Data.ListData, nil
}
