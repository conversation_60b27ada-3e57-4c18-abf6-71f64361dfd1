package rpc_process

import (
	"context"
	"errors"
	"testing"

	ufsThrift "git.xiaojukeji.com/dirpc/dirpc-go-thrift-UFS"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func mockProductFull(isTripcloud bool, prodcuctCategory int64, bizID int64, city int32, pid int64) *biz_runtime.ProductInfoFull {
	return &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ProductID:       1001,
			BusinessID:      bizID,
			ProductCategory: prodcuctCategory,
			IsTripcloud:     &isTripcloud,
		},
		BaseReqData: &models.BaseReqData{
			AreaInfo: models.AreaInfo{City: city},
			PassengerInfo: models.PassengerInfo{
				PID: pid,
			},
		},
	}
}

func TestUfsAuthFeature_Fetch(t *testing.T) {
	ctx := context.Background()
	patches := []*mockey.Mocker{}

	// mock IsTripcloudProduct
	patchTripcloud := mockey.Mock((*models.Product).IsTripcloudProduct).To(func(_ *models.Product, ctx context.Context) bool {
		return true
	}).Build()
	patches = append(patches, patchTripcloud)

	// mock apollo.FeatureToggle
	patchApollo := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, key, def string, params map[string]string) bool {
		return true
	}).Build()
	patches = append(patches, patchApollo)

	// mock ufs.MultiGetFeatures
	ufsResp := map[string]*ufsThrift.FeatureResponse{
		"feature1": {Errno: 0, Value: strPtr("1")},
		"feature2": {Errno: 0, Value: strPtr(`[12345]`)},
		"feature3": {Errno: 0, Value: strPtr("0")},
	}
	patchUfs := mockey.Mock(ufs.MultiGetFeatures).To(func(ctx context.Context, features []ufsClient.Feature, s string) (map[string]*ufsThrift.FeatureResponse, error) {
		return ufsResp, nil
	}).Build()

	// mock ufsClient.ParseFeature
	patchParse := mockey.Mock(ufsClient.ParseFeature).To(func(str string) (string, string, map[string]string, error) {
		switch str {
		case "feature1":
			return "", ufs.TripcloudAuthKey, map[string]string{"business_id": "123"}, nil
		case "feature2":
			return "", ufs.VideoAuthKey, map[string]string{}, nil
		case "feature3":
			return "", ufs.ButianAuthKey, map[string]string{}, nil
		default:
			return "", "", nil, errors.New("parse error")
		}
	}).Build()

	defer func() {
		for _, p := range patches {
			p.UnPatch()
		}
	}()

	uf := NewUserAuthRPC(ctx, "test_domain", "pid123")
	productFulls := []*biz_runtime.ProductInfoFull{
		mockProductFull(true, int64(product_category.ProductCategoryFastTaxi), 123, 1, 2),
	}
	ok := uf.Fetch(ctx, productFulls)
	assert.True(t, ok)
	assert.True(t, uf.isButianTripCloud)
	assert.Contains(t, uf.FinalMapBusinessID, int64(123))

	// 覆盖 ufs.MultiGetFeatures 返回 error
	patchUfs.UnPatch()
	patchUfsErr := mockey.Mock(ufs.MultiGetFeatures).To(func(ctx context.Context, features []ufsClient.Feature, s string) (map[string]*ufsThrift.FeatureResponse, error) {
		return nil, errors.New("ufs error")
	}).Build()
	uf2 := NewUserAuthRPC(ctx, "test_domain", "pid123")
	ok = uf2.Fetch(ctx, productFulls)
	assert.False(t, ok)
	assert.Equal(t, 2, len(uf2.RemoveTripCloud))

	// 覆盖 ParseFeature 返回 error
	patchParse.UnPatch()
	patchUfsErr.UnPatch()
	patchUfsOk := mockey.Mock(ufs.MultiGetFeatures).To(func(ctx context.Context, features []ufsClient.Feature, s string) (map[string]*ufsThrift.FeatureResponse, error) {
		return map[string]*ufsThrift.FeatureResponse{"bad": {Errno: 0, Value: strPtr("1")}}, nil
	}).Build()
	patchParseErr := mockey.Mock(ufsClient.ParseFeature).To(func(str string) (string, string, map[string]string, error) {
		return "", "", nil, errors.New("parse error")
	}).Build()
	defer patchUfsOk.UnPatch()
	defer patchParseErr.UnPatch()
	uf3 := NewUserAuthRPC(ctx, "test_domain", "pid123")
	ok = uf3.Fetch(ctx, productFulls)
	assert.False(t, ok)
	assert.Equal(t, 2, len(uf3.RemoveTripCloud))
}

func TestUfsAuthFeature_BuildProductBizInfo(t *testing.T) {
	ctx := context.Background()
	uf := NewUserAuthRPC(ctx, "test_domain", "pid123")

	// case1: 非三方且非补天
	product := biz_runtime.ProductInfoFull{
		Product: &models.Product{
			IsTripcloud:     nil,
			ProductID:       1001,
			ProductCategory: 999,
		},
	}
	info := &models.PrivateBizInfo{}
	uf.BuildProductBizInfo(ctx, product, info)
	assert.False(t, info.IsButianNeedAuth)
	assert.Nil(t, info.UfsTripCloudAuthBusinessID)

	// case2: 补天品类，isButianTripCloudNeedAuth为true
	product2 := biz_runtime.ProductInfoFull{
		Product: &models.Product{
			IsTripcloud:     nil,
			ProductID:       1001,
			ProductCategory: int64(product_category.ProductCategoryFastTaxi),
		},
	}
	info2 := &models.PrivateBizInfo{}
	uf.isButianTripCloudNeedAuth = true
	uf.BuildProductBizInfo(ctx, product2, info2)
	assert.True(t, info2.IsButianNeedAuth)

	// case3: 三方品类，FinalMapBusinessID命中
	product3 := biz_runtime.ProductInfoFull{
		Product: &models.Product{
			IsTripcloud:     nil,
			ProductID:       1001,
			BusinessID:      888,
			ProductCategory: 8888,
		},
	}
	info3 := &models.PrivateBizInfo{}
	uf.isButianTripCloudNeedAuth = false
	uf.FinalMapBusinessID[888] = true
	patchTripcloud := mockey.Mock((*models.Product).IsTripcloudProduct).To(func(_ *models.Product, ctx context.Context) bool {
		return true
	}).Build()
	uf.BuildProductBizInfo(ctx, product3, info3)
	assert.True(t, info3.UfsTripCloudAuthBusinessID[888]) // 已授权不需要绑定

	// case4: 三方品类，FinalMapBusinessID未命中
	product4 := biz_runtime.ProductInfoFull{
		Product: &models.Product{
			IsTripcloud:     nil,
			ProductID:       1001,
			BusinessID:      999,
			ProductCategory: 8888,
		},
	}
	info4 := &models.PrivateBizInfo{}
	uf.FinalMapBusinessID = make(map[int64]bool)
	patchTripcloud.UnPatch()
	patchTripcloud2 := mockey.Mock((*models.Product).IsTripcloudProduct).To(func(_ *models.Product, ctx context.Context) bool {
		return true
	}).Build()
	defer patchTripcloud2.UnPatch()
	uf.BuildProductBizInfo(ctx, product4, info4)
	assert.Nil(t, info4.UfsTripCloudAuthBusinessID)
}
