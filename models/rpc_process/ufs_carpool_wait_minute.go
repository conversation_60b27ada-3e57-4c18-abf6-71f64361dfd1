package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"strconv"

	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/nuwa/trace"
)

type UfsWaitMinuteFeature struct {
	Domain           string
	Key              string
	SendEmptyCarTime int64
}

func (uf *UfsWaitMinuteFeature) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if !carpool.IsPinCheCheV2(product.Product.ProductCategory, product.Product.CarpoolPriceType) {
		return
	}

	// 单位秒
	if uf.SendEmptyCarTime > 0 {
		info.WaitMinuteSendEmptyCarTime = uf.SendEmptyCarTime
	}
}

func (uf *UfsWaitMinuteFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

func NewWaitMinuteRPC(ctx context.Context) *UfsWaitMinuteFeature {
	return &UfsWaitMinuteFeature{
		Domain:           "passenger",
		Key:              ufs.WaitSendEmptyCarTimeKey,
		SendEmptyCarTime: 0,
	}
}

func (uf *UfsWaitMinuteFeature) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	EID := ""

	// 仅限拼成乐
	for _, p := range productFulls {
		if carpool.IsPinCheChe(p.GetProductCategory()) {
			EID = p.GetEstimateID()
		}
	}

	if len(EID) == 0 {
		return false
	}

	var (
		features = make([]ufsClient.Feature, 0)
	)

	feature := ufsClient.Feature{
		Domain: uf.Domain,
		Keys:   []string{uf.Key},
		Params: map[string]string{
			"estimate_id": EID,
		},
	}

	features = append(features, feature)
	waitRewardResp, err := ufs.MultiGetFeatures(ctx, features, "")
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "Getting send empty car time failed with err %v", err)
		return false
	}

	for _, ufsResp := range waitRewardResp {
		if ufsResp == nil || ufsResp.Errno != 0 {
			continue
		}

		//获得ufs结果
		if *ufsResp.Value != "" {
			second, err := strconv.ParseInt(*ufsResp.Value, 10, 64)
			if err == nil {
				uf.SendEmptyCarTime = second
			}
		}
	}

	return true
}
