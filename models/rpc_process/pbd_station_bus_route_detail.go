package rpc_process

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
)

// BatchRouterDetailRPC 站点巴士批量通过路线获取站点
type BatchPbdStationBusRouteDetailRPC struct {
	baseReq *models.BaseReqData

	routeDetailList map[int32]*models.RouteDetail
}

// NewPbdStationBusRouteDetail 站点巴士批量通过路线获取站点
func NewPbdStationBusRouteDetail(baseReq *models.BaseReqData) *BatchPbdStationBusRouteDetailRPC {
	return &BatchPbdStationBusRouteDetailRPC{
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (rd *BatchPbdStationBusRouteDetailRPC) GetErrorInfo(ctx context.Context) error {
	if rd.routeDetailList == nil {
		return errors.ErrGetRoueDetailFailed
	}
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (rd *BatchPbdStationBusRouteDetailRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {

	if rd.routeDetailList != nil {
		//设置路线明细
		info.RouteDetailV2 = rd.routeDetailList[int32(product.BizInfo.ComboID)]
		fromIndex := 0
		toIndex := len(info.RouteDetailV2.RouteBasicInfo.StationList) - 1
		for k, v := range info.RouteDetailV2.RouteBasicInfo.StationList {
			if int(v.StationId) == info.StationInventoryInfo.SelectInfo.FromStationId {
				info.StationInventoryInfo.SelectInfo.FromStationInfo = v
				fromIndex = k
			}
			if int(v.StationId) == info.StationInventoryInfo.SelectInfo.DestStationId {
				info.StationInventoryInfo.SelectInfo.DestStationInfo = v
				toIndex = k
			}
		}
		info.StationInventoryInfo.StationList = info.RouteDetailV2.RouteBasicInfo.StationList[fromIndex : toIndex+1]
		//设置用户座位数
		if len(info.SeatDetailInfo) <= 0 {
			if rd.baseReq.CommonBizInfo.PassengerDetailInfo == nil {
				info.CarpoolSeatNum = 1
			}
		} else {
			var (
				carpoolSeatNum int32
			)
			for _, seatDetail := range info.SeatDetailInfo {
				if seatDetail == nil {
					continue
				}
				seatDetail.IsOccupySeat = 1
				carpoolSeatNum += seatDetail.PassengerCount
			}
			info.CarpoolSeatNum = carpoolSeatNum
		}
	}
}

func (rd *BatchPbdStationBusRouteDetailRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

// Fetch 获取/加载
func (rd *BatchPbdStationBusRouteDetailRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	routeIdList := []string{}

	for _, product := range products {
		if product == nil {
			continue
		}
		if product.BizInfo != nil && product.BizInfo.ComboID != 0 {
			id := strconv.Itoa(int(product.BizInfo.ComboID))
			if !util.InArrayStr(id, routeIdList) {
				routeIdList = append(routeIdList, id)
			}

		}
	}

	if len(routeIdList) == 0 {
		return true
	}
	routeIdListStr, err := json.Marshal(routeIdList)
	if err != nil {
		log.Trace.Warnf(ctx, "BatchPbdStationBusRouteDetailRPC err err||%v", err.Error())
	}
	req := &Prfs.BatchRouteDetailReq{
		RouteIdList: string(routeIdListStr),
		CallerScene: PRFS_CALLER_SCENE,
		StartCityId: rd.baseReq.AreaInfo.City,
		CarpoolType: consts.CarPoolTypeInterCityStation,
	}

	resp := prfs.GetBatchRouteDetail(ctx, req)
	if resp == nil || len(resp.Data) <= 0 {
		return false
	}
	rd.routeDetailList = make(map[int32]*models.RouteDetail)
	for key, data := range resp.Data {
		routeDetailInfo := models.RouteDetail(*data)
		rd.routeDetailList[key] = &routeDetailInfo
	}
	return true
}
