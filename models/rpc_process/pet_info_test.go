package rpc_process

import (
	"context"
	"encoding/json"
	"fmt"
	sdk "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	Dirpc_SDK_PetTripApi "git.xiaojukeji.com/dirpc/dirpc-go-http-PetTripApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/pettripapi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/logic/route_estimate/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"testing"
)

var (
	serviceReq = &model.ServiceReq{
		OrderInfo: &order_info.OrderInfo{
			OrderInfo: &sdk.OrderInfo{
				EstimatePcId: proto.String("8200"),
			},
		},
		UserInfo: &passport.UserInfo{},
	}

	pet = &PetInfo{ServiceReq: serviceReq}
)

func mock() *gomonkey.Patches {

	p := gomonkey.NewPatches()

	// log.Trace.Infof
	log.Trace = &ddlog.DiLogHandle{}
	p.ApplyMethodFunc(log.Trace, "Infof", func(ctx context.Context, tag string, format string, args ...interface{}) {
		return
	})

	// pettripapi.GetPetList
	p.ApplyFunc(pettripapi.GetPetList, func(ctx context.Context, req *Dirpc_SDK_PetTripApi.PetListReq) (list *Dirpc_SDK_PetTripApi.PetListResp, err error) {
		return &Dirpc_SDK_PetTripApi.PetListResp{
			PetList: []*Dirpc_SDK_PetTripApi.PetInfo{
				{
					PetNo:              "123",
					PetType:            123,
					PetTypeDesc:        "123",
					Nickname:           "123",
					Avatar:             "123",
					WeightCategory:     123,
					WeightCategoryDesc: "123",
					IsDefault:          true,
				},
			},
		}, nil
	})

	// cast.ToString
	p.ApplyFunc(cast.ToString, func(i interface{}) string {
		return ""
	})

	// apollo.GetParameters
	p.ApplyFunc(apollo.GetParameters, func(name, userID string, params map[string]string) (isHit bool, assignParams map[string]string) {
		return true, map[string]string{
			"id":           "1",
			"service_type": "2",
		}
	})

	// ufs.GetFeatureV2
	p.ApplyFunc(ufs.GetFeatureV2, func(ctx context.Context, domain string, key string, params map[string]string) (string, error) {
		pet := UfsValue{
			LastOrderPetInfo: &price_api.PetInfo{
				PetNo:                    "123",
				PetType:                  123,
				PetTypeDesc:              "123",
				NickName:                 "123",
				Avatar:                   "123",
				WeightCategory:           123,
				WeightCategoryDesc:       "123",
				PetServiceTag:            123,
				PersonalizedCustomOption: "123",
			},
		}
		marshal, _ := json.Marshal(pet)
		return string(marshal), nil
	})

	return p

}

func Test_NewPetInfo(t *testing.T) {

	p := mock()
	defer p.Reset()

	NewPetInfo(serviceReq)
}

func Test_Fetch(t *testing.T) {
	p := mock()
	defer p.Reset()

	pet.Fetch(context.Background(), nil)

	fmt.Println(pet.petInfo)
}

func Test_BuildCommonBizInfo(t *testing.T) {
	p := mock()
	defer p.Reset()

	pet.BuildCommonBizInfo(context.Background(), &models.CommonBizInfo{})
}

func Test_BuildProductBizInfo(t *testing.T) {
	pet.BuildProductBizInfo(context.Background(), models.Product{}, nil)
}

func Test_GetErrorInfo(t *testing.T) {
	pet.GetErrorInfo(context.Background())
}
