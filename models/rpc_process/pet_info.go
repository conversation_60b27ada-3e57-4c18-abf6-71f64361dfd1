package rpc_process

import (
	"context"
	"encoding/json"
	Dirpc_SDK_PetTripApi "git.xiaojukeji.com/dirpc/dirpc-go-http-PetTripApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/pettripapi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/logic/route_estimate/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace/v2"
	"github.com/spf13/cast"
	"strconv"
)

const (
	PetServiceTagNoBag = 1000
)

type PetInfo struct {
	*model.ServiceReq

	petInfo *models.PetInfo
}

type UfsValue struct {
	LastOrderPetInfo *price_api.PetInfo `json:"last_order_pet_info"`
}

func NewPetInfo(serviceReq *model.ServiceReq) biz_runtime.RpcProcessWithBaseProducts {
	return &PetInfo{
		ServiceReq: serviceReq,
	}
}

func (p *PetInfo) Fetch(ctx context.Context, products []*models.Product) bool {
	// 准入
	if !p.allow(ctx) {
		return false
	}

	// 获取宠物信息
	if !p.getPetInfo(ctx, products) {
		return false
	}

	// 获取增值服务映射关系: 非阻塞
	p.getPersonalizedCustomOption(ctx, products)

	// 航空仓信息: 非阻塞
	p.getPetServiceTag(ctx, products)

	return true
}

func (p *PetInfo) allow(ctx context.Context) bool {
	if p.ServiceReq == nil || p.ServiceReq.OrderInfo == nil {
		return false
	}

	// 只针对宠物快车
	// 宠物专车没有这个费项，宠物专车走的是增值服务的链路
	if cast.ToInt(p.ServiceReq.OrderInfo.EstimatePcId) != estimate_pc_id.EstimatePcIdPetFastCar {
		return false
	}

	return true
}

func (p *PetInfo) getPetInfo(ctx context.Context, products []*models.Product) bool {
	if p.ServiceReq == nil || p.ServiceReq.UserInfo == nil {
		return false
	}

	reqPets := &Dirpc_SDK_PetTripApi.PetListReq{
		Uid: int64(p.ServiceReq.UserInfo.UID),
	}

	resp, err := pettripapi.GetPetList(ctx, reqPets)
	if err != nil || len(resp.PetList) == 0 {
		return false
	}

	pet := resp.PetList[0]
	p.petInfo = &models.PetInfo{
		PetNo:              pet.PetNo,
		PetType:            pet.PetType,
		PetTypeDesc:        pet.PetTypeDesc,
		NickName:           pet.Nickname,
		Avatar:             pet.Avatar,
		WeightCategory:     pet.WeightCategory,
		WeightCategoryDesc: pet.WeightCategoryDesc,
		IsDefault:          pet.IsDefault,
		PetServiceTag:      PetServiceTagNoBag, // 默认不带航空仓
	}

	return true
}

// 获取增值服务映射关系
func (p *PetInfo) getPersonalizedCustomOption(ctx context.Context, products []*models.Product) bool {

	if p.petInfo == nil || p.ServiceReq == nil || p.ServiceReq.UserInfo == nil {
		return false
	}

	apolloParams := map[string]string{
		"weight_category": cast.ToString(p.petInfo.WeightCategory),
		"pet_service_tag": cast.ToString(p.petInfo.PetServiceTag),
	}

	isAllow, assignParams := apollo.GetParameters("convert_pet_service_info", cast.ToString(p.ServiceReq.UserInfo.PID), apolloParams)
	feature := dos.CustomFeatureStruct{
		Count: 1,
	}
	if isAllow {
		id, err1 := strconv.Atoi(assignParams["id"])
		serviceType, err2 := strconv.Atoi(assignParams["service_type"])
		if err1 == nil && err2 == nil {
			feature.Id = int64(id)
			st32 := int32(serviceType)
			feature.ServiceType = &st32
		}

	}
	var featureList = make([]dos.CustomFeatureStruct, 0)
	featureList = append(featureList, feature)
	jsonData, err := json.Marshal(featureList)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "json marshal err = %v", err)
		return false
	}

	listStr := string(jsonData)
	p.petInfo.PersonalizedCustomOption = listStr
	return true
}

func (p *PetInfo) getPetServiceTag(ctx context.Context, products []*models.Product) bool {

	if p.ServiceReq == nil || p.ServiceReq.UserInfo == nil || p.petInfo == nil {
		return false
	}

	ufsParams := map[string]string{
		"passenger_id": cast.ToString(p.ServiceReq.UserInfo.PID),
	}

	feature, err := ufs.GetFeatureV2(ctx, ufs.DomainPassenger, ufs.KeyPetsTravelCarryBag, ufsParams)
	if err != nil || len(feature) == 0 {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "get ufs feature, key = %v | err = %v", ufs.KeyPetsTravelCarryBag, err)
		return false
	}

	ufsValue := &UfsValue{}
	err = json.Unmarshal([]byte(feature), ufsValue)
	if err != nil || ufsValue.LastOrderPetInfo == nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "ufs feature unmarshal err = %v||ufsValue = %v", err, ufsValue)
		return false
	}

	p.petInfo.PetServiceTag = ufsValue.LastOrderPetInfo.PetServiceTag

	return true
}

func (p *PetInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.PetInfo = p.petInfo
}

func (p *PetInfo) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}

func (p *PetInfo) GetErrorInfo(ctx context.Context) error {
	return nil
}
