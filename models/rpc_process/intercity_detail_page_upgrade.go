package rpc_process

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

// IntercityDetailPageUpgrade
type IntercityDetailPageUpgrade struct {
	baseReq *models.BaseReqData

	isNewUpgrade  bool
	targetProduct *models.Product
}

// NewIntercityDetailPageUpgrade
func NewIntercityDetailPageUpgrade(baseReq *models.BaseReqData) *IntercityDetailPageUpgrade {
	return &IntercityDetailPageUpgrade{
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (in *IntercityDetailPageUpgrade) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (in *IntercityDetailPageUpgrade) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	return
}

// BuildCommonBizInfo ...
func (in *IntercityDetailPageUpgrade) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	commonInfo := in.baseReq.CommonInfo
	product := in.targetProduct
	agentType := in.baseReq.CommonBizInfo.AgentType
	sceneFlag := DetailConst.FormatDetail
	if agentType == DetailConst.QuickScanCode || agentType == DetailConst.FormatScanCode {
		sceneFlag = DetailConst.ScanCode
	}
	newPageParams := &NewPageSwitchParams{
		AppVersion:  commonInfo.AppVersion,
		AccessKeyId: cast.ToString(commonInfo.AccessKeyID),
		Lang:        commonInfo.Lang,
		Pid:         cast.ToString(in.baseReq.PassengerInfo.PID),
		Uid:         cast.ToString(in.baseReq.PassengerInfo.UID),
		Channel:     cast.ToString(commonInfo.Channel),
		RouteId:     cast.ToString(commonInfo.RouteID),
		CityId:      cast.ToString(in.baseReq.AreaInfo.City),
		ProductId:   cast.ToString(product.ProductID),
		BusinessId:  cast.ToString(product.BusinessID),
		SceneFlag:   sceneFlag,
	}
	info.IsNewPageUpgrade = JudgeNewOrderPageSwitch(ctx, newPageParams)
}

// Fetch 获取/加载
func (in *IntercityDetailPageUpgrade) Fetch(ctx context.Context, products []*models.Product) bool {
	var targetProduct *models.Product
	for _, product := range products {
		if product == nil {
			continue
		}
		// 大巴
		if carpool.IsIntercityStation(ctx, int(product.CarpoolType)) {
			targetProduct = product
			break
		}
	}

	if targetProduct == nil {
		return true
	}

	in.targetProduct = targetProduct
	return true
}

type NewPageSwitchParams struct {
	Pid         string `json:"pid"`
	Uid         string `json:"uid"`
	AccessKeyId string `json:"access_key_id"`
	AppVersion  string `json:"app_version"`
	Lang        string `json:"lang"`
	Channel     string `json:"channel"`
	BusinessId  string `json:"business_id"`
	ProductId   string `json:"product_id"`
	CarpoolType string `json:"carpool_type"`
	ComboType   string `json:"combo_type"`
	SceneFlag   string `json:"scene_flag"`
	ComboId     string `json:"combo_id"`
	CityId      string `json:"city_id"`
	RouteId     string `json:"route_id"`
}

// JudgeNewOrderPageSwitch 发单页改造开关
func JudgeNewOrderPageSwitch(ctx context.Context, pageParams *NewPageSwitchParams) bool {
	if pageParams == nil {
		return false
	}
	params := map[string]string{
		"pid":           pageParams.Pid,
		"uid":           pageParams.Uid,
		"access_key_id": pageParams.AccessKeyId,
		"app_version":   pageParams.AppVersion,
		"lang":          pageParams.Lang,
		"channel":       pageParams.Channel,
		"business_id":   pageParams.BusinessId,
		"product_id":    pageParams.ProductId,
		"carpool_type":  pageParams.CarpoolType,
		"combo_type":    pageParams.ComboType,
		"scene_flag":    pageParams.SceneFlag,
		"combo_id":      pageParams.ComboId,
		"city":          pageParams.CityId,
	}
	return apollo.FeatureToggle(ctx, DetailConst.IntercityNewOrderPageSwitch, pageParams.Pid, params)
}
