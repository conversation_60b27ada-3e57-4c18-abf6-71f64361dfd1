package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	productlib "git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace/v2"
	"strconv"
)

type UfsHkAuthFeature struct {
	Ctx         context.Context
	Domain      string
	PassengerID string
	AuthKey     string

	RemoveTripCloud []models.ProductCategory
	// FinalMapProductCategory map[int64]bool
	FinalMapBusinessID map[int64]bool
}

func NewUserHkAuthRPC(ctx context.Context, domain string, pid string) *UfsHkAuthFeature {
	return &UfsHkAuthFeature{
		Ctx:                ctx,
		Domain:             domain,
		PassengerID:        pid,
		AuthKey:            ufs.TripcloudAuthKey,
		RemoveTripCloud:    make([]models.ProductCategory, 0),
		FinalMapBusinessID: make(map[int64]bool),
	}
}

func (uf *UfsHkAuthFeature) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	var (
		// businessId->productCategory
		searchMap = make(map[int64][]models.ProductCategory, 0)
		// UFSFeatures UFS特征
		UFSFeatures = make([]ufsClient.Feature, 0)
		// AuthBusiness VideoBusiness key：businessID，value：是否授权
		AuthBusiness = make(map[int64]bool, 0)
	)

	// 获取符合要求，需要请求ufs的品类
	for _, p := range productFulls {
		if productlib.IsHongKongThird(consts.ProductID(p.GetProductId())) {
			searchMap[p.GetBusinessID()] = append(searchMap[p.GetBusinessID()], models.ProductCategory(p.GetProductCategory()))
		}
	}

	// 构建用户未授权的三方feature列表
	for businessID := range searchMap {
		feature := ufsClient.Feature{
			Domain: uf.Domain,
			Keys:   []string{uf.AuthKey},
			Params: map[string]string{
				"passenger_id": uf.PassengerID,
				"business_id":  strconv.FormatInt(businessID, 10),
			},
		}
		UFSFeatures = append(UFSFeatures, feature)
	}

	UFSResp, err := ufs.MultiGetFeatures(ctx, UFSFeatures, "")
	if err != nil {
		// 若获取三方授权若状态失败，则remove所有三方车型
		log.Trace.Info(ctx, trace.DLTagUndefined, "get auth status from ufs fail with err %v", err)
		return false
	}

	// 获取需要授权的列表
	for strFeature, ufsResp := range UFSResp {
		_, key, params, featureErr := ufsClient.ParseFeature(strFeature)
		if key != ufs.TripcloudAuthKey {
			continue
		}
		if featureErr != nil {
			continue
		}

		businessID, authErr := strconv.ParseInt(params["business_id"], 10, 64)
		if authErr != nil || businessID == 0 {
			return false
		}

		if ufsResp == nil || ufsResp.Errno != 0 || *ufsResp.Value != "1" {
			AuthBusiness[businessID] = true
		}
	}
	log.Trace.Infof(ctx, trace.DLTagUndefined, "hk auth Business list:  %v", AuthBusiness)
	uf.FinalMapBusinessID = AuthBusiness

	return true
}

func (uf *UfsHkAuthFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

func (uf *UfsHkAuthFeature) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if !productlib.IsHongKongThird(consts.ProductID(product.GetProductId())) {
		return
	}

	_, IsNeed := uf.FinalMapBusinessID[product.GetBusinessID()]
	// 已授权的三方品类不需要绑定
	if !IsNeed {
		return
	}

	info.UfsTripCloudAuthBusinessID = uf.FinalMapBusinessID

	return
}

func (uf *UfsHkAuthFeature) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) []models.ProductCategory {
	// 补天ufs读失败，应该不出，给remove掉
	return uf.RemoveTripCloud
}
