package rpc_process

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	dirpcSdkBrick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/brick"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// TicketRuleRPC 站点巴士 票规则信息
type TicketRuleRPC struct {
	baseReq *models.BaseReqData

	req              *proto.StationBusInsteadOrderEstimateReq
	identityPageInfo *dirpcSdkBrick.IdentityPageInfoData
}

// NewIdentityPageInfo 获取身份信息
func NewTicketRuleInfo(req *proto.StationBusInsteadOrderEstimateReq, baseReq *models.BaseReqData) *TicketRuleRPC {
	return &TicketRuleRPC{
		req:     req,
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (m *TicketRuleRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (ip *TicketRuleRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	if ip.baseReq != nil && info != nil && info.RouteDetailV2 != nil {
		if len(info.SeatDetailInfo) <= 0 {
			info.CarpoolSeatNum = 1
		} else {
			var (
				carpoolSeatNum   int32
				carryChildrenNum int32
			)
			for _, seatDetail := range info.SeatDetailInfo {
				if seatDetail == nil {
					continue
				}
				//根据规则信息和prfs的信息，重新赋值是否占座和座位数信息
				if seatDetail.PassengerType == seat_selection_consts.CarryChildren.ToInt32() {
					carryChildrenNum += seatDetail.PassengerCount
					seatDetail.IsOccupySeat = ip.getCarryChildrenIsOccupySeat(&product, 0)

				}
				if seatDetail.IsOccupySeat == seat_selection_consts.IsOccupy.ToInt32() {
					carpoolSeatNum += seatDetail.PassengerCount
				}
			}

			info.CarpoolSeatNum = carpoolSeatNum
			info.CarryChildrenNum = carryChildrenNum
		}
		info.CarryChildrenIsOccupySeat = ip.getCarryChildrenIsOccupySeat(&product, 0)
	}
}

// BuildCommonBizInfo ...
func (ip *TicketRuleRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.IdentityPageInfo = ip.identityPageInfo
}

// Fetch 获取/加载
func (ip *TicketRuleRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		targetProduct *models.Product

		err error
	)

	targetProduct = ip.getTargetProduct(ctx, products)

	if targetProduct == nil {
		return true
	}

	err = ip.fetchGetIdentityPageInfo(ctx, targetProduct)
	if err != nil {
		return false
	}

	return true
}

// getTargetProduct 获乘车人变动，使用报价单的品类信息即可，非乘车人变动获取渲染的目标品类,优先级：用户选择班次>最近班次
func (ip *TicketRuleRPC) getTargetProduct(ctx context.Context, products []*models.Product) *models.Product {

	//1、乘车人变动，直接使用通过报价单获取的product即可
	if ip.baseReq.CommonBizInfo.SceneType == consts.SceneTypeChangePassenger {
		for _, product := range products {
			if product == nil {
				continue
			}
			return product
		}
	} else { //2、非乘车人变动
		//2.1用户选择的班次
		busServiceShiftId := ip.req.ShiftId
		if busServiceShiftId != "" {
			for _, product := range products {
				if product == nil {
					continue
				}
				if product.ShiftID == busServiceShiftId {
					return product
				}
			}
		} else { //2.2最近库存>0的班次
			for _, product := range products {
				if product == nil {
					continue
				}
				//库存>0
				if product.BizInfo.MaxCarpoolSeatNum > 0 {
					return product
				}
			}
		}
	}
	return nil
}

// buildRequest ...
func (ip *TicketRuleRPC) buildGetIdentityPageInfoRequest(ctx context.Context, targetProduct *models.Product) *dirpcSdkBrick.GetIdentityPageInfoReq {
	if targetProduct == nil {
		return nil
	}

	if targetProduct.BizInfo == nil {
		return nil
	}

	return &dirpcSdkBrick.GetIdentityPageInfoReq{
		PassengerId:       ip.baseReq.PassengerInfo.PID,
		PassengerPhone:    ip.baseReq.PassengerInfo.Phone,
		Caller:            IdentityPageTypeCaller,
		BusServiceShiftId: &targetProduct.ShiftID,
		BusinessId:        &targetProduct.BusinessID,
		ComboId:           &targetProduct.BizInfo.ComboID,
	}
}

// fetchGetIdentityPageInfo ...
func (ip *TicketRuleRPC) fetchGetIdentityPageInfo(ctx context.Context, targetProduct *models.Product) error {
	if targetProduct == nil {
		return errors.New("target product is nil")
	}

	if targetProduct.BizInfo == nil {
		return errors.New("biz info is nil")
	}

	if targetProduct.BizInfo.RouteDetailV2 == nil {
		return errors.New("route detail is nil")
	}

	req := ip.buildGetIdentityPageInfoRequest(ctx, targetProduct)
	if req == nil {
		return errors.New("req is nil")
	}

	data, err := brick.GetIdentityPageInfo(ctx, req)
	if err != nil {
		return err
	}

	log.Trace.Infof(ctx, "fetchGetIdentityPageInfo", "history:%v", util.JustJsonEncode(data))

	ip.identityPageInfo = data

	return err
}

// getCarryChildrenIsOccupySeat ... 优先从brick规则中获取携童是否占座，取不到走原逻辑：从prfs路线详情中获取
func (ip *TicketRuleRPC) getCarryChildrenIsOccupySeat(product *models.Product, defaultOccupy int32) int32 {
	//新逻辑 从brick规则信息中获取携童是否占座
	if ip.identityPageInfo != nil {
		ruleInfo := ip.identityPageInfo.RuleInfo
		if ruleInfo != nil && len(ruleInfo.SpecialSeatRules) > 0 {
			for _, rules := range ruleInfo.SpecialSeatRules {
				if rules == nil {
					continue
				}

				if rules.RuleName == seat_selection_consts.CarryChildTicket && rules.IsSupport == 1 {
					return int32(rules.OccupySeat)
				}

			}
		}
	}
	// 兼容老逻辑 从prfs路线获取
	if product != nil && product.BizInfo != nil && product.BizInfo.RouteDetailV2 != nil {
		return util.Bool2I32(product.BizInfo.RouteDetailV2.IsChildOccupy())
	}

	return defaultOccupy
}
