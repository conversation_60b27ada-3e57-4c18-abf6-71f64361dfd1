package rpc_process

import (
	"context"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_open_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// BatchShiftDetailRPC 站点巴士批量通过路线获取站点
type BatchShiftDetailRPC struct {
	baseReq *models.BaseReqData

	ShiftDetailList map[string]*CarpoolOpenApi.ShiftDetail
}

// NewBatchShiftDetail 站点巴士批量通过路线获取站点
func NewBatchShiftDetail(baseReq *models.BaseReqData) *BatchShiftDetailRPC {
	return &BatchShiftDetailRPC{
		baseReq: baseReq,
	}
}

func (b *BatchShiftDetailRPC) buildRequest(products []*models.Product) *CarpoolOpenApi.BatchQueryShiftDetailRequest {
	if len(products) == 0 {
		return nil
	}
	req := &CarpoolOpenApi.BatchQueryShiftDetailRequest{
		ReqList: make([]*CarpoolOpenApi.QueryShiftDetailRequest, 0),
	}
	for _, v := range products {
		if v.BizInfo == nil || v.BizInfo.StationInventoryInfo == nil {
			continue
		}
		tmp := &CarpoolOpenApi.QueryShiftDetailRequest{
			ShiftID:       v.BizInfo.StationInventoryInfo.SelectInfo.ShiftID,
			RouteGroup:    &v.BizInfo.StationInventoryInfo.SelectInfo.RouteId,
			DepartureTime: &v.BizInfo.StationInventoryInfo.SelectInfo.DepartureTime,
			CityID:        b.baseReq.AreaInfo.City,
		}
		req.ReqList = append(req.ReqList, tmp)
	}
	return req
}

func (b *BatchShiftDetailRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	const (
		LogTag = "batch_bus_shift_info"
	)
	req := b.buildRequest(products)
	log.Trace.Infof(ctx, LogTag, "fetch batch bus shift detail info request %v", req)
	if req == nil {
		log.Trace.Infof(ctx, LogTag, "req is nil")
		return false
	}
	resp, err := carpool_open_api.GetBatchQueryShiftDetail(ctx, req, buildTrace(ctx))
	if resp == nil || resp.RetCode != 0 || err != nil {
		log.Trace.Warnf(ctx, LogTag, "fetch batch bus shift detail info failed %s", err)
		return false
	}
	b.formatShiftDetail(resp.RespList)
	return true
}

func (b *BatchShiftDetailRPC) formatShiftDetail(data []*CarpoolOpenApi.QueryShiftDetailResponse) {
	b.ShiftDetailList = make(map[string]*CarpoolOpenApi.ShiftDetail)
	for _, v := range data {
		if v == nil || v.RetCode != 0 || v.ShiftDetail == nil {
			continue
		}
		b.ShiftDetailList[v.ShiftDetail.ShiftID] = v.ShiftDetail
	}
}

func (b *BatchShiftDetailRPC) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
	commonBizInfo.BatchShiftInfo = b.ShiftDetailList
}

func (b *BatchShiftDetailRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	if value, ok := b.ShiftDetailList[info.StationInventoryInfo.SelectInfo.ShiftID]; ok {
		info.ShiftDetail = value
	}
}

func (m *BatchShiftDetailRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}
