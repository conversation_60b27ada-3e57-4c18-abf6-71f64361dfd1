package rpc_process

import (
	"context"
	"fmt"
	ufsThrift "git.xiaojukeji.com/dirpc/dirpc-go-thrift-UFS"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"os"
	"path/filepath"
	"runtime"

	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
)

// 查找配置文件：从当前文件起，向上找 conf/app.toml，返回绝对路径
func findConfigPath() (string, error) {
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		return "", fmt.Errorf("无法获取当前文件路径")
	}

	// 当前源代码所在目录
	dir := filepath.Dir(filename)

	// 向上递归查找 conf/app.toml
	for {
		configPath := filepath.Join(dir, "conf", "app_dev.toml")
		if _, err := os.Stat(configPath); err == nil {
			return configPath, nil
		}

		// 到根目录就停止
		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}

	return "", fmt.Errorf("未找到 conf/app.toml")
}

// log未初始化，全局初始化，会被并发的其他协程释放，建议在每个run中初始化
func initMainFunc() {
	path, err := findConfigPath()
	if err != nil {
		fmt.Println("错误：", err)
		return
	}
	conf.InitConf(path)
	log.Init()
}

// 创建模拟的UFS响应
func createMockUFSResponse(features []ufsClient.Feature) map[string]*ufsThrift.FeatureResponse {
	var (
		featuresStringList = make([]string, len(features))
		featureResp        = make(map[string]*ufsThrift.FeatureResponse)
	)

	featuresStringList, _ = ufsClient.BuildFeatures(features)
	for _, feature := range featuresStringList {
		featureResp[feature] = &ufsThrift.FeatureResponse{
			Errno: 0,
			Value: getStringPtr("0"),
		}
	}
	return featureResp
}

// 设置所需的依赖项模拟
func setupMockDependencies() func() {
	//initMainFunc()
	var patches []*mockey.Mocker

	// Mock MultiGetFeatures 函数
	patches = append(patches, mockey.Mock(ufs.MultiGetFeatures).
		To(func(_ context.Context, features []ufsClient.Feature, _ string) (map[string]*ufsThrift.FeatureResponse, error) {
			return createMockUFSResponse(features), nil
		}).Build())
	patches = append(patches, mockey.Mock((*ddlog.DiLogHandle).Infof).Return().Build())
	patches = append(patches, mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build())

	return func() {
		for _, patch := range patches {
			patch.UnPatch()
		}
	}
}

type Want struct {
	fetch       bool
	needAuthMap map[int64]bool
}

func TestUfsHkAuthFeature_Fetch(t *testing.T) {
	tests := []*struct {
		name         string
		ctx          context.Context
		productFulls []*biz_runtime.ProductInfoFull
		want         Want
	}{
		{
			name: "正常测试",
			ctx:  context.Background(),
			productFulls: []*biz_runtime.ProductInfoFull{
				{
					Product: &models.Product{
						ProductID:       7501,
						BusinessID:      7501,
						ProductCategory: 12200,
					},
				},
				{
					Product: &models.Product{
						ProductID:       7502,
						BusinessID:      7502,
						ProductCategory: 12201,
					},
				},
				{
					Product: &models.Product{
						ProductID:       7520,
						BusinessID:      7520,
						ProductCategory: 12202,
					},
				},
			},
			want: Want{
				fetch: true,
				needAuthMap: map[int64]bool{
					7501: true,
					7502: true,
					7520: true,
				},
			},
		},
		{
			name: "Business为空",
			ctx:  context.Background(),
			productFulls: []*biz_runtime.ProductInfoFull{
				{
					Product: &models.Product{
						ProductID: 7501,
						//BusinessID:      7501,
						ProductCategory: 12200,
					},
				},
				{
					Product: &models.Product{
						ProductID: 7502,
						//BusinessID:      7502,
						ProductCategory: 12201,
					},
				},
				{
					Product: &models.Product{
						ProductID:       7520,
						BusinessID:      7520,
						ProductCategory: 12202,
					},
				},
			},
			want: Want{
				fetch:       false,
				needAuthMap: map[int64]bool{
					//7501: true,
					//7502: true,
					//7520: true,
				},
			},
		},
		{
			name: "部分非三方品类",
			ctx:  context.Background(),
			productFulls: []*biz_runtime.ProductInfoFull{
				{
					Product: &models.Product{
						ProductID:       7500,
						BusinessID:      7501,
						ProductCategory: 12200,
					},
				},
				{
					Product: &models.Product{
						ProductID:       7400,
						BusinessID:      7502,
						ProductCategory: 12201,
					},
				},
				{
					Product: &models.Product{
						ProductID:       7520,
						BusinessID:      7520,
						ProductCategory: 12202,
					},
				},
			},
			want: Want{
				fetch: true,
				needAuthMap: map[int64]bool{
					//7501: true,
					//7502: true,
					7520: true,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置mock
			cleanup := setupMockDependencies()
			defer cleanup()

			uf := NewUserHkAuthRPC(tt.ctx, "passenger", "123456")
			got := uf.Fetch(tt.ctx, tt.productFulls)
			assert.Equal(t, tt.want.fetch, got)
			assert.Equal(t, tt.want.needAuthMap, uf.FinalMapBusinessID)
		})
	}
}

// 空函数，没有实际意义，为了覆盖率
func TestUfsHkAuthFeature_BuildCommonBizInfo(t *testing.T) {
	ctx := context.Background()
	uf := NewUserHkAuthRPC(ctx, "passenger", "123456")
	uf.BuildCommonBizInfo(ctx, &models.CommonBizInfo{})
}

func TestUfsHkAuthFeature_BuildProductBizInfo(t *testing.T) {
	tests := []*struct {
		name    string
		ctx     context.Context
		want    map[int64]bool
		product []*biz_runtime.ProductInfoFull
		info    *models.PrivateBizInfo
	}{
		{
			name:    "normal test case",
			ctx:     context.Background(),
			product: []*biz_runtime.ProductInfoFull{},
			info:    &models.PrivateBizInfo{},
			want:    map[int64]bool{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			uf := NewUserHkAuthRPC(tt.ctx, "passenger", "123456")
			for _, product := range tt.product {
				uf.BuildProductBizInfo(tt.ctx, *product, tt.info)
				assert.Equal(t, tt.info.UfsTripCloudAuthBusinessID, tt.want)
			}
		})
	}
}

func TestUfsHkAuthFeature_HandlerFilter(t *testing.T) {
	tests := []*struct {
		name       string
		ctx        context.Context
		want       []models.ProductCategory
		productMap map[int64]*biz_runtime.ProductInfoFull
	}{
		{
			name:       "normal test case",
			ctx:        context.Background(),
			want:       []models.ProductCategory{},
			productMap: map[int64]*biz_runtime.ProductInfoFull{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			uf := NewUserHkAuthRPC(tt.ctx, "passenger", "123456")
			got := uf.HandlerFilter(tt.ctx, tt.productMap)
			assert.Equal(t, tt.want, got)
		})
	}
}
