package rpc_process

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"

	Sps "git.xiaojukeji.com/dirpc/dirpc-go-http-Sps"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	jsoniter "github.com/json-iterator/go"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"github.com/spf13/cast"
)

const (
	formInteractive      = 1
	isTaxiPeakFee        = "1"
	defaultTaxiPeakFee   = "0"
	apolloEstimateBoxKey = "gs_unione_aggregation_multi_product_categories"
	apolloConfigNS       = "taxi_peak_fee"

	// BillItemName 费用项
	BillItemName = "taxi_peak_price"

	// TaxiPeakFeeServiceID 出租车峰期加价serviceID
	TaxiPeakFeeServiceID = 107
)

const (
	isMockSelected = 1
	isSelected     = 1
	// OrderType 实时单订单类型
	OrderType int32 = 0
)

// TaxiPeakFeeConfig 峰期加价配置
type TaxiPeakFeeConfig struct {
	CityID          string   `json:"city_id"`
	DcmpKey         *DcmpKey `json:"dcmp_key"`
	OpenRules       *Rules   `json:"open_rules"`
	OpenStatus      string   `json:"open_status"`
	ProductCategory int      `json:"product_category"`
}

// DcmpKey dcmp配置
type DcmpKey struct {
	PeakPeriod map[string]interface{} `json:"peak_period"`
	WayOutConf string                 `json:"way_out"`
}

// Rules 开城规则
type Rules struct {
	AccessKeyID      []string    `json:"access_key_id"`
	CountyInfo       *CountyConf `json:"county_conf"`
	FenceInfo        *FenceConf  `json:"fence_info"`
	LaunchApolloName string      `json:"launch_apollo_name"`
}

// FenceConf 围栏配置
type FenceConf struct {
	FenceCheckType int      `json:"fence_check_type"`
	GroupID        int      `json:"group_id"`
	StartFenceID   []string `json:"start_fence_id"`
	StopFenceID    []string `json:"stop_fence_id"`
}

// CountyConf 区县列表
type CountyConf struct {
	CountyList  []string `json:"county_list"`
	SupportType int      `json:"support_type"`
}

// ExtraInfo hestia_charge extraInfo
type ExtraInfo struct {
	// 由于峰期加价追加模型预估展示为强制无出口模式
	// 在当前城市配置为有出口则模拟用户勾选
	IsSelected           int   `json:"is_selected"`
	IsMockCanNotSelected int   `json:"is_mock_cannot_select"`
	PID                  int64 `json:"passenger_id"`
}

// TaxiPeakFee 峰期加价参数
type TaxiPeakFee struct {
	City                string
	AccessKeyID         string
	AppVersion          string
	PID                 string
	Phone               string
	CountyID            string
	ToCountyID          string
	IsTaxiPeakFee       string
	ToCity              string
	MenuID              string
	PageType            int32
	SourceId            int32
	OrderType           int32
	MultiRequireProduct []models.RequireProduct
	Sps                 map[int64]*hestia_charge.TaxiSpsData
	DepartureTime       int64
	IsHoliday           bool // 是否特殊节假日
}

// customExtraInfo 获取节假日信息接口的ExtraInfo
type customExtraInfo struct {
	PassengerPrice    float32 `json:"passenger_price"`
	DriverPrice       float32 `json:"driver_price"`
	PassengerDiscount float32 `json:"passenger_discount"`
	CanSelect         int     `json:"can_select"`
	IsHoliday         int     `json:"is_holiday"`
	IsFixPrice        int     `json:"is_fix_price"`
}

// NewTaxiPeakFee 出租车峰期加价
func NewTaxiPeakFee(ctx context.Context, req *models.BaseReqData) *TaxiPeakFee {
	return &TaxiPeakFee{
		City:                cast.ToString(req.AreaInfo.City),
		ToCity:              cast.ToString(req.AreaInfo.ToArea),
		AccessKeyID:         cast.ToString(req.CommonInfo.AccessKeyID),
		AppVersion:          req.CommonInfo.AppVersion,
		PID:                 cast.ToString(req.PassengerInfo.PID),
		Phone:               req.PassengerInfo.Phone,
		CountyID:            cast.ToString(req.AreaInfo.FromCounty),
		ToCountyID:          cast.ToString(req.AreaInfo.ToCounty),
		IsTaxiPeakFee:       defaultTaxiPeakFee,
		Sps:                 make(map[int64]*hestia_charge.TaxiSpsData),
		MenuID:              req.CommonInfo.MenuID,
		PageType:            req.CommonInfo.PageType,
		SourceId:            req.CommonInfo.SourceID,
		OrderType:           req.CommonInfo.OrderType,
		MultiRequireProduct: req.CommonBizInfo.MultiRequireProduct,
		DepartureTime:       req.CommonInfo.DepartureTime,
	}
}

// 获取rpc错误信息
func (t *TaxiPeakFee) GetErrorInfo(ctx context.Context) error {
	return nil
}

// Fetch 判断基础开城状态后请求出租车sps
func (t *TaxiPeakFee) Fetch(ctx context.Context, products []*models.Product) bool {
	return t.checkTaxiPeakFee(ctx, products)
}

// BuildCommonBizInfo rpc
func (t *TaxiPeakFee) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
}

// BuildProductBizInfo RPC
func (t *TaxiPeakFee) BuildProductBizInfo(ctx context.Context, product models.Product,
	privateBizInfo *models.PrivateBizInfo) {
	if product_id.ProductIdUnitaxi == product.ProductID && t.IsTaxiPeakFee == isTaxiPeakFee {
		taxiSps, ok := t.Sps[product.ProductCategory]
		if !ok {
			return
		}
		privateBizInfo.TaxiSps = taxiSps
		privateBizInfo.IsTaxiPeakFeeUserSelected = t.getPeakFeeUserSelectRes(ctx, product)
		privateBizInfo.IsSwitchIntel = t.checkSwitchIntel(ctx, &product)
	}
}

func (t *TaxiPeakFee) checkTaxiPeakFeeSps(ctx context.Context, filterProducts []*models.Product) error {
	reqList := make([]*Sps.ReqItem, 0)
	for _, product := range filterProducts {
		reqList = append(reqList, &Sps.ReqItem{
			PassengerId:     util.ToInt64(t.PID),
			City:            util.ToInt32(t.City),
			CountyId:        util.ToInt32(t.CountyID),
			ProductId:       util.ToInt32(product.ProductID),
			CarLevel:        product.RequireLevel,
			ComboType:       util.ToInt32(product.ComboType),
			ProductCategory: util.ToInt32(product.ProductCategory),
			DepartureTime:   t.DepartureTime,
			ServiceList: []*Sps.ServiceItem{{
				ServiceId: TaxiPeakFeeServiceID,
				Count:     1,
			}},
			OrderType:   t.OrderType,
			AccessKeyId: util.ToInt32(t.AccessKeyID),
			AppVersion:  t.AppVersion,
		})
	}

	req := &Sps.GetCustomedServiceFeeInfoReq{
		ReqList: reqList,
	}
	resp, err := hestia_charge.SpsClient.GetCustomedServiceFeeInfo(ctx, req)
	if err != nil || resp.Errno != 0 {
		return err
	}
	for _, feeInfo := range resp.GetData().GetFeeInfo() {
		if feeInfo == nil {
			continue
		}
		for _, feeItem := range feeInfo.GetFeeList() {
			if feeItem == nil {
				continue
			}
			if feeItem.ServiceId != TaxiPeakFeeServiceID {
				continue
			}
			spsData := new(hestia_charge.TaxiSpsData)
			if err = jsoniter.UnmarshalFromString(feeItem.GetExtraInfo(), &spsData); err != nil {
				return err
			}
			if t.Sps != nil {
				spsData.HasFee = consts.HasFee
				t.Sps[int64(feeInfo.ProductCategory)] = spsData
			}
		}
	}
	return nil
}

// check方法全部返回true为需要过滤场景
func (t *TaxiPeakFee) checkTaxiPeakFee(ctx context.Context, products []*models.Product) bool {

	// 检查聚合模式和展示预估价
	// 峰期加价与盒子||非展示预估价（打表计价）任一场景都互斥
	filterProducts := make([]*models.Product, 0)

	for _, product := range products {
		//必须是出租车
		if product_id.ProductIdUnitaxi != product.ProductID {
			continue
		}
		// 检查峰期加价api侧流量配置以及开关
		config := TaxiPeakFeeConfig{}
		if config.checkConfig(ctx, t, product) {
			continue
		}
		filterProducts = append(filterProducts, product)
	}
	err := t.checkTaxiPeakFeeSps(ctx, filterProducts)
	if err != nil {
		return false
	}

	if len(t.Sps) > 0 {
		t.IsTaxiPeakFee = isTaxiPeakFee
	}

	return true

}

func (t *TaxiPeakFee) isNewVersion(ctx context.Context) bool {
	return apollo.FeatureToggle(ctx, "passenger_taxi_peak_fee_switch", t.PID, map[string]string{
		"pid":           t.PID,
		"passenger_id":  t.PID,
		"access_key_id": t.AccessKeyID,
		"app_version":   t.AppVersion,
		"city":          t.City,
		"op":            "switch",
	})
}

// check方法全部用true过滤
func (config *TaxiPeakFeeConfig) checkConfig(ctx context.Context, t *TaxiPeakFee, product *models.Product) bool {

	condition := model.NewCondition(map[string]string{
		"city_id":          "0",
		"product_category": cast.ToString(product.ProductCategory),
	})

	configJ, err := apollo.GetConfigsByNamespaceAndConditions(ctx, apolloConfigNS, condition)
	if err != nil {
		return true
	}
	configList := []*TaxiPeakFeeConfig{
		config,
	}
	err = json.Unmarshal(configJ, &configList)
	if err != nil {
		return true
	}

	// 未开城直接返回
	if config.OpenStatus != isTaxiPeakFee {
		return true
	}

	// 限定渠道
	if !inArray(config.OpenRules.AccessKeyID, t.AccessKeyID) {
		return true
	}

	// 检查区县
	if config.checkCounty(t) {
		return true
	}

	// 检查灰度
	if config.checkToggle(ctx, t, product) {
		return true
	}
	return false
}

func (config *TaxiPeakFeeConfig) checkCounty(t *TaxiPeakFee) bool {
	if config.OpenRules.CountyInfo.SupportType == 1 && !inArray(config.OpenRules.CountyInfo.CountyList, t.CountyID) {
		return true
	}

	if config.OpenRules.CountyInfo.SupportType == 2 && !(t.ToCountyID == t.CountyID || inArray(config.OpenRules.CountyInfo.CountyList, t.CountyID)) {
		return true
	}
	return false
}

func (config *TaxiPeakFeeConfig) checkToggle(ctx context.Context, t *TaxiPeakFee, product *models.Product) bool {
	if config.OpenRules.LaunchApolloName == "" {
		return false
	}
	// 检查峰期加价灰度
	params := map[string]string{
		"phone":            t.Phone,
		"city":             t.City,
		"access_key_id":    t.AccessKeyID,
		"product_category": cast.ToString(product.ProductCategory),
		"app_version":      t.AppVersion,
	}

	if apollo.FeatureToggle(ctx, config.OpenRules.LaunchApolloName, t.PID, params) {
		return false
	}
	return true
}

func inArray(confList []string, item string) bool {
	for _, v := range confList {
		if v == item {
			return true
		}
	}

	return false
}

func (t *TaxiPeakFee) getPeakFeeUserSelectRes(ctx context.Context, product models.Product) bool {
	if product_id.ProductIdUnitaxi != product.ProductID || t.IsTaxiPeakFee != isTaxiPeakFee || t.Sps == nil {
		return false
	}
	spsData, ok := t.Sps[product.ProductCategory]
	if !ok || spsData.HasFee != consts.HasFee {
		return false
	}

	if spsData.CanSelect != formInteractive {
		return false
	}

	// 非主表单都是无出口，mock乘客勾选
	if t.PageType != page_type.PageTypeUndefined || t.SourceId != source_id.SourceIDCompositeTravel85 {
		return true
	}

	// 预约单无出口，模拟乘客勾选
	if t.OrderType == order.OrderTypeBooking {
		return true
	}

	// 实时单，首次根据配置是否采用新勾选策略默勾，非首次取乘客勾选
	if t.OrderType == order.OrderTypeNow {
		if t.MultiRequireProduct == nil || len(t.MultiRequireProduct) == 0 {
			return true
		}
		for _, p := range t.MultiRequireProduct {
			if p.ProductCategory == product.ProductCategory {
				features := make([]*models.FeatureItem, 0)
				_ = json.Unmarshal([]byte(p.CustomFeature), &features)
				for _, feature := range features {
					if feature.ID == TaxiPeakFeeServiceID && feature.Count > 0 {
						return true
					}
				}
			}
		}

	}
	return false
}

// checkSwitchIntel 是否切换到峰期智能出价
func (t *TaxiPeakFee) checkSwitchIntel(ctx context.Context, product *models.Product) bool {
	return apollo.FeatureToggle(ctx, "TaxiPeakFeeTransferToggle", t.PID, map[string]string{
		"key":          t.PID,
		"pid":          t.PID,
		"city":         t.City,
		"phone":        t.Phone,
		"county_id":    t.CountyID,
		"combo_type":   cast.ToString(product.ComboType),
		"carpool_type": cast.ToString(product.CarpoolType),
	})
}
