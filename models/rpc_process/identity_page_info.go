package rpc_process

import (
	"context"
	"encoding/json"
	"errors"

	ticketApi "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ticket_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"

	dirpcSdkBrick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	Horae "git.xiaojukeji.com/dirpc/dirpc-go-http-Horae"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/security"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/brick"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/horae"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	IdentityPageTypeCaller = "mamba-estimate"
)

// RouterDetailRPC 大车班车站点模式路线推荐与库存查询
type identityPageRPC struct {
	baseReq *models.BaseReqData

	req                 *proto.IntercityEstimateDetailRequest
	identityPageInfo    *dirpcSdkBrick.IdentityPageInfoData
	passengerHistoryMap map[string]*dirpcSdkBrick.IdentityItem
}

// NewIdentityPageInfo 获取身份信息
func NewIdentityPageInfo(req *proto.IntercityEstimateDetailRequest, baseReq *models.BaseReqData) *identityPageRPC {
	return &identityPageRPC{
		req:     req,
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (m *identityPageRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (ip *identityPageRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	if ip.baseReq != nil && info != nil && len(info.SeatDetailInfo) > 0 {
		// 更新携童占不占座信息
		var carpoolSeatNum int32
		var carryChildrenNum int32

		for _, seatDetail := range info.SeatDetailInfo {
			if seatDetail == nil {
				continue
			}

			if seatDetail.PassengerType == seat_selection_consts.CarryChildren.ToInt32() {
				carryChildrenNum += seatDetail.PassengerCount
				seatDetail.IsOccupySeat = ip.getCarryChildrenIsOccupySeat(&product, 0)
				info.CarryChildrenIsOccupySeat = seatDetail.IsOccupySeat
			}

			if seatDetail.IsOccupySeat == seat_selection_consts.IsOccupy.ToInt32() {
				carpoolSeatNum += seatDetail.PassengerCount
			}
		}

		info.CarpoolSeatNum = carpoolSeatNum
		info.CarryChildrenNum = carryChildrenNum
	}
}

// BuildCommonBizInfo ...
func (ip *identityPageRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.IdentityPageInfo = ip.identityPageInfo
	info.CarryChildrenIsOccupySeat = ip.getCarryChildrenIsOccupySeat(nil, info.CarryChildrenIsOccupySeat)
}

// Fetch 获取/加载
func (ip *identityPageRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		targetProduct *models.Product

		err error
	)

	for _, product := range products {
		if product == nil {
			continue
		}
		// 大巴
		if carpool.IsIntercityStation(ctx, int(product.CarpoolType)) {
			targetProduct = product
			break
		}
	}

	if targetProduct == nil {
		return true
	}

	err = ip.fetchGetIdentityPageInfo(ctx, targetProduct)
	if err != nil {
		return false
	}

	err = ip.fetchSetCheckedIdentity(ctx, targetProduct)
	if err != nil {
		return false
	}

	return true
}

// buildRequest ...
func (ip *identityPageRPC) buildGetIdentityPageInfoRequest(ctx context.Context, targetProduct *models.Product) *dirpcSdkBrick.GetIdentityPageInfoReq {
	if targetProduct == nil {
		return nil
	}

	if targetProduct.BizInfo == nil {
		return nil
	}

	return &dirpcSdkBrick.GetIdentityPageInfoReq{
		PassengerId:       ip.baseReq.PassengerInfo.PID,
		PassengerPhone:    ip.baseReq.PassengerInfo.Phone,
		Caller:            IdentityPageTypeCaller,
		BusServiceShiftId: &targetProduct.ShiftID,
		BusinessId:        &targetProduct.BusinessID,
		ComboId:           &targetProduct.BizInfo.ComboID,
	}
}

// fetchGetIdentityPageInfo ...
func (ip *identityPageRPC) fetchGetIdentityPageInfo(ctx context.Context, targetProduct *models.Product) error {
	if targetProduct == nil {
		return errors.New("target product is nil")
	}

	if targetProduct.BizInfo == nil {
		return errors.New("biz info is nil")
	}

	if targetProduct.BizInfo.RouteDetailV2 == nil {
		return errors.New("route detail is nil")
	}

	req := ip.buildGetIdentityPageInfoRequest(ctx, targetProduct)
	if req == nil {
		return errors.New("req is nil")
	}

	data, err := brick.GetIdentityPageInfo(ctx, req)
	if err != nil {
		return err
	}

	if data != nil && len(data.IdentityHistoryInfo) > 0 {
		for _, identityItem := range data.IdentityHistoryInfo {
			if identityItem.Type == seat_selection_consts.Undefined.ToInt32() {
				identityItem.Type = seat_selection_consts.Adult.ToInt32()
			}
		}
	}

	log.Trace.Infof(ctx, "fetchGetIdentityPageInfo", "history:%v", util.JustJsonEncode(data))

	ip.identityPageInfo = data

	return err
}

// setCheckedIdentity ...
func (ip *identityPageRPC) setCheckedIdentity(ctx context.Context, targetProduct *models.Product) error {
	if ip.baseReq == nil || ip.baseReq.CommonBizInfo.PassengerDetailInfo == nil || len(ip.baseReq.CommonBizInfo.PassengerDetailInfo.PassengerList) <= 0 {
		return intercity_estimate_detail.NoCheckPassenger
	}

	infoList, err := ip.buildInfoList(ctx, targetProduct)
	if err != nil {
		return err
	}

	if infoList == "" {
		return errors.New("info list is nil")
	}

	req := &ticketApi.PSetCheckedIdentityReq{
		Token:      ip.req.Token,
		EstimateId: targetProduct.EstimateID,
		InfoList:   infoList,
	}

	resp, err := ticket_api.GetTicketAPIClient().PSetCheckedIdentity(ctx, req)
	if err != nil || resp.Errno != 0 {
		return err
	}
	return nil
}

// buildSetCheckerIdentityRequest ...
func (ip *identityPageRPC) buildSetCheckerIdentityRequest(ctx context.Context, targetProduct *models.Product) (*Horae.SetCheckedIdentityReq, error) {
	if ip.baseReq == nil || ip.baseReq.CommonBizInfo.PassengerDetailInfo == nil || len(ip.baseReq.CommonBizInfo.PassengerDetailInfo.PassengerList) <= 0 {
		return nil, intercity_estimate_detail.NoCheckPassenger
	}

	infoList, err := ip.buildInfoList(ctx, targetProduct)
	if err != nil {
		return nil, err
	}

	if infoList == "" {
		return nil, errors.New("info list is nil")
	}

	return &Horae.SetCheckedIdentityReq{
		Token:      ip.req.Token,
		EstimateId: targetProduct.EstimateID,
		InfoList:   infoList,
	}, nil
}

// fetchSetCheckedIdentity ...
func (ip *identityPageRPC) fetchSetCheckedIdentity(ctx context.Context, product *models.Product) error {
	if product != nil && product.BizInfo != nil && !product.BizInfo.RouteDetailV2.IsNeedVerified() {
		return nil
	}

	if ip.identityPageInfo == nil || len(ip.identityPageInfo.IdentityHistoryInfo) <= 0 {
		return nil
	}

	// 构建历史乘车人
	ip.buildHistoryPassengerMap(ctx)

	// 根据场景构造
	ip.buildPassengerInfoByScene(ctx, product)

	// 切量调用ticket-api
	pid := ip.baseReq.PassengerInfo.PID
	phone := ip.baseReq.PassengerInfo.Phone
	allow := apollo.FeatureToggle(ctx, "station_set_checked_identity_switch", cast.ToString(pid),
		map[string]string{
			"product_id":      cast.ToString(product.ProductID),
			"passenger_id":    cast.ToString(pid),
			"passenger_phone": cast.ToString(phone),
		})

	if allow {
		err := ip.setCheckedIdentity(ctx, product)
		// 如果因为没有用户勾选导致的,直接返回成功
		if errors.Is(err, intercity_estimate_detail.NoCheckPassenger) {
			return nil
		}

		if err != nil {
			return err
		}

		return nil
	}

	req, err := ip.buildSetCheckerIdentityRequest(ctx, product)
	// 如果因为没有用户勾选导致的，不调用赫拉
	if errors.Is(err, intercity_estimate_detail.NoCheckPassenger) {
		return nil
	}

	if req == nil {
		return errors.New("req is nil")
	}

	err = horae.SetCheckedIdentity(ctx, req)
	if err != nil {
		return err
	}

	return nil
}

// buildInfoList ...
func (ip *identityPageRPC) buildInfoList(ctx context.Context, product *models.Product) (string, error) {
	var (
		passengerInfoList = make([]*models.InfoItem, 0)

		tag = "buildInfoList"
	)

	// 获取用户选择乘车人
	if ip.baseReq != nil && ip.baseReq.CommonBizInfo.PassengerDetailInfo != nil && len(ip.baseReq.CommonBizInfo.PassengerDetailInfo.PassengerList) > 0 {
		for _, passengerInfo := range ip.baseReq.CommonBizInfo.PassengerDetailInfo.PassengerList {
			if historyItem, exist := ip.passengerHistoryMap[models.GenPassengerKeyIngoreTicketType(passengerInfo.DeIdentityID, passengerInfo.IdentityType)]; exist {
				passengerInfoList = append(passengerInfoList, &models.InfoItem{
					ID:              int(historyItem.Id),
					Name:            historyItem.Name,
					IdentityType:    util.Int32String(historyItem.IdentityType),
					IdentityNo:      historyItem.IdentityNo,
					Type:            int(historyItem.Type),
					CoverIdentityNo: util.CoverStrByStart(historyItem.IdentityNo, 2, 4),
				})
			}
		}
	}

	if len(passengerInfoList) == 0 {
		log.Trace.Warnf(ctx, tag, "passenger no check")
		return "", intercity_estimate_detail.NoCheckPassenger
	}

	// 3. 加密喽
	passengerInfoData, err := json.Marshal(&passengerInfoList)
	if err != nil {
		log.Trace.Warnf(ctx, tag, "info data marshal fail, err:%v", err)
		return "", err
	}

	enStr, err := security.AesEncryptMsg(string(passengerInfoData))
	if err != nil {
		log.Trace.Warnf(ctx, tag, "aes decrypt fail, err:%v, origin:%v", err, string(passengerInfoData))
		return "", err
	}

	return enStr, err
}

// buildHistoryPassengerMap 构建历史乘车人映射
func (ip *identityPageRPC) buildHistoryPassengerMap(ctx context.Context) {
	var (
		passengerHistoryMap = make(map[string]*dirpcSdkBrick.IdentityItem)
	)

	if ip.identityPageInfo != nil && len(ip.identityPageInfo.IdentityHistoryInfo) > 0 {
		for _, passengerInfo := range ip.identityPageInfo.IdentityHistoryInfo {
			if passengerInfo == nil {
				continue
			}

			passengerHistoryMap[models.GenPassengerKeyIngoreTicketType(passengerInfo.IdentityNo, passengerInfo.IdentityType)] = passengerInfo
		}
	}

	ip.passengerHistoryMap = passengerHistoryMap
}

// buildDefaultCheck 初次预估且存在历史乘车人，需要默勾
func (ip *identityPageRPC) buildDefaultCheck(ctx context.Context, product *models.Product) {
	if ip.baseReq == nil || product == nil || product.BizInfo == nil {
		return
	}

	var (
		newPassengerList   = make([]*models.PassengerItem, 0)
		carryChildIsOccupy = seat_selection_consts.IsOccupy.ToInt32()

		logTag = "buildDefaultCheck"
	)
	defaultCheckPassenger := ip.buildHistoryPassenger(ctx, ip.baseReq, ip.identityPageInfo.IdentityHistoryInfo)
	if defaultCheckPassenger == nil {
		return
	}
	newPassengerList = append(newPassengerList, &models.PassengerItem{
		TicketType:   defaultCheckPassenger.Type,
		IdentityType: defaultCheckPassenger.IdentityType,
		IdentityName: defaultCheckPassenger.Name,
		DeIdentityID: defaultCheckPassenger.IdentityNo,
	})

	passengerDetailInfo := new(models.PassengerDetailInfo)
	passengerDetailInfo.Mode = models.RealName.ToInt32()
	passengerDetailInfo.PassengerList = newPassengerList

	carryChildIsOccupy = ip.getCarryChildrenIsOccupySeat(product, 0)

	log.Trace.Infof(ctx, logTag, "default check:%v", util.JustJsonEncode(passengerDetailInfo))

	ip.baseReq.CommonBizInfo.PassengerDetailInfo = passengerDetailInfo
	product.BizInfo.SeatDetailInfo = models.BuildSeatDetailInfoList(passengerDetailInfo, carryChildIsOccupy)
}

// buildHistoryPassenger
func (ip *identityPageRPC) buildHistoryPassenger(ctx context.Context, baseReq *models.BaseReqData, identityHistoryInfo []*dirpcSdkBrick.IdentityItem) *dirpcSdkBrick.IdentityItem {
	if ip.identityPageInfo == nil || len(ip.identityPageInfo.IdentityHistoryInfo) <= 0 {
		return nil
	}
	if !baseReq.CommonBizInfo.IsNewPageUpgrade {
		return identityHistoryInfo[0]
	}

	for _, item := range identityHistoryInfo {
		if item.Expired == intercity_estimate_detail.ChildIdExpired {
			continue
		}
		// 拿到非过期的第一个人进行check
		return item
	}
	return nil
}

func (ip *identityPageRPC) buildPassengerInfoByScene(ctx context.Context, product *models.Product) {
	if ip.baseReq == nil {
		return
	}

	if ip.baseReq.CommonBizInfo.PassengerDetailInfo == nil && len(ip.passengerHistoryMap) > 0 {
		// 情况1：用户第一次预估且历史乘车人
		// 默勾第一个
		ip.buildDefaultCheck(ctx, product)
	} else if ip.baseReq.CommonBizInfo.PassengerDetailInfo != nil && len(ip.baseReq.CommonBizInfo.PassengerDetailInfo.PassengerList) <= 0 {
		// 情况2：用户手动取消了所有历史乘车人
		// 不处理
	} else if ip.baseReq.CommonBizInfo.PassengerDetailInfo != nil {
		// 情况3：用户勾选了乘车人
		// 校验端传上来的数据
		ip.checkPassengerInfo(ctx, product)
	}
}

// checkPassengerInfo ...
func (ip *identityPageRPC) checkPassengerInfo(ctx context.Context, product *models.Product) {
	if ip.baseReq == nil || ip.baseReq.CommonBizInfo.PassengerDetailInfo == nil || product == nil || product.BizInfo == nil {
		return
	}

	var (
		logTag             = "checkPassengerInfo"
		newPassengerList   = make([]*models.PassengerItem, 0)
		carryChildIsOccupy = seat_selection_consts.IsOccupy.ToInt32()
	)

	for _, passengerItem := range ip.baseReq.CommonBizInfo.PassengerDetailInfo.PassengerList {
		if passengerItem == nil {
			continue
		}

		// 携童不需要校验
		if passengerItem.TicketType == seat_selection_consts.CarryChildren.ToInt32() {
			newPassengerList = append(newPassengerList, passengerItem)
			continue
		}

		if historyInfo, exist := ip.passengerHistoryMap[models.GenPassengerKeyIngoreTicketType(passengerItem.DeIdentityID, passengerItem.IdentityType)]; exist {
			// 不支持儿童票的班次，前端回传的passenger—info 儿童票type是2，需要根据brick 获取到乘客类型进行修正
			passengerItem.TicketType = historyInfo.Type
			newPassengerList = append(newPassengerList, passengerItem)
		} else {
			log.Trace.Infof(ctx, logTag, "history not exist the passenger, %v", util.JustJsonEncode(passengerItem))
		}
	}

	//if product.BizInfo.RouteDetailV2 != nil {
	//	carryChildIsOccupy = util.Bool2I32(product.BizInfo.RouteDetailV2.IsChildOccupy())
	//}
	carryChildIsOccupy = ip.getCarryChildrenIsOccupySeat(product, 0)

	ip.baseReq.CommonBizInfo.PassengerDetailInfo.PassengerList = newPassengerList
	product.BizInfo.SeatDetailInfo = models.BuildSeatDetailInfoList(ip.baseReq.CommonBizInfo.PassengerDetailInfo, carryChildIsOccupy)
	product.BizInfo.CarpoolSeatNum = models.BuildCarpoolSeatNumBySeatDetailInfo(product.BizInfo.SeatDetailInfo)
}

// getCarryChildrenIsOccupySeat ...
func (ip *identityPageRPC) getCarryChildrenIsOccupySeat(product *models.Product, defaultOccupy int32) int32 {
	if ip.identityPageInfo != nil {
		ruleInfo := ip.identityPageInfo.RuleInfo
		if ruleInfo != nil && len(ruleInfo.SpecialSeatRules) > 0 {
			for _, rules := range ruleInfo.SpecialSeatRules {
				if rules == nil {
					continue
				}

				if rules.RuleName == seat_selection_consts.CarryChildTicket && rules.IsSupport == 1 {
					return int32(rules.OccupySeat)
				}

			}
		}
	}
	// 兼容老逻辑 从prfs路线获取
	if product != nil && product.BizInfo != nil && product.BizInfo.RouteDetailV2 != nil {
		return util.Bool2I32(product.BizInfo.RouteDetailV2.IsChildOccupy())
	}

	return defaultOccupy
}
