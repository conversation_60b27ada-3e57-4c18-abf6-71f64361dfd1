package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/shift_data"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/carpoolstation"
	trace "git.xiaojukeji.com/lego/context-go"
)

type BusRobinInfo struct {
	baseReq    *models.BaseReqData
	req        *proto.IntercityEstimateDetailRequest
	commonData *model.CommonData

	busRobinData *carpoolstation.BusServerShift
}

func NewBusRobinInfo(req *proto.IntercityEstimateDetailRequest, baseReq *models.BaseReqData, commonData *model.CommonData) *BusRobinInfo {
	return &BusRobinInfo{
		baseReq:    baseReq,
		req:        req,
		commonData: commonData,
	}
}

func (b *BusRobinInfo) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (b *BusRobinInfo) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
}

func (b *BusRobinInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if b.busRobinData != nil {
		info.BusShiftInventoryRobinData = map[string]*carpoolstation.BusServerShift{b.busRobinData.BusServiceShiftID: b.busRobinData}
	}
}

func (b *BusRobinInfo) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	if b.commonData == nil || b.commonData.Quotation == nil || b.commonData.Quotation.ShiftId == nil {
		return true
	}
	data, err := shift_data.GetShiftData(ctx, *b.commonData.Quotation.ShiftId)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "BusRobinInfo get robin data error: %v", err)
		return true
	}
	if temp, ok := data.(carpoolstation.BusServerShift); ok {
		b.busRobinData = &temp
	}
	return true
}
