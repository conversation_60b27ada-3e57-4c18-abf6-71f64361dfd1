package rpc_process

import (
	"context"

	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
)

type UfsFeature struct {
	reqTuples    []*FeatureReqTuple
	featureReqs  []ufsClient.Feature
	featureKVMap map[string]string
}

type FeatureReqTuple struct {
	Domain    string
	Key       string
	ParamKeys []string // 所有param key定义成常量，以便后续识别
	Filters   []func(*biz_runtime.ProductInfoFull) bool
}

func NewUfsFeature(_ *models.CommonInfo, reqTuples []*FeatureReqTuple) *UfsFeature {
	return &UfsFeature{
		reqTuples:    reqTuples,
		featureReqs:  make([]ufsClient.Feature, 0),
		featureKVMap: map[string]string{},
	}
}

func (rp *UfsFeature) loadFeatureReqs(productFulls []*biz_runtime.ProductInfoFull) {
	for _, prod := range productFulls {
		if prod == nil {
			continue
		}

		for _, tuple := range rp.reqTuples {
			if tuple == nil {
				continue
			}

			// filter校验：满足filter才添加req
			if tuple.Filters != nil {
				allowByFilter := true
				for _, filter := range tuple.Filters {
					if !allowByFilter {
						break
					}

					if !filter(prod) {
						allowByFilter = false
					}
				}
				if !allowByFilter {
					continue
				}
			}

			// 加载params
			params := map[string]string{}
			for _, param := range tuple.ParamKeys {
				switch param {
				case ufs.FeatureParamKey_EID:
					params[ufs.FeatureParamKey_EID] = prod.GetEstimateID()
				}
			}

			rp.featureReqs = append(rp.featureReqs, ufs.GenFeature(tuple.Domain, tuple.Key, params))
		}
	}
}

func (rp *UfsFeature) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	rp.loadFeatureReqs(productFulls)

	resp, err := ufs.MultiGetFeatures(ctx, rp.featureReqs, "")
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "MultiGetFeatures failed. err = %s", err.Error())
		return false
	}

	for k, v := range resp {
		if v == nil {
			continue
		}

		if v.Errno != 0 || v.Value == nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "MultiGetFeatures single feature failed. key=%s, value=%+v", k, v)
			continue
		}

		rp.featureKVMap[k] = *v.Value
	}

	return true
}

func (rp *UfsFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

func (rp *UfsFeature) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	// TODO 后续可以按需分拣
	info.UfsFeatures = rp.featureKVMap
}

func ProductCategoryFilter(pcIds []int64) func(*biz_runtime.ProductInfoFull) bool {
	pcIdSet := map[int64]struct{}{}

	for _, id := range pcIds {
		pcIdSet[id] = struct{}{}
	}

	return func(full *biz_runtime.ProductInfoFull) bool {
		curPcId := full.GetProductCategory()
		if _, exit := pcIdSet[curPcId]; exit {
			return true
		}

		return false
	}
}
