package rpc_process

import (
	"context"
	"encoding/json"
	Geofence "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Geofence"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/fence"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/entity"
	"github.com/spf13/cast"
)

type BargainLogic struct {
	baseReq         *models.BaseReqData // 请求信息
	version         string
	EstimateTraceID string

	StartFenceList []int64             `json:"start_fence_id"` //起点围栏列表
	StopFenceList  []int64             `json:"stop_fence_id"`  //终点围栏列表
	FixPrice       *entity.FixPrice    // 改价后费用
	SenseConfig    *entity.SenseConfig // 命中场景配置
}

// NewBargainInfoLogic 司乘议价
func NewBargainInfoLogic(ctx context.Context, estimateTraceID string, req *models.BaseReqData, version string) *BargainLogic {
	return &BargainLogic{
		EstimateTraceID: estimateTraceID,
		baseReq:         req,
		version:         version,
	}
}

func (f *BargainLogic) AfterDdsFilter(ctx context.Context, products []*models.Product) (removeProductList []models.ProductCategory, reason string) {
	if "v1" == f.version {
		// 旧版等待应答直接过滤司乘议价
		return []models.ProductCategory{estimate_pc_id.EstimatePcIdBargain}, ""
	}

	if f.baseReq.SendOrder.UnderWaterBargainOrder {
		// 水下单过滤
		return []models.ProductCategory{estimate_pc_id.EstimatePcIdBargain}, ""
	}

	return removeProductList, ""
}

func (f *BargainLogic) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (f *BargainLogic) Fetch(ctx context.Context, products []*models.Product) bool {
	if "v1" == f.version {
		// 旧版等待应答直接过滤司乘议价
		return false
	}

	if len(products) == 0 {
		return false
	}

	var product *models.Product
	for _, p := range products {
		if estimate_pc_id.EstimatePcIdBargain == p.ProductCategory {
			product = p
			break
		}
	}
	if product == nil {
		return false
	}

	feature, err := ufs.GetFeatureV2(ctx, ufs.DomainOrder, "bargain_adjust_price.fee_info", map[string]string{
		"order_id": util.ToString(f.baseReq.SendOrder.OrderIdLow)},
	)
	if err == nil && len(feature) > 0 {
		err2 := json.Unmarshal([]byte(feature), &f.FixPrice)
		if err2 == nil {
		}
	}

	feature2, err2 := ufs.GetFeatureV2(ctx, ufs.DomainPassenger, "bargain_estimate.margin_fee", map[string]string{
		"trace_id": util.ToString(f.EstimateTraceID)},
	)
	if err2 == nil && len(feature2) > 0 {
		err3 := json.Unmarshal([]byte(feature2), &f.SenseConfig)
		if err3 == nil {
		}
	}

	mapType := cast.ToInt(f.baseReq.AreaInfo.MapType)
	cds := []*Geofence.Coordinate{{Lat: f.baseReq.AreaInfo.FromLat, Lng: f.baseReq.AreaInfo.FromLng},
		{Lat: f.baseReq.AreaInfo.ToLat, Lng: f.baseReq.AreaInfo.ToLng}}
	hitFenceList, err := fence.MultiInFence(ctx, cds, []int32{2233}, mapType)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "MultiInFence err=[%v]", err)
		return false
	}

	if hitFenceList[0] != nil {
		for _, fenceBean := range hitFenceList[0] {
			f.StartFenceList = append(f.StartFenceList, fenceBean.FenceIds...)
		}
	}
	if hitFenceList[1] != nil {
		for _, fenceBean := range hitFenceList[1] {
			f.StopFenceList = append(f.StopFenceList, fenceBean.FenceIds...)
		}
	}

	return true
}

// BuildCommonBizInfo RPC
func (f *BargainLogic) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
}

// BuildProductBizInfo RPC
func (f *BargainLogic) BuildProductBizInfo(ctx context.Context, product models.Product, privateBizInfo *models.PrivateBizInfo) {
	if estimate_pc_id.EstimatePcIdBargain == product.ProductCategory {
		// FillBargainFence
		privateBizInfo.BargainData.StartFenceList = f.StartFenceList
		privateBizInfo.BargainData.StopFenceList = f.StopFenceList

		if f.SenseConfig != nil && len(f.SenseConfig.ConfigID) > 0 {
			privateBizInfo.BargainData.SenseConfig = f.SenseConfig
		}
		// 改价后费用
		if f.FixPrice != nil && len(f.FixPrice.EstimateFee) > 0 {
			privateBizInfo.BargainData.FixPrice = f.FixPrice
		}

	}

}
