package rpc_process

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"

	utilhelper "git.xiaojukeji.com/gulfstream/beatles-common/util"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
)

const (
	DefaultSelectedCompensation = "default_selected_compensation"
	NoAnswerCompensation        = "no_answer_compensation"
)

type CompensationAbilityDetailInfo struct {
	baseReq         *models.BaseReqData
	compensationReq *Compensation.GetCompensationAbilityDetailReq
	orderInfo       *dos.OrderInfo
	resp            map[string]*Compensation.CompensationAbilityDetail
}

func NewCompensationAbilityDetail(ctx context.Context, generator *models.BaseReqData, orderInfo *dos.OrderInfo) *CompensationAbilityDetailInfo {
	req := &Compensation.GetCompensationAbilityDetailReq{}

	return &CompensationAbilityDetailInfo{generator, req, orderInfo, nil}
}

func (rp *CompensationAbilityDetailInfo) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (rp *CompensationAbilityDetailInfo) Fetch(ctx context.Context, products []*models.Product) bool {
	rp.compensationReq.OrderId = util.StringPtr(rp.orderInfo.OrderId)                                   // 低位订单号
	rp.compensationReq.CompensationBusinesses = util.StringPtr(DefaultSelectedCompensation)             // 这个用,连接多个business
	rp.compensationReq.BubbleTraceId = util.StringPtr(rp.orderInfo.ExtendFeatureParsed.EstimateTraceID) // 预估id
	rp.compensationReq.OrderType = util.StringPtr(rp.orderInfo.Type)                                    // 订单类型
	rp.compensationReq.PassengerId = util.StringPtr(util.ToString(rp.baseReq.PassengerInfo.PID))        // pid
	rp.compensationReq.AccessKeyId = util.Int32Ptr(rp.baseReq.CommonInfo.AccessKeyID)                   // 端来源
	rp.compensationReq.AppVersion = util.StringPtr(rp.baseReq.CommonInfo.AppVersion)                    // app版本
	rp.compensationReq.Area = util.Int32Ptr(rp.baseReq.AreaInfo.Area)                                   // 城市
	if rp.orderInfo.ReassignLastOrderId != "0" {
		lastOrderID, _ := utilhelper.GenLowIntOrderID(cast.ToInt64(rp.orderInfo.ReassignLastOrderId)) // decode
		rp.compensationReq.LastOrderId = util.StringPtr(util.ToString(lastOrderID))                   // 改派上一单
	}

	resp := compensation.GetCompensationAbilityDetail(ctx, rp.compensationReq)
	if resp == nil {
		return false
	}

	if resp.Errno != 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get compensation ability detail error with errno %d", resp.Errno)
	}

	rp.resp = resp.Data
	return true
}

func (rp *CompensationAbilityDetailInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	var (
		compensationAbilityDetail   *Compensation.CompensationAbilityDetail
		defaultCompensationContents *models.DefaultSelectedCompensationContents
	)

	if rp.resp == nil {
		return
	}

	// 默勾无车赔
	if compensationAbilityDetail = rp.resp[DefaultSelectedCompensation]; compensationAbilityDetail != nil {
		if compensationAbilityDetail.CompensationContents == "" {
			return
		}

		err := json.Unmarshal([]byte(compensationAbilityDetail.CompensationContents), &defaultCompensationContents)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "defaultSelectedCompensationInfo unmarshal error")
			return
		}

		info.DefaultSelectedCompensation = new(models.DefaultSelectedCompensation)
		info.DefaultSelectedCompensation.Decision = compensationAbilityDetail.Decision
		info.DefaultSelectedCompensation.IsAddInsure = compensationAbilityDetail.IsAddInsure
		info.DefaultSelectedCompensation.Contents = defaultCompensationContents
	}

	return
}

func (rp *CompensationAbilityDetailInfo) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}
