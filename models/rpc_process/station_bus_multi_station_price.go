package rpc_process

import (
	"context"
	"errors"
	"time"

	dirpcsdkticketprice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	pool2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/pool"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ticket_price"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

// StationBusMultiStationPriceRPC 批量获取阶梯定价
type StationBusMultiStationPriceRPC struct {
	baseReq          *models.BaseReqData
	stationPriceData []*dirpcsdkticketprice.StationPriceData
}

// NewStationBusMultiStationPriceRPC 批量获取阶梯定价
func NewStationBusMultiStationPriceRPC(baseReq *models.BaseReqData) *StationBusMultiStationPriceRPC {
	return &StationBusMultiStationPriceRPC{
		baseReq: baseReq,
	}
}

// GetErrorInfo 获取rpc错误信息
func (rd *StationBusMultiStationPriceRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (rd *StationBusMultiStationPriceRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	//根据班次匹配到对应的价格信息
	for _, value := range rd.stationPriceData {
		if product.ShiftID == value.BusShiftId {
			product.BizInfo.IntercityData.StationBusStationPrice = value
		}
	}

}

func (rd *StationBusMultiStationPriceRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

// Fetch 获取/加载
func (rd *StationBusMultiStationPriceRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	stationPriceReqList := make([]*dirpcsdkticketprice.PGetStationPriceReq, 0)
	for _, product := range products {
		if product == nil {
			continue
		}
		stationPriceReq := &dirpcsdkticketprice.PGetStationPriceReq{
			ProductId:  product.ProductID,
			ComboType:  product.ComboType,
			CarLevel:   product.RequireLevel,
			Role:       2,
			District:   rd.baseReq.AreaInfo.District,
			BusShiftId: product.ShiftID,
			ComboId:    product.BizInfo.ComboID,
		}
		stationPriceReqList = append(stationPriceReqList, stationPriceReq)
	}
	rd.processHandler(ctx, stationPriceReqList)
	return true
}

// 并发请求 每次请求20个 下游支持的单次最大请求数为20
func (rd *StationBusMultiStationPriceRPC) processHandler(ctx context.Context, params []*dirpcsdkticketprice.PGetStationPriceReq) {
	paramGroups := rd.splitGroup(params)
	pool := pool2.NewPoolByTimeout(ctx, 1000*time.Millisecond)
	for _, param := range paramGroups {
		pool.AddTask(&pool2.Task{
			Req:     param,
			RunTask: getStationBusMultiStationPrice,
		})
	}
	_, err := pool.MultiProc()
	if err != nil {
		log.Trace.Warnf(ctx, "MultiStationPrice MultiProc err||%v", err.Error())
		return
	}
	results := make([]*dirpcsdkticketprice.StationPriceData, 0)
	for _, task := range pool.GetResult() {
		if task.GetErr() != nil || task.Resp == nil {
			continue
		}
		if data, ok := task.Resp.([]*dirpcsdkticketprice.StationPriceData); ok {
			results = append(results, data...)
		}
	}
	rd.stationPriceData = results

}

// 分割参数
func (rd *StationBusMultiStationPriceRPC) splitGroup(params []*dirpcsdkticketprice.PGetStationPriceReq) []*dirpcsdkticketprice.PGetMultiStationPriceReq {
	pidStr := cast.ToString(rd.baseReq.PassengerInfo.PID)
	apolloParams := map[string]string{
		"city": cast.ToString(rd.baseReq.AreaInfo.Area),
	}
	_, hitParams := apollo.GetParameters("multi_station_price_batch_size", pidStr, apolloParams)
	batchSize := 20
	if hitParams["size"] != "" {
		batchSize = cast.ToInt(hitParams["size"])
	}
	var groups []*dirpcsdkticketprice.PGetMultiStationPriceReq
	for i := 0; i < len(params); i += batchSize {
		end := i + batchSize
		if end > len(params) {
			end = len(params)
		}
		group := params[i:end]
		req := new(dirpcsdkticketprice.PGetMultiStationPriceReq)
		req.MultiRequest = group
		groups = append(groups, req)
	}
	return groups
}

// 获取站点价格
func getStationBusMultiStationPrice(ctx context.Context, input interface{}) (interface{}, error) {
	request, convertError := input.(*dirpcsdkticketprice.PGetMultiStationPriceReq)
	if !convertError {
		return nil, errors.New("PGetMultiStationPrice req convertError")
	}
	res, err := ticket_price.GetMultiStationPrice(ctx, request)
	if err != nil || res == nil || res.Data == nil || len(res.Data.MultiResponse) <= 0 {
		return nil, errors.New("PGetMultiStationPrice fail")
	}
	return res.Data.MultiResponse, nil
}
