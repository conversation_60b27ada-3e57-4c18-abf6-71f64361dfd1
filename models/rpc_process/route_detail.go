package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"github.com/spf13/cast"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
)

// routeDetailRpc 大车班车站点模式路线推荐与库存查询
type routeDetailRpc struct {
	baseReq *models.BaseReqData

	routeDetail *models.RouteDetail
}

// NewRouteDetail 模式路线推荐与库存查询
func NewRouteDetail(baseReq *models.BaseReqData) *routeDetailRpc {
	return &routeDetailRpc{
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (m *routeDetailRpc) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (rd *routeDetailRpc) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	if rd.routeDetail != nil && rd.routeDetail.ProductId == strconv.Itoa(int(product.ProductID)) {
		info.RouteDetailV2 = rd.routeDetail
	}

	if rd.baseReq != nil && info != nil && info.RouteDetailV2 != nil {
		if len(info.SeatDetailInfo) <= 0 {
			if rd.baseReq.CommonBizInfo.PassengerDetailInfo == nil {
				info.CarpoolSeatNum = 1
			}
		} else {
			var (
				carpoolSeatNum   int32
				carryChildrenNum int32
			)
			for _, seatDetail := range info.SeatDetailInfo {
				if seatDetail == nil {
					continue
				}

				if seatDetail.PassengerType == seat_selection_consts.CarryChildren.ToInt32() {
					carryChildrenNum += seatDetail.PassengerCount
					seatDetail.IsOccupySeat = util.Bool2I32(info.RouteDetailV2.IsChildOccupy())
				}

				if seatDetail.IsOccupySeat == seat_selection_consts.IsOccupy.ToInt32() {
					carpoolSeatNum += seatDetail.PassengerCount
				}
			}

			info.CarpoolSeatNum = carpoolSeatNum
			info.CarryChildrenNum = carryChildrenNum
		}
		// 公交路线赋值
		if info.RouteDetailV2.RouteExtendInfo != nil && info.RouteDetailV2.RouteExtendInfo.RouteTag == consts.BusModeRouteTag {
			info.BusMode = cast.ToString(consts.BusModeRouteTag)
		}
	}
}

// BuildCommonBizInfo ...
func (rd *routeDetailRpc) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if rd.baseReq != nil && rd.routeDetail != nil && rd.routeDetail.RouteBasicInfo != nil && len(rd.routeDetail.RouteBasicInfo.StationList) > 0 {
		for _, stationItem := range rd.routeDetail.RouteBasicInfo.StationList {
			if stationItem == nil {
				continue
			}

			if int64(stationItem.StationId) == rd.baseReq.CommonBizInfo.StartStationId {
				info.StartStation = stationItem
			}

			if int64(stationItem.StationId) == rd.baseReq.CommonBizInfo.EndStationId {
				info.EndStation = stationItem
			}
		}
		info.CarryChildrenIsOccupySeat = util.Bool2I32(rd.routeDetail.IsChildOccupy())
	}
}

// Fetch 获取/加载
func (rd *routeDetailRpc) Fetch(ctx context.Context, products []*models.Product) bool {
	if rd.baseReq == nil {
		return true
	}

	routeGroup := rd.baseReq.CommonBizInfo.RouteId

	req := rd.buildRequest(ctx, products, routeGroup)
	if req == nil {
		return false
	}

	resp := prfs.GetRouteDetail(ctx, req)
	if resp == nil || len(resp.Data) <= 0 {
		return false
	}

	if routeDetail, ok := resp.Data[int32(routeGroup)]; ok {
		routeDetailInfo := models.RouteDetail(*routeDetail)
		rd.routeDetail = &routeDetailInfo
	}

	return true
}

// buildRequest ...
func (rd *routeDetailRpc) buildRequest(ctx context.Context, products []*models.Product, routeGroup int64) *Prfs.RouteDetailReq {
	var (
		targetProduct *models.Product
	)

	for _, product := range products {
		if product == nil {
			continue
		}

		// 大巴
		if carpool.IsIntercityStation(ctx, int(product.CarpoolType)) {
			targetProduct = product
			break
		}
	}

	if targetProduct == nil {
		return nil
	}

	return &Prfs.RouteDetailReq{
		ProductId:   util.Int642String(targetProduct.ProductID),
		RouteId:     strconv.Itoa(int(routeGroup)),
		CallerScene: PRFS_CALLER_SCENE,
		StartCityId: rd.baseReq.AreaInfo.Area,
		DestCityId:  rd.baseReq.AreaInfo.ToArea,
	}
}
