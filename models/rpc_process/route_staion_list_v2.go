package rpc_process

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	// "strconv"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
)

// RouteStaionListV2RPC 站点巴士获取筛选站点列表
type RouteStaionListV2RPC struct {
	baseReq *models.BaseReqData

	stationList *Prfs.PGetOpenCityStationDataV2
}

// NewRouteStaionListV2 站点巴士获取筛选站点列表
func NewRouteStaionListV2(baseReq *models.BaseReqData) *RouteStaionListV2RPC {
	if baseReq == nil || baseReq.CommonBizInfo.StationInfo.SupportCounty != 1 {
		return nil
	}

	return &RouteStaionListV2RPC{
		baseReq: baseReq,
	}

}

func (rd *RouteStaionListV2RPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if rd.stationList == nil || len(rd.stationList.StartGroup) == 0 || len(rd.stationList.EndGroup) == 0 {
		return
	}
	fromCity := rd.stationList.StartGroup[0].Cities
	toCity := rd.stationList.EndGroup[0].Cities
	if len(fromCity) == 0 || len(toCity) == 0 {
		return
	}
	fromCountyName, toCountyName := "", ""
	if info.StationInfo.StartCountyId != 0 && info.StationInfo.StartCountyId != -1 {
		for _, v := range fromCity[0].Counties {
			if v.Countyid == info.StationInfo.StartCountyId {
				fromCountyName = v.Name
			}
		}
	}
	if info.StationInfo.EndCountyId != 0 && info.StationInfo.EndCountyId != -1 {
		for _, v := range toCity[0].Counties {
			if v.Countyid == info.StationInfo.EndCountyId {
				toCountyName = v.Name
			}
		}
	}
	info.StationListV2.FromName = fromCity[0].Name + fromCountyName
	info.StationListV2.ToName = toCity[0].Name + toCountyName
	info.StationListV2.FromCityName = fromCity[0].Name
	info.StationListV2.ToCityName = toCity[0].Name
	fromCounties := fromCity[0].Counties
	toCounties := toCity[0].Counties
	if len(fromCounties) == 0 || len(toCounties) == 0 {
		return
	}
	info.StationListV2.FromCounties = fromCounties
	info.StationListV2.ToCounties = toCounties

}

// Fetch 获取/加载
func (rd *RouteStaionListV2RPC) Fetch(ctx context.Context) bool {

	req := &Prfs.PGetOpenCityStationV2Req{
		StartCityId:    rd.baseReq.AreaInfo.City,
		EndCityId:      rd.baseReq.AreaInfo.ToArea,
		StartCountyId:  rd.baseReq.CommonBizInfo.StationInfo.StartCountyId,
		EndCountyId:    rd.baseReq.CommonBizInfo.StationInfo.EndCountyId,
		StartStationId: int32(rd.baseReq.CommonBizInfo.StationInfo.StartStationId),
		EndStationId:   int32(rd.baseReq.CommonBizInfo.StationInfo.EndStationId),
		PassengerId:    rd.baseReq.GenDDSProductsReq().UserInfo.Pid,
		Dchn:           rd.baseReq.CommonInfo.Dchn,
	}

	if rd.baseReq.CommonBizInfo.StationInfo.RouteId != 0 {
		req.RouteId = proto.StrPtr(util.Int32String(rd.baseReq.CommonBizInfo.StationInfo.RouteId))
	}

	resp := prfs.GetCityStationV2(ctx, req)
	if resp == nil || resp.Data == nil {
		return false
	}
	rd.stationList = resp.Data

	return true
}
