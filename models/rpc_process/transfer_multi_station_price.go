package rpc_process

import (
	"context"
	"errors"
	Dirpc_SDK_TicketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	TripCloudPassenger "git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassenger"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	pool2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/pool"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ticket_price"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"time"
)

// MultiStationPriceRPC 批量获取阶梯定价
type TransferMultiStationPriceRPC struct {
	baseReq          *models.BaseReqData
	hitNewLink       bool
	hitDiff          bool
	stationPriceData []*Dirpc_SDK_TicketPrice.StationPriceData
}

// NewTransferGetMultiStationPrice 批量获取阶梯定价
func NewTransferGetMultiStationPrice(baseReq *models.BaseReqData, hitNewLink bool, hitDiff bool) *TransferMultiStationPriceRPC {
	return &TransferMultiStationPriceRPC{
		baseReq:    baseReq,
		hitNewLink: hitNewLink,
		hitDiff:    hitDiff,
	}
}

// 获取rpc错误信息
func (rd *TransferMultiStationPriceRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (rd *TransferMultiStationPriceRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	//根据班次匹配到对应的价格信息
	tcStationPriceData := new(TripCloudPassenger.StationPriceDatas)
	for _, value := range rd.stationPriceData {
		if product.ShiftID == value.BusShiftId {
			//转化结果
			tcStationPriceData.IsPrice = value.IsPrice
			tcStationPriceData.Price = value.Price
			tcStationPriceData.BusShiftId = value.BusShiftId
			itemList := make([]*TripCloudPassenger.StationPriceItem, 0)
			for _, stationPrice := range value.StationPrice {
				item := new(TripCloudPassenger.StationPriceItem)
				item.From = stationPrice.From
				item.To = stationPrice.To
				item.Price = stationPrice.Price
				itemList = append(itemList, item)
			}
			tcStationPriceData.StationPrice = itemList

		}
	}
	if rd.hitNewLink {
		product.BizInfo.IntercityData.StationPrice = tcStationPriceData
	} else {
		//diff对比
		if rd.hitDiff {
			diffPrice(ctx, product.BizInfo.IntercityData.StationPrice, tcStationPriceData)
		}
	}

}

func (rd *TransferMultiStationPriceRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

// Fetch 获取/加载
func (rd *TransferMultiStationPriceRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	stationPriceReqList := make([]*Dirpc_SDK_TicketPrice.PGetStationPriceReq, 0)
	for _, product := range products {
		if product == nil {
			continue
		}
		stationPriceReq := &Dirpc_SDK_TicketPrice.PGetStationPriceReq{
			ProductId:  product.ProductID,
			ComboType:  product.ComboType,
			CarLevel:   product.RequireLevel,
			Role:       2,
			District:   rd.baseReq.AreaInfo.District,
			BusShiftId: product.ShiftID,
			ComboId:    product.BizInfo.ComboID,
		}
		stationPriceReqList = append(stationPriceReqList, stationPriceReq)
	}
	rd.processHandler(ctx, stationPriceReqList)
	return true
}

// 并发请求 每次请求20个 下游支持的单次最大请求数为20
func (rd *TransferMultiStationPriceRPC) processHandler(ctx context.Context, params []*Dirpc_SDK_TicketPrice.PGetStationPriceReq) {
	paramGroups := rd.splitGroup(params)
	pool := pool2.NewPoolByTimeout(ctx, 1000*time.Millisecond)
	for _, param := range paramGroups {
		pool.AddTask(&pool2.Task{
			Req:     param,
			RunTask: sendTransferMultiStationPrice,
		})
	}
	_, err := pool.MultiProc()
	if err != nil {
		log.Trace.Warnf(ctx, "MultiStationPrice MultiProc err||%v", err.Error())
		return
	}
	results := make([]*Dirpc_SDK_TicketPrice.StationPriceData, 0)
	for _, task := range pool.GetResult() {
		if task.GetErr() != nil || task.Resp == nil {
			continue
		}
		if data, ok := task.Resp.([]*Dirpc_SDK_TicketPrice.StationPriceData); ok {
			results = append(results, data...)
		}
	}
	rd.stationPriceData = results

}

func diffPrice(ctx context.Context, data1, data2 *TripCloudPassenger.StationPriceDatas) {
	if data1 == nil || data2 == nil {
		log.Trace.Warnf(ctx, "bus_multi_price_diff data is nil", "")
		return
	}
	if data1.IsPrice != data2.IsPrice {
		log.Trace.Warnf(ctx, "bus_multi_price_diff isPrice: %s", data2.BusShiftId)
	}
	if data1.Price != data2.Price {
		log.Trace.Warnf(ctx, "bus_multi_price_diff price: %s", data2.BusShiftId)
	}
	for _, v1 := range data2.StationPrice {
		for _, v2 := range data1.StationPrice {
			if v1.From == v2.From && v1.To == v2.To {
				if v1.Price != v2.Price {
					log.Trace.Warnf(ctx, "bus_multi_price_diff shiftId: %s,from: %s,to: %s", data2.BusShiftId, v1.From, v1.To)
				}
			}
		}
	}
	if len(data1.StationPrice) != len(data2.StationPrice) {
		log.Trace.Warnf(ctx, "bus_multi_price_diff len is err ,shift: %s", data2.BusShiftId)
	}
}

// 分割参数
func (rd *TransferMultiStationPriceRPC) splitGroup(params []*Dirpc_SDK_TicketPrice.PGetStationPriceReq) []*Dirpc_SDK_TicketPrice.PGetMultiStationPriceReq {
	pidStr := cast.ToString(rd.baseReq.PassengerInfo.PID)
	apolloParams := map[string]string{
		"city": cast.ToString(rd.baseReq.AreaInfo.Area),
	}
	_, hitParams := apollo.GetParameters("multi_station_price_batch_size", pidStr, apolloParams)
	batchSize := 20
	if hitParams["size"] != "" {
		batchSize = cast.ToInt(hitParams["size"])
	}
	var groups []*Dirpc_SDK_TicketPrice.PGetMultiStationPriceReq
	for i := 0; i < len(params); i += batchSize {
		end := i + batchSize
		if end > len(params) {
			end = len(params)
		}
		group := params[i:end]
		req := new(Dirpc_SDK_TicketPrice.PGetMultiStationPriceReq)
		req.MultiRequest = group
		groups = append(groups, req)
	}
	return groups
}

// 获取站点价格
func sendTransferMultiStationPrice(ctx context.Context, input interface{}) (interface{}, error) {
	request, convertError := input.(*Dirpc_SDK_TicketPrice.PGetMultiStationPriceReq)
	if !convertError {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, " PGetMultiStationPrice req convertError")
		return nil, errors.New("PGetMultiStationPrice req convertError")
	}
	res, err := ticket_price.GetMultiStationPrice(ctx, request)
	if err != nil || res == nil || res.Data == nil || len(res.Data.MultiResponse) <= 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, " PGetMultiStationPrice res is nil ")
		return nil, errors.New("PGetMultiStationPrice fail")
	}
	return res.Data.MultiResponse, nil
}
