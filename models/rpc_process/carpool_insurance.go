package rpc_process

/*
import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
)

const (
	STAGE_ETD_LATE_PAY_PRE_SALE = "pre_sale"
)

type CarpoolInsurance struct {
	instranceReq *InsuranceReq
	resp         *InsuranceResp
}

type InsuranceReq struct {
	order_id     string
	district     string
	passenger_id string
	stage        string
}

type InsuranceResp struct {
}

func NewinstranceInfo(orderInfo *dos.orderInfo, areaInfo *models.AreaInfo, passengerInfo *models.PassengerInfo) *CarpoolInsurance {
	return &CarpoolInsurance{&InsuranceReq{
		order_id:     orderInfo.OrderId,
		district:     areaInfo.District,
		passenger_id: cast.ToString(passengerInfo.PID),
		stage:        STAGE_ETD_LATE_PAY_PRE_SALE,
	}, nil}
}

func (ci *CarpoolInsurance) Fetch(ctx context.Context, productInfos []*biz_runtime.ProductInfoFull) bool {

	if resp.Errno != 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get insurance info error with errno %d", resp.Errno)
	}
	return true
}

func (ci *CarpoolInsurance) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if ci.instranceReq == nil {
		return
	}

}
func (ci *CarpoolInsurance) BuildProductBizInfo(ctx context.Context, productInfo biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if ci.instranceReq != nil {

	}
}*/
