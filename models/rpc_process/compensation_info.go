package rpc_process

import (
	"context"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/client_type"

	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
)

type CompensationInfo struct {
	compensationReq *Compensation.GetMultiCompensationAbilityReq
	resp            map[string]map[string]*Compensation.CompensationAbilityResult
}

func NewCompensationInfoRpc(commonInfo *models.CommonInfo, areaInfo *models.AreaInfo, passengerInfo *models.PassengerInfo, sendOrder *models.SendOrder, compensationBusiness string) *CompensationInfo {
	req := &Compensation.GetMultiCompensationAbilityReq{
		From:                   "mamba",
		CompensationBusinesses: &compensationBusiness,
		OrderId:                util.StringPtr(util.ToString(sendOrder.OrderIdLow)),
		District:               &areaInfo.District,
		PassengerId:            getStringPtr(strconv.FormatInt(passengerInfo.PID, 10)),
		PassengerPhone:         &passengerInfo.Phone,
		CallCarType:            &commonInfo.CallCarType,
		FromWebapp:             getIntPtrFromBool(client_type.IsFromWebApp(commonInfo.ClientType)),
		AccessKeyId:            &commonInfo.AccessKeyID,
		ClientType:             &commonInfo.ClientType,
		AppVersion:             &commonInfo.AppVersion,
		Area:                   getStringPtr(cast.ToString(areaInfo.Area)),
		ToArea:                 getStringPtr(cast.ToString(areaInfo.ToArea)),
		FromLat:                getStringPtr(cast.ToString(areaInfo.FromLat)),
		FromLng:                getStringPtr(cast.ToString(areaInfo.FromLng)),
		ToLat:                  getStringPtr(cast.ToString(areaInfo.ToLat)),
		ToLng:                  getStringPtr(cast.ToString(areaInfo.ToLng)),
	}

	return &CompensationInfo{req, nil}
}

// 获取rpc错误信息
func (m *CompensationInfo) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (rp *CompensationInfo) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		airportType        int32
		productCategoryIds = make([]string, 0)
	)

	for _, p := range products {
		if airportType == 0 {
			airportType = p.AirportType
		}
		productCategoryIds = append(productCategoryIds, strconv.FormatInt(p.ProductCategory, 10))
	}
	rp.compensationReq.AirportType = &airportType
	rp.compensationReq.ProductCategoryIds = getStringPtr(strings.Join(productCategoryIds, ","))

	resp := compensation.GetMultiCompensationAbility(ctx, rp.compensationReq)
	if resp == nil {
		return false
	}

	if resp.Errno != 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get compensation info error with errno %d", resp.Errno)
	}

	rp.resp = resp.Data
	return true
}

func (rp *CompensationInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

func (rp *CompensationInfo) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	if rp.resp != nil {
		pcId := strconv.FormatInt(product.ProductCategory, 10)
		if cInfo, exsit := rp.resp[pcId]; exsit && cInfo != nil {
			info.CompensationInfo = cInfo
		}
	}
}

func getStringPtr(str string) *string {
	return &str
}

func getIntPtrFromBool(b bool) *int32 {
	var intVal int32
	if b {
		intVal = 1
	} else {
		intVal = 0
	}
	return &intVal
}
