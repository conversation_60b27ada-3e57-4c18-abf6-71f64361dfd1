package rpc_process

import (
	"context"
	"encoding/json"
	"errors"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	pool2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/pool"
	"git.xiaojukeji.com/nuwa/trace"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
)

// PbdCtripBatchRouterDetailRPC
type PbdCtripBatchRouterDetailRPC struct {
	baseReq *models.BaseReqData

	routeDetailList map[int32]*models.RouteDetail
}

// NewPbdCtripBatchRouteDetail
func NewPbdCtripBatchRouteDetail(baseReq *models.BaseReqData) *PbdCtripBatchRouterDetailRPC {
	return &PbdCtripBatchRouterDetailRPC{
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (rd *PbdCtripBatchRouterDetailRPC) GetErrorInfo(ctx context.Context) error {
	if rd.routeDetailList == nil {
		return BizError.ErrGetRoueDetailFailed
	}
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (rd *PbdCtripBatchRouterDetailRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	if rd.routeDetailList != nil {
		info.RouteDetailV2 = rd.routeDetailList[int32(product.BizInfo.ComboID)]
	}
}

func (rd *PbdCtripBatchRouterDetailRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

// Fetch 获取/加载
func (rd *PbdCtripBatchRouterDetailRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	routeIdList := []string{}

	for _, product := range products {
		if product == nil {
			continue
		}
		if product.BizInfo != nil && product.BizInfo.ComboID != 0 {
			id := strconv.Itoa(int(product.BizInfo.ComboID))
			if !util.InArrayStr(id, routeIdList) {
				routeIdList = append(routeIdList, id)
			}
		}
	}

	if len(routeIdList) == 0 {
		return true
	}
	rd.processHandler(ctx, routeIdList)
	return true
}

// 并发请求 每次请求20个 下游支持的单次最大请求数为20
func (rd *PbdCtripBatchRouterDetailRPC) processHandler(ctx context.Context, params []string) {
	paramGroups := rd.splitGroup(ctx, params)
	pool := pool2.NewPoolByTimeout(ctx, 500*time.Millisecond)
	for _, param := range paramGroups {
		pool.AddTask(&pool2.Task{
			Req:     param,
			RunTask: batchRouterDetail,
		})
	}
	_, err := pool.MultiProc()
	if err != nil {
		log.Trace.Warnf(ctx, "CtripBatchRouterDetail MultiProc err||%v", err.Error())
		return
	}
	results := make(map[int32]*models.RouteDetail)
	for _, task := range pool.GetResult() {
		if task.GetErr() != nil || task.Resp == nil {
			continue
		}
		if data, ok := task.Resp.(map[int32]*models.RouteDetail); ok {
			results = MergeMaps(results, data)
		}
	}

	rd.routeDetailList = results
}

// 合并两个map
func MergeMaps(map1, map2 map[int32]*models.RouteDetail) map[int32]*models.RouteDetail {
	if map1 == nil {
		return map2
	}
	if map2 == nil {
		return map1
	}
	for key, value := range map2 {
		map1[key] = value
	}
	return map1
}

// 分割参数
func (rd *PbdCtripBatchRouterDetailRPC) splitGroup(ctx context.Context, params []string) []*Prfs.BatchRouteDetailReq {
	batchSize := 20
	var groups []*Prfs.BatchRouteDetailReq
	for i := 0; i < len(params); i += batchSize {
		end := i + batchSize
		if end > len(params) {
			end = len(params)
		}
		group := params[i:end]
		routeIdListStr, err := json.Marshal(group)
		if err != nil {
			log.Trace.Warnf(ctx, "PbdCtripBatchRouterDetailRPC err err||%v", err.Error())
		}
		req := &Prfs.BatchRouteDetailReq{
			RouteIdList: string(routeIdListStr),
			CallerScene: PRFS_CALLER_SCENE,
			StartCityId: rd.baseReq.AreaInfo.City,
			CarpoolType: consts.CarPoolTypeInterCityStation,
		}
		groups = append(groups, req)
	}
	return groups
}

// 获取路线明细
func batchRouterDetail(ctx context.Context, input interface{}) (interface{}, error) {
	req, convertError := input.(*Prfs.BatchRouteDetailReq)
	if !convertError {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, " PbdCtripBatchRouterDetailRPC req convertError")
		return nil, errors.New("PbdCtripBatchRouterDetailRPC req convertError")
	}
	resp := prfs.GetBatchRouteDetail(ctx, req)
	if resp == nil || len(resp.Data) <= 0 {
		return nil, errors.New("batchRouterDetail is err")
	}
	routeDetailList := make(map[int32]*models.RouteDetail)
	for key, data := range resp.Data {
		routeDetailInfo := models.RouteDetail(*data)
		routeDetailList[key] = &routeDetailInfo
	}
	return routeDetailList, nil
}
