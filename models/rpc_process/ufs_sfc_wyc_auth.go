package rpc_process

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	WycAnyCar = 372
)

type UfsSFCWYCAuthFeature struct {
	Domain      string
	PassengerID string
	AuthKey     string

	RemoveTripCloud    []models.ProductCategory
	FinalMapBusinessID map[int64]bool
}

func NewSFCWYCAuthRPC(ctx context.Context, domain string, pid string) *UfsSFCWYCAuthFeature {

	return &UfsSFCWYCAuthFeature{
		Domain:             domain,
		PassengerID:        pid,
		AuthKey:            ufs.SFCWYCAuthKey,
		RemoveTripCloud:    make([]models.ProductCategory, 0),
		FinalMapBusinessID: make(map[int64]bool),
	}
}

func (uf *UfsSFCWYCAuthFeature) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	var (
		//自营车型
		allWyc    = make([]models.ProductCategory, 0)
		searchMap = make(map[int64][]models.ProductCategory)
		// UFSFeatures UFS特征
		UFSFeatures = make([]ufsClient.Feature, 0)
		// AuthBusiness VideoBusiness key：businessID，value：是否授权
		AuthBusiness = make(map[int64]bool)
	)

	for _, p := range productFulls {
		if p.GetProductCategory() == 5 {
			allWyc = append(allWyc, models.ProductCategory(p.GetProductCategory()))
			searchMap[WycAnyCar] = append(searchMap[p.GetBusinessID()], models.ProductCategory(p.GetProductCategory()))
		}
	}

	//构建用户未授权的自营feature列表
	for businessID := range searchMap {
		feature := ufsClient.Feature{
			Domain: uf.Domain,
			Keys:   []string{uf.AuthKey},
			Params: map[string]string{
				"passenger_id": uf.PassengerID,
				"business_id":  strconv.FormatInt(businessID, 10),
			},
		}
		UFSFeatures = append(UFSFeatures, feature)
	}

	UFSResp, err := ufs.MultiGetFeatures(ctx, UFSFeatures, "")
	if err != nil {
		//若获取三方授权和录音录像授权，若状态失败，则remove所有三方车型
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get auth status from ufs fail with err %v", err)
		uf.RemoveTripCloud = allWyc
		return false
	}

	//把UFS结果映射为map
	for strFeature, ufsResp := range UFSResp {
		_, key, params, featureErr := ufsClient.ParseFeature(strFeature)
		if featureErr != nil {
			uf.RemoveTripCloud = allWyc
			return false
		}

		if key == uf.AuthKey {
			//三方品类授权状态
			businessID, authErr := strconv.ParseInt(params["business_id"], 10, 64)
			if authErr != nil || businessID == 0 {
				uf.RemoveTripCloud = allWyc
				return false
			}

			if ufsResp == nil || ufsResp.Errno != 0 || *ufsResp.Value != "1" {
				AuthBusiness[businessID] = true
			}
		}
	}

	// 1表示授权过了
	//保存未授权录音录像与三方协议的全部品类
	for _, p := range productFulls {
		if p.GetProductCategory() != 5 {
			continue
		}

		_, isAuth := AuthBusiness[WycAnyCar]
		if isAuth {
			uf.FinalMapBusinessID[WycAnyCar] = true
			continue
		}
	}

	return true
}

func (uf *UfsSFCWYCAuthFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

func (uf *UfsSFCWYCAuthFeature) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	//非三方品类不需要判定要不要授权
	if product.GetProductCategory() != 5 {
		return
	}

	info.UfsWycAuthBusinessID = uf.FinalMapBusinessID

	return
}

func (uf *UfsSFCWYCAuthFeature) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) []models.ProductCategory {
	// ufs读失败，应该不出，给remove掉
	return uf.RemoveTripCloud
}
