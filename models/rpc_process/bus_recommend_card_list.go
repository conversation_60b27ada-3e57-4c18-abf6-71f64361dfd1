package rpc_process

import (
	"context"
	"encoding/json"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Dirpc_SDK_Vcard "git.xiaojukeji.com/dirpc/dirpc-go-http-Vcard"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/vcard"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
	"strconv"
)

type RecommendBusCardList struct {
	baseReq      *models.BaseReqData
	data         *Dirpc_SDK_Vcard.PGetBusCardListData
	productFulls []*biz_runtime.ProductInfoFull
}

func NewBusRecommendCardList(baseReq *models.BaseReqData) *RecommendBusCardList {
	return &RecommendBusCardList{
		baseReq: baseReq,
	}
}

func (c *RecommendBusCardList) BuildReq(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) *Dirpc_SDK_Vcard.PGetBusCardListReq {
	if len(productFulls) == 0 {
		return nil
	}
	productFull := productFulls[0]
	ticketTypeList := make([]*Dirpc_SDK_Vcard.TicketTypeInfo, 0)
	ticketSeatInfoStr := productFull.GetTicketSeatInfo()
	ticketSeatInfoList := make([]*PriceApi.TicketDetailInfo, 0)
	if err := json.Unmarshal([]byte(ticketSeatInfoStr), &ticketSeatInfoList); err != nil {
		log.Trace.Info(ctx, LogTag, "Unmarshal ticketSeatInfoStr, fail to get ticketSeatInfoList")
		return nil
	}
	for _, v := range ticketSeatInfoList {
		ticketTypeInfo := new(Dirpc_SDK_Vcard.TicketTypeInfo)
		ticketTypeInfo.Type = v.Type
		ticketTypeInfo.PassengerCount = v.PassengerCount
		ticketTypeInfo.EachFee = cast.ToInt32(util.Float64Hundred(productFull.GetBusTicketPriceByType(v.Type)))
		ticketTypeList = append(ticketTypeList, ticketTypeInfo)
	}
	req := &Dirpc_SDK_Vcard.PGetBusCardListReq{
		PassengerId:    c.baseReq.PassengerInfo.PID,
		RouteId:        cast.ToInt32(c.baseReq.CommonBizInfo.RouteId),
		DepartureTime:  c.baseReq.CommonInfo.DepartureTime,
		TicketList:     ticketTypeList,
		CheckRouteCard: 1,
		StartCityId:    c.baseReq.AreaInfo.Area,
		EndCityId:      c.baseReq.AreaInfo.ToArea,
	}
	return req
}
func (c *RecommendBusCardList) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	const (
		LogTag = "vcard_card_list"
	)
	productFull := productFulls[0]
	//1、是否展示
	if !productFull.GetBizInfo().BusCardIsShow {
		log.Trace.Info(ctx, LogTag, "no hit BusCardIsShow, fail to get card list")
		return true
	}
	//2、只支持通用收银台
	if !apollo.FeatureToggle(ctx, "inter_carpool_station_common_cashier_switch", strconv.Itoa(int(c.baseReq.PassengerInfo.UID)), map[string]string{
		"access_key_id":   cast.ToString(c.baseReq.CommonInfo.AccessKeyID),
		"app_version":     c.baseReq.CommonInfo.AppVersion,
		"phone":           c.baseReq.PassengerInfo.Phone,
		"passenger_phone": c.baseReq.PassengerInfo.Phone,
		"city":            cast.ToString(c.baseReq.AreaInfo.Area),
		"route_id":        productFull.Product.RouteID,
		"product_id":      cast.ToString(productFull.Product.ProductID),
		"carpool_type":    cast.ToString(productFull.Product.CarpoolType),
		"channel":         cast.ToString(c.baseReq.CommonInfo.Channel),
		"caller":          "mamba",
	}) {
		log.Trace.Info(ctx, LogTag, "no hit common cashier switch, fail to get card list")
		return true
	}
	//3、命中开量开关
	if !biz_runtime.IsHitBusCardTyingSale(ctx, c.baseReq, productFull) {
		log.Trace.Info(ctx, LogTag, "no hit IsHitBusCardTyingSale, fail to get card list")
		return true
	}
	c.productFulls = productFulls
	req := c.BuildReq(ctx, productFulls)
	if req == nil {
		log.Trace.Info(ctx, LogTag, "empty product, fail to get card list")
		return false
	}
	resp, err := vcard.PGetBusCardList(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "fetch PGetBusCardList info failed %s", err)
		return false
	}
	if resp == nil || resp.Data == nil {
		return false
	}
	c.data = resp.Data
	return true
}

// BuildCommonBizInfo RPC
func (c *RecommendBusCardList) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
}

// BuildProductBizInfo RPC
func (c *RecommendBusCardList) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if c.data != nil && len(c.data.CardList) > 0 {
		info.BusCardListData = c.data
	}
}
