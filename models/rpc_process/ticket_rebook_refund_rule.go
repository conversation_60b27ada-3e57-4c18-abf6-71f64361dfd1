package rpc_process

import (
	"context"

	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ticket_price"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/constants/rule"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
)

type TicketReBookRefundRuleInfo struct {
	baseReq    *models.BaseReqData
	req        *proto.IntercityEstimateDetailRequest
	commonData *model.CommonData

	intercityRule *ticketPrice.RuleData
}

func NewTicketReBookRefundRule(req *proto.IntercityEstimateDetailRequest, baseReq *models.BaseReqData, commonData *model.CommonData) *TicketReBookRefundRuleInfo {
	return &TicketReBookRefundRuleInfo{
		baseReq:    baseReq,
		req:        req,
		commonData: commonData,
	}
}

func (t *TicketReBookRefundRuleInfo) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if t.intercityRule != nil {
		info.IntercityRule = t.intercityRule
	}
}

func (t *TicketReBookRefundRuleInfo) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
}

func (t *TicketReBookRefundRuleInfo) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	if len(productFulls) == 0 {
		return true
	}

	product := productFulls[0]
	if product == nil {
		return true
	}
	var shiftId string
	agentType := t.baseReq.CommonBizInfo.AgentType
	if (agentType == DetailConst.QuickScanCode || agentType == DetailConst.FormatScanCode) &&
		len(t.baseReq.CommonBizInfo.BusServiceShiftId) != 0 {
		shiftId = t.baseReq.CommonBizInfo.BusServiceShiftId
	}

	if t.commonData != nil && t.commonData.Quotation != nil && t.commonData.Quotation.ShiftId != nil {
		shiftId = *t.commonData.Quotation.ShiftId
	}

	req := &ticketPrice.GetCarpoolIntercityRuleRequest{
		RuleType:          rule.CarpoolIntercityRuleTypeRefundAndRebook,
		BusServiceShiftId: shiftId,
		BusinessId:        cast.ToInt32(product.GetBusinessID()),
		ComboId:           cast.ToInt32(product.GetComboID()),
	}

	data, err := ticket_price.PGetCarpoolIntercityRule(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "TicketReBookRefundRuleInfo get rule data error: %v", err)
		return true
	}
	if data.Errno != cast.ToInt32(errors.Success) || data.Data == nil {
		return true
	}

	t.intercityRule = data.Data
	return true
}
