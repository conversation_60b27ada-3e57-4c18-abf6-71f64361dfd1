package rpc_process

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	context2 "git.xiaojukeji.com/lego/context-go"
)

const (
	MiniBusEvent = "mini_bus_rec"
)

type MiniBusRecommendInfo struct {
	userRecommendReq *AthenaApiv3.AthenaUserRecommendInfoReq
	resp             *AthenaApiv3.AthenaUserRecommendInfoResp
	CheckedPcIdMap   map[int64]int
}

func NewMiniBusRecommendInfoRPC(ctx context.Context, generator *models.BaseReqData) *MiniBusRecommendInfo {

	if generator == nil {
		return nil
	}

	logic := &MiniBusRecommendInfo{
		userRecommendReq: &AthenaApiv3.AthenaUserRecommendInfoReq{
			AccessKeyID:   int64(generator.CommonInfo.AccessKeyID),
			AppVersion:    generator.CommonInfo.AppVersion,
			Lang:          generator.CommonInfo.Lang,
			Phone:         generator.PassengerInfo.Phone,
			Pid:           strconv.FormatInt(generator.PassengerInfo.PID, 10),
			FromArea:      int64(generator.AreaInfo.Area),
			FromLng:       generator.AreaInfo.FromLng,
			FromLat:       generator.AreaInfo.FromLat,
			Event:         MiniBusEvent,
			BubbleTraceID: context2.GetTrace(ctx).GetTraceId(),
		},
	}

	return logic
}

func (rp *MiniBusRecommendInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

func (rp *MiniBusRecommendInfo) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if len(rp.CheckedPcIdMap) > 0 && rp.CheckedPcIdMap[product.GetProductCategory()] == consts.Checked {
		info.CheckStatus = consts.Checked
	}
}

func (rp *MiniBusRecommendInfo) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {

	log.Trace.Debugf(ctx, LogTag, "req -> %s", util.JustJsonEncode(rp.userRecommendReq))

	productRes := make([]int64, 0)

	for _, product := range productFulls {
		if product == nil {
			continue
		}
		if carpool.IsMiniBus(int(product.Product.CarpoolType)) {
			productRes = append(productRes, product.Product.ProductCategory)
		}
	}

	if len(productRes) == 0 {
		return true
	}
	rp.resp = athena.GetUserRecommendInfo(ctx, rp.userRecommendReq)

	if rp.resp == nil || rp.resp.GetMiniBusRecInfo() == nil {
		return false
	}
	if len(rp.resp.MiniBusRecInfo.CheckedProductCategory) > 0 {
		rp.CheckedPcIdMap = make(map[int64]int)
		for _, item := range rp.resp.MiniBusRecInfo.GetCheckedProductCategory() {
			rp.CheckedPcIdMap[int64(item.GetProductCategory())] = 1
		}
	}
	log.Trace.Debugf(ctx, LogTag, "response -> %s", util.JustJsonEncode(rp.resp))
	return true
}
