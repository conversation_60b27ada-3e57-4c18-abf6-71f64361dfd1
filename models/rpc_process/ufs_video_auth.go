package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type UfsVideoFeature struct {
	Domain      string
	PassengerID string
	VideoKey    string

	IsNeedAuthVideo bool
}

func NewVideoAuthRPC(ctx context.Context, pid string) *UfsVideoFeature {
	return &UfsVideoFeature{
		Domain:      ufs.DomainPassenger,
		PassengerID: pid,
		VideoKey:    ufs.VideoAuthKeyNew,
	}
}

func (uf *UfsVideoFeature) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	var (
		// UFSFeatures UFS特征
		UFSFeatures = make([]ufsClient.Feature, 0)
	)

	// 获得用户录音录像授权 feature
	feature := ufsClient.Feature{
		Domain: uf.Domain,
		Keys:   []string{uf.VideoKey},
		Params: map[string]string{
			"passenger_id": uf.PassengerID,
		},
	}

	UFSFeatures = append(UFSFeatures, feature)

	UFSResp, err := ufs.MultiGetFeatures(ctx, UFSFeatures, "")
	if err != nil {
		return false
	}

	// 把UFS结果映射为map
	for strFeature, ufsResp := range UFSResp {
		_, key, _, featureErr := ufsClient.ParseFeature(strFeature)
		if key != uf.VideoKey {
			continue
		}

		if featureErr != nil {
			return false
		}

		// 录音录像授权状态
		if ufsResp == nil || ufsResp.Errno != 0 {
			return false
		}

		if *ufsResp.Value == "" || *ufsResp.Value == "0" {
			uf.IsNeedAuthVideo = true
			return true
		}

		if *ufsResp.Value == "1" {
			uf.IsNeedAuthVideo = false
			return true
		}
	}

	return false
}

func (uf *UfsVideoFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

func (uf *UfsVideoFeature) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	info.IsNeedDefaultAuth = uf.IsNeedAuthVideo
	return
}
