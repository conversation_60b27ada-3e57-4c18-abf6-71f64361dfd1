package rpc_process

import (
	"context"
	"encoding/json"
	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ticket_price"
	"git.xiaojukeji.com/nuwa/golibs/goutils"
	"github.com/google/go-cmp/cmp"
	apolloV2 "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"strconv"
	"strings"

	TripCloudPassenger "git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassenger"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/trip_cloud_passenger"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/passenger-common/util/carpoolstation"
)

const (
	fromTripCloud = iota
	fromTicketApi
	fromDivertSelectTc
	fromDivertSelectTicket
)

const (
	TICKETPRICE   = "PGetStationPrice"
	PassengerRole = 2
)

type StationPrice struct {
	baseReq    *models.BaseReqData
	resData    *ticketPrice.StationPriceData
	tcData     *TripCloudPassenger.StationPriceData
	ticketData *ticketPrice.StationPriceData
}

func NewStationPrice(baseReq *models.BaseReqData) *StationPrice {
	return &StationPrice{
		baseReq:    baseReq,
		resData:    nil,
		tcData:     nil,
		ticketData: nil,
	}
}

func (m *StationPrice) BuildReq(products []*models.Product) *TripCloudPassenger.GetStationPriceRequest {
	if len(products) == 0 {
		return nil
	}
	req := &TripCloudPassenger.GetStationPriceRequest{
		ProductId:  products[0].ProductID,
		ComboType:  products[0].ComboType,
		CarLevel:   products[0].RequireLevel,
		Role:       PassengerRole,
		District:   m.baseReq.AreaInfo.District,
		BusShiftId: m.baseReq.CommonBizInfo.BusServiceShiftId,
		ComboId:    m.baseReq.CommonBizInfo.RouteId,
	}
	return req
}

func (m *StationPrice) Fetch(ctx context.Context, products []*models.Product) bool {
	if len(products) == 0 {
		return false
	}

	model := m.genModel(ctx, products[0])
	if res := m.getData(ctx, products, model); !res {
		return false
	}
	m.formatData(model)
	return true
}

func (m *StationPrice) GetErrorInfo(ctx context.Context) error {
	return consts.ErrorGetRoutePriceFail
}

// BuildCommonBizInfo RPC
func (m *StationPrice) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
	commonBizInfo.RoutePrice = m.resData
}

// BuildProductBizInfo RPC
func (m *StationPrice) BuildProductBizInfo(ctx context.Context, product models.Product,
	privateBizInfo *models.PrivateBizInfo) {
}

func (m *StationPrice) getTCStationBasicPrice(ctx context.Context, products []*models.Product) (bool, *TripCloudPassenger.StationPriceData) {
	const (
		LogTag = "tripcloud_route_price"
	)
	req := m.BuildReq(products)
	if req == nil {
		log.Trace.Warnf(ctx, LogTag, "empty product, fail to get route price")
		return false, nil
	}
	resp, err := trip_cloud_passenger.GetStationBasicPrice(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "fetch route price info failed %s", err)
		return false, nil
	}
	if resp == nil || resp.Data == nil {
		log.Trace.Warnf(ctx, LogTag, "fetch empty route price info")
		return false, nil
	}
	return true, resp.Data
}

func (m *StationPrice) BuildRequest(product *models.Product) *ticketPrice.PGetStationPriceReq {
	return &ticketPrice.PGetStationPriceReq{
		ProductId:  product.ProductID,
		ComboType:  product.ComboType,
		CarLevel:   product.RequireLevel,
		Role:       PassengerRole,
		District:   m.baseReq.AreaInfo.District,
		BusShiftId: m.baseReq.CommonBizInfo.BusServiceShiftId,
		ComboId:    m.baseReq.CommonBizInfo.RouteId,
	}
}

func (m *StationPrice) getTicketStationBasicPrice(ctx context.Context, product *models.Product) (bool, *ticketPrice.StationPriceData) {
	const (
		LogTag = "ticket_route_price"
	)
	req := m.BuildRequest(product)

	if req == nil {
		log.Trace.Warnf(ctx, LogTag, "empty product, fail to get route price")
		return false, nil
	}
	resp, err := ticket_price.GetStationPrice(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "fetch route price info failed %s", err)
		return false, nil
	}
	if resp == nil || resp.Data == nil {
		log.Trace.Warnf(ctx, LogTag, "fetch empty route price info")
		return false, nil
	}
	return true, resp.Data
}

func (m *StationPrice) genModel(ctx context.Context, product *models.Product) int {
	toggle1 := carpoolstation.MoveFlowFunctionSwitchWithProductID(TICKETPRICE, int(product.ProductID))
	toggle2 := apollo.FeatureToggle(ctx, "gs_bus_divert_tc", m.baseReq.PassengerInfo.Phone, map[string]string{
		"product_id":   strconv.Itoa(int(product.ProductID)),
		"function_url": TICKETPRICE,
	})

	if toggle1 && toggle2 {
		return fromDivertSelectTicket
	}
	if toggle1 && !toggle2 {
		return fromDivertSelectTc
	}
	if !toggle1 && toggle2 {
		return fromTicketApi
	}
	return fromTripCloud
}

func (m *StationPrice) getData(ctx context.Context, products []*models.Product, model int) bool {
	var (
		tcValid     bool
		ticketValid bool
	)
	switch model {
	case fromTripCloud:
		tcValid, m.tcData = m.getTCStationBasicPrice(ctx, products)
	case fromTicketApi:
		ticketValid, m.ticketData = m.getTicketStationBasicPrice(ctx, products[0])
	case fromDivertSelectTc, fromDivertSelectTicket:
		tcValid, m.tcData = m.getTCStationBasicPrice(ctx, products)
		ticketValid, m.ticketData = m.getTicketStationBasicPrice(ctx, products[0])
		// 比diff
		var diffKey string
		var diffKeyMap = make(map[string]int)

		toggle, err := apolloV2.FeatureToggle("gs_intercity_divert_compare_diff", ApolloModel.NewUser(""))
		if err == nil && toggle.IsAllow() {
			diffKey = toggle.GetAssignment().GetParameter("diffKey", "")
		}
		values := strings.Split(diffKey, ",")
		for _, value := range values {
			diffKeyMap[value] = 0
		}
		defer goutils.Go(ctx, func(ctx context.Context, args ...interface{}) {
			Compare(ctx, adapterFormat(m.tcData), m.ticketData, "20240401", TICKETPRICE, diffKeyMap)
		})
	default:
		tcValid, m.tcData = m.getTCStationBasicPrice(ctx, products)
	}
	return tcValid || ticketValid
}

func Compare(ctx context.Context, originResponse *ticketPrice.StationPriceData, newResponse *ticketPrice.StationPriceData, errorCode, functionName string, diffKeyMap map[string]int) string {
	opt := cmp.FilterPath(func(p cmp.Path) bool {
		if _, ok := diffKeyMap[p.String()]; ok {
			return true
		}
		return false
	}, cmp.Ignore())
	diff := cmp.Diff(originResponse, newResponse, opt)
	if len(diff) != 0 {
		log.Trace.Warnf(ctx, LogTag, functionName+"-"+errorCode, "there is diff: %#v", diff)
	}
	return diff
}

func (m *StationPrice) formatData(model int) {
	if model == fromTicketApi || model == fromDivertSelectTicket {
		m.resData = m.ticketData
		return
	}
	if m.tcData == nil {
		return
	}
	m.resData = adapterFormat(m.tcData)
	return
}

func adapterFormat(data *TripCloudPassenger.StationPriceData) *ticketPrice.StationPriceData {
	res := &ticketPrice.StationPriceData{}
	tmp, err := json.Marshal(data)
	if err != nil {
		return nil
	}
	if err := json.Unmarshal(tmp, res); err != nil {
		return nil
	}
	return res
}
