package rpc_process

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	dirpcSdkBrick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/brick"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// PbdTicketRuleRPC 站点巴士 票规则信息
type PbdTicketRuleRPC struct {
	baseReq *models.BaseReqData

	req      *proto.PbdStationBusDetailEstimateReq
	ruleData *dirpcSdkBrick.RuleData
}

// NewPbdTicketRuleInfo 获取身份信息
func NewPbdTicketRuleInfo(req *proto.PbdStationBusDetailEstimateReq, baseReq *models.BaseReqData) *PbdTicketRuleRPC {
	return &PbdTicketRuleRPC{
		req:     req,
		baseReq: baseReq,
	}
}

// 获取rpc错误信息
func (ip *PbdTicketRuleRPC) GetErrorInfo(ctx context.Context) error {
	return nil
}

// BuildProductBizInfo 设置单产品信息
func (ip *PbdTicketRuleRPC) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}

// BuildCommonBizInfo ...
func (ip *PbdTicketRuleRPC) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.RuleData = ip.ruleData
}

// Fetch 获取/加载
func (ip *PbdTicketRuleRPC) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		targetProduct *models.Product

		err error
	)

	targetProduct = ip.getTargetProduct(ctx, products)

	if targetProduct == nil {
		return true
	}

	err = ip.fetchGetBusRuleData(ctx, targetProduct)
	if err != nil {
		return false
	}

	return true
}

// getTargetProduct
func (ip *PbdTicketRuleRPC) getTargetProduct(ctx context.Context, products []*models.Product) *models.Product {
	for _, product := range products {
		if product != nil && carpool.IsIntercityStation(ctx, int(product.CarpoolType)) {
			return product
		}
	}
	return nil
}

// buildRequest ...
func (ip *PbdTicketRuleRPC) buildGetIdentityPageInfoRequest(ctx context.Context, targetProduct *models.Product) *dirpcSdkBrick.GetBusTicketRuleInfoReq {
	if targetProduct == nil {
		return nil
	}

	if targetProduct.BizInfo == nil {
		return nil
	}

	return &dirpcSdkBrick.GetBusTicketRuleInfoReq{
		RuleType:          "3",
		BusServiceShiftId: &targetProduct.ShiftID,
		BusinessId:        &targetProduct.BusinessID,
		ComboId:           &targetProduct.BizInfo.ComboID,
	}
}

// fetchGetBusRuleData ...
func (ip *PbdTicketRuleRPC) fetchGetBusRuleData(ctx context.Context, targetProduct *models.Product) error {
	if targetProduct == nil {
		return errors.New("target product is nil")
	}

	if targetProduct.BizInfo == nil {
		return errors.New("biz info is nil")
	}

	if targetProduct.BizInfo.RouteDetailV2 == nil {
		return errors.New("route detail is nil")
	}

	req := ip.buildGetIdentityPageInfoRequest(ctx, targetProduct)
	if req == nil {
		return errors.New("req is nil")
	}

	resp, err := brick.GetBusTicketRuleInfo(ctx, req)
	if err != nil {
		return err
	}

	log.Trace.Infof(ctx, "pbd GetBusTicketRuleInfo", "identityPageInfo:%v", util.JustJsonEncode(resp))
	if resp != nil && resp.RuleInfo != nil {
		ip.ruleData = resp.RuleInfo
	}
	return err
}
