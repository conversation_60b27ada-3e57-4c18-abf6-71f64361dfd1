// models/biz_runtime/info_view_test.go
package biz_runtime

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"reflect"
	"testing"

	"git.xiaojukeji.com/nuwa/golibs/knife"
)

// 该测试函数由AI自动生成
func TestProductInfoFull_GetOverseaEts(t *testing.T) {
	tests := []struct {
		name    string
		setup   func() (*ProductInfoFull, *gomonkey.Patches)
		wantEts int64
	}{
		//{
		//	name: "正常场景-有ETS信息",
		//	setup: func() (*ProductInfoFull, *gomonkey.Patches) {
		//		info := &ProductInfoFull{}
		//		patches := gomonkey.NewPatches()
		//		patches.ApplyMethod(reflect.TypeOf(info), "GetBizInfo",
		//			func(_ *ProductInfoFull) *models.PrivateBizInfo {
		//				return &models.PrivateBizInfo{
		//					OverseaExtraInfo: &models.OverseaExtraInfo{
		//						ETS: 300, // 5分钟
		//					},
		//				}
		//			})
		//		return info, patches
		//	},
		//	wantEts: 300,
		//},
		{
			name: "OverseaExtraInfo为空",
			setup: func() (*ProductInfoFull, *gomonkey.Patches) {
				info := &ProductInfoFull{}
				patches := gomonkey.NewPatches()
				patches.ApplyMethod(reflect.TypeOf(info), "GetBizInfo",
					func(_ *ProductInfoFull) *models.PrivateBizInfo {
						return &models.PrivateBizInfo{
							OverseaExtraInfo: nil,
						}
					})
				return info, patches
			},
			wantEts: 0,
		},
		//{
		//	name: "BizInfo为空",
		//	setup: func() (*ProductInfoFull, *gomonkey.Patches) {
		//		info := &ProductInfoFull{}
		//		patches := gomonkey.NewPatches()
		//		patches.ApplyMethod(reflect.TypeOf(info), "GetBizInfo",
		//			func(_ *ProductInfoFull) *models.PrivateBizInfo {
		//				return nil
		//			})
		//		return info, patches
		//	},
		//	wantEts: 0,
		//},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			info, patches := tt.setup()
			defer patches.Reset()

			gotEts := info.GetOverseaEts()
			assert.Equal(t, tt.wantEts, gotEts)
		})
	}
}

func TestIsHitNormalNoAnswerCompensation(t *testing.T) {
	ctx := context.Background()
	product := &ProductInfoFull{
		Product: &models.Product{
			ProductCategory: 1001,
		},
	}

	tests := []struct {
		name         string
		setupContext interface{}
		expectResult bool
	}{
		{
			name:         "context中无值时应返回false",
			setupContext: nil,
			expectResult: false,
		},
		{
			name:         "context中值为true时应返回true",
			setupContext: true,
			expectResult: true,
		},
		{
			name:         "context中值为false时应返回false",
			setupContext: false,
			expectResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx = knife.New(ctx)
			knife.Set(ctx, "hit_normal_no_answer_compensation", tt.setupContext)

			// 执行测试方法
			result := product.IsHitNormalNoAnswerCompensation(ctx)

			// 验证结果
			assert.Equal(t, tt.expectResult, result, "无车赔状态应匹配")
		})
	}
}
