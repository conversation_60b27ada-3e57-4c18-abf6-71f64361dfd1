package biz_runtime

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestQuotation
func TestQuotation(t *testing.T) {

	t.Run("测试 01- 无text", func(t *testing.T) {
		quo := Quotation{
			NTuple: "{carpool_type:2}",
		}
		// 准备 - 文本为空的场景
		carpool := quo.GetCarpoolType()
		assert.NotNil(t, carpool)
		assert.NotNil(t, carpool, int64(2))
	})
	t.Run("测试 02- ", func(t *testing.T) {
		// 准备 - 文本为空的场景
		quo := Quotation{}
		carpool := quo.GetCarpoolType()
		assert.NotNil(t, carpool)
		assert.NotNil(t, carpool, int64(0))
	})
	t.Run("测试 03- GetQuotationPageType", func(t *testing.T) {
		quo := Quotation{PageType: 123}
		pageType := quo.GetQuotationPageType()
		assert.NotNil(t, pageType)
		assert.Equal(t, int64(123), *pageType)

		var nilQuo *Quotation = nil
		assert.Nil(t, nilQuo.GetQuotationPageType())
	})
	t.Run("测试 04- GetTCDiscountFee", func(t *testing.T) {
		quo := Quotation{TripCloudDiscountFee: 12.34}
		assert.Equal(t, 12.34, quo.GetTCDiscountFee())

		quo2 := Quotation{}
		assert.Equal(t, 0.0, quo2.GetTCDiscountFee())
	})
}
