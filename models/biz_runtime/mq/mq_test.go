package mq

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/ddmq"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"testing"
)

func mockLogger() {
	logConfig := &ddlog.FileConfig{
		AutoClear:  true,
		ClearHours: 24,
		FilePrefix: "test",
		FileDir:    "./",
		Separate:   true,
		Level:      "debug",
	}
	log.Trace, _ = ddlog.NewLoggerWithCfg(logConfig)
}

func TestSyncSendMQMessageNocheck(t *testing.T) {
	mockLogger()
	ctx := context.Background()

	testCases := []struct {
		name string
		pif  *biz_runtime.ProductInfoFull
	}{
		{
			name: "send success",
			pif: &biz_runtime.ProductInfoFull{
				BaseReqData: &models.BaseReqData{
					CommonInfo: models.CommonInfo{
						// 初始化 CommonInfo 的字段
						AccessKeyID: 123,
						AppVersion:  "1.0.0",
					},
					AreaInfo: models.AreaInfo{
						// 初始化 AreaInfo 的字段
						City: 1,
					},
					PassengerInfo: models.PassengerInfo{
						// 初始化 PassengerInfo 的字段
						UID: 12345,
					},
					CommonBizInfo: models.CommonBizInfo{
						// 初始化 CommonBizInfo 的字段
						RouteId: 123,
					},
				},
				Product: &models.Product{
					ProductID:       123,
					ProductCategory: 1,
					CarpoolType:     1,
					BizInfo: &models.PrivateBizInfo{
						// 初始化 BizInfo 的字段
						CarpoolSeatNum: 1,
					},
				},
				ExtraInfo: map[string]string{
					"key1": "value1",
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			patche := mockey.Mock(ddmq.SendV2).To(func(ctx context.Context, topic string, msg string, opts ...ddmq.MQOption) error {
				return nil
			}).Build()
			patcheGetConfigItem := mockey.Mock(ApolloSDK.GetConfigItem).To(func(namespace string, name string, key string) (item string, err error) {
				return "{\"123\":{\"allow_airport\":0,\"allow_carpool\":1,\"allow_inter_city_carpool\":0,\"belongs_car_group\":[2],\"belongs_product_group\":[2],\"business_id\":281,\"cancel_rule\":\"fastcar_likeness\",\"compet_show_dest\":1,\"coupon_product_id\":230,\"cover_car_level\":[\"600\"],\"credit_business_type\":256,\"default_car_level\":600,\"dprelation\":[\"kuaiche\",260,2,1],\"driver_endcharge_bill_config\":\"fastcar\",\"driver_reassign_new_order\":1,\"duse_sid\":\"gulfstream\",\"fence_group\":{\"coupon\":{\"id\":32,\"is_open\":1},\"risk_ban\":{\"id\":0,\"is_open\":0},\"risk_not_ban\":{\"id\":0,\"is_open\":0}},\"fence_group_id\":32,\"im_business_id\":281,\"im_salt\":\"cdbec71998a647852085dfcd8403199d\",\"open_virtual_phone\":1,\"order_title\":\"uber_fast\",\"pangu_product_id\":3,\"passenger_discount_fold\":1,\"price\":{\"0\":{\"schema_id\":4},\"4\":{\"schema_id\":5},\"product_token\":\"6f0d3a18ec8d63345aadad3922f099fc\"},\"product_type\":281,\"push_estimate_highway_remind\":1,\"show_complain_item\":1,\"show_highway_fee_remind\":1,\"show_pay_cash_only\":0,\"show_question_evaluate\":1,\"show_report_item\":1,\"simple_name\":\"fast\",\"sms\":{\"1\":{\"1\":\"132200007\",\"2\":\"132200007\",\"3\":\"132200007\"},\"2\":{\"1\":\"132200007\",\"2\":\"132200007\",\"3\":\"132200007\"}},\"tag_config\":{\"driver_servicephone\":\"4000006555\",\"passenger_servicephone\":\"4000006555\"},\"transfer_order_type\":18,\"write_dfs_order_feature\":1}}", nil
			}).Build()
			defer func() {
				patche.UnPatch()
				patcheGetConfigItem.UnPatch()
			}()
			// 执行测试
			SyncSendMQMessageNocheck(ctx, tc.pif)
		})
	}
}
