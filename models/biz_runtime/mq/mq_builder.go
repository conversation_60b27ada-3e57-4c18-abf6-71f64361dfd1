package mq

import (
	"bytes"
	"context"
	"encoding/json"
	"strconv"
	"time"

	context2 "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type MsgBuilder struct {
	msg *Message
}

func NewMQMsgBuilder() *MsgBuilder {
	return &MsgBuilder{
		msg: &Message{
			No:   0,
			Type: 21030,
			Ct:   time.Now().Unix(),
		},
	}
}

func (bd *MsgBuilder) SetTraceFromCtx(ctx context.Context) *MsgBuilder {
	if trace := context2.GetTrace(ctx); trace != nil {
		bd.msg.Trace = &Trace{
			TraceID:     trace.GetTraceId(),
			SpanID:      trace.GetCSpanId(),
			HintCode:    trace.GetHintCode(),
			HintContent: trace.GetHintContent(),
		}
	}
	return bd
}

func (bd *MsgBuilder) SetDataByProduct(product *biz_runtime.ProductInfoFull) *MsgBuilder {
	encodeNoCheck := func(x interface{}) string {
		var buf bytes.Buffer
		encoder := json.NewEncoder(&buf)
		_ = encoder.Encode(x)
		return buf.String()
	}

	passenger := product.GetUserInfo()
	area := product.GetAreaInfo()
	nTuple := product.GetNTuple()

	bd.msg.Data = &Data{
		CreateTime:     strconv.FormatInt(bd.msg.Ct, 10),
		Biztype:        int(product.Product.BusinessID),
		PassengerPhone: passenger.Phone,
		UID:            passenger.UID,
		Gpid:           strconv.FormatInt(passenger.PID, 10),

		AppVersion:   product.BaseReqData.CommonInfo.AppVersion,
		Channel:      product.BaseReqData.CommonInfo.Channel,
		ClientType:   int(product.BaseReqData.CommonInfo.ClientType),
		PlatformType: int(product.BaseReqData.CommonInfo.PlatformType),
		OriginID:     int(product.BaseReqData.CommonInfo.OriginID),

		CurrentLng: area.CurLng,
		CurrentLat: area.CurLat,

		District: area.District,
		Area:     int(area.City),

		StartingName:         area.FromName,
		StartingLng:          area.FromLng,
		StartingLat:          area.FromLat,
		County:               strconv.FormatInt(int64(area.FromCounty), 10),
		FromPoiID:            area.FromPoiID,
		DestLng:              area.ToLng,
		DestLat:              area.ToLat,
		DestName:             area.ToName,
		ToPoiID:              area.ToPoiID,
		NTuple:               encodeNoCheck(nTuple),
		ProductCategory:      int(product.Product.ProductCategory),
		ComboType:            int(nTuple.ComboType),
		CarType:              product.Product.RequireLevel,
		SceneType:            int(product.Product.SceneType),
		ProductID:            int(product.Product.ProductID),
		OType:                int(product.Product.OType),
		DepartureTime:        product.BaseReqData.CommonInfo.DepartureTime,
		EstimateID:           product.Product.EstimateID,
		BubbleID:             product.Product.EstimateID,
		PayType:              int(product.Product.PaymentsType),
		BasicTotalFee:        product.GetBasicTotalFee(),
		MenuID:               product.GetMenuId(),
		SelectType:           product.GetProductCheckStatus(),
		Role:                 2, //1-司机 2-乘客
		EstimateType:         "0",
		OffPeakPushTimestamp: -1,
	}
	multiRequireProductBytes, _ := json.Marshal(product.GetMultiRequireProduct())
	bd.msg.Data.MultiRequireProduct = string(multiRequireProductBytes)
	bd.msg.Data.EstimateFee = product.GetEstimateFee()
	bd.msg.Data.EstimateDistanceMetre = int(product.GetBillDriverMetre())
	bd.msg.Data.EstimateTimeMinutes = int(product.GetBillDriverMinute())
	bd.msg.Data.DynamicTotalFee = product.GetDynamicTotalFee()
	return bd
}

func (bd *MsgBuilder) Build() *Message {
	return bd.msg
}
