package mq

import (
	"bytes"
	"context"
	"encoding/json"
	"math/rand"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/ddmq"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

func SyncSendMQMessageNocheck(ctx context.Context, pif *biz_runtime.ProductInfoFull) {
	rawMsg := NewMQMsgBuilder().SetTraceFromCtx(ctx).SetDataByProduct(pif).Build()

	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	if err := encoder.Encode(rawMsg); err != nil {
		log.Trace.Warnf(ctx, "pbd_estimate_mq", "mq send failed %s", err)
		return
	}

	hashOption := func(bd *ddmq.MessageBuilder) {
		const hashRng = 16383
		rand.Seed(time.Now().Unix())
		bd.SetHashId(rand.Int63n(hashRng))
	}

	if err := ddmq.SendV2(ctx, ddmq.TopicWanliuPassengerEstimate, buf.String(), hashOption); err != nil {
		log.Trace.Warnf(ctx, "pbd_estimate_mq", "mq send failed %s", err)
	}
}
