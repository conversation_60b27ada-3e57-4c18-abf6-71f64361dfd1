package biz_runtime

import "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"

type OrderEnv struct {
	Area              int64               `json:"a"`
	DepartureTime     int64               `json:"tm,omitempty"`
	CouponType        interface{}         `json:"ot"`
	Fee               int64               `json:"c"`
	EstimatePrice     int64               `json:"estimate_price"`
	Source            []int               `json:"billing_channel"` // 客户端标识
	BusinessID        consts.BusinessID   `json:"business_id"`
	ComboType         consts.ComboType    `json:"combo_type"`
	OrderType         consts.OrderType    `json:"order_type"`
	GEOFence          []int64             `json:"geofce"`
	StartFence        []int64             `json:"start_fence"`
	Loc               *Location           `json:"ps,omitempty"`
	ActualComboType   int                 `json:"actual_combo_type,omitempty"`
	CarLevel          consts.CarLevel     `json:"car_level,omitempty"`
	ClientType        consts.CouponClient `json:"terminal,omitempty"` //客户端标识
	Only              int                 `json:"only,omitempty"`
	PhoenixChannel    int                 `json:"phoenix_channel,omitempty"` //支付方式
	PayType           int32               `json:"pay_type,omitempty"`
	TaxiSpDiscountFee int64               `json:"taxi_sp_discount_fee,omitempty"`
	Upgrade           int                 `json:"upgrade,omitempty"`            // 是否升舱
	MixedPaymentType  int                 `json:"mixed_payment_type,omitempty"` // 混合支付类型，1：企业+个人
	ShieldType        string              `json:"shield_type,omitempty"`
	ShieldBatch       []string            `json:"shield_batch,omitempty"`
	AppChannel        string              `json:"app_channel,omitempty"`
	EstimateDistance  int64               `json:"estimate_distance,omitempty"`
}

// Location ...
type Location struct {
	Start *Pos `json:"sta"`
	Dest  *Pos `json:"des"`
}

// Pos ...
type Pos struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

func GetBillingChannel(accessKeyID int) []int {
	switch accessKeyID {
	case consts.AccessKeyIDDiDiIos:
		return []int{1, 101}
	case consts.AccessKeyIDDiDiAndroid:
		return []int{1, 102}
	case consts.AccessKeyIDDiDiWechatMini:
		return []int{201}
	case consts.AccessKeyIDDiDiAlipayMini:
		return []int{202}
	default:
		return []int{}
	}
}
