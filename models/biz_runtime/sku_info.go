package biz_runtime

import (
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	constsCommon "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

// 判断自营库存模式
func RenderSelfSupportSkuMode(prod *ProductInfoFull) bool {
	if prod.GetProductCategory() != ProductCategory.ProductCategoryCarpoolInterCity {
		return false
	}
	if prod.GetAccessKeyId() == constsCommon.AccessKeyIDDiDiAndroid || prod.GetAccessKeyId() == constsCommon.AccessKeyIDDiDiIos {
		//na版本号>6.6.5才可渲染库存模式
		if util.CompareAppVersion(prod.GetAppVersion(), "6.6.5") > 0 && prod.GetIsSkuMode() {
			return true
		}
		return false
	} else {
		return prod.GetIsSkuMode()
	}
}
