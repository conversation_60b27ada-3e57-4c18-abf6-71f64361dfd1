package biz_runtime

import (
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"testing"

	"github.com/stretchr/testify/assert"
)

//
//func TestIntToBool(t *testing.T) {
//	type TestCase struct {
//		input interface{}
//		want  bool
//	}
//
//	testcases := []TestCase{
//		{
//			input: int(1),
//			want:  true,
//		},
//		{
//			input: int8(1),
//			want:  false,
//		},
//		{
//			input: int16(1),
//			want:  false,
//		},
//		{
//			input: int32(1),
//			want:  false,
//		},
//		{
//			input: int64(1),
//			want:  false,
//		},
//		{
//			input: uint(1),
//			want:  false,
//		},
//	}
//
//	for _, testcase := range testcases {
//		assert.Equal(t, testcase.want, cast.ToBool(testcase.input))
//	}
//}

// 该测试函数由AI自动生成
func TestGetExternalEid(t *testing.T) {
	tests := []struct {
		name     string
		product  *ProductInfoFull
		expected string
	}{
		{
			name: "hu is nil",
			product: &ProductInfoFull{
				hu: nil,
			},
			expected: "",
		},
		{
			name: "hu is not nil",
			product: &ProductInfoFull{
				hu: &PriceApi.EstimateNewFormData{ExternalEid: "external-id"},
			},
			expected: "external-id",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.product.GetExternalEid()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNewSceneEstimateFeeViewer(t *testing.T) {
	NewSceneEstimateFeeViewer(nil, nil, nil, nil)
}
