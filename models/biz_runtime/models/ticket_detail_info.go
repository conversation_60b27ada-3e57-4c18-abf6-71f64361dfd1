package models

type TicketDetailInfo struct {
	TicketId     string `json:"ticket_id"`
	TicketType   int32  `json:"ticket_type"`
	IsFocus      int32  `json:"is_focus"`
	TicketStatus int32  `json:"ticket_status"`
}

type RebookBusExtraInfo struct {
	RemainCarryChildNum string `json:"remain_carry_child_num"`
	ShiftId             string `json:"shift_id"`
	HasChildTicket      string `json:"has_child_ticket"`
	HasCouponTicket     string `json:"has_coupon_ticket"` // 是否支持优待票
	CarryChildNeedSeats string `json:"carry_child_need_seats"`
}
