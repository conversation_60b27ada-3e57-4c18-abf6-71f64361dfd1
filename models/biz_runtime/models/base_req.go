package models

import (
	"strconv"

	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
)

type BaseReqData struct {
	AreaInfo      AreaInfo
	CommonInfo    CommonInfo
	PassengerInfo PassengerInfo
	DriverInfo    DriverInfo
	CommonBizInfo CommonBizInfo
	SendOrder     SendOrder

	RawUserSelectOption *UserOption //
}

func (br *BaseReqData) GetApolloParam() map[string]string {
	return map[string]string{
		"uid":           strconv.Itoa(int(br.PassengerInfo.UID)),
		"pid":           strconv.Itoa(int(br.PassengerInfo.PID)),
		"phone":         br.PassengerInfo.Phone,
		"city":          strconv.Itoa(int(br.AreaInfo.Area)),
		"app_version":   br.CommonInfo.AppVersion,
		"access_key_id": strconv.Itoa(int(br.CommonInfo.AccessKeyID)),
		"lang":          br.CommonInfo.Lang,
		"city_id":       strconv.Itoa(int(br.AreaInfo.Area)),
	}
}

func (br *BaseReqData) GenDDSProductsReq() *EstimateDecision.ProductsReq {
	ddsReq := &EstimateDecision.ProductsReq{}
	br.CommonInfo.genDDSProductsReq(ddsReq)
	br.PassengerInfo.genDDSProductsReq(ddsReq)
	br.AreaInfo.genDDSProductsReq(ddsReq)
	br.CommonBizInfo.genDDSProductsReq(ddsReq)
	br.DriverInfo.genDDSProductsReq(ddsReq)
	return ddsReq
}
