package models

import (
	"context"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"

	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
)

type PassengerInfo struct {
	UID      int64  `json:"uid"`
	PID      int64  `json:"pid"`
	Phone    string `json:"phone"`
	Role     int32  `json:"role"`
	Channel  string `json:"channel"`
	AppID    int32  `json:"app_id"`
	UserType int32  `json:"user_type"`
	OriginId string `json:"origin_id"`
	Token    string `json:"token"`

	UserGender int32
	NeedMember bool   //是否需要构建会员信息
	ExtPid     string //腾讯出行透传pid
}

func (p *PassengerInfo) InitByReq(ctx context.Context, token string, userType int32) error {
	p.UserType = userType
	userInfo, err := passport.GetUserInfo(ctx, token, "")
	if err != nil || userInfo == nil {
		return err
	}

	p.PID = int64(userInfo.PID)
	p.UID = int64(userInfo.UID)
	p.Phone = userInfo.Phone
	p.Channel = userInfo.Channel
	p.Role = userInfo.Role
	p.OriginId = userInfo.OriginId
	return nil
}

func (p *PassengerInfo) BuildByUserInfo(ctx context.Context, userInfo *passport.UserInfo, feature *ufs.OrderFeature) {
	p.PID = int64(userInfo.PID)
	p.UID = int64(userInfo.UID)
	p.Channel = userInfo.Channel
	p.Phone = userInfo.Phone
	p.OriginId = userInfo.OriginId
	p.Role = userInfo.Role
	p.UserGender = userInfo.UserGender

	if ut, err := strconv.ParseInt(feature.UserType, 10, 64); err == nil {
		p.UserType = int32(ut)
	}
}

func (p *PassengerInfo) BuildByUserInfoWithUserType(ctx context.Context, userInfo *passport.UserInfo, userType int32) {
	p.PID = int64(userInfo.PID)
	p.UID = int64(userInfo.UID)
	p.Channel = userInfo.Channel
	p.Phone = userInfo.Phone
	p.OriginId = userInfo.OriginId
	p.Role = userInfo.Role
	p.UserType = userType
}

func (p *PassengerInfo) genDDSProductsReq(request *EstimateDecision.ProductsReq) {
	if request.UserInfo == nil {
		request.UserInfo = &EstimateDecision.UserInfoV2{}
	}
	request.UserInfo.Phone = p.Phone
	request.UserInfo.Pid = p.PID
	request.UserInfo.Uid = p.UID
	request.UserInfo.Id = p.PID
}

func (p *PassengerInfo) BuildPriceReq(request *price_api.PriceEstimateReq, baseProduct *Product) {
	if request.PassengerInfo == nil {
		request.PassengerInfo = &price_api.PassengerInfo{}
	}
	request.PassengerInfo.ID = p.PID
	request.PassengerInfo.UID = p.UID
	request.PassengerInfo.Phone = p.Phone
	request.PassengerInfo.Channel, _ = strconv.Atoi(p.Channel)
	request.PassengerInfo.Role = int64(p.Role)
	request.PassengerInfo.Origin, _ = strconv.ParseInt(p.OriginId, 10, 64)
	if baseProduct.BizInfo != nil {
		request.PassengerInfo.MemberInfo = &price_api.MemberProfileV2Req{
			LevelID: baseProduct.BizInfo.UserMemberProfile.LevelID,
		}
	}
	request.PassengerInfo.Token = p.Token
	if request.OrderInfoSt == nil {
		request.OrderInfoSt = &PriceApi.OrderInfoSt{}
	}
	request.OrderInfoSt.UserType = int64(p.UserType)
}
