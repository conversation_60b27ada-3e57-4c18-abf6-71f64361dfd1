package models

import (
	"context"
	"testing"

	bizconsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	tcutil "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tripcloud"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func TestProduct_IsTripcloudProduct(t *testing.T) {
	// case 1: p.IsTripcloud 非空，直接返回
	valTrue := true
	valFalse := false
	p1 := &Product{IsTripcloud: &valTrue}
	p2 := &Product{IsTripcloud: &valFalse}
	assert.True(t, p1.IsTripcloudProduct(context.Background()))
	assert.False(t, p2.IsTripcloudProduct(context.Background()))

	// case 2: p.IsTripcloud 为空，走 tcutil.IsTripcloudProductID
	patch := mockey.Mock(tcutil.IsTripcloudProductID).To(func(ctx context.Context, pid bizconsts.ProductID) bool {
		return pid == 1001
	}).Build()
	defer patch.UnPatch()

	p3 := &Product{ProductID: 1001, IsTripcloud: nil}
	p4 := &Product{ProductID: 1002, IsTripcloud: nil}
	assert.True(t, p3.IsTripcloudProduct(context.Background()))
	assert.False(t, p4.IsTripcloudProduct(context.Background()))
}
