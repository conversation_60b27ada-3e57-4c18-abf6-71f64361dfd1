package models

import (
	"encoding/json"
	Locsvr "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Locsvr"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/locsvr"
	"testing"

	"context"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

// TestTryFillCityInfoWithStopover 测试TryFillCityInfoWithStopover函数
func TestTryFillCityInfoWithStopover(t *testing.T) {
	assert := assert.New(t)
	// 定义测试用例集合
	cases := []struct {
		Name       string
		areaInfo   AreaInfo
		patch      func(*gomonkey.Patches)
		ExpectFunc func(*testing.T, AreaInfo)
	}{
		{
			Name: "normal case",
			areaInfo: AreaInfo{
				FromLat: 39.805424,
				FromLng: 116.445865,
				ToLat:   39.854619,
				ToLng:   116.367434,
				StopoverPointInfo: []*price_api.StopoverPointInfo{
					{
						StopID: 1,
						Lat:    31.222,
						Lng:    121.444,
					},
					{
						StopID: 2,
						Lat:    31.233,
						Lng:    121.455,
					},
				},
			},
			patch: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(locsvr.MultiAreaInfoByCoords,
					func(ctx context.Context, coordsList []map[string]float64, mapType string) (map[int64]*Locsvr.Cityinfo, error) {
						// 初始化 map[int64]*Locsvr.Cityinfo
						cityInfos := make(map[int64]*Locsvr.Cityinfo)
						// 创建示例数据
						districtCode := "100000"
						canonicalCountryCode := "CN"
						city1 := &Locsvr.Cityinfo{
							Cityid:               1,
							Countyid:             1,
							CityDesc:             "City1",
							CountyDesc:           "County1",
							DistrictCode:         &districtCode,
							CanonicalCountryCode: &canonicalCountryCode,
						}

						city2 := &Locsvr.Cityinfo{
							Cityid:               2,
							Countyid:             2,
							CityDesc:             "City2",
							CountyDesc:           "County2",
							DistrictCode:         &districtCode,
							CanonicalCountryCode: &canonicalCountryCode,
						}

						// 将城市信息添加到 map 中
						cityInfos[0] = city1
						cityInfos[1] = city2
						cityInfos[2] = city2
						cityInfos[3] = city2
						return cityInfos, nil
					})
			},
			ExpectFunc: func(t *testing.T, areaInfo AreaInfo) {
				assert.Equal(json.Number("2"), areaInfo.StopoverPointInfo[0].CityID)
			},
		},
	}

	ctx := context.Background()
	for _, tc := range cases {
		t.Run(tc.Name, func(t *testing.T) {
			defer func() { recover() }()
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			tc.patch(patches)
			tc.areaInfo.TryFillCityInfoWithStopover(ctx)
			tc.ExpectFunc(t, tc.areaInfo)
		})
	}
}
