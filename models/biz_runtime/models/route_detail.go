package models

import Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"

type RouteDetail Prfs.RouteDetailData

// IsNeedVerified 是否需要实名认证
func (rd *RouteDetail) IsNeedVerified() bool {
	if rd.RouteExtendInfo == nil || rd.RouteExtendInfo.IsNeedVerified == nil {
		return false
	}

	return *rd.RouteExtendInfo.IsNeedVerified
}

// IsChildOccupy 携童是否占座
func (rd *RouteDetail) IsChildOccupy() bool {
	if rd.RouteExtendInfo == nil || rd.RouteExtendInfo.ChildOccupySeat == nil {
		return true
	}

	return *rd.RouteExtendInfo.ChildOccupySeat
}
