package biz_runtime

import (
	"context"
	"encoding/json"
	"fmt"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool"
	"math"
	"reflect"
	"strconv"

	CommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"github.com/shopspring/decimal"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/bargain"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	trace "git.xiaojukeji.com/lego/context-go"
	GoCast "github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
)

type PriceDataViewer interface {
	// 预估标识
	GetEstimateID() string
	// 价格信息, 预估结果汇总
	GetSceneEstimatePrice() []SceneEstimateFeeViewer
}

type FeeAttribute interface {
	GetInt(path string) *int64
	GetBool(path string) *bool
	GetString(path string) *string
}

type SceneEstimateFeeViewer interface {
	// 预估金额
	GetFee() float64
	// 预估金额
	GetExactFee() float64
	// 费用详情, 从基础报账到最后预估价的计算过程, 可用来渲染优惠和加价项, 升舱拆费也在这里
	GetFeeDetail() IFeeDetail
	// 支付信息, 支付方式影响最终价格
	GetPayment() Payment
	// 获取价格描述, 比如拼到几人, 比如count_price_type
	GetFeeAttributes() FeeAttribute
}

type IFeeDetail interface {
	GetTotalFeeWithoutAnyDiscount() float64
	// 优惠前, 目前对应账单返回的 dynamic_total_fee
	GetTotalFeeWithoutCouponSome() float64
	// 获取对应基础费的pre_total_fee
	GetPreTotalFee() float64
	// 获取券
	GetCoupon() *Coupon
	// 获取打车金
	GetBonusAmount() *float64
	// 企业支付金额
	GetBusinessPayAmount() *float64

	GetFeeItem() []*price_api.FeeItem
	// 获取一口价面额
	GetCapPrice() float64
	// 获取fee_detail_info
	GetFeeDetailInfo() map[string]float64
	// 获取折上折
	GetDiscountCard() *PriceApi.DiscountCard
	// 获取支付宝折扣
	GetExternalCoupon() *PriceApi.EstimateNewFormCouponInfo
	//获取dynamic_total_fee
	GetDynamicTotalFee() float64
}

func (s *ProductInfoFull) GetEstimateID() string {
	if s.hu == nil {
		return ""
	}
	return s.hu.EstimateId
}

func (s *ProductInfoFull) GetExternalEid() string {
	if s.hu == nil {
		return ""
	}
	return s.hu.ExternalEid
}

type tMixPayMent struct {
	MixedPayType       int32
	RecommendDeductFee float64
}
type Payment struct {
	PayType int32
	// BusinessConstSet int32
	MixPay *tMixPayMent
}

type Coupon struct {
	Amount   float64
	Discount int32
	Tag      string
	Source   string
}

type tFeeDetail struct {
	belong *SceneEstimateFee
}

func (fee *tFeeDetail) GetDynamicTotalFee() float64 {
	bill := fee.belong.GetRawBill()
	return bill.DynamicTotalFee
}

func (fee *tFeeDetail) GetTotalFeeWithoutAnyDiscount() float64 {
	bill := fee.belong.GetRawBill()
	return bill.TotalFeeWithoutDiscount
}

func (fee *tFeeDetail) GetTotalFeeWithoutCouponSome() float64 {
	bill := fee.belong.GetRawBill()
	return bill.DynamicTotalFee
}

func (fee *tFeeDetail) GetPreTotalFee() float64 {
	bill := fee.belong.GetRawBill()
	return bill.PreTotalFee
}

func (fee *tFeeDetail) GetCapPrice() float64 {
	bill := fee.belong.GetRawBill()
	return bill.CapPrice
}

func (fee *tFeeDetail) GetFeeDetailInfo() map[string]float64 {
	bill := fee.belong.GetRawBill()
	return bill.FeeDetailInfo
}

func (fee *tFeeDetail) GetDiscountCard() *PriceApi.DiscountCard {
	if fee.belong == nil || fee.belong.discountSet == nil {
		return nil
	}

	return fee.belong.discountSet.DiscountCard
}

func (fee *tFeeDetail) GetExternalCoupon() *PriceApi.EstimateNewFormCouponInfo {
	if fee.belong == nil || fee.belong.discountSet == nil {
		return nil
	}

	return fee.belong.discountSet.ExternalCoupon
}

func (fee *tFeeDetail) GetCoupon() *Coupon {
	if fee.belong == nil {
		return nil
	}
	if ds := fee.belong.discountSet; ds != nil {
		if c := ds.Coupon; c != nil {
			v, e := strconv.Atoi(c.Amount)
			if e != nil {
				return nil
			}
			return &Coupon{
				Amount:   float64(v) / 100.0,
				Discount: c.Discount,
				Tag:      c.CustomTag,
				Source:   c.CouponSource,
			}
		}
	}
	return nil
}

func (fee *tFeeDetail) GetBonusAmount() *float64 {
	if fee.belong == nil {
		return nil
	}
	if ds := fee.belong.discountSet; ds != nil {
		if b := ds.Bonus; b != nil {
			v, e := strconv.ParseFloat(b.Amount, 64)
			if e != nil {
				return nil
			}
			v = v / 100.0
			return &v
		}
	}
	return nil
}

func (fee *tFeeDetail) GetBusinessPayAmount() *float64 {
	if fee.belong == nil {
		return nil
	}
	if mixPay := fee.belong.GetPayment().MixPay; mixPay != nil {
		deduct := fee.belong.GetFee()
		if mixPay.RecommendDeductFee > 0 {
			deduct = mixPay.RecommendDeductFee
		}
		return &deduct
	}
	return nil
}

// GetDisplayline 返回费项
func (fee *tFeeDetail) GetFeeItem() []*price_api.FeeItem {
	return fee.belong.billInfo.DisplayLines
}

func NewSceneEstimateFeeViewer(paymentInfo *price_api.PaymentInfoV3, billInfo *price_api.BillInfoV3, discountSet *price_api.DiscountSetV3, sceneMark map[string]string) SceneEstimateFeeViewer {
	return &SceneEstimateFee{
		paymentInfo:        paymentInfo,
		billInfo:           billInfo,
		discountSet:        discountSet,
		estimatePrice:      0,
		exactEstimatePrice: 0,
		sceneMark:          sceneMark,
	}
}

type SceneEstimateFee struct {
	paymentInfo        *price_api.PaymentInfoV3
	billInfo           *price_api.BillInfoV3
	discountSet        *price_api.DiscountSetV3
	estimatePrice      float64
	exactEstimatePrice float64
	sceneMark          map[string]string // ??
}

func (sf *SceneEstimateFee) GetExactFee() float64 {
	return sf.exactEstimatePrice
}

func (sf *SceneEstimateFee) GetFee() float64 {
	return sf.estimatePrice
}

func (sf *SceneEstimateFee) GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	if sf.discountSet == nil {
		return nil
	}
	return sf.discountSet.Coupon
}

func (sf *SceneEstimateFee) GetPbdCallReturn() *PriceApi.NewFormPbdCallReturn {
	if sf.discountSet == nil {
		return nil
	}
	return sf.discountSet.PbdCallReturn
}

func (sf *SceneEstimateFee) GetPayment() Payment {
	//如果支付获取失败，则返回一个空的payment，结果无意义，不可做业务参考
	if sf.paymentInfo == nil {
		return Payment{}
	}

	shadow := Payment{
		PayType: sf.paymentInfo.DefaultPayType,
	}
	if mixed := sf.paymentInfo.MixedPayDeductInfo; mixed != nil {
		shadow.MixPay = &tMixPayMent{
			MixedPayType:       mixed.MixedPayType,
			RecommendDeductFee: mixed.DeductFee,
		}
	}
	return shadow
}
func (sf *SceneEstimateFee) GetRawBill() *price_api.BillInfoV3 {
	return sf.billInfo
}

type tFeeAttribute struct {
	belong *SceneEstimateFee
}

func (attr *tFeeAttribute) getFromSceneMarkNotChecked(path string) string {
	if attr.belong.sceneMark == nil {
		return ""
	}
	s, ok := attr.belong.sceneMark[path]
	if !ok {
		return ""
	}
	return s
}

func (attr *tFeeAttribute) getFromBillInfo(path string) interface{} {
	bill := attr.belong.GetRawBill()
	switch path {
	case "count_price_type":
		return bill.CountPriceType
	case "driver_metre":
		return bill.DriverMetre
	case "driver_minute":
		return bill.DriverMinute
	case "dc_extra_json":
		extraMap, ok := bill.HistoryExtraMap.(map[string]interface{})
		if ok && len(extraMap) != 0 {
			discountInfo, bok := extraMap["discount_info"].(map[string]interface{})
			if bok && len(discountInfo) > 0 {
				dcExtraInfo := discountInfo["dc_extra_json"]
				return dcExtraInfo
			}
		}
	default:
	}
	return nil
}

func (attr *tFeeAttribute) GetString(path string) *string {
	if x := attr.getFromBillInfo(path); x != nil {
		s := GoCast.ToString(x)
		return &s
	}
	if s := attr.getFromSceneMarkNotChecked(path); s != "" {
		return &s
	}
	return nil
}

func (attr *tFeeAttribute) GetInt(path string) *int64 {
	if x := attr.getFromBillInfo(path); x != nil {
		i := GoCast.ToInt64(x)
		return &i
	}
	if s := attr.getFromSceneMarkNotChecked(path); s != "" {
		if i, err := strconv.ParseInt(s, 10, 64); err == nil {
			return &i
		}
	}
	return nil
}

func (attr *tFeeAttribute) GetBool(path string) *bool {
	if x := attr.getFromBillInfo(path); x != nil {
		i := GoCast.ToBool(x) // MAYBUG int64 的 1, 会返回false
		return &i
	}
	if s := attr.GetString(path); s != nil {
		if b, err := strconv.ParseBool(*s); err == nil {
			return &b
		}
	}
	return nil
}

func (sf *SceneEstimateFee) GetFeeAttributes() FeeAttribute {
	return &tFeeAttribute{belong: sf}
}

func (sf *SceneEstimateFee) GetFeeDetail() IFeeDetail {
	return &tFeeDetail{
		belong: sf,
	}
}

func sceneEstimateFeeFromEstimateData(data *price_api.EstimateDataV3) SceneEstimateFeeViewer {
	if data == nil {
		return nil
	}
	return &SceneEstimateFee{
		paymentInfo:        data.PaymentInfo,
		billInfo:           data.BillInfo,
		discountSet:        data.DiscountSet,
		estimatePrice:      data.EstimateFee,
		exactEstimatePrice: data.ExactEstimateFee,
	}
}

func sceneEstimateFeeFromExtendData(data *price_api.EstimateDataExtend, pay *price_api.PaymentInfoV3) *SceneEstimateFee {
	// 用个人支付作为兜底
	if pay == nil {
		pay = &price_api.PaymentInfoV3{
			DefaultPayType:     2,
			Payment:            nil,
			MixedPayDeductInfo: nil,
		}
	}
	if data == nil {
		return nil
	}
	return &SceneEstimateFee{
		paymentInfo:        pay,
		billInfo:           data.BillInfo,
		discountSet:        data.DiscountSet,
		estimatePrice:      data.EstimateFee,
		exactEstimatePrice: data.ExactEstimateFee,
		sceneMark:          data.SceneMark,
	}
}

func sceneEstimateFeeFromExtendDataNoPay(data *price_api.EstimateDataExtend) *SceneEstimateFee {
	if data == nil {
		return nil
	}
	return &SceneEstimateFee{
		billInfo:      data.BillInfo,
		discountSet:   data.DiscountSet,
		estimatePrice: data.EstimateFee,
		sceneMark:     data.SceneMark,
	}
}

func (s *ProductInfoFull) GetSceneEstimatePrice() []SceneEstimateFeeViewer {
	ret := make([]SceneEstimateFeeViewer, 0, 1+len(s.hu.ExtendList))

	ret = append(
		ret,
		sceneEstimateFeeFromEstimateData(s.hu),
	)
	payment := s.hu.PaymentInfo
	for _, ss := range s.hu.ExtendList {
		if tmp := sceneEstimateFeeFromExtendData(ss, payment); tmp != nil {
			ret = append(ret, tmp)
		}
	}
	return ret
}

// GetSceneDataBySceneMark 根据map获取多口价
func (s *ProductInfoFull) GetSceneDataBySceneMark(reqSceneMark map[string]string) *SceneEstimateFee {
	for _, ss := range s.hu.ExtendList {
		if ok := reflect.DeepEqual(reqSceneMark, ss.SceneMark); ok {
			payment := s.hu.PaymentInfo
			return sceneEstimateFeeFromExtendData(ss, payment)
		}
	}
	return nil
}

// GetSceneDataBySceneMarkNoPay 根据map获取多口价 不包含支付方式的判断
func (s *ProductInfoFull) GetSceneDataBySceneMarkNoPay(reqSceneMark map[string]string) *SceneEstimateFee {
	for _, ss := range s.hu.ExtendList {
		if ok := reflect.DeepEqual(reqSceneMark, ss.SceneMark); ok {
			return sceneEstimateFeeFromExtendDataNoPay(ss)
		}
	}
	return nil
}

func SetSeatNum(opt string) func() (k, v string) {
	return func() (k, v string) {
		return "seat_num", opt
	}
}

func SetIsCarpoolSuccess(opt string) func() (k, v string) {
	return func() (k, v string) {
		return "is_carpool_success", opt
	}
}

func (s *ProductInfoFull) GetOneSceneDataByOption(opts ...func() (k, v string)) *SceneEstimateFee {
	//命中一个即返回，当前场景是否仅一个，又上层业务自己决策
	for _, ss := range s.hu.ExtendList {
		bMatch := true
		for _, opt := range opts {
			k, v := opt()
			if tv, ok := ss.SceneMark[k]; !ok || tv != v {
				bMatch = false
			}
		}
		if bMatch {
			return sceneEstimateFeeFromExtendData(ss, s.hu.PaymentInfo)
		}
	}

	return nil
}

func (s *ProductInfoFull) GetBillInfoCurrency() string {
	if s.hu == nil || s.hu.BillInfo == nil {
		return ""
	}
	return s.hu.BillInfo.Currency
}

func (s *ProductInfoFull) GetCapPrice() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.CapPrice
}

// 是否使用优惠
func (s *ProductInfoFull) IsUseDiscount() bool {
	if tripcloud.IsTripcloudProductID(CommonConsts.ProductID(s.Product.ProductID)) {
		return false
	}
	return true
}

////GetCarpoolFailedEstimateFee 获取未拼成价格
////Deprecated 未拼成价格理论上只需要关注is_carpool_success
//func (s *ProductInfoFull) GetCarpoolFailedEstimateFee() (float64, bool) {
//	sceneData := s.GetSceneDataBySceneMark(map[string]string{
//		"pool_num":           "0",
//		"seat_num":           "0",
//		"is_carpool_success": "0",
//	})
//
//	if sceneData == nil {
//		return 0, false
//	}
//	return sceneData.GetFee(), true
//}

func (s *ProductInfoFull) GetCarpoolFailEstimateFee() (float64, bool) {
	sceneData := s.GetOneSceneDataByOption(SetIsCarpoolSuccess("0"))
	if sceneData == nil {
		return 0, false
	}

	return sceneData.GetFee(), true
}

func (s *ProductInfoFull) GetCarpoolFailRawBill() *price_api.BillInfoV3 {
	sceneData := s.GetOneSceneDataByOption(SetIsCarpoolSuccess("0"))
	if sceneData == nil {
		return nil
	}

	return sceneData.GetRawBill()
}

func (s *ProductInfoFull) GetCarpoolFailCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	sceneData := s.GetOneSceneDataByOption(SetIsCarpoolSuccess("0"))
	if sceneData == nil || sceneData.discountSet == nil {
		return nil
	}

	return sceneData.discountSet.Coupon
}

// Deprecated: 有问题
func (s *ProductInfoFull) GetCarpoolFailedBill() *price_api.BillInfoV3 {
	sceneData := s.GetSceneDataBySceneMark(map[string]string{
		"pool_num":           "0",
		"seat_num":           "0",
		"is_carpool_success": "0",
	})

	if sceneData == nil {
		return nil
	}
	return sceneData.GetRawBill()
}

// Deprecated: 有问题
func (s *ProductInfoFull) GetCarpoolFailedCoupon() *PriceApi.EstimateNewFormCouponInfo {
	sceneData := s.GetSceneDataBySceneMark(map[string]string{
		"pool_num":           "0",
		"seat_num":           "0",
		"is_carpool_success": "0",
	})

	if sceneData == nil || sceneData.discountSet == nil || sceneData.discountSet.Coupon == nil {
		return nil
	}

	return sceneData.discountSet.Coupon
}

func (s *ProductInfoFull) GetCarpoolFailedCouponFromRawData() *PriceApi.EstimateNewFormCouponInfo {
	if len(s.hu.ExtendList) > 0 {
		for _, failedData := range s.hu.ExtendList {
			if failedData.DiscountSet != nil && failedData.DiscountSet.Coupon != nil {
				return failedData.DiscountSet.Coupon
			}
		}
	}
	return nil
}

func (s *ProductInfoFull) GetExtendList() []*PriceApi.EstimateNewFormExtend {
	if s.hu == nil {
		return nil
	}
	return s.hu.ExtendList
}

func (s *ProductInfoFull) GetCarpoolFailExactEstimateFee() float64 {
	if len(s.GetExtendList()) == 1 {
		return s.GetExtendList()[0].ExactEstimateFee
	}

	return 0
}

func (s *ProductInfoFull) GetDirectEstimatePrice() SceneEstimateFeeViewer {
	return sceneEstimateFeeFromEstimateData(s.hu)
}

func (s *ProductInfoFull) GetBillDisplayLines() []*PriceApi.DisplayLine {
	if s.hu == nil || s.hu.BillInfo == nil {
		return nil
	}
	return s.hu.BillInfo.DisplayLines
}

func (s *ProductInfoFull) GetDisplayLines() map[string]*PriceApi.DisplayLine {
	if s.hu == nil || s.hu.BillInfo == nil {
		return nil
	}

	var displayLinesMap = make(map[string]*PriceApi.DisplayLine)

	for _, displayLine := range s.hu.BillInfo.DisplayLines {
		displayLinesMap[displayLine.Name] = displayLine
	}

	return displayLinesMap
}

func (s *ProductInfoFull) GetFeeDetailInfoAmount(name string) float64 {
	if s.hu == nil || s.hu.BillInfo == nil || len(s.hu.BillInfo.FeeDetailInfo) == 0 {
		return 0
	}
	if s.hu.BillInfo.FeeDetailInfo[name] > 0 {
		return s.hu.BillInfo.FeeDetailInfo[name]
	}

	return 0
}

func (s *ProductInfoFull) GetBillFeeDetailInfo() map[string]float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return nil
	}
	return s.hu.BillInfo.FeeDetailInfo
}
func (s *ProductInfoFull) GetBillInfo() *PriceApi.EstimateNewFormBillInfo {
	if s.hu == nil || s.hu.BillInfo == nil {
		return nil
	}
	return s.hu.BillInfo
}

func (s *ProductInfoFull) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return s.GetBillInfo()
}

func (s *ProductInfoFull) GetBillTotalFee() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.TotalFee
}

func (s *ProductInfoFull) GetRealEstimateFee() (float64, bool) {
	if s.hu == nil {
		return 0, false
	}
	return s.hu.EstimateFee, true
}

func (s *ProductInfoFull) GetDynamicDiffPrice() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.DynamicDiffPrice
}

func (s *ProductInfoFull) GetDynamicTotalFee() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.DynamicTotalFee
}

func (s *ProductInfoFull) GetDynamicTimes() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.DynamicTimes
}

// GetDupsDynamicRaise 开平新动调 涨价
func (s *ProductInfoFull) GetDupsDynamicRaise() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}

	return s.hu.BillInfo.DupsDynamicRaise
}

// GetDupsDynamicTimes 开平新动调 倍数
func (s *ProductInfoFull) GetDupsDynamicTimes() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}

	return s.hu.BillInfo.DupsDynamicTimes
}

// GetNewDynamicDiffPrice 有新动调取新动调
func (s *ProductInfoFull) GetNewDynamicDiffPrice() float64 {
	if s.GetDupsDynamicRaise() > 0 {
		return s.GetDupsDynamicRaise()
	}

	return s.GetDynamicDiffPrice()
}

func (s *ProductInfoFull) GetHighWayFee() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.HighwayFee
}

func (s *ProductInfoFull) GetCountPriceType() int32 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.CountPriceType
}

func (s *ProductInfoFull) GetDefaultPayType() int32 {
	if s.hu == nil || s.hu.PaymentInfo == nil {
		return 2
	}
	return s.hu.PaymentInfo.DefaultPayType
}

func (s *ProductInfoFull) GetBusinessConstSetByPayType(payType int32) int32 {
	if s.hu == nil || s.hu.PaymentInfo == nil || len(s.hu.PaymentInfo.Payment) == 0 {
		return 0
	}
	for _, paymentInfoItem := range s.hu.PaymentInfo.Payment {
		if paymentInfoItem.PayType == payType {
			return paymentInfoItem.BusinessConstSet
		}
	}
	return 0
}

func (s *ProductInfoFull) IsBusinessPay() bool {
	return s.GetDefaultPayType() == consts.BusinessPaymentType ||
		s.GetDefaultPayType() == consts.BusinessPayByTeam
}

func (s *ProductInfoFull) GetEstimateFee() float64 {
	if s.hu == nil {
		return 0
	}
	return s.hu.EstimateFee
}

func (s *ProductInfoFull) GetCombineEstimateFee() float64 {
	if s.hu == nil {
		return 0
	}
	return s.hu.CombineEstimateFee
}

func (s *ProductInfoFull) SetBargainEstimateFee(amount float64) { // 谨慎使用
	if s.hu == nil {
		return
	}
	s.hu.EstimateFee = amount
	s.hu.PersonalEstimateFee = amount
}

func (s *ProductInfoFull) GetPersonalEstimateFee() float64 {
	if s.hu == nil {
		return 0
	}
	return s.hu.PersonalEstimateFee
}

func (s *ProductInfoFull) GetBusinessPaymentFee() float64 {
	if s.hu == nil {
		return 0
	}
	if s.hu.PaymentInfo != nil || s.hu.PaymentInfo.MixedPayDeductInfo != nil {
		return s.hu.EstimateFee
	}
	deductFee := s.hu.PaymentInfo.MixedPayDeductInfo.DeductFee
	if deductFee > s.hu.EstimateFee {
		return s.hu.EstimateFee
	}
	return deductFee
}

func (s *ProductInfoFull) GetPreTotalFee() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.PreTotalFee
}

func (s *ProductInfoFull) GetTotalFeeWithoutDiscount() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.TotalFeeWithoutDiscount
}

func (s *ProductInfoFull) GetHistoryExtraMap() interface{} {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}
	return s.hu.BillInfo.HistoryExtraMap
}
func (s *ProductInfoFull) GetBillExtraMap() map[string]interface{} {
	if s.GetHistoryExtraMap() == nil {
		return nil
	}

	extraMap, ok := s.GetHistoryExtraMap().(map[string]interface{})
	if !ok || len(extraMap) == 0 {
		return nil
	}
	return extraMap
}

func (s *ProductInfoFull) GetStartPrice() float64 {

	if s.hu == nil || s.hu.BillInfo == nil || s.hu.BillInfo.HistoryExtraMap == nil {
		return 0
	}
	extraMap, ok := s.hu.BillInfo.HistoryExtraMap.(map[string]interface{})
	if !ok || len(extraMap) == 0 {
		return 0
	}
	startPrice, ok := extraMap["start_price"].(json.Number)
	if !ok {
		return 0
	}
	return util.ToFloat64(string(startPrice))
}
func (s *ProductInfoFull) GetBasicTotalFee() float64 {

	if s.hu == nil || s.hu.BillInfo == nil || s.hu.BillInfo.HistoryExtraMap == nil {
		return 0
	}
	extraMap, ok := s.hu.BillInfo.HistoryExtraMap.(map[string]interface{})
	if !ok || len(extraMap) == 0 {
		return 0
	}
	BasicTotalFee, ok := extraMap["basic_total_fee"].(json.Number)
	if !ok {
		return 0
	}
	return util.ToFloat64(string(BasicTotalFee))
}
func (s *ProductInfoFull) GetHaveTollStation() bool {

	if s.hu == nil || s.hu.BillInfo == nil || s.hu.BillInfo.HistoryExtraMap == nil {
		return false
	}
	extraMap, ok := s.hu.BillInfo.HistoryExtraMap.(map[string]interface{})
	if !ok || len(extraMap) == 0 {
		return false
	}
	haveTollStation, ok := extraMap["have_toll_station"].(bool)
	if !ok {
		return false
	}
	return haveTollStation
}

func (s *ProductInfoFull) GetBargainRecommendInfo() *bargain.RecommendPriceInfo {
	if s.GetBillExtraMap() == nil {
		return nil
	}

	if recommendInfo := bargain.GetRecommendInfo(s.GetBillExtraMap()); recommendInfo != nil {
		return recommendInfo.BubbleInfo
	}
	return nil
}

func (s *ProductInfoFull) GetFastRangeRecommendInfo(feeItem string) string {
	extraMap := s.GetBillExtraMap()
	recommendInfo := map[string]string{}
	if extraMap == nil {
		return ""
	}

	if len(fmt.Sprint(extraMap[consts.FastRangeFeeInfo])) > 0 {
		err := json.Unmarshal([]byte(fmt.Sprint(extraMap[consts.FastRangeFeeInfo])), &recommendInfo)
		if err != nil {
			return ""
		}
	}

	if fee, ok := recommendInfo[feeItem]; ok {
		return fee
	}

	return ""
}

func (s *ProductInfoFull) ShowAnimation() bool {
	if s.GetBillExtraMap() == nil {
		return false
	}

	show, ok := s.GetBillExtraMap()["show_animation"].(json.Number)
	if !ok {
		return false
	}

	return util.ToInt32(string(show)) == consts.Enable
}

// EnableFeeRange 是否命中特快range
func (s *ProductInfoFull) EnableFeeRange() bool {
	if sceneType := s.GetSceneDataBySceneMarkNoPay(map[string]string{
		"is_a_plus_range": "1",
	}); sceneType != nil {
		return true
	}

	return false
}

// GetMinFeeRange 特快range最低价
func (s *ProductInfoFull) GetMinFeeRange() float64 {
	if sceneType := s.GetSceneDataBySceneMarkNoPay(map[string]string{
		"is_a_plus_range": "1",
	}); sceneType != nil {
		return sceneType.GetFee()
	}

	return 0.0
}

func (s *ProductInfoFull) GetCouponAmount() string {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.Coupon == nil || s.hu.DiscountSet.Coupon.Amount == "" {
		return ""
	}
	iAmount, err := strconv.ParseFloat(s.hu.DiscountSet.Coupon.Amount, 64)
	if err != nil {
		return ""
	}
	sAmount := util.FormatPrice(iAmount/100, -1)
	return sAmount
}

func (s *ProductInfoFull) GetCouponAmountFloat64() (float64, bool) { // todo
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.Coupon == nil || s.hu.DiscountSet.Coupon.Amount == "" {
		return 0, false
	}

	iAmount, err := strconv.ParseFloat(s.hu.DiscountSet.Coupon.Amount, 64)
	if err != nil {
		return 0, false
	}

	return iAmount / 100, true
}

func (s *ProductInfoFull) GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.Coupon == nil || s.hu.DiscountSet.Coupon.Amount == "" {
		return nil
	}
	return s.hu.DiscountSet.Coupon
}

func (s *ProductInfoFull) GetExternalCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.ExternalCoupon == nil || s.hu.DiscountSet.ExternalCoupon.Amount == "" {
		return nil
	}
	return s.hu.DiscountSet.ExternalCoupon
}

func (s *ProductInfoFull) GetMultiPriceCouponInfoByScene(scene map[string]string) *PriceApi.EstimateNewFormCouponInfo {
	if s.hu == nil || len(s.hu.ExtendList) < 1 {
		return nil
	}
	sceneData := s.GetSceneDataBySceneMark(scene)
	if sceneData == nil || sceneData.discountSet == nil {
		return nil
	}
	return sceneData.discountSet.Coupon
}

func (s *ProductInfoFull) GetBonus() *PriceApi.EstimateNewFormBonusInfo {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.Bonus == nil || s.hu.DiscountSet.Bonus.Amount == "" {
		return nil
	}
	return s.hu.DiscountSet.Bonus
}

func (s *ProductInfoFull) GetBonusAmountStr() string {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.Bonus == nil || s.hu.DiscountSet.Bonus.Amount == "" {
		return ""
	}
	iAmount, err := strconv.ParseFloat(s.hu.DiscountSet.Bonus.Amount, 64)
	if err != nil {
		return ""
	}
	sAmount := util.FormatPrice(iAmount/100, -1)
	return sAmount
}

func (p *ProductInfoFull) GetRevolvingAccountDiscount() *PriceApi.RevolvingAccountDiscountInfo {
	if p.hu == nil || p.hu.DiscountSet == nil || p.hu.DiscountSet.RevolvingAccountDiscountInfo == nil || p.hu.DiscountSet.RevolvingAccountDiscountInfo.Amount == "" {
		return nil
	}
	return p.hu.DiscountSet.RevolvingAccountDiscountInfo
}

func (p *ProductInfoFull) GetRevolvingAccountRebate() *PriceApi.RevolvingAccountRebateInfo {
	if p.hu == nil || p.hu.DiscountSet == nil || p.hu.DiscountSet.RevolvingAccountRebateInfo == nil || p.hu.DiscountSet.RevolvingAccountRebateInfo.Amount == "" {
		return nil
	}
	return p.hu.DiscountSet.RevolvingAccountRebateInfo
}

func (s *ProductInfoFull) GetSpsLikeWaitReward() *PriceApi.NewFormSpsLikeWaitReward {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.SpsLikeWaitReward == nil || s.hu.DiscountSet.SpsLikeWaitReward.Amount == 0 {
		return nil
	}
	return s.hu.DiscountSet.SpsLikeWaitReward
}

func (s *ProductInfoFull) GetPBDCallReturn() *PriceApi.NewFormPbdCallReturn {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.PbdCallReturn == nil || s.hu.DiscountSet.PbdCallReturn.Amount == 0 {
		return nil
	}
	return s.hu.DiscountSet.PbdCallReturn
}

func (s *ProductInfoFull) GetHkBonus() *PriceApi.EstimateNewFormBonusInfo {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.HkBonus == nil || s.hu.DiscountSet.HkBonus.Amount == "" {
		return nil
	}
	return s.hu.DiscountSet.Bonus
}

// GetAllPaymentOption ...
func (s *ProductInfoFull) GetAllPaymentOption() (paymentOption []*price_api.PaymentOption) {
	if s.hu == nil {
		return nil
	}
	if s.hu.PaymentInfo == nil {
		log.Trace.Warnf(context.TODO(), trace.DLTagUndefined, "PaymentInfo is nil")
		return paymentOption
	}

	return s.hu.PaymentInfo.Payment
}

func (s *ProductInfoFull) GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo {
	if s.hu == nil {
		return nil
	}
	return s.hu.PaymentInfo
}

func (s *ProductInfoFull) GetETPInfo() *AthenaApiv3.EstimateEtpEtdInfo {
	if s.Product.BizInfo == nil {
		return nil
	}
	return s.Product.BizInfo.AthenaEstimateEtpEtdInfo

}

// GetMixedDeductPrice 返回企业付 抵扣金额
func (s *ProductInfoFull) GetMixedDeductPrice() float64 {
	var deductInfo float64

	if !s.IsBusinessPay() {
		return 0.0
	}

	deductInfo = s.GetEstimateFee()

	if payInfo := s.GetPaymentInfo(); payInfo != nil && payInfo.MixedPayDeductInfo != nil && payInfo.MixedPayDeductInfo.DeductFee > 0 {
		deductInfo = math.Min(deductInfo, payInfo.MixedPayDeductInfo.DeductFee)
	}

	return deductInfo
}

func (s *ProductInfoFull) GetAllPaymentOptions() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := s.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (s *ProductInfoFull) GetCurrentPaymentType() int {
	opts := s.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (s *ProductInfoFull) GetVCard() *PriceApi.EstimateNewFormVCardInfo {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.VCard == nil {
		return nil
	}
	return s.hu.DiscountSet.VCard
}

func (s *ProductInfoFull) GetSpecZiYouBao() *PriceApi.EstimateNewFormSpecZiYoubao {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.SpecZiyoubao == nil {
		return nil
	}

	return s.hu.DiscountSet.SpecZiyoubao
}

func (s *ProductInfoFull) GetBusCard() *PriceApi.NewFormBusCard {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.BusCard == nil {
		return nil
	}

	return s.hu.DiscountSet.BusCard
}

func (s *ProductInfoFull) GetMemberDiscountCard() *PriceApi.DiscountCard {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.DiscountCard == nil {
		return nil
	}

	return s.hu.DiscountSet.DiscountCard
}

func (s *ProductInfoFull) GetTCDiscountFee() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}

	return s.hu.BillInfo.TripCloudDiscountFee
}

func (s *ProductInfoFull) GetAlipayCoupon() *PriceApi.EstimateNewFormCouponInfo {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.ExternalCoupon == nil {
		return nil
	}

	if "alipay" == s.hu.DiscountSet.ExternalCoupon.CouponSource {
		return s.hu.DiscountSet.ExternalCoupon
	}

	return nil
}

func (s *ProductInfoFull) GetRenderDiscount() []*PriceApi.RenderDiscount {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.RenderDiscount == nil {
		return nil
	}

	return s.hu.DiscountSet.RenderDiscount
}

func (s *ProductInfoFull) GetRight() *PriceApi.EstimateNewFormRightInfo {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.Right == nil {
		return nil
	}

	return s.hu.DiscountSet.Right
}

func (s *ProductInfoFull) GetCard() *PriceApi.EstimateNewFormCardInfo {
	if s.hu == nil || s.hu.DiscountSet == nil || s.hu.DiscountSet.Card == nil {
		return nil
	}

	return s.hu.DiscountSet.Card
}

// GetMemberDynamicProtectFee 获取溢价保护
func (s *ProductInfoFull) GetMemberDynamicProtectFee() float64 {
	if s.hu == nil || s.hu.BillInfo == nil {
		return 0
	}

	return s.hu.BillInfo.DynamicMemberReduce
}

// GetDiscountSet 获取discount set
func (s *ProductInfoFull) GetDiscountSet() *PriceApi.EstimateNewFormDiscountSet {
	if s.hu == nil || s.hu.DiscountSet == nil {
		return nil
	}

	return s.hu.DiscountSet
}

// IsHaveCarpoolVCard 是否用卡
func (s *ProductInfoFull) IsHaveCarpoolVCard(ctx context.Context) bool {
	if vCard := s.GetVCard(); vCard != nil {
		if apollo.FeatureToggle(ctx, "gs_carpool_commute_card_open", "", map[string]string{
			"city":      strconv.Itoa(int(s.BaseReqData.AreaInfo.City)),
			"menu_id":   s.BaseReqData.CommonInfo.MenuID,
			"page_type": "0",
		}) {
			switch vCard.Source {
			case "send":
				return true
			case "send_loss":
				return true
			case "usable":
				return true
			case "usable_give":
				return true
			default:
				return false
			}
		}
	}

	return false
}

func (s *ProductInfoFull) GetHolidayFee() (amount float64) {
	if s.GetBillDisplayLines() == nil {
		return
	}

	for _, item := range s.GetBillDisplayLines() {
		if item.Name == "red_packet" && item.Value > 0.0 {
			amount = item.Value
		}
	}

	return
}

func (s *ProductInfoFull) GetEstimateFeeAmount() float64 {
	fee := s.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	return fee.GetFee()
}

func (s *ProductInfoFull) GetBusinessPayAmount() float64 {
	fee := s.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}

	paymentType := s.GetCurrentPaymentType()
	if consts.BusinessPaymentType == paymentType {
		return fee.GetFee()
	}

	return 0
}
func (s *ProductInfoFull) GetBonusAmount() float64 {
	fee := s.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

//func (s *ProductInfoFull) GetCoupon() *fee_info_render.FeeItem {
//	fee := s.GetDirectEstimatePrice()
//	if fee == nil {
//		return nil
//	}
//	coupon := fee.GetFeeDetail().GetCoupon()
//	if coupon == nil {
//		return nil
//	}
//	return &fee_info_render.FeeItem{
//		Tag:    coupon.Tag,
//		Amount: coupon.Amount,
//	}
//}

func (s *ProductInfoFull) IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool {
	if !carpool.IsCarpoolDualPriceFull(s) {
		return false
	}

	if s.GetExtendList() == nil {
		return false
	}

	if s.IsHaveCarpoolVCard(ctx) {
		return true
	}

	var (
		carpoolSuccessFee float64
		carpoolFailFee    float64
	)
	carpoolSuccessFee = s.GetEstimateFee()
	if carpoolFailFeeTmp, ok := s.GetCarpoolFailEstimateFee(); ok {
		carpoolFailFee = carpoolFailFeeTmp
	}

	if carpoolSuccessFee <= 0 || carpoolFailFee <= 0 {
		return false
	}

	formatedSucc := carpool3.FormatPrice(carpoolSuccessFee, carpool3.PageDefault, util.ToString(s.GetCityID()), util.ToString(s.GetUserPID()), math.Ceil(carpoolSuccessFee*10)/10).FloatVal
	formatedFail := carpool3.FormatPrice(carpoolFailFee, carpool3.PageDefault, util.ToString(s.GetCityID()), util.ToString(s.GetUserPID()), math.Ceil(carpoolFailFee*10)/10).FloatVal
	return formatedSucc == formatedFail
}
func (s *ProductInfoFull) GetMultiPriceCouponInfo() {

}

func (s *ProductInfoFull) GetDriverPrice() *float64 {
	if s.GetBillExtraMap() == nil {
		return nil
	}
	driverPriceStr, ok := s.GetBillExtraMap()["dri_price_for_huoyun"]
	if !ok {
		return nil
	}
	driverPrice, err := util.ConvertToFloat64(driverPriceStr)
	if err != nil {
		return nil
	}
	return &driverPrice
}

// GetCarpoolScenePrice 获取拼成乐价格
func (s *ProductInfoFull) GetCarpoolScenePrice() []*PriceApi.CarpoolScenePrice {
	return make([]*PriceApi.CarpoolScenePrice, 0)
}

// GetTicketSeatInfo 获取大巴票信息
func (s *ProductInfoFull) GetTicketSeatInfo() string {
	if s.hu == nil || s.hu.BillInfo == nil {
		return ""
	}
	return s.hu.BillInfo.TicketSeatInfo
}

// 获取大巴票价
func (s *ProductInfoFull) GetBusTicketPriceByType(passengerType int32) float64 {
	ticketSeatInfoStr := s.GetTicketSeatInfo()
	if ticketSeatInfoStr == "" {
		return 0
	}
	ticketSeatInfoList := make([]*PriceApi.TicketDetailInfo, 0)
	if err := json.Unmarshal([]byte(ticketSeatInfoStr), &ticketSeatInfoList); err != nil {
		return 0
	}
	for _, ticketSeatInfo := range ticketSeatInfoList {
		if passengerType == ticketSeatInfo.Type {
			actualPrice := GoCast.ToInt64(ticketSeatInfo.ActualPrice)
			return util.ToFloat64(util.FormatFenToYuan(actualPrice, 2))
		}
	}
	return 0
}

// 获取大巴省的钱=可核销的票数量*(当前订单起终点的基础定价 - 卡总额/卡次数)
func (s *ProductInfoFull) GetBusCardCheaperFee() float64 {
	adultPrice := util.Float64Hundred(s.GetBusTicketPriceByType(int32(seat_selection_consts.Adult)))
	if s.GetBusCard() == nil {
		return 0
	}
	if s.GetBusCard().CardTotalPrice <= 0 || s.GetBusCard().CardTotalTimes <= 0 || s.GetBusCard().DiscountTimes <= 0 {
		return 0
	}
	d1 := decimal.NewFromInt(s.GetBusCard().CardTotalPrice)
	d2 := decimal.NewFromInt(GoCast.ToInt64(s.GetBusCard().CardTotalTimes))
	eachFee := d1.DivRound(d2, 2).IntPart() // 四舍五入计算单次价格

	discountAmount := adultPrice - eachFee
	//避免负数的情况
	if discountAmount <= 0 {
		return 0
	}
	//此处使用真正可以抵扣的次数来计算
	d3 := decimal.NewFromInt(discountAmount)
	d4 := decimal.NewFromInt(s.GetBusCard().DiscountTimes)
	d5 := decimal.New(1, 2)
	fee, _ := d3.Mul(d4).DivRound(d5, 2).Float64()
	return fee
}

func (s *ProductInfoFull) GetPaymentType() int32 {
	return s.Product.PaymentsType
}
