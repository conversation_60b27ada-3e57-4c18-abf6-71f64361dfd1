package biz_runtime

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"strconv"

	Member "git.xiaojukeji.com/dirpc/dirpc-go-http-Member"

	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	ApolloSDK "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/member"
)

type MemberPrivilegeRpc struct {
	BaseReqData         *models.BaseReqData
	ProductPrivilegeMap map[string]*GoMember.ProductListRespV2 // 品类会员权益Map
	UseDpaMap           map[int64]*DpaInfo                     // 缓存是否使用DPA
}
type DpaInfo struct {
	IsUserUseDpa   bool  // 是否用户主动选择
	UseDpaSelected int32 // 是否使用dpa
}

const (
	ApolloShowSwitch = "gs_price_privilege_show_switch"
	ApolloOpenSwitch = "add_car_price_privilege_switch"

	PricePrivilegeTypeFastUpDefault = int32(1) // 权益计价类型  1: 快车价格打专车
)

func NewMemberPrivilegeRPC(baseReqData *models.BaseReqData, multiRequireProduct []models.RequireProduct) *MemberPrivilegeRpc {
	rpc := &MemberPrivilegeRpc{
		BaseReqData: baseReqData,
	}

	if len(multiRequireProduct) > 0 {
		rpc.UseDpaMap = make(map[int64]*DpaInfo)
		for _, product := range multiRequireProduct {
			if product.UseDpa != nil {
				rpc.UseDpaMap[product.ProductCategory] = &DpaInfo{
					IsUserUseDpa:   true,
					UseDpaSelected: *product.UseDpa,
				}
			} else {
				rpc.UseDpaMap[product.ProductCategory] = &DpaInfo{
					IsUserUseDpa:   false,
					UseDpaSelected: 1,
				}
			}
		}
	}

	return rpc
}

// 获取rpc错误信息
func (m *MemberPrivilegeRpc) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (rpc *MemberPrivilegeRpc) Fetch(ctx context.Context, products []*models.Product) bool {
	var productList []member.Product
	for _, p := range products {
		memberProduct := member.Product{
			BusinessId:   p.BusinessID,
			RequireLevel: p.RequireLevelInt,
			ComboType:    p.ComboType,
			CarpoolType:  p.CarpoolType,
		}
		productList = append(productList, memberProduct)
	}
	var enableNewWaitReply int32
	if rpc.BaseReqData.CommonInfo.AccessKeyID == 19 {
		enableNewWaitReply = 1
	} else {
		enableNewWaitReply = 0
	}
	res, err := member.MultiProductQuery(ctx, rpc.BaseReqData.PassengerInfo.UID, rpc.BaseReqData.AreaInfo.City, productList, enableNewWaitReply)
	if err != nil {
		// log.Trace.Warnf(ctx, trace.DLTagThriftFailed, "MultiProductQuery error:%v", err)
		return false
	}
	if res.Errno != 0 || res.Data == nil || len(res.Data.ProductList) <= 0 {
		//log.Trace.Warnf(ctx, trace.DLTagThriftFailed, "MultiProductQuery error msg=[%s]", res.Errmsg)
		return false
	}

	// 缓存各个品类权益
	rpc.ProductPrivilegeMap = make(map[string]*GoMember.ProductListRespV2, len(res.Data.ProductList))
	for _, productPrivilege := range res.Data.ProductList {
		key := fmt.Sprintf("%d-%d-%d-%d", productPrivilege.ProductInfo.BusinessId, productPrivilege.ProductInfo.RequireLevel,
			productPrivilege.ProductInfo.ComboType, productPrivilege.ProductInfo.CarpoolType)
		rpc.ProductPrivilegeMap[key] = productPrivilege
	}

	return true
}

func (rpc *MemberPrivilegeRpc) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
}

// BuildProductBizInfo 把各个品类权益放入privateBizInfo
func (rpc *MemberPrivilegeRpc) BuildProductBizInfo(ctx context.Context, product models.Product, privateBizInfo *models.PrivateBizInfo) {
	key := fmt.Sprintf("%d-%s-%d-%d", product.BusinessID, product.RequireLevel,
		product.ComboType, product.CarpoolType)

	if rpc.ProductPrivilegeMap[key] == nil {
		return
	}
	memberInfo := rpc.ProductPrivilegeMap[key]

	rpc.buildMemberPrivilegeList(privateBizInfo, memberInfo)
	rpc.buildPricePrivilege(ctx, product, privateBizInfo, memberInfo)
	rpc.buildMemberProfile(product, privateBizInfo, memberInfo)
}

func (rpc *MemberPrivilegeRpc) buildMemberPrivilegeList(privateBizInfo *models.PrivateBizInfo,
	memberInfo *GoMember.ProductListRespV2) {
	privateBizInfo.MemberPrivilegeList = make(map[string]interface{}, 0)

	airportPickUpPrivilege := memberInfo.Privileges["airport_pick_up"]
	//机场助理权益
	if airportPickUpPrivilege != nil {
		privateBizInfo.MemberPrivilegeList["airport_pick_up"] = 1
	}

	return
}

func (rpc *MemberPrivilegeRpc) buildMemberProfile(product models.Product, privateBizInfo *models.PrivateBizInfo,
	memberInfo *GoMember.ProductListRespV2) {
	privateBizInfo.MemberProfile = &Member.V1QueryInfo{
		LevelId: memberInfo.LevelId,
	}
	privateBizInfo.MemberProfile.LevelName = memberInfo.LevelName
	privateBizInfo.MemberProfile.LevelIcon = memberInfo.LevelIcon
	privateBizInfo.LevelID = memberInfo.LevelId

	// 若预估上传的multi_require_products 标识了是否使用dpa 则尊重其结果，否则默认使用
	if dpaInfo, ok := rpc.UseDpaMap[product.ProductCategory]; ok {
		privateBizInfo.IsUserUseDpa = dpaInfo.IsUserUseDpa
		privateBizInfo.UseDpaSelected = dpaInfo.UseDpaSelected
		// 不使用DPA不展示会员权益
		if dpaInfo.UseDpaSelected == 0 {
			return
		}
	}

	privateBizInfo.LevelID = memberInfo.LevelId

	// 查找dpa 权益
	if memberInfo.Privileges == nil {
		return
	}

	var (
		dpaItem      *GoMember.PrivInfo
		fastWayItem  *GoMember.PrivInfo
		privilegeMap = make(map[string]*GoMember.PrivInfo, 0)
	)

	if memberInfo.Privileges["dpa"] != nil {
		dpaItem = adaptDPA(memberInfo.Privileges["dpa"])
		if dpaItem != nil {
			privilegeMap["dpa"] = dpaItem
		}
	}

	if memberInfo.Privileges["fast_way"] != nil {
		fastWayItem = adaptFastWay(memberInfo.Privileges["fast_way"])
		if fastWayItem != nil {
			privilegeMap["fast_way"] = fastWayItem
		}
	}

	privateBizInfo.MemberProfile = &Member.V1QueryInfo{
		LevelId:    memberInfo.LevelId,
		LevelName:  memberInfo.LevelName,
		LevelIcon:  memberInfo.LevelIcon,
		Privileges: privilegeMap,
	}
	privateBizInfo.MemberProfile.Privileges = privilegeMap
}

// buildPricePrivilege 设置品类权益到privateBizInfo
func (rpc *MemberPrivilegeRpc) buildPricePrivilege(ctx context.Context, product models.Product,
	privateBizInfo *models.PrivateBizInfo, memberInfo *GoMember.ProductListRespV2) {

	privilege := memberInfo.Privileges["other_category"]
	if privilege == nil || len(privilege.Item) == 0 {
		return
	}

	params := map[string]string{
		"key":           strconv.FormatInt(rpc.BaseReqData.PassengerInfo.PID, 10),
		"phone":         rpc.BaseReqData.PassengerInfo.Phone,
		"passenger_id":  strconv.FormatInt(rpc.BaseReqData.PassengerInfo.PID, 10),
		"product_id":    strconv.FormatInt(product.ProductID, 10),
		"combo_type":    strconv.FormatInt(product.ComboType, 10),
		"require_level": product.RequireLevel,
		"type":          strconv.FormatInt(int64(product.OrderType), 10),
		"city":          strconv.FormatInt(int64(rpc.BaseReqData.AreaInfo.City), 10),
		"access_key_id": strconv.FormatInt(int64(rpc.BaseReqData.CommonInfo.AccessKeyID), 10),
		"app_version":   rpc.BaseReqData.CommonInfo.AppVersion,
		"lang":          rpc.BaseReqData.CommonInfo.Lang,
		"payments_type": strconv.FormatInt(int64(rpc.BaseReqData.CommonInfo.PaymentsType), 10),
	}
	// 开城Apollo
	if !ApolloSDK.FeatureToggle(ctx, ApolloOpenSwitch, strconv.FormatInt(rpc.BaseReqData.PassengerInfo.UID, 10), params) {
		return
	}

	var privInfo *GoMember.PrivInfo
	for _, item := range privilege.Item {
		if !item.Enabled || item.Backend == nil ||
			item.Backend.EstimateTravelLimit == nil || *item.Backend.EstimateTravelLimit == 0 {
			// EstimateTravelLimit 预估的时候展示的阈值
			continue
		}
		privInfo = item
		break
	}

	if privInfo == nil || !privInfo.Enabled {
		return
	}

	privateBizInfo.PricePrivilegeInfo = map[string]interface{}{
		"privilege_distance_limit": *privInfo.Backend.EstimateTravelLimit * 1000,
		"upgrade_privilege_enable": 1,
	}
}

func adaptDPA(privilege *GoMember.PrivOptionsItem) *GoMember.PrivInfo {
	var privilegeItem *GoMember.PrivInfo

	if len(privilege.Item) == 1 {
		privilegeItem = privilege.Item[0]
	} else {
		var freeDap, autoDpa, paidMemberDpa *GoMember.PrivInfo
		for _, item := range privilege.Item {
			if item.Type == "free_dpa" {
				freeDap = item
				break
			}

			if item.IsAuto == 1 {
				autoDpa = item
			}

			if item.Backend != nil && item.Backend.PrivilegeSource != nil && *item.Backend.PrivilegeSource == "paid_member" {
				paidMemberDpa = item
			}
		}

		if freeDap != nil {
			privilegeItem = freeDap
		} else if paidMemberDpa != nil {
			privilegeItem = paidMemberDpa
		} else if autoDpa != nil {
			privilegeItem = autoDpa
		} else {
			privilegeItem = privilege.Item[0]
		}
	}

	return privilegeItem
}

func adaptFastWay(privilege *GoMember.PrivOptionsItem) *GoMember.PrivInfo {
	var privilegeItem *GoMember.PrivInfo

	if len(privilege.Item) == 1 {
		privilegeItem = privilege.Item[0]
	} else {
		for _, item := range privilege.Item {
			if item.Type == "rapid_response" || item.Type == "priority_response" {
				privilegeItem = item
				break
			}
		}
	}

	return privilegeItem
}
