package biz_runtime

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/ab"
)

func mockProductInfoFull() *ProductInfoFull {
	return &ProductInfoFull{
		Product: &models.Product{},
	}
}

func TestWithPageType(t *testing.T) {
	t.Run("当Product为nil时", func(t *testing.T) {
		p := &ProductInfoFull{
			Product: nil,
		}

		key, value := WithPageType(p)

		assert.Equal(t, "", key, "当Product为nil时，键应该是空字符串")
		assert.Equal(t, "", value, "当Product为nil时，值应该是空字符串")
	})

	t.Run("当Product不为nil时", func(t *testing.T) {
		p := mockProductInfoFull()

		// Mock GetPageType方法
		mockGetPageType := mockey.Mock((*ProductInfoFull).GetPageType).Return(int32(2)).Build()
		defer mockGetPageType.UnPatch()

		key, value := WithPageType(p)

		assert.Equal(t, "page_type", key, "键应该是page_type")
		assert.Equal(t, "2", value, "值应该是cast.ToString(p.GetPageType())的结果")
		assert.Equal(t, 1, mockGetPageType.Times(), "应调用一次GetPageType方法")
	})
}

func TestWithToCity(t *testing.T) {
	t.Run("当Product为nil时", func(t *testing.T) {
		p := &ProductInfoFull{
			Product: nil,
		}

		key, value := WithToCity(p)

		assert.Equal(t, "", key, "当Product为nil时，键应该是空字符串")
		assert.Equal(t, "", value, "当Product为nil时，值应该是空字符串")
	})

	t.Run("当Product不为nil时", func(t *testing.T) {
		p := mockProductInfoFull()

		// Mock GetToCityID方法
		mockGetToCityID := mockey.Mock((*ProductInfoFull).GetToCityID).Return(101).Build()
		defer mockGetToCityID.UnPatch()

		key, value := WithToCity(p)

		assert.Equal(t, "to_city", key, "键应该是to_city")
		assert.Equal(t, "101", value, "值应该是cast.ToString(p.GetToCityID())的结果")
		assert.Equal(t, 1, mockGetToCityID.Times(), "应调用一次GetToCityID方法")
	})
}

func TestWithFontScaleType(t *testing.T) {
	t.Run("当Product为nil时", func(t *testing.T) {
		p := &ProductInfoFull{
			Product: nil,
		}

		key, value := WithFontScaleType(p)

		assert.Equal(t, "", key, "当Product为nil时，键应该是空字符串")
		assert.Equal(t, "", value, "当Product为nil时，值应该是空字符串")
	})

	t.Run("当Product不为nil时", func(t *testing.T) {
		p := mockProductInfoFull()

		// Mock GetFontScaleType方法
		mockGetFontScaleType := mockey.Mock((*ProductInfoFull).GetFontScaleType).Return(int32(3)).Build()
		defer mockGetFontScaleType.UnPatch()

		key, value := WithFontScaleType(p)

		assert.Equal(t, "font_scale_type", key, "键应该是font_scale_type")
		assert.Equal(t, "3", value, "值应该是cast.ToString(p.GetFontScaleType())的结果")
		assert.Equal(t, 1, mockGetFontScaleType.Times(), "应调用一次GetFontScaleType方法")
	})
}

func TestIsHitDynamicIconAb(t *testing.T) {
	t.Run("验证IsHitDynamicIconAb方法调用了正确的参数", func(t *testing.T) {
		ctx := context.Background()
		p := mockProductInfoFull()

		// Mock GetApolloParams方法
		mockGetApolloParams := mockey.Mock((*ProductInfoFull).GetApolloParams).Return("test_pid", map[string]string{
			"page_type":       "2",
			"to_city":         "101",
			"font_scale_type": "3",
		}).Build()
		defer mockGetApolloParams.UnPatch()

		// Mock ab.IsHitDynamicIconAb
		mockAbIsHitDynamicIconAb := mockey.Mock(ab.IsHitDynamicIconAb).Return(true).Build()
		defer mockAbIsHitDynamicIconAb.UnPatch()

		result := p.IsHitDynamicIconAb(ctx)

		assert.True(t, result, "当ab.IsHitDynamicIconAb返回true时, 方法应返回true")
		assert.Equal(t, 1, mockGetApolloParams.Times(), "应调用一次GetApolloParams方法")
		assert.Equal(t, 1, mockAbIsHitDynamicIconAb.Times(), "应调用一次ab.IsHitDynamicIconAb方法")
	})
}
