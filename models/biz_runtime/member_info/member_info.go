package member_info

import (
	"fmt"

	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	midl "git.xiaojukeji.com/dirpc/dirpc-go-http-Member"
)

type MemberInfo GoMember.ProductListRespV2

func GenKey(productInfo *GoMember.ProductInfo) string {
	if productInfo == nil {
		return ""
	}

	return fmt.Sprintf("%d_%d_%d_%d", productInfo.BusinessId, productInfo.RequireLevel, productInfo.CarpoolType, productInfo.RequireLevel)
}

// MemberInfo2MemberInfo 会员类型转一下
func MemberInfo2MemberInfo(memberInfo *GoMember.ProductListRespV2) *MemberInfo {
	if memberInfo == nil {
		return nil
	}

	newMemberInfo := MemberInfo(*memberInfo)
	return &newMemberInfo
}

// ToV1 转成v1结构
func (m *MemberInfo) ToV1() *midl.V1QueryInfo {
	return &midl.V1QueryInfo{
		LevelId:    m.LevelId,
		LevelName:  m.LevelName,
		LevelIcon:  m.LevelIcon,
		Privileges: m.Privileges,
	}
}
