package biz_runtime_test

import (
	"context"
	"testing"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/ddmq"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	context2 "git.xiaojukeji.com/lego/context-go"
	"github.com/agiledragon/gomonkey/v2"
)

func TestSendEstimateReqKafka(t *testing.T) {
	// mock trace id
	mockTrace := context2.New(nil)
	ctx := context2.SetCtxTrace(context.Background(), mockTrace)

	// mock ddmq.Send
	patches := gomonkey.ApplyFunc(ddmq.Send, func(ctx context.Context, topic string, msg interface{}, hashId int64, topicType int32) error {
		return nil
	})
	defer patches.Reset()

	// 构造测试数据
	req := &models.BaseReqData{
		CommonInfo: models.CommonInfo{
			MainEstimateTraceId: "test_trace_id",
			ClientType:          1,
			AppVersion:          "3.14.0",
			AccessKeyID:         1001,
			MenuID:              "menu_001",
			PageType:            page_type.PageTypeGuideAnyCar,
			OrderType:           1,
			Lang:                "zh_CN",
			DepartureTime:       time.Now().Unix(),
			PaymentsType:        1,
			CallCarType:         1,
		},
		AreaInfo: models.AreaInfo{
			Area:            1000,
			FromCounty:      101,
			District:        "朝阳区",
			FromPoiID:       "poi_123",
			FromLng:         116.397128,
			FromLat:         39.916527,
			ToPoiID:         "poi_456",
			ToLng:           116.301234,
			ToLat:           39.987654,
			MapType:         "gcj02",
			FromName:        "北京站",
			DestName:        "北京西站",
			FromPoiType:     "poi",
			ToPoiType:       "poi",
			CurLat:          39.916527,
			CurLng:          116.397128,
			ToArea:          2000,
			ChooseFSearchid: "test_f_search",
			ChooseTSearchid: "test_t_search",
		},
		PassengerInfo: models.PassengerInfo{
			Phone:    "***********",
			PID:      10001,
			UID:      100001,
			ExtPid:   "ext_pid_001",
			UserType: 1,
		},
	}

	products := []*models.Product{
		{
			EstimateID:      "1",
			ProductID:       1,
			BusinessID:      1000,
			RequireLevelInt: 1,
			ComboType:       1,
			CarpoolType:     1,
			ProductCategory: 1,
		},
	}

	// 执行测试
	biz_runtime.SendEstimateReqKafka(ctx, req, products, "1234")
}
