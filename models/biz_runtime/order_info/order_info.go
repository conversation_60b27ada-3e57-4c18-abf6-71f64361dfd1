package order_info

import (
	"context"
	"encoding/json"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
)

type OrderInfo dos.OrderInfo
type OrderStatus int

type PassengerDetail struct {
	TicketType   int32 `json:"type"`
	IsOccupySeat int32 `json:"is_occupy_seat"`
	Status       int32 `json:"status"`
	TicketId     int32 `json:"ticket_id"`
}

const (
	DefaultEmptyID = 0

	DriverGrabbing OrderStatus = 1 // 司机抢单
	ArrivingPoint  OrderStatus = 2 // 到达上车点
	StartBilling   OrderStatus = 4 // 开始计费
)

// OrderInfo2OrderInfo 订单类型转换
func OrderInfo2OrderInfo(info *dos.OrderInfo) *OrderInfo {
	if info == nil {
		return nil
	}

	orderInfo := OrderInfo(*info)

	return &orderInfo
}

func (o OrderStatus) ToInt() int {
	return int(o)
}

// stringPtr2Int 字符串转int
func stringPtr2Int(origin *string) int {
	if origin == nil {
		return DefaultEmptyID
	}

	if len(*origin) <= 0 {
		return DefaultEmptyID
	}

	target, err := strconv.Atoi(*origin)
	if err != nil {
		return DefaultEmptyID
	}

	return target
}

// stringPtr2Flot64 字符串转float64
func stringPtr2Flot64(origin *string) float64 {
	if origin == nil {
		return DefaultEmptyID
	}

	if len(*origin) <= 0 {
		return DefaultEmptyID
	}

	target, err := strconv.ParseFloat(*origin, 64)
	if err != nil {
		return DefaultEmptyID
	}

	return target
}

// GetProductID 获取productID
func (o *OrderInfo) GetProductID() int64 {
	return int64(stringPtr2Int(&o.ProductId))
}

func (o *OrderInfo) GetProductCategory() int64 {
	return int64(stringPtr2Int(o.EstimatePcId))
}

// GetBusinessID 获取businessID
func (o *OrderInfo) GetBusinessID() int64 {
	return int64(stringPtr2Int(&o.BusinessId))
}

// GetRequireLevel 获取requireLevel
func (o *OrderInfo) GetRequireLevel() int64 {
	return int64(stringPtr2Int(&o.RequireLevel))
}

// GetCarpoolType 获取carpoolType
func (o *OrderInfo) GetCarpoolType() int64 {
	return int64(stringPtr2Int(&o.CarpoolType))
}

// GetCarpoolPriceType 获取拼车价格类型
func (o *OrderInfo) GetCarpoolPriceType() int32 {
	return int32(stringPtr2Int(&o.CarpoolPriceType))
}

// GetMultiPriceScene 获取拼车价格
func (o *OrderInfo) GetMultiPriceScene() []*PriceApi.CarpoolScenePrice {
	if len(o.MultiPriceScene) <= 0 {
		return nil
	}

	priceList := make([]*PriceApi.CarpoolScenePrice, 0)

	err := json.Unmarshal([]byte(o.MultiPriceScene), &priceList)
	if err != nil {
		return nil
	}

	return priceList
}

// GetLevelType 获取levelType
func (o *OrderInfo) GetLevelType() int32 {
	return int32(stringPtr2Int(&o.LevelType))
}

// GetComboType 获取comboType
func (o *OrderInfo) GetComboType() int64 {
	return int64(stringPtr2Int(&o.ComboType))
}

// GetIsSpecialPrice 获取是否是isSpecialPrice
func (o *OrderInfo) GetIsSpecialPrice() bool {
	if len(o.IsSpecialPrice) < 0 {
		return false
	}

	if o.IsSpecialPrice == "1" {
		return true
	}

	return false
}

// GetIsDualCarpoolPrice 获取isDualCarpoolPrice
func (o *OrderInfo) GetIsDualCarpoolPrice() bool {
	if len(o.IsDualCarpoolPrice) < 0 {
		return false
	}

	if o.IsDualCarpoolPrice == "1" {
		return true
	}

	return false
}

// GetAirportType 获取airportType
func (o *OrderInfo) GetAirportType() int32 {
	return int32(stringPtr2Int(&o.AirportType))
}

// GetRailwayType 获取railwayType
func (o *OrderInfo) GetRailwayType() int32 {
	return int32(stringPtr2Int(&o.RailwayType))
}

// GetHotelType 获取hotelType
func (o *OrderInfo) GetHotelType() int32 {
	return int32(stringPtr2Int(&o.HotelType))
}

// GetStationServiceControl 获取命中场站卡服务
func (o *OrderInfo) GetStationServiceControl() int32 {
	return int32(stringPtr2Int(&o.StationServiceControl))
}

// GetLongRentType 包车类型
func (o *OrderInfo) GetLongRentType() int16 {
	return int16(stringPtr2Int(&o.RouteType))
}

// GetEmergencyServiceType 应急类型
func (o *OrderInfo) GetEmergencyServiceType() int32 {
	return int32(stringPtr2Int(&o.EmergencyServiceType))
}

// GetRouteType 获取路线类型
func (o *OrderInfo) GetRouteType() int64 {
	return int64(stringPtr2Int(&o.RouteType))
}

// GetPayType 支付类型
func (o *OrderInfo) GetPayType() int32 {
	return int32(stringPtr2Int(&o.PayType))
}

// GetIsPickOnTime 获取极高确标识
func (o *OrderInfo) GetIsPickOnTime() int16 {
	return int16(stringPtr2Int(o.IsPickOnTime))
}

// GetType 获取订单类型
func (o *OrderInfo) GetType() int {
	return stringPtr2Int(&o.Type)
}

// GetOrderStatus 获取订单状态
func (o *OrderInfo) GetOrderStatus() int {
	return stringPtr2Int(&o.OrderStatus)
}

// GetCountryIsoCode ...
func (o *OrderInfo) GetCountryIsoCode() string {
	return o.CountryIsoCode
}

// GetArea 获取起点城市
func (o *OrderInfo) GetArea() int32 {
	return int32(stringPtr2Int(&o.Area))
}

// GetToArea 获取终点城市
func (o *OrderInfo) GetToArea() int32 {
	return int32(stringPtr2Int(&o.ToArea))
}

// GetCurrentLat 获取起点经度
func (o *OrderInfo) GetCurrentLat() float64 {
	return stringPtr2Flot64(&o.CurrentLat)
}

// GetCurrentLng 获取起点经度
func (o *OrderInfo) GetCurrentLng() float64 {
	return stringPtr2Flot64(&o.CurrentLng)
}

// GetFromLat 获取起点经度
func (o *OrderInfo) GetFromLat() float64 {
	return stringPtr2Flot64(&o.StartingLat)
}

// GetFromLng 获取起点经度
func (o *OrderInfo) GetFromLng() float64 {
	return stringPtr2Flot64(&o.StartingLng)
}

// GetToLat 获取终点经度
func (o *OrderInfo) GetToLat() float64 {
	return stringPtr2Flot64(&o.DestLat)
}

// GetToLng 获取终点经度
func (o *OrderInfo) GetToLng() float64 {
	return stringPtr2Flot64(&o.DestLng)
}

// GetChannel 获取channel
func (o *OrderInfo) GetChannel() int64 {
	return int64(stringPtr2Int(&o.Channel))
}

// GetComboID 获取comboID
func (o *OrderInfo) GetComboID() int64 {
	return int64(stringPtr2Int(&o.ComboId))
}

// GetCallCarType 获取代叫类型
func (o *OrderInfo) GetCallCarType() int64 {
	return int64(stringPtr2Int(&o.CallCar))
}

// GetPassengerCount 获取乘车人数
func (o *OrderInfo) GetPassengerCount() int32 {
	return int32(stringPtr2Int(&o.PassengerCount))
}

// GetCapPrice 获取一口价价格
func (o *OrderInfo) GetCapPrice() int32 {
	return int32(stringPtr2Flot64(&o.CapPrice))
}

// GetCounty 获取county
func (o *OrderInfo) GetCounty() int32 {
	return int32(stringPtr2Int(&o.County))
}

// GetToCounty 获取to county
func (o *OrderInfo) GetToCounty() int32 {
	return int32(stringPtr2Int(&o.ToCounty))
}

// GetOrderID 获取order id
func (o *OrderInfo) GetOrderID() int64 {
	return int64(stringPtr2Int(&o.OrderId))
}

func (o *OrderInfo) GetSceneList() []*dos.SceneItem {
	if o.SceneList == nil || len(*o.SceneList) <= 0 {
		return nil
	}

	sceneList := make([]*dos.SceneItem, 0)
	err := json.Unmarshal([]byte(*o.SceneList), &sceneList)
	if err != nil {
		return nil
	}

	return sceneList
}

func (o *OrderInfo) GetDepartureTime(ctx context.Context) int64 {
	layout := "2006-01-02 15:04:05" // Go语言中的时间格式化字符串

	// 解析时间字符串为time.Time对象
	t, err := time.ParseInLocation(layout, o.DepartureTime, time.Local)
	if err != nil {
		log.Trace.Warnf(ctx, "order_info", "get departure time fail:err:%v", err)
		return time.Now().Unix()
	}

	return t.Unix()
}
