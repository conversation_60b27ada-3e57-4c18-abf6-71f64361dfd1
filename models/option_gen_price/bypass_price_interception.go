package option_gen_price

import (
	"context"
	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"

	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
)

type BypassPriceInterception struct {
}

func NewBypassPriceInterception() *BypassPriceInterception {
	return &BypassPriceInterception{}
}

func GetSetLen(data *ticketPrice.StationPriceData) int {
	priceSet := make(map[float64]int32, 0)
	for _, v := range data.StationPrice {
		priceSet[v.Price] = 1
	}
	return len(priceSet)
}

func getCallPriceApiAbility(brd *models.BaseReqData) bool {
	var res bool = false
	if brd.CommonBizInfo.AgentType == DetailConst.FormatScanCode {
		res = true
	}
	if brd.CommonBizInfo.AgentType == DetailConst.QuickScanCode {
		res = GetSetLen(brd.CommonBizInfo.RoutePrice) == 1 ||
			brd.CommonBizInfo.AdultPrice != 0 || brd.CommonBizInfo.RoutePrice.IsPrice == DetailConst.CapPrice
	}

	brd.CommonBizInfo.NeedVisitPrice = res
	return res
}

func (s *BypassPriceInterception) ExecPrice(ctx context.Context, brd *models.BaseReqData, baseProducts []*models.Product) ([]*biz_runtime.ProductInfoFull, error) {
	// 6. 组装productInfoFull list
	var (
		products []*biz_runtime.ProductInfoFull
		priceErr error
	)
	if getCallPriceApiAbility(brd) {
		products, priceErr = biz_runtime.GetPriceInfov3(ctx, "pIntercityEstimateDetail", brd, baseProducts)
		if priceErr != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "priceErr: %v", priceErr)
			return nil, priceErr
		}
	} else {
		for _, product := range baseProducts {
			pFull := &biz_runtime.ProductInfoFull{
				BaseReqData:  brd,
				Product:      product,
				BillDetail:   nil,
				DiscountInfo: nil,
				PayInfo:      nil,
			}
			products = append(products, pFull)
		}
	}

	return products, nil
}
