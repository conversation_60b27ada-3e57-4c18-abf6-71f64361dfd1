package option_gen_price

import (
	"context"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/ticket_detail_consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// StationBusCouponTicketRebookEstimate 大巴优待票改签
type StationBusCouponTicketRebookEstimate struct {
}

func NewStationBusCouponTicketRebookEstimate(baseReqData *models.BaseReqData) *StationBusCouponTicketRebookEstimate {
	if baseReqData == nil {
		return nil
	}

	// 站点巴士改签情况下用户还没有勾选班次，不需要预估，勾选班次后才需要
	if needSkipEstimate(baseReqData) {
		return &StationBusCouponTicketRebookEstimate{}
	}

	return nil
}

// needSkipEstimate 是否需要跳过预估 改签优待票场景，用户第一次未勾选时不需要预估
func needSkipEstimate(baseReqData *models.BaseReqData) bool {
	// 非大巴改签场景 不跳过
	if baseReqData.CommonBizInfo.Scene != ticket_detail_consts.SceneRebook {
		return false
	}

	// 用户手动选择了改签班次 不跳过
	if baseReqData.CommonBizInfo.SeletedBusServiceShiftId != "" {
		return false
	}

	var rebookCouponTicket bool
	// 改签优待票跳过
	seatDetailInfo := baseReqData.CommonBizInfo.SeatDetailInfo
	for _, ticketItem := range seatDetailInfo {
		if ticketItem == nil {
			continue
		}

		if ticketItem.PassengerType == seat_selection_consts.PreferentialPeople.ToInt32() && ticketItem.PassengerCount > 0 {
			rebookCouponTicket = true
		}
	}

	return rebookCouponTicket
}

func (c *StationBusCouponTicketRebookEstimate) ExecPrice(ctx context.Context, brd *models.BaseReqData, baseProducts []*models.Product) ([]*biz_runtime.ProductInfoFull, error) {
	products := make([]*biz_runtime.ProductInfoFull, 0, 0)
	for _, product := range baseProducts {
		pFull := &biz_runtime.ProductInfoFull{
			BaseReqData:  brd,
			Product:      product,
			BillDetail:   nil,
			DiscountInfo: nil,
			PayInfo:      nil,
		}

		hu := &PriceApi.EstimateNewFormData{
			PaymentInfo: &PriceApi.EstimateNewFormPaymentInfo{},
			BillInfo:    &PriceApi.EstimateNewFormBillInfo{},
			DiscountSet: &PriceApi.EstimateNewFormDiscountSet{},
		}
		pFull.SetHu(hu)
		products = append(products, pFull)
	}
	return products, nil
}
