package option_gen_price

import (
	"context"
	"errors"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
	"runtime"
	"sync"
)

const (
	CallerEstimateCarpoolOrder = "estimate_carpool_order"
)

type CarpoolOrderEstimatePrice struct {
	BaseReqMap       map[int64]*models.BaseReqData
	CarpoolSceneData map[int64]*CarpoolSceneData
}

type CarpoolSceneData struct {
	Option           *PriceApi.CarpoolPriceSceneOption
	OrderInfo        string
	DynamicTotalFee  float64
	FeeDetailInfo    map[string]float64
	BubbleEstimateId string
}

func (c *CarpoolOrderEstimatePrice) ExecPrice(ctx context.Context, brd *models.BaseReqData, products []*models.Product) ([]*biz_runtime.ProductInfoFull, error) {
	wg := sync.WaitGroup{}

	var failedEstimate = make(chan struct{}, len(products))
	var finshedProducts = make(chan *biz_runtime.ProductInfoFull, len(products))
	var productFulls = make([]*biz_runtime.ProductInfoFull, 0)

	for _, oneProduct := range products {
		if oneProduct.BizInfo == nil || oneProduct.BizInfo.SendOrderId == 0 {
			continue
		}
		sendOid := oneProduct.BizInfo.SendOrderId
		wg.Add(1)
		go func(ctx context.Context, oneProduct *models.Product, baseReq *models.BaseReqData, sceneData *CarpoolSceneData, wgDone func()) {
			defer wgDone()
			defer func() {
				if err := recover(); err != nil {
					stack := make([]byte, 8192)
					stack = stack[:runtime.Stack(stack, false)]
					log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "multi_call panic:%v\n%s", err, stack)
				}
			}()

			fullInfo, err := getPriceInfo(ctx, oneProduct, baseReq, sceneData)
			if err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "priceErr: %v", err)
				failedEstimate <- struct{}{}
				return
			}

			if fullInfo != nil {
				finshedProducts <- fullInfo
			}

		}(ctx, oneProduct, c.BaseReqMap[sendOid], c.CarpoolSceneData[sendOid], wg.Done)
	}

	wg.Wait()
	close(finshedProducts)

	for product := range finshedProducts {
		productFulls = append(productFulls, product)
	}

	if len(failedEstimate) != 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "price part estimate failed")
	}

	return productFulls, nil
}

func getPriceInfo(ctx context.Context, product *models.Product, baseReq *models.BaseReqData, sceneData *CarpoolSceneData) (*biz_runtime.ProductInfoFull, error) {
	request := buildRequest(ctx, product, baseReq, sceneData)

	if request == nil {
		return nil, errors.New("build price req is fail")
	}

	resp, err := price_api.MultiEstimateByOrder(ctx, request)
	if err != nil {
		return nil, err
	}

	priceResp, ok := resp[product.EstimateID]
	if product == nil || !ok || priceResp == nil || priceResp.EstimateData == nil {
		return nil, errors.New("get price resp fail")
	}

	if product.BizInfo == nil {
		product.BizInfo = &models.PrivateBizInfo{}
	}

	product.BizInfo.OriginBills = priceResp.OriginBills

	productFull := &biz_runtime.ProductInfoFull{
		BaseReqData: baseReq,
		Product:     product,
	}

	productFull.SetHu(priceResp.EstimateData)

	return productFull, nil
}

func buildRequest(ctx context.Context, product *models.Product, baseReq *models.BaseReqData, sceneData *CarpoolSceneData) *PriceApi.MultiEstimateByOrderRequest {
	if product == nil {
		return nil
	}

	request := &PriceApi.MultiEstimateByOrderRequest{}
	reqs := make([]*PriceApi.EstimatePriceReq, 0)
	priceReq := biz_runtime.GenProductPriceReq(ctx, "", baseReq, product)

	if priceReq != nil {
		extraInfo := priceReq.ExtraInfo
		if extraInfo == nil {
			extraInfo = make(map[string]string)
		}

		extraInfo["order"] = util.JustJsonEncode(priceReq.OrderExt)

		reqs = append(reqs, &PriceApi.EstimatePriceReq{
			CommonInfo:        priceReq.CommonInfoSt,
			PassengerInfo:     util.JustJsonEncode(priceReq.PassengerInfo),
			OrderInfo:         priceReq.OrderInfoSt,
			ExtraInfo:         extraInfo,
			CustomServiceInfo: "[]",
			OneKeyActivity:    "{}",
		})
	}

	request.OrderId = product.BizInfo.SendOrderId
	request.AccessKeyId = baseReq.CommonInfo.AccessKeyID
	request.Caller = CallerEstimateCarpoolOrder
	request.BubbleEstimateId = sceneData.BubbleEstimateId
	request.OrderInfo = sceneData.OrderInfo
	request.FeeDetailInfo = sceneData.FeeDetailInfo
	request.DynamicTotalFee = sceneData.DynamicTotalFee
	request.Option = sceneData.Option
	request.EstimatePriceReq = reqs

	return request
}

func NewCarpoolOrderEstimatePrice(baseReqMap map[int64]*models.BaseReqData, carpoolSceneData map[int64]*CarpoolSceneData) biz_runtime.OptionProcessPrice {
	return &CarpoolOrderEstimatePrice{
		BaseReqMap:       baseReqMap,
		CarpoolSceneData: carpoolSceneData,
	}
}
