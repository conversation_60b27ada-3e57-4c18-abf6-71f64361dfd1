package option_gen_price

import (
	"context"
	"errors"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_common"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
	"runtime"
	"sync"
)

type CarpoolLowPriceParallelEstimate struct{}

func NewCarpoolLowPriceEstimateOpt(baseReqData *models.BaseReqData) *CarpoolLowPriceParallelEstimate {
	if baseReqData == nil {
		return nil
	}

	if baseReqData.CommonBizInfo.PinCheCheFormInfo.FormSytle == 1 {
		return &CarpoolLowPriceParallelEstimate{}
	}

	return nil
}

func (c *CarpoolLowPriceParallelEstimate) ExecPrice(ctx context.Context, brd *models.BaseReqData, baseProducts []*models.Product) ([]*biz_runtime.ProductInfoFull, error) {
	wg := sync.WaitGroup{}
	productGroups := groupingProductByDepartureTime(baseProducts)

	var failedEstimate = make(chan struct{})
	var finshedProducts = make(chan []*biz_runtime.ProductInfoFull, len(productGroups))
	var products = make([]*biz_runtime.ProductInfoFull, 0)

	for _, partProducts := range productGroups {
		wg.Add(1)
		go func(ctx context.Context, partProducts []*models.Product, wgDone func()) {
			defer wgDone()
			defer func() {
				if err := recover(); err != nil {
					stack := make([]byte, 8192)
					stack = stack[:runtime.Stack(stack, false)]
					log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "multi_call panic:%v\n%s", err, stack)
				}
			}()

			partProductsFull, err := biz_runtime.GetPriceInfov3(ctx, intercity_common.GetCaller(brd.CommonInfo.PageType), brd, partProducts)
			if err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "priceErr: %v", err)
				failedEstimate <- struct{}{}
				return
			}

			if len(partProductsFull) > 0 {
				finshedProducts <- partProductsFull
			}

		}(ctx, partProducts, wg.Done)
	}

	wg.Wait()
	close(finshedProducts)

	for product := range finshedProducts {
		products = append(products, product...)
	}

	if len(failedEstimate) != 0 {
		return products, NewErrors.NewBizError(errors.New("shortage of price data"), NewErrors.EstimateDegradePriceEmpty)
	}

	return products, nil
}

func groupingProductByDepartureTime(baseProducts []*models.Product) map[int64][]*models.Product {
	res := make(map[int64][]*models.Product)
	for _, product := range baseProducts {
		if product == nil || product.BizInfo == nil {
			continue
		}

		var dt = product.BizInfo.DepartureTime
		if len(product.BizInfo.DepartureRange) == 2 {
			dt = product.BizInfo.DepartureRange[1]
		}

		if _, ok := res[dt]; ok {
			res[dt] = append(res[dt], product)
		} else {
			res[dt] = []*models.Product{product}
		}
	}

	return res
}
