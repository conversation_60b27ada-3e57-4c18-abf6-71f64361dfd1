package option_gen_price

import (
	"context"
	"encoding/json"
	"errors"
	Dirpc_SDK_Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	midl "git.xiaojukeji.com/dirpc/dirpc-go-http-Member"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ferrari"
	"strconv"
	"time"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Plutus "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/client_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/route_estimate/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"github.com/spf13/cast"
)

const Caller = "p_route_estimate"

type routeEstimatePriceOutput struct {
	routeIDList []string
	serviceReq  *model.ServiceReq
}

func NewRouteEstimatePriceOutput(routeIDList []string, serviceReq *model.ServiceReq) *routeEstimatePriceOutput {
	return &routeEstimatePriceOutput{
		routeIDList: routeIDList,
		serviceReq:  serviceReq,
	}
}

func (r *routeEstimatePriceOutput) ExecPrice(ctx context.Context, baseReq *models.BaseReqData, products []*models.Product) ([]*biz_runtime.ProductInfoFull, error) {
	if r.serviceReq == nil || r.serviceReq.OrderInfo == nil {
		return nil, errors.New("service req is nil")
	}

	orderInfo := r.serviceReq.OrderInfo

	if util.InArrayInt(orderInfo.GetOrderStatus(), []int{order_info.DriverGrabbing.ToInt(), order_info.ArrivingPoint.ToInt()}) {
		resp, err := price_api.GetMultiEstimatePriceForRoute(ctx, Caller, r.buildRequest(ctx, products), nil)
		if err != nil {
			return nil, err
		}

		// 路线价格校验
		err = r.checkPrice(ctx, resp)
		if err != nil {
			return nil, err
		}

		return r.formatRespByPickUp(ctx, products, resp), nil
	} else if util.InArrayInt(orderInfo.GetOrderStatus(), []int{order_info.StartBilling.ToInt()}) {
		if orderInfo.GetCapPrice() > 0 {
			resp, err := price_api.GetMultiEstimatePriceForRouteCapPrice(ctx, r.buildRequestForDriverOff(ctx, products, baseReq.CommonBizInfo.TravelQuotation))
			if err != nil {
				return nil, err
			}

			return r.formatRespByCapPrice(ctx, products, resp), nil

		} else {
			resp, err := price_api.GetMultiEstimatePriceForRouteDriverOff(ctx, r.buildRequestForDriverOff(ctx, products, baseReq.CommonBizInfo.TravelQuotation))
			// 路线价格校验
			err = r.checkPrice(ctx, resp)
			if err != nil {
				return nil, err
			}

			return r.formatRespByPickUp(ctx, products, resp), nil
		}
	}
	return nil, nil
}

// buildRequest 构建入参
func (r *routeEstimatePriceOutput) buildRequest(ctx context.Context, products []*models.Product) []*price_api.PriceEstimateReq {
	if len(products) <= 0 {
		return nil
	}

	reqs := make([]*price_api.PriceEstimateReq, 0)

	for _, product := range products {
		req := &price_api.PriceEstimateReq{
			CommonInfoSt:  r.buildCommonInfoSt(ctx, product),
			OrderInfoSt:   r.buildOrderInfoSt(ctx, product),
			OrderExt:      r.buildOrderExtInfoSt(ctx, product),
			PassengerInfo: r.buildPassengerInfoSt(ctx, product),
			ExtraInfo:     r.buildExtraInfoSt(ctx, product),
		}

		reqs = append(reqs, req)
	}

	return reqs
}

// buildRequestForDriverOff ...
func (r *routeEstimatePriceOutput) buildRequestForDriverOff(ctx context.Context, products []*models.Product, travelQuotation *Dirpc_SDK_Ferrari.TravelQuotation) *PriceApi.MultiEstimatePriceByRouteForDriveOffReq {
	if r.serviceReq == nil || r.serviceReq.OrderInfo == nil || r.serviceReq.BaseReq == nil || r.serviceReq.OriginRequest == nil {
		return nil
	}

	originReq, commonInfo, orderInfo := r.serviceReq.OriginRequest, r.serviceReq.BaseReq.CommonInfo, r.serviceReq.OrderInfo

	return &PriceApi.MultiEstimatePriceByRouteForDriveOffReq{
		Caller:                Caller,
		AppVersion:            commonInfo.AppVersion,
		AccessKeyId:           commonInfo.AccessKeyID,
		OrderId:               orderInfo.GetOrderID(),
		OrderInfo:             r.buildOrderInfo(ctx),
		CurEstimateId:         orderInfo.EstimateId,
		MultiRouteProduct:     r.buildMultiRouteProduct(ctx, products),
		IsCapPrice:            originReq.IsCapPrice,
		IsUseDiscount:         util.Int32Ptr(1),
		UpdateTravelSceneType: originReq.UpdateTravelSceneType,
		ExtraInfo:             r.buildExtraInfo(ctx, orderInfo, travelQuotation),
	}
}

// buildCommonInfoSt 构建通用数据
func (r *routeEstimatePriceOutput) buildCommonInfoSt(ctx context.Context, product *models.Product) *PriceApi.CommonInfoSt {
	if r.serviceReq == nil || r.serviceReq.BaseReq == nil {
		return nil
	}

	commonInfo := r.serviceReq.BaseReq.CommonInfo

	return &PriceApi.CommonInfoSt{
		TransparentData: "",
		BusinessId:      product.BusinessID,
		OriginId:        commonInfo.OriginID,
		AppVersion:      commonInfo.AppVersion,
		ClientType:      int64(commonInfo.ClientType),
		Imei:            commonInfo.Imei,
		Lang:            commonInfo.Lang,
		DataType:        0,
		IsFromB2B:       client_type.IsFromB2B(commonInfo.ClientType),
		IsFromWebapp:    client_type.IsFromWebApp(commonInfo.ClientType),
		GuideRequest:    false,
		OpenId:          nil,
		Suuid:           nil,
		PlatformType:    util.Int32Ptr(commonInfo.PlatformType),
		TerminalId:      commonInfo.TerminalID,
		RouteId:         util.Int64Ptr(util.String2int64(ctx, product.RouteID)),
	}
}

// buildOrderInfoSt 构建订单信息
func (r *routeEstimatePriceOutput) buildOrderInfoSt(ctx context.Context, product *models.Product) *PriceApi.OrderInfoSt {
	if r.serviceReq == nil || r.serviceReq.OrderInfo == nil || r.serviceReq.BaseReq == nil {
		return nil
	}

	orderInfo, originReq, commonInfo, areaInfo := r.serviceReq.OrderInfo, r.serviceReq.OriginRequest, r.serviceReq.BaseReq.CommonInfo, r.serviceReq.BaseReq.AreaInfo

	var (
		fromLng float64
		fromLat float64
	)

	if orderInfo.GetOrderStatus() == order_info.StartBilling.ToInt() {
		fromLat = originReq.Lat
		fromLng = originReq.Lng
	} else {
		fromLat = areaInfo.FromLat
		fromLng = areaInfo.FromLng
	}

	return &PriceApi.OrderInfoSt{
		CurrentLng:            areaInfo.CurLng,
		CurrentLat:            areaInfo.CurLat,
		Area:                  int64(areaInfo.Area),
		OrderType:             int64(product.OrderType),
		Channel:               commonInfo.Channel,
		FromLng:               fromLng,
		FromLat:               fromLat,
		FromPoiId:             areaInfo.FromPoiID,
		FromPoiType:           areaInfo.FromPoiType,
		FromAddress:           areaInfo.FromAddress,
		FromName:              areaInfo.FromName,
		ToLng:                 areaInfo.ToLng,
		ToLat:                 areaInfo.ToLat,
		ToPoiId:               areaInfo.ToPoiID,
		ToPoiType:             areaInfo.ToPoiType,
		ToAddress:             areaInfo.ToAddress,
		ToName:                areaInfo.ToName,
		SceneType:             product.SceneType,
		ComboType:             product.ComboType,
		ComboId:               orderInfo.GetComboID(),
		DepartureTime:         time.Now().Unix(),
		CallCarType:           orderInfo.GetCallCarType(),
		CallCarPhone:          "",
		WillingWait:           0,
		MapType:               originReq.MapType,
		ActivityId:            0,
		UserType:              originReq.UserType,
		PaymentsType:          orderInfo.GetPayType(),
		GuideType:             0,
		GuideState:            0,
		ProductId:             product.ProductID,
		RequireLevel:          product.RequireLevel,
		IsFastCar:             product_id.IsFastCar(product.ProductID),
		CarpoolSeatNum:        orderInfo.GetPassengerCount(),
		CarpoolStationType:    0,
		CarpoolRequireTraceId: "",
		CurrentStationId:      0,
		StartingName:          areaInfo.StartingName,
		DestName:              areaInfo.DestName,
		District:              util.StringPtr(areaInfo.District),
		AbstractDistrict:      util.StringPtr(areaInfo.AbstractDistrict),
		SeatDetailInfo:        nil,
	}
}

// buildOrderExtInfoSt 构建额外信息
func (r *routeEstimatePriceOutput) buildOrderExtInfoSt(ctx context.Context, product *models.Product) *price_api.OrderExt {
	tag := "buildOrderExtInfoSt"
	if r.serviceReq == nil || r.serviceReq.OrderInfo == nil || r.serviceReq.BaseReq == nil || product == nil {
		return nil
	}

	commonInfo, areaInfo, sendOrder := r.serviceReq.BaseReq.CommonInfo, r.serviceReq.BaseReq.AreaInfo, r.serviceReq.BaseReq.SendOrder

	nTuple := &price_api.OrderNTuple{}

	if orderNTupleStr, err := json.Marshal(product); err == nil {
		err = json.Unmarshal(orderNTupleStr, nTuple)
		if err != nil {
			log.Trace.Warnf(ctx, tag, "n tuple unmarshal fail, err:%v", err)
		}
	}

	return &price_api.OrderExt{
		ActualComboType:           0,
		StartDestDistance:         0,
		Tip:                       0,
		CouponType:                0,
		ComboIDs:                  "",
		EstimateID:                product.EstimateID,
		MatchRoutes:               nil,
		AirportID:                 0,
		AirportCode:               "",
		GsAirport:                 0,
		ToArea:                    int64(areaInfo.ToArea),
		County:                    int64(areaInfo.FromCounty),
		ToCounty:                  int64(areaInfo.ToCounty),
		TripCountry:               areaInfo.TripCountry,
		AutoTrigger:               0,
		EstimateCarpoolOpen:       0,
		OrderNTuple:               nTuple,
		DepartureRange:            nil,
		IsSelectInsurance:         false,
		InterInsuranceInfo:        nil,
		AgentType:                 "",
		IsAthenaAddProduct:        false,
		CommuteRouteID:            "",
		InsuranceInfo:             nil,
		RecognitionResult:         nil,
		StationList:               nil,
		DesignatedDriver:          "",
		DesignatedDriverTag:       "",
		HotelID:                   0,
		StartBroadcastTime:        0,
		FilterCashPay:             0,
		StopoverPoints:            nil,
		WayPointsVersion:          "",
		WayPointsAInfo:            "",
		WayPointsAStatus:          0,
		WayPointsBInfo:            "",
		WayPointsBStatus:          0,
		IsExtraStop:               0,
		RedPacket:                 0,
		RedPacketList:             nil,
		IsSpecialPrice:            product.IsSpecialPrice,
		MenuID:                    "",
		PersonalizedCustomOptions: nil,
		BubbleTime:                nil,
		ProductCategory:           product.ProductCategory,
		PreferredRouteId:          0,
		DepartureTimeBuffer:       0,
		AccessKeyID:               commonInfo.AccessKeyID,
		LevelType:                 product.LevelType,
		CommuteCardInfo:           nil,
		PageType:                  0,
		ExamType:                  product.ExamType,
		PricePrivilegeInfo:        nil,
		OrderID:                   sendOrder.OrderIdLow,
	}
}

// buildPassengerInfoSt 构建用户信息
func (r *routeEstimatePriceOutput) buildPassengerInfoSt(ctx context.Context, product *models.Product) *price_api.PassengerInfo {
	if r.serviceReq == nil || r.serviceReq.UserInfo == nil || product == nil {
		return nil
	}

	var memberProfileV1 *midl.V1QueryInfo
	if product.BizInfo != nil {
		memberProfileV1 = product.BizInfo.MemberProfile
	}

	userInfo := r.serviceReq.UserInfo
	channel := util.String2Int(ctx, userInfo.Channel)
	originID := util.String2Int(ctx, userInfo.OriginId)

	return &price_api.PassengerInfo{
		UID:                  int64(userInfo.UID),
		ID:                   int64(userInfo.PID),
		Phone:                userInfo.Phone,
		Channel:              channel,
		Status:               0,
		Role:                 int64(userInfo.Role),
		Origin:               int64(originID),
		UserDpaSelected:      1,
		IsUserUseDpa:         false,
		MemberProfile:        memberProfileV1,
		MemberProductProfile: nil,
		FreeDistance:         0,
	}
}

// buildExtraInfoSt 构建附加信息
func (r *routeEstimatePriceOutput) buildExtraInfoSt(ctx context.Context, product *models.Product) map[string]string {
	extraInfo := make(map[string]string)

	extraInfo["product_category"] = cast.ToString(product.ProductCategory)
	extraInfo["order"] = util.JustJsonEncode(r.buildOrderExtInfoSt(ctx, product))
	extraInfo["is_use_discount"] = "1"
	return extraInfo
}

// formatRespByPickUp 格式化响应接驾阶段
func (r *routeEstimatePriceOutput) formatRespByPickUp(ctx context.Context, products []*models.Product, resp *PriceApi.EstimateNewFormResponse) []*biz_runtime.ProductInfoFull {
	if resp == nil || r.serviceReq == nil {
		return nil
	}

	productFulls := make([]*biz_runtime.ProductInfoFull, 0)

	for _, product := range products {
		if product == nil {
			continue
		}

		productFull := &biz_runtime.ProductInfoFull{
			BaseReqData: r.serviceReq.BaseReq,
			Product:     product,
		}

		productFull.SetHu(resp.Data[product.EstimateID])
		productFulls = append(productFulls, productFull)
	}

	return productFulls
}

// checkPrice 检查价格
func (r *routeEstimatePriceOutput) checkPrice(ctx context.Context, resp *PriceApi.EstimateNewFormResponse) error {
	if resp == nil {
		return errors.New("resp is nil")
	}

	if len(resp.Data) <= 0 {
		return errors.New("data is nil")
	}

	if len(resp.Data) != len(r.routeIDList) {
		return errors.New("route list not match price list")
	}

	for _, data := range resp.Data {
		if data == nil {
			continue
		}

		if data.EstimateFee == 0 {
			return errors.New("estimate fee is zero")
		}
	}

	return nil
}

// buildOrderInfo ...
func (r *routeEstimatePriceOutput) buildOrderInfo(ctx context.Context) string {
	if r.serviceReq == nil || r.serviceReq.OrderInfo == nil {
		return ""
	}

	orderData := r.serviceReq.OrderInfo

	orderInfo := &Plutus.Order{
		OrderId:                      util.String2int64(ctx, orderData.OrderId),
		DriverId:                     util.String2int64(ctx, orderData.DriverId),
		DriverPhone:                  orderData.DriverPhone,
		PassengerId:                  util.String2int64(ctx, orderData.PassengerId),
		PassengerPhone:               orderData.PassengerPhone,
		PassengerCount:               util.String2int64(ctx, orderData.PassengerCount),
		TravelId:                     util.String2int64(ctx, orderData.TravelId),
		SchemaId:                     util.String2int64(ctx, orderData.SchemaId),
		ComboType:                    util.String2int64(ctx, orderData.ComboType),
		ComboId:                      util.String2int64(ctx, orderData.ComboId),
		StrategyToken:                orderData.StrategyToken,
		CarId:                        util.String2int64(ctx, orderData.CarId),
		Area:                         util.String2int64(ctx, orderData.Area),
		Type:                         util.String2int64(ctx, orderData.Type),
		ExtraType:                    util.String2int64(ctx, orderData.ExtraType),
		DriverType:                   util.String2int64(ctx, orderData.DriverType),
		ProductId:                    util.String2int64(ctx, orderData.ProductId),
		BusinessId:                   orderData.BusinessId,
		Tip:                          util.String2int64(ctx, orderData.Tip),
		IncentiveFee:                 0,
		Token:                        "",
		ProductToken:                 "",
		IsSep:                        0,
		LimitFee:                     0,
		CapPrice:                     util.String2float64(ctx, orderData.CapPrice),
		CarpoolFailFee:               0,
		DynamicPrice:                 util.String2float64(ctx, orderData.DynamicPrice),
		DelayTimeStart:               orderData.DelayTimeStart,
		BeginChargeTime:              orderData.BeginChargeTime,
		FinishTime:                   orderData.FinishTime,
		DriverDisplayPrice:           util.String2float64(ctx, orderData.DriverDisplayPrice),
		Channel:                      util.String2int64(ctx, orderData.Channel),
		PreTotalFee:                  util.String2float64(ctx, orderData.PreTotalFee),
		Pangu:                        "",
		IsAirport:                    0,
		Bonus:                        util.String2float64(ctx, orderData.Bonus),
		Bouns:                        util.String2float64(ctx, orderData.Bouns),
		Airport:                      0,
		StriveCarLevel:               orderData.StriveCarLevel,
		WaitFee:                      0,
		WaitTime:                     0,
		StartDestDistance:            util.String2PtrString(orderData.StartDestDistance),
		DepartureTime:                orderData.DepartureTime,
		StartingLng:                  util.String2PtrString(orderData.StartingLng),
		StartingLat:                  util.String2PtrString(orderData.StartingLat),
		DestLng:                      util.String2PtrString(orderData.DestLng),
		DestLat:                      util.String2PtrString(orderData.DestLat),
		OrderStatus:                  util.String2int64(ctx, orderData.OrderStatus),
		District:                     orderData.District,
		AbstractDistrict:             "",
		BeginChargeLng:               util.String2PtrString(orderData.BeginChargeLng),
		BeginChargeLat:               util.String2PtrString(orderData.BeginChargeLat),
		EstimateId:                   orderData.EstimateId,
		PayType:                      util.String2PtrString(orderData.PayType),
		CompanyCarpoolFlag:           0,
		TripCountry:                  "",
		CountPriceType:               util.String2PtrString(orderData.CountPriceType),
		DonateFee:                    0,
		BegunTime:                    orderData.BegunTime,
		BegunLat:                     util.String2PtrString(orderData.BegunLat),
		BegunLng:                     util.String2PtrString(orderData.BegunLng),
		OrderNTuple:                  nil,
		IsInsure:                     false,
		InsureCount:                  0,
		BeginWaitFeeChargeTime:       0,
		ProductFields:                nil,
		SerialOrderForDispatch:       nil,
		County:                       orderData.County,
		CountryIsoCode:               orderData.CountryIsoCode,
		EstimateTime:                 util.String2PtrString(orderData.EstimateTime),
		DriverDynamicTimes:           orderData.DriverDynamicTimes,
		FreeCommission:               0,
		UpgradePrivilegeEnable:       0,
		PrivilegeDistanceLimit:       0,
		CompanyId:                    util.String2int64(ctx, util.StringPtr2String(orderData.CompanyId)),
		FenceId:                      0,
		DriverFenceId:                0,
		ToArea:                       &orderData.ToArea,
		DriverCarpoolFailFee:         0,
		DriverDynamicPrice:           util.String2PtrString(orderData.DriverDynamicPrice),
		DriverDynamicType:            util.String2PtrString(orderData.DriverDynamicType),
		MemberLevel:                  0,
		CapacityLevel:                orderData.CapacityLevel,
		RequireLevel:                 orderData.RequireLevel,
		DepartureRange:               nil,
		OriDistrict:                  "",
		EndWaitFeeChargeTime:         0,
		WaitFeeAssignToDriver:        false,
		DriverWaitFee:                0,
		StriveTime:                   orderData.StriveTime,
		ArriveTime:                   orderData.ArriveTime,
		TaxesFee:                     0,
		CreateTimeStamp:              0,
		DriverProductId:              orderData.DriverProductId,
		OptionalFeature:              nil,
		PlutusActivity:               0,
		CoronavirusDriverHasFee:      0,
		IsCarpoolPriority:            0,
		OriginalPassengerCount:       util.String2PtrString(orderData.OriginalPassengerCount),
		AdditionalEstimateId:         orderData.AdditionalEstimateId,
		AdditionalCapPrice:           util.String2PtrString(orderData.AdditionalCapPrice),
		AdditionalPreTotalFee:        util.String2PtrString(orderData.AdditionalPreTotalFee),
		AdditionalOrderNTuple:        nil,
		AdditionalExtraType:          0,
		AdditionalDynamicPrice:       0,
		AdditionalLevelId:            nil,
		AdditionalDynamicCapping:     nil,
		AdditionalCountPriceType:     nil,
		CarpoolOrderSence:            util.String2PtrString(orderData.CarpoolOrderScene),
		PickUpDistance:               0,
		FreeWait:                     "",
		ReducePrice:                  0,
		AdditionalEstimateFixedFees:  orderData.AdditionalEstimateFixedFees,
		IsMixedPayment:               0,
		BaseCarLevelTotalFee:         0,
		MockPrice:                    0,
		SpBillType:                   0,
		SpDriverLevelType:            0,
		AssignedLat:                  util.String2PtrString(orderData.AssignedLat),
		AssignedLng:                  util.String2PtrString(orderData.AssignedLng),
		PreparedLat:                  util.String2PtrString(orderData.PreparedLat),
		PreparedLng:                  util.String2PtrString(orderData.PreparedLng),
		KflowerAppointmentTrPrice:    0,
		KflowerCarpoolSuccTrPrice:    0,
		KflowerCarpoolFailTrPrice:    0,
		CarpoolScenePrice:            nil,
		PoolNum:                      0,
		PayHourFlag:                  0,
		MachinePhone:                 "",
		MachineUserId:                0,
		FromName:                     orderData.FromName,
		ToName:                       orderData.ToName,
		RequiredLevel:                orderData.RequireLevel,
		WayPointsVersion:             orderData.WayPointsVersion,
		PLang:                        "",
		DLang:                        "",
		IntercityBasePrice:           0,
		UpperLimitPriceDiscount:      util.String2PtrString(orderData.UpperLimitPriceDiscount),
		UpperLimitPrice:              util.String2PtrString(orderData.UpperLimitPrice),
		UpperLimitPriceType:          util.String2PtrString(orderData.UpperLimitPriceType),
		OpenId:                       "",
		AssignedTime:                 &orderData.AssignedTime,
		ZeroTrType:                   0,
		StartingName:                 orderData.StartingName,
		DestName:                     orderData.DestName,
		IsCanoe:                      0,
		DispatchPackType:             0,
		DriverTakeRateMode:           0,
		DriverTakeRate:               0,
		PassengerPriceModel:          0,
		DriverPriceModel:             0,
		FreeRideType:                 0,
		CrossAreaOrderType:           0,
		NotUseCouponIds:              nil,
		CarpoolChooseOrderPriceModel: orderData.CarpoolChooseOrderPriceModel,
		PickOnTimeReward:             nil,
		PickOnTimePtimes:             nil,
		PickOnTimeDtimes:             nil,
		WaitFeeType:                  nil,
		DepartureTimeOrigin:          orderData.DepartureTimeOrigin,
	}

	orderInfoStr, err := json.Marshal(orderInfo)
	if err != nil {
		log.Trace.Warnf(ctx, "buildOrderInfo", "")
		return ""
	}

	return string(orderInfoStr)
}

// buildMultiRouteProduct ...
func (r *routeEstimatePriceOutput) buildMultiRouteProduct(ctx context.Context, products []*models.Product) []*PriceApi.MultiRouteProduct {
	if r.serviceReq == nil || r.serviceReq.BaseReq == nil || len(products) <= 0 {
		return nil
	}

	areaInfo, originReq := r.serviceReq.BaseReq.AreaInfo, r.serviceReq.OriginRequest
	multiRouteProduct := make([]*PriceApi.MultiRouteProduct, 0)

	for _, product := range products {
		if product == nil {
			continue
		}

		multiRouteProduct = append(multiRouteProduct, &PriceApi.MultiRouteProduct{
			FromLat:          originReq.Lat,
			FromLng:          originReq.Lng,
			ToLat:            areaInfo.ToLat,
			ToLng:            areaInfo.ToLng,
			DepartureTime:    time.Now().Unix(),
			FromName:         areaInfo.FromName,
			FromAddress:      areaInfo.FromAddress,
			ToName:           areaInfo.ToName,
			ToAddress:        areaInfo.ToAddress,
			EstimateId:       product.EstimateID,
			TripCountry:      areaInfo.TripCountry,
			PlatformType:     int64(originReq.PlatformType),
			RouteId:          util.String2int64(ctx, product.RouteID),
			District:         util.String2PtrString(areaInfo.District),
			AbstractDistrict: util.String2PtrString(areaInfo.AbstractDistrict),
			CommonInfo:       r.buildCommonInfoSt(ctx, product),
			PassengerInfo:    util.JustJsonEncode(r.buildPassengerInfoSt(ctx, product)),
			OrderInfo:        r.buildOrderInfoSt(ctx, product),
			ExtraInfo:        r.buildExtraInfoSt(ctx, product),
		})
	}

	return multiRouteProduct
}

// buildExtraInfo ...
func (r *routeEstimatePriceOutput) buildExtraInfo(ctx context.Context, orderInfo *order_info.OrderInfo, travelQuotation *Dirpc_SDK_Ferrari.TravelQuotation) map[string]string {
	extraInfo := make(map[string]string)

	extraInfo["estimate_time"] = orderInfo.EstimateTime
	extraInfo["start_dest_distance"] = orderInfo.StartDestDistance
	extraInfo["cap_price"] = orderInfo.CapPrice
	extraInfo["driver_display_price"] = orderInfo.DriverDisplayPrice
	extraInfo["region_type"] = orderInfo.RegionType
	extraInfo["is_short_book"] = orderInfo.IsShortBook
	extraInfo["assign_type"] = orderInfo.AssignType

	if travelQuotation != nil {
		if priceData, ok := travelQuotation.PriceData[ferrari.WaitFreeUpgradeFee]; priceData != nil && ok {
			extraInfo[ferrari.WaitFreeUpgradeFee] = strconv.Itoa(int(priceData.FeeValue))
			var priceDataList []*Dirpc_SDK_Ferrari.PriceItem
			priceDataList = append(priceDataList, priceData)
			priceDataStr, err := json.Marshal(priceDataList)
			if err == nil {
				extraInfo[ferrari.PassengerBillInfoList] = string(priceDataStr)
			}
		}
	}
	return extraInfo
}

// formatRespByCapPrice ...
func (r *routeEstimatePriceOutput) formatRespByCapPrice(ctx context.Context, products []*models.Product, data map[string]*PriceApi.EstimateForRouteCapPriceData) []*biz_runtime.ProductInfoFull {
	if data == nil || r.serviceReq == nil {
		return nil
	}

	var (
		productFulls = make([]*biz_runtime.ProductInfoFull, 0)
		originBills  = make(map[string]*proto.OriginBills)
	)

	for _, product := range products {
		if product == nil {
			continue
		}

		dataItem, ok := data[product.EstimateID]
		if !ok {
			continue
		}

		productFull := &biz_runtime.ProductInfoFull{
			BaseReqData: r.serviceReq.BaseReq,
			Product:     product,
		}

		newHu := &PriceApi.EstimateNewFormData{
			EstimateId:          dataItem.EstimateId,
			ProductCategory:     dataItem.ProductCategory,
			PaymentInfo:         dataItem.PaymentInfo,
			BillInfo:            dataItem.BillInfo,
			DiscountSet:         dataItem.DiscountSet,
			EstimateFee:         dataItem.EstimateFee,
			PersonalEstimateFee: dataItem.PersonalEstimateFee,
			ExtendList:          dataItem.ExtendList,
		}

		originBillsData := dataItem.OriginBills
		if originBillsData != nil {
			originBills[product.EstimateID] = &proto.OriginBills{
				DriverMinute:                originBillsData.DriverMinute,
				DriverMetre:                 originBillsData.DriverMetre,
				LowSpeedTime:                originBillsData.LowSpeedTime,
				EstimateId:                  originBillsData.EstimateId,
				PassengerOrderResult:        r.buildOrderResult(ctx, originBillsData.PassengerOrderResult),
				DriverOrderResult:           r.buildOrderResult(ctx, originBillsData.DriverOrderResult),
				MultiRouteArgs:              originBillsData.MultiRouteArgs,
				RbArgs:                      originBillsData.RbArgs,
				ModifyDestEstimateFixedFees: originBillsData.ModifyDestEstimateFixedFees,
			}
		}

		productFull.SetHu(newHu)
		productFulls = append(productFulls, productFull)
	}

	r.serviceReq.OriginBills = originBills
	return productFulls
}

// buildOrderResult ...
func (r *routeEstimatePriceOutput) buildOrderResult(ctx context.Context, result *PriceApi.OrderResult) *proto.OrderResult {
	if result == nil {
		return nil
	}

	orderResult := &proto.OrderResult{
		TotalFee:      result.TotalFee,
		CapPrice:      result.CapPrice,
		PreTotalFee:   result.PreTotalFee,
		FenceId:       result.FenceId,
		FeeDetailInfo: result.FeeDetailInfo,
		ExtraInfo:     result.ExtraInfo,
	}

	billExtra := result.BillExtra
	if billExtra != nil {
		orderResult.BillExtra = &proto.BillExtra{
			CompanyCarpoolFlag:     billExtra.CompanyCarpoolFlag,
			PlanInterceptDiffFee:   billExtra.PlanInterceptDiffFee,
			RealtimePriceBack:      billExtra.RealtimePriceBack,
			BasicTotalFee:          billExtra.BasicTotalFee,
			PreTotalFee:            billExtra.PreTotalFee,
			DynamicTotalFee:        billExtra.DynamicTotalFee,
			TotalFeeNotIntercept:   billExtra.TotalFeeNotIntercept,
			FreeCommission:         billExtra.FreeCommission,
			FreeCommissionExtraFee: billExtra.FreeCommissionExtraFee,
		}
	}

	return orderResult
}
