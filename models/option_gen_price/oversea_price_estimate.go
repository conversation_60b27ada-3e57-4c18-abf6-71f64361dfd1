package option_gen_price

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type OverseaPriceEstimate struct {
}

func NewOverseaPriceEstimate() *OverseaPriceEstimate {
	return &OverseaPriceEstimate{}
}

func (o *OverseaPriceEstimate) ExecPrice(ctx context.Context, brd *models.BaseReqData, baseProducts []*models.Product) ([]*biz_runtime.ProductInfoFull, error) {
	var (
		products []*biz_runtime.ProductInfoFull
		priceErr error
	)
	products, priceErr = biz_runtime.GetPriceInfoOversea(ctx, "PAbroadEstimate", brd, baseProducts)
	if priceErr != nil || len(products) == 0 {
		log.Trace.Infof(ctx, "OverseaPriceEstimate", "get product price fail.")
		return nil, fmt.Errorf("%w: %s", consts.ErrorGetFromPriceApiFail, priceErr)
	}

	return products, nil
}
