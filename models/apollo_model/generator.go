package apollo_model

// ApolloParamsGen 绑定在ApolloParam上的获取ApolloParams
// 如果当前logic的Provider，无需额外实现其他参数的获取方法，eg：当前除了Apollo参数以外，没有其他逻辑需要使用CarLevel，则可以通过注册WithCarLevel补充Apollo参数
// 如果当前logic的Provider,已经实现了GetCarLevel()，有除了Apollo参数之外的逻辑需要，则也可以直接在params返回后进行添加，eg：params['car_level'] = prov.GetCarLevel()
// 本文件所提供的paramsFunc，是用于解决当前数据源依赖缺失的情况下补充额外Apollo参数用的，已经添加了比较常见的参数，大家可以进行挑选使用
func (ap *ParamsConnector) ApolloParamsGen(keyFunc func(param *ParamsConnector) string, paramsFunc ...func(param *ParamsConnector) (key, value string)) (key string, params map[string]string) {

	if keyFunc != nil {
		key = keyFunc(ap)
	} else {
		key = ap.Phone
	}

	params = map[string]string{
		"city":          ap.City,
		"phone":         ap.Phone,
		"uid":           ap.UID,
		"pid":           ap.PID,
		"city_id":       ap.City,
		"lang":          ap.Lang,
		"access_key_id": ap.AccessKeyID,
		"app_version":   ap.AppVersion,
	}

	for _, f := range paramsFunc {
		if f == nil {
			continue
		}

		k, v := f(ap)
		if k != "" && v != "" {
			params[k] = v
		}
	}

	return
}

func WithPhoneKey(p *ParamsConnector) string {
	return p.Phone
}

func WithUIDKey(p *ParamsConnector) string {
	return p.UID
}

func WithPIDKey(p *ParamsConnector) string {
	return p.PID
}

func WithFromCounty(p *ParamsConnector) (string, string) {
	return "county", p.findExtraKey("county")
}

func WithFromCountyId(p *ParamsConnector) (string, string) {
	return "county_id", p.findExtraKey("county")
}

//
//func WithCarLevel(p *ParamsConnector) (string, string) {
//	//if p.Product == nil {
//	//	return "", ""
//	//}
//
//	return "car_level", p.Product.RequireLevel
//}

//func WithIsSpecialPrice(p *ParamsConnector) (string, string) {
//	if p.Product == nil {
//		return "", ""
//	}
//
//	return "is_special_price", util.Bool2string(p.Product.IsSpecialPrice)
//}

func WithIsCrossCity(p *ParamsConnector) (string, string) {
	return "is_cross_city", p.findExtraKey("is_cross_city")
}

//func WithComboID(p *ParamsConnector) (string, string) {
//	if p.Product == nil || p.Product.BizInfo == nil {
//		return "", ""
//	}
//	return "combo_id", cast.ToString(p.Product.BizInfo.ComboID)
//}

//func WithRouteGroup(p *ParamsConnector) (string, string) {
//	if p.Product == nil || p.Product.BizInfo == nil || p.Product.BizInfo.RouteInfo == nil {
//		return "", ""
//	}
//
//	return "route_group", cast.ToString(p.Product.BizInfo.RouteInfo.RouteGroup)
//}

func WithMenuID(p *ParamsConnector) (string, string) {
	return "menu_id", p.findExtraKey("menu_id")
}

func WithProductCategory(p *ParamsConnector) (string, string) {
	return "product_category", p.findExtraKey("product_category")
}

//func WithProductID(p *ParamsConnector) (string, string) {
//	if p.Product == nil {
//		return "", ""
//	}
//	return "product_id", cast.ToString(p.Product.ProductID)
//}
