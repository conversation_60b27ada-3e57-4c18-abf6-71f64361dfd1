package apollo_model

import (
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

type ParamsConnector struct {
	Phone       string
	UID         string
	PID         string
	City        string
	Lang        string
	AccessKeyID string
	AppVersion  string
	_extraInfo  map[string]string
}

func (ap *ParamsConnector) _addExtraInfo(key string, value string) {
	if ap == nil {
		return
	}

	if ap._extraInfo == nil {
		ap._extraInfo = make(map[string]string)
	}

	ap._extraInfo[key] = value
}

func (ap *ParamsConnector) SetProductCategory(ProductCategory string) *ParamsConnector {
	ap._addExtraInfo("product_category", ProductCategory)
	return ap
}

func (ap *ParamsConnector) SetCounty(county string) *ParamsConnector {
	ap._addExtraInfo("county", county)
	return ap
}

func (ap *ParamsConnector) SetIsCrossCity(isCrossCity string) *ParamsConnector {
	ap._addExtraInfo("is_cross_city", isCrossCity)
	return ap
}

func (ap *ParamsConnector) SetMenuID(menuID string) *ParamsConnector {
	ap._addExtraInfo("menu_id", menuID)
	return ap
}

func (ap *ParamsConnector) findExtraKey(key string) string {
	var value string
	if ap != nil && ap._extraInfo != nil {
		value = ap._extraInfo[key]
	}

	if value == "" {
		log.Trace.Warn("apollo_params_build_err", "key = %v", key)
	}

	return value
}
