package option_gen_product

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

type PbdStationBusOrderService struct {
	baseReq *models.BaseReqData
}
type PbdOrderStationResMes struct {
	BaseProducts map[int32]*models.Product `json:"base_products"`
	SkuInfo      models.SkuInfo            `json:"sku_info"`
}

func NewPbdStationBusOrderService(baseReq *models.BaseReqData) *PbdStationBusOrderService {
	return &PbdStationBusOrderService{
		baseReq: baseReq,
	}
}

func (s *PbdStationBusOrderService) ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product {
	var (
		product  *models.Product
		products []*models.Product
	)

	product = s.buildProduct(ctx, brd)
	if product == nil {
		return nil
	}

	products = append(products, product)
	return products
}

// buildProductByQuotation ...
func (s *PbdStationBusOrderService) buildProduct(ctx context.Context, brd *models.BaseReqData) *models.Product {
	if s.baseReq == nil {
		return nil
	}
	stationResMes := &PbdOrderStationResMes{}
	var (
		skuFilter *models.SkuInfo
	)
	baseProducts := make(map[int32]*models.Product, 0)
	ddsReq := brd.GenDDSProductsReq()
	//1、调用dds获取基本品类信息
	ddsResp, err := decision.GetDDSProducts(ctx, ddsReq)
	if ddsResp == nil || err != nil {
		return nil
	}
	if len(ddsResp) == 0 {
		return nil
	}
	productList := []int32{}
	for _, ddsProduct := range ddsResp {
		product := &models.Product{}
		if !ddsProduct.RemoveFlag {
			product.BuildFromDDsResponse(ctx, ddsProduct, brd)
			baseProducts[int32(product.ProductID)] = product
			productList = append(productList, int32(product.ProductID))
		}
	}
	if len(productList) == 0 {
		return nil
	}
	//2、调用duse
	skuList := rpc_process.NewMutliStationInventory(brd).Fetch(ctx, productList)
	for _, v := range skuList {
		if v.ShiftID == brd.CommonBizInfo.BusServiceShiftId {
			skuFilter = &v
			continue
		}
	}
	if skuFilter == nil {
		return nil
	}
	stationResMes = &PbdOrderStationResMes{
		SkuInfo:      *skuFilter,
		BaseProducts: baseProducts,
	}
	baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo.ProductID]
	product := NewOrderStationProduct(ctx, baseProduct, stationResMes.SkuInfo, brd)
	return product
}
func NewOrderStationProduct(ctx context.Context, baseProduct *models.Product, skuInfo models.SkuInfo, brd *models.BaseReqData) *models.Product {
	p := &models.Product{}
	p.ProductCategory = baseProduct.ProductCategory
	p.OrderType = baseProduct.OrderType
	p.ProductID = baseProduct.ProductID
	p.BusinessID = baseProduct.BusinessID
	p.RequireLevelInt = baseProduct.RequireLevelInt
	p.RequireLevel = baseProduct.RequireLevel
	p.CarpoolType = baseProduct.CarpoolType
	p.ComboType = baseProduct.ComboType
	p.IsSpecialPrice = baseProduct.IsSpecialPrice
	p.CarpoolPriceType = baseProduct.CarpoolPriceType
	p.IsDualCarpoolPrice = baseProduct.IsDualCarpoolPrice
	p.AirportType = baseProduct.AirportType
	p.RailwayType = baseProduct.RailwayType
	p.HotelType = baseProduct.HotelType
	p.ExamType = baseProduct.ExamType
	p.StationServiceControl = baseProduct.StationServiceControl
	p.LevelType = baseProduct.LevelType
	p.RouteType = baseProduct.RouteType
	p.LongRentType = baseProduct.LongRentType
	p.EmergencyServiceType = baseProduct.EmergencyServiceType
	p.BuildEstimateIDByShiftID(ctx, &skuInfo)
	p.ShiftID = skuInfo.ShiftID
	p.BizInfo = &models.PrivateBizInfo{
		ComboID:           skuInfo.RouteGroup,
		DepartureTime:     skuInfo.DepartureTime,
		MaxCarpoolSeatNum: skuInfo.RemainSeats,
		IntercityData: models.IntercityData{
			SeatDetailInfo: brd.CommonBizInfo.SeatDetailInfo,
			StationInventoryInfo: &models.StationInventoryInfo{
				SelectInfo: models.StationInventorySelectInfo{
					DepartureTime:             skuInfo.DepartureTime,
					RouteId:                   skuInfo.RouteGroup,
					RemainSeats:               skuInfo.RemainSeats,
					FromStationId:             int(brd.CommonBizInfo.StartStationId),
					DestStationId:             int(brd.CommonBizInfo.EndStationId),
					ShiftID:                   skuInfo.ShiftID,
					CarryChildrenMaxInventory: skuInfo.CarryChildrenMaxInventory,
				}},
		},
	}
	p.BizInfo.StartStationID = brd.CommonBizInfo.StartStationId
	p.BizInfo.DestStationID = brd.CommonBizInfo.EndStationId
	return p

}
