package option_gen_product

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"github.com/spf13/cast"
)

type SfcModifyTimeGenernator struct {
	OrderInfo *order_info.OrderInfo
}

func (c SfcModifyTimeGenernator) ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product {
	if c.OrderInfo == nil {
		return nil
	}

	products := make([]*models.Product, 0)
	product := c.buildProduct(ctx, c.OrderInfo)
	products = append(products, product)

	return products
}

func (c SfcModifyTimeGenernator) buildProduct(ctx context.Context, orderInfo *order_info.OrderInfo) *models.Product {
	if orderInfo == nil {
		return nil
	}

	product := &models.Product{}
	var requiredProduct dos.RequiredProductStruct
	for _, val := range orderInfo.ExtendFeatureParsed.MultiRequiredProduct {
		if val.ProductId == 15 {
			requiredProduct = val
		}
	}
	// 基础信息
	product.ProductCategory = cast.ToInt64(requiredProduct.EstimatePcID)
	product.ProductID = cast.ToInt64(requiredProduct.ProductId)
	product.BusinessID = cast.ToInt64(requiredProduct.BusinessId)
	product.RequireLevelInt = cast.ToInt64(requiredProduct.RequiredLevel)
	product.RequireLevel = requiredProduct.RequiredLevel
	product.CarpoolType = requiredProduct.CarpoolType
	product.ComboType = cast.ToInt64(requiredProduct.ComboType)

	// 场景标识
	product.AirportType = cast.ToInt32(requiredProduct.AirportType)
	product.RailwayType = cast.ToInt32(requiredProduct.RailwayType)
	product.HotelType = cast.ToInt32(requiredProduct.HotelType)
	product.PaymentsType = cast.ToInt32(requiredProduct.PayType)
	//product.StationServiceControl = orderInfo.GetStationServiceControl()
	//product.LongRentType = orderInfo.GetLongRentType()
	//product.EmergencyServiceType = orderInfo.GetEmergencyServiceType()
	//product.RouteType = orderInfo.GetRouteType()
	//product.IsPickOnTime = orderInfo.GetIsPickOnTime()
	//product.LevelType = orderInfo.GetLevelType()

	product.BuildEstimateByOption(ctx, []string{
		orderInfo.StartingLat,
		orderInfo.StartingLng,
		orderInfo.DestLat,
		orderInfo.DestLng,
	})
	product.BizInfo = &models.PrivateBizInfo{CarpoolSeatNum: cast.ToInt32(orderInfo.PassengerCount)}

	return product
}

func NewSfcModifyTimeGenernator(orderInfo *order_info.OrderInfo) biz_runtime.OptionProcessProduct {

	return &SfcModifyTimeGenernator{
		OrderInfo: orderInfo,
	}
}
