package option_gen_product

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

const (
	RedisKeyPre = "intercity_estimate_station_"
)

const (
	DefaultRouteType = 0
	FenceRouteType   = 7
)

type StationResMes struct {
	BaseProducts map[int32]*models.Product `json:"base_products"`
	SkuInfo      []models.SkuInfo          `json:"sku_info"`
}

type StationService struct {
	baseReq *models.BaseReqData
}

func NewStationService(ctx context.Context) *StationService {
	return &StationService{}

}

func (s *StationService) ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product {
	if brd == nil || brd.CommonBizInfo.StationInfo.EndCity == 0 || brd.CommonBizInfo.StationInfo.StartCity == 0 {
		return nil
	}
	stationInfo := brd.CommonBizInfo.StationInfo
	stationResMes := &StationResMes{}
	var resProducts []*models.Product
	res, err := redis.GetEstimateClient().Get(ctx, RedisKeyPre+strconv.Itoa(int(brd.PassengerInfo.PID)))

	refresh := int32(1)
	// 1. 入参班次id是空或者缓存无数据 则真实请求下游
	if stationInfo.LastShiftId == "" || err != nil || res == "" {
		// 2. 查dds
		baseProducts := make(map[int32]*models.Product, 0)
		ddsReq := brd.GenDDSProductsReq()
		ddsResp, err := decision.GetDDSProducts(ctx, ddsReq)
		if ddsResp == nil || err != nil {
			return nil
		}
		if len(ddsResp) == 0 {
			return nil
		}
		productList := []int32{}
		for _, ddsProduct := range ddsResp {
			product := &models.Product{}
			if !ddsProduct.RemoveFlag {
				product.BuildFromDDsResponse(ctx, ddsProduct, brd)
				baseProducts[int32(product.ProductID)] = product
				productList = append(productList, int32(product.ProductID))
			}
		}

		// 过滤product_id
		productList = s.filterProduct(brd, productList)
		if len(productList) == 0 {
			return nil
		}

		// 3. 查库存
		skuList := rpc_process.NewMutliStationInventory(brd).Fetch(ctx, productList)

		skuListFilter := []models.SkuInfo{}
		for _, v := range skuList {
			if util.InArrayInt32(v.ProductID, productList) {
				skuListFilter = append(skuListFilter, v)
			}
		}
		// 缓存预估班次
		stationResMes = &StationResMes{
			SkuInfo:      skuListFilter,
			BaseProducts: baseProducts,
		}
		data, _ := json.Marshal(stationResMes)
		log.Trace.Infof(ctx, "intercity_station_service", "%s", string(data))
		if _, err := redis.GetEstimateClient().SetEx(ctx, RedisKeyPre+strconv.Itoa(int(brd.PassengerInfo.PID)), 120*time.Second, data); err != nil {
			log.Trace.Warnf(ctx, "station_service", "set redis failed %s", err)
		}
	} else {
		refresh = 0
		redis.GetEstimateClient().Expire(ctx, RedisKeyPre+strconv.Itoa(int(brd.PassengerInfo.PID)), 120*time.Second)
		err := json.Unmarshal([]byte(res), stationResMes)
		if err != nil {
			log.Trace.Warnf(ctx, "station_service", "get redis failed %s", err)
		}
	}

	// 有了元数据 有了上一次的班次号，一次截取十个
	lenNum := 10
	if stationInfo.LastShiftId == "" {
		for k, v := range stationResMes.SkuInfo {
			baseProduct := stationResMes.BaseProducts[v.ProductID]
			if k >= lenNum {
				break
			}
			if k == len(stationResMes.SkuInfo)-1 {
				resProducts = append(resProducts, NewProduct(ctx, baseProduct, v, true, refresh))
				break
			}
			resProducts = append(resProducts, NewProduct(ctx, baseProduct, v, false, refresh))
		}
	} else {
		indexNum := 0
		// stationInfo.LastShiftId
		for k, v := range stationResMes.SkuInfo {
			if v.ShiftID == stationInfo.LastShiftId {
				indexNum = k + 1
			}
		}
		for i := indexNum; i < len(stationResMes.SkuInfo); i++ {
			baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo[i].ProductID]
			if i >= indexNum+lenNum {
				break
			}
			if i == len(stationResMes.SkuInfo)-1 {
				resProducts = append(resProducts, NewProduct(ctx, baseProduct, stationResMes.SkuInfo[i], true, refresh))
				break
			}
			resProducts = append(resProducts, NewProduct(ctx, baseProduct, stationResMes.SkuInfo[i], false, refresh))
		}
	}
	return resProducts
}

func getRouteType(baseProduct *models.Product, skuInfo models.SkuInfo) int64 {
	if baseProduct == nil {
		return DefaultRouteType
	}
	if value, ok := skuInfo.ExtraInfo[intercity.RouteSceneType]; !ok || value != "1" {
		return baseProduct.RouteType
	}
	return FenceRouteType
}

func NewProduct(ctx context.Context, baseProduct *models.Product, skuInfo models.SkuInfo, haveLastItem bool, refresh int32) *models.Product {
	p := &models.Product{}
	p.ProductCategory = baseProduct.ProductCategory
	p.OrderType = baseProduct.OrderType
	p.ProductID = baseProduct.ProductID
	p.BusinessID = baseProduct.BusinessID
	p.RequireLevelInt = baseProduct.RequireLevelInt
	p.RequireLevel = baseProduct.RequireLevel
	p.CarpoolType = baseProduct.CarpoolType
	p.ComboType = baseProduct.ComboType
	p.IsSpecialPrice = baseProduct.IsSpecialPrice
	p.CarpoolPriceType = baseProduct.CarpoolPriceType
	p.IsDualCarpoolPrice = baseProduct.IsDualCarpoolPrice
	p.AirportType = baseProduct.AirportType
	p.RailwayType = baseProduct.RailwayType
	p.HotelType = baseProduct.HotelType
	p.ExamType = baseProduct.ExamType
	p.StationServiceControl = baseProduct.StationServiceControl
	p.LevelType = baseProduct.LevelType
	p.RouteType = getRouteType(baseProduct, skuInfo)
	p.LongRentType = baseProduct.LongRentType
	p.EmergencyServiceType = baseProduct.EmergencyServiceType
	p.BuildEstimateIDByShiftID(ctx, &skuInfo)
	p.BizInfo = &models.PrivateBizInfo{
		ComboID:           skuInfo.RouteGroup,
		DepartureTime:     skuInfo.DepartureTime,
		CarpoolSeatNum:    1,
		MaxCarpoolSeatNum: skuInfo.RemainSeats,
		IntercityData: models.IntercityData{
			StationInventoryInfo: &models.StationInventoryInfo{
				SelectInfo: models.StationInventorySelectInfo{
					DepartureTime:             skuInfo.DepartureTime,
					RouteId:                   skuInfo.RouteGroup,
					SeatNum:                   1,
					RemainSeats:               skuInfo.RemainSeats,
					FromStationId:             int(skuInfo.StartStationID),
					DestStationId:             int(skuInfo.EndStationID),
					SrcCost:                   skuInfo.SrcCost,
					DestCost:                  skuInfo.DestCost,
					ShiftID:                   skuInfo.ShiftID,
					LastItem:                  haveLastItem,
					CarryChildrenMaxInventory: skuInfo.CarryChildrenMaxInventory,
					Refresh:                   refresh,
					ShiftType:                 skuInfo.ShiftType,
					ExtraInfo:                 skuInfo.ExtraInfo,
				}},
		},
	}

	return p

}

func (s *StationService) filterProduct(brd *models.BaseReqData, productIdList []int32) []int32 {
	stationInfo := brd.CommonBizInfo.StationInfo

	if stationInfo.ProductId == 0 || len(stationInfo.OpenSource) == 0 {
		return productIdList
	}

	param := ApolloModel.NewUser("").
		With("passenger_phone", brd.PassengerInfo.Phone).
		With("city", cast.ToString(brd.AreaInfo.City)).
		With("open_source", stationInfo.OpenSource)
	toggle, err := ApolloSDK.FeatureToggle("gs_intercity_station_filter_product", param)
	if err != nil || !toggle.IsAllow() {
		return productIdList
	}

	var filterProducts []int32
	for _, product := range productIdList {
		if product == stationInfo.ProductId {
			filterProducts = append(filterProducts, product)
			break
		}
	}
	return filterProducts
}
