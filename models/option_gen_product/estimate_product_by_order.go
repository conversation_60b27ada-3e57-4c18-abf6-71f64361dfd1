package option_gen_product

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
)

type EstimateProductByOrderOutput struct {
	orderInfo *order_info.OrderInfo
}

func NewEstimateProductByOrderOutput(orderInfo *order_info.OrderInfo) biz_runtime.OptionProcessProduct {
	return &EstimateProductByOrderOutput{
		orderInfo: orderInfo,
	}
}

func (e *EstimateProductByOrderOutput) ExecProducts(ctx context.Context, baseReq *models.BaseReqData) []*models.Product {
	if e.orderInfo == nil {
		return nil
	}

	products := make([]*models.Product, 0)

	product := e.buildProduct(ctx)
	products = append(products, product)

	return products
}

func (e *EstimateProductByOrderOutput) buildProduct(ctx context.Context) *models.Product {
	if e.orderInfo == nil || e.orderInfo.EstimatePcId == nil {
		return nil
	}

	product := &models.Product{}

	// 基础信息
	product.ProductCategory = util.String2int64(ctx, *e.orderInfo.EstimatePcId)
	product.ProductID = e.orderInfo.GetProductID()
	product.BusinessID = e.orderInfo.GetBusinessID()
	product.RequireLevelInt = e.orderInfo.GetRequireLevel()
	product.RequireLevel = e.orderInfo.RequireLevel
	product.CarpoolType = e.orderInfo.GetCarpoolType()
	product.LevelType = e.orderInfo.GetLevelType()
	product.ComboType = e.orderInfo.GetComboType()
	product.CarpoolPriceType = e.orderInfo.GetCarpoolPriceType()
	product.IsSpecialPrice = e.orderInfo.GetIsSpecialPrice()
	product.IsDualCarpoolPrice = e.orderInfo.GetIsDualCarpoolPrice()

	// 场景标识
	product.AirportType = e.orderInfo.GetAirportType()
	product.RailwayType = e.orderInfo.GetRailwayType()
	product.HotelType = e.orderInfo.GetHotelType()
	product.StationServiceControl = e.orderInfo.GetStationServiceControl()
	product.LongRentType = e.orderInfo.GetLongRentType()
	product.EmergencyServiceType = e.orderInfo.GetEmergencyServiceType()
	product.PaymentsType = e.orderInfo.GetPayType()
	product.RouteType = e.orderInfo.GetRouteType()
	product.IsPickOnTime = e.orderInfo.GetIsPickOnTime()

	product.BuildEstimateByOption(ctx, []string{
		e.orderInfo.StartingLat,
		e.orderInfo.StartingLng,
		e.orderInfo.DestLat,
		e.orderInfo.DestLng,
	})

	product.BizInfo = &models.PrivateBizInfo{}

	return product
}
