package option_gen_product

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"github.com/spf13/cast"
)

type PbdStationBusMultiResMes struct {
	BaseProducts map[int32]*models.Product `json:"base_products"`
	SkuInfo      []models.SkuInfo          `json:"sku_info"`
}

type PbdStationBusMultiService struct {
	baseReq *models.BaseReqData
}

func NewPbdStationBusMultiService(ctx context.Context) *PbdStationBusMultiService {
	return &PbdStationBusMultiService{}

}

func (s *PbdStationBusMultiService) ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product {
	if brd == nil || brd.CommonBizInfo.StationInfo.EndCity == 0 || brd.CommonBizInfo.StationInfo.StartCity == 0 {
		return nil
	}
	stationInfo := brd.CommonBizInfo.StationInfo
	stationResMes := &StationResMes{}
	var resProducts []*models.Product
	// 1. 查dds
	baseProducts := make(map[int32]*models.Product, 0)
	ddsReq := brd.GenDDSProductsReq()
	ddsResp, err := decision.GetDDSProducts(ctx, ddsReq)
	if ddsResp == nil || err != nil {
		return nil
	}
	if len(ddsResp) == 0 {
		return nil
	}
	productList := []int32{}
	for _, ddsProduct := range ddsResp {
		product := &models.Product{}
		if !ddsProduct.RemoveFlag {
			product.BuildFromDDsResponse(ctx, ddsProduct, brd)
			baseProducts[int32(product.ProductID)] = product
			productList = append(productList, int32(product.ProductID))
		}
	}
	if len(productList) == 0 {
		return nil
	}
	// 2. 查库存
	skuList := rpc_process.NewMutliStationInventory(brd).Fetch(ctx, productList)

	skuListFilter := []models.SkuInfo{}
	for _, v := range skuList {
		if util.InArrayInt32(v.ProductID, productList) {
			skuListFilter = append(skuListFilter, v)
		}
	}
	stationResMes = &StationResMes{
		SkuInfo:      skuListFilter,
		BaseProducts: baseProducts,
	}
	// 有了元数据 有了上一次的班次号，一次截取十个
	lenNum := 10
	if stationInfo.LastShiftId == "" {
		for k, v := range stationResMes.SkuInfo {
			baseProduct := stationResMes.BaseProducts[v.ProductID]
			if k >= lenNum {
				break
			}
			//若查询的班次为最后一个班次
			if k == len(stationResMes.SkuInfo)-1 {
				resProducts = append(resProducts, NewPbdStationBusMultiProduct(ctx, baseProduct, v, true, brd))
				break
			}
			resProducts = append(resProducts, NewPbdStationBusMultiProduct(ctx, baseProduct, v, false, brd))
		}
	} else {
		indexNum := 0
		for k, v := range stationResMes.SkuInfo {
			if v.ShiftID == stationInfo.LastShiftId {
				indexNum = k + 1
			}
		}
		for i := indexNum; i < len(stationResMes.SkuInfo); i++ {
			baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo[i].ProductID]
			if i >= indexNum+lenNum {
				break
			}
			if i == len(stationResMes.SkuInfo)-1 {
				resProducts = append(resProducts, NewPbdStationBusMultiProduct(ctx, baseProduct, stationResMes.SkuInfo[i], true, brd))
				break
			}
			resProducts = append(resProducts, NewPbdStationBusMultiProduct(ctx, baseProduct, stationResMes.SkuInfo[i], false, brd))
		}
	}

	return resProducts
}

func NewPbdStationBusMultiProduct(ctx context.Context, baseProduct *models.Product, skuInfo models.SkuInfo, haveLastItem bool, brd *models.BaseReqData) *models.Product {
	p := &models.Product{}
	p.ProductCategory = baseProduct.ProductCategory
	p.OrderType = baseProduct.OrderType
	p.ProductID = baseProduct.ProductID
	p.BusinessID = baseProduct.BusinessID
	p.RequireLevelInt = baseProduct.RequireLevelInt
	p.RequireLevel = baseProduct.RequireLevel
	p.CarpoolType = baseProduct.CarpoolType
	p.ComboType = baseProduct.ComboType
	p.IsSpecialPrice = baseProduct.IsSpecialPrice
	p.CarpoolPriceType = baseProduct.CarpoolPriceType
	p.IsDualCarpoolPrice = baseProduct.IsDualCarpoolPrice
	p.AirportType = baseProduct.AirportType
	p.RailwayType = baseProduct.RailwayType
	p.HotelType = baseProduct.HotelType
	p.ExamType = baseProduct.ExamType
	p.StationServiceControl = baseProduct.StationServiceControl
	p.LevelType = baseProduct.LevelType
	p.RouteType = baseProduct.RouteType
	p.LongRentType = baseProduct.LongRentType
	p.EmergencyServiceType = baseProduct.EmergencyServiceType
	p.BuildEstimateIDByShiftID(ctx, &skuInfo)
	p.RouteID = cast.ToString(skuInfo.RouteGroup)
	p.ShiftID = skuInfo.ShiftID
	p.BizInfo = &models.PrivateBizInfo{
		ComboID:           skuInfo.RouteGroup,
		DepartureTime:     skuInfo.DepartureTime,
		MaxCarpoolSeatNum: skuInfo.RemainSeats,
		IntercityData: models.IntercityData{
			SeatDetailInfo: brd.CommonBizInfo.SeatDetailInfo,
			StationInventoryInfo: &models.StationInventoryInfo{
				SelectInfo: models.StationInventorySelectInfo{
					DepartureTime:             skuInfo.DepartureTime,
					RouteId:                   skuInfo.RouteGroup,
					RemainSeats:               skuInfo.RemainSeats,
					FromStationId:             int(skuInfo.StartStationID),
					DestStationId:             int(skuInfo.EndStationID),
					ShiftID:                   skuInfo.ShiftID,
					LastItem:                  haveLastItem,
					CarryChildrenMaxInventory: skuInfo.CarryChildrenMaxInventory,
				}},
		},
	}

	return p

}
