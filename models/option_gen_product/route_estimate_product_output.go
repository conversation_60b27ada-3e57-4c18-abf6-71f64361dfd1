package option_gen_product

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"git.xiaojukeji.com/gulfstream/passenger-common/dto"
	util2 "git.xiaojukeji.com/gulfstream/passenger-common/util"
)

type routeEstimateProductOutput struct {
	routeIDList []string
	orderInfo   *order_info.OrderInfo
}

func NewRouteEstimateProductOutput(routeIDList []string, orderInfo *order_info.OrderInfo) *routeEstimateProductOutput {
	return &routeEstimateProductOutput{
		routeIDList: routeIDList,
		orderInfo:   orderInfo,
	}
}

func (r *routeEstimateProductOutput) ExecProducts(ctx context.Context, baseReq *models.BaseReqData) []*models.Product {
	if len(r.routeIDList) <= 0 || r.orderInfo == nil {
		return nil
	}

	products := make([]*models.Product, 0)
	pcID := getProductCategory(ctx, r.orderInfo)

	for _, routeID := range r.routeIDList {
		product := r.buildProduct(ctx, baseReq, pcID, routeID)
		if product == nil {
			continue
		}

		products = append(products, product)
	}

	return products
}

func (r *routeEstimateProductOutput) buildProduct(ctx context.Context, baseReq *models.BaseReqData, pcID int64, routeID string) *models.Product {
	if baseReq == nil || r.orderInfo == nil {
		return nil
	}

	product := &models.Product{}

	// 基础信息
	product.ProductCategory = pcID
	product.ProductID = r.orderInfo.GetProductID()
	product.BusinessID = r.orderInfo.GetBusinessID()
	product.RequireLevelInt = r.orderInfo.GetRequireLevel()
	product.RequireLevel = r.orderInfo.RequireLevel
	product.CarpoolType = r.orderInfo.GetCarpoolType()
	product.LevelType = r.orderInfo.GetLevelType()
	product.ComboType = r.orderInfo.GetComboType()
	product.CarpoolPriceType = r.orderInfo.GetCarpoolPriceType()
	product.IsSpecialPrice = r.orderInfo.GetIsSpecialPrice()
	product.IsDualCarpoolPrice = r.orderInfo.GetIsDualCarpoolPrice()

	// 场景标识
	product.AirportType = r.orderInfo.GetAirportType()
	product.RailwayType = r.orderInfo.GetRailwayType()
	product.HotelType = r.orderInfo.GetHotelType()
	product.StationServiceControl = r.orderInfo.GetStationServiceControl()
	product.LongRentType = r.orderInfo.GetLongRentType()
	product.EmergencyServiceType = r.orderInfo.GetEmergencyServiceType()
	product.PaymentsType = r.orderInfo.GetPayType()
	product.RouteType = r.orderInfo.GetRouteType()
	product.IsPickOnTime = r.orderInfo.GetIsPickOnTime()
	// product.SceneType = data.OrderInfo.Scene // dds代码写死赋值为0 理论无用
	// product.ExamType = data.OrderInfo.GetExamType() // 高考场景标识，dos没存，跟价格计算无关，只涉及呼返高考券，行中不涉及呼返理论上无用

	product.RouteID = routeID

	product.BuildEstimateIDByRouteID(ctx, &baseReq.AreaInfo)

	return product
}

// getProductCategory 通过订单信息获取pcID
func getProductCategory(ctx context.Context, orderInfo *order_info.OrderInfo) int64 {
	if orderInfo == nil {
		return 0
	}

	pcID := orderInfo.EstimatePcId
	if pcID != nil {
		productCategory, err := strconv.Atoi(*pcID)
		if err != nil {
			log.Trace.Warnf(ctx, "getProductCategory", "a to i fail:%v", *pcID)
		}

		if productCategory > 0 {
			return int64(productCategory)
		}
	}

	tuple := &dto.OrderInfo{
		ProductId:      int(orderInfo.GetProductID()),
		BusinessId:     int(orderInfo.GetBusinessID()),
		LevelType:      int(orderInfo.GetLevelType()),
		RequireLevel:   int(orderInfo.GetRequireLevel()),
		ComboType:      int(orderInfo.GetComboType()),
		CarpoolType:    int(orderInfo.GetCarpoolType()),
		IsSpecialPrice: orderInfo.GetIsSpecialPrice(),
		Type:           orderInfo.GetType(),
	}

	iProductCategory := util2.NewProductCategoryUtil().GetProductCategoryByNTuple(ctx, tuple)

	return int64(iProductCategory)
}
