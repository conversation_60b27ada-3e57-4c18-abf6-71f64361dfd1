package option_gen_product

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
)

type CarpoolOrderProductGenernator struct {
	OrderInfoList map[int64]*order_info.OrderInfo
}

func (c CarpoolOrderProductGenernator) ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product {
	if len(c.OrderInfoList) == 0 {
		return nil
	}

	products := make([]*models.Product, 0)

	for _, orderInfo := range c.OrderInfoList {
		if !carpool.IsCarpool(util.String2int64(ctx, orderInfo.CarpoolType)) {
			continue
		}

		product := c.buildProduct(ctx, orderInfo)
		products = append(products, product)
	}

	return products
}

func (c CarpoolOrderProductGenernator) buildProduct(ctx context.Context, orderInfo *order_info.OrderInfo) *models.Product {
	if orderInfo == nil || orderInfo.EstimatePcId == nil {
		return nil
	}

	product := &models.Product{}

	// 基础信息
	product.ProductCategory = util.String2int64(ctx, *orderInfo.EstimatePcId)
	product.ProductID = orderInfo.GetProductID()
	product.BusinessID = orderInfo.GetBusinessID()
	product.RequireLevelInt = orderInfo.GetRequireLevel()
	product.RequireLevel = orderInfo.RequireLevel
	product.CarpoolType = orderInfo.GetCarpoolType()
	product.LevelType = orderInfo.GetLevelType()
	product.ComboType = orderInfo.GetComboType()
	product.CarpoolPriceType = orderInfo.GetCarpoolPriceType()
	product.IsSpecialPrice = orderInfo.GetIsSpecialPrice()
	product.IsDualCarpoolPrice = orderInfo.GetIsDualCarpoolPrice()

	// 场景标识
	product.AirportType = orderInfo.GetAirportType()
	product.RailwayType = orderInfo.GetRailwayType()
	product.HotelType = orderInfo.GetHotelType()
	product.StationServiceControl = orderInfo.GetStationServiceControl()
	product.LongRentType = orderInfo.GetLongRentType()
	product.EmergencyServiceType = orderInfo.GetEmergencyServiceType()
	product.PaymentsType = orderInfo.GetPayType()
	product.RouteType = orderInfo.GetRouteType()
	product.IsPickOnTime = orderInfo.GetIsPickOnTime()

	product.BuildEstimateByOption(ctx, []string{
		orderInfo.StartingLat,
		orderInfo.StartingLng,
		orderInfo.DestLat,
		orderInfo.DestLng,
	})

	product.BizInfo = &models.PrivateBizInfo{
		SendOrderId:    util.String2int64(ctx, orderInfo.OrderId),
		CarpoolSeatNum: util.String2int32(ctx, orderInfo.PassengerCount),
	}

	return product
}

func NewCarpoolOrderProductGenernator(orderInfoList map[int64]*order_info.OrderInfo) biz_runtime.OptionProcessProduct {
	return &CarpoolOrderProductGenernator{
		OrderInfoList: orderInfoList,
	}
}
