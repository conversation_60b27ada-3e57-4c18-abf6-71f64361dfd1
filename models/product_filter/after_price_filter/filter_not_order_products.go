package after_price_filter

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type FilterNoOrderProducts struct {
	orderProducts   []models.ProductCategory
	orderProductMap map[models.ProductCategory]int64
	filterProducts  []models.ProductCategory
}

func NewFilterNoOrderProductsAfterPrice(ctx context.Context, estimatePcID string) *FilterNoOrderProducts {
	var productMap = make(map[models.ProductCategory]int64)
	filter := &FilterNoOrderProducts{}
	filter.orderProducts = append(filter.orderProducts, models.ProductCategory(util.ToInt64(estimatePcID)))

	for _, pcid := range filter.orderProducts {
		productMap[pcid] = 1
	}
	filter.orderProductMap = productMap

	return filter
}

func (f *FilterNoOrderProducts) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	for _, pItem := range products {
		value, ok := f.orderProductMap[models.ProductCategory(pItem.Product.ProductCategory)]
		if !ok || value != 1 {
			// 过滤未发单品类
			f.filterProducts = append(f.filterProducts, models.ProductCategory(pItem.Product.ProductCategory))
		}
	}

	return f.filterProducts
}
