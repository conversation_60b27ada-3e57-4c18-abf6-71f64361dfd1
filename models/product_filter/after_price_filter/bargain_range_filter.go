package after_price_filter

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/passenger-common/util"
	apolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"math"
	"strconv"
	"strings"
	"time"
)

const (
	bargainRangeCheckableToggle = "bargain_range_checkable_toggle"
	bargainRangeMultiSelecteExp = "huixuan_multi_select_ab"
	bargainRangeOpenWhiteList   = "bargain_range_open_white_list"
	bargainRangeOpenConf        = "bargain_range_open_city_conf"
	expParams                   = "hit_checkable"
	enable                      = "1"
	logTag                      = "_bargain_range_filter"
)

type BargainRangeFilter struct {
	hasFastCar           bool
	orderedProducts      map[string]dos.RequiredProductStruct
	FastCarEstimateFee   float64
	SpFastCarEstimateFee float64
}

type BargainRangeOpenRule []struct {
	CityId         int               `json:"city_id"`
	CheckCondition []*CheckCondition `json:"check_condition"`
}
type CheckCondition struct {
	StartTime        string `json:"start_time"`
	EndTime          string `json:"end_time"`
	StartDriverMetre int64  `json:"start_driver_metre"`
	EndDriverMetre   int64  `json:"end_driver_metre"`
}

func (b *BargainRangeFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var (
		allRemoved             = []models.ProductCategory{estimate_pc_id.EstimatePcIdHuiXuanCar}
		orderedMinPrice        = -1.0
		recommendPriceLowerNew float64
	)

	accessFilter, bargainRangeProd := b.isAccess(ctx, products)
	if !accessFilter {
		return allRemoved
	}

	if !b.hasFastCar || bargainRangeProd.GetBizInfo() == nil {
		log.Trace.Infof(ctx, logTag, "filter_product_by_no_fast_car")
		return allRemoved
	}

	if bargainRangeProd == nil || bargainRangeProd.GetBargainRecommendInfo() == nil {
		return allRemoved
	}

	if !isBargainRangeOpen(ctx, bargainRangeProd) {
		return allRemoved
	}

	// 已选车型的最低价， 快车、特惠预估价
	for _, productInfo := range b.orderedProducts {
		if orderedMinPrice == -1 {
			orderedMinPrice = productInfo.EstimateFee
		}

		orderedMinPrice = math.Min(orderedMinPrice, productInfo.EstimateFee)
	}

	// 设置预估价，发单使用
	bargainInfo := bargainRangeProd.GetBizInfo()

	// 惠选车左右边界调整
	// 左边界：「惠选车推荐价左边界」，和「已勾车型价格最小值」中，更大的那个
	recommendPriceUpper := util.String2Float64(ctx, bargainRangeProd.GetFastRangeRecommendInfo(consts.RecommendPriceUpper))
	recommendPriceLower := util.String2Float64(ctx, bargainRangeProd.GetFastRangeRecommendInfo(consts.RecommendPriceLower))
	recommendPriceLowerNew = math.Floor(math.Max(recommendPriceLower, orderedMinPrice))

	// 调整后的左边界大于右边界 则过滤掉
	if recommendPriceLowerNew >= recommendPriceUpper {
		return allRemoved
	}

	bargainInfo.PriceLimitLower = util.String2Int64(ctx, bargainRangeProd.GetFastRangeRecommendInfo(consts.BorderPriceLower))
	bargainInfo.PriceLimitUpper = util.String2Int64(ctx, bargainRangeProd.GetFastRangeRecommendInfo(consts.BorderPriceUpper))
	bargainInfo.RecommendPriceLower = int64(recommendPriceLowerNew)
	bargainInfo.RecommendPriceUpper = int64(recommendPriceUpper)
	bargainInfo.FastCarEstimateFee = b.FastCarEstimateFee
	bargainInfo.SpFastCarEstimateFee = b.SpFastCarEstimateFee

	return []models.ProductCategory{}
}

func (b *BargainRangeFilter) isAccess(ctx context.Context, products []*biz_runtime.ProductInfoFull) (bool, *biz_runtime.ProductInfoFull) {
	// 是否存在惠选车
	var baseProduct *biz_runtime.ProductInfoFull
	for _, pfull := range products {
		if estimate_pc_id.EstimatePcIdHuiXuanCar == pfull.GetProductCategory() {
			baseProduct = pfull
		}
		if estimate_pc_id.EstimatePcIdFastCar == pfull.GetProductCategory() {
			b.hasFastCar = true
			b.FastCarEstimateFee = pfull.GetEstimateFee()
		}
		if estimate_pc_id.EstimatePcIdSpecialRate == pfull.GetProductCategory() || estimate_pc_id.EstimatePcIdFastSpecialRate == pfull.GetProductCategory() {
			b.SpFastCarEstimateFee = pfull.GetEstimateFee()
		}
	}

	if baseProduct == nil {
		return false, nil
	}

	pid, apolloParams := baseProduct.GetApolloParams(biz_runtime.WithPIDKey)
	// 功能开量
	allow := apollo.FeatureToggle(ctx, bargainRangeCheckableToggle, pid, apolloParams)
	// 实验分组
	checkableExp, assignParams := apollo.GetParameters(bargainRangeMultiSelecteExp, pid, apolloParams)

	if allow && checkableExp && assignParams != nil && len(assignParams) > 0 {
		if checkable, ok := assignParams[expParams]; ok {
			return checkable == enable, baseProduct
		}
	}

	return false, nil
}

func isBargainRangeOpen(ctx context.Context, prod *biz_runtime.ProductInfoFull) bool {
	if isHitWhiteList(ctx, prod) {
		return true
	}

	config, err := apollo.GetConfigsByNamespaceAndConditions(ctx, bargainRangeOpenConf, apolloModel.NewDefaultCondition().With("city_id", strconv.Itoa(prod.GetCityID())))
	if err != nil || config == nil {
		return false
	}

	openConf := BargainRangeOpenRule{}
	if err = json.Unmarshal(config, &openConf); err != nil || len(openConf) < 1 {
		return false
	}

	rules := openConf[0]
	now := time.Now().Local()
	driverMeter := prod.GetBillDriverMetre()

	for _, rule := range rules.CheckCondition {
		start := hm2TodayTime(rule.StartTime)
		end := hm2TodayTime(rule.EndTime)

		if !(start.Before(now) && end.After(now) && driverMeter >= rule.StartDriverMetre && driverMeter <= rule.EndDriverMetre) {
			return false
		}
	}

	return true
}

func isHitWhiteList(ctx context.Context, prod *biz_runtime.ProductInfoFull) bool {
	pidKey, apoParams := prod.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo.FeatureToggle(ctx, bargainRangeOpenWhiteList, pidKey, apoParams)
}

// hm2TodayTime 时刻h:m 转换为 当天时间
func hm2TodayTime(hm string) time.Time {
	now := time.Now().Local()
	var hour, minu int

	list := strings.Split(hm, ":")
	if hh, err := strconv.Atoi(list[0]); err == nil {
		hour = hh
	}
	if mm, err := strconv.Atoi(list[1]); err == nil {
		minu = mm
	}

	return time.Date(now.Year(), now.Month(), now.Day(), hour, minu, 0, 0, time.Local)
}

func NewBargainRangeFilter(multiRequireProduct map[string]dos.RequiredProductStruct) *BargainRangeFilter {
	return &BargainRangeFilter{
		hasFastCar:           false,
		orderedProducts:      multiRequireProduct,
		FastCarEstimateFee:   -1,
		SpFastCarEstimateFee: -1,
	}
}
