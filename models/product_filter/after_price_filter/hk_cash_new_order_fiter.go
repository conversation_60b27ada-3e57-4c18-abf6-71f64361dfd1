package after_price_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type HkCashNewOrderFilter struct{}

// NewHkCashNewOrderFilter 香港现金支付发单，需要在等应答屏蔽所有不支持现金支付的品类
func NewHkCashNewOrderFilter() *HkCashNewOrderFilter {
	return &HkCashNewOrderFilter{}
}

func (f *HkCashNewOrderFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var (
		allRemoved = make([]models.ProductCategory, 0)
	)

	// 先确定是否是香港，以及是否选择现金支付
	for _, product := range products {
		// 非香港直接返回
		if !product.IsHongKong() {
			return allRemoved
		}
		// 不是现金支付直接返回
		if consts.PayTypeByCash != product.GetUserSelectPayType() {
			return allRemoved
		}
		// 经过上述判断，符合执行过滤要求，则结束当前循环
		break
	}

	// 过滤判断逻辑
	for _, product := range products {
		var isFilter = true

		paymentInfo := product.GetPaymentInfo()
		if paymentInfo == nil {
			allRemoved = append(allRemoved, models.ProductCategory(product.GetProductCategory()))
			continue
		}
		for _, paymentItem := range paymentInfo.Payment {
			// 当前品类所有的支付方式中包含现金支付就不过滤
			if consts.PayTypeByCash == paymentItem.PayType {
				isFilter = false
			}
		}

		if isFilter {
			allRemoved = append(allRemoved, models.ProductCategory(product.GetProductCategory()))
		}
	}

	return allRemoved
}
