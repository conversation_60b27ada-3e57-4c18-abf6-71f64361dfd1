package after_price_filter

import (
	"context"
	"encoding/json"
	"fmt"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"git.xiaojukeji.com/gulfstream/biz-lib-go/logger"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"

	"math"
	"strconv"
)

const (
	OrderFinishCntKey  = "bubble_filter.ord_finish_cnt_14d"
	OnlineDriverCntKey = "bubble_filter.online_dri_cnt_14d"
	StriveOrderCntKey  = "bubble_filter.strive_cnt_14d"
	UfsDominUnity      = "unity"
)

type AbnormalEstimate struct{}

func (filter *AbnormalEstimate) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {

	filterProductCategory := make([]models.ProductCategory, 0)

	configData := apollo.GetConfig(ctx, "tripcloud_config", "tripcloud_abnormal_estimate_filter")
	countData := configData["count_estimate"]
	countEstimate, err := strconv.Atoi(countData)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get tc filter config from apollo error= %v", err)
		return nil
	}
	if len(products) < countEstimate {
		return nil
	}
	product := GetTCLowFeeEstimateId(products)
	phone := products[0].BaseReqData.PassengerInfo.Phone
	params := map[string]string{
		"channel":     fmt.Sprint(product.BaseReqData.CommonInfo.Channel),
		"client_type": fmt.Sprint(product.BaseReqData.CommonInfo.ClientType),
		"city":        fmt.Sprint(product.BaseReqData.AreaInfo.ToArea),
		"product":     fmt.Sprint(product.Product.ProductID),
		"car_level":   fmt.Sprint(product.Product.RequireLevel),
	}

	if !apollo.FeatureToggle(ctx, "gs_tripcloud_hide_abnormal_estimate", phone, params) {
		return nil
	}

	data, err := GetUfsUnityFeature(ctx, int(product.Product.ProductID))
	if err != nil || data == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get tc filter config from ufs error= %v", err)
		return nil
	}

	ufsData := make(map[string]int)
	ufsConfigData := configData["ufs_data"]
	err = json.Unmarshal([]byte(ufsConfigData), &ufsData)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal error= %v", err)
		return nil

	}
	if data[OrderFinishCntKey] > ufsData[OrderFinishCntKey] {
		return nil
	}
	if tripcloud.IsHonghuProduct(ctx, int(product.Product.ProductID)) {
		if data[OnlineDriverCntKey] > ufsData[OnlineDriverCntKey] {
			return nil
		}
		if data[OrderFinishCntKey] < ufsData[OrderFinishCntKey] && data[OnlineDriverCntKey] < ufsData[OnlineDriverCntKey] {
			filterProductCategory = append(filterProductCategory, models.ProductCategory(product.GetProductCategory()))
		}
	} else if tripcloud.IsTripcloudProductID(consts.ProductID(product.Product.ProductID)) {
		if data[StriveOrderCntKey] > ufsData[StriveOrderCntKey] {
			return nil
		}
		if data[OrderFinishCntKey] < ufsData[OrderFinishCntKey] && data[StriveOrderCntKey] < ufsData[StriveOrderCntKey] {
			filterProductCategory = append(filterProductCategory, models.ProductCategory(product.GetProductCategory()))
		}
	}
	writePublicLog(ctx, product)
	return filterProductCategory
}

func writePublicLog(ctx context.Context, product *biz_runtime.ProductInfoFull) {
	publicLogData := make(map[string]interface{})
	publicLogData["product_id"] = product.Product.ProductID
	publicLogData["require_level"] = product.Product.RequireLevel
	publicLogData["business_id"] = product.Product.BusinessID
	publicLogData["total_fee"] = product.BillDetail.TotalFee
	publicLogData["channel"] = product.BaseReqData.CommonInfo.Channel
	publicLogData["client_type"] = product.BaseReqData.CommonInfo.ClientType
	publicLogData["area"] = product.GetAreaInfo().ToArea
	publicLogData["is_hide_estimate"] = true
	logger.Public(ctx, "g_tripcloud_abnormal_estimate", publicLogData)
}

// 获取三方运力最低价的预估id
func GetTCLowFeeEstimateId(products []*biz_runtime.ProductInfoFull) *biz_runtime.ProductInfoFull {
	minEstimateFee := math.MaxFloat64
	var data *biz_runtime.ProductInfoFull
	for _, product := range products {
		if product.BillDetail.TotalFee < minEstimateFee {
			minEstimateFee = product.BillDetail.TotalFee
			data = product

		}
	}
	return data
}

func GetUfsUnityFeature(ctx context.Context, ProductId int) (map[string]int, error) {

	keys := []string{OrderFinishCntKey, OnlineDriverCntKey, StriveOrderCntKey}
	paramData := map[string]string{
		"product_id": strconv.Itoa(ProductId),
	}
	params := map[string]interface{}{
		"params": paramData,
		"target": "kflower",
	}
	ufsFeature, err := ufs.MgetProxy(ctx, UfsDominUnity, keys, params)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get unity from ufs err: key:%s, err:%v", keys, err)
		return nil, err
	}

	data := make(map[string]int)
	for _, key := range keys {
		featureData := ufsFeature.GetData()[key]
		if featureData == nil || featureData.Errno != 0 || featureData.Value == nil {
			return nil, nil
		}
		val, err := strconv.Atoi(featureData.GetValue())
		if err != nil {
			return nil, err
		}
		data[key] = val
		d, err := json.Marshal(data)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "json marshal error:%v", err)

		}
		log.Trace.Infof(ctx, trace.DLTagUndefined, "GetUfsUnityFeature resp:%s", string(d))
	}
	return data, nil
}
