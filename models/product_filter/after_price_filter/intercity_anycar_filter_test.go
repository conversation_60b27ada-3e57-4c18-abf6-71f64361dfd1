package after_price_filter

import (
	"context"
	"flag"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"path/filepath"
	"runtime"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

func TestMain(m *testing.M) {
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		fmt.Println("无法获取当前文件路径")
		return
	}
	dir := filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filename))))
	confPath := filepath.Join(dir, "/conf/app_dev.toml")
	flag.StringVar(&confPath, "c", confPath, "-c set config file path") // default config file is conf/app.toml
	flag.Parse()
	fmt.Printf("confPath is %s\n", confPath)
	conf.InitConf(confPath)
	log.Init()
	fmt.Printf("confPath is %s\n", confPath)
	m.Run()
}

func buildBaseReqData(pageType int32, from, to int32) *models.BaseReqData {
	baseReq := &models.BaseReqData{
		CommonInfo: models.CommonInfo{
			PageType: pageType,
			SourceID: 1,
			Channel:  1,
		},
		AreaInfo: models.AreaInfo{
			Area:   from,
			ToArea: to,
		},
		PassengerInfo: models.PassengerInfo{
			Phone: "1234567890",
		},
	}
	return baseReq
}

func buildProducts(pcids []int64, seatNum, maxSeatNum int32) []*biz_runtime.ProductInfoFull {
	products := make([]*biz_runtime.ProductInfoFull, 0)
	for _, pcid := range pcids {
		products = append(products, &biz_runtime.ProductInfoFull{
			Product: &models.Product{
				ProductCategory: pcid,
				CarpoolType:     3,
				ComboType:       302,
				BizInfo: &models.PrivateBizInfo{
					CarpoolSeatNum:    seatNum,
					MaxCarpoolSeatNum: maxSeatNum,
					DepartureRange:    []int64{},
				},
			},
		})
	}
	return products
}

func TestDo_Success(t *testing.T) {
	convey.Convey("Test Do function success case", t, func() {
		// 初始化测试数据
		baseReq := buildBaseReqData(page_type.PageTypeIntercityEstimate, 1, 2)
		products := buildProducts([]int64{5, 7501, 7502}, 1, 4)
		products = append(products, &biz_runtime.ProductInfoFull{
			Product: &models.Product{
				ProductCategory: 666,
				CarpoolType:     4,
				ComboType:       303,
				BizInfo: &models.PrivateBizInfo{
					CarpoolSeatNum:    1,
					MaxCarpoolSeatNum: 4,
					DepartureRange:    []int64{},
				},
			},
		})
		filter := NewIntercityAnycarFilter(baseReq)

		// 执行测试
		result := filter.Do(context.Background(), products)

		// 验证结果
		convey.So(result, convey.ShouldNotBeNil)
	})
	convey.Convey("Test Do function no product case", t, func() {
		// 初始化测试数据
		baseReq := buildBaseReqData(page_type.PageTypeIntercityEstimate, 1, 1)
		products := buildProducts([]int64{}, 1, 4)

		filter := NewIntercityAnycarFilter(baseReq)

		// 执行测试
		result := filter.Do(context.Background(), products)

		// 验证结果
		convey.So(result, convey.ShouldBeNil)
	})
	convey.Convey("Test Do function page type case", t, func() {
		// 初始化测试数据
		baseReq := buildBaseReqData(page_type.PageTypeCarpoolTabEstimate, 1, 1)
		products := buildProducts([]int64{5}, 1, 4)

		filter := NewIntercityAnycarFilter(baseReq)

		// 执行测试
		result := filter.Do(context.Background(), products)

		// 验证结果
		convey.So(result, convey.ShouldBeNil)
	})
	convey.Convey("Test Do function seat num case", t, func() {
		// 初始化测试数据
		baseReq := buildBaseReqData(page_type.PageTypeIntercityEstimate, 1, 1)
		products := buildProducts([]int64{5, 7501}, 3, 2)

		filter := NewIntercityAnycarFilter(baseReq)
		patches := gomonkey.ApplyFunc(filter.getPredictDistance, func(_ *biz_runtime.ProductInfoFull) int64 {
			return 40000
		})
		defer patches.Reset()
		// 执行测试
		result := filter.Do(context.Background(), products)

		// 验证结果
		convey.So(result, convey.ShouldNotBeNil)
	})
}

func TestGetIndividuationDistanceConfig(t *testing.T) {
	convey.Convey("Test GetIndividuationDistanceConfig success case", t, func() {
		defer mockey.UnPatchAll()
		ctx := context.Background()
		baseReq := &models.BaseReqData{
			CommonInfo: models.CommonInfo{
				SourceID: 1,
			},
		}
		distanceConvert := innerDistanceConvert{
			FromCityID: 1,
			ToCity:     2,
			PcID:       5,
			PageType:   8,
			Phone:      "1234567890",
			Channel:    5,
		}
		assignment := model.NewAssignment(IntercityDistanceThresholdIndividuation, "", "control_group", map[string]interface{}{
			"upper_limit": 100000,
			"same_city":   "30000",
			"diff_city":   "20000",
		})
		toggle := model.NewToggleResult(IntercityDistanceThresholdIndividuation, true, assignment, nil, "", 0, "", "")

		mockey.Mock(ApolloSDK.FeatureToggle).Return(toggle, nil).Build()
		mockey.Mock((*model.ToggleResult).IsAllow).Return(true).Build()

		filter := &IntercityAnycarFilter{}
		config := filter.GetIndividuationDistanceConfig(ctx, baseReq, distanceConvert)

		convey.So(config, convey.ShouldNotBeNil)
		convey.So(config.UpperLimit, convey.ShouldEqual, int64(100000))
		convey.So(config.SameCityThreshold, convey.ShouldEqual, int64(30000))
		convey.So(config.DiffCityThreshold, convey.ShouldEqual, int64(20000))
	})
}

func TestGetIntercityConfigByCity_Success(t *testing.T) {
	convey.Convey("Test GetIntercityConfigByCity success case", t, func() {
		defer mockey.UnPatchAll()
		ctx := context.Background()
		cityID := 1
		user := model.NewUser("")

		// 模拟Apollo返回的配置
		assignment := model.NewAssignment("gs_intercity_distance_open_rule_by_city", "", "test_group", map[string]interface{}{
			"same_city_low_limit":   "20000",
			"same_city_upper_limit": "80000",
			"diff_city_low_limit":   "15000",
			"diff_city_upper_limit": "90000",
		})
		toggle := model.NewToggleResult("gs_intercity_distance_open_rule_by_city", true, assignment, nil, "", 0, "", "")

		// 模拟ApolloSDK.FeatureToggle调用
		mockey.Mock(ApolloSDK.FeatureToggle).Return(toggle, nil).Build()
		mockey.Mock((*model.ToggleResult).IsAllow).Return(true).Build()

		filter := &IntercityAnycarFilter{}
		config := filter.getIntercityConfigByCity(ctx, cityID, user)

		convey.So(config, convey.ShouldNotBeNil)
		convey.So(config.UpperLimit, convey.ShouldEqual, int64(90000))
		convey.So(config.SameCityThreshold, convey.ShouldEqual, int64(20000))
		convey.So(config.DiffCityThreshold, convey.ShouldEqual, int64(15000))
	})
}
