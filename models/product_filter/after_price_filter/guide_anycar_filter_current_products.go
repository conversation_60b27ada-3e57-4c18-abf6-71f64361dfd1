package after_price_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type GuideAnycarFilterCurrentProducts struct {
	currentProducts []models.ProductCategory
}

func NewGuideAnyCarFilterFromNewOrder(ctx context.Context, multiProductCategory []ufs.OneMRP) *GuideAnycarFilterCurrentProducts {
	//if len(multiProductCategory) == 0 {
	//	return nil
	//}
	//multiProductCategory := []struct {
	//	ProductCategory int `json:"product_category"`
	//}{}
	//err := json.Unmarshal([]byte(multiRequireProduct), &multiProductCategory)
	//if err != nil {
	//	log.Trace.Errorf(ctx, trace.DLTagUndefined, "json unmarshal error : %v", err)
	//	return nil
	//}
	if len(multiProductCategory) == 0 {
		return nil
	}
	filter := &GuideAnycarFilterCurrentProducts{}
	for _, p := range multiProductCategory {
		filter.currentProducts = append(filter.currentProducts, models.ProductCategory(p.ProductCategory))
	}
	return filter
}

func (f *GuideAnycarFilterCurrentProducts) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	return f.currentProducts
}

func NewGuideAnyCarFilterCurrentProducts(ctx context.Context, multiRequireProduct map[string]dos.RequiredProductStruct) *GuideAnycarFilterCurrentProducts {
	if len(multiRequireProduct) == 0 {
		return nil
	}
	filter := &GuideAnycarFilterCurrentProducts{}
	for _, p := range multiRequireProduct {
		estimatePcId := cast.ToInt(p.EstimatePcID)
		if estimatePcId <= 0 {
			estimatePcId = p.ProductCategory
		}

		filter.currentProducts = append(filter.currentProducts, models.ProductCategory(estimatePcId))
	}
	return filter
}
