package after_price_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type RecommendPriceFilter struct{}

func NewRecommendPriceFilter() *RecommendPriceFilter {
	return &RecommendPriceFilter{}
}

func (r *RecommendPriceFilter) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) (filter []models.ProductCategory) {
	var allRemoved = make([]models.ProductCategory, 0)
	for _, p := range productMap {
		if p.GetProductCategory() != estimate_pc_id.EstimatePcIdBargain {
			allRemoved = append(allRemoved, models.ProductCategory(p.GetProductCategory()))
		}
	}

	return allRemoved
}
