package after_price_filter

import (
	"context"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"reflect"
	"testing"
)

// 检查目标是否存在于 slice 或 map 中
func assertTargetInContains(target interface{}, container interface{}) bool {
	v := reflect.ValueOf(container)
	kind := v.Kind()

	switch kind {
	case reflect.Slice, reflect.Array:
		found := false
		for i := 0; i < v.Len(); i++ {
			if reflect.DeepEqual(v.Index(i).Interface(), target) {
				found = true
				break
			}
		}
		return found
	case reflect.Map:
		for _, key := range v.MapKeys() {
			if reflect.DeepEqual(key.Interface(), target) {
				return true
			}
		}
		return false
	default:
		return false
	}
}

// 设置所需的依赖项模拟
func setupMockDependencies() func() {
	var patches []*mockey.Mocker

	// Mock MultiGetFeatures 函数
	patches = append(patches, mockey.Mock((*ddlog.DiLogHandle).Infof).Return().Build())
	patches = append(patches, mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build())

	return func() {
		for _, patch := range patches {
			patch.UnPatch()
		}
	}
}

func buildProduct(products []*biz_runtime.ProductInfoFull, haveCash []bool) []*biz_runtime.ProductInfoFull {
	for index, product := range products {
		if haveCash[index] {
			hu := &PriceApi.EstimateNewFormData{
				PaymentInfo: &PriceApi.EstimateNewFormPaymentInfo{
					Payment: []*PriceApi.PaymentElem{
						{
							PayType: 1024,
						},
						{
							PayType: 5,
						},
						{
							PayType: 20,
						},
					},
				},
			}
			product.SetHu(hu)
			continue
		}
		hu := &PriceApi.EstimateNewFormData{
			PaymentInfo: &PriceApi.EstimateNewFormPaymentInfo{
				Payment: []*PriceApi.PaymentElem{
					{
						PayType: 5,
					},
					{
						PayType: 20,
					},
				},
			},
		}
		product.SetHu(hu)
	}
	return products
}

type Want struct {
	want    bool
	wantObj []models.ProductCategory
}

func Test_HkCashNewOrderFilter_Do(t *testing.T) {
	tests := []*struct {
		name     string
		ctx      context.Context
		products []*biz_runtime.ProductInfoFull
		want     Want
	}{
		{
			name: "null case",
			ctx:  context.Background(),
			want: Want{
				wantObj: make([]models.ProductCategory, 0),
			},
		},
		{
			name: "not in HK",
			ctx:  context.Background(),
			products: []*biz_runtime.ProductInfoFull{
				{
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							//Area:   357,
							//ToArea: 357,
						},
						CommonInfo: models.CommonInfo{
							PaymentsType: 1024,
						},
					},
				},
				{},
			},
			want: Want{
				wantObj: make([]models.ProductCategory, 0),
			},
		},
		{
			name: "not cash pay",
			ctx:  context.Background(),
			products: []*biz_runtime.ProductInfoFull{
				{
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
						CommonInfo: models.CommonInfo{
							//PaymentsType: 1024,
						},
					},
				},
			},
			want: Want{
				wantObj: make([]models.ProductCategory, 0),
			},
		},
		{
			name: "prouctcategory存在但是payment不存在",
			ctx:  context.Background(),
			products: []*biz_runtime.ProductInfoFull{
				{
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
						CommonInfo: models.CommonInfo{
							PaymentsType: 1024,
						},
					},
					Product: &models.Product{
						ProductCategory: 357,
					},
				},
			},
			want: Want{
				wantObj: []models.ProductCategory{
					357,
				},
			},
		},
		{
			name: "not payment info",
			ctx:  context.Background(),
			products: []*biz_runtime.ProductInfoFull{
				{
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
						CommonInfo: models.CommonInfo{
							PaymentsType: 1024,
						},
					},
					Product: &models.Product{
						ProductCategory: 7520,
					},
				},
			},
			want: Want{
				wantObj: []models.ProductCategory{
					7520,
				},
			},
		},
		{
			name: "have cash pay",
			ctx:  context.Background(),
			products: buildProduct([]*biz_runtime.ProductInfoFull{
				{
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
						CommonInfo: models.CommonInfo{
							PaymentsType: 1024,
						},
					},
					Product: &models.Product{
						ProductCategory: 7520,
					},
				},
				{
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
						CommonInfo: models.CommonInfo{
							PaymentsType: 1024,
						},
					},
					Product: &models.Product{
						ProductCategory: 7501,
					},
				},
			}, []bool{true, true}),
			want: Want{
				wantObj: []models.ProductCategory{
					//7501,
				},
			},
		},
		{
			name: "user select cash , but product not cash pay",
			ctx:  context.Background(),
			products: buildProduct([]*biz_runtime.ProductInfoFull{
				{
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
						CommonInfo: models.CommonInfo{
							PaymentsType: 1024,
						},
					},
					Product: &models.Product{
						ProductCategory: 7520,
					},
				},
				{
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							Area:   357,
							ToArea: 357,
						},
						CommonInfo: models.CommonInfo{
							PaymentsType: 1024,
						},
					},
					Product: &models.Product{
						ProductCategory: 7501,
					},
				},
			}, []bool{false, false}),
			want: Want{
				wantObj: []models.ProductCategory{
					7520,
					7501,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer setupMockDependencies()()
			filter := NewHkCashNewOrderFilter()
			got := filter.Do(tt.ctx, tt.products)
			assert.Equal(t, tt.want.wantObj, got)
		})
	}
}
