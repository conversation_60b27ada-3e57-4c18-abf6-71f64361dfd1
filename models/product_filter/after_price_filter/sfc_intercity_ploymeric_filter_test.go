package after_price_filter

//import (
//	"context"
//	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
//	apolloNew "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
//	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
//	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
//	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
//	. "github.com/bytedance/mockey"
//	. "github.com/smartystreets/goconvey/convey"
//	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
//	"testing"
//)
//
//func Test_filterABtoggle(t *testing.T) {
//	filter := &SfcIntercityPolymericFilter{
//		req: &models.BaseReqData{
//			AreaInfo:   models.AreaInfo{},
//			CommonInfo: models.CommonInfo{},
//		},
//	}
//	intercityProducts := make([]*biz_runtime.ProductInfoFull, 1)
//	for i, _ := range intercityProducts {
//		intercityProducts[i] = &biz_runtime.ProductInfoFull{
//			Product: &models.Product{
//				ProductCategory: 7201,
//				BizInfo: &models.PrivateBizInfo{
//					ComboID: 1,
//				},
//			},
//		}
//	}
//
//	PatchConvey("Test_IsHitAbMaster", t, func() {
//		Convey("Case1: 正常获取配置", func() {
//			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("{\"ab_multi_test\": [\"sfc_chengji_daoliu\"]}").Build()
//			defer mockGetDcmpContent.UnPatch()
//
//			mockToggleResult := Mock(apolloNew.FeatureExp).Return(true, &model.Assignment{}).Build()
//			defer mockToggleResult.UnPatch()
//
//			mockGetParameter := Mock((*model.Assignment).GetParameter).Return("2").Build()
//			defer mockGetParameter.UnPatch()
//
//			// storeAdditionalInfo
//			// log.Trace.Infof
//			mockstoreAdditionalInfo := Mock(storeAdditionalInfo).Return().Build()
//			defer mockstoreAdditionalInfo.UnPatch()
//
//			mockTrace := Mock((*ddlog.DiLogHandle).Infof).Return().Build()
//			defer mockTrace.UnPatch()
//
//			var context context.Context
//			filter.filterABtoggle(context, intercityProducts)
//		})
//
//		Convey("Case2: 正常获取配置", func() {
//			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("{\"ab_multi_test\": [\"sfc_chengji_daoliu\"]}").Build()
//			defer mockGetDcmpContent.UnPatch()
//
//			mockToggleResult := Mock(apolloNew.FeatureExp).Return(true, &model.Assignment{}).Build()
//			defer mockToggleResult.UnPatch()
//
//			mockGetParameter := Mock((*model.Assignment).GetParameter).Return("1").Build()
//			defer mockGetParameter.UnPatch()
//
//			mockstoreAdditionalInfo := Mock(storeAdditionalInfo).Return().Build()
//			defer mockstoreAdditionalInfo.UnPatch()
//
//			mockTrace := Mock((*ddlog.DiLogHandle).Infof).Return().Build()
//			defer mockTrace.UnPatch()
//
//			var context context.Context
//			filter.filterABtoggle(context, intercityProducts)
//		})
//
//		Convey("Case3: 正常获取配置", func() {
//			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("{\"ab_multi_test\": [\"sfc_chengji_daoliu\"]}").Build()
//			defer mockGetDcmpContent.UnPatch()
//
//			mockToggleResult := Mock(apolloNew.FeatureExp).Return(true, &model.Assignment{}).Build()
//			defer mockToggleResult.UnPatch()
//
//			mockGetParameter := Mock((*model.Assignment).GetParameter).Return("5").Build()
//			defer mockGetParameter.UnPatch()
//
//			mockstoreAdditionalInfo := Mock(storeAdditionalInfo).Return().Build()
//			defer mockstoreAdditionalInfo.UnPatch()
//
//			mockTrace := Mock((*ddlog.DiLogHandle).Infof).Return().Build()
//			defer mockTrace.UnPatch()
//
//			var context context.Context
//			filter.filterABtoggle(context, intercityProducts)
//		})
//
//		Convey("Case4: 无法正常获取配置", func() {
//			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("").Build()
//			defer mockGetDcmpContent.UnPatch()
//
//			mockToggleResult := Mock(apolloNew.FeatureExp).Return(true, &model.Assignment{}).Build()
//			defer mockToggleResult.UnPatch()
//
//			mockGetParameter := Mock((*model.Assignment).GetParameter).Return("2").Build()
//			defer mockGetParameter.UnPatch()
//
//			// storeAdditionalInfo
//			mockstoreAdditionalInfo := Mock(storeAdditionalInfo).Return().Build()
//			defer mockstoreAdditionalInfo.UnPatch()
//
//			mockTrace := Mock((*ddlog.DiLogHandle).Infof).Return().Build()
//			defer mockTrace.UnPatch()
//
//			var context context.Context
//			intercityProducts1 := make([]*biz_runtime.ProductInfoFull, 1)
//			for i, _ := range intercityProducts1 {
//				intercityProducts[i] = &biz_runtime.ProductInfoFull{}
//			}
//			filter.filterABtoggle(context, intercityProducts1)
//		})
//
//		Convey("Case7: 无法正常获取配置", func() {
//			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("{\"ab_multi_test\": [\"sfc_chengji_daoliu\"]}").Build()
//			defer mockGetDcmpContent.UnPatch()
//
//			mockToggleResult := Mock(apolloNew.FeatureExp).Return(true, &model.Assignment{}).Build()
//			defer mockToggleResult.UnPatch()
//
//			mockGetParameter := Mock((*model.Assignment).GetParameter).Return("1").Build()
//			defer mockGetParameter.UnPatch()
//
//			// storeAdditionalInfo
//			mockstoreAdditionalInfo := Mock(storeAdditionalInfo).Return().Build()
//			defer mockstoreAdditionalInfo.UnPatch()
//
//			mockTrace := Mock((*ddlog.DiLogHandle).Infof).Return().Build()
//			defer mockTrace.UnPatch()
//
//			var context context.Context
//			filter.filterABtoggle(context, intercityProducts)
//		})
//
//		Convey("Case6: 无法正常获取配置", func() {
//			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("{\"ab_multi_test\": [\"sfc_chengji_daoliu\"]}").Build()
//			defer mockGetDcmpContent.UnPatch()
//
//			mockToggleResult := Mock(apolloNew.FeatureExp).Return(true, &model.Assignment{}).Build()
//			defer mockToggleResult.UnPatch()
//
//			mockGetParameter := Mock((*model.Assignment).GetParameter).Return("0").Build()
//			defer mockGetParameter.UnPatch()
//
//			// storeAdditionalInfo
//			mockstoreAdditionalInfo := Mock(storeAdditionalInfo).Return().Build()
//			defer mockstoreAdditionalInfo.UnPatch()
//
//			var context context.Context
//			filter.filterABtoggle(context, intercityProducts)
//		})
//
//	})
//}
