package after_price_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const CarpoolInterDistance = 30000

type CarpoolExclusive struct{}

func NewCarpoolExclusivePriceFilter() *CarpoolExclusive {
	return &CarpoolExclusive{}
}

func (c *CarpoolExclusive) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var (
		distance       int64
		carpoolStation bool
		carpoolInter   bool
		allRemoved     = make([]models.ProductCategory, 0)
	)

	for _, p := range products {
		if p.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
			distance = c.getPredictDistance(p)
			carpoolStation = true
		}

		if p.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolInterNew {
			carpoolInter = true
		}
	}

	if carpoolStation && carpoolInter {
		if distance > CarpoolInterDistance {
			allRemoved = append(allRemoved, estimate_pc_id.EstimatePcIdCarpoolStation)
		} else {
			allRemoved = append(allRemoved, estimate_pc_id.EstimatePcIdCarpoolInterNew)
		}
	}

	return allRemoved
}

func (c *CarpoolExclusive) getPredictDistance(prod *biz_runtime.ProductInfoFull) int64 {
	if prod == nil {
		return 0
	}
	estimateFee := prod.GetDirectEstimatePrice()
	if estimateFee == nil {
		return 0
	}
	if meter := estimateFee.GetFeeAttributes().GetInt("driver_metre"); meter != nil {
		return *meter
	}

	return 0
}
