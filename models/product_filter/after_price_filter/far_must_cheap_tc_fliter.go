package after_price_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/terminal/access_key_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const TCSubGroupId = 1
const FMCSubGroupId = 6
const ChaoZhiDaGroupId = 10
const KFlowerSp = 115

type FarMustCheapAgainstTCFilter struct {
	productsGen *biz_runtime.ProductsGenerator
}

func NewFarMustCheapAgainstTCFilter(productsGen *biz_runtime.ProductsGenerator) *FarMustCheapAgainstTCFilter {
	return &FarMustCheapAgainstTCFilter{
		productsGen: productsGen,
	}
}

func (f *FarMustCheapAgainstTCFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var (
		allRemoved        = make([]models.ProductCategory, 0)
		farMustCheaperMap = make(map[int64]bool, 0)
		farMustCheapList  = make([]models.ProductCategory, 0)
	)

	if len(products) <= 0 {
		return allRemoved
	}

	for _, p := range biz_runtime.GetAggregationCars(ctx, FMCSubGroupId, products) {
		farMustCheaperMap[p] = true
	}

	// 当前列表中包含的所有远必省品类
	for _, pfull := range products {
		if _, ok := farMustCheaperMap[pfull.GetProductCategory()]; ok {
			farMustCheapList = append(farMustCheapList, models.ProductCategory(pfull.GetProductCategory()))
		}
	}

	// 端不支持展示远必省盒子
	if !isSupport(ctx, *products[0].BaseReqData) {
		return farMustCheapList
	}

	// 远必省盒子车型价格不一致 过滤所有远必省特价车
	ok, farMustCheapProds := priceEqual(ctx, products, 105)
	if !ok {
		return farMustCheapProds
	}

	ret := biz_runtime.GetTCExperimentParams(ctx, *products[0].BaseReqData)
	if hitFarMustCheaperBox, ok := ret["hitFarMustCheaperBox"]; ok {
		// 获取组信息与参数
		isHuaxiaozhuCapOut := ret["isHuaxiaozhuCapOut"]
		needFilterThirdPartyBox := ret["needFilterThirdPartyBox"]
		// 命中远必省盒子
		if "1" == hitFarMustCheaperBox {
			// 过滤三方车型
			if "1" == needFilterThirdPartyBox {
				for _, item := range f.productsGen.FinalSubGroupId2Config[TCSubGroupId].ProductList {
					p := util.ToInt64(item)
					// 过滤三方车型时， 花小猪出盒子 不过滤
					if "1" == isHuaxiaozhuCapOut && p == KFlowerSp {
						continue
					}
					allRemoved = append(allRemoved, models.ProductCategory(p))
				}
			}
		} else {
			return farMustCheapList
		}
	} else {
		return farMustCheapList
	}

	return allRemoved
}

func isSupport(ctx context.Context, data models.BaseReqData) bool {
	if util.InArrayInt32(data.CommonInfo.AccessKeyID,
		[]int32{
			access_key_id.AccessKeyIdDidiIosPassengerApp,
			access_key_id.AccessKeyIdDidiAndroidPassengerApp,
		},
	) { //na
		if util.VersionCompare(data.CommonInfo.AppVersion, "6.6.15") > 0 {
			return true
		}
	}

	if util.InArrayInt32(data.CommonInfo.AccessKeyID,
		[]int32{
			access_key_id.AccessKeyIdDidiWeChatMiniProgram,
			access_key_id.AccessKeyIdDidiAlipayMiniProgram,
		},
	) { //小程序
		if util.VersionCompare(data.CommonInfo.AppVersion, "6.6.94") > 0 {
			return true
		}
	}

	return false
}

func priceEqual(ctx context.Context, products []*biz_runtime.ProductInfoFull, levelType int32) (bool, []models.ProductCategory) {
	var (
		baseEstimateFee = -1.0
		equal           = true
		pcIdList        = make([]models.ProductCategory, 0)
	)

	for _, p := range products {
		if levelType != p.GetLevelType() {
			continue
		}

		pcIdList = append(pcIdList, models.ProductCategory(p.GetProductCategory()))

		if baseEstimateFee < 0 {
			baseEstimateFee = p.GetEstimateFee()
			continue
		}
		if baseEstimateFee != p.GetEstimateFee() {
			equal = false
		}
	}

	return equal, pcIdList
}
