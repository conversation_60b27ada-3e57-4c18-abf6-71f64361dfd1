package before_price_filter

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type scanCodeFilter struct {
	baseReq *models.BaseReqData
}

func NewScanCodeFilter(baseReq *models.BaseReqData) biz_runtime.BeforePriceProductsFilter {
	return &scanCodeFilter{
		baseReq: baseReq,
	}
}

// 区别于其他过滤，扫码上车班次有效需将其他班次过滤，该过滤逻辑走clearProducts
func (c *scanCodeFilter) Exec(ctx context.Context, products []*models.Product) []models.ProductCategory {
	if len(products) == 0 {
		return nil
	}

	isScanCode := c.baseReq.CommonInfo.IsScanCode
	if 0 == len(isScanCode) {
		return nil
	}
	busServerShiftId := c.baseReq.CommonInfo.ScanCodeShiftId

	if hitScanCode(ctx, busServerShiftId) {
		// 有票只保留扫码班次
		if judgeRestSeat(ctx, products, busServerShiftId) {
			c.clearProducts(ctx, products, busServerShiftId)
			c.baseReq.CommonBizInfo.ValidScanCode = true
		}
	}

	return nil
}

func judgeRestSeat(ctx context.Context, products []*models.Product, busServerShiftId string) bool {
	for j := 0; j < len(products); j++ {
		if !judgeValidPoint(ctx, products[j]) {
			continue
		}
		prod := products[j].BizInfo.StationInventoryInfo.StationInventorys
		for i := 0; i < len(prod); i++ {
			if prod[i].BusServiceShiftID == busServerShiftId {
				if prod[i].RemainSeats > 0 {
					return true
				}
			}
		}
	}

	return false
}

func hitScanCode(ctx context.Context, busServerShiftId string) bool {
	if len(busServerShiftId) == 0 {
		return false
	}

	return true
}

func judgeValidPoint(ctx context.Context, product *models.Product) bool {
	return product != nil && product.BizInfo != nil &&
		product.BizInfo.StationInventoryInfo != nil &&
		product.BizInfo.StationInventoryInfo.StationInventorys != nil
}

func (c *scanCodeFilter) clearProducts(ctx context.Context, products []*models.Product, busServerShiftId string) {
	for i := 0; i < len(products); i++ {
		exist := false
		if !judgeValidPoint(ctx, products[i]) {
			continue
		}
		prod := products[i].BizInfo.StationInventoryInfo.StationInventorys
		for j := 0; j < len(prod); j++ {
			if prod[j].BusServiceShiftID == busServerShiftId {
				products[i].BizInfo.StationInventoryInfo.StationInventorys =
					products[i].BizInfo.StationInventoryInfo.StationInventorys[j : j+1]
				exist = true
				break
			}
		}
		if exist {
			products = products[i : i+1]
			c.baseReq.CommonBizInfo.ValidScanCode = true
			return
		}
	}
	return
}
