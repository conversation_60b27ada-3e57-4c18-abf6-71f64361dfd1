package before_price_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const defaultMaxInventory = 5

type FixMaxInventory struct {
	baseReq *models.BaseReqData
}

func NewFixMaxInventory(req *models.BaseReqData) *FixMaxInventory {
	return &FixMaxInventory{
		baseReq: req,
	}
}

func (f *FixMaxInventory) Exec(ctx context.Context, products []*models.Product) []models.ProductCategory {
	var (
		toBeRemoved []models.ProductCategory
	)
	if len(products) > 0 {
		f.fixMaxInventory(ctx, products[0])
	}
	return toBeRemoved
}

func (f *FixMaxInventory) fixMaxInventory(ctx context.Context, product *models.Product) {
	maxInventory := int32(defaultMaxInventory)
	if product.BizInfo != nil && product.BizInfo.RouteDetailV2 != nil && product.BizInfo.RouteDetailV2.RouteExtendInfo != nil &&
		product.BizInfo.RouteDetailV2.RouteExtendInfo.SeatLimit != nil && product.BizInfo.RouteDetailV2.RouteExtendInfo.SeatLimit.MaxPassengerCount > 0 {
		// 路线有最大购票限制
		maxInventory = product.BizInfo.RouteDetailV2.RouteExtendInfo.SeatLimit.MaxPassengerCount
	}
	if f.baseReq.CommonBizInfo.ShiftInfo != nil {
		// 成人票，根据duse返回的字段
		if f.baseReq.CommonBizInfo.ShiftInfo.RemainSeats != nil && *f.baseReq.CommonBizInfo.ShiftInfo.RemainSeats >= 0 {
			maxInventory = util.MinInt32(*f.baseReq.CommonBizInfo.ShiftInfo.RemainSeats, maxInventory)
		}
		// 携童票，根据duse返回的字段
		if f.baseReq.CommonBizInfo.ShiftInfo.RemainCarryChildNum != nil {
			f.baseReq.CommonBizInfo.CarryChildrenMaxInventory = *f.baseReq.CommonBizInfo.ShiftInfo.RemainCarryChildNum
		}
	}
	if maxInventory <= 0 {
		// 用户可以添加乘车人，在发单的时候拦截
		f.baseReq.CommonBizInfo.MaxInventory = defaultMaxInventory
	} else {
		f.baseReq.CommonBizInfo.MaxInventory = maxInventory
	}
}
