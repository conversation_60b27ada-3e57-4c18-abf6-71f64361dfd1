package before_price_filter

import (
	"context"
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type FilterBusStations struct {
	baseReq *models.BaseReqData
}

func NewFilterBusStations(baseReq *models.BaseReqData) *FilterBusStations {
	return &FilterBusStations{baseReq: baseReq}
}

func (f *FilterBusStations) Exec(ctx context.Context, products []*models.Product) []models.ProductCategory {
	if f.baseReq.CommonBizInfo.BatchShiftInfo == nil {
		return nil
	}
	for _, info := range products {
		if info == nil {
			continue
		}
		if info.BizInfo.StationInventoryInfo == nil {
			continue
		}
		if value, ok := f.baseReq.CommonBizInfo.BatchShiftInfo[info.BizInfo.StationInventoryInfo.SelectInfo.ShiftID]; ok && value != nil {
			tmpStationList := make([]*Prfs.StationInfo, 0, len(info.BizInfo.StationInventoryInfo.StationList))
			for _, v := range value.StationDetails {
				for _, v1 := range info.BizInfo.StationInventoryInfo.StationList {
					if v.StationID == int64(v1.StationId) {
						tmpStationList = append(tmpStationList, v1)
						break
					}
				}
			}
			info.BizInfo.StationInventoryInfo.StationList = tmpStationList
		}
	}
	return nil
}
