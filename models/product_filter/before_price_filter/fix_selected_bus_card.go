package before_price_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type FixSelectedBusCard struct {
	baseReq *models.BaseReqData
}

func NewFixSelectedBusCard(req *models.BaseReqData) *FixSelectedBusCard {
	return &FixSelectedBusCard{
		baseReq: req,
	}
}

func (f *FixSelectedBusCard) Exec(ctx context.Context, products []*models.Product) []models.ProductCategory {
	var (
		toBeRemoved []models.ProductCategory
	)
	f.fixSelectedCardBatchId(ctx, products[0])
	return toBeRemoved
}
func (f *FixSelectedBusCard) fixSelectedCardBatchId(ctx context.Context, product *models.Product) {
	product.BizInfo.BusCardSelectedBatchId = f.baseReq.CommonBizInfo.BusCardSelectedBatchId
	product.BizInfo.BusCardIsShow = true
	//修正传入的卡batchId,若用户没有选择成人则将batchId设置为0
	mode := f.buildMode(ctx, product.BizInfo)
	//1、未知||实名无历史乘车人
	if mode == models.Unknown.ToInt32() || mode == models.RealNameNoHistory.ToInt32() {
		product.BizInfo.BusCardSelectedBatchId = 0
		product.BizInfo.BusCardIsShow = false
		log.Trace.Infof(ctx, "fixSelectedBusCard", "mode match BusCardIsShow is false")
		return
	}
	containAdult := false
	containHomeOwner := false
	//2、非实名制没有选乘客，默勾成人
	if mode == models.NoRealName.ToInt32() && (f.baseReq.CommonBizInfo.PassengerDetailInfo == nil || len(f.baseReq.CommonBizInfo.PassengerDetailInfo.PassengerList) <= 0) {
		containAdult = true
	}
	//3、没有成人或者包含业主票的情况下。ps:卡只能给成人使用；有业主票的时候，不允许使用出行卡
	if product.BizInfo.SeatDetailInfo != nil && len(product.BizInfo.SeatDetailInfo) > 0 {
		for _, v := range product.BizInfo.SeatDetailInfo {
			if v.PassengerCount > 0 && v.PassengerType == seat_selection_consts.Adult.ToInt32() {
				containAdult = true
			}
			if v.PassengerCount > 0 && (v.PassengerType == seat_selection_consts.HomeOwnerAdult.ToInt32() || v.PassengerType == seat_selection_consts.HomeOwnerChildren.ToInt32() ||
				v.PassengerType == seat_selection_consts.HomeOwnerOldMan.ToInt32()) {
				containHomeOwner = true
			}
		}
	}
	if !containAdult || containHomeOwner {
		product.BizInfo.BusCardSelectedBatchId = 0
		product.BizInfo.BusCardIsShow = false
		log.Trace.Infof(ctx, "fixSelectedBusCard", "!containAdult || containHomeOwner BusCardIsShow is false")
	}
}
func (f *FixSelectedBusCard) buildMode(ctx context.Context, bizInfo *models.PrivateBizInfo) int32 {
	if bizInfo == nil || bizInfo.RouteDetailV2 == nil {
		return models.Unknown.ToInt32()
	}
	// 需要实名制
	if bizInfo.RouteDetailV2.IsNeedVerified() {
		if f.baseReq.CommonBizInfo.IdentityPageInfo != nil &&
			len(f.baseReq.CommonBizInfo.IdentityPageInfo.IdentityHistoryInfo) > 0 {
			return models.RealName.ToInt32()
		} else {
			return models.RealNameNoHistory.ToInt32()
		}
	} else {
		return models.NoRealName.ToInt32()
	}
}
