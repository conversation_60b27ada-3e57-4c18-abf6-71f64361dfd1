package fill_product_info

import (
	"context"
	"strconv"
	"time"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/combo_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type IntercityPreferential struct {
	baseReq *models.BaseReqData // 请求信息
}

func NewIntercityPreferential(ctx context.Context, req *models.BaseReqData) *IntercityPreferential {
	return &IntercityPreferential{
		baseReq: req,
	}
}

func (c *IntercityPreferential) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {

	// 需要过实验 把城际实时单改成预约单 用第n个时间片去预估价格和呼返等
	for _, p := range products {
		// 城际预约更便宜需求
		if p.ComboType != combo_type.ComboTypeCarpoolInterCity {
			continue
		}

		params := c.baseReq.GetApolloParam()
		params["combo_id"] = p.RouteID
		if p.BizInfo != nil && p.BizInfo.RouteInfo != nil && p.BizInfo.RouteInfo.RouteGroup != nil {
			params["route_group"] = cast.ToString(*p.BizInfo.RouteInfo.RouteGroup)
		}
		params["product_id"] = cast.ToString(p.ProductID)
		params["product_category"] = cast.ToString(p.ProductCategory)
		status, iTimeInterval := fee_info_render.PreferentialStatus(ctx, strconv.Itoa(int(c.baseReq.PassengerInfo.PID)), params)
		if status && iTimeInterval > 0 {
			c.getTime(p, int64(iTimeInterval))
		}
	}
	return nil, ""
}

func (c *IntercityPreferential) getTime(p *models.Product, iTimeInterval int64) {
	if p.BizInfo != nil && p.BizInfo.RouteInfo != nil && len(p.BizInfo.RouteInfo.TimeSpan) > 0 {
		for _, timeSpan := range p.BizInfo.RouteInfo.TimeSpan {
			if len(timeSpan.Range) > 0 {
				for _, item := range timeSpan.Range {
					timeList := util.StringTIntArray(item.Value)
					if len(timeList) == 2 {
						// 若左时间片 - 当前时间 > 时间间隔，就使用这项改写
						if timeList[0]-time.Now().Unix() > iTimeInterval {
							p.BizInfo.SpecialOrderInfo = &models.SpecialOrderInfo{
								OrderType:      1,
								DepartureRange: timeList,
								DepartureTime:  timeList[1],
							}
							return
						}
					}
				}
			}
		}
	}
}
