package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
)

type GuideAnycarFilterCarpool struct {
	isHitFilterDualPriceCarpool bool
}

func NewGuideAnyCarFilterCarpool(ctx context.Context, multiRequireProduct map[string]dos.RequiredProductStruct) *GuideAnycarFilterCarpool {
	if len(multiRequireProduct) == 0 {
		return nil
	}

	hasTargetCarpool := false
	for _, p := range multiRequireProduct {
		if p.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolFlatRateBySeat {
			hasTargetCarpool = true
			break
		}
	}

	if !hasTargetCarpool {
		return nil
	}

	filter := &GuideAnycarFilterCarpool{
		true,
	}

	return filter
}

func (f *GuideAnycarFilterCarpool) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	if f.isHitFilterDualPriceCarpool {
		return []models.ProductCategory{estimate_pc_id.EstimatePcIdCarpoolStation}, ""
	} else {
		return nil, ""
	}
}
