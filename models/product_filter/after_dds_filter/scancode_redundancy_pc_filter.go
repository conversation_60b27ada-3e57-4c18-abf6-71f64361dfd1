package after_dds_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type ScanCodeRedundancyPCFilter struct {
	baseReq *models.BaseReqData
}

func NewScanCodeRedundancyPCFilter(req *models.BaseReqData) *ScanCodeRedundancyPCFilter {
	return &ScanCodeRedundancyPCFilter{
		baseReq: req,
	}
}

func (sc *ScanCodeRedundancyPCFilter) transplantData(product *models.Product) {
	product.BizInfo.ComboID = sc.baseReq.CommonBizInfo.RouteId
	product.ShiftID = sc.baseReq.CommonBizInfo.BusServiceShiftId
	product.BizInfo.IntercityData.SeatDetailInfo = sc.baseReq.CommonBizInfo.SeatDetailInfo
	product.BizInfo.CarpoolSeatNum = sc.baseReq.CommonBizInfo.CarpoolSeatNum
	if sc.baseReq.CommonBizInfo.RouteDetail.RouteBasicInfo != nil && sc.baseReq.CommonBizInfo.RouteDetail.RouteBasicInfo.RouteSceneType == intercity_estimate_detail.StationToFenceRoute {
		product.RouteType = intercity_estimate_detail.FenceRouteType
	}
}

func (sc *ScanCodeRedundancyPCFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	if sc.baseReq.CommonBizInfo.RouteDetail == nil {
		return nil, ""
	}
	productId, err := strconv.ParseInt(sc.baseReq.CommonBizInfo.RouteDetail.ProductId, 10, 64)
	if err != nil {
		return nil, ""
	}
	var toBeRemoved []models.ProductCategory
	for _, v := range products {
		if v.ProductID != productId {
			toBeRemoved = append(toBeRemoved, models.ProductCategory(v.ProductCategory))
		} else {
			sc.transplantData(v)
		}
	}
	return toBeRemoved, ""
}
