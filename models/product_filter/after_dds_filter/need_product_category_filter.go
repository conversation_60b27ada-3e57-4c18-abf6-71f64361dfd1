package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type NeedProductCategoryFilterCurrentProducts struct {
	needProducts map[int64]bool
}

func NewNeedProductCategoryFilterCurrentProducts(_ context.Context, needProducts []int64) *NeedProductCategoryFilterCurrentProducts {
	if len(needProducts) == 0 {
		return nil
	}

	filter := &NeedProductCategoryFilterCurrentProducts{}
	filter.needProducts = make(map[int64]bool)
	var hasSpecialPrice, hasFastCar bool
	for _, p := range needProducts {
		filter.needProducts[p] = true
		if p == estimate_pc_id.EstimatePcIdSpecialRate || p == estimate_pc_id.EstimatePcIdFastSpecialRate {
			hasSpecialPrice = true
		}

		if p == estimate_pc_id.EstimatePcIdFastCar {
			hasFastCar = true
		}
	}

	//如果只有特惠没有快车，需要补充快车
	if hasSpecialPrice && !hasFastCar {
		filter.needProducts[estimate_pc_id.EstimatePcIdFastCar] = true
	}

	return filter
}

func (f *NeedProductCategoryFilterCurrentProducts) AfterDdsFilter(_ context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var ret = make([]models.ProductCategory, 0)

	for _, p := range products {
		if _, hit := f.needProducts[p.ProductCategory]; !hit {
			ret = append(ret, models.ProductCategory(p.ProductCategory))
		}
	}

	return ret, ""
}
