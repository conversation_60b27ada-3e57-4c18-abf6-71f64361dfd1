package after_dds_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	consts2 "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/level_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/user_type"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
)

type GuideAnycarFilterCurrentProducts struct {
	userType        int32
	currentProducts []models.ProductCategory
}

func NewAnyCarFilterSendProducts(ctx context.Context, multiRequireProduct map[string]dos.RequiredProductStruct, userType int32) *GuideAnycarFilterCurrentProducts {
	if len(multiRequireProduct) == 0 {
		return nil
	}
	filter := &GuideAnycarFilterCurrentProducts{}
	for _, p := range multiRequireProduct {

		estimatePcId := cast.ToInt(p.EstimatePcID)
		if estimatePcId <= 0 {
			estimatePcId = p.ProductCategory
		}

		// 企业用户需要保留快车支持拆费
		if user_type.IsBusinessUser(userType) && estimatePcId == estimate_pc_id.EstimatePcIdFastCar {
			continue
		}
		// 远必省盒子预估依赖 先保留基础品类
		if p.LevelType != level_type.FarMustCheaperLevelType && tripcloud.IsTripcloudProductID(consts2.ProductID(p.ProductId)) {
			continue
		}
		// 泛快内循环,如果已发单品类中包含快车、特快、特惠不做过滤
		if util.ToInt64(p.EstimatePcID) == estimate_pc_id.EstimatePcIdFastCar ||
			util.ToInt64(p.EstimatePcID) == estimate_pc_id.EstimatePcIdFastSpecialRate ||
			util.ToInt64(p.EstimatePcID) == estimate_pc_id.EstimatePcIdAplus ||
			util.ToInt64(p.EstimatePcID) == estimate_pc_id.EstimatePcIdSpecialRate {
			continue
		}
		filter.currentProducts = append(filter.currentProducts, models.ProductCategory(estimatePcId))
	}
	return filter
}

func (f *GuideAnycarFilterCurrentProducts) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	if len(f.currentProducts) > 0 {
		return f.currentProducts, consts.FilterSendProduct
	}
	return f.currentProducts, ""
}
