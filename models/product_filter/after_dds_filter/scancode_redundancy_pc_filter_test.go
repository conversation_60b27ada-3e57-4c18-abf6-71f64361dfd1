package after_dds_filter

import (
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	. "github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestTransplantData(t *testing.T) {
	filter := &ScanCodeRedundancyPCFilter{
		baseReq: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				RouteDetail: &Prfs.RouteDetailData{
					RouteBasicInfo: &Prfs.RouteBasicInfo{
						RouteSceneType: 1,
					},
				},
			},
		},
	}
	product := &models.Product{
		BizInfo: &models.PrivateBizInfo{},
	}
	Convey("1", t, func() {
		filter.transplantData(product)
		So(product.RouteType, ShouldEqual, 7)
	})
}
