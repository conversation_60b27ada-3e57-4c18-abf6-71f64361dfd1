package after_dds_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

type PetFilterCurrentProducts struct {
	userType        int32
	currentProducts []models.ProductCategory
}

func NewPetFilterSendProducts(ctx context.Context, multiRequireProduct map[string]dos.RequiredProductStruct) *PetFilterCurrentProducts {
	if len(multiRequireProduct) == 0 {
		return nil
	}
	filter := &PetFilterCurrentProducts{}
	for _, p := range multiRequireProduct {
		estimatePcId := cast.ToInt(p.EstimatePcID)
		if estimatePcId <= 0 {
			estimatePcId = p.ProductCategory
		}
		filter.currentProducts = append(filter.currentProducts, models.ProductCategory(estimatePcId))
	}
	return filter
}

func (f *PetFilterCurrentProducts) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	if len(f.currentProducts) > 0 {
		return f.currentProducts, consts.FilterSendProduct
	}
	return f.currentProducts, ""
}
