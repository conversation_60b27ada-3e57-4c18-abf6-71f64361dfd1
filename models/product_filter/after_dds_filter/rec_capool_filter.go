package after_dds_filter

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type RecCarpoolFilter struct {
	existProducts   map[models.ProductCategory]struct{}
	removedProducts []models.ProductCategory
}

func NewRecCarpoolFilter(ctx context.Context, baseReqData *models.BaseReqData) *RecCarpoolFilter {
	apolloParam := ApolloModel.NewUser(strconv.FormatInt(baseReqData.PassengerInfo.PID, 10)).
		With("city", strconv.FormatInt(int64(baseReqData.AreaInfo.City), 10)).
		With("phone", baseReqData.PassengerInfo.Phone).
		With("access_key_id", strconv.FormatInt(int64(baseReqData.CommonInfo.AccessKeyID), 10)).
		With("county_id", strconv.FormatInt(int64(baseReqData.AreaInfo.FromCounty), 10)).
		With("app_version", baseReqData.CommonInfo.AppVersion)
	toggle, err := ApolloSDK.FeatureToggle("gs_cap_new_opencity_customization", apolloParam)
	if err != nil || toggle == nil || !toggle.IsAllow() {
		log.Trace.Warnf(ctx, consts.TagApolloLoadErr, "fail to check gs_cap_new_opencity_customization with err %v, and toggle %v", err, toggle)
		return nil
	}
	f := &RecCarpoolFilter{
		existProducts: make(map[models.ProductCategory]struct{}),
	}
	if assign := toggle.GetAssignment(); assign != nil {
		f.existProducts[models.ProductCategory(util.IntParseWithDefault(assign.GetParameter("carpool_pc", "3"), 3))] = struct{}{}
		for _, v := range int64ToPC(util.ListIntParseByJson(assign.GetParameter("nocarpool_pc", "[1]"))) {
			f.existProducts[v] = struct{}{}
		}
	}
	return f
}

func (f *RecCarpoolFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	f.removedProducts = []models.ProductCategory{}
	for _, p := range products {
		if _, b := f.existProducts[models.ProductCategory(p.ProductCategory)]; !b {
			f.removedProducts = append(f.removedProducts, models.ProductCategory(p.ProductCategory))
		}
	}
	return f.removedProducts, ""
}

func int64ToPC(list []int64) []models.ProductCategory {
	res := []models.ProductCategory{}
	for _, v := range list {
		if v != 0 {
			res = append(res, models.ProductCategory(v))
		}
	}
	return res
}
