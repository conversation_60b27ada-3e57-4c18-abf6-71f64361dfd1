package after_dds_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type StationBusInsteadOrderFilter struct {
	productId int64
}

func NewStationBusInsteadOrderFilter(productId int64) *StationBusInsteadOrderFilter {
	return &StationBusInsteadOrderFilter{productId}
}
func (r *StationBusInsteadOrderFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var allRemoved = make([]models.ProductCategory, 0)
	for _, p := range products {
		if p.GetProductId() != r.productId {
			allRemoved = append(allRemoved, models.ProductCategory(p.ProductCategory))
		}
	}

	return allRemoved, ""
}
