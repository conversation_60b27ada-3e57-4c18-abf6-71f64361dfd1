package after_dds_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car/charter_open_products"
	trace "git.xiaojukeji.com/lego/context-go"
)

type CharterCityOpenPcFilter struct {
	charterOpenProduct *charter_open_products.CharterOpenProductHandler
	commonBizInfo      *models.CommonBizInfo
	comboId            int
	city               int
	sourceId           int
}

func NewCharterCityOpenPcFilter(ctx context.Context, pg *biz_runtime.ProductsGenerator) *CharterCityOpenPcFilter {
	ret := &CharterCityOpenPcFilter{
		commonBizInfo: &pg.BaseReqData.CommonBizInfo,
		comboId:       util.ToInt(pg.BaseReqData.CommonBizInfo.ComboId),
		city:          util.ToInt(pg.BaseReqData.AreaInfo.Area),
		sourceId:      util.ToInt(pg.BaseReqData.CommonInfo.SourceID),
	}

	return ret
}

func (f *CharterCityOpenPcFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	toRemove := make([]models.ProductCategory, 0, len(products))

	if f.commonBizInfo.CharterOpenProduct == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "CharterCityOpenPcFilter charterOpenProduct nil")
		return toRemove, ""
	}

	f.charterOpenProduct = f.commonBizInfo.CharterOpenProduct

	openPcIds, err := f.charterOpenProduct.GetOpenPcIds(f.city, f.sourceId)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "CharterCityOpenPcFilter GetOpenPcIds failed. err=%s, city=%d, sourceId=%d", err.Error(), f.city, f.sourceId)
	}

	pcIdSet := map[int]struct{}{}
	for _, pcId := range openPcIds {
		pcIdSet[pcId] = struct{}{}
	}

	for _, product := range products {
		if product == nil {
			continue
		}

		_, hit := pcIdSet[int(product.ProductCategory)]
		if !hit {
			toRemove = append(toRemove, models.ProductCategory(product.ProductCategory))
		}

		if product.BizInfo == nil {
			product.BizInfo = &models.PrivateBizInfo{}
		}

		if f.comboId > 0 {
			product.BizInfo.ComboID = int64(f.comboId)
		}

		product.SceneType = consts.SceneTypeCharter
	}

	return toRemove, ""
}
