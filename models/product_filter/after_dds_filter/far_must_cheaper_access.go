package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const (
	V3FORM              = "pAnycarEstimateV3"
	V4FORM              = "pAnycarEstimateV4"
	FarMustCheapGroupId = 6
)

type FarMustCheaperAccess struct {
	access bool
}

func NewFarMustCheaperAccess(ctx context.Context, reqFrom string, reqData *models.BaseReqData) *FarMustCheaperAccess {
	return &FarMustCheaperAccess{access: reqFrom == V3FORM || reqFrom == V4FORM}
}

func (f *FarMustCheaperAccess) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var toBeRemoved []models.ProductCategory
	if f.access {
		return toBeRemoved, ""
	} else {
		for _, p := range products {
			if p.LevelType == 105 {
				toBeRemoved = append(toBeRemoved, models.ProductCategory(p.ProductCategory))
			}
		}
		return toBeRemoved, ""
	}
}
