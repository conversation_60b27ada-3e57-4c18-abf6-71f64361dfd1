package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type PbdAnycarFilterCurrentProducts struct {
	currentProducts []models.ProductCategory
}

func NewPbdAnyCarFilterCurrentProducts(ctx context.Context, multiProductCategory []ufs.OneMRP) *PbdAnycarFilterCurrentProducts {
	if len(multiProductCategory) == 0 {
		return nil
	}
	filter := &PbdAnycarFilterCurrentProducts{}
	for _, p := range multiProductCategory {
		filter.currentProducts = append(filter.currentProducts, models.ProductCategory(p.ProductCategory))
	}
	return filter
}

func (f *PbdAnycarFilterCurrentProducts) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	return f.currentProducts, ""
}
