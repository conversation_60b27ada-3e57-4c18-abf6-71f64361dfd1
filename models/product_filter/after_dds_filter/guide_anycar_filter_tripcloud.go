package after_dds_filter

import (
	"context"
	"encoding/json"
	"strconv"

	ufsThrift "git.xiaojukeji.com/dirpc/dirpc-go-thrift-UFS"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	mambaConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/logic/prepay_interoperation_product"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	trace "git.xiaojukeji.com/lego/context-go"
)

const UFS_KEY = "tripcloud.is_used"
const DOMAIN = "passenger"

const ApolloNsDiscountProducts = "tripcloud_config"
const ApolloFNameDiscountProducts = "tripcloud_discounts_conf"
const ApolloKeyDiscountProducts = "product_category"

type GuideAnycarFilterTripCloud struct {
	passenger_id        string
	extraType           string
	multiRequireProduct map[string]dos.RequiredProductStruct
	isAddFilter         bool
	OrderInfo           *dos.OrderInfo
}

func NewGuideAnyCarFilterTripCloud(pid string, extraType string, multiRequireProduct map[string]dos.RequiredProductStruct, isFilter bool, orderInfo *dos.OrderInfo) *GuideAnycarFilterTripCloud {
	return &GuideAnycarFilterTripCloud{passenger_id: pid, extraType: extraType, multiRequireProduct: multiRequireProduct, isAddFilter: isFilter, OrderInfo: orderInfo}
}

// 预付/限制特惠联盟车型/过滤三方未授权车型
func (f *GuideAnycarFilterTripCloud) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var (
		features     = make([]ufsClient.Feature, 0)
		allTripCloud = make([]models.ProductCategory, 0)
		allRemoved   = make([]models.ProductCategory, 0)
		searchMap    = make(map[int64][]models.ProductCategory, 0)
		resp         map[string]*ufsThrift.FeatureResponse
		err          error
	)

	//筛选出所有的三方车型, 及UFS的search map
	for _, p := range products {
		if p.IsTripcloudProduct(ctx) {
			allTripCloud = append(allTripCloud, models.ProductCategory(p.ProductCategory))
			searchMap[p.BusinessID] = append(searchMap[p.BusinessID], models.ProductCategory(p.ProductCategory))
		}
	}

	intExtraType, _ := strconv.ParseUint(f.extraType, 10, 64)
	if order.IsPrepayOrder(intExtraType) {
		if f.OrderInfo != nil {
			//预付单 根据三方预付互通推进阶段做不同过滤
			prepayProductId := prepay_interoperation_product.GetPrePayProductIDFromUfs(ctx, f.OrderInfo.OrderId)
			allRemoved = prepay_interoperation_product.GetUnbroadCastAbleProductLists(ctx, f.OrderInfo, products, prepayProductId, allTripCloud)
		} else { //订单为空 异常情况老逻辑处理
			allRemoved = allTripCloud
			goto ReturnValue
		}
	}

	//未授权过滤
	if !f.isAddFilter {
		goto ReturnValue
	}

	//多车型呼叫限制
	//if len(f.multiRequireProduct) > 1 {
	//	//过滤非特惠联盟的车型
	//	allDiscountProducts := getDiscountProductCategory(ctx)
	//	if len(allDiscountProducts) > 0 {
	//		bCheckPass := true
	//		for _, product := range f.multiRequireProduct {
	//			if tripcloud.IsTripcloudProductID(consts.ProductID(product.ProductId)) && !allDiscountProducts[models.ProductCategory(product.ProductCategory)] {
	//				bCheckPass = false
	//				break
	//			}
	//		}
	//		if !bCheckPass {
	//			allRemoved = allTripCloud
	//			goto ReturnValue
	//		}
	//
	//		for _, pcID := range allTripCloud {
	//			if !allDiscountProducts[pcID] {
	//				allRemoved = append(allRemoved, pcID)
	//			}
	//		}
	//	}
	//}

	for businessID := range searchMap {
		//构建UFS feature 列表
		feature := ufsClient.Feature{
			Domain: DOMAIN,
			Keys:   []string{UFS_KEY},
			Params: map[string]string{
				"passenger_id": f.passenger_id,
				"business_id":  strconv.FormatInt(businessID, 10),
			},
		}
		features = append(features, feature)
	}

	//若获取三方是否使用过 状态失败，则remove所有三方车型，
	resp, err = ufs.MultiGetFeatures(ctx, features, "")
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get trip cloud is used fail with err %v", err)
		allRemoved = allTripCloud
		goto ReturnValue
	}

	for strFeature, ufsResp := range resp {
		_, _, params, err := ufsClient.ParseFeature(strFeature)
		if err != nil {
			allRemoved = allTripCloud
			goto ReturnValue
		}

		businessID, err := strconv.ParseInt(params["business_id"], 10, 64)
		if err != nil || businessID == 0 {
			allRemoved = allTripCloud
			goto ReturnValue
		}
		if ufsResp == nil || ufsResp.Errno != 0 || *ufsResp.Value != "1" {
			allRemoved = append(allRemoved, searchMap[businessID]...)
		}
	}

ReturnValue:
	log.Trace.Infof(ctx, trace.DLTagUndefined, "tc removed not used product %v", allRemoved)
	return allRemoved, consts2.FilterThirdParty
}

func getDiscountProductCategory(ctx context.Context) map[models.ProductCategory]bool {
	var (
		allDiscountProducts    = make([]int64, 0)
		allDiscountProductsMap = make(map[models.ProductCategory]bool)
	)

	config := apollo.GetConfig(ctx, ApolloNsDiscountProducts, ApolloFNameDiscountProducts)
	if configStr, exist := config[ApolloKeyDiscountProducts]; exist && configStr != "" {
		err := json.Unmarshal([]byte(configStr), &allDiscountProducts)
		if err != nil {
			log.Trace.Warnf(ctx, mambaConsts.TagErrJsonUnMarshal, "json %s fail with err %v", configStr, err)
		}
		for _, p := range allDiscountProducts {
			allDiscountProductsMap[models.ProductCategory(p)] = true
		}
	}
	return allDiscountProductsMap
}
