package after_dds_filter

import (
	"context"
	"github.com/spf13/cast"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type TaxiPricingFilter struct {
	userType        int32
	currentProducts []models.ProductCategory
}

func NewTaxiPricingFilter(ctx context.Context, baseReqData *models.BaseReqData,
	sendCarList map[string]dos.RequiredProductStruct, version string) *TaxiPricingFilter {
	if len(sendCarList) == 0 {
		return nil
	}

	// 如果阿波罗通过才走这个filter
	initApolloParams(ctx, baseReqData, version)
	if baseReqData.CommonInfo.PricingBoxData == nil {
		return nil
	}

	taxiBox := []int{estimate_pc_id.EstimatePcIdUnione, estimate_pc_id.EstimatePcIdTaxiMarketisationPutong, estimate_pc_id.EstimatePcIdFastTaxi}
	taxiBox2 := []models.ProductCategory{
		models.ProductCategory(estimate_pc_id.EstimatePcIdUnione),
		models.ProductCategory(estimate_pc_id.EstimatePcIdTaxiMarketisationPutong),
		models.ProductCategory(estimate_pc_id.EstimatePcIdFastTaxi),
	}
	filter := &TaxiPricingFilter{}
	isSendTaxi := false
	for _, p := range sendCarList {
		estimatePcId := cast.ToInt(p.EstimatePcID)
		if estimatePcId <= 0 {
			estimatePcId = p.ProductCategory
		}
		// 如果发了任意出租车 均过滤剩余出租车
		if util.InArrayInt(estimatePcId, taxiBox) {
			filter.currentProducts = append(filter.currentProducts, taxiBox2...)
			isSendTaxi = true
			break
		}
	}

	// 没发出租车订单
	if !isSendTaxi {
		// v1:未发版情况 追加出租车只展示1个品类，追加打表计价,展示"出租车"
		if "v1" == version {
			filter.currentProducts = append(filter.currentProducts, models.ProductCategory(estimate_pc_id.EstimatePcIdTaxiMarketisationPutong))
		}

		// v3:均追加 不过滤

	}

	return filter
}

func NewTaxiPricingEstimateFilter(ctx context.Context, baseReqData *models.BaseReqData) *TaxiPricingFilter {
	// 如果阿波罗通过才走这个filter
	initApolloParams(ctx, baseReqData, "")
	return nil
}

func initApolloParams(ctx context.Context, baseReqData *models.BaseReqData, version string) {
	apolloParam := ApolloModel.NewUser(strconv.FormatInt(baseReqData.PassengerInfo.UID, 10)).
		With("pid", strconv.FormatInt(baseReqData.PassengerInfo.PID, 10)).
		With("city", strconv.FormatInt(int64(baseReqData.AreaInfo.Area), 10)).
		With("phone", baseReqData.PassengerInfo.Phone).
		With("access_key_id", strconv.FormatInt(int64(baseReqData.CommonInfo.AccessKeyID), 10)).
		With("county_id", strconv.FormatInt(int64(baseReqData.AreaInfo.FromCounty), 10)).
		With("lang", baseReqData.CommonInfo.Lang).
		With("app_version", baseReqData.CommonInfo.AppVersion)
	pricingBoxData, disableUnione := taxi.Init(ctx, apolloParam, version)
	baseReqData.CommonInfo.DisableUnione = disableUnione
	baseReqData.CommonInfo.PricingBoxData = pricingBoxData
}

func (f *TaxiPricingFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	return f.currentProducts, ""
}
