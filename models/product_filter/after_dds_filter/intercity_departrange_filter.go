package after_dds_filter

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const (
	MINUTE     = 60
	LIMIT      = 15
	VALID_TIME = LIMIT * MINUTE
)

type IntercityDepartureRangeFilter struct {
	CommonInfo *models.CommonInfo
	model      Deal
}

func NewIntercityDepartureRangeFilter(ctx context.Context, commonInfo *models.CommonInfo) *IntercityDepartureRangeFilter {
	filter := &IntercityDepartureRangeFilter{
		CommonInfo: commonInfo,
	}
	filter.initModel(commonInfo)
	return filter
}

type Deal interface {
	dealDepartureRange(ctx context.Context, commonInfo *models.CommonInfo, products []*models.Product) ([]models.ProductCategory, string)
}

type Pbd struct{}

type Sfc struct{}

func (p *Pbd) dealDepartureRange(ctx context.Context, commonInfo *models.CommonInfo, products []*models.Product) ([]models.ProductCategory, string) {
	ret := []models.ProductCategory{}
	for _, product := range products {
		if product.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolInter {
			formatDepartureRange(commonInfo, product)
			// 如果出发时间不在timespan营运时间内，需要过滤
			if !checkDepartureRangeInTimeSpan(ctx, commonInfo, product.BizInfo) {
				ret = append(ret, models.ProductCategory(product.ProductCategory))
			}
		}
	}
	return ret, ""
}

func (p *Sfc) dealDepartureRange(ctx context.Context, commonInfo *models.CommonInfo, products []*models.Product) ([]models.ProductCategory, string) {
	var ret []models.ProductCategory
	for _, product := range products {
		if product.CarpoolType == consts.CarPoolTypeInterCity {
			formatDepartureRange(commonInfo, product)
			// 如果出发时间不在timespan营运时间内，需要过滤
			if !p.checkDepartureRangeInTimeSpan(ctx, commonInfo, product.BizInfo) {
				ret = append(ret, models.ProductCategory(product.ProductCategory))
			}
		}
	}
	return ret, ""
}

func (f *IntercityDepartureRangeFilter) initModel(commonInfo *models.CommonInfo) {
	if commonInfo.PageType == page_type.PageTypeSFCEstimate {
		f.model = &Sfc{}
	} else {
		f.model = &Pbd{}
	}
}

func (f *IntercityDepartureRangeFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	return f.model.dealDepartureRange(ctx, f.CommonInfo, products)
}

func formatDepartureRange(commonInfo *models.CommonInfo, product *models.Product) {
	departureRange := genDepartureRange(commonInfo)
	if product.BizInfo != nil && len(departureRange) > 1 {
		product.BizInfo.DepartureTime = departureRange[1]
		product.BizInfo.DepartureRange = departureRange
	}
}

func genDepartureRange(commonInfo *models.CommonInfo) []int64 {
	var (
		departureRange []int64
		currentTime    = time.Now().Unix()
	)
	if len(commonInfo.DepartureRange) > 0 {
		if checkDepartureRangeValid(commonInfo.DepartureRange) {
			departureRange = commonInfo.DepartureRange
		} else {
			departureRange = initDepartureRange(commonInfo.DepartureRange[0])
		}
	} else {
		if currentTime > commonInfo.DepartureTime {
			departureRange = initDepartureRange(currentTime)
		} else {
			departureRange = initDepartureRange(commonInfo.DepartureTime)
		}
	}
	return departureRange
}

func checkDepartureRangeValid(departureRange []int64) bool {
	return len(departureRange) == 2 && departureRange[0]+VALID_TIME <= departureRange[1]
}

func initDepartureRange(departureTime int64) []int64 {
	return []int64{departureTime, departureTime + VALID_TIME}
}

func checkDepartureRangeInTimeSpan(ctx context.Context, commonInfo *models.CommonInfo, bizInfo *models.PrivateBizInfo) bool {
	params := map[string]string{
		"access_key_id": strconv.FormatInt(int64(commonInfo.AccessKeyID), 10),
		"channel":       strconv.FormatInt(commonInfo.Channel, 10),
	}
	// 不通过此apollo,即代表 不执行 该渠道的departure_time 是否需要在timespan内的检查逻辑，返回合法值true
	if !apollo.FeatureToggle(ctx, "gs_pbd_intercity_carpool_timespan_filter", "", params) {
		return true
	}

	if bizInfo == nil || len(bizInfo.TimeSpan) == 0 {
		return false
	}
	// 获取dds第一个可用城际时间片
	firstTimeSpanStr := ""
	if len(bizInfo.TimeSpan) > 0 && len(bizInfo.TimeSpan[0].Range) > 0 {
		firstTimeSpanStr = bizInfo.TimeSpan[0].Range[0].Value
	} else if len(bizInfo.TimeSpan) > 1 && len(bizInfo.TimeSpan[1].Range) > 0 {
		//  当天23点后可能没有时间片，需要取明天的第一个时间片
		firstTimeSpanStr = bizInfo.TimeSpan[1].Range[0].Value
	}
	firstTimeSpan := util.StringTIntArray(firstTimeSpanStr)
	if len(firstTimeSpan) != 2 {
		return false
	}

	stack := [][]int64{firstTimeSpan}

	// leetCode 合并区间？
	// 每天的span
	for _, timeSpanEachDay := range bizInfo.TimeSpan {
		if timeSpanEachDay.Range == nil || len(timeSpanEachDay.Range) == 0 {
			continue
		}

		stack = processTimeSpanEachDay(timeSpanEachDay, stack)
	}

	// 确认departureRange 是否在合并之后的区间内
	for _, validStackSpan := range stack {
		// 是否是子区间
		if bizInfo != nil && len(bizInfo.DepartureRange) > 1 && bizInfo.DepartureRange[0] >= validStackSpan[0] && bizInfo.DepartureRange[1] <= validStackSpan[1] {
			return true
		}
	}

	return false

}

func (p *Sfc) checkDepartureRangeInTimeSpan(ctx context.Context, commonInfo *models.CommonInfo, bizInfo *models.PrivateBizInfo) bool {
	if bizInfo == nil || len(bizInfo.TimeSpan) == 0 {
		return false
	}
	// 获取dds第一个可用城际时间片
	firstTimeSpanStr := ""
	if len(bizInfo.TimeSpan) > 0 && len(bizInfo.TimeSpan[0].Range) > 0 {
		firstTimeSpanStr = bizInfo.TimeSpan[0].Range[0].Value
	} else if len(bizInfo.TimeSpan) > 1 && len(bizInfo.TimeSpan[1].Range) > 0 {
		//  当天23点后可能没有时间片，需要取明天的第一个时间片
		firstTimeSpanStr = bizInfo.TimeSpan[1].Range[0].Value
	}
	firstTimeSpan := util.StringTIntArray(firstTimeSpanStr)
	if len(firstTimeSpan) != 2 {
		return false
	}

	stack := [][]int64{firstTimeSpan}

	// leetCode 合并区间？
	// 每天的span
	for _, timeSpanEachDay := range bizInfo.TimeSpan {
		if timeSpanEachDay.Range == nil || len(timeSpanEachDay.Range) == 0 {
			continue
		}

		stack = processTimeSpanEachDay(timeSpanEachDay, stack)
	}

	// 确认departureRange 是否在合并之后的区间内
	for _, validStackSpan := range stack {
		// 是否是子区间
		if commonInfo.DepartureRange[0] >= validStackSpan[0] && commonInfo.DepartureRange[1] <= validStackSpan[1] {
			return true
		}
	}

	return false
}

func processTimeSpanEachDay(timeSpanEachDay *EstimateDecision.TimeSpanV2, stack [][]int64) [][]int64 {

	for _, item := range timeSpanEachDay.Range {
		departureRange := item.Value
		if departureRange != "" {
			var currentRange []int64
			err := json.Unmarshal([]byte(departureRange), &currentRange)
			if err != nil || len(currentRange) != 2 {
				continue
			}

			// 数据合法，执行合并区间操作。
			//departureRange = currentRange[1]
			stackTopItem := stack[len(stack)-1]
			if stackTopItem[0] <= currentRange[0] && stackTopItem[1] >= currentRange[0] {
				stackTopItem[1] = util.MaxInt64(stackTopItem[1], currentRange[1])
				stack[len(stack)-1] = stackTopItem
			} else {
				stack = append(stack, currentRange)
			}

		}
	}
	return stack
}
