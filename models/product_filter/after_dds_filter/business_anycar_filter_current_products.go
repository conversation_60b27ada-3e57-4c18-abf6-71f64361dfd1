package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/user_type"
)

type BusinessAnycarFilterCurrentProducts struct {
	userType        int32
	currentProducts []models.ProductCategory
}

func NewBusinessAnyCarFilterCurrentProducts(ctx context.Context, multiProductCategory []ufs.OneMRP, userType int32) *BusinessAnycarFilterCurrentProducts {

	if len(multiProductCategory) == 0 {
		return nil
	}
	filter := &BusinessAnycarFilterCurrentProducts{}
	for _, p := range multiProductCategory {
		// 企业用户需要保留快车支持拆费
		if user_type.IsBusinessUser(userType) && p.ProductCategory == estimate_pc_id.EstimatePcIdBusinessFastCar {
			continue
		}
		filter.currentProducts = append(filter.currentProducts, models.ProductCategory(p.ProductCategory))
	}
	return filter
}

func (f *BusinessAnycarFilterCurrentProducts) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	return f.currentProducts, ""
}
