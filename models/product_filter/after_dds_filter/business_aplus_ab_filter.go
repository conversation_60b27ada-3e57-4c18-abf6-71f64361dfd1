package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type BusinessAPlusAbFilter struct {
	remove bool
}

func NewBusinessAPlusAbFilter(ctx context.Context, extraType string, reqData *models.BaseReqData) *BusinessAPlusAbFilter {
	filter := &BusinessAPlusAbFilter{remove: false}
	if reqData == nil {
		return filter
	}
	// 不跨城
	if reqData.AreaInfo.Area != reqData.AreaInfo.ToArea {
		filter.remove = true
		return filter
	}

	return filter
}

func (f *BusinessAPlusAbFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	if f.remove {
		return []models.ProductCategory{estimate_pc_id.EstimatePcIdBusinessAPlusCar}, ""
	} else {
		return nil, ""
	}
}
