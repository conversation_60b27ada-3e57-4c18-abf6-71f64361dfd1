// Package after_dds_filter
package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// SFCCarpoolPassengerFilter carpoolFilter
type SFCCarpoolPassengerFilter struct{}

// NewSFCCarpoolPassengerFilter 乘车人数等于最大座位数时，屏蔽拼车品类
func NewSFCCarpoolPassengerFilter(ctx context.Context, reqData *models.BaseReqData, dcmpData proto.SFCEstimateDcmp) *SFCCarpoolPassengerFilter {
	var (
		//最大座位数
		maxSeatNum int32
	)
	maxSeatNum = dcmpData.SeatInfo.MaxNum

	if reqData.CommonBizInfo.CarpoolSeatNum == maxSeatNum {
		// log.Trace.Warnf(ctx, "sfc", "filter maxSeatNum=%+v", maxSeatNum)
		return &SFCCarpoolPassengerFilter{}
	}

	return nil
}

// Do 执行方法
func (f *SFCCarpoolPassengerFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var allRemoved = make([]models.ProductCategory, 0)
	for _, p := range products {
		if p.CarpoolType == consts.CarPoolTypeSFCar || p.CarpoolType == consts.CarPoolTypeSFCarCrossCity {
			allRemoved = append(allRemoved, models.ProductCategory(p.ProductCategory))
		}
	}

	return allRemoved, ""
}
