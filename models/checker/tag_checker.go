package checker

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/tag_service"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/nuwa/trace"
)

type TagChecker struct {
	bigDataTag []string
}

func (c *TagChecker) SetRule(ctx context.Context, input interface{}) bool {
	bigDataTag, ok := input.([]string)
	if !ok {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "tag SetRule false")
		return false
	}
	c.bigDataTag = bigDataTag
	return true
}

func (c *TagChecker) CheckRuleValid(params *Params) bool {
	return true
}

func (c *TagChecker) Do(ctx context.Context, params *Params) (bool, int) {
	// 如果没有配置人群标签，默认通过
	if len(c.bigDataTag) == 0 {
		return true, SuccessChecker
	}

	// 判断命中的人群标签
	hitTags, err := tag_service.GetHitTags(ctx, util.ToString(params.Pid), c.bigDataTag)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "request tag_service fail||pid=%d||big_data_tag=%v||err=%v",
			params.Pid, c.bigDataTag, err)
		return false, BigDataTagFail
	}

	if hitTags == nil || len(hitTags) == 0 {
		return false, BigDataTagFail
	}

	if len(params.BigDataTag) == 0 {
		return false, BigDataTagFail
	}

	return true, SuccessChecker
}
