package after_dds_fission

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
)

type RecCarpoolFission struct {
	goalPcIDs map[models.ProductCategory]struct{}
}

func NewRecCarpoolFission(ctx context.Context, baseReqData *models.BaseReqData) *RecCarpoolFission {
	if baseReqData == nil {
		return nil
	}
	rcFission := &RecCarpoolFission{
		goalPcIDs: make(map[models.ProductCategory]struct{}),
	}
	rcFission.goalPcIDs[estimate_pc_id.EstimatePcIdCarpoolStation] = struct{}{}
	rcFission.goalPcIDs[estimate_pc_id.EstimatePcIdCarpoolFlatRateBySeat] = struct{}{}
	return rcFission
}

func (f *RecCarpoolFission) Do(ctx context.Context, products []*models.Product) []*models.Product {
	var res []*models.Product
	for _, product := range products {
		if product == nil {
			continue
		}
		if _, b := f.goalPcIDs[models.ProductCategory(product.ProductCategory)]; !b {
			continue
		}
		if p := buildNewProduct(ctx, product, 2); p != nil {
			res = append(res, p)
		}
	}
	return res
}

func buildNewProduct(ctx context.Context, products *models.Product, seatNumByFission int) *models.Product {
	res := &models.Product{
		ProductCategory:       products.ProductCategory,
		OrderType:             products.OrderType,
		ProductID:             products.ProductID,
		BusinessID:            products.BusinessID,
		RequireLevelInt:       products.RequireLevelInt,
		RequireLevel:          products.RequireLevel,
		CarpoolType:           products.CarpoolType,
		ComboType:             products.ComboType,
		IsSpecialPrice:        products.IsSpecialPrice,
		CarpoolPriceType:      products.CarpoolPriceType,
		IsDualCarpoolPrice:    products.IsDualCarpoolPrice,
		AirportType:           products.AirportType,
		RailwayType:           products.RailwayType,
		HotelType:             products.HotelType,
		ExamType:              products.ExamType,
		StationServiceControl: products.StationServiceControl,
		LevelType:             products.LevelType,
		RouteType:             products.RouteType,
		LongRentType:          products.LongRentType,
		OType:                 products.OType,
		BizInfo:               GetNewBizInfo(products.BizInfo, seatNumByFission),
	}
	res.BuildEstimateID(ctx, nil, nil)
	return res
}

func GetNewBizInfo(bizInfo *models.PrivateBizInfo, seatNumByFission int) *models.PrivateBizInfo {
	res := &models.PrivateBizInfo{
		CarpoolSeatNum:     int32(seatNumByFission),
		ComboID:            bizInfo.ComboID,
		MaxCarpoolSeatNum:  bizInfo.MaxCarpoolSeatNum,
		TimeSpan:           bizInfo.TimeSpan,
		DepartureRange:     bizInfo.DepartureRange,
		MatchRoutes:        bizInfo.MatchRoutes,
		CarpoolRouteInfo:   bizInfo.CarpoolRouteInfo,
		CarpoolCommuteCard: bizInfo.CarpoolCommuteCard,
	}
	return res
}
