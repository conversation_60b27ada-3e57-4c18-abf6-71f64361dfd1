package after_dds_fission

import (
	"context"
	"encoding/json"
	"fmt"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type CarpoolLowPriceFission struct{
	DefaultNow bool
}

func NewCarpoolLowPriceFission(ctx context.Context, baseReqData *models.BaseReqData) *CarpoolLowPriceFission {
	if baseReqData == nil {
		return nil
	}

	if baseReqData.CommonBizInfo.PinCheCheFormInfo.FormSytle == 1 {
		return &CarpoolLowPriceFission{
			DefaultNow: baseReqData.CommonBizInfo.PinCheCheFormInfo.DefaultOrderType == 0,
		}
	}

	return nil
}

func (f *CarpoolLowPriceFission) Do(ctx context.Context, products []*models.Product) []*models.Product {
	var res []*models.Product
	for _, product := range products {
		if product == nil {
			continue
		}

		if product.ProductCategory == estimate_pc_id.EstimatePcIdLowPriceCarpool {
			if p := buildPinchecheFissionProduct(ctx, product, f.DefaultNow); p != nil {
				res = append(res, p)
				break
			}
		}
	}

	return res
}

func buildPinchecheFissionProduct(ctx context.Context, products *models.Product, defaultNow bool) *models.Product {
	if products.BizInfo == nil {
		return nil
	}

	res := &models.Product{
		ProductCategory:       products.ProductCategory,
		ProductID:             products.ProductID,
		BusinessID:            products.BusinessID,
		RequireLevelInt:       products.RequireLevelInt,
		RequireLevel:          products.RequireLevel,
		CarpoolType:           products.CarpoolType,
		ComboType:             products.ComboType,
		IsSpecialPrice:        products.IsSpecialPrice,
		CarpoolPriceType:      products.CarpoolPriceType,
		IsDualCarpoolPrice:    products.IsDualCarpoolPrice,
		AirportType:           products.AirportType,
		RailwayType:           products.RailwayType,
		HotelType:             products.HotelType,
		ExamType:              products.ExamType,
		StationServiceControl: products.StationServiceControl,
		LevelType:             products.LevelType,
		RouteType:             products.RouteType,
		LongRentType:          products.LongRentType,
		OType:                 products.OType,
	}
	res.BuildEstimateID(ctx, nil, nil)

	// 获取第一个预约单时间片
	var firstBookingRange []int64
	timeSpans, _ := ConvertTimeSpan(products.BizInfo.TimeSpan, 3, 0)
	for _, timeSpan := range timeSpans[1:] {
		if timeSpan == nil {
			continue
		}
		if timeRange := util.StringTIntArray(timeSpan.Value); len(timeRange) == 2 {
			firstBookingRange = timeRange
			break
		}
	}

	// 如果请求的时间片不合法或已过期, 则重置为【现在出发】
	if len(products.BizInfo.DepartureRange) == 2 && products.BizInfo.DepartureRange[1] <= firstBookingRange[0] && products.OrderType == 1 || len(products.BizInfo.DepartureRange) != 2 {
		products.BizInfo.DepartureRange = nil
		products.BizInfo.DepartureTime = 0
		products.OrderType = 0
	}

	// 如果当前请求品类有时间片且落在预约出发区间 将当前请求品类置为【预约出发】
	if len(products.BizInfo.DepartureRange) == 2 && products.BizInfo.DepartureRange[1] > firstBookingRange[0] {
		products.BizInfo.DepartureTime = products.BizInfo.DepartureRange[1]
		products.OrderType = 1
	}

	// 如果当前请求品类是【预约出发】时间，构造现在出发
	if products.OrderType == 1 && len(products.BizInfo.DepartureRange) == 2 {
		res.OrderType = 0
		res.BizInfo = generateNewBizInfo(products.BizInfo, nil)
	} else {
		// 如果当前请求品类是【现在出发】时间，构造预约出发
		res.OrderType = 1
		res.BizInfo = generateNewBizInfo(products.BizInfo, firstBookingRange)
	}

	// 重新赋值裂变标识
	if products.OrderType == 0 && len(products.BizInfo.DepartureRange) != 2 && !defaultNow {
		products.BizInfo.IsFission = true
		res.BizInfo.IsFission = false
	}

	return res
}

func generateNewBizInfo(bizInfo *models.PrivateBizInfo, departureRange []int64) *models.PrivateBizInfo {
	res := &models.PrivateBizInfo{
		CarpoolSeatNum:     bizInfo.CarpoolSeatNum,
		ComboID:            bizInfo.ComboID,
		MaxCarpoolSeatNum:  bizInfo.MaxCarpoolSeatNum,
		TimeSpan:           bizInfo.TimeSpan,
		MatchRoutes:        bizInfo.MatchRoutes,
		CarpoolRouteInfo:   bizInfo.CarpoolRouteInfo,
		CarpoolCommuteCard: bizInfo.CarpoolCommuteCard,
		IsFission:          true,
	}

	if len(departureRange) == 2 {
		res.DepartureRange = departureRange
	}

	return res
}

func ConvertTimeSpan(timeSpans []*EstimateDecision.TimeSpanV2, spanNum int, departureTime int64) ([]*EstimateDecision.MinuteValue, []*proto.PinchecheTimeSpan) {
	var spanFlatten = make([]*EstimateDecision.MinuteValue, 0)
	var newTimeSpan = make([]*proto.PinchecheTimeSpan, 0)

	if len(timeSpans) == 0 {
		return spanFlatten, newTimeSpan
	}

	for _, dayTime := range timeSpans {
		if dayTime == nil || len(dayTime.Time) == 0 {
			continue
		}

		var daySpan = &proto.PinchecheTimeSpan{
			Title: dayTime.Title,
			Date:  dayTime.Date,
		}
		var hourSpans = make([]*proto.HourTime, 0)

		for _, hourTime := range dayTime.Time {
			if hourTime == nil || len(hourTime.MinuteList) == 0 {
				continue
			}

			var minuSpans = make([]*proto.MinuteTime, 0)
			var hourSpan = &proto.HourTime{
				Text: hourTime.Text,
				Hour: fmt.Sprintf("%d点", hourTime.Hour),
			}

			for _, minuTime := range hourTime.MinuteList {
				if len(spanFlatten) < spanNum {
					spanFlatten = append(spanFlatten, minuTime)
				}

				var minuSpan = &proto.MinuteTime{
					Minute:     fmt.Sprintf("%d分", minuTime.Minute),
					Value:      minuTime.Value,
					Selected:   buildSelected(minuTime.Value, departureTime, minuTime.DefaultSelected),
					OrderType:  minuTime.OrderType,
					ButtonText: minuTime.ButtonText,
				}

				if minuSpan.OrderType == 0 {
					minuSpan.Value = buildDepartureRangeNow(minuTime.Value, minuTime.ValueNow)
					minuSpan.Selected = buildSelected(minuSpan.Value, departureTime, minuTime.DefaultSelected)
					minuSpan.ButtonText = "现在出发"
					minuSpan.Minute = ""
					minuSpans = append(minuSpans, minuSpan)
					hourSpan.MinuteList = minuSpans
					hourSpan.Hour = "现在"
					hourSpans = append(hourSpans, hourSpan)
					minuSpans = make([]*proto.MinuteTime, 0)
					hourSpan = &proto.HourTime{
						Text: hourTime.Text,
						Hour: fmt.Sprintf("%d点", hourTime.Hour),
					}
					continue
				}
				minuSpans = append(minuSpans, minuSpan)
			}
			if len(minuSpans) > 0 {
				hourSpan.MinuteList = minuSpans
				hourSpans = append(hourSpans, hourSpan)
			}
		}
		if len(hourSpans) > 0 {
			daySpan.Time = hourSpans
			newTimeSpan = append(newTimeSpan, daySpan)
		}
	}

	return spanFlatten, newTimeSpan
}

//	func buildButtonTextNow(rightTime int64, timeConf map[string]string) string {
//		return fmt.Sprintf("现在-%s出发", time.Unix(rightTime, 0).Format("15:04"))
//	}
func buildDepartureRangeNow(timeRangeStr string, defaultTime string) string {
	timeRange := util.StringTIntArray(timeRangeStr)
	if len(timeRange) != 2 {
		return defaultTime
	}
	a := []int64{0, timeRange[1]}
	s, _ := json.Marshal(a)
	return string(s)
}

func buildSelected(timeRangeStr string, departureTime int64, defaultSelected bool) bool {
	timeRange := util.StringTIntArray(timeRangeStr)
	if departureTime == 0 || len(timeRange) != 2 {
		return defaultSelected
	}

	return departureTime > timeRange[0] && departureTime <= timeRange[1]
}
